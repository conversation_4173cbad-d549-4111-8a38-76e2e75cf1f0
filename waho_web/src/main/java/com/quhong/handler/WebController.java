package com.quhong.handler;

import com.alibaba.fastjson.JSON;
import com.quhong.datas.GzipData;
import com.quhong.enums.EncodingType;
import com.quhong.enums.HttpCode;
import com.quhong.utils.AESUtils;
import com.quhong.utils.ZipUtils;

/**
 * 根据客户端的encoding返回
 */
@Deprecated
public class WebController extends BaseController {

    @Deprecated
    protected String createResult(HttpEnvData envData, HttpCode httpCode, Object data) {
        String result = super.createResult(envData, httpCode, data);
        if (EncodingType.AES == envData.getEncoding() && needEncrypt(envData)) {
            return JSON.toJSONString(new HttpPackData(AESUtils.encryptServerData(result, envData.getNew_versioncode())));
        } else if (EncodingType.GZIP == envData.getEncoding()) {
            return JSON.toJSONString(new GzipData(ZipUtils.compress(result)));
        } else {
            return result;
        }
    }

    protected boolean needEncrypt(HttpEnvData envData) {
        return envData.debug == 0;
    }
}
