package com.quhong.filter;

import jakarta.servlet.DispatcherType;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.web.servlet.FilterRegistrationBean;
import org.springframework.context.annotation.Bean;
import org.springframework.core.Ordered;

import java.util.*;


public abstract class AbstractFilterConfig {
    @Value("${baseUrl}")
    protected String baseUrl;

    @Bean
    public FilterRegistrationBean<FacadeFilter> loginFilter() {
        FacadeFilter facadeFilter = new FacadeFilter();
        facadeFilter.setRequestWarnDurationMap(getRequestWarnDurationMap());
        facadeFilter.setAnonymousPath(getAnonymousPath());
        facadeFilter.setExcludePaths(getExcludePaths());
        FilterRegistrationBean<FacadeFilter> filter = new FilterRegistrationBean<>();
        filter.setFilter(facadeFilter);
        // 过滤名称
        filter.setName("facadeFilter");
        filter.setDispatcherTypes(DispatcherType.REQUEST);
        // 需要过滤的路径
        Set<String> set = new LinkedHashSet<>();
        set.add("/*");
        Map<String, String> map = new HashMap<>();
        map.put("encoding", "UTF-8");
        filter.setUrlPatterns(set);
        filter.setInitParameters(map);
        filter.setOrder(Ordered.HIGHEST_PRECEDENCE);
        return filter;
    }

    /**
     * 该List的路径不会进行Post参数解密和token校验，如第三方回调等，健康检查...
     */
    protected List<String> getExcludePaths() {
        return Collections.emptyList();
    }

    /**
     * 接口告警时间
     */
    protected Map<String, Long> getRequestWarnDurationMap() {
        return Collections.emptyMap();
    }

    /**
     * 该List的路径不会进行token校验，但会进行参数解密，如登录接口...
     */
    public List<String> getAnonymousPath() {
        return Collections.emptyList();
    }
}
