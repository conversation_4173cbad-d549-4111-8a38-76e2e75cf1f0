// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: cmdid.proto

package com.quhong.proto;

public final class Cmdid {
  private Cmdid() {}
  public static void registerAllExtensions(
      com.google.protobuf.ExtensionRegistryLite registry) {
  }

  public static void registerAllExtensions(
      com.google.protobuf.ExtensionRegistry registry) {
    registerAllExtensions(
        (com.google.protobuf.ExtensionRegistryLite) registry);
  }
  /**
   * Protobuf enum {@code CmdID}
   */
  public enum CmdID
      implements com.google.protobuf.ProtocolMessageEnum {
    /**
     * <code>CMD_ID_INVALID = 0;</code>
     */
    CMD_ID_INVALID(0),
    /**
     * <pre>
     *客户端登录
     * </pre>
     *
     * <code>CMD_ID_LOGIN = 1001;</code>
     */
    CMD_ID_LOGIN(1001),
    /**
     * <pre>
     *心跳
     * </pre>
     *
     * <code>CMD_ID_HEART_BEAT = 1003;</code>
     */
    CMD_ID_HEART_BEAT(1003),
    /**
     * <pre>
     *客户端收到消息确认
     * </pre>
     *
     * <code>CMD_ID_CLIENT_RECV_ACK = 1005;</code>
     */
    CMD_ID_CLIENT_RECV_ACK(1005),
    /**
     * <pre>
     *改变场景
     * </pre>
     *
     * <code>CMD_ID_CHANGE_SCENE = 1007;</code>
     */
    CMD_ID_CHANGE_SCENE(1007),
    /**
     * <pre>
     *用户在别的设备登录 使用 ResponseAck
     * </pre>
     *
     * <code>CMD_ID_KICK_OUT = 1010;</code>
     */
    CMD_ID_KICK_OUT(1010),
    /**
     * <pre>
     *room cmd_id
     * </pre>
     *
     * <code>CMD_ID_ENTER_ROOM = 2001;</code>
     */
    CMD_ID_ENTER_ROOM(2001),
    /**
     * <pre>
     *进入房间推送
     * </pre>
     *
     * <code>CMD_ID_ENTER_ROOM_PUSH = 2002;</code>
     */
    CMD_ID_ENTER_ROOM_PUSH(2002),
    /**
     * <pre>
     *离开房间请求
     * </pre>
     *
     * <code>CMD_ID_LEAVE_ROOM = 2003;</code>
     */
    CMD_ID_LEAVE_ROOM(2003),
    /**
     * <pre>
     * 房间文字消息请求
     * </pre>
     *
     * <code>CMD_ID_SEND_TEXT_ROOM = 2005;</code>
     */
    CMD_ID_SEND_TEXT_ROOM(2005),
    /**
     * <pre>
     *房间文字消息推送
     * </pre>
     *
     * <code>CMD_ID_SEND_TEXT_ROOM_PUSH = 2006;</code>
     */
    CMD_ID_SEND_TEXT_ROOM_PUSH(2006),
    /**
     * <pre>
     *房间礼物消息推送
     * </pre>
     *
     * <code>CMD_ID_SEND_GIFT_ROOM_PUSH = 2008;</code>
     */
    CMD_ID_SEND_GIFT_ROOM_PUSH(2008),
    /**
     * <pre>
     *房间幸运转盘推送
     * </pre>
     *
     * <code>CMD_ID_LUCKY_WHEEL_ROOM_PUSH = 2010;</code>
     */
    CMD_ID_LUCKY_WHEEL_ROOM_PUSH(2010),
    /**
     * <pre>
     *房间幸运宝箱推送
     * </pre>
     *
     * <code>CMD_ID_LUCKY_BOX_ROOM_PUSH = 2012;</code>
     */
    CMD_ID_LUCKY_BOX_ROOM_PUSH(2012),
    /**
     * <pre>
     *房间幸运数字推送
     * </pre>
     *
     * <code>CMD_ID_LUCKY_NUM_ROOM_PUSH = 2014;</code>
     */
    CMD_ID_LUCKY_NUM_ROOM_PUSH(2014),
    /**
     * <pre>
     *房间骰子推送
     * </pre>
     *
     * <code>CMD_ID_DICE_ROOM_PUSH = 2016;</code>
     */
    CMD_ID_DICE_ROOM_PUSH(2016),
    /**
     * <pre>
     *解散房间推送
     * </pre>
     *
     * <code>CMD_ID_DISMISS_ROOM_PUSH = 2018;</code>
     */
    CMD_ID_DISMISS_ROOM_PUSH(2018),
    /**
     * <pre>
     *离开房间房间推送
     * </pre>
     *
     * <code>CMD_ID_LEAVE_ROOM_PUSH = 2020;</code>
     */
    CMD_ID_LEAVE_ROOM_PUSH(2020),
    /**
     * <pre>
     *用户监控推送
     * </pre>
     *
     * <code>CMD_ID_USER_MONITOR_PUSH = 3002;</code>
     */
    CMD_ID_USER_MONITOR_PUSH(3002),
    /**
     * <pre>
     *通用错误推送，如果带roomId，则按房间内消息处理
     * </pre>
     *
     * <code>CMD_ID_ERROR_NOTIFICATION_PUSH = 3004;</code>
     */
    CMD_ID_ERROR_NOTIFICATION_PUSH(3004),
    UNRECOGNIZED(-1),
    ;

    /**
     * <code>CMD_ID_INVALID = 0;</code>
     */
    public static final int CMD_ID_INVALID_VALUE = 0;
    /**
     * <pre>
     *客户端登录
     * </pre>
     *
     * <code>CMD_ID_LOGIN = 1001;</code>
     */
    public static final int CMD_ID_LOGIN_VALUE = 1001;
    /**
     * <pre>
     *心跳
     * </pre>
     *
     * <code>CMD_ID_HEART_BEAT = 1003;</code>
     */
    public static final int CMD_ID_HEART_BEAT_VALUE = 1003;
    /**
     * <pre>
     *客户端收到消息确认
     * </pre>
     *
     * <code>CMD_ID_CLIENT_RECV_ACK = 1005;</code>
     */
    public static final int CMD_ID_CLIENT_RECV_ACK_VALUE = 1005;
    /**
     * <pre>
     *改变场景
     * </pre>
     *
     * <code>CMD_ID_CHANGE_SCENE = 1007;</code>
     */
    public static final int CMD_ID_CHANGE_SCENE_VALUE = 1007;
    /**
     * <pre>
     *用户在别的设备登录 使用 ResponseAck
     * </pre>
     *
     * <code>CMD_ID_KICK_OUT = 1010;</code>
     */
    public static final int CMD_ID_KICK_OUT_VALUE = 1010;
    /**
     * <pre>
     *room cmd_id
     * </pre>
     *
     * <code>CMD_ID_ENTER_ROOM = 2001;</code>
     */
    public static final int CMD_ID_ENTER_ROOM_VALUE = 2001;
    /**
     * <pre>
     *进入房间推送
     * </pre>
     *
     * <code>CMD_ID_ENTER_ROOM_PUSH = 2002;</code>
     */
    public static final int CMD_ID_ENTER_ROOM_PUSH_VALUE = 2002;
    /**
     * <pre>
     *离开房间请求
     * </pre>
     *
     * <code>CMD_ID_LEAVE_ROOM = 2003;</code>
     */
    public static final int CMD_ID_LEAVE_ROOM_VALUE = 2003;
    /**
     * <pre>
     * 房间文字消息请求
     * </pre>
     *
     * <code>CMD_ID_SEND_TEXT_ROOM = 2005;</code>
     */
    public static final int CMD_ID_SEND_TEXT_ROOM_VALUE = 2005;
    /**
     * <pre>
     *房间文字消息推送
     * </pre>
     *
     * <code>CMD_ID_SEND_TEXT_ROOM_PUSH = 2006;</code>
     */
    public static final int CMD_ID_SEND_TEXT_ROOM_PUSH_VALUE = 2006;
    /**
     * <pre>
     *房间礼物消息推送
     * </pre>
     *
     * <code>CMD_ID_SEND_GIFT_ROOM_PUSH = 2008;</code>
     */
    public static final int CMD_ID_SEND_GIFT_ROOM_PUSH_VALUE = 2008;
    /**
     * <pre>
     *房间幸运转盘推送
     * </pre>
     *
     * <code>CMD_ID_LUCKY_WHEEL_ROOM_PUSH = 2010;</code>
     */
    public static final int CMD_ID_LUCKY_WHEEL_ROOM_PUSH_VALUE = 2010;
    /**
     * <pre>
     *房间幸运宝箱推送
     * </pre>
     *
     * <code>CMD_ID_LUCKY_BOX_ROOM_PUSH = 2012;</code>
     */
    public static final int CMD_ID_LUCKY_BOX_ROOM_PUSH_VALUE = 2012;
    /**
     * <pre>
     *房间幸运数字推送
     * </pre>
     *
     * <code>CMD_ID_LUCKY_NUM_ROOM_PUSH = 2014;</code>
     */
    public static final int CMD_ID_LUCKY_NUM_ROOM_PUSH_VALUE = 2014;
    /**
     * <pre>
     *房间骰子推送
     * </pre>
     *
     * <code>CMD_ID_DICE_ROOM_PUSH = 2016;</code>
     */
    public static final int CMD_ID_DICE_ROOM_PUSH_VALUE = 2016;
    /**
     * <pre>
     *解散房间推送
     * </pre>
     *
     * <code>CMD_ID_DISMISS_ROOM_PUSH = 2018;</code>
     */
    public static final int CMD_ID_DISMISS_ROOM_PUSH_VALUE = 2018;
    /**
     * <pre>
     *离开房间房间推送
     * </pre>
     *
     * <code>CMD_ID_LEAVE_ROOM_PUSH = 2020;</code>
     */
    public static final int CMD_ID_LEAVE_ROOM_PUSH_VALUE = 2020;
    /**
     * <pre>
     *用户监控推送
     * </pre>
     *
     * <code>CMD_ID_USER_MONITOR_PUSH = 3002;</code>
     */
    public static final int CMD_ID_USER_MONITOR_PUSH_VALUE = 3002;
    /**
     * <pre>
     *通用错误推送，如果带roomId，则按房间内消息处理
     * </pre>
     *
     * <code>CMD_ID_ERROR_NOTIFICATION_PUSH = 3004;</code>
     */
    public static final int CMD_ID_ERROR_NOTIFICATION_PUSH_VALUE = 3004;


    public final int getNumber() {
      if (this == UNRECOGNIZED) {
        throw new java.lang.IllegalArgumentException(
            "Can't get the number of an unknown enum value.");
      }
      return value;
    }

    /**
     * @deprecated Use {@link #forNumber(int)} instead.
     */
    @java.lang.Deprecated
    public static CmdID valueOf(int value) {
      return forNumber(value);
    }

    public static CmdID forNumber(int value) {
      switch (value) {
        case 0: return CMD_ID_INVALID;
        case 1001: return CMD_ID_LOGIN;
        case 1003: return CMD_ID_HEART_BEAT;
        case 1005: return CMD_ID_CLIENT_RECV_ACK;
        case 1007: return CMD_ID_CHANGE_SCENE;
        case 1010: return CMD_ID_KICK_OUT;
        case 2001: return CMD_ID_ENTER_ROOM;
        case 2002: return CMD_ID_ENTER_ROOM_PUSH;
        case 2003: return CMD_ID_LEAVE_ROOM;
        case 2005: return CMD_ID_SEND_TEXT_ROOM;
        case 2006: return CMD_ID_SEND_TEXT_ROOM_PUSH;
        case 2008: return CMD_ID_SEND_GIFT_ROOM_PUSH;
        case 2010: return CMD_ID_LUCKY_WHEEL_ROOM_PUSH;
        case 2012: return CMD_ID_LUCKY_BOX_ROOM_PUSH;
        case 2014: return CMD_ID_LUCKY_NUM_ROOM_PUSH;
        case 2016: return CMD_ID_DICE_ROOM_PUSH;
        case 2018: return CMD_ID_DISMISS_ROOM_PUSH;
        case 2020: return CMD_ID_LEAVE_ROOM_PUSH;
        case 3002: return CMD_ID_USER_MONITOR_PUSH;
        case 3004: return CMD_ID_ERROR_NOTIFICATION_PUSH;
        default: return null;
      }
    }

    public static com.google.protobuf.Internal.EnumLiteMap<CmdID>
        internalGetValueMap() {
      return internalValueMap;
    }
    private static final com.google.protobuf.Internal.EnumLiteMap<
        CmdID> internalValueMap =
          new com.google.protobuf.Internal.EnumLiteMap<CmdID>() {
            public CmdID findValueByNumber(int number) {
              return CmdID.forNumber(number);
            }
          };

    public final com.google.protobuf.Descriptors.EnumValueDescriptor
        getValueDescriptor() {
      return getDescriptor().getValues().get(ordinal());
    }
    public final com.google.protobuf.Descriptors.EnumDescriptor
        getDescriptorForType() {
      return getDescriptor();
    }
    public static final com.google.protobuf.Descriptors.EnumDescriptor
        getDescriptor() {
      return com.quhong.proto.Cmdid.getDescriptor().getEnumTypes().get(0);
    }

    private static final CmdID[] VALUES = values();

    public static CmdID valueOf(
        com.google.protobuf.Descriptors.EnumValueDescriptor desc) {
      if (desc.getType() != getDescriptor()) {
        throw new java.lang.IllegalArgumentException(
          "EnumValueDescriptor is not for this type.");
      }
      if (desc.getIndex() == -1) {
        return UNRECOGNIZED;
      }
      return VALUES[desc.getIndex()];
    }

    private final int value;

    private CmdID(int value) {
      this.value = value;
    }

    // @@protoc_insertion_point(enum_scope:CmdID)
  }


  public static com.google.protobuf.Descriptors.FileDescriptor
      getDescriptor() {
    return descriptor;
  }
  private static  com.google.protobuf.Descriptors.FileDescriptor
      descriptor;
  static {
    java.lang.String[] descriptorData = {
      "\n\013cmdid.proto*\277\004\n\005CmdID\022\022\n\016CMD_ID_INVALI" +
      "D\020\000\022\021\n\014CMD_ID_LOGIN\020\351\007\022\026\n\021CMD_ID_HEART_B" +
      "EAT\020\353\007\022\033\n\026CMD_ID_CLIENT_RECV_ACK\020\355\007\022\030\n\023C" +
      "MD_ID_CHANGE_SCENE\020\357\007\022\024\n\017CMD_ID_KICK_OUT" +
      "\020\362\007\022\026\n\021CMD_ID_ENTER_ROOM\020\321\017\022\033\n\026CMD_ID_EN" +
      "TER_ROOM_PUSH\020\322\017\022\026\n\021CMD_ID_LEAVE_ROOM\020\323\017" +
      "\022\032\n\025CMD_ID_SEND_TEXT_ROOM\020\325\017\022\037\n\032CMD_ID_S" +
      "END_TEXT_ROOM_PUSH\020\326\017\022\037\n\032CMD_ID_SEND_GIF" +
      "T_ROOM_PUSH\020\330\017\022!\n\034CMD_ID_LUCKY_WHEEL_ROO" +
      "M_PUSH\020\332\017\022\037\n\032CMD_ID_LUCKY_BOX_ROOM_PUSH\020",
      "\334\017\022\037\n\032CMD_ID_LUCKY_NUM_ROOM_PUSH\020\336\017\022\032\n\025C" +
      "MD_ID_DICE_ROOM_PUSH\020\340\017\022\035\n\030CMD_ID_DISMIS" +
      "S_ROOM_PUSH\020\342\017\022\033\n\026CMD_ID_LEAVE_ROOM_PUSH" +
      "\020\344\017\022\035\n\030CMD_ID_USER_MONITOR_PUSH\020\272\027\022#\n\036CM" +
      "D_ID_ERROR_NOTIFICATION_PUSH\020\274\027B\022\n\020com.q" +
      "uhong.protob\006proto3"
    };
    com.google.protobuf.Descriptors.FileDescriptor.InternalDescriptorAssigner assigner =
        new com.google.protobuf.Descriptors.FileDescriptor.    InternalDescriptorAssigner() {
          public com.google.protobuf.ExtensionRegistry assignDescriptors(
              com.google.protobuf.Descriptors.FileDescriptor root) {
            descriptor = root;
            return null;
          }
        };
    com.google.protobuf.Descriptors.FileDescriptor
      .internalBuildGeneratedFileFrom(descriptorData,
        new com.google.protobuf.Descriptors.FileDescriptor[] {
        }, assigner);
  }

  // @@protoc_insertion_point(outer_class_scope)
}
