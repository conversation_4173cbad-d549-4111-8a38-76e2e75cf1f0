[← 返回项目AI配置](../index.md) | [工具和服务索引](index.md) | **Redis操作**

# Redis操作工具使用指南

本文档提供了项目中Redis缓存操作工具的使用方法和最佳实践。

## Redis配置

项目使用Jedis作为Redis客户端，通过Spring Boot进行配置。

### 依赖配置

```xml
<!-- pom.xml -->
<dependency>
    <groupId>org.springframework.boot</groupId>
    <artifactId>spring-boot-starter-data-redis</artifactId>
</dependency>

<!-- 使用Jedis作为客户端 -->
<dependency>
    <groupId>redis.clients</groupId>
    <artifactId>jedis</artifactId>
</dependency>
```

### Redis连接池配置

```yaml
# application.yml
spring:
  redis:
    host: 127.0.0.1
    port: 6379
    password:
    database: 0
    timeout: 3000
    jedis:
      pool:
        max-active: 8
        max-wait: -1ms
        max-idle: 8
        min-idle: 0
```

## Redis工具类

项目提供了`RedisUtil`工具类用于操作Redis，封装了常用的Redis操作，简化了Redis的使用。

### 常用方法

#### 字符串操作

```java
// 存储字符串
RedisUtil.set("key", "value");

// 存储字符串并设置过期时间（秒）
RedisUtil.set("key", "value", 60);

// 获取字符串
String value = RedisUtil.get("key");

// 删除键
RedisUtil.delete("key");

// 批量删除键
RedisUtil.delete(Arrays.asList("key1", "key2"));

// 设置过期时间
RedisUtil.expire("key", 60);

// 检查键是否存在
boolean exists = RedisUtil.hasKey("key");
```

#### 哈希操作

```java
// 存储哈希
RedisUtil.hset("hashKey", "field", "value");

// 获取哈希中的字段值
String value = RedisUtil.hget("hashKey", "field");

// 获取哈希的所有字段和值
Map<Object, Object> entries = RedisUtil.hgetAll("hashKey");

// 删除哈希中的字段
RedisUtil.hdel("hashKey", "field");
```

#### 列表操作

```java
// 从列表左侧添加元素
RedisUtil.lpush("listKey", "value1", "value2");

// 从列表右侧添加元素
RedisUtil.rpush("listKey", "value3");

// 获取列表指定范围的元素
List<String> values = RedisUtil.lrange("listKey", 0, -1);

// 获取列表长度
long size = RedisUtil.llen("listKey");

// 从列表左侧弹出元素
String value = RedisUtil.lpop("listKey");
```

#### 集合操作

```java
// 向集合添加元素
RedisUtil.sadd("setKey", "member1", "member2");

// 获取集合所有成员
Set<String> members = RedisUtil.smembers("setKey");

// 判断元素是否是集合成员
boolean isMember = RedisUtil.sismember("setKey", "member1");

// 获取集合大小
long size = RedisUtil.scard("setKey");

// 从集合移除成员
RedisUtil.srem("setKey", "member1");
```

#### 有序集合操作

```java
// 向有序集合添加元素，带分数
RedisUtil.zadd("zsetKey", 1.0, "member1");
RedisUtil.zadd("zsetKey", 2.0, "member2");

// 获取有序集合指定范围的元素
Set<String> range = RedisUtil.zrange("zsetKey", 0, -1);

// 获取元素的分数
Double score = RedisUtil.zscore("zsetKey", "member1");

// 获取元素的排名
Long rank = RedisUtil.zrank("zsetKey", "member1");

// 获取指定分数范围的元素
Set<String> rangeByScore = RedisUtil.zrangeByScore("zsetKey", 1.0, 2.0);
```

## 对象序列化与反序列化

RedisUtil支持对象的存储和获取，内部使用JSON进行序列化和反序列化。

```java
// 存储对象
User user = new User("张三", 20);
RedisUtil.setObject("user:1", user);

// 获取对象
User retrievedUser = RedisUtil.getObject("user:1", User.class);

// 存储对象列表
List<User> users = Arrays.asList(new User("张三", 20), new User("李四", 25));
RedisUtil.setList("users", users);

// 获取对象列表
List<User> retrievedUsers = RedisUtil.getList("users", User.class);
```

## 缓存注解使用

除了直接使用RedisUtil外，项目也支持使用Spring Cache注解来操作缓存。

```java
@Service
public class UserService {

    // 缓存查询结果，key为方法参数，value为返回值
    @Cacheable(value = "users", key = "#id")
    public User getUserById(Long id) {
        // 从数据库查询用户
        return userMapper.selectById(id);
    }

    // 更新后清除缓存
    @CacheEvict(value = "users", key = "#user.id")
    public void updateUser(User user) {
        userMapper.updateById(user);
    }

    // 新增或更新后更新缓存
    @CachePut(value = "users", key = "#result.id")
    public User saveUser(User user) {
        if (user.getId() == null) {
            userMapper.insert(user);
        } else {
            userMapper.updateById(user);
        }
        return user;
    }

    // 删除后清除缓存
    @CacheEvict(value = "users", key = "#id")
    public void deleteUser(Long id) {
        userMapper.deleteById(id);
    }

    // 清除指定缓存的所有条目
    @CacheEvict(value = "users", allEntries = true)
    public void clearUserCache() {
        // 方法体可以为空
    }
}
```

## 分布式锁实现

RedisUtil提供了基于Redis的分布式锁实现，用于多实例并发控制。

```java
// 获取分布式锁（带超时自动释放）
boolean locked = RedisUtil.tryLock("lockKey", "requestId", 10);
if (locked) {
    try {
        // 执行需要加锁的操作
    } finally {
        // 释放锁
        RedisUtil.releaseLock("lockKey", "requestId");
    }
}
```

或者使用带有回调的方法：

```java
// 尝试获取锁并执行操作
RedisUtil.tryLockAndExecute("lockKey", "requestId", 10, () -> {
    // 执行需要加锁的操作
    return true; // 返回操作结果
});
```

## 缓存穿透、击穿和雪崩防护

### 缓存穿透防护

缓存穿透是指查询一个不存在的数据，导致每次都要穿透到数据库查询。

```java
public User getUserById(Long id) {
    String cacheKey = "user:" + id;
    User user = RedisUtil.getObject(cacheKey, User.class);

    if (user == null) {
        // 从数据库查询
        user = userMapper.selectById(id);

        // 即使为null也缓存，但过期时间较短
        if (user != null) {
            RedisUtil.setObject(cacheKey, user, 3600); // 存在的数据缓存1小时
        } else {
            RedisUtil.setObject(cacheKey, new NullValueObject(), 60); // 不存在的数据缓存1分钟
        }
    }

    return user == NullValueObject.class ? null : user;
}
```

或者使用布隆过滤器：

```java
@Autowired
private BloomFilter<Long> userIdBloomFilter;

public User getUserById(Long id) {
    // 先判断ID是否可能存在
    if (!userIdBloomFilter.mightContain(id)) {
        return null;
    }

    String cacheKey = "user:" + id;
    User user = RedisUtil.getObject(cacheKey, User.class);

    if (user == null) {
        // 从数据库查询
        user = userMapper.selectById(id);

        if (user != null) {
            RedisUtil.setObject(cacheKey, user, 3600);
        }
    }

    return user;
}
```

### 缓存击穿防护

缓存击穿是指热点数据过期后，大量并发请求同时查询数据库。

```java
public User getUserById(Long id) {
    String cacheKey = "user:" + id;
    User user = RedisUtil.getObject(cacheKey, User.class);

    if (user == null) {
        // 使用分布式锁防止并发查询数据库
        String lockKey = "lock:user:" + id;
        boolean locked = RedisUtil.tryLock(lockKey, UUID.randomUUID().toString(), 10);

        try {
            if (locked) {
                // 双重检查，防止其他线程已经更新了缓存
                user = RedisUtil.getObject(cacheKey, User.class);
                if (user == null) {
                    // 从数据库查询
                    user = userMapper.selectById(id);

                    if (user != null) {
                        // 添加随机过期时间，防止缓存雪崩
                        int expireTime = 3600 + new Random().nextInt(300);
                        RedisUtil.setObject(cacheKey, user, expireTime);
                    }
                }
            } else {
                // 没有获取到锁，稍等后重试获取缓存
                Thread.sleep(50);
                return getUserById(id);
            }
        } catch (InterruptedException e) {
            Thread.currentThread().interrupt();
        } finally {
            if (locked) {
                RedisUtil.releaseLock(lockKey, UUID.randomUUID().toString());
            }
        }
    }

    return user;
}
```

### 缓存雪崩防护

缓存雪崩是指大量缓存同时过期，导致大量请求直接访问数据库。

```java
// 初始化缓存时使用随机过期时间
public void initCache() {
    List<User> users = userMapper.selectAll();
    for (User user : users) {
        // 基础过期时间加随机值
        int expireTime = 3600 + new Random().nextInt(600);
        RedisUtil.setObject("user:" + user.getId(), user, expireTime);
    }
}
```

## 缓存预热

系统启动时预热热点数据的缓存：

```java
@Component
public class CachePreheater implements ApplicationRunner {

    @Autowired
    private UserService userService;

    @Override
    public void run(ApplicationArguments args) {
        // 加载热门用户数据
        List<Long> hotUserIds = userService.getHotUserIds();
        for (Long userId : hotUserIds) {
            User user = userService.getUserById(userId);
            RedisUtil.setObject("user:" + userId, user, 3600);
        }

        // 加载系统配置
        Map<String, String> configMap = configService.getAllConfigs();
        RedisUtil.hmset("system:config", configMap);
    }
}
```

## 缓存监控

项目集成了Redis的监控工具，用于监控Redis的使用情况：

```java
@Service
public class RedisMonitorService {

    @Autowired
    private StringRedisTemplate redisTemplate;

    /**
     * 获取Redis信息
     */
    public Map<String, Object> getRedisInfo() {
        Properties info = redisTemplate.getConnectionFactory().getConnection().info();
        Map<String, Object> result = new HashMap<>();

        // 提取关键监控指标
        result.put("usedMemory", info.getProperty("used_memory"));
        result.put("clients", info.getProperty("connected_clients"));
        result.put("commandProcessed", info.getProperty("total_commands_processed"));

        return result;
    }

    /**
     * 获取指定前缀的键数量
     */
    public Long getKeysCount(String pattern) {
        return redisTemplate.execute((RedisCallback<Long>) connection -> {
            long count = 0;
            Cursor<byte[]> cursor = connection.scan(ScanOptions.scanOptions().match(pattern).build());
            while (cursor.hasNext()) {
                cursor.next();
                count++;
            }
            return count;
        });
    }

    /**
     * 获取缓存命中率
     */
    public double getCacheHitRatio() {
        Properties info = redisTemplate.getConnectionFactory().getConnection().info();
        String keyspace_hits = info.getProperty("keyspace_hits");
        String keyspace_misses = info.getProperty("keyspace_misses");

        long hits = Long.parseLong(keyspace_hits);
        long misses = Long.parseLong(keyspace_misses);

        return (double) hits / (hits + misses);
    }
}
```

## 最佳实践

### 键命名规范

1. 使用冒号分隔不同部分：`业务名:实体名:ID:属性`
2. 使用统一的前缀：`项目名:业务名:实体名:ID`
3. 例如：
   - 用户信息：`quhong:user:1001`
   - 用户令牌：`quhong:token:1001`
   - 用户权限：`quhong:permission:1001`

### 过期时间设置

1. 根据数据更新频率设置合理的过期时间
2. 热点数据缓存时间较长，非热点数据缓存时间较短
3. 类似数据采用相近的过期时间，但增加随机值，防止缓存雪崩
4. 常见过期时间参考：
   - 用户基本信息：1小时
   - 配置信息：5分钟
   - 验证码：5分钟
   - 令牌：7天

### 大键处理

避免存储过大的值，会导致Redis性能下降：

1. 对于大对象，考虑拆分存储
2. 仅缓存关键字段，而不是完整对象
3. 对于列表类数据，只缓存ID列表，具体对象单独缓存

### 频繁更新数据

对于频繁更新的数据，考虑以下策略：

1. 降低缓存时间或不缓存
2. 使用增量更新方式，例如哈希结构中只更新变化的字段
3. 实时性要求高的数据考虑使用本地缓存+消息通知的方式

### Redis事务

对于需要原子性操作的场景，使用Redis事务：

```java
// 示例：转账操作
public boolean transfer(String fromAccount, String toAccount, double amount) {
    return RedisUtil.executeTransaction(operations -> {
        Double fromBalance = Double.valueOf(operations.get("account:" + fromAccount));
        if (fromBalance < amount) {
            return false;
        }

        operations.set("account:" + fromAccount, String.valueOf(fromBalance - amount));

        Double toBalance = Double.valueOf(operations.get("account:" + toAccount));
        operations.set("account:" + toAccount, String.valueOf(toBalance + amount));

        return true;
    });
}
```

### 使用管道提升性能

对于批量操作，使用Redis管道以减少网络开销：

```java
// 批量获取用户信息
public List<User> batchGetUsers(List<Long> userIds) {
    return RedisUtil.executePipeline(operations -> {
        for (Long userId : userIds) {
            operations.get("user:" + userId);
        }
    }).stream()
        .map(result -> JSON.parseObject((String) result, User.class))
        .collect(Collectors.toList());
}
```

[返回首页](../index.md)