**提示词：**

请根据我提供的 Java 后端项目模块目录，**完全立足项目代码**，**严格读取并详细分析**模块代码，并结合 Apifox API Model（如果提供），生成一套完整的项目编码规范、模板、调用方法和工具信息，用于后续 AI 代码生成和迭代。

**提供的项目模块目录：**

（请在此处提供项目模块目录的路径。例如：`/path/to/project/modules`）

**提供的 Apifox API Model（可选）：**

（请在此处提供 Apifox API Model 的 JSON 或 YAML 文件路径，或者直接提供 Apifox API Model 的文本内容。）

**具体要求：**

1.  **完全立足项目代码，严格读取并分析：**
    * **必须完全立足项目代码**，自动读取提供的模块目录下的所有 Java 代码文件，**不得凭空生成任何代码或信息**。
    * **严格读取并详细分析** 读取的代码，提取项目的技术架构（基于代码结构分析）、技术栈（基于代码依赖分析）、编码规范、模板、工具使用、基础服务工具、模块之间的调用方式等信息，**仅关注编码相关的细节**。
    * **详细识别** 项目常用的方法和工具类，包括核心库中的工具类和方法，**仅关注编码相关的调用方式**。
2.  **整合 Apifox API Model 信息（如果提供）：**
    * 如果提供了 Apifox API Model，**必须** 详细分析 API Model，提取 API 接口定义、请求参数、响应数据、错误码、认证方式、数据模型（API Model）等信息。
    * 将提取的 API 信息和数据模型信息整合到相应的规范文档（例如 `api_design.md`）和工具和服务文档（例如 `api_client.md`）中，确保 AI 能够理解和使用 API 接口和数据模型。
    * 特别注意 Apifox 中定义的参数类型，返回数据类型，和返回的例子。
3.  **生成详细的规范文档，专注于编码规范：**
    * 在 `project_ai_config/specifications` 目录下，使用 Markdown 语法生成一份详细的项目编码规范文档。
    * 将每个规范部分作为一个独立的 Markdown 文件，例如 `code_style.md`、`api_design.md`、`database_design.md`、`error_handling.md`、`security.md` 等。
    * 在主规范文档（例如 `project_ai_config/specifications/project_spec.md`）中使用 Markdown 语法创建导航，通过超链接链接到每个独立的规范文件。
    * 在规范文档中使用 Mermaid 语法（例如 `mermaid` 代码块）绘制流程图、时序图、类图等，清晰展示编码规范的逻辑和结构。
    * **在每个规范文档的末尾，添加一个返回入口文档 `index.md` 的超链接。**
4.  **生成详细的模板文档，专注于代码模板：**
    * 在 `project_ai_config/templates` 目录下，使用 Markdown 语法生成一套详细的代码模板，并将每个模板作为一个独立的 Markdown 文件。
    * 例如 `controller_template.md`、`service_template.md`、`repository_template.md`、`dto_template.md`、`util_template.md` 等。
    * 在主模板文档（例如 `project_ai_config/templates/project_templates.md`）中使用 Markdown 语法创建导航，通过超链接链接到每个独立的模板文件。
    * **严格根据** 自动读取的代码和 Apifox API Model（如果提供），详细完善代码模板，确保模板覆盖各种常见编码场景。
    * **特别注意：Service 类的实现必须直接在 Service 类中完成，不得使用单独的实现类。**
    * 在模板文档中使用 Mermaid 语法（例如 `mermaid` 代码块）绘制流程图、时序图、类图等，清晰展示模板的结构和使用方式。
    * **在每个模板文档的末尾，添加一个返回入口文档 `index.md` 的超链接。**
5.  **生成详细的工具和服务文档，专注于调用方法：**
    * 在 `project_ai_config/tools_and_services` 目录下，使用 Markdown 语法详细描述项目使用的工具、基础服务工具和模块之间的调用方式，**包括核心库中的工具和方法**，**仅关注编码相关的调用方法**。
    * 例如，详细说明日志工具的配置和使用方法、配置中心的读取方式、数据库操作工具的封装、RESTful API 调用规范等。
    * 在工具和服务文档中使用 Mermaid 语法（例如 `mermaid` 代码块）绘制流程图、时序图、类图等，清晰展示工具和服务的使用方式和调用关系。
    * **在每个工具和服务文档的末尾，添加一个返回入口文档 `index.md` 的超链接。**
6.  **生成入口索引文档：**
    * 在 `project_ai_config` 目录下，生成一个入口的 Markdown 文档 `index.md`，作为整个配置的索引。
    * `index.md` 文档应包含项目概述、技术架构导航、技术栈导航、规范导航、模板导航、工具和服务导航等详细内容。
    * `index.md` 文档应具有强大的索引功能，例如使用 Markdown 的标题、列表、表格、超链接等，方便快速查找和定位所需信息。
7.  **明确 AI 的查找和使用逻辑：**
    * 当您提出功能需求时，AI **必须** 能够自动分析需求，识别相关的代码元素（例如 Controller、Service、Repository、DTO、Util 等）和核心库工具。
    * 根据识别出的代码元素和核心库工具，AI **必须** 能够自动在 `project_ai_config` 目录下查找对应的模板、规范、工具和服务文档。
    * AI **必须** 能够根据找到的模板、规范、工具和服务文档，生成符合项目要求的代码，并放置在合适的位置。
    * AI 生成的代码 **必须** 严格符合项目的编码规范。
8.  **生成项目技术栈依赖：**
    * 生成项目技术栈依赖，方便后续 AI 生成代码时正确引入依赖。

**重要提示：**

* **必须完全立足项目代码**，**不得胡编乱造**。
* 生成的文档 **必须** 详细、清晰、易于理解和执行，方便 AI 在编程时快速查找和使用。
* **重点关注编码相关的细节，忽略业务相关的逻辑。**
* **在每个文档的末尾，添加一个返回入口文档 `index.md` 的超链接。**
* **特别注意：Service 类的实现必须直接在 Service 类中完成，不得使用单独的实现类。**

通过在提示词中明确说明 Service 类的实现方式，我们可以确保 AI 能够生成符合项目要求的代码。
