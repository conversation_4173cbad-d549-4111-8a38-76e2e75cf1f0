[← 返回项目AI配置](../index.md) | [模板导航](project_templates.md) | **Service模板**

# Service模板

Service层负责处理业务逻辑，是系统的核心层。本模板提供了创建Service的标准方式。

## 基本结构

```java
package com.quhong.service;

import com.alibaba.fastjson.JSON;
import com.quhong.common.enums.HttpCode;
import com.quhong.common.exception.WebException;
import com.quhong.core.monitor.MonitorSender;
import com.quhong.dao.ExampleDao;
import com.quhong.dao.datas.ExampleData;
import com.quhong.data.dto.ExampleDTO;
import com.quhong.data.vo.ExampleVO;
import com.quhong.redis.ExampleRedis;
import com.quhong.utils.BeanCopyUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;

/**
 * @ClassName ExampleService
 * @Descripion 示例服务，处理示例相关的业务逻辑
 * <AUTHOR> <EMAIL>
 * @Date 2023/01/01 10:00 上午
 * @Version 1.0
 **/
@Service
@Slf4j
public class ExampleService {
    @Autowired
    private ExampleDao exampleDao;

    @Resource
    private ExampleRedis exampleRedis;

    @Autowired
    private MonitorSender monitorSender;

    @Resource
    private BeanCopyUtils beanCopyUtils;

    /**
     * 查询示例数据
     *
     * @param dto 查询参数
     * @return 示例数据
     */
    public ExampleVO query(ExampleDTO dto) {
        try {
            // 参数校验
            if (dto == null || StringUtils.isEmpty(dto.getId())) {
                log.warn("Invalid parameters. dto={}", JSON.toJSONString(dto));
                throw new WebException(HttpCode.PARAM_ERROR);
            }

            // 从缓存中获取
            ExampleData data = exampleRedis.getExampleData(dto.getId());
            if (data == null) {
                // 缓存未命中，从数据库获取
                data = exampleDao.getExampleById(dto.getId());
                if (data == null) {
                    log.warn("Example not found. id={}", dto.getId());
                    throw new WebException(HttpCode.EMPTY_DATA);
                }
                // 存入缓存
                exampleRedis.saveExampleData(data);
            }

            // 转换为VO
            ExampleVO vo = convertToVO(data);

            return vo;
        } catch (WebException e) {
            // Web异常直接抛出，由全局异常处理器统一处理
            throw e;
        } catch (Exception e) {
            log.error("Failed to query example data. dto={}", JSON.toJSONString(dto), e);
            monitorSender.error(0, "example/query", e.getMessage());
            throw new WebException(HttpCode.SERVER_ERROR);
        }
    }

    /**
     * 查询示例数据列表
     *
     * @param dto 查询参数
     * @return 示例数据列表
     */
    public List<ExampleVO> queryList(ExampleDTO dto) {
        try {
            // 参数校验
            if (dto == null) {
                log.warn("Invalid parameters. dto is null");
                throw new WebException(HttpCode.PARAM_ERROR);
            }

            // 设置分页参数
            int page = dto.getPage();
            if (page < 0) {
                page = 0;
            }

            int pageSize = dto.getPageSize();
            if (pageSize <= 0) {
                pageSize = 20; // 默认每页20条
            }

            // 查询数据
            List<ExampleData> dataList = exampleDao.queryExampleList(dto.getType(), page, pageSize);
            if (CollectionUtils.isEmpty(dataList)) {
                return new ArrayList<>();
            }

            // 转换为VO列表
            List<ExampleVO> voList = new ArrayList<>(dataList.size());
            for (ExampleData data : dataList) {
                voList.add(convertToVO(data));
            }

            return voList;
        } catch (WebException e) {
            // Web异常直接抛出，由全局异常处理器统一处理
            throw e;
        } catch (Exception e) {
            log.error("Failed to query example list. dto={}", JSON.toJSONString(dto), e);
            monitorSender.error(0, "example/queryList", e.getMessage());
            throw new WebException(HttpCode.SERVER_ERROR);
        }
    }

    /**
     * 创建示例数据
     *
     * @param dto 创建参数
     * @return 创建的数据
     */
    public ExampleVO create(ExampleDTO dto) {
        try {
            // 参数校验
            if (dto == null || StringUtils.isEmpty(dto.getName())) {
                log.warn("Invalid parameters. dto={}", JSON.toJSONString(dto));
                throw new WebException(HttpCode.PARAM_ERROR);
            }

            // 检查是否已存在
            if (exampleDao.existsByName(dto.getName())) {
                log.warn("Example already exists. name={}", dto.getName());
                throw new WebException(HttpCode.DATA_ALREADY_EXISTS);
            }

            // 创建数据对象
            ExampleData data = new ExampleData();
            data.setName(dto.getName());
            data.setType(dto.getType());
            data.setDescription(dto.getDescription());
            data.setCreateTime(System.currentTimeMillis());
            data.setUpdateTime(System.currentTimeMillis());

            // 保存到数据库
            boolean success = exampleDao.saveExample(data);
            if (!success) {
                log.error("Failed to save example. data={}", JSON.toJSONString(data));
                throw new WebException(HttpCode.SERVER_ERROR);
            }

            // 更新缓存
            exampleRedis.saveExampleData(data);

            // 转换为VO
            ExampleVO vo = convertToVO(data);

            return vo;
        } catch (WebException e) {
            // Web异常直接抛出，由全局异常处理器统一处理
            throw e;
        } catch (Exception e) {
            log.error("Failed to create example. dto={}", JSON.toJSONString(dto), e);
            monitorSender.error(0, "example/create", e.getMessage());
            throw new WebException(HttpCode.SERVER_ERROR);
        }
    }

    /**
     * 更新示例数据
     *
     * @param dto 更新参数
     * @return 更新后的数据
     */
    public ExampleVO update(ExampleDTO dto) {
        try {
            // 参数校验
            if (dto == null || StringUtils.isEmpty(dto.getId())) {
                log.warn("Invalid parameters. dto={}", JSON.toJSONString(dto));
                throw new WebException(HttpCode.PARAM_ERROR);
            }

            // 检查是否存在
            ExampleData data = exampleDao.getExampleById(dto.getId());
            if (data == null) {
                log.warn("Example not found. id={}", dto.getId());
                throw new WebException(HttpCode.EMPTY_DATA);
            }

            // 更新字段
            if (!StringUtils.isEmpty(dto.getName())) {
                data.setName(dto.getName());
            }

            if (dto.getType() > 0) {
                data.setType(dto.getType());
            }

            if (!StringUtils.isEmpty(dto.getDescription())) {
                data.setDescription(dto.getDescription());
            }

            data.setUpdateTime(System.currentTimeMillis());

            // 保存到数据库
            boolean success = exampleDao.updateExample(data);
            if (!success) {
                log.error("Failed to update example. data={}", JSON.toJSONString(data));
                throw new WebException(HttpCode.SERVER_ERROR);
            }

            // 更新缓存
            exampleRedis.saveExampleData(data);

            // 转换为VO
            ExampleVO vo = convertToVO(data);

            return vo;
        } catch (WebException e) {
            // Web异常直接抛出，由全局异常处理器统一处理
            throw e;
        } catch (Exception e) {
            log.error("Failed to update example. dto={}", JSON.toJSONString(dto), e);
            monitorSender.error(0, "example/update", e.getMessage());
            throw new WebException(HttpCode.SERVER_ERROR);
        }
    }

    /**
     * 删除示例数据
     *
     * @param id 数据ID
     * @return 是否删除成功
     */
    public Boolean delete(String id) {
        try {
            // 参数校验
            if (StringUtils.isEmpty(id)) {
                log.warn("Invalid parameters. id is empty");
                throw new WebException(HttpCode.PARAM_ERROR);
            }

            // 检查是否存在
            ExampleData data = exampleDao.getExampleById(id);
            if (data == null) {
                log.warn("Example not found. id={}", id);
                throw new WebException(HttpCode.EMPTY_DATA);
            }

            // 从数据库删除
            boolean success = exampleDao.deleteExample(id);
            if (!success) {
                log.error("Failed to delete example. id={}", id);
                throw new WebException(HttpCode.SERVER_ERROR);
            }

            // 删除缓存
            exampleRedis.deleteExampleData(id);

            return true;
        } catch (WebException e) {
            // Web异常直接抛出，由全局异常处理器统一处理
            throw e;
        } catch (Exception e) {
            log.error("Failed to delete example. id={}", id, e);
            monitorSender.error(0, "example/delete", e.getMessage());
            throw new WebException(HttpCode.SERVER_ERROR);
        }
    }

    /**
     * 将数据对象转换为VO
     *
     * @param data 数据对象
     * @return VO对象
     */
    private ExampleVO convertToVO(ExampleData data) {
        if (data == null) {
            return null;
        }

        ExampleVO vo = new ExampleVO();
        vo.setId(data.getId());
        vo.setName(data.getName());
        vo.setType(data.getType());
        vo.setDescription(data.getDescription());
        vo.setCreateTime(data.getCreateTime());
        vo.setUpdateTime(data.getUpdateTime());

        return vo;
    }
}
```

## 命名规范

1. Service类名应以Service结尾，如UserService、OrderService。
2. 方法名应使用动词+名词形式，表示对资源的操作，如getUser、createOrder。
3. 查询单个资源的方法通常命名为get或query开头，如getUser、queryProduct。
4. 查询列表的方法通常命名为list、query或getList开头，如listUsers、queryProducts。
5. 创建资源的方法通常命名为create或add开头，如createUser、addProduct。
6. 更新资源的方法通常命名为update开头，如updateUser、updateProduct。
7. 删除资源的方法通常命名为delete或remove开头，如deleteUser、removeProduct。

## 代码规范

1. Service类应使用@Service注解，而不是@Component。
2. 使用@Slf4j注解进行日志记录，而不是手动创建Logger。
3. 使用构造函数注入或@Autowired注入依赖。
4. 所有公共方法都应该有详细的JavaDoc注释。
5. 方法应该有适当的参数校验和异常处理。
6. 业务异常应该通过抛出WebException来处理，由全局异常处理器统一处理。
7. 使用日志记录关键操作和错误信息。
8. 长方法应适当拆分为更小的私有方法。
9. 对于复杂业务逻辑，应该添加必要的注释说明。

## 事务处理

对于需要事务支持的方法，应添加@Transactional注解：

```java
@Transactional(rollbackFor = Exception.class)
public ExampleVO create(ExampleDTO dto) {
    // 业务逻辑
}
```

注意事项：
1. 事务方法应该是public的，否则事务不会生效。
2. 通常应指定rollbackFor = Exception.class，使所有异常都回滚事务。
3. 避免在事务方法中调用其他事务方法，可能导致事务行为不符合预期。

## 缓存处理

1. 查询操作通常应先查缓存，缓存未命中再查数据库。
2. 写操作（创建、更新、删除）应同时更新或删除相关缓存。
3. 缓存操作应该封装在专门的Redis类中，Service不应直接操作Redis。

## 异常处理

Service应该将可预见的业务异常封装为WebException，由全局异常处理器统一处理。常见模式如下：

```java
try {
    // 业务逻辑
} catch (WebException e) {
    // Web异常直接抛出，由全局异常处理器统一处理
    throw e;
} catch (Exception e) {
    // 记录日志
    log.error("操作失败", e);
    // 发送监控告警
    monitorSender.error(0, "example/operation", e.getMessage());
    // 转换为Web异常抛出
    throw new WebException(HttpCode.SERVER_ERROR);
}
```

[返回模板目录](project_templates.md)

[返回首页](../index.md)