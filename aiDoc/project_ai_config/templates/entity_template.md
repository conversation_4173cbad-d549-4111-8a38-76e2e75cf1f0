[← 返回项目AI配置](../index.md) | [模板导航](project_templates.md) | **Entity模板**

# 实体类模板

本文档提供了创建实体类的标准模板，包括数据库实体类(Entity/Data)、数据传输对象(DTO)、视图对象(VO)和业务对象(BO)。

## 项目调用链

项目遵循以下严格的调用链顺序：

```
Controller -> Service -> Redis缓存 -> DAO -> Mapper -> XML -> 数据库
```

这种调用链确保了系统架构的清晰性和职责分离，每一层只能调用其直接下层组件，不允许跨层调用。

## 使用Lombok简化实体类

项目中推荐使用Lombok的注解来简化实体类的编写，避免冗长的getter/setter代码。

```java
package com.quhong.data;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @since 2023/01/01 10:00
 */
@Data                    // 自动生成getter、setter、equals、hashCode和toString方法
@NoArgsConstructor       // 自动生成无参构造函数
@AllArgsConstructor      // 自动生成包含所有字段的构造函数
public class ExampleData {

    /**
     * 主键ID - 必须使用@Id和@GeneratedValue注解
     */
    @Id
    @GeneratedValue(generator = "JDBC")
    private String id;

    private String name;

    /**
     * 枚举类型字段的说明
     * @see com.quhong.enums.ExampleTypeEnum
     */
    private Integer type;
}
```

常用Lombok注解说明：

1. `@Data`：自动生成getter/setter、equals()、hashCode()和toString()方法
2. `@Getter/@Setter`：只生成getter或setter方法
3. `@NoArgsConstructor`：生成无参构造函数
4. `@AllArgsConstructor`：生成包含所有字段的构造函数
5. `@RequiredArgsConstructor`：为所有标记为final或@NonNull的字段生成构造函数
6. `@Builder`：启用流式构建API
7. `@Slf4j`：自动创建一个名为log的SLF4J Logger实例

## 数据库实体类(Entity/Data)

数据库实体类用于映射数据库表结构，通常放在dao.datas包下。

```java
package com.quhong.dao.datas;

import lombok.Data;
import java.io.Serializable;
import javax.persistence.Id;
import javax.persistence.GeneratedValue;

/**
 * @ClassName ExampleData
 * @Descripion 示例数据实体类，对应example_table表
 * <AUTHOR> <EMAIL>
 * @Date 2023/01/01 10:00 上午
 * @Version 1.0
 **/
@Data
public class ExampleData implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 主键ID
     * 数据库实体类的主键必须使用@Id和@GeneratedValue注解
     */
    @Id
    @GeneratedValue(generator = "JDBC")
    private String id;

    /**
     * 名称
     */
    private String name;

    /**
     * 类型
     * 1-类型一
     * 2-类型二
     * 3-类型三
     * @see com.quhong.enums.ExampleTypeEnum
     */
    private Integer type;

    /**
     * 描述信息
     */
    private String description;

    /**
     * 创建时间（毫秒时间戳）
     */
    private Long createTime;

    /**
     * 更新时间（毫秒时间戳）
     */
    private Long updateTime;

    /**
     * 用户ID
     * 引用其他实体必须使用@see注解标注完整类名
     * @see com.quhong.dao.datas.UserData
     */
    private Long userId;
}
```

## 实体类关联关系示例

```java
package com.quhong.dao.datas;

import lombok.Data;
import java.io.Serializable;
import javax.persistence.Id;
import javax.persistence.GeneratedValue;
import javax.persistence.ManyToOne;
import javax.persistence.JoinColumn;
import javax.persistence.OneToMany;
import java.util.List;

/**
 * @ClassName OrderData
 * @Descripion 订单实体类，对应order表，展示各种关联关系
 * <AUTHOR> <EMAIL>
 * @Date 2023/01/01 10:00 上午
 * @Version 1.0
 **/
@Data
public class OrderData implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 主键ID
     * 数据库实体类的主键必须使用@Id和@GeneratedValue注解
     */
    @Id
    @GeneratedValue(generator = "JDBC")
    private String id;

    /**
     * 订单编号
     */
    private String orderNo;

    /**
     * 用户ID
     * @see com.quhong.dao.datas.UserData
     */
    private Long userId;

    /**
     * 用户信息（多对一关系）
     * 引用其他实体必须使用@see注解标注完整类名
     * @see com.quhong.dao.datas.UserData
     */
    @ManyToOne
    @JoinColumn(name = "user_id", insertable = false, updatable = false)
    private UserData user;

    /**
     * 订单项列表（一对多关系）
     * 引用其他实体必须使用@see注解标注完整类名
     * @see com.quhong.dao.datas.OrderItemData
     */
    @OneToMany(mappedBy = "order")
    private List<OrderItemData> orderItems;

    // ... 其他字段
}
```

## 数据传输对象(DTO)

DTO用于接收前端请求参数，通常放在data.dto包下。可能继承自特定基类如ProtoDTO，用于处理协议转换。

```java
package com.quhong.data.dto;

import com.quhong.common.data.ProtoDTO;
import lombok.Data;
import javax.validation.constraints.Min;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;

/**
 * @ClassName UserBindModifyDTO
 * @Descripion 用户绑定信息修改请求对象
 * <AUTHOR> <EMAIL>
 * @Date 2023/01/01 10:00 上午
 * @Version 1.0
 **/
@Data
public class UserBindModifyDTO extends ProtoDTO {

    /**
     * 绑定值（邮箱或手机号）
     */
    @NotBlank(message = "绑定值不能为空")
    @Size(max = 64, message = "绑定值长度不能超过64个字符")
    private String bindValue;

    /**
     * 绑定类型 0-邮箱验证 1-手机号验证
     * 引用枚举或常量必须使用@see注解标注完整类名
     * @see com.quhong.constant.ActorBindConstant
     */
    @Min(value = 0, message = "绑定类型不能小于0")
    private int verifyType;

    /**
     * 绑定场景
     * 引用枚举或常量必须使用@see注解标注完整类名
     * @see com.quhong.constant.ActorBindConstant
     */
    private int scene;

    @Override
    protected void doFromBody(byte[] bytes) throws Exception {
        // 协议转换逻辑
    }

    @Override
    protected byte[] doToBody() throws Exception {
        return new byte[0];
    }
}
```

## 视图对象(VO)

VO用于返回数据给前端，通常放在data.vo包下。可能继承自特定基类如ProtoVO，用于处理协议转换。

```java
package com.quhong.data.vo;

import com.quhong.common.data.ProtoVO;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;

/**
 * @ClassName UserBindInfoVO
 * @Descripion 用户绑定信息视图对象
 * <AUTHOR> <EMAIL>
 * @Date 2023/01/01 10:00 上午
 * @Version 1.0
 **/
@Slf4j
@Data
public class UserBindInfoVO extends ProtoVO {

    /**
     * 用户邮箱
     */
    private String email;

    /**
     * 邮箱验证状态 1-未验证 2-已验证
     */
    private int emailStatus;

    /**
     * 邮箱奖励类型 0-无赠送 1-金币 2-vip
     */
    private int emailRewardType;

    /**
     * 奖励图片URL
     */
    private String emailRewardImgUrl;

    /**
     * 绑定的手机号
     */
    private String phone;

    /**
     * 手机验证状态 1-未验证 2-已验证
     */
    private int phoneStatus;

    @Override
    protected void doFromBody(byte[] bytes) throws Exception {
        // 协议转换逻辑
    }

    @Override
    protected byte[] doToBody() throws Exception {
        // 返回协议转换结果
        return null;
    }
}
```

## 业务对象(BO)

BO用于在业务层之间传递数据，通常放在data.bo包下。不直接暴露给前端，仅用于内部业务逻辑处理。

```java
package com.quhong.data.bo;

import lombok.Data;
import java.io.Serializable;

/**
 * @ClassName UserInviteResBO
 * @Descripion 用户邀请结果业务对象
 * <AUTHOR> <EMAIL>
 * @Date 2023/01/01 10:00 上午
 * @Version 1.0
 **/
@Data
public class UserInviteResBO implements Serializable {
    /**
     * 注册时间
     */
    private long registerTime;

    /**
     * 用户名称
     */
    private String name;

    /**
     * 用户充值金额
     */
    private long consume;

    /**
     * 充值次数
     */
    private int rechargeTimes;

    /**
     * 收入钻石数量
     */
    private int incomeDiamonds;

    /**
     * 充值用户ID
     * 引用其他实体时应使用@see注解标注完整类名
     * @see com.quhong.dao.datas.UserData
     */
    private String rechargeUid;

    // 业务处理方法（如果有）
    public boolean isValidInvite() {
        return rechargeTimes > 0 && consume > 0;
    }
}
```

## 分页响应对象(PageVO)

分页响应对象用于封装分页查询结果，通常放在data.vo包下。

```java
package com.quhong.data.vo;

import lombok.Data;
import java.io.Serializable;
import java.util.List;

/**
 * @ClassName PageVO
 * @Descripion 分页响应对象，用于封装分页查询结果
 * <AUTHOR> <EMAIL>
 * @Date 2023/01/01 10:00 上午
 * @Version 1.0
 **/
@Data
public class PageVO<T> implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 当前页码，从0开始
     */
    private Integer pageNum;

    /**
     * 每页大小
     */
    private Integer pageSize;

    /**
     * 总记录数
     */
    private Long total;

    /**
     * 总页数
     */
    private Integer pages;

    /**
     * 当前页的数据列表
     */
    private List<T> list;

    // 构造方法
    public PageVO() {
    }

    public PageVO(Integer pageNum, Integer pageSize, Long total, List<T> list) {
        this.pageNum = pageNum;
        this.pageSize = pageSize;
        this.total = total;
        this.list = list;
        this.pages = (int) Math.ceil((double) total / pageSize);
    }
}
```

## 命名规范

### 数据库实体类(Entity/Data)

- 类名应与数据库表名一致，采用驼峰命名法，如`UserInfo`对应`user_info`表
- 类名通常以Data或Entity结尾，如`UserData`或`UserEntity`
- 属性名应与数据库字段名一致，采用驼峰命名法，如`createTime`对应`create_time`字段
- **主键必须使用@Id和@GeneratedValue(generator = "JDBC")注解标记**，这是强制要求
- **带有运营操作的表，必须有ctime创建时间（bigint），单位/秒，mtime更新时间（bigint）单位/秒；operator操作人（varchar）长度64**，这是强制要求
- 应实现Serializable接口，便于序列化
- 引用其他实体或枚举类时，必须使用@see注解标注完整类名
- 使用@Data注解简化getter/setter等方法



### 视图对象(VO)

- 类名以VO结尾，如`UserBindInfoVO`
- 可以包含格式化的字段，如createTimeStr、updateTimeStr等
- 应使用@Data注解简化getter/setter等方法
- 引用其他VO或枚举时，必须使用@see注解标注完整类名
- 如需继承协议转换基类，应实现相应的转换方法

### 业务对象(BO)

- 类名以BO结尾，如`UserInviteResBO`
- 仅用于业务层之间传递数据，不直接暴露给前端
- 可以包含业务处理方法
- 应使用@Data注解简化getter/setter等方法
- 可以包含业务逻辑验证方法
- 引用其他实体时，必须使用@see注解标注完整类名
- 根据需要可以实现特定接口，如IProto等

## 注释规范

- 类注释应包含类名、描述、作者、日期和版本信息
- 属性注释应说明其用途，对于枚举类型的属性，应列出所有可能的值
- 重写的方法（如doFromBody、doToBody）应保留@Override注解
- 枚举值应有清晰的注释说明
- 引用关系必须使用@see注解标注被引用的类的完整类名

## 验证注解

DTO类中常用的验证注解：

- `@NotNull`：值不能为null
- `@NotBlank`：字符串不能为null且去除两端空白字符后不能为空字符串
- `@NotEmpty`：集合不能为null且不能为空
- `@Size`：长度或大小在指定范围内
- `@Min`：数值最小值
- `@Max`：数值最大值
- `@Pattern`：字符串需匹配指定的正则表达式
- `@Email`：字符串需符合Email格式
- `@Past`：日期需在当前时间之前
- `@Future`：日期需在当前时间之后

示例：

```java
@NotBlank(message = "名称不能为空")
@Size(max = 50, message = "名称长度不能超过50个字符")
private String name;
```

## 转换方法

在项目中，通常需要在不同实体类之间进行转换。推荐使用Spring的BeanUtils、MapStruct工具或手动编写转换方法。

### 使用BeanUtils

```java
// 单个对象转换
ExampleVO vo = new ExampleVO();
BeanUtils.copyProperties(data, vo);

// 批量对象转换通常需要手动实现
List<ExampleVO> voList = dataList.stream()
    .map(data -> {
        ExampleVO vo = new ExampleVO();
        BeanUtils.copyProperties(data, vo);
        return vo;
    })
    .collect(Collectors.toList());
```

### 手动转换

```java
/**
 * 将数据对象转换为视图对象
 *
 * @param data 数据对象
 * @return 视图对象
 */
private ExampleVO convertToVO(ExampleData data) {
    if (data == null) {
        return null;
    }

    ExampleVO vo = new ExampleVO();
    vo.setId(data.getId());
    vo.setName(data.getName());
    vo.setType(data.getType());
    vo.setDescription(data.getDescription());
    vo.setCreateTime(data.getCreateTime());
    vo.setUpdateTime(data.getUpdateTime());

    // 处理特殊字段
    vo.setTypeName(getTypeName(data.getType()));
    vo.setCreateTimeStr(formatTime(data.getCreateTime()));
    vo.setUpdateTimeStr(formatTime(data.getUpdateTime()));

    // 处理关联对象
    if (data.getUserId() != null) {
        UserData userData = userDao.getUserById(data.getUserId());
        if (userData != null) {
            vo.setUser(convertToUserVO(userData));
        }
    }

    return vo;
}
```

## 最佳实践

- 使用Lombok的@Data注解简化实体类的编写，避免冗长的模板代码
- 不同类型的实体类应该明确分开，避免混用
- DTO类应只包含与前端请求相关的字段
- VO类应包含所有需要返回给前端的字段
- BO类应专注于业务逻辑所需的数据结构
- **数据库实体类的主键必须使用@Id和@GeneratedValue(generator = "JDBC")注解标记**
- 引用其他实体、枚举或常量时，必须使用@see注解标注完整类名
- 考虑使用建造者模式(@Builder)创建复杂对象
- 枚举类型的属性应该使用Integer或String类型，避免使用枚举类型引起的序列化问题
- 日期类型的属性推荐使用Long类型表示毫秒时间戳，同时提供格式化的字符串字段
- 对于集合类属性，初始化为空集合而不是null，避免空指针异常
- 严格遵循调用链顺序：Controller -> Service -> Redis -> DAO -> Mapper -> XML

[返回模板目录](templates_index.md)

[返回首页](../index.md)