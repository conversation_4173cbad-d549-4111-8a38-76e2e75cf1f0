# 怪兽狩猎活动系统

## 概述

怪兽狩猎活动是一个多人协作的活动系统，玩家通过消耗宝剑攻击怪兽，造成伤害并获得奖励。

## 核心功能

### 1. 活动机制
- **怪兽血量**: 怪兽有固定的最大血量（默认1,000,000）
- **宝剑系统**: 玩家通过Greedy下注获得宝剑（20,000钻石 = 1把宝剑）
- **攻击系统**: 消耗宝剑攻击怪兽，每把宝剑造成100-500随机伤害
- **暴击系统**: 10%概率触发暴击，造成3倍伤害，每日最多3次
- **协作机制**: 全服玩家共同攻击同一只怪兽

### 2. 奖励系统
- **阶段奖励**: 全服总伤害达到25%、50%、75%、100%时系统自动给所有参与用户发放奖励
- **排行榜奖励**: 活动结束后Top3玩家获得特殊奖励
- **暴击奖励**: 触发暴击时立即获得奖励
- **击败奖励**: 怪兽被击败时重置血量（可扩展击败奖励）

### 3. 数据统计
- **个人数据**: 宝剑数量、造成伤害、暴击次数
- **全服数据**: 总伤害、怪兽血量、排行榜
- **实时更新**: 所有数据实时同步更新
- **自动奖励**: 阶段奖励自动发放，无需手动领取

## API接口

### 获取活动信息
```
GET /monster-hunting/info?activityId={activityId}&uid={uid}
```

### 攻击怪兽
```
POST /monster-hunting/attack?activityId={activityId}&uid={uid}&num={swordCount}
```



### 获取奖励记录
```
GET /monster-hunting/reward-record?activityId={activityId}&uid={uid}&page={page}
```

## 数据结构

### MonsterHuntingVO
```json
{
  "startTime": 1234567890,
  "endTime": 1234567890,
  "swordNum": 10,
  "monsterHp": 800000,
  "maxMonsterHp": 1000000,
  "monsterStatus": 2,
  "totalDamage": 200000,
  "userDamage": 5000,
  "stageRewards": [...],
  "topRewards": [...],
  "rankingList": [...],
  "myRank": {...},
  "rewardList": [...],
  "criticalCount": 2,
  "maxCriticalCount": 3,
  "lastAttackDamage": 1500,
  "isCritical": true
}
```

## 配置说明

### 活动参数配置
- 所有配置参数都在 MonsterHuntingService 类中定义为常量
- 可根据需要调整伤害范围、暴击概率、奖励配置等

### Redis存储结构
- 用户数据: `activity:{activityId}:user:{uid}:{dataType}`
- 公共数据: `activity:{activityId}:common:{dataType}`
- 排行榜: 使用Redis Sorted Set维护

## 部署说明

### 1. 数据库配置
- 确保MongoDB中有相应的活动配置数据
- 确保Redis可用于数据缓存



### 2. 奖励配置
- 在ResourceKeyHandlerService中配置各种奖励的具体内容
- 确保奖励Key与配置文件中的Key匹配

## 扩展功能

### 1. 多怪兽支持
- 可扩展支持多个不同的怪兽
- 每个怪兽有不同的血量和奖励配置

### 2. 技能系统
- 可添加不同类型的武器和技能
- 不同武器有不同的伤害和特效

### 3. 公会协作
- 可添加公会间的协作和竞争机制
- 公会排行榜和公会奖励

### 4. 实时通知
- WebSocket推送实时战斗信息
- 重要事件（怪兽击败、暴击等）的全服广播

## 注意事项

1. **并发控制**: 使用分布式锁确保数据一致性
2. **性能优化**: 合理使用Redis缓存，避免频繁数据库操作
3. **异常处理**: 完善的异常处理和日志记录
4. **数据校验**: 严格的参数校验防止作弊
5. **活动状态**: 确保只在活动期间内允许操作

## 测试建议

1. **单元测试**: 测试各个服务方法的逻辑正确性
2. **集成测试**: 测试完整的攻击流程和奖励发放
3. **压力测试**: 测试高并发情况下的系统稳定性
4. **边界测试**: 测试各种边界条件和异常情况
