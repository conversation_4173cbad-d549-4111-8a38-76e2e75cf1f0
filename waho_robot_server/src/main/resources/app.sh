#!/bin/sh
SHELL_PROG=./app.sh
ulimit -n 65535
DIR=$(cd `dirname $0`; pwd)
APP_NAME=$DIR/room_service
start() {
        echo "[`date`] Begin starting $APP_NAME ... "
        nohup java -server -Xmx1g -Xms512m -Xmn256m -XX:+UseG1GC -XX:ParallelGCThreads=2 -XX:ConcGCThreads=2 -Xloggc:./gc.log -XX:+PrintGCDetails -jar $APP_NAME.jar >out.out 2>&1 &
        if [ $? -eq 0 ]
        then
			echo "[`date`] Startup $APP_NAME success."
			return 0
        else
			echo "[`date`] Startup $APP_NAME fail."
			return 1
        fi
}

stop() {
    echo "[`date`] Begin stop $APP_NAME... "
    PROGID=`ps -ef|grep "$APP_NAME"|grep -v "grep"|sed -n '1p'|awk '{print $2" "$3}'`
	if [ -z "$PROGID" ]
	then
		echo "[`date`] Stop $APP_NAME fail, service is not exist."
		return 1
	fi

   kill -9 $PROGID
   #kill -s SIGUSR2 $PROGID
    #kill -s USR2 $PROGID

    sleep 2s
    PROGID=`ps -ef|grep "$APP_NAME"|grep -v "grep"|sed -n '1p'|awk '{print $2" "$3}'`
    if [ -z "$PROGID" ]
    #if [ $? -eq 0 ]
    then
		echo "[`date`] Stop $APP_NAME success."
		return 0
    else
		echo "[`date`] Stop $APP_NAME fail."
		return 1
    fi
}


case "$1" in
start)
  start
  exit $?
  ;;
stop)
  stop
  exit $?
  ;;
restart)
  stop
  start
  exit $?
  ;;
debug)
  debug
  exit $?
  ;;
*)
  echo "[`date`] Usage: $SHELL_PROG {start|debug|stop|restart}"
  exit 1
  ;;
esac

