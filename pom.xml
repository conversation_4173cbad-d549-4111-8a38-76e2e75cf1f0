<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>
    <parent>
        <groupId>org.springframework.boot</groupId>
        <artifactId>spring-boot-starter-parent</artifactId>
        <version>3.1.0</version>
        <relativePath/>
    </parent>
    <groupId>com.quhong</groupId>
    <artifactId>waho_java_game</artifactId>
    <version>1.0.1</version>
    <name>waho_java_game</name>

    <properties>
        <java.version>17</java.version>
        <launch.class>com.quhong.GameApplication</launch.class>
        <waho.core.version>1.0-SNAPSHOT</waho.core.version>
    </properties>

    <dependencies>
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-actuator</artifactId>
        </dependency>
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-web</artifactId>
        </dependency>
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-test</artifactId>
            <scope>test</scope>
        </dependency>
        <dependency>
            <groupId>com.quhong</groupId>
            <artifactId>quhong-common-mq</artifactId>
            <version>${waho.core.version}</version>
        </dependency>
        <dependency>
            <groupId>com.quhong</groupId>
            <artifactId>waho_data</artifactId>
            <version>${waho.core.version}</version>
        </dependency>
        <dependency>
            <groupId>com.quhong</groupId>
            <artifactId>waho_k8s</artifactId>
            <version>${waho.core.version}</version>
        </dependency>
        <dependency>
            <groupId>com.quhong</groupId>
            <artifactId>waho_feign</artifactId>
            <version>${waho.core.version}</version>
        </dependency>
        <dependency>
            <groupId>tech.sud.mgp.auth</groupId>
            <artifactId>sud-mgp-auth-java</artifactId>
            <version>1.0.4</version>
            <scope>system</scope>
            <systemPath>${pom.basedir}/lib/sud-mgp-auth-java-1.0.4.jar</systemPath>
        </dependency>
        <dependency>
            <groupId>com.quhong</groupId>
            <artifactId>waho_analysis</artifactId>
            <version>${waho.core.version}</version>
        </dependency>
        <dependency>
            <groupId>com.auth0</groupId>
            <artifactId>java-jwt</artifactId>
            <version>3.10.3</version>
        </dependency>
        <dependency>
            <groupId>org.projectlombok</groupId>
            <artifactId>lombok</artifactId>
            <version>1.18.24</version>
        </dependency>
        <dependency>
            <groupId>cn.hutool</groupId>
            <artifactId>hutool-crypto</artifactId>
            <version>5.8.2</version>
        </dependency>
        <dependency>
            <groupId>com.google.guava</groupId>
            <artifactId>guava</artifactId>
            <version>31.1-jre</version>
        </dependency>
    </dependencies>

    <build>
        <finalName>app</finalName>
        <plugins>
            <plugin>
                <groupId>org.springframework.boot</groupId>
                <artifactId>spring-boot-maven-plugin</artifactId>
                <configuration>
                    <mainClass>${launch.class}</mainClass>
                    <includeSystemScope>true</includeSystemScope>
                </configuration>
                <executions>
                    <execution>
                        <goals>
                            <goal>repackage</goal>
                        </goals>
                    </execution>
                </executions>
            </plugin>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-install-plugin</artifactId>
                <version>3.0.1</version>
                <executions>
                    <execution>
                        <id>copy-sud-lib</id>
                        <phase>package</phase>
                        <configuration>
                            <file>${basedir}/lib/sud-mgp-auth-java-1.0.4.jar</file>
                            <groupId>tech.sud.mgp.auth</groupId>
                            <artifactId>sud-mgp-auth-java</artifactId>
                            <version>1.0.4</version>
                            <packaging>jar</packaging>
                            <generatePom>true</generatePom>
                        </configuration>
                        <goals>
                            <goal>install-file</goal>
                        </goals>
                    </execution>
                </executions>
            </plugin>
        </plugins>
    </build>
</project>
