<?xml version="1.0" encoding="UTF-8"?>

<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>
    <groupId>com.quhong</groupId>
    <artifactId>waho_java_core</artifactId>
    <packaging>pom</packaging>
    <version>1.0-SNAPSHOT</version>
    <modules>
        <!-- 并发任务的封装，包含任务队列，线程池，时间服务，分布式锁，分布式凭证等 -->
        <module>quhong_task</module>
        <!-- 基于netty封装的TCP网络框架，包含server和client-->
        <module>quhong_netty</module>
        <!-- mq -->
        <module>quhong-common-mq</module>
        <!-- protobuf文件 -->
        <module>waho_message</module>
        <!-- waho基础部分, 包含redis的一些处理，为waho所有公共部分  -->
        <module>waho_base</module>
        <!-- web服务的公共基础部分 -->
        <module>waho_web</module>
        <!-- waho数据处理部分，包含基于mysql，mongodb的所有公共业务 -->
        <module>waho_data</module>
        <!-- 客户端接口调用 -->
        <module>waho_api</module>
        <!-- 客户端接口调用(feign) -->
        <module>waho_feign</module>
        <!-- 数据报表分析 -->
        <module>waho_analysis</module>
        <!-- k8s -->
        <module>waho_k8s</module>
    </modules>
    <name>waho_java_core</name>
    <properties>
        <java.version>17</java.version>
        <project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
        <waho.core.version>1.0-SNAPSHOT</waho.core.version>
        <spring.core.version>6.0.9</spring.core.version>
        <spring-boot.version>3.1.0</spring-boot.version>
        <fastJson.version>1.2.83</fastJson.version>
        <logback.version>1.4.7</logback.version>
        <spring-data-redis.version>3.0.5</spring-data-redis.version>
        <jedis.version>4.3.2</jedis.version>
        <http.client.version>4.5.7</http.client.version>
        <http.nio.version>4.4.14</http.nio.version>
        <http.nio.client.version>4.1.4</http.nio.client.version>
        <protobuf.version>3.7.1</protobuf.version>
        <netty.version>4.1.33.Final</netty.version>
        <mybatis-spring.version>3.0.2</mybatis-spring.version>
        <druid.version>1.2.18</druid.version>
        <caffeine.version>3.1.6</caffeine.version>
        <mysql.version>8.0.33</mysql.version>
        <mongodb.version>4.1.0</mongodb.version>
        <mongodb.driver.version>4.9.1</mongodb.driver.version>
        <spring.rabbitmq.version>3.0.4</spring.rabbitmq.version>
        <mybatis-plus.version>3.5.4.1</mybatis-plus.version>
    </properties>
    <profiles>
        <profile>
            <id>javax.annotation</id>
            <activation>
                <jdk>[11,)</jdk>
            </activation>
            <dependencies>
                <dependency>
                    <groupId>jakarta.annotation</groupId>
                    <artifactId>jakarta.annotation-api</artifactId>
                    <version>2.1.1</version>
                </dependency>
            </dependencies>
        </profile>
    </profiles>
    <build>
        <pluginManagement>
            <plugins>
                <!--设置Java编译级别 -->
                <plugin>
                    <groupId>org.apache.maven.plugins</groupId>
                    <artifactId>maven-compiler-plugin</artifactId>
                    <version>3.11.0</version>
                    <configuration>
                        <source>${java.version}</source>
                        <target>${java.version}</target>
                        <compilerArgs>
                            <compilerArg>
                                -parameters
                            </compilerArg>
                        </compilerArgs>
                    </configuration>
                </plugin>
            </plugins>
        </pluginManagement>
        <plugins>
            <!--打包生成源码 -->
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-source-plugin</artifactId>
                <version>3.2.1</version>
                <executions>
                    <execution>
                        <id>attach-sources</id>
                        <phase>verify</phase>
                        <goals>
                            <goal>jar-no-fork</goal>
                        </goals>
                    </execution>
                </executions>
            </plugin>
        </plugins>
    </build>
</project>
