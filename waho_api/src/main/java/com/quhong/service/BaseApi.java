package com.quhong.service;

import com.quhong.core.web.WebClient;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import java.util.HashMap;
import java.util.Map;

public class BaseApi {

    /**
     * 容器
     */
    @Value("${api.slb.path}")
    protected String apiSlbPath;

    /**
     * 属主机
     */
    @Value("${api.alb.path}")
    protected String apiAlbPath;

    @Value("${api.path}")
    protected String apiPath;
    @Autowired
    protected WebClient webClient;

    protected Map<String, String> headerMap;

    public BaseApi(){
        initHeaders();
    }

    protected void initHeaders(){
        headerMap = new HashMap<>();
        headerMap.put("Content-Type", "application/json");
    }
}
