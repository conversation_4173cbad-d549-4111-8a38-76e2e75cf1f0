apiVersion: networking.k8s.io/v1
kind: Ingress
metadata:
  namespace: devops
  name: waho-java-game
  annotations:
    alb.ingress.kubernetes.io/group.name: waho
    kubernetes.io/ingress.class: alb
    alb.ingress.kubernetes.io/scheme: internet-facing
    alb.ingress.kubernetes.io/target-type: ip
    alb.ingress.kubernetes.io/target-group-attributes: stickiness.enabled=true,stickiness.lb_cookie.duration_seconds=600
    alb.ingress.kubernetes.io/listen-ports: '[{"HTTPS": 443}, {"HTTP": 80}, {"HTTP": 8080}]'
    alb.ingress.kubernetes.io/certificate-arn: arn:aws:acm:eu-central-1:462636422633:certificate/cac3a38d-f669-47f2-9737-023c464b503a
    alb.ingress.kubernetes.io/healthcheck-protocol: HTTP
    alb.ingress.kubernetes.io/backend-protocol: HTTP
    alb.ingress.kubernetes.io/healthcheck-path: /system/health_check
    alb.ingress.kubernetes.io/healthcheck-interval-seconds: '5'
    alb.ingress.kubernetes.io/healthcheck-timeout-seconds: '2'  #健康检查超时时间设置为2s
spec:
  rules:
    - host: apiv1.opswaho.com
      http:
        paths:
          - path: /game/
            pathType: Prefix
            backend:
              service:
                name: waho-java-game
                port:
                  number: 8080
---
apiVersion: networking.k8s.io/v1
kind: Ingress
metadata:
  namespace: devops
  name: inner-waho-java-game
  annotations:
    alb.ingress.kubernetes.io/group.name: waho-test-inner
    alb.ingress.kubernetes.io/load-balancer-name: test-inner
    kubernetes.io/ingress.class: alb
    alb.ingress.kubernetes.io/scheme: internal
    alb.ingress.kubernetes.io/target-type: ip
    alb.ingress.kubernetes.io/listen-ports: '[{"HTTP": 80}]'
    alb.ingress.kubernetes.io/healthcheck-protocol: HTTP
    alb.ingress.kubernetes.io/backend-protocol: HTTP
    alb.ingress.kubernetes.io/healthcheck-path: /system/health_check
    alb.ingress.kubernetes.io/healthcheck-interval-seconds: '5'
    alb.ingress.kubernetes.io/healthcheck-timeout-seconds: '2'  #健康检查超时时间设置为2s
spec:
  rules:
    - http:
        paths:
          - path: /inner/game/
            pathType: Prefix
            backend:
              service:
                name: waho-java-game
                port:
                  number: 8080
