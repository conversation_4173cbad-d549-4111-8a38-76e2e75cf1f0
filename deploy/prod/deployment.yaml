apiVersion: apps/v1
kind: Deployment
metadata:
  labels:
    app.kubernetes.io/name: waho-java-detect
  name: waho-java-detect
  namespace: devops
spec:
  replicas: 1
  selector:
    matchLabels:
      app.kubernetes.io/name: waho-java-detect
  template:
    metadata:
      labels:
        app.kubernetes.io/name: waho-java-detect
    spec:
      serviceAccountName: dubbo-sa
      containers:
        - name: waho-java-detect
          image: ************.dkr.ecr.eu-central-1.amazonaws.com/waho-java-detect:master_latest
          imagePullPolicy: Always
          args: [ "-server",
                  "-jar",
                  "/opt/app.jar",
                  "--spring.profiles.active=prod" ]
          env:
            - name: "JAVA_TOOL_OPTIONS"
              value: "-XX:+UseContainerSupport -XX:InitialRAMPercentage=30.0 -XX:MaxRAMPercentage=75.0 -XX:+UseG1GC -XX:ParallelGCThreads=2 -XX:ConcGCThreads=2"
          livenessProbe:
            failureThreshold: 3
            httpGet:
              path: /system/health_check
              port: 8080
              scheme: HTTP
            periodSeconds: 10
            successThreshold: 1
            timeoutSeconds: 5
          ports:
            - containerPort: 8080
              name: http
              protocol: TCP
          readinessProbe:
            failureThreshold: 3
            httpGet:
              path: /system/health_check
              port: 8080
              scheme: HTTP
            periodSeconds: 10
            successThreshold: 1
            timeoutSeconds: 5
          resources:
            limits:
              memory: 1536Mi
            requests:
              memory: 1024Mi
          startupProbe:
            failureThreshold: 30
            httpGet:
              path: /system/health_check
              port: 8080
              scheme: HTTP
            periodSeconds: 5
            successThreshold: 1
            timeoutSeconds: 5
          lifecycle:
            preStop:
              exec:
                command: [ "/bin/sh","-c","sleep 20" ]
          volumeMounts:
            - mountPath: /opt/config
              name: java-config
            - name: logpath
              mountPath: /opt/logs/
      nodeSelector:
        kubernetes.io/os: linux
      restartPolicy: Always
      volumes:
        - name: java-config
          secret:
            defaultMode: 420
            secretName: java-prod-config
        - name: logpath
          emptyDir: { }
        - name: filebeatconf
          configMap:
            name: filebeatconf
            items:
              - key: filebeat.yml
                path: usr/share/filebeat/filebeat.yml
