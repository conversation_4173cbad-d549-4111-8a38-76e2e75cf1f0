syntax = "proto3"; // 指定protobuf版本
import "base.proto";
import "gift.proto";
import "lucky_wheel.proto";
import "uname.proto";
import "mic_info.proto";
import "lucky_gift.proto";
import "highlightText.proto";
import "ludo_player_info.proto";
import "video_info.proto";

option java_package = "com.quhong.proto";
option java_outer_classname = "WahoProtoRoom";

//进入房间 请求 cmdid = 2001 推送 cmdid = 2002
message EnterRoomMessage {
    MsgHeader header = 1;
    string content = 2;
    Uname uname = 3;
    int32 onlineNumber = 4; // 房间内在线人数
    int32 roomDevote = 5; // 房间贡献值
}

//离开房间请求  cmdid = 2003
message LeaveRoomMessage {
    MsgHeader header = 1;
}

message MsgInfo {
    string fromUid = 1; //发送者uid
    string uname = 2;
    string content = 3; //消息内容
    int32 msgType = 4; //消息类型
    int32 msgCategory = 5; //消息分类
    int32 sendTime = 6; //发送消息时间
    Uname uname2 = 7; //发送者基本信息
    int32 isRepeatMsg = 8; // 是否是重复消息 0不是 1是 2是重复消息的第一条 3重复消息的最后一条
    string fromLabel = 9; // 发送者公屏发言标签，例如：d-1,w-2,m-3
    int32 pkTeam = 10; // pk队伍1黄队 2蓝队
    string ownerAid = 11; // 房主aid
}

// 房间文字消息  请求 cmdid = 2005
message SendRoomMessage {
    MsgHeader header = 1;
    string uname = 2;
    string content = 3; //消息内容
    int32 msgType = 4; //消息类型
    int32 msgCategory = 5; //消息分类
	int32 slang = 6; // 1 英语 2 阿语 3 土耳其语
	int32 isRepeatMsg = 7; // 是否是重复消息 0不是 1是 2是重复消息的第一条 3重复消息的最后一条
    int32 pkTeam = 8; // pk队伍1黄队 2蓝队
    string ownerAid = 9; // 房主aid
}

// 房间文字消息 推送 cmdid = 2006
message SendRoomPushMessage {
    MsgHeader header = 1;
    repeated MsgInfo msgList = 2;
}

// 房间礼物消息 推送 cmdid 2008
message RoomGiftMessage {
    MsgHeader header = 1;
    repeated ReceiveInfo g_recevInfo_list = 2;
    SendInfo g_sendInfo = 3;
    GiftInfo g_gift_info = 4;
    int32 send_type = 5; //1 2 3
    int32 isAllMic = 6; // 是否发送给全麦位用户 1是
    int32 changeVoice = 7; // 是否是变声礼物 0否 1是 2清除变声
    int32 luckyGiftTimes = 8;  // 钻石版幸运礼物中奖数量，0未中奖
    int32 pkTeam = 9; // pk队伍1黄队 2蓝队
    string sendRoomId = 10;  // 送礼物房间id
    int32 svipExp = 11; // 获得svip经验值
    string ownerAid = 12; // 房主aid
}

//幸运转盘消息 推送 cmdid 2010
message LuckyWheelMessage {
    MsgHeader header = 1;
    Game game = 2;
    int32 game_status = 3; //0:游戏已停止  1：创建游戏   2：开始游戏   3：加入游戏
    int64 current_time = 4; //当前服务器时间 单位 秒
	Uname creator_uname = 5; // 创建者的信息
    int32 whell_leftnums = 6; //当前房间转盘剩余次数   game_status 等于 0 不用返回
    int32 autoClose = 7 ; // 1 游戏超时结束  0 不是游戏超时结束
}

//幸运宝箱消息  推送 cmdid 2012、2248、2250
message LuckyBoxMessage {
    MsgHeader header = 1;
    Uname uname = 2;
	string box_id = 3;
    string msg = 4;
    int32 box_type = 5; //0 钻石红包,1 礼物红包
    int32 box_num = 6;
    int32 valid_box = 7;
    int32 end_time = 8; // 红包过期时间
    int32 c_time = 9;
    int32 money = 10;
    RidInfo fromRidInfo = 11;
    string fromLabel = 12; // 发送者公屏发言标签，例如：d-1,w-2,m-3
}

//幸运数字消息 推送 cmdid 2014
message LuckyNumberMessage {
    MsgHeader header = 1;
    Uname uname = 2;
    int32 num = 3;
    int32 ret = 4; // 0 未中奖 1中奖
	  string luckyNum = 5;  // 格式化后的幸运数字
}

//骰子消息  推送 cmdid 2016
message DiceMessage {
    MsgHeader header = 1;
    Uname uname = 2;
    int32 num = 3;
}

// 解散房间 推送 cmdid 2018
message DismissMessage {
    MsgHeader header = 1;
    string content_en = 2;
    string content_ar = 3;
    int32 type = 4; // 1封禁房间
}

// 用户进出房间 推送 cmdid 2020
message RoomUserChangeMessage {
	MsgHeader header = 1;
	int32 type = 2; // 1 进入 2 离开(离开时vip_level和head为默认值)
	int32 vip_level = 3; // vip 等级
	string head = 4; // 头像
    int32 online_nums = 5; //房间总在线人数
	int32 identify = 6; // 0 无 1 vip 2 svip
	string name = 7; //名称
    int32 mystery = 8; // 1神秘人
}


//关注房主(215) 推送2022
message FollowOwnerMessage{
    MsgHeader header = 1;
    Uname from_name = 2;
    string fromLabel = 3; // 发送者公屏发言标签，例如：d-1,w-2,m-3
}

//授予播放音乐权限（直播房）(244)  推送2024
message OpenMusicLiveMessage {
    MsgHeader header = 1;
}

//授予摄像头权限（直播房）(246) 推送 2026
message OpenCamLiveMessage {
    MsgHeader header = 1;
}

//房主切换房间类型(220) 推送 2028
message ChangeTypeMessage {
    MsgHeader header = 1;
    int32 comp = 2;
    int32 room_type = 3; //1 直播 2 普通语音 3 音乐房
}

//成为房间会员(214)  推送  2030
message MemberMessage {
    MsgHeader header = 1;
    Uname name = 2;
}

//资料卡禁麦or解除禁麦(242 243)  推送2032
message MuteMicMessage {
    MsgHeader header = 1;
    int32 opt = 2;  //1:禁麦  2：解除禁麦
    string from_name = 3;
    string from_Img = 4;   //
	string to_uid = 5; //被禁止的人
	UserInfo opt_user = 6;
}

//锁麦or取消锁麦(237) 推送2034
message LockMicMessage {
    MsgHeader header = 1;
    string toUserID = 2;
    string from_name = 3;
    string from_Img = 4;
    int32 opt = 5;
    int32 index = 6;
}

//禁言(236 241)  推送2036
message OptBandTxtMessage {
    MsgHeader header = 1;
    string from_name = 2;
    string from_Img = 3;
    int32 opt = 4; // 1 禁言  0取消禁言
	UserInfo opt_user = 5;
	int32 official = 6; // 1官方管理员
}

//创建猜拳游戏(889)  推送2038
message CreateRoshamboMessage{
    MsgHeader header = 1;
    string from_name = 2;  //游戏创建者名字
    string from_head = 3;  //游戏创建者头像

}

//邀请上麦(238 239)  推送2040
message InviteMicMessage {
    MsgHeader header = 1;
    string from_name = 2;
	string fromHead = 3;
	int32 micIndex = 4; // 邀请上麦的麦位号
	int32 autoUpMic = 5; // 1自动上麦
}

//踢下麦(204 234) 推送2042
message RemoveSeatMessage {
    MsgHeader header = 1;
    string from_name = 2;
    string from_Img = 3;
    int32 reason = 4; // 0默认 1切换pk模式
}

//房间公告(722)  推送 2044
message AnnounceMessage {
    MsgHeader header = 1;
    string announce = 2;
	UserInfo opt_user = 3;
}

//关注房间(723)  推送 2046
message FollowRoomMessage {
    MsgHeader header = 1;
    string from_name = 2;
    string from_Img = 3;
}

//打开or关闭房间公屏消息(750 751)  推送2048
message OptChatMessage {
    MsgHeader header = 1;
    int32 opt = 2;   //1 关闭   1打开
	UserInfo opt_user = 3;
}

//打开or关闭房间发图片功能(752 753)  推送2050
message OptSendPicMessage {
    MsgHeader header = 1;
    int32 opt = 2;   //1 关闭   0打开
	UserInfo opt_user = 3;
}

//打开or关闭房间所有麦位声音(209 210) 推送2056
message OptAllMicMessage {
    MsgHeader header = 1;
    int32 opt = 2;  // 2 1
}

//emoji表情(727)      推送 2058
message EmojiMessage{
    MsgHeader header = 1;
    string icon_file = 2;
    int32 cycles =3; // 循环次数
}

//设置房间锁(240) 推送2060
message ChangePwdMessage{
    MsgHeader header = 1;
}

//发图片(2 send_picture) 推送2062
message SendPicMessage{
    MsgHeader header = 1;
    Uname name = 2;
    int32 width = 3;
    int32 height = 4;
    string url = 5;
    string thumbnailUrl = 6; // 缩略图
    int32 imageType = 7;
    string thumbnailStaticUrl = 8; // 静图缩略图
    int32 pkTeam = 9; // pk队伍1黄队 2蓝队
}

// mars开关 2066
message MarsSwitchChange{
	MsgHeader header = 1;
	int32 type = 2; // 0 关闭 1 开启
}

message TeamPkInfo {
    int32 status = 1; // pk状态 pk状态 1创建pk等待开始 2开始pk
    string createUid = 2; // 创建者uid
    int32 redScore = 3; // 红队分数
    int32 blueScore = 5; // 蓝队分数
    PkUserInfo redVip = 6;   // 红队vip
    PkUserInfo blueVip = 7;   // 蓝队vip
    int32 endTime = 8;   // pk结束时间
    int32 redLevel = 9; // 红队pk等级
    int32 blueLevel = 10; // 蓝队pk等级
}

message LivePkInfo {
    string pkId = 1; // pkId
    int32 status = 2; // 当前状态 1等待开始 2进行中
    int32 yellowScore = 3; // 黄队分数（本房间分数）
    int32 blueScore = 5; // 蓝队分数
    repeated string topYellow = 6;   // 黄队top贡献者头像
    repeated string topBlue = 7;   // 蓝队top贡献者头像
    int32 endTime = 8;   // pk结束时间
    int32 pkType = 9; // 0:1V1PK，1:TeamPK(2V2)
    string yellowLeaderAid = 10; // 黄队队长
    string blueLeaderAid = 11; // 蓝队队长
}

//房间麦位信息变更 2070
message RoomMicInfoChangeMessage {
    MsgHeader header = 1;
    int32 version = 2;
    repeated RoomMicInfo list = 3;
    TeamPkInfo teamPk = 4;
    LivePkInfo livePk = 5;
}

// 麦位控制开关 2072
message MicControlChangeMessage{
    MsgHeader header = 1;
    int32 type = 2; // 0 关闭 1 开启
}

//全房间成员礼物通知消息（单次推送） 2084
message RoomEveryoneGiftNotiMessage {
    MsgHeader header = 1;
    SendInfo g_sendInfo = 2;
    GiftInfo g_gift_info = 3;
    int32 changeVoice = 4; // 是否是变声礼物 1是
    int32 luckyGiftTimes = 5;  // 钻石版幸运礼物中奖倍数，0未中奖
    int32 svipExp = 6; // 获得svip经验值
}

//踢出房间 2090
message RoomKickOutMessage {
    MsgHeader header = 1;
	  string from_uid = 2;
	  string from_name = 3;
	  string from_head = 4;
	  int32 official = 5;
    int32 type = 6; // 踢出类型  0：踢出房间, 1: 被房间拉黑
    string tipsEn = 7; // 踢出提示文字英语
    string tipsAr = 8; // 踢出提示文字阿语
}

// 排行榜推送 cmdid = 2094
message RankNotificationMessage{
	MsgHeader header = 1;
	string title_en = 2; //活动名称
	string title_ar = 3; //活动名称
	repeated RankInfoList rankList = 4; //排行信息
}

// 排行榜推送 cmdid = 2096
message ActivityBroadcastMessage{
	MsgHeader header = 1;
	Uname user = 2;
	string title_en = 3; // 活动名称
	string title_ar = 4; // 活动名称
	int32 rank = 5; // top1, top2, top3
	int32 type = 6; // 1 用户榜，2 房间榜
	RankInfo roomInfo = 7; // 房间榜才有
    string from_uid = 8;  //上榜人的uid
    string content_en = 9;
    string content_ar = 10;
}

//创建房间通知  cmdid = 2098
message CreateRoomNotifyMessage {
  MsgHeader header = 1;
  string c_uid = 2; //创建房间者
  string c_room_id = 3; //房间id
  string c_head = 4;  //创建者头像
  string c_name = 5;  //创建者名字
  string text_ar = 6; //房间标题阿语
  string text_en = 7; //房间标题英语
}

//房间抽奖中大奖广播通知（全房间通知） cmdid = 2100
message RoomLotteryNotifyMessage {
  MsgHeader header = 1;
  string head = 2; //中奖者头像
  string name = 3; //中奖者名字
  string url = 4;  //抽奖h5链接
  string prize = 5; //奖品名称
  int32 a_type = 6; //中奖类型
  string gift_token = 7; //消息唯一token ，做消息排重用
}

//猜拳结果 cmdid = 2102
message RoomRoshamboResultMessage{
  MsgHeader header = 1;
  int32 g_result = 2; //猜拳结果 2是有胜负结果 其他是平局
  string g_icon = 3; //猜拳礼物图标
  string win_name = 4; //赢家姓名
  string loser_name = 5; //输家姓名
  string create_room_id = 6; //游戏创建房间id
  string rec_room_id = 7; //挑战者房间id
}

//转盘入口数量发生变化 cmdid = 2104
message RoomRoshamboEntranceChangeMessage {
  MsgHeader header = 1;
  int32 number = 2;  //当前猜拳大厅总数量
}

//房间幸运礼物中奖通知 cmdid = 2106
message RoomLuckyGiftWinLotteryMessage {
  MsgHeader header = 1;
  string head = 2; //中奖者头像
  string name = 3; //中奖者名字
  string prize = 4; //奖品名
  int32 a_type = 5;//中奖类型
  string arPrize = 6; //阿语奖品名
  string fromRoomId = 7; // 来源房间id
}

//房间幸运礼物中奖奖励 cmdid = 2108
message RoomLuckGiftRewardMessage {
  MsgHeader header = 1;
  string aid = 2;
  int32 a_type = 3;//是否是坐骑礼物，0否，1是
  int32 type = 4;//1幸运礼物，2抽奖礼物
  repeated LuckyGiftReward lucky_gift_reward = 5;
}

//房间公屏通知消息下发 推送2134
message RoomNotificationMessage {
    MsgHeader header = 1;
    string uid = 2; //用户uid
    string user_name = 3; //用户名字
    string user_head = 4; //用户头像
    string text = 5; //活动文案
    string text_ar = 6; //活动文案
    repeated HighlightText highlight_text = 7; //通知样式，例如：[{text : string, highlightColor : string (例如#FFFFFF)}]
    repeated HighlightText highlight_text_ar = 8; //通知样式，例如：[{text : string, highlightColor : string (例如#FFFFFF)}]
    string web_url = 9; //跳转url地址
    int32 web_type = 10; //网页打开方式 0 跳转 1 房间内全屏 2 房间内半屏
    string game_type = 11; // smash_egg 为砸蛋
    int32 width = 12; //半宽宽度
    int32 height = 13; //半屏高度
    int32 hide_head = 14; // 是否隐藏头像，1是（过度处理，以后如果要隐藏头像，直接不下发user_head字段）
    int32 floating = 15; // 是否展示飘屏广播
    string fromRoomId = 16; // 来源房间id
    int32 mystery = 17; // 1神秘人
}

//房间Lodo列表变更 推送2138
message RoomLudoChangeMessage {
    MsgHeader header = 1;
    int32 status = 2; // 0 无 1匹配中 2 进行中 3 结束 4 关闭
    string gameId = 3; // 游戏id
    int32 gameType = 4; //1 经典 2 快速
    int32 totalCurrency = 5; // 总游戏币
    int32 currencyType = 6; // 游戏币类型 1 心心 2 钻石
    repeated PlayerInfo playerList = 7; // 玩家数据
    int32 playerNumber = 8; // 玩家数量
    string gameData = 9; // 第三方json字符串
    int32 currencyId = 10; //游戏币类型Id
    repeated WinnerInfo winnerList = 11; // 游戏结算数据
}

// 视频信息同步消息 推送2142
message RoomVideoInfoMessage {
	MsgHeader header = 1;
	RoomVideoInfo videoInfo = 2;
	RoomVideoInfo nextVideoInfo = 3;
	int32 syncType = 4; // 操作类型 1 用户主动操作，需要强同步
	int32 videoVersion = 5; // 版本号
}

// 插件切换开关 推送2144
message VideoSwitchMessage {
    MsgHeader header = 1;
    int32 video_switch = 2;//话题是否开启及话题状态 1开启 2关闭 3 切换视频
    string aid = 3;//操作用户uid
    string name = 4;//操作用户名称
    string head = 5;//操作用户头像
}

//ludo游戏状态更新推送 2146
message RoomLudoStatusChangedMessage {
    MsgHeader header = 1;
    int32 status = 2; // 1:创建
    string head = 3; //操作者头像
    string name = 4; //操作者名称
    string aid = 5; //操作者aid
    int32 level = 6; //操作者等级
    repeated string badge = 7; //操作者徽章
    int32 vip = 8; //操作者vip等级
    int32 role = 9; //操作者在当前房间的身份
}

//ludo游戏踢人推送 2148
message RoomLudoKitOutMessage {
    MsgHeader header = 1;
    string head = 2;//踢人者头像
    string name = 3;//踢人者名称
    string aid = 4;//踢人者id
}

// 点播消息 推送2150
message RoomVideoSuggestMessage {
	MsgHeader header = 1;
	RoomVideoInfo videoInfo = 2;
	Uname suggestUser = 3; // 推荐者的信息
}

// 新用户进房间推荐关注用户 2154
message RoomRecommendFollowMessage {
    MsgHeader header = 1;
    string head = 2; //用户头像
    string aid = 3; //用户id
    string name = 4; //用户名
    string msg = 5; //关注消息英语
    string msg_ar = 6; //关注消息阿语
}

// 新用户被关注 2158
message RoomFollowMeMessage {
    MsgHeader header = 1;
    string head = 2; //用户头像
    string aid = 3; //用户id
    string name = 4; //用户名
    int32 gender = 5; //对方性别
}

// 新用户收到加好友请求 2160
message RoomAddFriendsMessage {
    MsgHeader header = 1;
    string head = 2; //用户头像
    string aid = 3; //用户id
    string name = 4; //用户名
}

// 关注房间提示消息 2162
message RoomFollowRoomTipsMessage {
    MsgHeader header = 1;
    string room_head = 2; //房间头像
    string room_id = 3; //房间id
    string msg = 4; //关注消息英语
    string msg_ar = 5; //关注消息阿语
}

// 关注房间提示消息 2164
message RoomTaskFinishMessage {
    MsgHeader header = 1;
    int32 task_id = 2;
    int32 type = 3;
    int32 task_type = 4; //任务类型：0 新手任务 1 日常任务
}

// 房间信息更改消息 2168
message RoomInfoChangeMessage {
    MsgHeader header = 1;
    string room_id = 2; //房间id
    string theme_url = 3; //房间背景图
    string room_name = 4; //房间名称
    string room_head = 5; //房间封面
    int32 svipLevel = 6; // 封面svip等级，-1未发生改变
}

// 平台礼物广播 2170
message PlatformGiftBroadcastMessage {
    MsgHeader header = 1;
    string sender_head = 2;  // 发送者头像
    string sender_name = 3; // 发送者名称
    int32 sender_rid = 4; // 发送者rid
    string from_uid = 5; // 发送者uid
    string receiver_name = 6; // 礼物图标
    int32 gift_number = 7; // 发送数量
    string gift_icon = 8; // 礼物图标
    string room_id = 9; // 发送者所在房间id
    int32 atype = 10; // 发送类型：1: 1对1发、 2: 全房间发
    string room_rid = 11; //发送者所在房间rid
    RidInfo fromRidInfo = 12; // 发送者rid
}

message RankUserInfo {
    string name = 1;
    string rid = 2;
    string uid = 3;
    string head = 4;
    string diamond = 5;
}

// 房间排名更改消息 2172
message RoomRankChangeMessage {
    MsgHeader header = 1;
    string name = 2;
    int32 rank = 3;
    repeated RankUserInfo rank_user_old = 4;
    repeated RankUserInfo rank_user_new = 5;
    int32 type = 6; // 计数类型  1 钻石 2 礼物
}

//平台礼物 2174
message RoomPlatformGiftMessage {
    MsgHeader header = 1;
    repeated ReceiveInfo g_recevInfo_list = 2;
    SendInfo g_sendInfo = 3;
    GiftInfo g_gift_info = 4;
    int32 send_type = 5; //1 2 3
    string room_id = 6;
    int32 isAllMic = 7; // 是否发送给全麦位用户 1是
}

// 水果游戏状态改变推送 2180
message FruitPartyStatusChangeMessage {
    MsgHeader header = 1;
    int32 status = 2; // 1 开始 2 结束
    int32 loop = 3;
}

// 新用户引导回应消息 2182
message NewUserOnboardingResponseMessage {
    MsgHeader header = 1;
    string uid = 2; // 用户id
    string name = 3; // 用户名
    string head = 4; // 用户头像
    string msg_content = 5; // 消息内容英语
    string msg_content_ar = 6; // 消息内容阿语
    int32 admin = 7; // 是否是管理员 0否 1是 管理员/房主/副房主
}

// 房间内即构游戏操作消息 2186
message RoomSudGameOperateMessage {
	MsgHeader header = 1;
    Uname opt_user = 2;
    int32 opt = 3; // 操作 1创建游戏 2关闭游戏 4开始游戏
    string game_icon = 4; // 游戏图标
    int32 game_type = 5; // 游戏类型
    string msg = 6; // 英语消息
    string msg_ar = 7; // 阿语消息
}

// 房间内即构游戏超时结束消息 2188
message RoomSudGameTimeOutMessage {
	MsgHeader header = 1;
    string msg = 4; // 英语消息
    string msg_ar = 5; // 阿语消息
}

// sud小游戏组队页面变更消息 推送2190
message SudGameChangeMessage {
    MsgHeader header = 1;
    int32 gameType = 2; // 游戏类型 1碰碰 2Ludo 3UMO
    string selfUid = 3; // 游戏创建者
    int32 playerNumber = 4; // 创建游戏时由app选择玩家数量
    int32 currency = 5; // 入场费
    int32 currencyType = 6; // 游戏币类型 1 金币 2 钻石
    int32 status = 7; // 0 无 1匹配中 2 进行中 3 结束 4 关闭
    string rule = 8; // 游戏规则配置，json字符串
    repeated SudPlayerInfo playerList = 9; // 玩家数据
    string gameId = 10; // 游戏id
}

message SudPlayerInfo {
  string uid = 1;
  string name = 2;
  string head = 3;
  int32 rid = 4;
  int32 gender = 5;
  int32 robot = 6; // 是否是机器人 0不是 1是
  int32 mystery = 7; // 1神秘人
}

message SudGameResultInfo {
  string uid = 1;
  string name = 2;
  string head = 3;
  int32 isEscaped = 4; // 是否逃跑 1是
  string awardValue = 5; // 获得的奖励数值，金币或者钻石
  int32 awardType = 6; // 获得的奖励类型 1金币 2钻石
  int32 isWin = 7; // 是否是赢家 0表示没有信息 1输 2赢 3平局
}

// sud小游戏游戏结果消息 推送2192
message SudGameResultMessage {
    MsgHeader header = 1;
    repeated SudGameResultInfo gameResultList = 2; // 游戏结果
    int32 gameType = 3; // 游戏类型 1碰碰 2Ludo 3UMO
}

// 新版大礼物广播 2194
message BigGiftMessage {
    MsgHeader header = 1;
    string roomRid = 2;  //礼物发送房间rid
    string fromRid = 3;   //发送者rid
    string fromName = 4;   //发送者名字
    string fromHead = 5;  //发送者头像
    int32 fromVipLevel = 6; //发送者vip等级
    string toName = 7; //接收者名字(给1个用户发送大礼物时才会有值) 或者房间名字(发送给多个用户时才有值)
    string giftIcon = 8; //礼物图标
    int32 number = 9; // 发送礼物数量
    int32 level = 10;  // 大礼物广播UI样式分为3档：1299～3998钻、3999～7999钻、8000钻以上，分别对应1、2、3
    int32 sendType = 11;  // 发送类型  1为全麦位 2单个用户 3全房间 4多个用户
    string fromRoomId = 12; // 发送者所在房间id
    int32 giftId = 13; //礼物ID
    RidInfo fromRidInfo = 14; // 新版发送者rid
    string giftNameEn = 15; //礼物名称
    string giftNameAr = 16; //礼物名称阿语
    int32 svipLevel = 17;
}

// 房间操作消息 2196
message RoomOptMessage {
    MsgHeader header = 1;
    int32 optType = 2;  // 1清除房间公屏消息
    int32 role = 3; // 1房主 2副房主 3管理员
    string name = 4; // 操作者名称
}

//幸运数字修改消息  推送 2198
message LuckyNumRoomChangeMessage {
    MsgHeader header = 1;
    int32 num = 2;  // 设置的房间中奖数字
    int32 luckyNumCost = 3;  // 幸运数字扣费
    int32 luckyNumSwitch = 4;  // 是否匹配幸运数字
    int32 luckyNumAdmin = 5;  // 幸运设置admin是否可以设置
    int32 luckyNumRange = 6;  // 幸运数字范围
    UserInfo opt_user = 7;
}

//屏幕共享直播操作消息  推送 2200
message ScreenSharingLiveOptMessage {
    MsgHeader header = 1;
    int32 optType = 2;  // 0关闭直播 1开启直播
    string liveStreamId = 3; // 流id
    string optUserId = 4; // 操作人uid
    string optUserName = 5; // 操作人姓名
    int32 optRole = 6; // 操作者在当前房间的身份
}

//房间火箭发射消息  推送 2202
message RoomRocketLaunchMessage {
    MsgHeader header = 1;
    int32 rocketLevel = 2;  // 火箭等级
    UserInfo top1User = 3;  // Top1用户
    int32 boxId = 4;  // 宝箱id
}

//房间火箭发射平台消息  推送 2204
message RocketLaunchPlatformMessage {
    MsgHeader header = 1;
    int32 rocketLevel = 2;  // 火箭等级
    int32 rid = 3;  // 房间rid
    string fromRoomId = 4; // 房间id
    string roomHead = 5; // 房间封面
    string roomName = 6; // 房间名
}

//获得房间火箭奖励消息  推送 2206
message GetRoomRocketRewardMessage {
    MsgHeader header = 1;
    string uid = 2;  // 用户uid
    string userName = 3;  // 用户名称
    string userHead = 4;  // 用户头像
    string text = 5;  // 消息文本
    string textAr = 6; // 消息阿语文本
    int32 mystery = 7;  // 神秘人
}

//房间火箭能量进度改变消息  推送 2208
message RocketProgressChangeMessage {
    MsgHeader header = 1;
    int32 rocketLevel = 2;  // 房间火箭等级
    int32 energyRate = 3;  // 房间火箭能量进度 0为0% 100为100%
}

// 大红包广播 2210
message BigLuckBoxMessage {
    MsgHeader header = 1;
    string fromRoomId = 2; // 发送者所在房间id
    string fromRid = 3;   //发送者rid
    string fromName = 4;   //发送者名字
    string fromHead = 5;  //发送者头像
    int32 fromVipLevel = 6; //发送者vip等级
    string boxId = 7;   //红包id
    int32 boxType = 8; //0 钻石红包,1 礼物红包
    int32 rushTime = 9; // 红包可领取时间戳，机器人用
}

//房间活动数量改变消息  推送 2212
message RoomEventNumChangeMessage {
    MsgHeader header = 1;
    int32 roomEventNum = 2;  // 房间活动数量
}

//房间召集消息  推送 2214
message RoomGatheringMessage {
    MsgHeader header = 1;
    string head = 2;  // 用户头像
    int32 gender = 3; // 用户性别
    int32 vipLevel = 4; // 用户vip等级
    int32 type = 5; // 召集类型 0召集全球用户 1召集房间会员 2召集房间粉丝 3召集我的粉丝
    string msg = 6; // 广播内容
    string fromRoomId = 7; // 房间id
    int32 rid = 8; // 用户rid
    int32 gatherId = 9; // 召集id
}

// 麦位申请列表变更  推送 2216
message MicApplyChangeMessage {
    MsgHeader header = 1;
    int32 applyCount = 2;  // 申请数量
    string firstHead = 3;  // 最新一个申请用户头像，房主、副房主和管理员显示
}

//房间召集新记录  推送 2218
message RoomGatheringNewRecordMessage {
    MsgHeader header = 1;
    int32 type = 2; // 召集类型 0召集全球用户 1召集房间会员 2召集房间粉丝 3召集我的粉丝
    string aid = 3;  // 发送者uid
    string name = 4;  // 发送者昵称
    string head = 5;  // 发送者头像
    int32 vipLevel = 6; // 发送者vip等级
    int32 gender = 7; // 发送者性别
    int32 age = 8; // 发送者年龄
    string time = 9; // 发送时间
    string msg = 10; // 广播内容
    string fromRoomId = 11; // 发送者所在房间id
    string timeAr = 12; // 发送时间阿语
    int32 rid = 13; // 发送者rid
    int32 gatherId = 14; // 召集id
}

// 麦位申请已被取消  推送 2220
message MicApplyCancelMessage {
    MsgHeader header = 1;
}

// 房间投票  推送 2222
message RoomVoteMessage {
    MsgHeader header = 1;
    int32 voteId = 2; // 投票id
    int32 type = 3; // 投票类型 0礼物投票 1问答投票
    int32 status = 4; // 状态 0创建 1进行中 2手动结束 3自动结束
    int32 optionType = 5; // 选项类型 0单选 1多选
    string title = 6; // 投票标题
    int32 duration = 7; // 投票持续时间 单位：分钟 0为手动结束
    int32 costBeans = 8; // 投票费用
    int32 votingUserNum = 9; // 参与投票用户数
    string voteIcon = 10; // 投票图标
    Uname opt_user = 11; // 操作者信息
    repeated VoteOptionInfo optionList = 12; // 投票选项
    int32 ctime = 13; // 创建时间
    int32 endTime = 14; // 结束时间
    int32 feeType = 15; // 投票费用类型 0金币 1钻石
}

message VoteOptionInfo {
  int32 id = 1; // 投票选项id
  string optionContent = 2; // 选项内容
  string aid = 3; // 用户uid （礼物投票才有）
  string userName = 4; // 用户昵称 （礼物投票才有）
  string userHead = 5; // 用户头像（礼物投票才有）
  string giftIcon = 6; // 礼物icon（礼物投票才有）
  int32 votesNum = 7; // 票数
  int32 giftId = 8; // 礼物id（礼物投票才有）
}

//admin上开通vip的消息 cmdid = 2226
message VipActiveMessage {
    MsgHeader header = 1;
    string uid = 2; // 用户uid
    int32 vipLevel = 3; // vip等级
    string titleEn = 4; // 英语
    string titleAr = 5; // 阿语
}

// 幸运礼物中奖消息 cmdid = 2228
message LuckyGiftRewardMessage {
    MsgHeader header = 1;
    int32 reward = 2; // 中奖钻石数
    int32 jackpot = 3; // 奖池大小
}

//砸蛋中大奖消息 cmdid = 2234
message SmashEggJackpotMessage {
    MsgHeader header = 1;
    string head = 2;
    string text = 3; //活动文案
    string text_ar = 4; //活动文案
    repeated HighlightText highlight_text = 5; //通知样式，例如：[{text : string, highlightColor : string (例如#FFFFFF)}]
    repeated HighlightText highlight_text_ar = 6; //通知样式，例如：[{text : string, highlightColor : string (例如#FFFFFF)}]
    string icon = 7;
    string webUrl = 8;
}

// 钻石版幸运礼物中奖广播 cmdid = 2236
message LuckyGiftBroadcastMessage {
    MsgHeader header = 1;
    string fromRoomId = 2; // 发送者所在房间id
    RidInfo fromRid = 3;   // 发送者rid
    string fromName = 4;   // 发送者名字
    string fromHead = 5;   // 发送者头像
    string giftIcon = 6;   //礼物图标
    int32 luckyGiftTimes = 7;  // 钻石版幸运礼物中奖倍数
    int32 luckyGiftBeans = 8;  // 钻石版幸运礼物中奖钻石数
}

// 房间内用户排行榜变化  推送 2242
message UserRoomRankMessage {
    MsgHeader header = 1;
    repeated UserRoomRankInfo rankList = 2; //
}

message UserRoomRankInfo {
    string uid = 1;
    int32 rank = 2; // 排行名次
    int32 rankType = 3; // 排行类型 1天 2周
}

// 红包被抢完消息 cmdid = 2244
message LuckyBagFinishMessage {
  MsgHeader header = 1;
  string boxId = 2;
}

// 抢红包消息 cmdid = 2246
message LuckyBagRushMessage {
  MsgHeader header = 1;
  string name = 2; // 用户名称
  int32 money = 3; // 抢到的钻石
}

// 游戏用户标签变更消息 cmdid = 2254
message GameUserLabelChangeMessage {
  MsgHeader header = 1;
  repeated string gameUrlList = 2; // 游戏链接
}

// teamPk结果消息 cmdid = 2256
message TeamPkResultMessage {
  MsgHeader header = 1;
  int32 status = 2; // 结果状态 1红方赢 2蓝方赢 3平局 4提前关闭平局
  int32 redScore = 3; // 红队分数
  int32 blueScore = 4; // 蓝队分数RoomDevoteRankChangeMsg
  repeated PkUserInfo topContestant = 5;   // 强战队成员
  repeated PkUserInfo topSupporter = 6;   // TOP支持者
}

// 房间锁可使用状态改变消息 cmdid = 2258
message RoomLockCanUseStatusChangeMessage {
  MsgHeader header = 1;
  int32 canUseRoomLock = 2; // 是否可以使用房间锁 0否 1是
}

// 房间状态变更消息 cmdid = 2260
message RoomChangeMessage {
  MsgHeader header = 1;
  string roomId = 2; // 房间id
  int32 roomMode = 3; // 房间模式，1Voice、2Live
  int32 type = 4; // 变更类型，1开启live、2关闭live、3房主返回live、4房主离开live...预留其他
}

// 直播房强制关闭消息 cmdid = 2262
message LiveRoomForceCloseMessage {
  MsgHeader header = 1;
  string reason = 2; // 关闭原因
  int32 type = 3; // 关闭类型 1封禁 2警告
}

// 房间贡献排名变更消息 cmdid = 2264
message RoomDevoteRankChangeMessage {
  MsgHeader header = 1;
  string name = 2; // 名字
  string rank = 3; // 排名
  int32 diffPrev = 4; // 距离上一名差额
  int32 type = 5; // 1上升 2下降
  string aid = 6; // 用户uid
}

// 房间贡献小时榜第一名消息 cmdid = 2266
message RoomDevoteRank1Message {
  MsgHeader header = 1;
  string roomId = 2; // 房间id
  string name = 3; // 名字
  string head = 4; // 头像
  int32 live = 5; // 是否开播
}

// 房间热度值变更消息 cmdid = 2268
message RoomHotChangeMessage {
  MsgHeader header = 1;
  string hotScore = 2; // 房间热度值
}

// 房间用户贡献排名上升消息 cmdid = 2270
message ActorDevoteRankRiseMessage {
  MsgHeader header = 1;
  string name = 2; // 名字
  string rank = 3; // 排名
  string aid = 4; // 用户uid
}

// 房间通用横幅广播消息 推送 2272
message RoomCommonScrollMessage {
    MsgHeader header = 1;
    string uid = 2; //用户uid
    string prizeIcon = 3; //中奖图标
    string prizeTextEn = 4; //活动文案
    string prizeTextAr = 5; //活动文案
    repeated HighlightText highlightTextEn = 6; //通知样式，例如：[{text : string, highlightColor : string (例如#FFFFFF)}]
    repeated HighlightText highlightTextAr = 7; //通知样式，例如：[{text : string, highlightColor : string (例如#FFFFFF)}]
    int32 actionType = 8;   // 点击跳转规则, 按照banner跳转规则
    string actionValue = 9;   // 跳转值: 根据actionType定义跳转值, 如url、roomId等
}

// 房间礼物连击消息 推送 2274
message RoomGiftBatterMessage {
    MsgHeader header = 1;
    repeated ReceiveInfo receiveInfoList = 2;
    SendInfo sendInfo = 3;
    GiftInfo giftInfo = 4;
    int32 type = 5; // 0单个人 1多个人 2全麦位 3全房间
}

// LivePk邀请消息 推送 2276
message LivePkInviteMessage {
    MsgHeader header = 1;
    Uname inviter = 2;
    int32 type = 3; // 0普通邀请 1匹配邀请
    int32 endTime = 4; // 邀请有效期截至时间戳，秒
    int32 pkType = 5; // 0:1V1PK，1:TeamPK(2V2)
}

// LivePk结果消息 推送 2278
message LivePkResultMessage {
    MsgHeader header = 1;
    int32 yellowScore = 2; // 黄队分数（本房间分数）
    int32 blueScore = 3; // 蓝队分数
    int32 result = 4; // 1赢 2输 3平局（对于黄队，本房间来说） 4提关闭平局(无效)
}

// LivePk邀请被拒绝消息 推送 2280
message LivePkRejectMessage {
    MsgHeader header = 1;
    int32 type = 2; // 1用户拒绝 2超时拒绝
    int32 pkType = 3; // 0:1V1PK，1:TeamPK(2V2)
    int32 teammate = 4; // 1邀请队友
}

// LivePk静音消息 推送 2282
message LivePkMuteMessage {
    MsgHeader header = 1;
    int32 type = 2; // 1静音 2取消静音
}

// LiveTeamPk邀请消息 推送 2284
message LiveTeamPkInviteMessage {
    MsgHeader header = 1;
    string pkId = 2; // pkId
    string inviterUid = 3; // 邀请者uid
    string inviterName = 4; // 邀请者名字
    repeated Uname yellowTeam = 5; // 黄队，第一个为队长
    repeated Uname blueTeam = 6; // 蓝队，第一个为队长
}

// 新用户进房间消息 推送 2286
message NewbieJoinRoomMessage {
    MsgHeader header = 1;
    string aid = 2; // 进房用户的uid
    string name = 3 ; // 名称
    int32 age = 4;   // 年龄
    int32 gender = 5;   // 1 男 2女
    string head = 6; // 头像
    string location = 7;  // 国家
    int32 platform = 8;   // 1Apple标签 0 安卓
    int32 topUp = 9;   // 充值用户标签
    int32 richPlace = 10;   // RichPlace标签
    int32 newbie = 11;   // Newbie标签
    int32 svipLevel = 12;
    int32 mystery = 13;
}

// 进入pk房间 2288
message PkEnterRoomMessage {
    MsgHeader header = 1;
    Uname uname = 2;
    int32 pkTeam = 3; // pk队伍1黄队 2蓝队
    string ownerAid = 4; // 房主aid
}

// 高级场游戏解锁消息 2290
message AdvancedGameUnlockMessage {
    MsgHeader header = 1;
}

// svip全服广播 cmdid = 2292
message SvipBroadcastMessage {
  MsgHeader header = 1;
  Uname uname = 2;
  int32 type = 3; // 1激活/续订 2进房间
  string inRoomId = 4;  // 用户所在房间
}

// 房间召集广播 cmdid = 2294
message GatherMessage {
  MsgHeader header = 1;
  Uname uname = 2;
  string micFrame = 3;  // 麦位框
  string content = 4;  // 内容
  string fromRoomId = 5;  // 来源房间
}

// svip升级消息 cmdid = 2296
message SvipLevelUpMessage {
  MsgHeader header = 1;
  int32 svipLevel = 2;
}
