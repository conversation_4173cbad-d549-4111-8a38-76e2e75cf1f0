package com.quhong.config;

import com.quhong.core.annotation.MessageGroup;
import com.quhong.core.annotation.MsgExecutorGroup;
import com.quhong.core.clusters.servers.ClusterServerConnector;
import com.quhong.core.clusters.servers.ClusterSocketServer;
import com.quhong.core.net.server.adapter.IConnectorFactory;
import com.quhong.core.register.BaseRedisRegistry;
import jakarta.annotation.PostConstruct;
import jakarta.annotation.PreDestroy;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Bean;

public class TCPServerConfig {

    @Autowired
    private MessageGroup messageGroup;
    @Autowired
    private MsgExecutorGroup msgExecutorGroup;
    @Autowired
    private ClusterSocketServer socketServer;
    @Autowired
    private BaseRedisRegistry redisRegistry; // 开启服务注册

    @PostConstruct
    public void postInit() {
        socketServer.initParams(true, 0, 60, 8);
        socketServer.start();
    }

    @PreDestroy
    public void preDestroy() {
        if (socketServer != null) {
            socketServer.stop();
        }
    }

    @Bean
    public IConnectorFactory getConnectorFactory() {
        return () -> new ClusterServerConnector(messageGroup, msgExecutorGroup);
    }
}
