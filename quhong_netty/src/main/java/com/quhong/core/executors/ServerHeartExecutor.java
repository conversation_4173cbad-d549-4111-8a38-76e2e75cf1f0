package com.quhong.core.executors;

import com.quhong.core.annotation.MsgExecutor;
import com.quhong.core.msg.Msg;
import com.quhong.core.msg.server.ServerHeartAck;
import com.quhong.core.net.connect.IConnector;
import com.quhong.enums.BaseServerCmd;

@MsgExecutor
public class ServerHeartExecutor extends AbstractMsgExecutor {
    public ServerHeartExecutor() {
        super(BaseServerCmd.SERVER_HEART);
    }

    @Override
    public void execute(IConnector connector, Msg msg) {
        ServerHeartAck ack = new ServerHeartAck();
        connector.sendMsg(ack);
    }
}
