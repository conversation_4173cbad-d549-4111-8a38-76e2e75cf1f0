package com.quhong.core.register;

import com.quhong.core.concurrency.queues.TaskQueue;
import com.quhong.core.timers.LoopTask;
import com.quhong.core.timers.TimerService;
import com.quhong.core.utils.SpringUtils;
import com.quhong.datas.ServerData;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Component;

import java.util.*;

@Component
@Lazy
public class RedisDiscovery extends TaskQueue {
    private static final Logger logger = LoggerFactory.getLogger(RedisDiscovery.class);

    private static final int GET_INTERVAL = 50 * 1000;

    private ServerInfoRedis serverInfoRedis;
    private Map<Integer, List<ServerData>> serverMap;

    public RedisDiscovery() {
        this.serverMap = new HashMap<>();
        this.serverInfoRedis = SpringUtils.getBean(ServerInfoRedis.class);
        postInit();
    }

    private void postInit() {
        logger.info("redis discover start.");
        fetchServers();
        TimerService.getService().addDelay(new LoopTask(this, GET_INTERVAL) {
            @Override
            protected void execute() {
                fetchServers();
            }
        });
    }

    public void fetchServers() {
        Map<Integer, List<ServerData>> serverMap = new HashMap<>();
        List<ServerData> serverDataList = serverInfoRedis.getServerList();
        for (ServerData serverData : serverDataList) {
            List<ServerData> list = serverMap.computeIfAbsent(serverData.getClusterId(), k -> new ArrayList<>());
            list.add(serverData);
        }
        this.serverMap = serverMap;
    }

    public List<ServerData> getServerList(int clusterId) {
        return this.serverMap.get(clusterId);
    }

    public Set<Integer> getServerIdSet(int clusterId) {
        Set<Integer> set = new HashSet<>();
        List<ServerData> list = this.serverMap.get(clusterId);
        if (list == null) {
            return set;
        }
        list.forEach(data -> set.add(data.getServerId()));
        return set;
    }

    public Map<Integer, List<ServerData>> getServerMap() {
        return serverMap;
    }
}
