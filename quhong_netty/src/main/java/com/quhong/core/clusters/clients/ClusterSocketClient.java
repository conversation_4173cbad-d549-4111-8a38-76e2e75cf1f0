package com.quhong.core.clusters.clients;

import com.quhong.core.annotation.MessageGroup;
import com.quhong.core.annotation.MsgExecutorGroup;
import com.quhong.core.clusters.Cluster;
import com.quhong.core.config.ServerConfig;
import com.quhong.core.net.client.AbstractSocketClient;
import com.quhong.core.net.client.adapter.ClientNetAdapter;
import com.quhong.core.net.server.codec.decoder.ServerMsgDecoder;
import com.quhong.core.net.server.codec.encoder.ServerMsgEncoder;
import com.quhong.datas.ServerData;
import io.netty.channel.ChannelInitializer;
import io.netty.channel.socket.SocketChannel;
import io.netty.handler.timeout.IdleStateHandler;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.concurrent.TimeUnit;

public class ClusterSocketClient extends AbstractSocketClient {
    private static final Logger logger = LoggerFactory.getLogger(ClusterSocketClient.class);

    protected MessageGroup messageGroup;
    protected MsgExecutorGroup executorGroup;
    protected ServerData serverData;
    protected Cluster cluster;

    @Override
    protected void doDispose(){
        if(serverData != null) {
            logger.info("{} -> {} disposed", ServerConfig.getServerID(), serverData.getServerId());
        }
        super.doDispose();
    }

    public ClusterSocketClient(ServerData serverData, Cluster cluster, MessageGroup messageGroup, MsgExecutorGroup executorGroup){
        this.serverData = serverData;
        this.cluster = cluster;
        this.messageGroup = messageGroup;
        this.executorGroup = executorGroup;
    }

    @Override
    protected ChannelInitializer<SocketChannel> getChildHandler() {
        return new ServerChannelInitializer();
    }

    /**
     * @param host      地址
     * @param port      端口
     */
    public void init(String host, int port){
        super.init(host, port, new ClusterClientConnector(cluster, serverData, messageGroup, executorGroup, true), 4);
    }

    /**
     * @param host      地址
     * @param port      端口
     * @param connector connector
     */
    public void init(String host, int port, ClusterClientConnector connector){
        super.init(host, port, connector, 4);
    }

    /**
     * @param connector connector
     */
    public void init(ClusterClientConnector connector){
        super.init(serverData.getHost(), serverData.getPort(), connector, 4);
    }

    public ServerData getServerData() {
        return serverData;
    }

    public void setServerData(ServerData serverData) {
        this.serverData = serverData;
    }

    public void start() {
        if(serverData != null) {
            logger.info("{} -> {} start connect.", ServerConfig.getServerID(), this.serverData.getServerId());
        }
        super.start();
    }

    @Override
    public void sessionOpened() {
        if(isDispose()){
            if(serverData != null) {
                logger.info("{} -> {}  has disposed. will closed", ServerConfig.getServerID(), this.serverData.getServerId());
            }
            close();
            return;
        }
        super.sessionOpened();
    }

    private class ServerChannelInitializer
            extends ChannelInitializer<SocketChannel> {
        private int bothIdleTime;

        public ServerChannelInitializer() {
            this.bothIdleTime = ClusterSocketClient.this.bothIdleTime;
        }

        @Override
        public void initChannel(SocketChannel ch) {
            ch.pipeline().addLast(new IdleStateHandler(bothIdleTime,
                        bothIdleTime, bothIdleTime, TimeUnit.SECONDS));
            ch.pipeline().addLast(new ServerMsgEncoder());
            ch.pipeline().addLast(new ServerMsgDecoder(messageGroup));
            ch.pipeline().addLast(new ClientNetAdapter(ClusterSocketClient.this));
        }
    }
}
