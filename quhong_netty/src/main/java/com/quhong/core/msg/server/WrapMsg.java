package com.quhong.core.msg.server;

import com.google.protobuf.ByteString;
import com.quhong.core.annotation.Message;
import com.quhong.core.msg.ServerMsg;
import com.quhong.enums.BaseServerCmd;
import com.quhong.server.protobuf.ServerProto;

import java.util.Collection;

@Message(cmd = BaseServerCmd.WRAP_MSG, isServer = true)
public class WrapMsg extends ServerMsg {
    private int msgCmd;
    private Collection<String> uidList; // 发送给的uidList
    private byte[] body;
    private String balanceKey;

    @Override
    protected void doFromBody(byte[] bytes) throws Exception {
        ServerProto.WrapMessage msg = ServerProto.WrapMessage.parseFrom(bytes);
        this.msgCmd = msg.getMsgCmd();
        this.uidList = msg.getUidList();
        this.body = msg.getBody().toByteArray();
    }

    @Override
    protected byte[] doToBody() throws Exception {
        ServerProto.WrapMessage.Builder builder = ServerProto.WrapMessage.newBuilder();
        builder.setMsgCmd(msgCmd);
        if (uidList != null) {
            builder.addAllUid(uidList);
        }
        builder.setBody(ByteString.copyFrom(body));
        return builder.build().toByteArray();
    }

    public Collection<String> getUidList() {
        return uidList;
    }

    public void setUidList(Collection<String> uidList) {
        this.uidList = uidList;
    }

    public byte[] getBody() {
        return body;
    }

    public void setBody(byte[] body) {
        this.body = body;
    }

    public int getMsgCmd() {
        return msgCmd;
    }

    public void setMsgCmd(int msgCmd) {
        this.msgCmd = msgCmd;
    }

    public String getBalanceKey() {
        return balanceKey;
    }

    public void setBalanceKey(String balanceKey) {
        this.balanceKey = balanceKey;
    }
}
