package com.quhong.core.msg.server;

import com.quhong.core.annotation.Message;
import com.quhong.core.msg.ServerMsg;
import com.quhong.enums.BaseServerCmd;

@Message(cmd = BaseServerCmd.SERVER_HEART_ACK, isServer = true)
public class ServerHeartAck extends ServerMsg {
    @Override
    protected void doFromBody(byte[] bytes) throws Exception {
        //do nothing
    }

    @Override
    protected byte[] doToBody() throws Exception {
        return new byte[0];
    }
}
