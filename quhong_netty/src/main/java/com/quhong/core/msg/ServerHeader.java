package com.quhong.core.msg;

import com.quhong.core.config.ServerConfig;
import com.quhong.core.errors.CodecException;
import com.quhong.core.utils.DateHelper;
import com.quhong.utils.BytebufUtils;
import io.netty.buffer.ByteBuf;

public class ServerHeader extends MsgHeader {
    protected long msgId; //消息id，对该sesson一段时间内唯一  4字节
    protected int cmd; //消息类型，等同cmd 2字节
    protected int timestamp; //时间戳，单位 s 4字节
    private long sessionId; //用户回话id
    private String uid = ""; //用户id
    private int fromServerId; //从哪个服务器发出
    private int seq; //seq

    public ServerHeader() {
        msgId = Msg.generateMsgId();
        this.timestamp = (int) (System.currentTimeMillis() / 1000);
    }

    @Override
    public void fromHeader(ByteBuf buf) throws CodecException {
        this.headerLength = buf.readUnsignedShort();
        this.msgId = buf.readLong();
        this.cmd = buf.readUnsignedShort();
        this.timestamp = buf.readInt();
        this.sessionId = buf.readLong();
        this.uid = BytebufUtils.readString(buf);
        this.fromServerId = buf.readInt();
        this.seq = buf.readInt();
    }

    @Override
    public void toHeader(ByteBuf buf) {
        if (msgId == 0) {
            msgId = Msg.generateMsgId();
        }
        if (this.timestamp == 0) {
            this.timestamp = DateHelper.getNowSeconds();
        }
        int startIndex = buf.writerIndex();
        buf.writeShort(0);
        buf.writeLong(this.msgId);
        buf.writeShort(this.cmd);
        buf.writeInt(this.timestamp);
        buf.writeLong(this.sessionId);
        BytebufUtils.writeString(buf, this.uid);
        if (fromServerId == 0) {
            fromServerId = ServerConfig.getServerID();
        }
        buf.writeInt(fromServerId);
        buf.writeInt(seq);
        this.headerLength = buf.writerIndex() - startIndex;
        buf.setShort(startIndex, headerLength);
    }

    protected int readableBytes(ByteBuf buf) {
        return (this.headerLength + 2) - buf.readerIndex();
    }

    protected boolean readableInt(ByteBuf buf) {
        return readableBytes(buf) >= 4;
    }

    protected boolean readableShort(ByteBuf buf) {
        return readableBytes(buf) >= 2;
    }

    protected boolean readableLong(ByteBuf buf) {
        return readableBytes(buf) >= 8;
    }


    public long getMsgId() {
        return msgId;
    }

    public void setMsgId(long msgId) {
        this.msgId = msgId;
    }

    public int getCmd() {
        return cmd;
    }

    public void setCmd(int cmd) {
        this.cmd = cmd;
    }

    public int getTimestamp() {
        return timestamp;
    }

    public void setTimestamp(int timestamp) {
        this.timestamp = timestamp;
    }

    public long getSessionId() {
        return sessionId;
    }

    public void setSessionId(long sessionId) {
        this.sessionId = sessionId;
    }

    public String getUid() {
        return uid;
    }

    public void setUid(String uid) {
        this.uid = uid;
    }

    public int getFromServerId() {
        return fromServerId;
    }

    public void setFromServerId(int fromServerId) {
        this.fromServerId = fromServerId;
    }

    public int getSeq() {
        return seq;
    }

    public void setSeq(int seq) {
        this.seq = seq;
    }
}
