baseUrl: /data/
tcp:
  port: 9301
server:
  id: 4000
  port: 8080
spring:
  main:
    allow-circular-references: true
  application:
    name: waho-java-data
  messages:
    basename: i18n/data
    encoding: UTF-8
  cloud:
    kubernetes:
      discovery:
        catalog-services-watch:
          enabled: false
feign:
  hystrix:
    enabled: true
  client:
    config:
      default:
        connectTimeout: 3000
        readTimeout: 60000
hystrix:
  command:
    default:
      execution:
        timeout:
          enabled: true
        isolation:
          thread:
            timeoutInMilliseconds: 3000
