baseUrl: /message/
server:
  port: 8080
spring:
  main:
    allow-circular-references: true
  application:
    name: waho-java-msg
  messages:
    basename: i18n/msg
    encoding: UTF-8
  cloud:
    kubernetes:
      discovery:
        catalog-services-watch:
          enabled: false
feign:
  hystrix:
    enabled: true
  client:
    config:
      default:
        connectTimeout: 3000
        readTimeout: 60000
hystrix:
  command:
    default:
      execution:
        timeout:
          enabled: true
        isolation:
          thread:
            timeoutInMilliseconds: 10000
dubbo:
  application:
    name: ${spring.application.name}
    qos-enable: false
    metadata-service-port: 20885
  registry:
    address: kubernetes://DEFAULT_MASTER_HOST?registry-type=service&duplicate=false&trustCerts=true&namespace=devops
  protocol:
    name: dubbo
    port: 20880
  metadata-report:
    report-metadata: false
  service:
    shutdown:
      wait: 5000

background:
  backgroundList:
    - thumbUrl: https://cloudcdn.waho.live/appimage/thumb-01.png
      originalUrl: https://cloudcdn.waho.live/appimage/bg-01.png
    - thumbUrl: https://cloudcdn.waho.live/appimage/thumb-02.png
      originalUrl: https://cloudcdn.waho.live/appimage/bg-02.png
    - thumbUrl: https://cloudcdn.waho.live/appimage/thumb-03.png
      originalUrl: https://cloudcdn.waho.live/appimage/bg-03.png
    - thumbUrl: https://cloudcdn.waho.live/appimage/thumb-04.png
      originalUrl: https://cloudcdn.waho.live/appimage/bg-04.png
    - thumbUrl: https://cloudcdn.waho.live/appimage/thumb-05.png
      originalUrl: https://cloudcdn.waho.live/appimage/bg-05.png
    - thumbUrl: https://cloudcdn.waho.live/appimage/thumb-06.png
      originalUrl: https://cloudcdn.waho.live/appimage/bg-06.png
    - thumbUrl: https://cloudcdn.waho.live/appimage/thumb-07.png
      originalUrl: https://cloudcdn.waho.live/appimage/bg-07.png
    - thumbUrl: https://cloudcdn.waho.live/appimage/thumb-08.png
      originalUrl: https://cloudcdn.waho.live/appimage/bg-08.png
