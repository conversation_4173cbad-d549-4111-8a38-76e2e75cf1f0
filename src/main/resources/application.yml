baseUrl: /pay/
server:
  port: 8080
spring:
  main:
    allow-circular-references: true
  application:
    name: waho-java-pay
  messages:
    basename: i18n/pay
    encoding: UTF-8
  cloud:
    kubernetes:
      discovery:
        catalog-services-watch:
          enabled: false
feign:
  hystrix:
    enabled: true
  client:
    config:
      default:
        connectTimeout: 3000
        readTimeout: 60000
hystrix:
  command:
    default:
      execution:
        timeout:
          enabled: true
        isolation:
          thread:
            timeoutInMilliseconds: 10000
