baseUrl: /room_list/
server:
  port: 8080
spring:
  main:
    allow-circular-references: true
  application:
    name: waho-room-list
  messages:
    basename: i18n/room-list
    encoding: UTF-8
  cloud:
    kubernetes:
      discovery:
        catalog-services-watch:
          enabled: false
feign:
  hystrix:
    enabled: true
  client:
    config:
      default:
        connectTimeout: 3000
        readTimeout: 60000
hystrix:
  command:
    default:
      execution:
        timeout:
          enabled: true
        isolation:
          thread:
            timeoutInMilliseconds: 3000
