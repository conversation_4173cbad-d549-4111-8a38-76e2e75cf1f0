<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >

<mapper namespace="com.quhong.mysql.slave_mapper.waho_room.RoomMsgMapper" >

    <resultMap id="resultMap" type="com.quhong.operation.share.mysql.RoomMsg" >
        <result column="id" property="id"/>
        <result column="room_id" property="roomId"/>
        <result column="msg_category" property="msgCategory"/>
        <result column="msg_type" property="msyType"/>
        <result column="msg_body" property="msgBody"/>
        <result column="send_user_count" property="sendUserCount"/>
        <result column="user_count" property="userCount"/>
        <result column="msg_id" property="msgId"/>
        <result column="ctime" property="ctime"/>
        <result column="from_os" property="fromOs"/>
        <result column="from_uid" property="fromUid"/>
    </resultMap>

    <select id="selectByRoomId" resultMap="resultMap" parameterType="String">
        SELECT * FROM t_room_message_${tableSuffix}
        WHERE <![CDATA[ ctime >= #{startTime} AND ctime < #{endTime} ]]>
        <if test="roomId != null" >
            AND room_id = #{roomId}
        </if>
    </select>

    <select id="totalChatActor" resultType="String" parameterType="String">
        SELECT
            from_uid
        FROM t_room_message_${tableSuffix}
        WHERE <![CDATA[ ctime >= #{startTime} AND ctime < #{endTime} ]]>
        <if test="fromOs != null" >
            AND from_os = #{fromOs}
        </if>
        GROUP BY from_uid
    </select>

    <select id="totalChatCountByRoomId" resultType="Integer" parameterType="String">
        SELECT
            COUNT(*)
        FROM t_room_message_${tableSuffix}
        WHERE <![CDATA[ ctime >= #{startTime} AND ctime < #{endTime} ]]> AND room_id = #{roomId}
    </select>

    <select id="totalChatActorByRoomId" resultType="String" parameterType="String">
        SELECT
            from_uid
        FROM t_room_message_${tableSuffix}
        WHERE <![CDATA[ ctime >= #{startTime} AND ctime < #{endTime} ]]> AND room_id = #{roomId}
        GROUP BY from_uid
    </select>

    <select id="actorChatCount" resultType="Integer" parameterType="String">
        SELECT
            COUNT(*)
        FROM t_room_message_${tableSuffix}
        WHERE <![CDATA[ ctime >= #{startTime} AND ctime < #{endTime} ]]> AND from_uid = #{userId}
    </select>

    <select id="chatPersonNum" resultType="Integer" parameterType="String">
        SELECT
            COUNT(*) AS num
        FROM t_room_message_${tableSuffix}
        WHERE <![CDATA[ ctime >= #{startTime} AND ctime < #{endTime} ]]>

        <if test="uidList.size() > 0">
            AND from_uid IN
            <foreach item="item" collection="uidList" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        GROUP BY from_uid
    </select>

    <select id="roomMsgStat" resultType="com.quhong.operation.share.vo.RoomMsgStatVO" parameterType="String">
        SELECT
        COUNT(DISTINCT from_uid) AS chatUser,
        COUNT(*) AS chatCount
        FROM(
        <foreach collection="tableSuffixList" item="tableSuffix" separator="UNION ALL">
            SELECT from_uid
            FROM t_room_message_${tableSuffix}
            WHERE
            <![CDATA[ ctime >= #{startTime} AND ctime < #{endTime} ]]>
            <if test="os != null">
                AND from_os = #{os}
            </if>
        </foreach>
        ) AS stat
    </select>

    <select id="msgRookieRoomNewUsers" resultType="String" parameterType="String">
        SELECT distinct from_uid
        FROM t_room_message_${tableSuffix}
        WHERE
        <![CDATA[ ctime >= #{startTime} AND ctime < #{endTime}
        AND from_uid >= #{startUid} AND from_uid <= #{endUid}]]>
        <if test="ridSet != null and ridSet.size() > 0">
            AND room_id IN
            <foreach item="item" collection="ridSet" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
    </select>

</mapper>
