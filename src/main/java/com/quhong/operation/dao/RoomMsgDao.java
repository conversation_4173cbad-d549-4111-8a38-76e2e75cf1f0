package com.quhong.operation.dao;

import com.quhong.config.CaffeineCacheConfig;
import com.quhong.datas.DayTimeData;
import com.quhong.mysql.dao.MonthShardingDao;
import com.quhong.mysql.slave_mapper.waho_room.RoomMsgMapper;
import com.quhong.operation.common.ApiResult;
import com.quhong.operation.share.vo.RoomMsgStatVO;
import com.quhong.operation.utils.DateHelper;
import com.quhong.operation.utils.MongoUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import java.util.ArrayList;
import java.util.HashSet;
import java.util.List;
import java.util.Set;

/**
 * <AUTHOR>
 * @date 2020/8/1
 */
@Component
public class RoomMsgDao extends MonthShardingDao<RoomMsgMapper> {

    private final static Logger logger = LoggerFactory.getLogger(RoomMsgDao.class);
    @Autowired
    private RoomMsgMapper roomMsgMapper;

    public RoomMsgDao() {
        super("t_room_message");
    }

    /**
     * 统计一段时间内的所有聊天人数
     *
     * @param startTime 开始时间
     * @param endTime   结尾时间
     * @param fromOs    {0: android, 1: ios, null: all}
     * @return 统计的数据
     */
    public ApiResult<Integer> totalChatPerson(Integer startTime, Integer endTime, Integer fromOs) {
        ApiResult<Integer> result = new ApiResult<>();
        int chatPerson = totalCount(startTime, endTime, fromOs);
        logger.info("totalChatPerson param start={}, end={}, fromOs={} chat person num={}",
                startTime, endTime, fromOs, chatPerson);

        return result.ok(chatPerson);
    }

    private int totalCount(Integer startTime, Integer endTime, Integer fromOs) {
        // startTime 不能为空
        if (null == startTime) return 0;

        DateHelper dateHelper = DateHelper.ARABIAN;
        // 当前时区时间戳 秒
        Integer currentTimeZoneSeconds = dateHelper.currentTimeZoneSeconds();
        if (null == endTime || endTime > currentTimeZoneSeconds) {
            // 如果结尾时间是空或在大于当前时间则设置为当前时间
            logger.info("endTime={}  currentTimeZoneSeconds={}", endTime, currentTimeZoneSeconds);
            endTime = currentTimeZoneSeconds;
        }

        Set<String> set = new HashSet<>();
        List<String> arr = dateHelper.getTableSuffixArr(startTime, endTime);
        for (String tableSuffix : arr) {
            try {
                logger.info("t_room_message_" + tableSuffix + " => start=" + startTime + ", end=" + endTime);
                List<String> list = roomMsgMapper.totalChatActor(tableSuffix, startTime, endTime, fromOs);
                set.addAll(list);
            } catch (Exception e) {
                logger.error(e.getMessage(), e);
            }
        }
        return set.size();
    }

    /**
     * 统计房间内聊天次数
     *
     * @param startTime 开始时间
     * @param endTime   结尾时间
     * @param roomId    房间rid
     * @return 次数
     */
    public ApiResult<Integer> roomChatCount(Integer startTime, Integer endTime, String roomId) {
        ApiResult<Integer> result = new ApiResult<>();
        List<String> tableSuffixArr = DateHelper.ARABIAN.getTableSuffixArr(startTime, endTime);
        int count = 0;
        for (String tableSuffix : tableSuffixArr) {
            Integer num = roomMsgMapper.totalChatCountByRoomId(tableSuffix, startTime, endTime, roomId);
            if (null != num && num > 0) {
                count += num;
            }
        }
        logger.info("param start={} end={} roomId={} roomChatCount count={}", startTime, endTime, roomId, count);
        return result.ok(count);
    }

    /**
     * 统计房间内聊天人数
     *
     * @param startTime 开始时间
     * @param endTime   结尾时间
     * @param roomId    房间rid
     * @return 人数
     */
    public ApiResult<Integer> roomChatPerson(Integer startTime, Integer endTime, String roomId) {
        ApiResult<Integer> result = new ApiResult<>();
        List<String> tableSuffixArr = DateHelper.ARABIAN.getTableSuffixArr(startTime, endTime);
        Set<String> set = new HashSet<>();
        for (String tableSuffix : tableSuffixArr) {
            List<String> list = roomMsgMapper.totalChatActorByRoomId(tableSuffix, startTime, endTime, roomId);
            if (CollectionUtils.isEmpty(list)) {
                continue;
            }
            set.addAll(list);
        }
        logger.info("param start={} end={} roomId={} roomChatPerson count={}", startTime, endTime, roomId, set.size());
        return result.ok(set.size());
    }

    /**
     * 统计用户聊天次数
     *
     * @param startTime 开始时间
     * @param endTime   结尾时间
     * @param userId    用户user_id
     * @return 聊天次数
     */
    public ApiResult<Integer> actorChatCount(Integer startTime, Integer endTime, String userId) {
        ApiResult<Integer> result = new ApiResult<>();
        List<String> tableSuffixArr = DateHelper.ARABIAN.getTableSuffixArr(startTime, endTime);
        int count = 0;
        for (String tableSuffix : tableSuffixArr) {
            Integer num = roomMsgMapper.actorChatCount(tableSuffix, startTime, endTime, userId);
            if (null != num && num > 0)
                count += num;
        }
        logger.info("param start={} end={} roomId={} actorChatCount count={}", startTime, endTime, userId, count);
        return result.ok(count);
    }

    /**
     * 查询某批用户在某天的聊天人数
     *
     * @param dateStr 某天
     * @param uidList 某批
     * @return 人数
     */
    public ApiResult<Integer> chatPersonNum(String dateStr, List<String> uidList) {
        ApiResult<Integer> result = new ApiResult<>();
        if (CollectionUtils.isEmpty(uidList)) {
            logger.info("uid list is empty");
            return result.ok(0);
        }


        Integer[] time = DateHelper.ARABIAN.getTimeWhereRange(dateStr);
        // 获取表名后缀
        String tableSuffix = DateHelper.ARABIAN.getTableSuffixByString(dateStr);
        List<Integer> personNum = roomMsgMapper.chatPersonNum(tableSuffix, time[0], time[1], uidList);
        if (CollectionUtils.isEmpty(personNum)) personNum = new ArrayList<>();

        logger.info("date={} size={} chatPersonNum={}", dateStr, uidList.size(), personNum.size());
        return result.ok(personNum.size());
    }

    @Cacheable(value = "reports", key = "targetClass + methodName + #p0 + #p1 + #p2",
            condition = "#endTime<T(com.quhong.core.utils.DateHelper).DEFAULT.getTodayStartTime()/1000", cacheManager = CaffeineCacheConfig.EXPIRE_10M_AFTER_WRITE)
    public RoomMsgStatVO getRoomMsgStat(Integer startTime, Integer endTime, Integer os) {
        long begin = System.currentTimeMillis();
        List<String> tableSuffixArr = DateHelper.ARABIAN.getTableSuffixArr(startTime, endTime);
        RoomMsgStatVO result = new RoomMsgStatVO();
        List<String> tableSuffixList = new ArrayList<>();
        for (String tableSuffix : tableSuffixArr) {
            if (checkExist(tableSuffix)) {
                tableSuffixList.add(tableSuffix);
            }
        }
        if (CollectionUtils.isEmpty(tableSuffixList)) {
            return result;
        }
        result = roomMsgMapper.roomMsgStat(tableSuffixList, startTime, endTime, os);
        logger.info("getRoomMsgStat s={} e={} os={} spend={}", startTime, endTime, os, System.currentTimeMillis() - begin);
        return result;
    }


    public Set<String> msgRookieRoomNewUsers(DayTimeData dayTimeData, Set<String> ridSet) {
        int startTime = dayTimeData.getTime();
        int endTime = dayTimeData.getEndTime();
        Integer[] time = DateHelper.ARABIAN.getTimeWhereRange(dayTimeData.getDate());
        String[] uidRange = getUidWhereRange(time);
        List<String> tableSuffixList = DateHelper.ARABIAN.getTableSuffixArr(startTime, endTime);
        Set<String> uidSet = new HashSet<>();
        for (String tableSuffix : tableSuffixList) {
            if (checkExist(tableSuffix)) {
                List<String> uidList = roomMsgMapper.msgRookieRoomNewUsers(tableSuffix, ridSet,startTime, endTime,uidRange[0],uidRange[1]);
                if (!CollectionUtils.isEmpty(uidList)) {
                    uidSet.addAll(uidList);
                }
            }
        }
        return uidSet;
    }

    /**
     * 获取过滤的uid范围
     *
     * @param time 时间范围
     * @return uid范围
     */
    private String[] getUidWhereRange(Integer[] time) {
        String startUid = MongoUtils.create_idBySecond(time[0]);
        String endUid = MongoUtils.create_idBySecond(time[1]);
        return new String[]{startUid, endUid};
    }
}
