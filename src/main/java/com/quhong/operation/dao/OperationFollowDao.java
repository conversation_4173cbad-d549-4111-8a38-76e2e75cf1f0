package com.quhong.operation.dao;

import com.quhong.mongo.config.OpMongoBean;
import com.quhong.operation.share.data.AggStatData;
import com.quhong.operation.share.mongobean.FollowActor;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.data.mongodb.core.aggregation.Aggregation;
import org.springframework.data.mongodb.core.query.Criteria;
import org.springframework.stereotype.Component;

import jakarta.annotation.Resource;
import java.util.List;
import java.util.Set;

@Component
public class OperationFollowDao {

    private static final String TABLE_NAME = "follow";

    @Resource(name = OpMongoBean.MOVIES)
    private MongoTemplate mongoTemplate;

    /**
     * 获取用户uid的粉丝数
     */
    public int findFansCount(String uid) {
        Criteria criteria = Criteria.where("aid").is(uid);
        Aggregation aggregation = Aggregation.newAggregation(
                Aggregation.match(criteria),
                Aggregation.group("uid")
        );
        List<FollowActor> list = mongoTemplate.aggregate(aggregation, TABLE_NAME, FollowActor.class).getMappedResults();
        return list.size();
    }

    /**
     * 获取统计数据
     */
    public List<AggStatData> listStatByAid(Set<String> aidSet) {
        Criteria criteria = Criteria.where("aid").in(aidSet);
        Aggregation aggregation = Aggregation.newAggregation(
                Aggregation.match(criteria),
                Aggregation.group("aid").count().as("userCount"),
                Aggregation.project("userCount").and("_id").as("uid")
        );
        return mongoTemplate.aggregate(aggregation, TABLE_NAME, AggStatData.class).getMappedResults();
    }
}
