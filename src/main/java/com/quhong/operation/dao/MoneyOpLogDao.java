package com.quhong.operation.dao;

import com.quhong.mongo.config.MongoBean;
import com.quhong.operation.common.ApiResult;
import com.quhong.operation.share.mongobean.Actor;
import com.quhong.operation.share.mongobean.MoneyOpLog;
import com.quhong.operation.share.vo.OnCreditVO;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.data.mongodb.core.query.Criteria;
import org.springframework.data.mongodb.core.query.Query;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import jakarta.annotation.Resource;
import java.util.ArrayList;
import java.util.List;
import java.util.Set;

/**
 * <AUTHOR>
 * @date 2020/6/28
 */
@Component
public class MoneyOpLogDao {

    private final static Logger logger = LoggerFactory.getLogger(MoneyOpLogDao.class);

    @Resource(name=MongoBean.MOVIES)
    private MongoTemplate mongoTemp1;
    @Autowired
    private OperationActorDao actorDao;

    /**
     * 获取时间段内的credit done数据
     *
     * @param rid       可传可不传
     * @param startTime 开始时间
     * @param endTime   结束时间
     * @return 返回数据
     */
    public ApiResult<List<OnCreditVO>> getCreditDoneList(Integer rid, Integer startTime, Integer endTime) {
        ApiResult<List<OnCreditVO>> result = new ApiResult<>();
        Criteria criteria = Criteria.where("charge_type").is(1) // 表示荣誉充值
                .and("s_type").is(2) // 表示credit done
                .and("c_time").gte(startTime).lt(endTime);

        ApiResult<Actor> apiResult = actorDao.getActorByRidOrBeforeRid(rid);
        if (apiResult.isOK() && null != apiResult.getData())
            criteria.and("aid").is(apiResult.getData().get_id().toString());

        Query query = new Query(criteria);
        List<MoneyOpLog> moneyOpLogList = mongoTemp1.find(query, MoneyOpLog.class);
        logger.info("getMoneyOpLog getter moneyOpLogList size={}", moneyOpLogList.size());
        if (CollectionUtils.isEmpty(moneyOpLogList)) {
            return result.ok(null);
        }

        List<OnCreditVO> list = new ArrayList<>();
        for (MoneyOpLog op : moneyOpLogList) {
            OnCreditVO vo = new OnCreditVO();
            BeanUtils.copyProperties(op, vo);
            vo.setState(0);
            list.add(vo);
        }

        return result.ok(list);
    }

    public ApiResult<List<MoneyOpLog>> getMoneyOpList (Integer startTime, Integer endTime, Integer chargeType) {
        ApiResult<List<MoneyOpLog>> result = new ApiResult<>();
        Criteria criteria = Criteria.where("charge_type").is(chargeType) // 表示荣誉充值
                .and("c_time").gte(startTime).lt(endTime);

        Query query = new Query(criteria);
        List<MoneyOpLog> list = mongoTemp1.find(query, MoneyOpLog.class);
        logger.info("getMoneyOpList getter moneyOpLogList size={}", list.size());

        return result.ok(list);
    }

    /**
     * 获取线下充值数据
     *
     * @param startTime  开始时间
     * @param endTime    结束时间
     * @param chargeType 充值类型
     * @param uidSet     uidSet
     * @return 返回数据
     */
    public List<MoneyOpLog> getMoneyOpChargeList(Integer startTime, Integer endTime, Integer chargeType, Set<String> uidSet) {
        Criteria criteria = Criteria.where("charge_type").is(chargeType)
                .and("c_time").gte(startTime).lt(endTime)
                .and("money").gt(1)
                .and("uid").in(uidSet)
                .and("currency").exists(true);
        return mongoTemp1.find(new Query(criteria), MoneyOpLog.class);
    }

}
