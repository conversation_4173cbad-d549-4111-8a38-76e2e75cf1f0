package com.quhong.operation.dao;

import com.quhong.mongo.config.MongoBean;
import com.quhong.mongo.data.BanReasonData;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.data.mongodb.core.query.Criteria;
import org.springframework.data.mongodb.core.query.Query;
import org.springframework.stereotype.Component;
import org.springframework.util.StringUtils;

import jakarta.annotation.Resource;
import java.util.ArrayList;
import java.util.List;

@Component
public class BanReasonStatDao {

    private static final Logger logger = LoggerFactory.getLogger(BanReasonStatDao.class);

    @Resource(name = MongoBean.MOVIES)
    private MongoTemplate mongoTemplate;

    /**
     * 查询指定时间范围内封禁数据
     * @param start
     * @param end
     * @param uid
     * @return
     */
    public List<BanReasonData> listBanByMtime(int start,int end,String uid){
        Criteria criteria = Criteria.where("mtime").gte(start).lte(end);
        if(!StringUtils.isEmpty(uid)){
            criteria = criteria.where("uid").is(uid);
        }
        Query query = new Query(criteria);
        List<BanReasonData> list = mongoTemplate.find(query, BanReasonData.class);
        if(list == null){
            return new ArrayList<>();
        }
        return list;
    }


}
