package com.quhong.operation.dao;

import com.mongodb.client.MongoCollection;
import com.mongodb.client.MongoCursor;
import com.quhong.mongo.config.OpMongoBean;
import com.quhong.operation.share.mongobean.NewUserHonor;
import org.bson.Document;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.stereotype.Component;

import jakarta.annotation.Resource;
import java.util.ArrayList;
import java.util.List;
import java.util.Set;

@Component
public class NewUserHonorStatDao {

    private static final Logger logger = LoggerFactory.getLogger(NewUserHonorStatDao.class);

    private static final String TABLE_NAME = "new_user_honor";

    @Resource(name= OpMongoBean.MOVIES)
    private MongoTemplate mongoTemplate;

    /**
     * 获取用户荣耀积分
     * @param uidSet  用户id
     * @return
     */
    public List<NewUserHonor> findHonorByUid(Set<String> uidSet){
        try {
            /* NewUserHonorData _id为String类型，mongoTemplate通过正常方式是查找不到对应对象的
             * 因此，需要通过调用原始方法才能获取对象
             */
            MongoCollection<Document> collection = mongoTemplate.getDb().getCollection(TABLE_NAME);
            Document find = new Document();
            find.append("_id",new Document("$in",uidSet));
            MongoCursor<Document> iter = collection.find(find).iterator();
            List<NewUserHonor> target = new ArrayList<>();
            while (iter.hasNext()) {
                Document doc = iter.next();
                NewUserHonor newUserHonor = new NewUserHonor();
                newUserHonor.set_id(doc.getString("_id"));
                newUserHonor.setHonorLevel(doc.getInteger("honor_level"));
                newUserHonor.setBeans(doc.getInteger("beans"));
                Long endTime = null;
                try {
                    Integer temp = doc.getInteger("get_time");
                    if(temp != null){
                        endTime = temp.longValue();
                    }
                }catch (Exception e){
                    endTime = doc.getLong("get_time");
                }
                newUserHonor.setGetTime(endTime == null ? 0 : endTime.intValue());
                target.add(newUserHonor);
            }
            return target;
        }catch (Exception e){
            logger.info(e.getMessage());
        }
        return new ArrayList<>();
    }

}
