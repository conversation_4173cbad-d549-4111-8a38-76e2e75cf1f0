package com.quhong.operation.utils;

import org.springframework.util.ObjectUtils;

import java.util.HashMap;
import java.util.Map;


public class CountryEn {
    public static Map<String, String> map = new HashMap<>();

    static {
        map.put("AD", "Andorra");
        map.put("AE", "United Arab Emirates");
        map.put("AF", "Afghanistan");
        map.put("AG", "Antigua & Barbuda");
        map.put("AI", "Anguilla");
        map.put("AL", "Albania");
        map.put("AM", "Armenia");
        map.put("AO", "Angola");
        map.put("AQ", "Antarctica");
        map.put("AR", "Argentina");
        map.put("AS", "American Samoa");
        map.put("AT", "Austria");
        map.put("AU", "Australia");
        map.put("AW", "Aruba");
        map.put("AX", "Åland islands");
        map.put("AZ", "Azerbaijan");
        map.put("BA", "Bosnia & Herzegovina");
        map.put("BB", "Barbados");
        map.put("BD", "Bangladesh");
        map.put("BE", "Belgium");
        map.put("BF", "Burkina");
        map.put("BG", "Bulgaria");
        map.put("BH", "Bahrain");
        map.put("BI", "Burundi");
        map.put("BJ", "Benin");
        map.put("BL", "Saint Barthélemy");
        map.put("BM", "Bermuda");
        map.put("BN", "Brunei");
        map.put("BO", "Bolivia");
        map.put("BQ", "Caribbean Netherlands");
        map.put("BR", "Brazil");
        map.put("BS", "The Bahamas");
        map.put("BT", "Bhutan");
        map.put("BV", "Bouvet Island");
        map.put("BW", "Botswana");
        map.put("BY", "Belarus");
        map.put("BZ", "Belize");
        map.put("CA", "Canada");
        map.put("CC", "Cocos (Keeling) Islands");
        map.put("CF", "Central African Republic");
        map.put("CH", "Switzerland");
        map.put("CL", "Chile");
        map.put("CM", "Cameroon");
        map.put("CO", "Colombia");
        map.put("CR", "Costa Rica");
        map.put("CU", "Cuba");
        map.put("CV", "Cape Verde");
        map.put("CX", "Christmas Island");
        map.put("CY", "Cyprus");
        map.put("CZ", "Czech Republic");
        map.put("DE", "Germany");
        map.put("DJ", "Djibouti");
        map.put("DK", "Denmark");
        map.put("DM", "Dominica");
        map.put("DO", "Dominican Republic");
        map.put("DZ", "Algeria");
        map.put("EC", "Ecuador");
        map.put("EE", "Estonia");
        map.put("EG", "Egypt");
        map.put("EH", "Western Sahara");
        map.put("ER", "Eritrea");
        map.put("ES", "Spain");
        map.put("FI", "Finland");
        map.put("FJ", "Fiji");
        map.put("FK", "Falkland Islands");
        map.put("FM", "Federated States of Micronesia");
        map.put("FO", "Faroe Islands");
        map.put("FR", "France");
        map.put("GA", "Gabon");
        map.put("GD", "Grenada");
        map.put("GE", "Georgia");
        map.put("GF", "French Guiana");
        map.put("GH", "Ghana");
        map.put("GI", "Gibraltar");
        map.put("GL", "Greenland");
        map.put("GN", "Guinea");
        map.put("GP", "Guadeloupe");
        map.put("GQ", "Equatorial Guinea");
        map.put("GR", "Greece");
        map.put("GS", "South Georgia and the South Sandwich Islands");
        map.put("GT", "Guatemala");
        map.put("GU", "Guam");
        map.put("GW", "Guinea-Bissau");
        map.put("GY", "Guyana");
        map.put("HK", "Hong Kong");
        map.put("HM", "Heard Island and McDonald Islands");
        map.put("HN", "Honduras");
        map.put("HR", "Croatia");
        map.put("HT", "Haiti");
        map.put("HU", "Hungary");
        map.put("ID", "Indonesia");
        map.put("IE", "Ireland");
        map.put("IL", "Israel");
        map.put("IM", "Isle of Man");
        map.put("IN", "India");
        map.put("IO", "British Indian Ocean Territory");
        map.put("IQ", "Iraq");
        map.put("IR", "Iran");
        map.put("IS", "Iceland");
        map.put("IT", "Italy");
        map.put("JE", "Jersey");
        map.put("JM", "Jamaica");
        map.put("JO", "Jordan");
        map.put("JP", "Japan");
        map.put("KH", "Cambodia");
        map.put("KI", "Kiribati");
        map.put("KM", "The Comoros");
        map.put("KW", "Kuwait");
        map.put("KY", "Cayman Islands");
        map.put("LB", "Lebanon");
        map.put("LI", "Liechtenstein");
        map.put("LK", "Sri Lanka");
        map.put("LR", "Liberia");
        map.put("LS", "Lesotho");
        map.put("LT", "Lithuania");
        map.put("LU", "Luxembourg");
        map.put("LV", "Latvia");
        map.put("LY", "Libya");
        map.put("MA", "Morocco");
        map.put("MC", "Monaco");
        map.put("MD", "Moldova");
        map.put("ME", "Montenegro");
        map.put("MF", "Saint Martin (France)");
        map.put("MG", "Madagascar");
        map.put("MH", "Marshall islands");
        map.put("MK", "Republic of Macedonia (FYROM)");
        map.put("ML", "Mali");
        map.put("MM", "Myanmar (Burma)");
        map.put("MO", "Macao");
        map.put("MQ", "Martinique");
        map.put("MR", "Mauritania");
        map.put("MS", "Montserrat");
        map.put("MT", "Malta");
        map.put("MV", "Maldives");
        map.put("MW", "Malawi");
        map.put("MX", "Mexico");
        map.put("MY", "Malaysia");
        map.put("NA", "Namibia");
        map.put("NE", "Niger");
        map.put("NF", "Norfolk Island");
        map.put("NG", "Nigeria");
        map.put("NI", "Nicaragua");
        map.put("NL", "Netherlands");
        map.put("NO", "Norway");
        map.put("NP", "Nepal");
        map.put("NR", "Nauru");
        map.put("OM", "Oman");
        map.put("PA", "Panama");
        map.put("PE", "Peru");
        map.put("PF", "French polynesia");
        map.put("PG", "Papua New Guinea");
        map.put("PH", "The Philippines");
        map.put("PK", "Pakistan");
        map.put("PL", "Poland");
        map.put("PN", "Pitcairn Islands");
        map.put("PR", "Puerto Rico");
        map.put("PS", "Palestinian territories");
        map.put("PW", "Palau");
        map.put("PY", "Paraguay");
        map.put("QA", "Qatar");
        map.put("RE", "Réunion");
        map.put("RO", "Romania");
        map.put("RS", "Serbia");
        map.put("RU", "Russian Federation");
        map.put("RW", "Rwanda");
        map.put("SB", "Solomon Islands");
        map.put("SC", "Seychelles");
        map.put("SD", "Sudan");
        map.put("SE", "Sweden");
        map.put("SG", "Singapore");
        map.put("SI", "Slovenia");
        map.put("SJ", "SvalbardandJanMayenIslands");
        map.put("SK", "Slovakia");
        map.put("SL", "Sierra Leone");
        map.put("SM", "San Marino");
        map.put("SN", "Senegal");
        map.put("SO", "Somalia");
        map.put("SR", "Suriname");
        map.put("SS", "South Sudan");
        map.put("ST", "Sao Tome & Principe");
        map.put("SV", "El Salvador");
        map.put("SY", "Syria");
        map.put("SZ", "Swaziland");
        map.put("TC", "Turks & Caicos Islands");
        map.put("TD", "Chad");
        map.put("TG", "Togo");
        map.put("TH", "Thailand");
        map.put("TK", "Tokelau");
        map.put("TL", "Timor-Leste (East Timor)");
        map.put("TN", "Tunisia");
        map.put("TO", "Tonga");
        map.put("TR", "Turkey");
        map.put("TV", "Tuvalu");
        map.put("TZ", "Tanzania");
        map.put("UA", "Ukraine");
        map.put("UG", "Uganda");
        map.put("US", "United States of America (USA)");
        map.put("UY", "Uruguay");
        map.put("VA", "Vatican City (The Holy See)");
        map.put("VE", "Venezuela");
        map.put("VG", "British Virgin Islands");
        map.put("VI", "United States Virgin Islands");
        map.put("VN", "Vietnam");
        map.put("WF", "Wallis and Futuna");
        map.put("WS", "Samoa");
        map.put("YE", "Yemen");
        map.put("YT", "Mayotte");
        map.put("ZA", "South Africa");
        map.put("ZM", "Zambia");
        map.put("ZW", "Zimbabwe");
        map.put("CN", "China");
        map.put("CG", "Republic of the Congo");
        map.put("CD", "Democratic Republic of the Congo");
        map.put("MZ", "Mozambique");
        map.put("GG", "Guernsey");
        map.put("GM", "Gambia");
        map.put("MP", "Northern Mariana Islands");
        map.put("ET", "Ethiopia");
        map.put("NC", "New Caledonia");
        map.put("VU", "Vanuatu");
        map.put("TF", "French Southern Territories");
        map.put("NU", "Niue");
        map.put("UM", "United States Minor Outlying Islands");
        map.put("CK", "Cook Islands");
        map.put("GB", "Great Britain (United Kingdom; England)");
        map.put("TT", "Trinidad & Tobago");
        map.put("VC", "St. Vincent & the Grenadines");
        map.put("TW", "Taiwan");
        map.put("NZ", "New Zealand");
        map.put("SA", "Saudi Arabia");
        map.put("LA", "Laos");
        map.put("KP", "North Korea");
        map.put("KR", "South Korea");
        map.put("PT", "Portugal");
        map.put("KG", "Kyrgyzstan");
        map.put("KZ", "Kazakhstan");
        map.put("TJ", "Tajikistan");
        map.put("TM", "Turkmenistan");
        map.put("UZ", "Uzbekistan");
        map.put("KN", "St. Kitts & Nevis");
        map.put("PM", "Saint-Pierre and Miquelon");
        map.put("SH", "St. Helena & Dependencies");
        map.put("LC", "St. Lucia");
        map.put("MU", "Mauritius");
        map.put("CI", "Côte d'Ivoire");
        map.put("KE", "Kenya");
        map.put("MN", "Mongolia");
    }

    public static String getCountryName(String code) {
        if (ObjectUtils.isEmpty(code) || code.trim().length() < 2) {
            return code;
        }
        code = code.trim();
        String countryCode = code.substring(0, 2).toUpperCase();
        return map.getOrDefault(countryCode, code);
    }
}
