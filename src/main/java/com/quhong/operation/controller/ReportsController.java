package com.quhong.operation.controller;

import com.alibaba.excel.EasyExcel;
import com.alibaba.excel.ExcelWriter;
import com.alibaba.fastjson.JSON;
import com.quhong.datas.DayTimeData;
import com.quhong.enums.HttpCode;
import com.quhong.mongo.config.MongoBean;
import com.quhong.operation.annotation.RequireRole;
import com.quhong.operation.common.ApiResult;
import com.quhong.operation.common.HttpResult;
import com.quhong.operation.server.report.MoneyReportServer;
import com.quhong.operation.server.report.ReportsServer;
import com.quhong.operation.share.mongobean.Actor;
import com.quhong.operation.share.vo.*;
import com.quhong.operation.share.vo.reports.*;
import com.quhong.operation.share.vo.reports.money.MoneyDetailVO;
import com.quhong.operation.share.vo.reports.money.detail.AllMoneyDetailVO;
import com.quhong.operation.share.vo.reports.money.detail.ConsumeMoneyDetailVO;
import com.quhong.operation.share.vo.reports.money.detail.InMoneyDetailVO;
import com.quhong.operation.share.vo.reports.money.detail.UserBeansWaterVO;
import com.quhong.operation.share.vo.reports.msg.MsgGiftVO;
import com.quhong.operation.share.vo.user.ColumnChartVO;
import com.quhong.operation.utils.DateHelper;
import com.quhong.operation.utils.ExcelUtils;
import com.quhong.operation.utils.MongoUtils;
import jakarta.annotation.Resource;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import org.bson.types.ObjectId;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.data.mongodb.core.aggregation.Aggregation;
import org.springframework.data.mongodb.core.query.Criteria;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.multipart.MultipartFile;

import java.io.IOException;
import java.util.*;

/**
 * <AUTHOR>
 * @date 2020/6/11
 */
@RestController
@RequestMapping("/reports")
public class ReportsController {

    private static final Logger logger = LoggerFactory.getLogger(ReportsController.class);

    @Autowired
    private ReportsServer reportsServer;
    @Autowired
    private MoneyReportServer moneyReportServer;
    @Resource(name = MongoBean.MOVIES)
    private MongoTemplate mongoTemplate;

    private static final int WEAK_SECOND = 7 * 24 * 60 * 60;

    /**
     * 用户基本信息报表
     *
     * @param response  response
     * @param file      用户rid文件
     * @param startDate 开始时间(字符串日期)
     * @param endDate   结尾时间(字符串日期)
     */
    @RequireRole(2)
    @RequestMapping("/actorBaseInfoExcel")
    public void actorBaseInfoExcel(HttpServletResponse response, MultipartFile file, String startDate, String endDate) {
        try {
            logger.info("method actorBaseInfoExcel param starDate={}, endDate={}", startDate, endDate);
            if (null == file) {
                logger.info("上传文件为空");
                return;
            }
            List<String> ridList = getWorkbookData(file);
            // 设置时间
            Integer[] timeArr = DateHelper.ARABIAN.getStartOrEndSeconds(startDate, endDate);
            //查询数据
            ApiResult<List<ActorBaseInfoVO>> apiResult = reportsServer.listActorInfoByRid(timeArr[0], timeArr[1], ridList);
            if (!apiResult.isOK()) {
                logger.error("actorBaseInfoExcel two ok error info {}", apiResult.getMsg());
                return;
            }
            ExcelUtils.exportExcel(response, apiResult.getData(), ActorBaseInfoVO.class, "actorBaseInfo", "用户基础信息报表");
            logger.info("method actorBaseInfoExcel 执行完毕");
        } catch (Exception e) {
            logger.info("{}", e.getMessage(), e);
        }
    }

    /**
     * 获取用户重复数据报表
     */
    @RequireRole(2)
    @RequestMapping("/actorRepeatAccountExcel")
    public void actorRepeatAccountExcel(HttpServletResponse response, MultipartFile file, String startDate) {
        logger.info("start get actor repeat accounts excel start = {}", startDate);
        if (null == file) {
            logger.error("上传文件为空");
            return;
        }
        List<String> ridList = getWorkbookData(file);
        Integer start = DateHelper.ARABIAN.stringDateToStampSecond(startDate);
        ApiResult<List<RepeatAccountsVO>> apiResult = reportsServer.getRepeatAccounts(ridList, start);
        if (!apiResult.isOK()) {
            logger.error("actorRepeatAccountExcel error info {}", apiResult.getMsg());
            return;
        }
        logger.info("request getActorByRidList api result list size={}", apiResult.getData().size());
        ExcelUtils.exportExcel(response, apiResult.getData(), RepeatAccountsVO.class, "actorRepeatAccount", "用户重复数据报表");
        logger.info("method actorRepeatAccountExcel 执行完毕");
    }

    /**
     * 查询每日明细
     *
     * @param response
     * @param file
     * @param startDate
     * @param endDate
     */
    @RequireRole(3)
    @RequestMapping("/moneyDetailTotal")
    public void moneyDetailTotal(HttpServletResponse response, MultipartFile file, String startDate, String endDate) {
        logger.info("method moneyDetailTotal param starDate={}, endDate={}", startDate, endDate);
        if (null == file) {
            logger.error("上传文件为空");
            return;
        }
        List<String> ridList = getWorkbookData(file);

        // 设置时间
        Integer[] timeArr = DateHelper.ARABIAN.getStartOrEndSeconds(startDate, endDate);
        ApiResult<MoneyDetailVO> apiResult = reportsServer.moneyDetailTotal(timeArr[0], timeArr[1], ridList);
        if (!apiResult.isOK()) {
            logger.error("actorBaseInfoExcel error info {}", apiResult.getMsg());
            return;
        }

        MoneyDetailVO moneyDetailVO = apiResult.getData();
        List<AllMoneyDetailVO> allMoneyDetailVoList = moneyDetailVO.getAllMoneyDetailVoList();
        List<InMoneyDetailVO> inMoneyDetailVoList = moneyDetailVO.getInMoneyDetailVoList();
        List<ConsumeMoneyDetailVO> consumeMoneyDetailVoList = moneyDetailVO.getConsumeMoneyDetailVoList();
        ExcelWriter excelWriter = null;
        try {
            ExcelUtils.encodeFilename(response, "money_detail_total");
            excelWriter = EasyExcel.write(response.getOutputStream()).registerWriteHandler(ExcelUtils.simpleStyle()).build();
            excelWriter.write(allMoneyDetailVoList, EasyExcel.writerSheet(0, "总明细").head(AllMoneyDetailVO.class).build());
            excelWriter.write(inMoneyDetailVoList, EasyExcel.writerSheet(1, "收入明细").head(InMoneyDetailVO.class).build());
            excelWriter.write(consumeMoneyDetailVoList, EasyExcel.writerSheet(2, "支出明细").head(ConsumeMoneyDetailVO.class).build());
        } catch (IOException e) {
            e.printStackTrace();
        } finally {
            if (excelWriter != null) {
                excelWriter.finish();
            }
        }
        logger.info("method moneyDetailTotal 执行完毕");
    }

    /**
     * 用户钻石流水明细报表
     *
     * @param response  response
     * @param file      用户rid文件
     * @param startDate 开始时间(字符串日期)
     * @param endDate   结尾时间(字符串日期)
     */
    @RequireRole(3)
    @RequestMapping("/moneyDetailExcel")
    public void moneyDetailExcel(HttpServletResponse response, MultipartFile file, String startDate, String endDate) {
        logger.info("method moneyDetailExcel param starDate={}, endDate={}", startDate, endDate);
        if (null == file) {
            logger.info("上传文件为空");
            return;
        }
        List<String> ridList = getWorkbookData(file);
        if (CollectionUtils.isEmpty(ridList)) {
            logger.info("upload file is null！");
            return;
        }

        // 设置时间
        Integer[] timeArr = DateHelper.ARABIAN.getStartOrEndSeconds(startDate, endDate);
        ApiResult<List<UserBeansWaterVO>> apiResult;
        ExcelWriter excelWriter = null;
        try {
            ExcelUtils.encodeFilename(response, "money_detail");
            excelWriter = EasyExcel.write(response.getOutputStream()).registerWriteHandler(ExcelUtils.simpleStyle()).build();
            for (int i = 0; i < ridList.size(); i++) {
                apiResult = reportsServer.moneyDetail(timeArr[0], timeArr[1], Integer.parseInt(ridList.get(i)));
                if (apiResult.isOK() && !CollectionUtils.isEmpty(apiResult.getData())) {
                    excelWriter.write(apiResult.getData(), EasyExcel.writerSheet(i, ridList.get(i)).head(UserBeansWaterVO.class).build());
                }
            }
        } catch (IOException e) {
            e.printStackTrace();
        } finally {
            if (excelWriter != null) {
                excelWriter.finish();
            }
        }
        logger.info("method moneyDetailExcel 执行完毕");
    }

    /**
     * 迎新房报表数据
     *
     * @param response  response
     * @param startDate 开始时间
     * @param endDate   结尾时间
     */
    @RequireRole(2)
    @RequestMapping("/roomActiveInfo")
    public void roomActiveInfo(HttpServletResponse response, String startDate, String endDate) {
        // 设置时间
        Integer[] timeArr = DateHelper.ARABIAN.getStartOrEndSeconds(startDate, endDate);
        List<CompereRoomInfoVO> result = reportsServer.roomActiveInfo(timeArr[0], timeArr[1]);
        ExcelUtils.exportExcel(response, result, CompereRoomInfoVO.class, "welcome_room_data", "迎新房报表");
        logger.info("method roomActiveInfo 执行完毕");
    }

    /**
     * 用户行为情况报表
     *
     * @param response  response
     * @param rid       房间rid
     * @param startDate 开始时间
     * @param endDate   结尾时间
     */
    @RequireRole(2)
    @RequestMapping("/actorActionCondition")
    public void actorActionCondition(HttpServletResponse response, Integer rid, String startDate, String endDate) {
        // 设置时间
        Integer[] timeArr = DateHelper.ARABIAN.getStartOrEndSeconds(startDate, endDate);
        ApiResult<List<ActorActivityVO>> result = reportsServer.roomActorAction(rid, timeArr[0], timeArr[1]);
        List<ActorActivityVO> data = new ArrayList<>();
        if (!result.isOK() || null == result.getData()) {
            logger.error("roomActorAction error info {}", result.getMsg());
        } else {
            data = result.getData();
        }
        ExcelUtils.exportExcel(response, data, ActorActivityVO.class, "actor_active", "用户行为情况报表");
        logger.info("method actorActionCondition 执行完毕");
    }

    /**
     * 迎新房每天信息
     *
     * @param startDate 开始时间
     * @param endDate   结尾时间
     * @return 每天数据
     */
    @RequireRole(2)
    @RequestMapping("/welcomeNewRoomDataInfo")
    public HttpResult<List<WelcomeNewRoomVO>> welcomeNewRoomDataInfo(String startDate, String endDate) {
        logger.info("welcomeNewRoomDataInfo param start={} end={}", startDate, endDate);
        HttpResult<List<WelcomeNewRoomVO>> result = new HttpResult<>();
        // 字符串转时间
        Integer[] time = DateHelper.ARABIAN.getStartOrEndSeconds(startDate, endDate);

        // 时间段转每天日期数组
        String[] dateArr = DateHelper.ARABIAN.getDateDiffArray(time[0], time[1]);
        ApiResult<List<WelcomeNewRoomVO>> apiResult = reportsServer.welcomeNewRoomDataInfo(dateArr);
        if (!apiResult.isOK()) {
            logger.error("get welcome room data info fail, msg={}", apiResult.getMsg());
            return result.error(apiResult.getMsg());
        }

        return result.ok(apiResult.getData());
    }

    /**
     * 迎新房用户活跃程度报表
     *
     * @param response  response
     * @param startDate 开始时间(字符串日期)
     * @param endDate   结尾时间(字符串日期)
     */
    @RequireRole(2)
    @RequestMapping("/welcomeRoomReports")
    public void welcomeRoomReports(HttpServletResponse response, String startDate, String endDate) {
        logger.info("welcomeRoomReports param start={} end={}", startDate, endDate);
        // 字符串转时间
        Integer[] time = DateHelper.ARABIAN.getStartOrEndSeconds(startDate, endDate);
        // 时间段转每天日期数组
        String[] dateArr = DateHelper.ARABIAN.getDateDiffArray(time[0], time[1]);
        ApiResult<List<WelcomeNewRoomVO>> result = reportsServer.welcomeNewRoomDataInfo(dateArr);
        if (!result.isOK()) {
            logger.error("get welcome room data info fail, msg={}", result.getMsg());
            return;
        }
        ExcelUtils.exportExcel(response, result.getData(), WelcomeNewRoomVO.class, "welcome_room_data", "迎新房用户数据报表");
        logger.info("method welcomeRoomReports 执行完毕");
    }

    /**
     * 迎新房新用户留存报表
     *
     * @param response  response
     * @param startDate 开始时间(字符串日期)
     * @param endDate   结尾时间(字符串日期)
     */
    @RequireRole(2)
    @RequestMapping("/welcomeNewRoomKeep")
    public void welcomeNewRoomKeep(HttpServletResponse response, String startDate, String endDate) {
        logger.info("welcomeNewRoomKeep param start={} end={}", startDate, endDate);
        Integer[] time = DateHelper.ARABIAN.getStartOrEndSeconds(startDate, endDate);
        // 时间段转每天日期数组
        String[] dateArr = DateHelper.ARABIAN.getDateDiffArray(time[0], time[1]);
        ApiResult<List<WelcomeNewRoomKeepVO>> apiResult = reportsServer.welcomeNewRoomKeep(dateArr);
        if (!apiResult.isOK()) {
            logger.error("welcomeNewRoomKeep param s={} e={} error msg={}", startDate, endDate, apiResult.getMsg());
            return;
        }
        ExcelUtils.exportExcel(response, apiResult.getData(), WelcomeNewRoomKeepVO.class, "welcome_room_keep", "迎新房用户次留报表");
        logger.info("method welcomeNewRoomKeep 执行完毕");

    }

    /**
     * tap支付的记录
     *
     * @param startDate 开始时间(字符串日期)
     * @param endDate   结尾时间(字符串日期)
     */
    @RequireRole(3)
    @RequestMapping("/tapChargeLog")
    public void tapChargeLog(String startDate, String endDate) {
        logger.info("tapChargeLog param startDate={} endDate={}", startDate, endDate);
        logger.info("method tapChargeLog 执行完毕");
    }

    /**
     * 获取workbook第一sheet第一列，去掉第一行数据
     *
     * @param file workbook文件
     * @return 返回数据
     */
    private List<String> getWorkbookData(MultipartFile file) {
        List<String> result = new ArrayList<>();
        if (null == file) {
            logger.info("上传文件为空");
            return result;
        }
        try {
            Map<String, List<String>> listMap = ExcelUtils.readExcel(file.getInputStream());
            //返回第一列数据
            for (List<String> list : listMap.values()) {
                return list;
            }
        } catch (IOException e) {
            logger.error("获取文件内容失败");
            e.printStackTrace();
        }
        return result;
    }

    /**
     * 线上Google支付和Apple支付信息报表
     *
     * @param response  response
     * @param startDate 开始时间(字符串日期)
     * @param endDate   结尾时间(字符串日期)
     */
    @RequireRole(3)
    @RequestMapping("/onlinePayLog")
    public void onlinePayLog(HttpServletResponse response, String startDate, String endDate) {
        logger.info("onlinePayLog param s={} e={}", startDate, endDate);
        Integer[] timeArr = DateHelper.ARABIAN.getStartOrEndSeconds(startDate, endDate);
        ApiResult<List[]> result = moneyReportServer.getOnlineChargeInfo(timeArr[0], timeArr[1]);
        ExcelWriter excelWriter = null;
        try {
            ExcelUtils.encodeFilename(response, "online_pay_Info");
            excelWriter = EasyExcel.write(response.getOutputStream()).registerWriteHandler(ExcelUtils.simpleStyle()).build();
            excelWriter.write(result.getData()[0], EasyExcel.writerSheet(0, "付费情况").head(OnlinePayInfoVO.class).build());
            excelWriter.write(result.getData()[1], EasyExcel.writerSheet(1, "7天内注册的用户付费情况").head(OnlinePayInfoVO.class).build());
            excelWriter.write(result.getData()[2], EasyExcel.writerSheet(2, "商品销售情况").head(OnlinePayGoodsMarketVO.class).build());
        } catch (IOException e) {
            e.printStackTrace();
        } finally {
            if (excelWriter != null) {
                excelWriter.finish();
            }
        }
        logger.info("method onlinePayLog 执行完毕");
    }

    /**
     * 获取迎新房新用户留存数据报表
     */
    @RequireRole(2)
    @RequestMapping("/rookie/remain")
    public HttpResult<List> rookieRemain(HttpServletRequest request) {
        HttpResult<List> result = new HttpResult<>();
        String channel = request.getParameter("channel");
        String startDate = request.getParameter("startDate");
        String endDate = request.getParameter("endDate");
        logger.info("start into rookie remain channel = {},start date = {},end date = {}", channel, startDate, endDate);
        ApiResult<Map<String, Map<String, Object>>> apiResult = reportsServer.getRookieNewUserCount(channel, startDate, endDate);
        if (!apiResult.isOK()) {
            return result.error(apiResult.getMsg());
        }
        //sort6.3
        Map<String, Map<String, Object>> data = apiResult.getData();
        TreeSet<String> dates = new TreeSet<>(data.keySet());
        List<Map> list = new ArrayList<>();
        for (String date : dates) {
            Map<String, Map<String, Object>> target = new HashMap<>();
            target.put(date, data.get(date));
            list.add(target);
        }
        return result.ok(list);
    }

    /**
     * 迎新房用户留存数据报表下载
     */
    @RequireRole(2)
    @RequestMapping("/rookie/remain/download")
    public void rookieRemainDownload(HttpServletRequest request, HttpServletResponse response) {
        String channel = request.getParameter("channel");
        String startDate = request.getParameter("startDate");
        String endDate = request.getParameter("endDate");
        logger.info("start into rookie remain download channel = {},start date = {},end date = {}", channel, startDate, endDate);
        ApiResult<Map<String, Map<String, Object>>> apiResult = reportsServer.getRookieNewUserCount(channel, startDate, endDate);
        if (!apiResult.isOK()) {
            return;
        }
        Map<String, Map<String, Object>> data = apiResult.getData();
        //拼接title list
        List<String> titleList = new ArrayList<>();
        titleList.add("日期");
        for (int i = 0; i < data.keySet().size() - 1; i++) {
            if (i == 0 || i == 6 || i == 29) {
                titleList.add("第 " + i + " 天");
                titleList.add("第 " + (i + 1) + " 天");
                titleList.add("留存率");
            } else {
                titleList.add("第 " + (i + 1) + " 天");
            }
        }
        //设置每一行的具体值
        List<List<String>> lists = new ArrayList<>();
        //sort 按照日期进行排序
        TreeSet<String> dates = new TreeSet<>(data.keySet());
        for (String date : dates) {
            List<String> rowList = new ArrayList<>();
            rowList.add(date);
            Map<String, Object> rowMap = data.get(date);
            for (int i = 0; i < rowMap.size() / 2; i++) {
                String countKey = "count_" + i;
                rowList.add(rowMap.get(countKey).toString());
                if (i == 1 || i == 7 || i == 30) {
                    rowList.add(rowMap.get("rate_" + i).toString());
                }
            }
            lists.add(rowList);
        }
        ExcelUtils.exportExcel(response, lists, titleList, "rookie_room_remain", "迎新房新用户留存数据报表");
        logger.info("method rookie remain download report finished");
    }

    /**
     * 最近一周报表
     */
    @RequireRole(2)
    @RequestMapping("/weak/newUser/download")
    public void downloadTest(HttpServletResponse response) {
        long currentStamp = new Date().getTime() / 1000;
        int end = (int) currentStamp;
        int start = end - WEAK_SECOND;
        String endUid = MongoUtils.create_idBySecond(end);
        String startUid = MongoUtils.create_idBySecond(start);
        Criteria criteria = Criteria.where("_id").gte(new ObjectId(startUid)).lte(new ObjectId(endUid));
        Aggregation aggregation = Aggregation.newAggregation(Aggregation.match(criteria));
        logger.info("query sql = {}", aggregation.toString());
        List<Actor> actorList = mongoTemplate.aggregate(aggregation, "actor", Actor.class).getMappedResults();
        logger.info("find new user start = {} end = {} size = {}", start, end, actorList.size());
        List<String> titleList = new ArrayList<>();
        titleList.add("uid");
        titleList.add("rid");
        List<List<String>> lists = new ArrayList<>();
        for (Actor actor : actorList) {
            List<String> list = new ArrayList<>();
            list.add(actor.get_id().toString());
            list.add(String.valueOf(actor.getRid()));
            lists.add(list);
        }
        ExcelUtils.exportExcel(response, lists, titleList, "new_users", "新增用户id报表");
        logger.info("find new user report finished");
    }

    /**
     * 房间用户统计报表
     *
     * @param response  response
     * @param startDate 开始时间(字符串日期)
     * @param endDate   结尾时间(字符串日期)
     * @param os        用户操作系统 0安卓 1苹果
     */
    @RequireRole(2)
    @RequestMapping("/roomUserStatReports")
    public void roomUserStatReports(HttpServletResponse response, String startDate, String endDate, Integer os) {
        logger.info("roomUserStatReports param start={} end={} os={}", startDate, endDate, os);
        if (os != null && (os < 0 || os > 1)) {
            os = null;
        }
        Integer[] timeArr = DateHelper.ARABIAN.getStartOrEndSeconds(startDate, endDate);
        List<RoomUserStatVO> result = reportsServer.roomUserStat(timeArr[0], --timeArr[1], os, 0);
        ExcelUtils.exportExcel(response, result, RoomUserStatVO.class, "room_user_stat", "房间用户统计报表");
        logger.info("method roomUserStatReports done");
    }

    /**
     * 房间用户统计报表
     *
     * @param startDate 开始时间(字符串日期)
     * @param endDate   结尾时间(字符串日期)
     * @param os        用户操作系统 0安卓 1苹果
     */
    @RequireRole(2)
    @RequestMapping("/roomUserStatList")
    public ApiResult<Object> roomUserStatList(String startDate, String endDate, Integer os) {
        if (os != null && (os < 0 || os > 1)) {
            os = null;
        }
        logger.info("roomUserStatList param start={} end={} os={}", startDate, endDate, os);
        Integer[] timeArr = DateHelper.ARABIAN.getStartOrEndSeconds(startDate, endDate);
        String[] titleZh = new String[]{"日期", "日活", "新增", "创建房间人数", "创建房间次数", "进房人数", "进房次数", "人均进房次数", "上麦人数", "上麦次数", "发言人数", "发言次数", "送礼人数", "送礼次数", "人均房间停留(min)", "人均上麦时长(min)"};
        List<RoomUserStatVO> roomUserStat = reportsServer.roomUserStat(timeArr[0], --timeArr[1], os, 0);
        Map<String, Object> result = new HashMap<>();
        result.put("data", roomUserStat);
        result.put("zh", titleZh);
        result.put("en", titleZh);
        return new ApiResult<>().ok(0, result);
    }

    /**
     * 房间用户统计echarts调用
     *
     * @param startDate 开始时间(字符串日期)
     * @param endDate   结尾时间(字符串日期)
     * @param os        用户操作系统 0安卓 1苹果
     * @param type      数据类型 0所有
     */
    @RequireRole(2)
    @RequestMapping("/roomUserStat")
    public ApiResult<Object> roomUserStat(String startDate, String endDate, Integer os, int type) {
        if (os != null && (os < 0 || os > 1)) {
            os = null;
        }
        logger.info("roomUserStat param start={} end={} os={} type={}", startDate, endDate, os, type);
        Integer[] timeArr = DateHelper.ARABIAN.getStartOrEndSeconds(startDate, endDate);
        List<RoomUserStatVO> roomUserStat = reportsServer.roomUserStat(timeArr[0], --timeArr[1], os, type);
        return new ApiResult<>().ok(0, roomUserStat);
    }

    /**
     * 每日钻石商品销售报表下载
     */
    @RequireRole(4)
    @RequestMapping("/firstChargeReports")
    public void firstChargeReports(HttpServletResponse response, String startDate, String endDate, Integer os) {
        if (os != null && (os < 0 || os > 1)) {
            os = -1;
        }
        logger.info("firstChargeReports param start={} end={} os={}", startDate, endDate, os);
        Integer[] timeArr = DateHelper.ARABIAN.getStartOrEndSeconds(startDate, endDate);
        List<FirstChargeStatVO> firstChargeStat = reportsServer.firstChargeStat(timeArr[0], --timeArr[1], os);
        ExcelUtils.exportExcel(response, firstChargeStat, FirstChargeStatVO.class, "first_charge_stat", "每日钻石商品销售");
        logger.info("method firstChargeReports done");
    }

    /**
     * 每日钻石商品销售数据统计
     */
    @RequireRole(4)
    @RequestMapping("/firstChargeList")
    public ApiResult<Object> firstChargeList(String startDate, String endDate, Integer os) {
        if (os != null && (os < 0 || os > 1)) {
            os = -1;
        }
        logger.info("firstChargeList param start={} end={} os={}", startDate, endDate, os);
        Integer[] timeArr = DateHelper.ARABIAN.getStartOrEndSeconds(startDate, endDate);
        List<FirstChargeStatVO> firstChargeStat = reportsServer.firstChargeStat(timeArr[0], --timeArr[1], os);
        String[] titleZh = new String[]{"日期", "0.99购买人数", "0.99购买次数", "0.99购买金额", "4.99购买人数", "4.99购买次数", "4.99购买金额", "19.99购买人数", "19.99购买次数", "19.99购买金额", "49.99购买人数", "49.99购买次数", "49.99购买金额", "89.99购买人数", "89.99购买次数", "89.99购买金额", "199.99购买人数", "199.99购买次数", "199.99购买金额", "299.99购买人数", "299.99购买次数", "299.99购买金额", "总收入"};
        Map<String, Object> result = new HashMap<>();
        result.put("data", firstChargeStat);
        result.put("en", titleZh);
        result.put("zh", titleZh);
        return new ApiResult<>().ok(0, result);
    }

    /**
     * 房间在线时长分布数据统计
     */
    @RequireRole(2)
    @RequestMapping("/roomOnlineStat")
    public ApiResult<Object> roomOnlineStat(String startDate, String endDate) {
        Integer[] timeArr = DateHelper.ARABIAN.getStartOrEndSeconds(startDate, endDate);
        logger.info("roomOnlineStat param start={} end={} ctimeStart={} ctimeEnd={}", startDate, endDate, timeArr[0], timeArr[1]);
        RoomOnlineStatVO roomOnlineStatVO = reportsServer.roomOnlineStat(timeArr[0], --timeArr[1]);
        Map<String, Object> result = new HashMap<>();
        result.put("data", null == roomOnlineStatVO ? null : roomOnlineStatVO.convertToArray());
        return new ApiResult<>().ok(0, result);
    }

    /**
     * 24小时房间在线人数
     */
    @RequireRole(2)
    @RequestMapping("/roomUserOnlineByDay")
    public ApiResult<Object> roomUserOnlineByDay(String date) {
        Integer[] timeArr = DateHelper.ARABIAN.getTimeWhereRange(date);
        logger.info("roomUserOnlineByDay param date={} ctimeStart={} ctimeEnd={}", date, timeArr[0], timeArr[1]);
        List<RoomUserOnlineVO> roomUserOnlineByDay = reportsServer.roomUserOnlineByDay(timeArr[0], --timeArr[1]);
        Map<String, Object> result = new HashMap<>();
        result.put("data", roomUserOnlineByDay);
        return new ApiResult<>().ok(0, result);
    }

    /**
     * 用户在房间停留时长分布
     */
    @RequireRole(2)
    @RequestMapping("/roomUserOnlineStat")
    public ApiResult<Object> roomUserOnlineStat(String startDate, String endDate, String os, String userType) {
        Integer[] timeArr = DateHelper.ARABIAN.getStartOrEndSeconds(startDate, endDate);
        logger.info("roomUserOnlineStat param start={} end={} os={} userType={}", startDate, endDate, os, userType);
        RoomOnlineStatVO roomOnlineStatVO = reportsServer.roomUserOnlineStat(timeArr[0], --timeArr[1], os, userType);
        Map<String, Object> result = new HashMap<>();
        result.put("data", null == roomOnlineStatVO ? null : roomOnlineStatVO.convertToArray());
        return new ApiResult<>().ok(0, result);
    }

    /**
     * 用户上麦时长分布
     */
    @RequireRole(2)
    @RequestMapping("/userUpMicStat")
    public ApiResult<Object> userUpMicStat(String startDate, String endDate, String os, String userType) {
        Integer[] timeArr = DateHelper.ARABIAN.getStartOrEndSeconds(startDate, endDate);
        logger.info("userUpMicStat param start={} end={} os={} userType={}", startDate, endDate, os, userType);
        RoomOnlineStatVO roomOnlineStatVO = reportsServer.userUpMicStat(timeArr[0], --timeArr[1], os, userType);
        Map<String, Object> result = new HashMap<>();
        result.put("data", null == roomOnlineStatVO ? null : roomOnlineStatVO.convertToArray());
        return new ApiResult<>().ok(0, result);
    }

    /**
     * 华为渠道新增用户统计报表下载
     *
     * @param response  response
     * @param startDate 开始日期
     * @param endDate   结束日期
     */
    @RequireRole(2)
    @RequestMapping("/huaWeiPayChargeReports")
    public void huaWeiPayChargeReports(HttpServletResponse response, String startDate, String endDate) {
        logger.info("huaWeiPayChargeReports param s={}, e={}", startDate, endDate);
        Integer[] timeArr = DateHelper.ARABIAN.getStartOrEndSeconds(startDate, endDate);
        List<HuaWeiPayChargeVO> result = reportsServer.huaWeiPayChargeReports(timeArr[0], --timeArr[1]);
        ExcelUtils.exportExcel(response, result, HuaWeiPayChargeVO.class, "huaWeiPayChargeReports", "华为渠道新增用户统计");
        logger.info("method huaWeiPayChargeReports done");
    }

    /**
     * 华为渠道新增用户统计报表
     *
     * @param startDate 开始日期
     * @param endDate   结束日期
     */
    @RequireRole(2)
    @RequestMapping("/huaWeiPayChargeList")
    public ApiResult<Object> huaWeiPayChargeList(String startDate, String endDate) {
        logger.info("huaWeiPayChargeList param s={}, e={}", startDate, endDate);
        Integer[] timeArr = DateHelper.ARABIAN.getStartOrEndSeconds(startDate, endDate);
        Map<String, Object> result = new HashMap<>();
        result.put("data", reportsServer.huaWeiPayChargeReports(timeArr[0], --timeArr[1]));
        return new ApiResult<>().ok(0, result);
    }


    /**
     * 亲密度数据报表
     *
     * @param startDate 开始日期
     * @param endDate   结束日期
     * @param user      新1、-1全部
     */
    @RequireRole
    @RequestMapping("/friendshipReports")
    public void friendshipReports(HttpServletResponse response, String startDate, String endDate, Integer user) {
        logger.info("friendshipReports param s={}, e={} user={}", startDate, endDate, user);
        if (null == user) {
            user = -1;
        }
        Integer[] timeArr = DateHelper.ARABIAN.getStartOrEndSeconds(startDate, endDate);
        List<FriendshipVO> result = reportsServer.friendship(timeArr[0], --timeArr[1], user);
        ExcelUtils.exportExcel(response, result, FriendshipVO.class, "friendshipReports", "亲密度数据报表");
    }

    /**
     * 新注册用户推荐数据统计
     *
     * @param startDate 开始日期
     * @param endDate   结束日期
     */
    @RequireRole(3)
    @RequestMapping("/relationshipNew")
    public ApiResult<Object> relationshipNew(String startDate, String endDate) {
        logger.info("relationshipNew param s={}, e={}", startDate, endDate);
        Integer[] timeArr = DateHelper.ARABIAN.getStartOrEndSeconds(startDate, endDate);
        Map<String, Object> result = new HashMap<>();
        result.put("data", reportsServer.relationshipNew(timeArr[0], --timeArr[1]));
        return new ApiResult<>().ok(0, result);
    }

    /**
     * 新注册用户推荐数据统计
     *
     * @param startDate 开始日期
     * @param endDate   结束日期
     */
    @RequireRole(3)
    @RequestMapping("/relationshipNewReports")
    public void relationshipNewReports(HttpServletResponse response, String startDate, String endDate) {
        logger.info("relationshipReports param s={}, e={}", startDate, endDate);
        Integer[] timeArr = DateHelper.ARABIAN.getStartOrEndSeconds(startDate, endDate);
        List<RelationshipNewVO> result = reportsServer.relationshipNew(timeArr[0], --timeArr[1]);
        ExcelUtils.exportExcel(response, result, RelationshipNewVO.class, "relationshipNewReports", "新注册用户推荐数据统计");
    }

    /**
     * 回访用户推荐数据统计
     *
     * @param startDate 开始日期
     * @param endDate   结束日期
     */
    @RequireRole(3)
    @RequestMapping("/relationshipReturn")
    public ApiResult<Object> relationshipReturn(String startDate, String endDate) {
        logger.info("relationshipReturn param s={}, e={}", startDate, endDate);
        Integer[] timeArr = DateHelper.ARABIAN.getStartOrEndSeconds(startDate, endDate);
        Map<String, Object> result = new HashMap<>();
        result.put("data", reportsServer.relationshipReturn(timeArr[0], --timeArr[1]));
        return new ApiResult<>().ok(0, result);
    }

    /**
     * 回访用户推荐数据统计
     *
     * @param startDate 开始日期
     * @param endDate   结束日期
     */
    @RequireRole(3)
    @RequestMapping("/relationshipReturnReports")
    public void relationshipReturnReports(HttpServletResponse response, String startDate, String endDate) {
        logger.info("relationshipReturnReports param s={}, e={}", startDate, endDate);
        Integer[] timeArr = DateHelper.ARABIAN.getStartOrEndSeconds(startDate, endDate);
        List<RelationshipReturnVO> result = reportsServer.relationshipReturn(timeArr[0], --timeArr[1]);
        ExcelUtils.exportExcel(response, result, RelationshipReturnVO.class, "relationshipReturnReports", "回访用户推荐数据统计");
    }

    /**
     * 私信发礼物报表下载
     *
     * @param response
     * @param start
     * @param end
     * @param os
     * @param userType
     */
    @RequestMapping("/msg_gift_report/download")
    public void downloadMsgGiftReport(HttpServletResponse response, String start, String end, Integer os, Integer userType) {
        try {
            logger.info("begin download msg_gift report. start={} end={} os={} userType={}", start, end, os, userType);
            //参数校验
            if (StringUtils.isEmpty(start) || StringUtils.isEmpty(end)) {
                logger.error("download msg_gift report param error. start={} end={} os={} userType={}", start, end, os, userType);
                response.getWriter().write(JSON.toJSONString(new ApiResult<>().error("param error")));
                return;
            }
            if (os == null) {
                os = -1;
            }
            if (userType == null) {
                userType = -1;
            }
            //下载操作
            List<MsgGiftVO> list = new ArrayList<>();
            List<DayTimeData> dayTimeList = com.quhong.core.utils.DateHelper.ARABIAN.getContinuesDays(start, end);
            for (DayTimeData dayTimeData : dayTimeList) {
                MsgGiftVO statData = reportsServer.getDayMsgGift(dayTimeData, os, userType);
                list.add(statData);
            }
            ExcelUtils.exportExcel(response, list, MsgGiftVO.class, "msg_gift_report", "gift");
            logger.info("finish download msg_gift_report. ");
        } catch (IOException e) {
            logger.error("download msg_gift report error. {}", e.getMessage(), e);
        }
    }

    @RequireRole
    @RequestMapping("/feedback")
    public HttpResult<PageResultVO> feedbackReport(String start, String end, Integer page, Integer status, String username, Integer replayStatus) {
        HttpResult<PageResultVO> httpResult = new HttpResult<>();
        try {
            logger.info("begin get feedback report. start={} end={} page={} status={} username={} replay_status={}", start, end, page, status, username, replayStatus);
            //参数校验
            if (StringUtils.isEmpty(start) || StringUtils.isEmpty(end) || status == null || StringUtils.isEmpty(username) || replayStatus == null) {
                logger.error("feed back report param error. start={} end={} page={} status={} username={} replay_status={}", start, end, page, status, username, replayStatus);
                return httpResult.error(HttpCode.PARAM_ERROR.getCode(), HttpCode.PARAM_ERROR.getMsg());
            }
            if ("全部".equals(username)) {
                username = null;
            }
            if (page == null) {
                page = 1;
            }
            PageResultVO<FeedBackReportVO> feedbackReport = reportsServer.getFeedbackReport(start, end, page, status, username, replayStatus);
            return httpResult.ok(feedbackReport);
        } catch (Exception e) {
            logger.error("report feedback error. {}", e.getMessage(), e);
        }
        return httpResult.error(HttpCode.SERVER_ERROR.getMsg());
    }

    @RequireRole(2)
    @RequestMapping("/feedback/update")
    public HttpResult<String> updateFBK(int status, String manager, String remark, String reportId, int replayStatus) {
        HttpResult<String> httpResult = new HttpResult<>();
        try {
            logger.info("begin update feedback data. status={} manager={} remark={} report_id={} reply_status={}", status, manager, remark, reportId, replayStatus);
            //参数校验
            if (StringUtils.isEmpty(reportId)) {
                logger.error("update feedback data param error. status={} manager={} remark={} report_id={} reply_status={}", status, manager, remark, reportId, replayStatus);
                return httpResult.error(HttpCode.PARAM_ERROR.getCode(), HttpCode.PARAM_ERROR.getMsg());
            }
            reportsServer.updateFeedBackHandle(status, manager, remark, reportId, replayStatus);
            return httpResult.ok(null);
        } catch (Exception e) {
            logger.error("update feedback data error. {}", e.getMessage(), e);
        }
        return httpResult.error(HttpCode.SERVER_ERROR.getMsg());
    }

    /**
     * 员工迎新数据
     *
     * @param startDate 开始日期
     * @param endDate   结束日期
     */
    @RequireRole
    @RequestMapping("/staffWelcome")
    public ApiResult<Object> staffWelcome(String startDate, String endDate) {
        logger.info("staffWelcome param s={}, e={}", startDate, endDate);
        Integer[] timeArr = DateHelper.ARABIAN.getStartOrEndSeconds(startDate, endDate);
        Map<String, Object> result = new HashMap<>();
        result.put("data", reportsServer.staffWelcome(timeArr[0], --timeArr[1]));
        return new ApiResult<>().ok(0, result);
    }

    /**
     * 员工迎新数据报表下载
     *
     * @param startDate 开始日期
     * @param endDate   结束日期
     */
    @RequireRole
    @RequestMapping("/staffWelcomeReports")
    public void staffWelcomeReports(HttpServletResponse response, String startDate, String endDate) {
        logger.info("staffWelcomeReports param s={}, e={}", startDate, endDate);
        Integer[] timeArr = DateHelper.ARABIAN.getStartOrEndSeconds(startDate, endDate);
        List<StaffWelcomeVO> result = reportsServer.staffWelcome(timeArr[0], --timeArr[1]);
        ExcelUtils.exportExcel(response, result, StaffWelcomeVO.class, "staffWelcome", "员工迎新数据");
    }

    /**
     * api接口列表数据
     *
     * @param startDate 开始日期
     * @param endDate   结束日期
     * @param type      1=api,2=mars,3=resource,4=rtc
     * @param os        手机系统
     * @param name      接口名字
     */
    @RequireRole
    @RequestMapping("/apiReportList")
    public ApiResult<Object> apiReportList(String startDate, String endDate, String rid, Integer type, Integer os, String name, Integer page, Integer pageSize) {
        logger.info("apiReportList param s={}, e={} type={} os={} name={} page={} pageSize={}", type, startDate, endDate, os, name, page, pageSize);
        if (null == page) {
            page = 1;
        }
        if (null == pageSize) {
            pageSize = 20;
        }
        Integer[] timeArr = DateHelper.ARABIAN.getStartOrEndSeconds(startDate, endDate);
        Map<String, Object> result = new HashMap<>();
        result.put("data", reportsServer.apiReportList(timeArr[0], --timeArr[1], rid, type, os, name, page, pageSize));
        return new ApiResult<>().ok(0, result);
    }

    /**
     * api接口统计数据
     *
     * @param startDate 开始日期
     * @param endDate   结束日期
     * @param type      1=api,2=mars,3=resource,4=rtc
     * @param os        手机系统
     * @param name      接口名字
     */
    @RequireRole
    @RequestMapping("/apiReportStat")
    public ApiResult<Object> apiReportStat(String startDate, String endDate, Integer rid, Integer type, Integer os, String name, String countryCode) {
        logger.info("apiReportStat param s={}, e={} os={} name={}", startDate, endDate, os, name);
        Integer[] timeArr = DateHelper.ARABIAN.getStartOrEndSeconds(startDate, endDate);
        Map<String, Object> result = new HashMap<>();
        result.put("data", reportsServer.apiReportStat(timeArr[0], --timeArr[1], rid, type, os, name, countryCode));
        return new ApiResult<>().ok(0, result);
    }

    @RequestMapping("/roomMsgReports")
    public void roomMsgReports(HttpServletResponse response, int start, int end, String roomId) {
        logger.info("roomMsgReports param s={}, e={} roomId={}", start, end, roomId);
        List<RoomMsgVO> result = reportsServer.roomMsgReports(start, end, roomId);
        ExcelUtils.exportExcel(response, result, RoomMsgVO.class, "roomMsgReports", "房间公屏消息");
    }

    @RequestMapping("/enterRoomReports")
    public void enterRoomReports(HttpServletResponse response, int start, int end, String roomId) {
        logger.info("enterRoomReports param s={}, e={} roomId={}", start, end, roomId);
        List<EnterRoomUserVO> result = reportsServer.enterRoomReports(start, end, roomId);
        ExcelUtils.exportExcel(response, result, EnterRoomUserVO.class, "enterRoomReports", "进房间用户");
    }

    /**
     * 充值大盘数据（折线图）
     */
    @RequireRole(3)
    @RequestMapping("/rechargeDiamond")
    public HttpResult<ColumnChartVO> rechargeDiamondReports(@RequestParam String uid) {
        HttpResult<ColumnChartVO> result = new HttpResult<>();
        logger.info("select user recharge diamond report. uid={}", uid);
        ColumnChartVO vo = reportsServer.rechargeDiamondReports();
        return result.ok(vo);
    }

    /**
     * 检测记录
     */
    @RequireRole
    @RequestMapping("/detectUserRecord")
    public HttpResult<PageResultVO<DetectUserRecordVO>> detectUserRecord(@RequestParam String rid, @RequestParam Integer detectType, @RequestParam Integer page, @RequestParam Integer pageSize) {
        HttpResult<PageResultVO<DetectUserRecordVO>> result = new HttpResult<>();
        logger.info("detectUserRecord param detectType={}, page={}, pageSize={}", detectType, page, pageSize);
        return result.ok(reportsServer.detectUserRecord(rid, detectType, page, pageSize));
    }

    /**
     * 检测记录下载
     */
    @RequireRole
    @RequestMapping("/detectUserRecordReports")
    public void detectUserRecordReports(HttpServletResponse response, @RequestParam String rid, @RequestParam Integer detectType) {
        logger.info("detectUserRecordReports param rid={} detectType={}", rid, detectType);
        List<DetectUserRecordVO> result = reportsServer.detectUserRecord(rid, detectType, 1, 100000).getList();
        ExcelUtils.exportExcel(response, result, DetectUserRecordVO.class, "detectUserRecordReports", "bigo检测记录数据");
    }

    /**
     * 检测图片放行
     */
    @RequireRole(3)
    @RequestMapping("/detectImgPass")
    public HttpResult<Object> detectImgPass(@RequestParam String uid, @RequestParam Integer id) {
        logger.info("detectImgPass. uid={}, id={}", uid, id);
        return reportsServer.detectImgPass(id);
    }
}
