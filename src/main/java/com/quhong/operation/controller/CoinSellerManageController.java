package com.quhong.operation.controller;

import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.toolkit.StringUtils;
import com.quhong.core.config.ServerConfig;
import com.quhong.datas.HttpResult;
import com.quhong.enums.HttpCode;
import com.quhong.exception.CommonH5Exception;
import com.quhong.operation.annotation.OperationLog;
import com.quhong.operation.annotation.RequireRole;
import com.quhong.operation.constant.AccountConstant;
import com.quhong.operation.enums.OperationType;
import com.quhong.operation.server.CoinSellerManageService;
import com.quhong.operation.share.condition.CoinSellerCondition;
import com.quhong.operation.share.dto.CoinSellerDTO;
import com.quhong.operation.share.dto.CoinSellerRechargeDTO;
import com.quhong.operation.share.vo.*;
import com.quhong.operation.utils.DateHelper;
import com.quhong.operation.utils.ExcelUtils;
import com.quhong.vo.PageVO;
import jakarta.annotation.Resource;
import jakarta.servlet.http.HttpServletResponse;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.BeanUtils;
import org.springframework.util.CollectionUtils;
import org.springframework.web.bind.annotation.*;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;

/**
 * 币商管理
 *
 * <AUTHOR>
 * @date 2023/6/7
 */
@RestController
@RequestMapping("coinSeller")
public class CoinSellerManageController {

    private static final Logger logger = LoggerFactory.getLogger(CoinSellerManageController.class);

    private static final List<String> ONLY_READ_UID_LIST = Arrays.asList("64a2a9c2de6000002e006803", "64a6623b0a4956b5fbc3f410", "655ddafdcb0d00009a004f26", "6579103476260000d5003ac1", "65b44c7fec955218362ab91a");

    @Resource
    private CoinSellerManageService coinSellerManageService;

    /**
     * 新增币商
     */
    @OperationLog(apiName = "新增币商", operationType = OperationType.INSERT)
    @RequireRole(5)
    @PostMapping("/insert")
    public HttpResult<Object> insertCoinSellerData(@RequestParam String uid, @RequestBody CoinSellerDTO dto) {
        logger.info("insert coin seller data. uid={} dto={}", uid, JSONObject.toJSONString(dto));
        checkPermissions(uid, false);
        if (StringUtils.isEmpty(dto.getRid())) {
            throw new CommonH5Exception(new HttpCode(1, "用户id不能为空"));
        }
        if (dto.getMerchantType() == null) {
            throw new CommonH5Exception(new HttpCode(1, "币商类型不能为空"));
        }
        if (dto.getMerchantType() != 1) {
            throw new CommonH5Exception(new HttpCode(1, "币商类型参数错误"));
        }
        coinSellerManageService.insertCoinSellerData(dto);
        return HttpResult.getOk();
    }

    /**
     * 币商列表
     */
    @RequireRole(5)
    @PostMapping("/list")
    public HttpResult<PageResultVO<CoinSellerVO>> selectCoinSellerList(@RequestParam String uid, @RequestBody CoinSellerCondition condition) {
        logger.info("select coin seller list. uid={} condition={}", uid, JSONObject.toJSONString(condition));
        // checkPermissions(uid, true);
        return HttpResult.getOk(coinSellerManageService.selectCoinSellerList(condition));
    }

    /**
     * 修改币商状态
     */
    @OperationLog(apiName = "修改币商状态", operationType = OperationType.UPDATE)
    @RequireRole(5)
    @PostMapping("/updateStatus")
    public HttpResult<Object> updateStatus(@RequestParam String uid, @RequestBody CoinSellerDTO dto) {
        logger.info("update coin seller status. uid={} dto={}", uid, JSONObject.toJSONString(dto));
        checkPermissions(uid, false);
        if (StringUtils.isEmpty(dto.getUid()) || dto.getStatus() == null
                || (dto.getStatus() != 1 && dto.getStatus() != 2)) {
            throw new CommonH5Exception(HttpCode.PARAM_ERROR);
        }
        coinSellerManageService.updateStatus(dto);
        return HttpResult.getOk();
    }

    /**
     * 修改币商信息
     */
    @OperationLog(apiName = "修改币商信息", operationType = OperationType.UPDATE)
    @RequireRole(5)
    @PostMapping("/update")
    public HttpResult<Object> update(@RequestParam String uid, @RequestBody CoinSellerDTO dto) {
        logger.info("update coin seller data. uid={} dto={}", uid, JSONObject.toJSONString(dto));
        checkPermissions(uid, false);
        if (StringUtils.isEmpty(dto.getUid())) {
            throw new CommonH5Exception(HttpCode.PARAM_ERROR);
        }
        coinSellerManageService.update(dto);
        return HttpResult.getOk();
    }

    /**
     * 管理员给币商充值
     */
    @OperationLog(apiName = "管理员给币商充值", operationType = OperationType.UNKNOWN)
    @RequireRole(5)
    @PostMapping("/recharge")
    public HttpResult<Object> recharge(@RequestParam String uid, @RequestBody CoinSellerRechargeDTO dto) {
        logger.info("coin seller recharge. uid={} dto={}", uid, JSONObject.toJSONString(dto));
        checkPermissions(uid, false);
        if (StringUtils.isEmpty(dto.getUid())) {
            throw new CommonH5Exception(HttpCode.PARAM_ERROR);
        }
        if (StringUtils.isEmpty(dto.getRechargeAmount())) {
            throw new CommonH5Exception(new HttpCode(1, "充值金额不能为空"));
        }
        if (dto.getRechargeCoins() == null) {
            throw new CommonH5Exception(new HttpCode(1, "充值金币数不能为空"));
        }
        if (dto.getRechargeCoins() == 0) {
            throw new CommonH5Exception(new HttpCode(1, "充值金币数不能为0"));
        }
        if (dto.getRechargeType() == 0) {
            throw new CommonH5Exception(new HttpCode(1, "充值类型不能为空"));
        }
        if (StringUtils.isEmpty(dto.getPayMethod())) {
            throw new CommonH5Exception(new HttpCode(1, "支付方式不能为空"));
        }
        coinSellerManageService.recharge(uid, dto);
        return HttpResult.getOk();
    }

    /**
     * 币商充值记录
     */
    @RequireRole(5)
    @PostMapping("/rechargeRecord")
    public HttpResult<PageResultVO<CoinSellerRechargeRecordVO>> rechargeRecord(@RequestParam String uid, @RequestBody CoinSellerCondition condition) {
        logger.info("select coin seller recharge record. uid={} condition={}", uid, JSONObject.toJSONString(condition));
        // checkPermissions(uid, true);
        return HttpResult.getOk(coinSellerManageService.rechargeRecord(condition));
    }

    /**
     * 币商充值记录导出
     */
    @RequireRole(5)
    @PostMapping("/rechargeRecord/download")
    public void downloadRechargeRecord(HttpServletResponse response, @RequestParam String uid, @RequestBody CoinSellerCondition condition) {
        logger.info("rechargeRecord download. uid={} condition={}", uid, JSONObject.toJSONString(condition));
        // checkPermissions(uid, true);
        condition.setPage(1);
        condition.setPageSize(10000);
        try {
            PageResultVO<CoinSellerRechargeRecordVO> pageResultVO = coinSellerManageService.rechargeRecord(condition);
            List<CoinSellerRechargeRecordExportVO> list = new ArrayList<>();
            if (!CollectionUtils.isEmpty(pageResultVO.getList())) {
                for (CoinSellerRechargeRecordVO vo : pageResultVO.getList()) {
                    CoinSellerRechargeRecordExportVO exportVO = new CoinSellerRechargeRecordExportVO();
                    BeanUtils.copyProperties(vo, exportVO, "merchantType", "source", "ctime");
                    exportVO.setMerchantType(vo.getMerchantType() == 1 ? "一级币商" : "二级币商");
                    exportVO.setSource(vo.getSource() == 0 ? "运营平台充值" : "第三方充值");
                    exportVO.setCtime(DateHelper.ARABIAN.timestampToDatetimeStr(vo.getCtime() * 1000L));
                    list.add(exportVO);
                }
            }
            ExcelUtils.exportExcel(response, list, CoinSellerRechargeRecordExportVO.class, "recharge_record_report", "币商充值记录");
        } catch (Exception e) {
            logger.error("rechargeRecord download error. uid={} condition={} {}", uid, JSONObject.toJSONString(condition), e.getMessage(), e);
        }
    }

    /**
     * 用户充值记录
     */
    @RequireRole(5)
    @PostMapping("/userRechargeRecord")
    public HttpResult<PageResultVO<UserRechargeRecordVO>> userRechargeRecord(@RequestParam String uid, @RequestBody CoinSellerCondition condition) {
        logger.info("select user recharge record. uid={} condition={}", uid, JSONObject.toJSONString(condition));
        // checkPermissions(uid, true);
        return HttpResult.getOk(coinSellerManageService.userRechargeRecord(condition));
    }

    /**
     * 下属币商列表
     */
    @RequireRole(5)
    @PostMapping("/subList")
    public HttpResult<PageVO<SubCoinSellerVO>> subList(@RequestParam String uid, @RequestBody CoinSellerCondition condition) {
        logger.info("get coin seller sub list. uid={} condition={}", uid, JSONObject.toJSONString(condition));
        // checkPermissions(uid, true);
        return HttpResult.getOk(coinSellerManageService.subList(condition));
    }

    /**
     * 币商充值记录导出
     */
    @RequireRole(5)
    @PostMapping("/userRechargeRecord/download")
    public void downloadUserRechargeRecord(HttpServletResponse response, @RequestParam String uid, @RequestBody CoinSellerCondition condition) {
        logger.info("userRechargeRecord download. uid={} condition={}", uid, JSONObject.toJSONString(condition));
        // checkPermissions(uid, true);
        condition.setPage(1);
        condition.setPageSize(10000);
        try {
            PageResultVO<UserRechargeRecordVO> pageResultVO = coinSellerManageService.userRechargeRecord(condition);
            List<UserRechargeRecordExportVO> list = new ArrayList<>();
            if (!CollectionUtils.isEmpty(pageResultVO.getList())) {
                for (UserRechargeRecordVO vo : pageResultVO.getList()) {
                    UserRechargeRecordExportVO exportVO = new UserRechargeRecordExportVO();
                    BeanUtils.copyProperties(vo, exportVO, "merchantType", "rechargeType", "ctime");
                    exportVO.setMerchantType(vo.getMerchantType() == 1 ? "一级币商" : "二级币商");
                    exportVO.setRechargeType(vo.getRechargeType() == 1 ? "普通用户" : "一级充值二级");
                    exportVO.setCtime(DateHelper.ARABIAN.timestampToDatetimeStr(vo.getCtime() * 1000L));
                    list.add(exportVO);
                }
            }
            ExcelUtils.exportExcel(response, list, UserRechargeRecordExportVO.class, "user_recharge_record_report", "用户充值记录");
        } catch (Exception e) {
            logger.error("rechargeRecord download error. uid={} condition={} {}", uid, JSONObject.toJSONString(condition), e.getMessage(), e);
        }
    }

    /**
     * 选项列表
     */
    @RequireRole()
    @PostMapping("/optionList")
    public HttpResult<CoinSellerRecordOptionVO> optionList(@RequestParam String uid) {
        logger.info("optionList. uid={}", uid);
        return HttpResult.getOk(CoinSellerManageService.OPTION_VO);
    }

    /**
     * 校验权限
     */
    private void checkPermissions(String uid, boolean isRead) {
        if (ServerConfig.isNotProduct()) {
            return;
        }
        if (isRead) {
            if (!AccountConstant.HIGHEST_AUTHORITY_ADMIN.equals(uid) && !AccountConstant.TEST_ACCOUNT_LIST.contains(uid) && !ONLY_READ_UID_LIST.contains(uid)) {
                throw new CommonH5Exception(new HttpCode(1, "你没有币商管理页面的查看权限"));
            }
        } else {
            if (!AccountConstant.HIGHEST_AUTHORITY_ADMIN.equals(uid) && !AccountConstant.TEST_ACCOUNT_LIST.contains(uid)) {
                throw new CommonH5Exception(new HttpCode(1, "你没有币商管理页面的操作权限"));
            }
        }
    }
}
