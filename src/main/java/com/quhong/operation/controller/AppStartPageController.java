package com.quhong.operation.controller;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.serializer.SerializerFeature;
import com.quhong.constant.ActionTypeConstant;
import com.quhong.core.utils.DateHelper;
import com.quhong.data.ActorData;
import com.quhong.enums.ApiCode;
import com.quhong.enums.HttpCode;
import com.quhong.handler.BaseController;
import com.quhong.mongo.dao.ActorDao;
import com.quhong.mongo.dao.AppPageDao;
import com.quhong.mongo.data.AppPageData;
import com.quhong.operation.annotation.RequireRole;
import com.quhong.operation.common.HttpResult;
import com.quhong.operation.share.condition.AppPageCondition;
import com.quhong.operation.share.dto.AppPageDTO;
import com.quhong.operation.share.vo.PageResultVO;
import com.quhong.operation.utils.OSSUploadUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.BeanUtils;
import org.springframework.data.mongodb.core.query.Update;
import org.springframework.util.StringUtils;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.multipart.MultipartFile;

import jakarta.annotation.Resource;
import jakarta.servlet.http.HttpServletRequest;
import java.util.*;

@RestController
@RequestMapping("/appStartPage")
public class AppStartPageController extends BaseController {

    private static final Logger logger = LoggerFactory.getLogger(AppStartPageController.class);
    private final static String filePath = "flash/";

    @Resource
    private AppPageDao appPageDao;
    @Resource
    private ActorDao actorDao;

    @RequireRole(2)
    @RequestMapping("/page")
    public String selectPage(@RequestBody AppPageCondition condition) {


        logger.info("AppPage selectPage condition={}", JSON.toJSONString(condition));
        HttpResult<PageResultVO<AppPageDTO>> result = new HttpResult<>();
        PageResultVO<AppPageDTO> pageVO = new PageResultVO<>();

        int page = condition.getPage() == null ? 1 : condition.getPage();
        int pageSize = condition.getPageSize() == null ? 30 : condition.getPageSize();
        int start = (page - 1) * pageSize;

        try {
            List<AppPageData> appPageDataList = appPageDao.selectPage(condition.getStatus(), condition.getSearch(), start, pageSize);
            List<AppPageDTO> dtoList = new ArrayList<>();
            int nowTime = (int) (System.currentTimeMillis() / 1000);
            for (AppPageData pageData: appPageDataList) {
                AppPageDTO dto = new AppPageDTO();
                dto.setDocId(pageData.get_id().toString());
                if(pageData.getActionType() == ActionTypeConstant.JOIN_USER_PAGE){
                    String targetId = pageData.getTargetId();
                    ActorData actorData = actorDao.getActorDataFromCache(targetId);
                    pageData.setTargetId(String.valueOf(actorData.getRid()));
                }
                if(pageData.getActionType() == ActionTypeConstant.JOIN_ROOM){
                    String targetId = pageData.getTargetId().substring(2);
                    ActorData actorData = actorDao.getActorDataFromCache(targetId);
                    pageData.setTargetId(String.valueOf(actorData.getRid()));
                }
                BeanUtils.copyProperties(pageData, dto);
                if (pageData.getEndTime() > 0 && nowTime >= pageData.getEndTime() && pageData.getStatus() == 1) {
                    // 已过期的banner自动改成无效
                    dto.setStartTime(0);
                    dto.setEndTime(0);
                    dto.setStatus(0);
                    Update update = new Update();
                    update.set("status", dto.getStatus());
                    appPageDao.updateData(pageData, update);
                }
                dtoList.add(dto);
            }

            pageVO.setList(dtoList);
            pageVO.setTotal(appPageDao.selectCount(condition.getStatus(), condition.getSearch()));
        } catch (Exception e) {
            logger.info("AppPage selectPage error", e);
            return JSON.toJSONString(result.error(HttpCode.SERVER_ERROR.getMsg()));
        }
        return JSON.toJSONString(result.ok(pageVO), SerializerFeature.DisableCircularReferenceDetect, SerializerFeature.WriteMapNullValue);
    }

    /**
     * 保存闪屏
     */
    @RequireRole(2)
    @RequestMapping("/save")
    public HttpResult saveTemplate(HttpServletRequest request, @RequestBody AppPageData template) {
        HttpResult result = new HttpResult();
        try {
            logger.info("saveTemplate data={}", JSON.toJSONString(template));
            if(StringUtils.isEmpty(template.getName())){
                return result.error("名称不能为空");
            }

            if(StringUtils.isEmpty(template.getUrl()) || StringUtils.isEmpty(template.getUrlAr())){
                return result.error("图片为空");
            }

            if(template.getActionType() == ActionTypeConstant.JOIN_USER_PAGE){
                String targetId = template.getTargetId();

                if(StringUtils.isEmpty(targetId)){
                    return result.error("目标id不能为空");
                }

                int targetRid = Integer.parseInt(targetId);
                ActorData actorData = actorDao.getActorByRid(targetRid);
                if (actorData == null){
                    return result.error("不存在该用户");
                }
                template.setTargetId(actorData.getUid());
            }


            if(template.getActionType() == ActionTypeConstant.JOIN_ROOM){
                String targetId = template.getTargetId();

                if(StringUtils.isEmpty(targetId)){
                    return result.error("目标id不能为空");
                }

                int targetRid = Integer.parseInt(targetId);
                ActorData actorData = actorDao.getActorByRid(targetRid);
                if (actorData == null){
                    return result.error("不存在该房间");
                }
                template.setTargetId("r:" + actorData.getUid());
            }

            if(template.getStartTime() <= 0 || template.getEndTime() <= 0 || template.getStartTime() > template.getEndTime()){
                return result.error("有效期配置有错");
            }

            appPageDao.save(template);
        } catch (Exception e) {
            logger.error("save template error. {}", e.getMessage(), e);
            return result.error();
        }
        return result.ok();
    }

   /**
    * 更新闪屏
    */
   @RequireRole(2)
   @RequestMapping("/update")
   public HttpResult updateTemplate(HttpServletRequest request, @RequestBody AppPageDTO dto) {
       HttpResult result = new HttpResult();
       try {

           if (StringUtils.isEmpty(dto.getDocId())) {
               logger.error("The TemplateId cannot be empty.");
               return result.error(ApiCode.PARAM_ERROR.getMsg());
           }

           logger.info("update template activityId={} data={}", dto.getDocId(), JSON.toJSONString(dto));
           AppPageData template = appPageDao.findDataById(dto.getDocId());
           if (template == null) {
               logger.error("AppPageData template is empty. id={}", dto.getDocId());
               return result.error(ApiCode.PARAM_ERROR.getMsg());
           }

           if(StringUtils.isEmpty(dto.getName())){
               return result.error("名称不能为空");
           }

           if(StringUtils.isEmpty(dto.getUrl()) || StringUtils.isEmpty(dto.getUrlAr())){
               return result.error("图片为空");
           }

           if(dto.getActionType() == ActionTypeConstant.JOIN_USER_PAGE){
               String targetId = dto.getTargetId();

               if(StringUtils.isEmpty(targetId)){
                   return result.error("目标id不能为空");
               }

               int targetRid = Integer.parseInt(targetId);
               ActorData actorData = actorDao.getActorByRid(targetRid);
               if (actorData == null){
                   return result.error("不存在该用户");
               }
               dto.setTargetId(actorData.getUid());
           }


           if(dto.getActionType() == ActionTypeConstant.JOIN_ROOM){
               String targetId = dto.getTargetId();

               if(StringUtils.isEmpty(targetId)){
                   return result.error("目标id不能为空");
               }

               int targetRid = Integer.parseInt(targetId);
               ActorData actorData = actorDao.getActorByRid(targetRid);
               if (actorData == null){
                   return result.error("不存在该房间");
               }
               dto.setTargetId("r:" + actorData.getUid());
           }

           if(dto.getStartTime() <= 0 || dto.getEndTime() <= 0 || dto.getStartTime() > dto.getEndTime()){
               return result.error("有效期配置有错");
           }

           Update update = new Update();
           update.set("name", dto.getName());
           update.set("status", dto.getStatus());
           update.set("startTime", dto.getStartTime());
           update.set("endTime", dto.getEndTime());
           update.set("url", dto.getUrl());
           update.set("urlAr", dto.getUrlAr());
           update.set("iphoneXUrlEn", dto.getIphoneXUrlEn());
           update.set("iphoneXUrlAr", dto.getIphoneXUrlAr());
           update.set("link", dto.getLink());
           update.set("actionType", dto.getActionType());
           update.set("targetId", dto.getTargetId());
           update.set("mtime", DateHelper.getNowSeconds());
           update.set("sortNum", dto.getSortNum());
           appPageDao.updateData(template, update);


       } catch (Exception e) {
           logger.error("update template error. {}", e.getMessage(), e);
           return result.error();
       }
       return result.ok();
   }


    /**
     * 图片上传
     */
    @RequireRole
    @RequestMapping("/upload")
    public String uploadPicture(MultipartFile file) {

        return OSSUploadUtils.upload(file, filePath);
    }


}
