package com.quhong.operation.controller;

import com.quhong.datas.HttpResult;
import com.quhong.enums.HttpCode;
import com.quhong.exception.CommonH5Exception;
import com.quhong.handler.BaseController;
import com.quhong.operation.annotation.RequireRole;
import com.quhong.operation.server.RoomBannerService;
import com.quhong.operation.share.condition.RoomBannerCondition;
import com.quhong.operation.share.dto.RoomBannerDTO;
import com.quhong.operation.share.vo.PageResultVO;
import com.quhong.operation.share.vo.RoomBannerVO;
import com.quhong.operation.utils.OSSUploadUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.http.MediaType;
import org.springframework.util.StringUtils;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.multipart.MultipartFile;

import jakarta.annotation.Resource;

/**
 * 房间banner配置
 *
 * <AUTHOR>
 * @date 2022/06/24
 */

@RestController
@RequestMapping(value ="/roomBanner", produces = MediaType.APPLICATION_JSON_VALUE)
public class RoomBannerController extends BaseController {
    private final static Logger logger = LoggerFactory.getLogger(RoomBannerController.class);
    private final static String filePath = "common/";

    @Resource
    private RoomBannerService roomBannerService;


    /**
     * 房间banner列表
     */
    @RequireRole
    @RequestMapping("/list")
    public HttpResult<PageResultVO<RoomBannerVO>> roomBannerList(@RequestBody RoomBannerCondition condition) {
        logger.info("get roomBannerList {}", condition);
        return HttpResult.getOk(roomBannerService.roomBannerList(condition));

    }

    @RequireRole
    @PostMapping("/addData")
    public HttpResult<Object> addBannerData(@RequestBody RoomBannerDTO dto) {
        logger.info("addTeamData {}", dto);
        roomBannerService.addRoomBanner(dto);
        return HttpResult.getOk();
    }

    @RequireRole
    @PostMapping("/updateData")
    public HttpResult<Object> updateBannerData(@RequestBody RoomBannerDTO dto) {
        logger.info("updateTeamData {}", dto);
        if (!StringUtils.hasLength(dto.getBanner_id())) {
            throw new CommonH5Exception(HttpCode.PARAM_ERROR);
        }
        roomBannerService.updateBannerData(dto);
        return HttpResult.getOk();
    }

    /**
     * 图片上传
     */
    @RequireRole
    @RequestMapping("/upload")
    public String uploadPicture(MultipartFile file) {
        return OSSUploadUtils.upload(file, filePath);
    }
}
