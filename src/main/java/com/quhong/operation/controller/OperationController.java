package com.quhong.operation.controller;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.quhong.core.utils.DateHelper;
import com.quhong.data.ActorData;
import com.quhong.enums.HttpCode;
import com.quhong.exception.CommonH5Exception;
import com.quhong.mongo.dao.ActorDao;
import com.quhong.mongo.dao.MongoRoomDao;
import com.quhong.mongo.data.MongoRoomData;
import com.quhong.mysql.dao.BizRobotConfigDao;
import com.quhong.mysql.data.BizRobotConfigData;
import com.quhong.operation.annotation.RequireRole;
import com.quhong.operation.common.ApiResult;
import com.quhong.operation.common.HttpResult;
import com.quhong.operation.dao.BeautifulRidChangeLogOPDao;
import com.quhong.operation.dao.ManagerDao;
import com.quhong.operation.server.OperationService;
import com.quhong.operation.server.RechargeOrderService;
import com.quhong.operation.server.StartPageServer;
import com.quhong.operation.share.condition.BaseCondition;
import com.quhong.operation.share.condition.RechargeOrderCondition;
import com.quhong.operation.share.condition.RidRecordCondition;
import com.quhong.operation.share.vo.*;
import com.quhong.operation.utils.ExcelUtils;
import com.quhong.redis.LuckyGiftRedis;
import com.quhong.redis.OperationConfigRedis;
import com.quhong.utils.RoomUtils;
import com.quhong.vo.H5PageVO;
import jakarta.annotation.Resource;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.util.StringUtils;
import org.springframework.web.bind.annotation.*;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Set;

/**
 * 运营平台/通用配置
 */
@RestController
@RequestMapping("/operation")
public class OperationController {

    private final static Logger logger = LoggerFactory.getLogger(OperationController.class);

    @Resource
    private ActorDao actorDao;
    @Resource
    private MongoRoomDao mongoRoomDao;
    @Resource
    private OperationConfigRedis operationConfigRedis;
    @Resource
    private StartPageServer startPageServer;
    @Resource
    private BizRobotConfigDao bizRobotConfigDao;
    @Resource
    private ManagerDao managerDao;
    @Resource
    private RechargeOrderService rechargeOrderService;
    @Resource
    private BeautifulRidChangeLogOPDao beautifulRidChangeLogOPDao;
    @Resource
    private LuckyGiftRedis luckyGiftRedis;
    @Resource
    private OperationService operationService;

    @RequireRole(3)
    @RequestMapping("/addTopMomentUser")
    public HttpResult<?> addTopMomentUser(@RequestParam int rid) {
        ActorData actorData = actorDao.getActorByRid(rid);
        if (null == actorData) {
            return new HttpResult<>().error("未找到用户: " + rid);
        }
        operationConfigRedis.addTopMomentUser(actorData.getUid());
        return new HttpResult<>().ok();
    }

    @RequireRole(3)
    @RequestMapping("/delTopMomentUser")
    public HttpResult<?> delTopMomentUser(@RequestParam int rid) {
        ActorData actorData = actorDao.getActorByRid(rid);
        if (null == actorData) {
            return new HttpResult<>().error("未找到用户: " + rid);
        }
        operationConfigRedis.delTopMomentUser(actorData.getUid());
        return new HttpResult<>().ok();
    }

    @RequireRole(3)
    @RequestMapping("/listTopMomentUser")
    public HttpResult<?> listTopMomentUser() {
        Set<String> topMomentUser = operationConfigRedis.listTopMomentUser();
        List<OperationRoomVO> operationRoomVOList = new ArrayList<>();
        for (String uid : topMomentUser) {
            ActorData actorData = actorDao.getActorDataFromCache(uid);
            operationRoomVOList.add(new OperationRoomVO(actorData.getRid(), actorData.getName()));
        }
        return new HttpResult<>().ok(operationRoomVOList);
    }

    @RequireRole(3)
    @RequestMapping("/setMomentScoreWeight")
    public HttpResult<?> setMomentScoreWeight(@RequestBody JSONObject jsonObject) {
        if (null == jsonObject) {
            return new HttpResult<>().error("参数为空");
        }
        operationConfigRedis.setMomentScoreWeight(jsonObject);
        return new HttpResult<>().ok();
    }

    @RequireRole(3)
    @RequestMapping("/getMomentScoreWeight")
    public HttpResult<?> getMomentScoreWeight() {
        return new HttpResult<>().ok(operationConfigRedis.getMomentScoreWeight());
    }

    @RequireRole(3)
    @RequestMapping("/setForYouScoreWeight")
    public HttpResult<?> setForYouScoreWeight(@RequestBody JSONObject jsonObject) {
        if (null == jsonObject) {
            return new HttpResult<>().error("参数为空");
        }
        operationConfigRedis.setForYouScoreWeight(jsonObject);
        return new HttpResult<>().ok();
    }

    @RequireRole(3)
    @RequestMapping("/getForYouScoreWeight")
    public HttpResult<?> getForYouScoreWeight() {
        return new HttpResult<>().ok(operationConfigRedis.getForYouScoreWeight());
    }

    @RequireRole(3)
    @RequestMapping("/setRobotConfig")
    public HttpResult<?> setRobotConfig(@RequestBody JSONObject jsonObject) {
        if (null == jsonObject) {
            return new HttpResult<>().error("参数为空");
        }
        operationConfigRedis.setRobotConfig(jsonObject);
        return new HttpResult<>().ok();
    }

    @RequireRole(3)
    @RequestMapping("/getRobotConfig")
    public HttpResult<?> getRobotConfig() {
        return new HttpResult<>().ok(operationConfigRedis.getRobotConfig());
    }

    @RequireRole(3)
    @RequestMapping("/addVideoOperationRoom")
    public HttpResult<?> addVideoOperationRoom(@RequestParam int rid) {
        ActorData actorData = actorDao.getActorByRid(rid);
        if (null == actorData) {
            return new HttpResult<>().error("未找到用户: " + rid);
        }
        MongoRoomData roomData = mongoRoomDao.findData(RoomUtils.formatRoomId(actorData.getUid()));
        if (null == roomData) {
            return new HttpResult<>().error("未找到房间: " + rid);
        }
        operationConfigRedis.addVideoOperationRoom(roomData.getRid());
        return new HttpResult<>().ok();
    }

    @RequireRole(3)
    @RequestMapping("/delVideoOperationRoom")
    public HttpResult<?> delVideoOperationRoom(@RequestParam int rid) {
        ActorData actorData = actorDao.getActorByRid(rid);
        operationConfigRedis.delVideoOperationRoom(RoomUtils.formatRoomId(actorData.getUid()));
        return new HttpResult<>().ok();
    }

    @RequireRole(3)
    @RequestMapping("/listVideoOperationRoom")
    public HttpResult<?> listVideoOperationRoom() {
        Set<String> allVideoOperationRoom = operationConfigRedis.getAllVideoOperationRoom();
        List<OperationRoomVO> operationRoomVOList = new ArrayList<>();
        for (String roomId : allVideoOperationRoom) {
            MongoRoomData roomData = mongoRoomDao.findData(roomId);
            ActorData actorData = actorDao.getActorDataFromCache(RoomUtils.getRoomHostId(roomData.getRid()));
            operationRoomVOList.add(new OperationRoomVO(actorData.getRid(), roomData.getName()));
        }
        return new HttpResult<>().ok(operationRoomVOList);
    }


    @RequireRole
    @RequestMapping("/flashList")
    public HttpResult<PageResultVO> flashList(
            @RequestParam(value = "valid") Integer valid,
            @RequestParam(value = "page") Integer page,
            @RequestParam(value = "page_size") Integer pageSize,
            @RequestParam(value = "search", defaultValue = "") String search) {
        logger.info("get flashList valid={}, page={}, pageSize={}", valid, page, pageSize);

        HttpResult<PageResultVO> result = new HttpResult<>();

        try {
            //参数校验
            logger.info("method flashList valid = {} page = {} page_size = {}, search= {}", valid, page, pageSize, search);
            if (page == null) {
                page = 1;
            }

            if (pageSize == null) {
                pageSize = 12;
            }

            //业务调用
            ApiResult<PageResultVO<StartPageVO>> apiResult = startPageServer.listStartPage(valid, page, pageSize, search);
            if (apiResult.isOK()) {
                return result.ok(apiResult.getData());
            }
            return result.error(apiResult.getMsg());

        } catch (Exception e) {
            logger.error("list topic friend error. msg = {}", e.getMessage(), e);
        }

        return result.error(HttpCode.SERVER_ERROR.getCode(), HttpCode.SERVER_ERROR.getMsg());

    }

    @RequireRole(3)
    @RequestMapping("/createBizRobotConfig")
    public HttpResult<?> createBizRobotConfig(HttpServletRequest request, @RequestBody BizRobotConfigData configData) {
        String uid = request.getParameter("uid");
        logger.info("createBizRobotConfig data={} uid={}", JSON.toJSONString(configData), uid);
        paramCheck(configData);
        configData.setCreator(managerDao.getDataByUid(uid).getAccount());
        int nowSeconds = DateHelper.getNowSeconds();
        configData.setCtime(nowSeconds);
        configData.setMtime(nowSeconds);
        configData.setStatus(0);
        bizRobotConfigDao.saveConfig(configData);
        return new HttpResult<>().ok();
    }

    private void paramCheck(BizRobotConfigData configData) {
        if (null == configData) {
            throw new CommonH5Exception(new HttpCode(1, "参数为空"));
        }
        // 进入房间范围设定，1 所有房间 2指定语聊房间 3进入新用户房间 4所有语聊房间 5所有直播房间 6指定直播房间
        if (configData.getEnterRoomMode() == 2) {
            String[] roomArr = configData.getEnterRoomRids().split(";");
            String[] roomIdsArr = new String[roomArr.length];
            for (int i = 0; i < roomArr.length; i++) {
                ActorData actor = actorDao.getActorByRid(Integer.parseInt(roomArr[i]));
                if (null == actor) {
                    throw new CommonH5Exception(new HttpCode(1, roomArr[i] + "不存在"));
                }
                roomIdsArr[i] = RoomUtils.formatRoomId(actor.getUid());
            }
            configData.setEnterRoomIds(StringUtils.arrayToDelimitedString(roomIdsArr, ";"));
        } else if (configData.getEnterRoomMode() == 3) {
            // 参数校验
            try {
                configData.setEnterRoomIds(String.valueOf(Integer.parseInt(configData.getEnterRoomRids())));
            } catch (NumberFormatException e) {
                throw new CommonH5Exception(new HttpCode(1, "新用户注册天数参数错误"));
            }
        } else if (configData.getEnterRoomMode() == 6) {
            String[] roomArr = configData.getEnterRoomRids().split(";");
            String[] roomIdsArr = new String[roomArr.length];
            for (int i = 0; i < roomArr.length; i++) {
                ActorData actor = actorDao.getActorByRid(Integer.parseInt(roomArr[i]));
                if (null == actor) {
                    throw new CommonH5Exception(new HttpCode(1, roomArr[i] + "不存在"));
                }
                roomIdsArr[i] = RoomUtils.formatLiveRoomId(actor.getUid());
            }
            configData.setEnterRoomIds(StringUtils.arrayToDelimitedString(roomIdsArr, ";"));
        }
    }

    @RequireRole(3)
    @RequestMapping("/updateBizRobotConfig")
    public HttpResult<?> updateBizRobotConfig(HttpServletRequest request, @RequestBody BizRobotConfigData configData) {
        String uid = request.getParameter("uid");
        logger.info("updateBizRobotConfig data={} uid={}", JSON.toJSONString(configData), uid);
        paramCheck(configData);
        bizRobotConfigDao.updateConfig(configData);
        return new HttpResult<>().ok();
    }

    @RequireRole(3)
    @RequestMapping("/updateBizRobotConfigStatus")
    public HttpResult<?> updateBizRobotConfigStatus(HttpServletRequest request, @RequestBody BizRobotConfigData configData) {
        String uid = request.getParameter("uid");
        logger.info("updateBizRobotConfigStatus id={} status={} uid={}", configData.getId(), configData.getStatus(), uid);
        if (configData.getStatus() != 1 && configData.getStatus() != 2) {
            return new HttpResult<>().error("参数错误");
        }
        bizRobotConfigDao.updateStatus(configData.getId(), configData.getStatus());
        return new HttpResult<>().ok();
    }

    @RequireRole(3)
    @RequestMapping("/getBizRobotConfig")
    public HttpResult<?> getBizRobotConfig() {
        return new HttpResult<>().ok(bizRobotConfigDao.getConfigList());
    }

    @RequireRole(3)
    @RequestMapping("/delBizRobotConfig")
    public HttpResult<?> delBizRobotConfig(HttpServletRequest request, @RequestBody BizRobotConfigData configData) {
        String uid = request.getParameter("uid");
        logger.info("delBizRobotConfig id={} uid={}", configData.getId(), uid);
        bizRobotConfigDao.delConfig(configData.getId());
        return new HttpResult<>().ok();
    }

    /**
     * 充值订单数据列表
     */
    @RequireRole(3)
    @PostMapping("/rechargeOrderList")
    public com.quhong.datas.HttpResult<PageResultVO<RechargeOrderVO>> rechargeOrderList(@RequestBody RechargeOrderCondition condition) {
        logger.info("rechargeOrderList condition={}", JSON.toJSONString(condition));
        return com.quhong.datas.HttpResult.getOk(rechargeOrderService.selectPage(condition));
    }

    /**
     * 充值订单数据列表下拉选项
     */
    @RequireRole(3)
    @PostMapping("/rechargeOrderOptionList")
    public com.quhong.datas.HttpResult<RechargeOrderOptionVO> rechargeOrderList() {
        return com.quhong.datas.HttpResult.getOk(rechargeOrderService.optionList());
    }

    /**
     * 靓号操作记录列表
     */
    @RequireRole(3)
    @PostMapping("/beautifulRidOpList")
    public HttpResult<PageResultVO<RidRecordVO>> beautifulRidOpList(@RequestBody RidRecordCondition condition) {
        logger.info("beautifulRidOpList condition={}", JSON.toJSONString(condition));
        return new HttpResult<PageResultVO<RidRecordVO>>().ok(beautifulRidChangeLogOPDao.selectPage(condition));
    }

    /**
     * 下载靓号操作记录列表
     */
    @RequireRole(3)
    @RequestMapping("downloadBeautifulRidOpList")
    public void downloadBeautifulRidOpList(HttpServletResponse response, @RequestBody RidRecordCondition condition) {
        condition.setPage(1);
        condition.setPageSize(100000);
        logger.info("downloadBeautifulRidOpList. condition={}", JSONObject.toJSONString(condition));
        PageResultVO<RidRecordVO> ridRecordVOPageResultVO = beautifulRidChangeLogOPDao.selectPage(condition);
        ExcelUtils.exportExcel(response, ridRecordVOPageResultVO.getList(), RidRecordVO.class, "rid_record", "靓号操作记录");
    }

    /**
     * 游戏相关数据后台
     */
    @RequireRole(3)
    @GetMapping("/listGameConfig")
    public HttpResult<List<GameConfigVO>> listGameConfig() {
        logger.info("listGameConfig");
        Map<Object, Object> gameConfig = operationConfigRedis.getGameConfig();
        List<GameConfigVO> voList = new ArrayList<>();
        for (Map.Entry<Object, Object> entry : gameConfig.entrySet()) {
            GameConfigVO config = JSON.parseObject(String.valueOf(entry.getValue()), GameConfigVO.class);
            if (config.getType() == 1) {
                config.setName("钻石版幸运礼物");
                config.setJackpot(luckyGiftRedis.getJackpot(1, 1));
                voList.add(config);
            }
        }
        return new HttpResult<List<GameConfigVO>>().ok(voList);
    }

    /**
     * 设置游戏相关配置
     */
    @RequireRole(3)
    @GetMapping("/setGameConfig")
    public HttpResult<?> setGameConfig(@RequestBody GameConfigVO gameConfigVO) {
        logger.info("setGameConfig gameConfigVO={}", JSON.toJSONString(gameConfigVO));
        if (gameConfigVO.getRatio() < 0 || gameConfigVO.getRatio() >= 100) {
            return new HttpResult<>().error("回收比参数错误");
        }
        operationConfigRedis.setGameConfig(String.valueOf(gameConfigVO.getType()), JSON.toJSONString(gameConfigVO));
        return new HttpResult<>().ok();
    }

    /**
     * VIP背包记录查询
     */
    @RequireRole
    @PostMapping("/vipResourceLogList")
    public HttpResult<H5PageVO<VipResourceLogPageVO>> vipResourceLogList(@RequestBody BaseCondition baseCondition) {
        return new HttpResult<H5PageVO<VipResourceLogPageVO>>().ok(operationService.vipResourceLogList(baseCondition));
    }
}
