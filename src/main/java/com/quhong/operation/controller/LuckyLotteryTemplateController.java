package com.quhong.operation.controller;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.serializer.SerializerFeature;
import com.quhong.constant.ResourceConstant;
import com.quhong.core.config.ServerConfig;
import com.quhong.core.redis.BasePlayerRedis;
import com.quhong.core.utils.DateHelper;
import com.quhong.enums.ApiCode;
import com.quhong.enums.HttpCode;
import com.quhong.handler.BaseController;
import com.quhong.mongo.dao.LuckyLotteryActivityDao;
import com.quhong.mongo.data.LuckyLotteryActivity;
import com.quhong.monitor.MonitorSender;
import com.quhong.operation.annotation.RequireRole;
import com.quhong.operation.common.HttpResult;
import com.quhong.operation.share.condition.ActivityTemplateCondition;
import com.quhong.operation.share.dto.LotteryTemplateDTO;
import com.quhong.operation.share.vo.PageResultVO;
import com.quhong.redis.LuckyLotteryRedis;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.BeanUtils;
import org.springframework.data.mongodb.core.query.Update;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import jakarta.annotation.Resource;
import jakarta.servlet.http.HttpServletRequest;
import java.util.*;

@RestController
@RequestMapping("/luckyLottery")
public class LuckyLotteryTemplateController extends BaseController {

    private static final Logger logger = LoggerFactory.getLogger(LuckyLotteryTemplateController.class);
    private static final Set<String> SUPPORT_SET = new HashSet<>(Arrays.asList("gift", "mic", "buddle", "ride",
            "ripple", "diamond", "badge", "float_screen", "heart", "once_again", "thanks"));
    // wenmiaofang、yangml、yufengxia
    private static final Set<String> ADMIN_SET = new HashSet<>(Arrays.asList("5dba92651e23cd00c80b32bc", "61b1fa404cf5f82dff19a63e", "62b9780a1fb34e1c3520cb90"));

    // @Value("${online:true}")
    // private boolean online;


    @Resource
    private LuckyLotteryActivityDao luckyLotteryActivityDao;
    @Resource
    private BasePlayerRedis basePlayerRedis;

    @Resource
    private LuckyLotteryRedis luckyLotteryRedis;

    @Resource
    private MonitorSender monitorSender;

    /**
     * 保存活动模板
     */
    @RequireRole(2)
    @RequestMapping("/save_template")
    public HttpResult saveTemplate(HttpServletRequest request, @RequestBody LuckyLotteryActivity template) {
        HttpResult result = new HttpResult();
        try {
            logger.info("saveTemplate data={}", JSON.toJSONString(template));
            // String uid = request.getParameter("uid");
            // if (online && (!ADMIN_SET.contains(uid))) {
            //     logger.error("save_template no right to operate uid={}", uid);
            //     return result.error("您无操作权限，如有需求请联系技术人员!");
            // }
            // Map<String, LuckyLotteryActivity.RewardConfigDetail> rewardConfigMap = new HashMap<>();

            if (null == template.getRewardConfigList()
                    || null == template.getActivityConfig()
                    || null == template.getPartakeConfig()
                    || null == template.getStartTime()
                    || null == template.getEndTime()
                    || null == template.getAcNameAr()
                    || null == template.getAcNameEn()){
                return result.error(ApiCode.PARAM_ERROR.getMsg());
            }

            for (LuckyLotteryActivity.RewardConfigDetail reward : template.getRewardConfigList()) {
                if (!SUPPORT_SET.contains(reward.getRewardType())) {
                    return result.error("不支持的礼物资源");
                }

                // String rewardKey = reward.getRewardType() + ":" + reward.getRewardIndex();
                // rewardConfigMap.put(rewardKey, reward);

                if (ResourceConstant.ONCE_AGAIN.equals(reward.getRewardType())
                        || ResourceConstant.THANKS.equals(reward.getRewardType())) {
                    continue;
                }

                if (ResourceConstant.DIAMOND.equals(reward.getRewardType())
                        || ResourceConstant.HEART.equals(reward.getRewardType())) {
                    if (null == reward.getRewardNum()) {
                        return result.error("钻石/心心数量不能为空");
                    }
                } else {
                    if (null == reward.getSourceId()) {
                        return result.error("资源id不能为空");
                    }
                    if (null == reward.getRewardTime()) {
                        return result.error("资源时长不能为空");
                    }
                }
            }

            if (template.getEndTime() - DateHelper.getNowSeconds() <= 10 * 60) {
                return result.error("活动结束时间距离现在太近，请检查");
            }
            template.setAcUrl(ServerConfig.isProduct() ? "https://statics.waho.live/lucky_draw/" : "https://api.opswaho.com/lucky_draw/");
            // template.setRewardConfigMap(rewardConfigMap);
            luckyLotteryActivityDao.save(template);

            if (ServerConfig.isProduct()) {
                monitorSender.info("waho_activity_warn", "正式服-用户成功创建幸运抽奖活动", "活动名：" + template.getAcNameEn());
            }

        } catch (Exception e) {
            logger.error("save template error. {}", e.getMessage(), e);
            return result.error();
        }
        return result.ok();
    }

   /**
    * 更新活动模板
    */
   @RequireRole(2)
   @RequestMapping("/update_template")
   public HttpResult updateTemplate(HttpServletRequest request, @RequestBody LotteryTemplateDTO dto) {
       HttpResult result = new HttpResult();
       try {
           // String uid = request.getParameter("uid");
           // if (online && (!ADMIN_SET.contains(uid))) {
           //     logger.error("save_template no right to operate uid={}", uid);
           //     return result.error("您无操作权限，如有需求请联系技术人员!");
           // }
           if (Objects.isNull(dto.getActivityId())) {
               logger.error("The activityId cannot be empty.");
               return result.error(ApiCode.PARAM_ERROR.getMsg());
           }
           if (null == dto.getActivityConfig() || null == dto.getStartTime()
                   || null == dto.getEndTime() || null == dto.getAcNameAr()
                   || null == dto.getAcNameEn()
                   || null == dto.getPartakeConfig() || null == dto.getRewardConfigList()) {
               return result.error(ApiCode.PARAM_ERROR.getMsg());
           }

           // Map<String, LuckyLotteryActivity.RewardConfigDetail> rewardConfigMap = new HashMap<>();
           for (LuckyLotteryActivity.RewardConfigDetail reward : dto.getRewardConfigList()) {
               if (!SUPPORT_SET.contains(reward.getRewardType())) {
                   return result.error("不支持的礼物资源");
               }

               // String rewardKey = reward.getRewardType() + ":" + reward.getRewardIndex();
               // rewardConfigMap.put(rewardKey, reward);

               if (ResourceConstant.ONCE_AGAIN.equals(reward.getRewardType())
                       || ResourceConstant.THANKS.equals(reward.getRewardType())) {
                   continue;
               }

               if (ResourceConstant.DIAMOND.equals(reward.getRewardType())
                       || ResourceConstant.HEART.equals(reward.getRewardType())) {
                   if (null == reward.getRewardNum()) {
                       return result.error("钻石/心心数量不能为空");
                   }
               } else {
                   if (null == reward.getSourceId()) {
                       return result.error("资源id不能为空");
                   }
                   if (null == reward.getRewardTime()) {
                       return result.error("资源时长不能为空");
                   }
               }
           }

           logger.info("update template activityId={} data={}", dto.getActivityId(), JSON.toJSONString(dto));

           LuckyLotteryActivity templateToUpdate = luckyLotteryActivityDao.findData(dto.getActivityId());
           if (Objects.isNull(templateToUpdate)) {
               logger.error("templateToUpdate is empty. id={}", dto.getActivityId());
               return result.error(ApiCode.PARAM_ERROR.getMsg());
           }

           // if (templateToUpdate.getStatus() == 1 && ServerConfig.isProduct()) {
           //     return result.error("活动已结束，无法更新");
           // }


           // if (dto.getEndTime() - DateHelper.getNowSeconds() <= 10 * 60) {
           //     return result.error("活动结束时间距离现在太近，请检查");
           // }

           Update update = new Update();
           update.set("acNameEn", dto.getAcNameEn());
           update.set("acNameAr", dto.getAcNameAr());
           update.set("startTime", dto.getStartTime());
           update.set("endTime", dto.getEndTime());
           update.set("activityConfig", dto.getActivityConfig());
           update.set("partakeConfig", dto.getPartakeConfig());
           update.set("rewardConfigList", dto.getRewardConfigList());
           // update.set("rewardConfigMap", rewardConfigMap);
           update.set("mtime", DateHelper.getNowSeconds());
           luckyLotteryActivityDao.updateData(templateToUpdate, update);

           // 更新需要删除奖池
           luckyLotteryRedis.deletePoolSize(dto.getActivityId());
       } catch (Exception e) {
           logger.error("update template error. {}", e.getMessage(), e);
           return result.error();
       }
       return result.ok();
   }

    /**
     * 活动模板分页查询
     */
    @RequireRole(2)
    @RequestMapping("/page")
    public String selectPage(@RequestBody ActivityTemplateCondition condition) {


        logger.info("luckyLottery selectPage condition={}", JSON.toJSONString(condition));
        HttpResult<PageResultVO<LotteryTemplateDTO>> result = new HttpResult<>();
        PageResultVO<LotteryTemplateDTO> pageVO = new PageResultVO<>();
        try {
            List<LuckyLotteryActivity> rankingActivities = luckyLotteryActivityDao
                    .selectPage(condition.getAcNameEn(), (condition.getPage() - 1) * 10, 10);
            List<LotteryTemplateDTO> dtoList = new ArrayList<>();
            String token = basePlayerRedis.getToken(ServerConfig.isProduct() ? "5cdf784961d047a4adf44064" : "6006a874644f8e00374fca1d");
            rankingActivities.forEach(a -> {
                LotteryTemplateDTO dto = new LotteryTemplateDTO();
                dto.setActivityId(a.get_id().toString());
                BeanUtils.copyProperties(a, dto);
                dto.setUid(ServerConfig.isProduct() ? "5cdf784961d047a4adf44064" : "6006a874644f8e00374fca1d");
                dto.setToken(token);
                dtoList.add(dto);
            });
            pageVO.setList(dtoList);
            pageVO.setTotal(luckyLotteryActivityDao.selectCount());
        } catch (Exception e) {
            logger.info("luckyLottery selectPage error", e);
            return JSON.toJSONString(result.error(HttpCode.SERVER_ERROR.getMsg()));
        }
        return JSON.toJSONString(result.ok(pageVO), SerializerFeature.DisableCircularReferenceDetect, SerializerFeature.WriteMapNullValue);
    }

    /**
     * 活动模板详情
     */
    @RequireRole(2)
    @RequestMapping("/select_one")
    public String selectOne(@RequestBody ActivityTemplateCondition condition) {
        HttpResult<LotteryTemplateDTO> result = new HttpResult<>();
        if (Objects.isNull(condition.getActivityId())) {
            logger.error("The activityId cannot be empty.");
            return JSON.toJSONString(result.error(HttpCode.SERVER_ERROR.getMsg()));
        }
        LuckyLotteryActivity data = luckyLotteryActivityDao.findData(condition.getActivityId());
        LotteryTemplateDTO dto = new LotteryTemplateDTO();
        dto.setActivityId(data.get_id().toString());
        BeanUtils.copyProperties(data, dto);
        return JSON.toJSONString(result.ok(dto), SerializerFeature.DisableCircularReferenceDetect, SerializerFeature.WriteMapNullValue);
    }


}
