package com.quhong.operation.controller;

import com.quhong.mysql.data.OperationLogData;
import com.quhong.operation.annotation.RequireRole;
import com.quhong.operation.common.HttpResult;
import com.quhong.operation.server.OperatingLogService;
import jakarta.annotation.Resource;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

/**
 * 运营平台/操作日志
 */
@RestController
@RequestMapping("/log")
public class OperationLogController {

    private final static Logger logger = LoggerFactory.getLogger(OperationLogController.class);

    @Resource
    private OperatingLogService operatingLogService;

    /**
     * 列表查询操作日志
     */
    @RequireRole(1)
    @RequestMapping("/list")
    public HttpResult<?> list(@RequestBody OperationLogData operationLog,
                              @RequestParam(value = "page", defaultValue = "1") Integer page,
                              @RequestParam(value = "startDate", required = false) String startDate,
                              @RequestParam(value = "endDate", required = false) String endDate,
                              @RequestParam(value = "userRid", required = false) String userRid) {
        return new HttpResult<>().ok(operatingLogService.list(operationLog, page, startDate, endDate, userRid));
    }
}
