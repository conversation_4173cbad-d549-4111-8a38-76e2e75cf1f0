package com.quhong.operation.controller;

import com.alibaba.fastjson.JSONObject;
import com.quhong.core.config.ServerConfig;
import com.quhong.datas.HttpResult;
import com.quhong.enums.HttpCode;
import com.quhong.exception.CommonH5Exception;
import com.quhong.operation.annotation.OperationLog;
import com.quhong.operation.annotation.RequireRole;
import com.quhong.operation.constant.AccountConstant;
import com.quhong.operation.enums.OperationType;
import com.quhong.operation.server.BdAdminService;
import com.quhong.operation.share.condition.BaseCondition;
import com.quhong.operation.share.dto.BdAdminDTO;
import com.quhong.operation.share.vo.BdAdminRecordVO;
import com.quhong.operation.share.vo.PageResultVO;
import jakarta.annotation.Resource;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.web.bind.annotation.*;

import java.util.Set;

/**
 * BD或Admin管理
 *
 * <AUTHOR>
 * @date 2024/9/5
 */
@RestController
@RequestMapping("bdAdmin")
public class BdAdminController {

    private static final Logger logger = LoggerFactory.getLogger(BdAdminController.class);

    private static final Set<String> ACCOUNT_SET = Set.of("6673631f619719146cb92991", "667370a6619719146cb929a4");

    @Resource
    private BdAdminService bdAdminService;

    /**
     * BD或Admin列表
     */
    @RequireRole(5)
    @PostMapping("/list")
    public HttpResult<PageResultVO<BdAdminRecordVO>> selectList(@RequestParam String uid, @RequestBody BaseCondition condition) {
        logger.info("get bd or admin record list. uid={} condition={}", uid, JSONObject.toJSONString(condition));
        return HttpResult.getOk(bdAdminService.selectList(condition));
    }

    /**
     * 新增BD或Admin身份
     */
    @OperationLog(apiName = "新增BD或Admin身份", operationType = OperationType.INSERT)
    @RequireRole(5)
    @PostMapping("/add")
    public HttpResult<Object> add(@RequestParam String uid, @RequestBody BdAdminDTO dto) {
        logger.info("add bd or admin. uid={} rid={} type={} remark={}", uid, dto.getRid(), dto.getType(), dto.getRemark());
        // checkPermission(uid);
        bdAdminService.add(dto.getType(), dto.getRid(), dto.getRemark(), uid);
        return HttpResult.getOk();
    }

    /**
     * 修改BD或Admin身份
     */
    @OperationLog(apiName = "修改BD或Admin身份", operationType = OperationType.UPDATE)
    @RequireRole(5)
    @PostMapping("/update")
    public HttpResult<Object> update(@RequestParam String uid, @RequestBody BdAdminDTO dto) {
        logger.info("update bd or admin. uid={} aid={} type={} remark={}", uid, dto.getAid(), dto.getType(), dto.getRemark());
        // checkPermission(uid);
        bdAdminService.update(dto.getType(), dto.getAid(), dto.getRemark(), uid);
        return HttpResult.getOk();
    }

    /**
     * 移除BD或Admin身份
     */
    @OperationLog(apiName = "移除BD或Admin身份", operationType = OperationType.DELETE)
    @RequireRole(5)
    @PostMapping("/remove")
    public HttpResult<Object> remove(@RequestParam String uid, @RequestBody BdAdminDTO dto) {
        logger.info("remove bd or admin. uid={} aid={} type={}", uid, dto.getAid(), dto.getType());
        // checkPermission(uid);
        bdAdminService.remove(dto.getType(), dto.getAid());
        return HttpResult.getOk();
    }

    private void checkPermission(String uid) {
        if (ServerConfig.isNotProduct()) {
            return;
        }
        if (!AccountConstant.HIGHEST_AUTHORITY_ADMIN.equals(uid) && !AccountConstant.DEVELOPER_ACCOUNT_LIST.contains(uid)
                && !AccountConstant.TEST_ACCOUNT_LIST.contains(uid) && !ACCOUNT_SET.contains(uid)) {
            throw new CommonH5Exception(new HttpCode(1, "没有权限"));
        }
    }
}
