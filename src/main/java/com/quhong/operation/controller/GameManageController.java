package com.quhong.operation.controller;

import com.alibaba.fastjson.JSONObject;
import com.quhong.data.PointControlConfigData;
import com.quhong.datas.HttpResult;
import com.quhong.enums.HttpCode;
import com.quhong.exception.CommonH5Exception;
import com.quhong.operation.annotation.RequireRole;
import com.quhong.operation.server.GameManageService;
import com.quhong.operation.share.condition.BaseCondition;
import com.quhong.operation.share.condition.GameManageCondition;
import com.quhong.operation.share.dto.GameWatchlistDTO;
import com.quhong.operation.share.vo.GameControlRecordVO;
import com.quhong.operation.share.vo.GameWatchlistVO;
import com.quhong.operation.share.vo.PageResultVO;
import com.quhong.operation.share.vo.UserGameMoneyDetailVO;
import jakarta.annotation.Resource;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.util.StringUtils;
import org.springframework.web.bind.annotation.*;

/**
 * 运营平台/游戏管理
 *
 * <AUTHOR>
 * @date 2024/4/28
 */
@RestController
@RequestMapping("/game_manage")
public class GameManageController {

    private final static Logger logger = LoggerFactory.getLogger(GameManageController.class);

    @Resource
    private GameManageService gameManageService;

    /**
     * 特别关注名单列表
     */
    @RequireRole(3)
    @PostMapping(("/watchlist/list"))
    public HttpResult<PageResultVO<GameWatchlistVO>> watchlistList(@RequestParam String uid, @RequestBody GameManageCondition condition) {
        logger.info("select game watchlist page. uid={} condition={}", uid, JSONObject.toJSONString(condition));
        return HttpResult.getOk(gameManageService.watchlistList(condition));
    }

    /**
     * 新增关注名单
     */
    @RequireRole(3)
    @PostMapping("/watchlist/add")
    public HttpResult<Object> addWatchlist(@RequestParam String uid, @RequestBody GameWatchlistDTO dto) {
        logger.info("addWatchlist. uid={} dto={}", uid, JSONObject.toJSONString(dto));
        if (dto.getType() == null || !StringUtils.hasLength(dto.getStrRid())) {
            throw new CommonH5Exception(HttpCode.PARAM_ERROR);
        }
        gameManageService.addWatchlist(dto);
        return HttpResult.getOk();
    }

    /**
     * 修改关注名单
     */
    @RequireRole(3)
    @PostMapping("/watchlist/update")
    public HttpResult<Object> updateWatchlist(@RequestParam String uid, @RequestBody GameWatchlistDTO dto) {
        logger.info("updateWatchlist. uid={} dto={}", uid, JSONObject.toJSONString(dto));
        if (dto.getId() == null || dto.getId() == 0) {
            throw new CommonH5Exception(HttpCode.PARAM_ERROR);
        }
        gameManageService.updateWatchlist(dto);
        return HttpResult.getOk();
    }

    /**
     * 删除关注名单
     */
    @RequireRole(3)
    @PostMapping("/watchlist/remove")
    public HttpResult<Object> removeWatchlist(@RequestParam String uid, @RequestBody GameWatchlistDTO dto) {
        logger.info("removeWatchlist. uid={} dto={}", uid, JSONObject.toJSONString(dto));
        if (dto.getId() == null || dto.getId() == 0) {
            throw new CommonH5Exception(HttpCode.PARAM_ERROR);
        }
        gameManageService.removeWatchlist(dto);
        return HttpResult.getOk();
    }

    /**
     * 用户游戏流水
     */
    @RequireRole(3)
    @PostMapping("/userMoneyDetail")
    public HttpResult<UserGameMoneyDetailVO> userMoneyDetail(@RequestParam String uid, @RequestBody GameWatchlistDTO dto) {
        logger.info("userMoneyDetail. uid={} dto={}", uid, JSONObject.toJSONString(dto));
        if (!StringUtils.hasLength(dto.getUid())) {
            throw new CommonH5Exception(HttpCode.PARAM_ERROR);
        }
        return HttpResult.getOk(gameManageService.userMoneyDetail(dto));
    }

    /**
     * 点控列表
     */
    @RequireRole(3)
    @PostMapping("/pointControl/list")
    public HttpResult<PageResultVO<GameControlRecordVO>> pointControlList(@RequestParam String uid, @RequestBody BaseCondition condition) {
        logger.info("pointControlList. uid={} condition={}", uid, JSONObject.toJSONString(condition));
        return HttpResult.getOk(gameManageService.pointControlList(condition));
    }

    /**
     * 获取点控配置
     */
    @RequireRole(3)
    @GetMapping("/pointControl/getConfig")
    public HttpResult<PointControlConfigData> getPointControlConfig(@RequestParam String uid) {
        logger.info("getPointControlConfig. uid={}", uid);
        return HttpResult.getOk(gameManageService.getPointControlConfig());
    }

    /**
     * 保存点控配置
     */
    @RequireRole(3)
    @PostMapping("/pointControl/saveConfig")
    public HttpResult<Object> savePointControlConfig(@RequestParam String uid, @RequestBody PointControlConfigData data) {
        logger.info("savePointControlConfig. uid={} data={}", uid, JSONObject.toJSONString(data));
        gameManageService.savePointControlConfig(data);
        return HttpResult.getOk();
    }
}
