package com.quhong.operation.share.vo.reports.money;

import com.quhong.operation.share.vo.reports.money.detail.AllMoneyDetailVO;
import com.quhong.operation.share.vo.reports.money.detail.ConsumeMoneyDetailVO;
import com.quhong.operation.share.vo.reports.money.detail.InMoneyDetailVO;

import java.util.List;

public class MoneyDetailVO {

    /**
     * 总明细
     */
    private List<AllMoneyDetailVO> allMoneyDetailVoList;

    /**
     * 收入明细
     */
    private List<InMoneyDetailVO> inMoneyDetailVoList;

    /**
     * 支出明细
     */
    private List<ConsumeMoneyDetailVO> consumeMoneyDetailVoList;

    public List<AllMoneyDetailVO> getAllMoneyDetailVoList() {
        return allMoneyDetailVoList;
    }

    public void setAllMoneyDetailVoList(List<AllMoneyDetailVO> allMoneyDetailVoList) {
        this.allMoneyDetailVoList = allMoneyDetailVoList;
    }

    public List<InMoneyDetailVO> getInMoneyDetailVoList() {
        return inMoneyDetailVoList;
    }

    public void setInMoneyDetailVoList(List<InMoneyDetailVO> inMoneyDetailVoList) {
        this.inMoneyDetailVoList = inMoneyDetailVoList;
    }

    public List<ConsumeMoneyDetailVO> getConsumeMoneyDetailVoList() {
        return consumeMoneyDetailVoList;
    }

    public void setConsumeMoneyDetailVoList(List<ConsumeMoneyDetailVO> consumeMoneyDetailVoList) {
        this.consumeMoneyDetailVoList = consumeMoneyDetailVoList;
    }
}
