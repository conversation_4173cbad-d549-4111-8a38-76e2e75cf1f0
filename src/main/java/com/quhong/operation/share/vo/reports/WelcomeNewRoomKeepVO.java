package com.quhong.operation.share.vo.reports;

import com.alibaba.excel.annotation.ExcelProperty;

public class WelcomeNewRoomKeepVO {

    @ExcelProperty("日期")
    private String date;

    @ExcelProperty("ios昨天留存")
    private double iosYesterdayKeep;

    @ExcelProperty("android昨天留存")
    private double androidYesterdayKeep;

    @ExcelProperty("昨天留存")
    private double yesterdayKeep;

    @ExcelProperty("ios7天留存")
    private double iosSevenKeep;

    @ExcelProperty("android7天留存")
    private double androidSevenKeep;

    @ExcelProperty("7天留存")
    private double sevenKeep;

    @ExcelProperty("ios30天留存")
    private double iosThirtyKeep;

    @ExcelProperty("android30天留存")
    private double androidThirdKeep;

    @ExcelProperty("30天留存")
    private double thirtyKeep;

    public String getDate() {
        return date;
    }

    public void setDate(String date) {
        this.date = date;
    }

    public double getIosYesterdayKeep() {
        return iosYesterdayKeep;
    }

    public void setIosYesterdayKeep(double iosYesterdayKeep) {
        this.iosYesterdayKeep = iosYesterdayKeep;
    }

    public double getAndroidYesterdayKeep() {
        return androidYesterdayKeep;
    }

    public void setAndroidYesterdayKeep(double androidYesterdayKeep) {
        this.androidYesterdayKeep = androidYesterdayKeep;
    }

    public double getYesterdayKeep() {
        return yesterdayKeep;
    }

    public void setYesterdayKeep(double yesterdayKeep) {
        this.yesterdayKeep = yesterdayKeep;
    }

    public double getIosSevenKeep() {
        return iosSevenKeep;
    }

    public void setIosSevenKeep(double iosSevenKeep) {
        this.iosSevenKeep = iosSevenKeep;
    }

    public double getAndroidSevenKeep() {
        return androidSevenKeep;
    }

    public void setAndroidSevenKeep(double androidSevenKeep) {
        this.androidSevenKeep = androidSevenKeep;
    }

    public double getSevenKeep() {
        return sevenKeep;
    }

    public void setSevenKeep(double sevenKeep) {
        this.sevenKeep = sevenKeep;
    }

    public double getIosThirtyKeep() {
        return iosThirtyKeep;
    }

    public void setIosThirtyKeep(double iosThirtyKeep) {
        this.iosThirtyKeep = iosThirtyKeep;
    }

    public double getAndroidThirdKeep() {
        return androidThirdKeep;
    }

    public void setAndroidThirdKeep(double androidThirdKeep) {
        this.androidThirdKeep = androidThirdKeep;
    }

    public double getThirtyKeep() {
        return thirtyKeep;
    }

    public void setThirtyKeep(double thirtyKeep) {
        this.thirtyKeep = thirtyKeep;
    }

    @Override
    public String toString() {
        return "WelcomeNewRoomKeepVO{" +
                "date='" + date + '\'' +
                ", iosYesterdayKeep=" + iosYesterdayKeep +
                ", androidYesterdayKeep=" + androidYesterdayKeep +
                ", yesterdayKeep=" + yesterdayKeep +
                ", iosSevenKeep=" + iosSevenKeep +
                ", androidSevenKeep=" + androidSevenKeep +
                ", sevenKeep=" + sevenKeep +
                ", iosThirtyKeep=" + iosThirtyKeep +
                ", androidThirdKeep=" + androidThirdKeep +
                ", thirtyKeep=" + thirtyKeep +
                '}';
    }
}
