package com.quhong.operation.share.vo;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2024/11/19
 */
public class UserMsgListVO {

    private List<MsgRecord> user_lists;
    private int user_total;

    public UserMsgListVO() {
    }

    public UserMsgListVO(List<MsgRecord> user_lists, int user_total) {
        this.user_lists = user_lists;
        this.user_total = user_total;
    }

    public static class MsgRecord {
        private int count_msg;
        private String msg_index;
        private UserInfo to_user_info;
        private UserInfo from_user_info;

        public int getCount_msg() {
            return count_msg;
        }

        public void setCount_msg(int count_msg) {
            this.count_msg = count_msg;
        }

        public String getMsg_index() {
            return msg_index;
        }

        public void setMsg_index(String msg_index) {
            this.msg_index = msg_index;
        }

        public UserInfo getTo_user_info() {
            return to_user_info;
        }

        public void setTo_user_info(UserInfo to_user_info) {
            this.to_user_info = to_user_info;
        }

        public UserInfo getFrom_user_info() {
            return from_user_info;
        }

        public void setFrom_user_info(UserInfo from_user_info) {
            this.from_user_info = from_user_info;
        }
    }

    public static class UserInfo {
        private String aid;
        private String country;
        private Integer gender;
        private String head;
        private String name;
        private Integer os;
        private Integer rid;

        public String getAid() {
            return aid;
        }

        public void setAid(String aid) {
            this.aid = aid;
        }

        public String getCountry() {
            return country;
        }

        public void setCountry(String country) {
            this.country = country;
        }

        public Integer getGender() {
            return gender;
        }

        public void setGender(Integer gender) {
            this.gender = gender;
        }

        public String getHead() {
            return head;
        }

        public void setHead(String head) {
            this.head = head;
        }

        public String getName() {
            return name;
        }

        public void setName(String name) {
            this.name = name;
        }

        public Integer getOs() {
            return os;
        }

        public void setOs(Integer os) {
            this.os = os;
        }

        public Integer getRid() {
            return rid;
        }

        public void setRid(Integer rid) {
            this.rid = rid;
        }
    }

    public List<MsgRecord> getUser_lists() {
        return user_lists;
    }

    public void setUser_lists(List<MsgRecord> user_lists) {
        this.user_lists = user_lists;
    }

    public int getUser_total() {
        return user_total;
    }

    public void setUser_total(int user_total) {
        this.user_total = user_total;
    }
}
