package com.quhong.operation.share.vo;


public class WorldCupMatchVO {
    private Integer id;
    private String matchStage;
    private String matchType;
    private Integer firstKey;
    private String firstTeam;
    private String firstTeamAr;
    private String firstTeamIcon;
    private Integer firstTeamScore;
    private Integer secondKey;
    private Integer winType;
    private String secondTeam;
    private String secondTeamAr;
    private String secondTeamIcon;
    private Integer secondTeamScore;
    private Integer showStatus;
    private Integer startTime;
    private Integer endTime;

    public Integer getId() {
        return id;
    }

    public void setId(Integer id) {
        this.id = id;
    }

    public String getMatchStage() {
        return matchStage;
    }

    public void setMatchStage(String matchStage) {
        this.matchStage = matchStage;
    }

    public String getMatchType() {
        return matchType;
    }

    public void setMatchType(String matchType) {
        this.matchType = matchType;
    }

    public Integer getFirstKey() {
        return firstKey;
    }

    public void setFirstKey(Integer firstKey) {
        this.firstKey = firstKey;
    }

    public String getFirstTeam() {
        return firstTeam;
    }

    public void setFirstTeam(String firstTeam) {
        this.firstTeam = firstTeam;
    }

    public String getFirstTeamAr() {
        return firstTeamAr;
    }

    public void setFirstTeamAr(String firstTeamAr) {
        this.firstTeamAr = firstTeamAr;
    }

    public String getFirstTeamIcon() {
        return firstTeamIcon;
    }

    public void setFirstTeamIcon(String firstTeamIcon) {
        this.firstTeamIcon = firstTeamIcon;
    }

    public Integer getFirstTeamScore() {
        return firstTeamScore;
    }

    public void setFirstTeamScore(Integer firstTeamScore) {
        this.firstTeamScore = firstTeamScore;
    }

    public Integer getSecondKey() {
        return secondKey;
    }

    public void setSecondKey(Integer secondKey) {
        this.secondKey = secondKey;
    }

    public String getSecondTeam() {
        return secondTeam;
    }

    public void setSecondTeam(String secondTeam) {
        this.secondTeam = secondTeam;
    }

    public String getSecondTeamAr() {
        return secondTeamAr;
    }

    public void setSecondTeamAr(String secondTeamAr) {
        this.secondTeamAr = secondTeamAr;
    }

    public String getSecondTeamIcon() {
        return secondTeamIcon;
    }

    public void setSecondTeamIcon(String secondTeamIcon) {
        this.secondTeamIcon = secondTeamIcon;
    }

    public Integer getSecondTeamScore() {
        return secondTeamScore;
    }

    public void setSecondTeamScore(Integer secondTeamScore) {
        this.secondTeamScore = secondTeamScore;
    }

    public Integer getStartTime() {
        return startTime;
    }

    public void setStartTime(Integer startTime) {
        this.startTime = startTime;
    }

    public Integer getEndTime() {
        return endTime;
    }

    public void setEndTime(Integer endTime) {
        this.endTime = endTime;
    }

    public Integer getWinType() {
        return winType;
    }

    public void setWinType(Integer winType) {
        this.winType = winType;
    }

    public Integer getShowStatus() {
        return showStatus;
    }

    public void setShowStatus(Integer showStatus) {
        this.showStatus = showStatus;
    }
}
