package com.quhong.operation.share.vo;

/**
 * 加入/退出公会记录查询响应类
 *
 * <AUTHOR>
 * @date 2025/6/6 11:49
 */

public class JoinOrExitFamilyRecordVO {

    /**
     * 加入退出公会记录表t_join_family_record记录id
     */
    private Integer id;
    /**
     * 主播uid
     */
    private String uid;
    /**
     * 主播id
     */
    private String strRid;
    /**
     * 主播原始id
     */
    private Integer rid;
    /**
     * 主播昵称
     */
    private String name;
    /**
     * 公会rid
     */
    private Integer familyRid;
    /**
     * 公会名称
     */
    private String familyName;
    /**
     * 公会长uid
     */
    private String ownerUid;
    /**
     * 公会长id
     */
    private String ownerStrRid;
    /**
     * 公会长原始id
     */
    private Integer ownerRid;
    /**
     * 公会长昵称
     */
    private String ownerName;
    /**
     * 运营负责人
     */
    private String manager;
    /**
     * 超管
     */
    private String superManager;
    /**
     * 加入公会时间
     */
    private String joinTime;
    /**
     * 退出公会时间
     */
    private String exitTime;
    /**
     * 类型 0加入 1退出 null 或不传代表全部
     */
    private String actionStr;

    /**
     * 退出状态字段，null 或不传代表全部 1待公会长审核 2公会长已同意 3公会长已拒绝待处理 4公会长拒绝后官方同意 5公会长拒绝后官方拒绝
     */
    private String exitStatus;

    public String getActionStr() {
        return actionStr;
    }

    public void setActionStr(String actionStr) {
        this.actionStr = actionStr;
    }

    public Integer getId() {
        return id;
    }

    public void setId(Integer id) {
        this.id = id;
    }

    public String getUid() {
        return uid;
    }

    public void setUid(String uid) {
        this.uid = uid;
    }

    public String getStrRid() {
        return strRid;
    }

    public void setStrRid(String strRid) {
        this.strRid = strRid;
    }

    public Integer getRid() {
        return rid;
    }

    public void setRid(Integer rid) {
        this.rid = rid;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public Integer getFamilyRid() {
        return familyRid;
    }

    public void setFamilyRid(Integer familyRid) {
        this.familyRid = familyRid;
    }

    public String getFamilyName() {
        return familyName;
    }

    public void setFamilyName(String familyName) {
        this.familyName = familyName;
    }

    public String getOwnerUid() {
        return ownerUid;
    }

    public void setOwnerUid(String ownerUid) {
        this.ownerUid = ownerUid;
    }

    public String getOwnerStrRid() {
        return ownerStrRid;
    }

    public void setOwnerStrRid(String ownerStrRid) {
        this.ownerStrRid = ownerStrRid;
    }

    public Integer getOwnerRid() {
        return ownerRid;
    }

    public void setOwnerRid(Integer ownerRid) {
        this.ownerRid = ownerRid;
    }

    public String getOwnerName() {
        return ownerName;
    }

    public void setOwnerName(String ownerName) {
        this.ownerName = ownerName;
    }

    public String getManager() {
        return manager;
    }

    public void setManager(String manager) {
        this.manager = manager;
    }

    public String getSuperManager() {
        return superManager;
    }

    public void setSuperManager(String superManager) {
        this.superManager = superManager;
    }

    public String getJoinTime() {
        return joinTime;
    }

    public void setJoinTime(String joinTime) {
        this.joinTime = joinTime;
    }

    public String getExitTime() {
        return exitTime;
    }

    public void setExitTime(String exitTime) {
        this.exitTime = exitTime;
    }

    public String getExitStatus() {
        return exitStatus;
    }

    public void setExitStatus(String exitStatus) {
        this.exitStatus = exitStatus;
    }

    @Override
    public String toString() {
        return "JoinOrExitFamilyRecordVO{" +
                "id=" + id +
                ", uid='" + uid + '\'' +
                ", strRid='" + strRid + '\'' +
                ", rid=" + rid +
                ", name='" + name + '\'' +
                ", familyRid=" + familyRid +
                ", familyName='" + familyName + '\'' +
                ", ownerUid='" + ownerUid + '\'' +
                ", ownerStrRid='" + ownerStrRid + '\'' +
                ", ownerRid=" + ownerRid +
                ", ownerName='" + ownerName + '\'' +
                ", manager='" + manager + '\'' +
                ", superManager='" + superManager + '\'' +
                ", joinTime='" + joinTime + '\'' +
                ", ExitTime='" + exitTime + '\'' +
                ", actionStr='" + actionStr + '\'' +
                ", exitStatus='" + exitStatus + '\'' +
                '}';
    }
}
