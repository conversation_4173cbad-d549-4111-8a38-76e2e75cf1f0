package com.quhong.operation.share.vo;

/**
 * <AUTHOR>
 * @date 2023/9/6
 */
public class OperatingAccountVO {

    /**
     * 账号id
     */
    private String id;

    /**
     * 账号名称
     */
    private String account;

    /**
     * 类型 0普通账号 1公会运营账号
     */
    private int type;

    /**
     * 权限等级
     */
    private int role;

    /**
     * 运营平台角色 1运营负责人 2超管
     */
    private int familyManageRole;

    /**
     * 上级用户
     */
    private String superior;

    /**
     * 客服id
     */
    private int serviceRid;

    /**
     * 角色名称
     */
    private String roleName;

    /**
     * 备注
     */
    private String remark;

    /**
     * 创建时间
     */
    private int ctime;

    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public String getAccount() {
        return account;
    }

    public void setAccount(String account) {
        this.account = account;
    }

    public int getFamilyManageRole() {
        return familyManageRole;
    }

    public void setFamilyManageRole(int familyManageRole) {
        this.familyManageRole = familyManageRole;
    }

    public String getSuperior() {
        return superior;
    }

    public void setSuperior(String superior) {
        this.superior = superior;
    }

    public int getServiceRid() {
        return serviceRid;
    }

    public void setServiceRid(int serviceRid) {
        this.serviceRid = serviceRid;
    }

    public String getRoleName() {
        return roleName;
    }

    public void setRoleName(String roleName) {
        this.roleName = roleName;
    }

    public int getCtime() {
        return ctime;
    }

    public void setCtime(int ctime) {
        this.ctime = ctime;
    }

    public String getRemark() {
        return remark;
    }

    public void setRemark(String remark) {
        this.remark = remark;
    }

    public int getRole() {
        return role;
    }

    public void setRole(int role) {
        this.role = role;
    }

    public int getType() {
        return type;
    }

    public void setType(int type) {
        this.type = type;
    }
}
