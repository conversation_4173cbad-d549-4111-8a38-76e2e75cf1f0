package com.quhong.operation.share.vo;

import com.alibaba.excel.annotation.ExcelProperty;

/**
 * <AUTHOR>
 * @date 2024/4/29
 */
public class DeductCharmVO {

    @ExcelProperty("用户uid")
    private String actorUid;
    @ExcelProperty("应扣魅力值")
    private String deductCharm;
    @ExcelProperty("处理结果")
    private String result;
    @ExcelProperty("备注")
    private String remark;

    public DeductCharmVO() {
    }

    public DeductCharmVO(Object actorUid, Object deductCharm, Object result, Object remark) {
        this.actorUid = String.valueOf(actorUid);
        this.deductCharm = String.valueOf(deductCharm);
        this.result = String.valueOf(result);
        this.remark = String.valueOf(remark);
    }

    public String getActorUid() {
        return actorUid;
    }

    public void setActorUid(String actorUid) {
        this.actorUid = actorUid;
    }

    public String getDeductCharm() {
        return deductCharm;
    }

    public void setDeductCharm(String deductCharm) {
        this.deductCharm = deductCharm;
    }

    public String getResult() {
        return result;
    }

    public void setResult(String result) {
        this.result = result;
    }

    public String getRemark() {
        return remark;
    }

    public void setRemark(String remark) {
        this.remark = remark;
    }
}
