package com.quhong.operation.share.vo;

import com.alibaba.excel.annotation.ExcelProperty;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @date 2023/6/8
 */
public class CoinSellerRechargeRecordExportVO implements Serializable {

    private static final long serialVersionUID = -5810528386484855410L;

    @ExcelProperty("订单id")
    private Integer id; // 订单号
    @ExcelProperty("用户原始id")
    private Integer rid; // 币商id
    @ExcelProperty("用户id")
    private String strRid; // 币商id
    @ExcelProperty("币商昵称")
    private String name; // 币商昵称
    @ExcelProperty("币商类型")
    private String merchantType; // 币商类型 1一级币商 2二级币商
    @ExcelProperty("充值来源")
    private String source; // 充值来源  0运营平台充值 1第三方充值
    @ExcelProperty("充值金额")
    private String rechargeAmount; // 充值金额
    @ExcelProperty("充值金币数")
    private Integer rechargeCoins; // 充值金币数
    @ExcelProperty("充值类型")
    private String rechargeType; // 充值类型 1:运营平台充值 2:奖励及薪资 3:测试 4:第三方充值
    @ExcelProperty("支付方式")
    private String payMethod; // 支付方式
    @ExcelProperty("备注")
    private String remark; // 备注
    @ExcelProperty("充值时间")
    private String ctime; // 充值时间

    public Integer getId() {
        return id;
    }

    public void setId(Integer id) {
        this.id = id;
    }

    public Integer getRid() {
        return rid;
    }

    public void setRid(Integer rid) {
        this.rid = rid;
    }

    public String getStrRid() {
        return strRid;
    }

    public void setStrRid(String strRid) {
        this.strRid = strRid;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getMerchantType() {
        return merchantType;
    }

    public void setMerchantType(String merchantType) {
        this.merchantType = merchantType;
    }

    public String getSource() {
        return source;
    }

    public void setSource(String source) {
        this.source = source;
    }

    public String getRechargeAmount() {
        return rechargeAmount;
    }

    public void setRechargeAmount(String rechargeAmount) {
        this.rechargeAmount = rechargeAmount;
    }

    public Integer getRechargeCoins() {
        return rechargeCoins;
    }

    public void setRechargeCoins(Integer rechargeCoins) {
        this.rechargeCoins = rechargeCoins;
    }

    public String getRemark() {
        return remark;
    }

    public void setRemark(String remark) {
        this.remark = remark;
    }

    public String getCtime() {
        return ctime;
    }

    public void setCtime(String ctime) {
        this.ctime = ctime;
    }

    public String getRechargeType() {
        return rechargeType;
    }

    public void setRechargeType(String rechargeType) {
        this.rechargeType = rechargeType;
    }

    public String getPayMethod() {
        return payMethod;
    }

    public void setPayMethod(String payMethod) {
        this.payMethod = payMethod;
    }
}


