package com.quhong.operation.share.vo;

import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2020/6/28
 */
public class CreditTotalVO implements Serializable {

    private static final long serialVersionUID = -3149021237694261933L;
    private List<OnCreditVO> onCreditList;
    private Integer time;
    private Integer paid;
    private Integer totalBeans;
    private Integer paidBeans;
    private Double credibility1;
    private Double credibility2;

    public List<OnCreditVO> getOnCreditList() {
        return onCreditList;
    }

    public void setOnCreditList(List<OnCreditVO> onCreditList) {
        this.onCreditList = onCreditList;
    }

    public Integer getTime() {
        return time;
    }

    public void setTime(Integer time) {
        this.time = time;
    }

    public Integer getPaid() {
        return paid;
    }

    public void setPaid(Integer paid) {
        this.paid = paid;
    }

    public Integer getTotalBeans() {
        return totalBeans;
    }

    public void setTotalBeans(Integer totalBeans) {
        this.totalBeans = totalBeans;
    }

    public Integer getPaidBeans() {
        return paidBeans;
    }

    public void setPaidBeans(Integer paidBeans) {
        this.paidBeans = paidBeans;
    }

    public Double getCredibility1() {
        return credibility1;
    }

    public void setCredibility1(Double credibility1) {
        this.credibility1 = credibility1;
    }

    public Double getCredibility2() {
        return credibility2;
    }

    public void setCredibility2(Double credibility2) {
        this.credibility2 = credibility2;
    }

    @Override
    public String toString() {
        return "CreditTotalVO{" +
                "onCreditList size=" + (null == onCreditList ? 0 : onCreditList.size()) +
                "time=" + time +
                ", paid=" + paid +
                ", totalBeans=" + totalBeans +
                ", paidBeans=" + paidBeans +
                ", credibility1=" + credibility1 +
                ", credibility2=" + credibility2 +
                '}';
    }

}
