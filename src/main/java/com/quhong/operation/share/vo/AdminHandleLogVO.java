package com.quhong.operation.share.vo;

import com.alibaba.excel.annotation.ExcelProperty;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @date 2024/7/29
 */
public class AdminHandleLogVO implements Serializable {

    private static final long serialVersionUID = 2247423515523375612L;

    @ExcelProperty("用户原始id")
    private String rid;
    @ExcelProperty("用户id")
    private String strRid;
    @ExcelProperty("运营负责人")
    private String operationManager;
    @ExcelProperty("uid")
    private String uid;
    @ExcelProperty("操作类型")
    private String handle_name;
    @ExcelProperty("操作描述")
    private String handle_desc;
    @ExcelProperty("操作备注")
    private String handle_remark;
    @ExcelProperty("操作时间")
    private String handle_ctime;
    @ExcelProperty("admin账号")
    private String admin_account;
    @ExcelProperty("admin备注")
    private String admin_name;

    public String getRid() {
        return rid;
    }

    public void setRid(String rid) {
        this.rid = rid;
    }

    public String getStrRid() {
        return strRid;
    }

    public void setStrRid(String strRid) {
        this.strRid = strRid;
    }

    public String getOperationManager() {
        return operationManager;
    }

    public void setOperationManager(String operationManager) {
        this.operationManager = operationManager;
    }

    public String getUid() {
        return uid;
    }

    public void setUid(String uid) {
        this.uid = uid;
    }

    public String getHandle_name() {
        return handle_name;
    }

    public void setHandle_name(String handle_name) {
        this.handle_name = handle_name;
    }

    public String getHandle_desc() {
        return handle_desc;
    }

    public void setHandle_desc(String handle_desc) {
        this.handle_desc = handle_desc;
    }

    public String getHandle_ctime() {
        return handle_ctime;
    }

    public void setHandle_ctime(String handle_ctime) {
        this.handle_ctime = handle_ctime;
    }

    public String getAdmin_account() {
        return admin_account;
    }

    public void setAdmin_account(String admin_account) {
        this.admin_account = admin_account;
    }

    public String getAdmin_name() {
        return admin_name;
    }

    public void setAdmin_name(String admin_name) {
        this.admin_name = admin_name;
    }

    public String getHandle_remark() {
        return handle_remark;
    }

    public void setHandle_remark(String handle_remark) {
        this.handle_remark = handle_remark;
    }
}
