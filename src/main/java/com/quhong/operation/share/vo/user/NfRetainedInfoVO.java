package com.quhong.operation.share.vo.user;

import com.alibaba.excel.annotation.ExcelIgnore;
import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.fastjson.annotation.JSONField;

public class NfRetainedInfoVO {

    @ExcelProperty("日期")
    @JSONField(name = "d_str")
    private String dStr;
    @ExcelProperty("第0天")
    @JSONField(name = "user_0")
    private int user0;
    @ExcelProperty("第1天")
    @JSONField(name = "user_1")
    private int user1;
    @ExcelProperty("第2天")
    @JSONField(name = "user_2")
    private int user2;
    @ExcelProperty("第3天")
    @JSONField(name = "user_3")
    private int user3;
    @ExcelProperty("第4天")
    @JSONField(name = "user_4")
    private int user4;
    @ExcelProperty("第5天")
    @JSONField(name = "user_5")
    private int user5;
    @ExcelProperty("第6天")
    @JSONField(name = "user_6")
    private int user6;
    @ExcelProperty("第7天")
    @JSONField(name = "user_7")
    private int user7;
    @ExcelIgnore
    @JSONField(name = "user1Rate")
    private double user1Rate;
    @ExcelIgnore
    @JSONField(name = "user2Rate")
    private double user2Rate;
    @ExcelIgnore
    @JSONField(name = "user3Rate")
    private double user3Rate;
    @ExcelIgnore
    @JSONField(name = "user4Rate")
    private double user4Rate;
    @ExcelIgnore
    @JSONField(name = "user5Rate")
    private double user5Rate;
    @ExcelIgnore
    @JSONField(name = "user6Rate")
    private double user6Rate;
    @ExcelIgnore
    @JSONField(name = "user7Rate")
    private double user7Rate;

    public String getdStr() {
        return dStr;
    }

    public void setdStr(String dStr) {
        this.dStr = dStr;
    }

    public int getUser0() {
        return user0;
    }

    public void setUser0(int user0) {
        this.user0 = user0;
    }

    public int getUser1() {
        return user1;
    }

    public void setUser1(int user1) {
        this.user1 = user1;
    }

    public int getUser2() {
        return user2;
    }

    public void setUser2(int user2) {
        this.user2 = user2;
    }

    public int getUser3() {
        return user3;
    }

    public void setUser3(int user3) {
        this.user3 = user3;
    }

    public int getUser4() {
        return user4;
    }

    public void setUser4(int user4) {
        this.user4 = user4;
    }

    public int getUser5() {
        return user5;
    }

    public void setUser5(int user5) {
        this.user5 = user5;
    }

    public int getUser6() {
        return user6;
    }

    public void setUser6(int user6) {
        this.user6 = user6;
    }

    public int getUser7() {
        return user7;
    }

    public void setUser7(int user7) {
        this.user7 = user7;
    }

    public double getUser1Rate() {
        return user1Rate;
    }

    public void setUser1Rate(double user1Rate) {
        this.user1Rate = user1Rate;
    }

    public double getUser7Rate() {
        return user7Rate;
    }

    public void setUser7Rate(double user7Rate) {
        this.user7Rate = user7Rate;
    }

    public double getUser2Rate() {
        return user2Rate;
    }

    public void setUser2Rate(double user2Rate) {
        this.user2Rate = user2Rate;
    }

    public double getUser3Rate() {
        return user3Rate;
    }

    public void setUser3Rate(double user3Rate) {
        this.user3Rate = user3Rate;
    }

    public double getUser4Rate() {
        return user4Rate;
    }

    public void setUser4Rate(double user4Rate) {
        this.user4Rate = user4Rate;
    }

    public double getUser5Rate() {
        return user5Rate;
    }

    public void setUser5Rate(double user5Rate) {
        this.user5Rate = user5Rate;
    }

    public double getUser6Rate() {
        return user6Rate;
    }

    public void setUser6Rate(double user6Rate) {
        this.user6Rate = user6Rate;
    }
}
