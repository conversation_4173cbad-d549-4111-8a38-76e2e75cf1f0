package com.quhong.operation.share.vo;

import com.alibaba.excel.annotation.ExcelIgnore;
import com.alibaba.excel.annotation.ExcelProperty;

import java.io.Serializable;
import java.util.List;


public class BLockRoomLogVO implements Serializable {

    private static final long serialVersionUID = 1741972725121274724L;

    @ExcelProperty("用户原始id")
    private String rid; // 用户原始id
    @ExcelProperty("用户id")
    private String strRid; // 用户id
    @ExcelProperty("用户名")
    private String name;
    @ExcelProperty("用户身份")
    private int host; // 1主播 0用户
    @ExcelProperty("告警类型")
    private int warnType; // 告警类型
    @ExcelProperty("封禁时长")
    private String blockTerm; // 封禁时长： 1：24hours  2:7 days 3:永久 4:3分钟 5:30分钟
    @ExcelProperty("原因描述")
    private String reason; // 原因描述
    @ExcelIgnore
    private List<String> screenshotList; // 封禁前的截图
    @ExcelProperty("操作人")
    private String opName; // 操作人
    @ExcelProperty("注册时间")
    private String registerTime; // 注册时间
    @ExcelProperty("封禁时间")
    private String blockTime; // 封禁时间
    @ExcelProperty("直播时长，单位：分钟")
    private String liveTime; // 直播时长，单位：分钟
    @ExcelIgnore
    private long ctime;

    public String getRid() {
        return rid;
    }

    public void setRid(String rid) {
        this.rid = rid;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public int getHost() {
        return host;
    }

    public void setHost(int host) {
        this.host = host;
    }

    public String getBlockTerm() {
        return blockTerm;
    }

    public void setBlockTerm(String blockTerm) {
        this.blockTerm = blockTerm;
    }

    public String getReason() {
        return reason;
    }

    public void setReason(String reason) {
        this.reason = reason;
    }

    public List<String> getScreenshotList() {
        return screenshotList;
    }

    public void setScreenshotList(List<String> screenshotList) {
        this.screenshotList = screenshotList;
    }

    public String getOpName() {
        return opName;
    }

    public void setOpName(String opName) {
        this.opName = opName;
    }

    public long getCtime() {
        return ctime;
    }

    public void setCtime(long ctime) {
        this.ctime = ctime;
    }

    public int getWarnType() {
        return warnType;
    }

    public void setWarnType(int warnType) {
        this.warnType = warnType;
    }

    public String getStrRid() {
        return strRid;
    }

    public void setStrRid(String strRid) {
        this.strRid = strRid;
    }

    public String getRegisterTime() {
        return registerTime;
    }

    public void setRegisterTime(String registerTime) {
        this.registerTime = registerTime;
    }

    public String getBlockTime() {
        return blockTime;
    }

    public void setBlockTime(String blockTime) {
        this.blockTime = blockTime;
    }

    public String getLiveTime() {
        return liveTime;
    }

    public void setLiveTime(String liveTime) {
        this.liveTime = liveTime;
    }
}
