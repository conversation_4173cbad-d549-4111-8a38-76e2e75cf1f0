package com.quhong.operation.share.vo;

import com.quhong.data.vo.MsgRecordVO;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2024/9/13
 */
public class MsgListRecordVO {

    private User fromUser;
    private User toUser;
    private List<MsgRecordVO> msgList;

    public static class User {
        private String aid;
        private Integer rid;
        private String strRid;
        private String name;
        private String head;

        public User() {
        }

        public User(String aid, Integer rid, String strRid, String name, String head) {
            this.aid = aid;
            this.rid = rid;
            this.strRid = strRid;
            this.name = name;
            this.head = head;
        }

        public String getAid() {
            return aid;
        }

        public void setAid(String aid) {
            this.aid = aid;
        }

        public Integer getRid() {
            return rid;
        }

        public void setRid(Integer rid) {
            this.rid = rid;
        }

        public String getStrRid() {
            return strRid;
        }

        public void setStrRid(String strRid) {
            this.strRid = strRid;
        }

        public String getName() {
            return name;
        }

        public void setName(String name) {
            this.name = name;
        }

        public String getHead() {
            return head;
        }

        public void setHead(String head) {
            this.head = head;
        }
    }

    public User getFromUser() {
        return fromUser;
    }

    public void setFromUser(User fromUser) {
        this.fromUser = fromUser;
    }

    public User getToUser() {
        return toUser;
    }

    public void setToUser(User toUser) {
        this.toUser = toUser;
    }

    public List<MsgRecordVO> getMsgList() {
        return msgList;
    }

    public void setMsgList(List<MsgRecordVO> msgList) {
        this.msgList = msgList;
    }
}
