package com.quhong.operation.share.vo;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2023/12/11
 */
public class CoinSellerRecordOptionVO {

    private List<Option> optionList;

    public CoinSellerRecordOptionVO() {
    }

    public CoinSellerRecordOptionVO(List<Option> optionList) {
        this.optionList = optionList;
    }

    public static class Option {
        private int key;
        private String value;
        private List<String> list;

        public Option() {
        }

        public Option(int key, String value, List<String> list) {
            this.key = key;
            this.value = value;
            this.list = list;
        }

        public int getKey() {
            return key;
        }

        public void setKey(int key) {
            this.key = key;
        }

        public String getValue() {
            return value;
        }

        public void setValue(String value) {
            this.value = value;
        }

        public List<String> getList() {
            return list;
        }

        public void setList(List<String> list) {
            this.list = list;
        }
    }

    public List<Option> getOptionList() {
        return optionList;
    }

    public void setOptionList(List<Option> optionList) {
        this.optionList = optionList;
    }
}
