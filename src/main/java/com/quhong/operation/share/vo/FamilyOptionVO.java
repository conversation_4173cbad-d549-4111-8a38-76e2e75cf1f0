package com.quhong.operation.share.vo;

import com.quhong.mysql.data.CountriesData;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2023/6/6
 */
public class FamilyOptionVO {

    /**
     * 国家列表
     */
    private List<CountriesData> countriesList;

    /**
     * 运营负责人列表
     */
    private List<String> managerList;

    /**
     * 超管列表
     */
    private List<String> superManagerList;

    /**
     * 大区列表
     */
    private List<String> regionList;

    public List<CountriesData> getCountriesList() {
        return countriesList;
    }

    public void setCountriesList(List<CountriesData> countriesList) {
        this.countriesList = countriesList;
    }

    public List<String> getManagerList() {
        return managerList;
    }

    public void setManagerList(List<String> managerList) {
        this.managerList = managerList;
    }

    public List<String> getSuperManagerList() {
        return superManagerList;
    }

    public void setSuperManagerList(List<String> superManagerList) {
        this.superManagerList = superManagerList;
    }

    public List<String> getRegionList() {
        return regionList;
    }

    public void setRegionList(List<String> regionList) {
        this.regionList = regionList;
    }
}
