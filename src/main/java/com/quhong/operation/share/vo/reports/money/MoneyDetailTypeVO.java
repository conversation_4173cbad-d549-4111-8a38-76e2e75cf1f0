package com.quhong.operation.share.vo.reports.money;

import com.quhong.operation.share.vo.reports.money.detail.AllMoneyDetailVO;
import com.quhong.operation.share.vo.reports.money.detail.ConsumeMoneyDetailVO;
import com.quhong.operation.share.vo.reports.money.detail.InMoneyDetailVO;

public class MoneyDetailTypeVO {

    private AllMoneyDetailVO allMoneyDetailVO;

    private InMoneyDetailVO inMoneyDetailVO;

    private ConsumeMoneyDetailVO consumeMoneyDetailVO;

    public AllMoneyDetailVO getAllMoneyDetailVO() {
        return allMoneyDetailVO;
    }

    public void setAllMoneyDetailVO(AllMoneyDetailVO allMoneyDetailVO) {
        this.allMoneyDetailVO = allMoneyDetailVO;
    }

    public InMoneyDetailVO getInMoneyDetailVO() {
        return inMoneyDetailVO;
    }

    public void setInMoneyDetailVO(InMoneyDetailVO inMoneyDetailVO) {
        this.inMoneyDetailVO = inMoneyDetailVO;
    }

    public ConsumeMoneyDetailVO getConsumeMoneyDetailVO() {
        return consumeMoneyDetailVO;
    }

    public void setConsumeMoneyDetailVO(ConsumeMoneyDetailVO consumeMoneyDetailVO) {
        this.consumeMoneyDetailVO = consumeMoneyDetailVO;
    }

    @Override
    public String toString() {
        return "MoneyDetailTypeVO{" +
                "allMoneyDetailVO=" + allMoneyDetailVO +
                ", inMoneyDetailVO=" + inMoneyDetailVO +
                ", consumeMoneyDetailVO=" + consumeMoneyDetailVO +
                '}';
    }
}
