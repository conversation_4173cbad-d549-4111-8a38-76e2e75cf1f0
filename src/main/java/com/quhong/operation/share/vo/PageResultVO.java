package com.quhong.operation.share.vo;

import java.util.Collections;
import java.util.List;

public class PageResultVO<T> {

    // 总记录数
    private long total;
    // 分页总页数
    private long pages;
    private List<T> list;

    public PageResultVO() {
        this.total = 0;
        this.pages = 0;
        this.list = Collections.emptyList();
    }

    public PageResultVO(long total, List<T> list) {
        this.total = total;
        this.list = list;
    }

    public PageResultVO(long total, long pages, List<T> list) {
        this.total = total;
        this.pages = pages;
        this.list = list;
    }

    public long getTotal() {
        return total;
    }

    public void setTotal(long total) {
        this.total = total;
    }

    public long getPages() {
        return pages;
    }

    public void setPages(long pages) {
        this.pages = pages;
    }

    public List<T> getList() {
        return list;
    }

    public void setList(List<T> list) {
        this.list = list;
    }

    @Override
    public String toString() {
        return "PageResultVO{" +
                "total=" + total +
                "pages=" + pages +
                ", list=" + list +
                '}';
    }
}
