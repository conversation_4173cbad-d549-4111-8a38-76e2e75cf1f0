package com.quhong.operation.share.vo;

public class RoomEventVO {

    private int eventId; // 活动ID
    private int ctime; // 创建时间
    private int mtime; // 修改时间
    private String roomRid; // 房间ID
    private String roomStrRid; // 房间靓号
    private String creatorRid; // 创建者ID
    private String creatorStrRid; // 创建者靓号
    private String name; // 活动名称
    private String description; // 活动描述
    private String eventCoverUrl; // 活动封面
    private int startTime; // 活动开始时间
    private int duration; // 活动时长 单位：小时
    private int endTime; // 活动结束时间
    private int status; // 状态 -1失效 0审核中 1审核通过 2审核不通过
    private String reviewReason; // 审核原因
    private String reviewer; // 审核人

    public int getEventId() {
        return eventId;
    }

    public void setEventId(int eventId) {
        this.eventId = eventId;
    }

    public int getCtime() {
        return ctime;
    }

    public void setCtime(int ctime) {
        this.ctime = ctime;
    }

    public int getMtime() {
        return mtime;
    }

    public void setMtime(int mtime) {
        this.mtime = mtime;
    }

    public String getRoomRid() {
        return roomRid;
    }

    public void setRoomRid(String roomRid) {
        this.roomRid = roomRid;
    }

    public String getCreatorRid() {
        return creatorRid;
    }

    public void setCreatorRid(String creatorRid) {
        this.creatorRid = creatorRid;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getDescription() {
        return description;
    }

    public void setDescription(String description) {
        this.description = description;
    }

    public String getEventCoverUrl() {
        return eventCoverUrl;
    }

    public void setEventCoverUrl(String eventCoverUrl) {
        this.eventCoverUrl = eventCoverUrl;
    }

    public int getStartTime() {
        return startTime;
    }

    public void setStartTime(int startTime) {
        this.startTime = startTime;
    }

    public int getStatus() {
        return status;
    }

    public void setStatus(int status) {
        this.status = status;
    }

    public String getReviewReason() {
        return reviewReason;
    }

    public void setReviewReason(String reviewReason) {
        this.reviewReason = reviewReason;
    }

    public String getReviewer() {
        return reviewer;
    }

    public void setReviewer(String reviewer) {
        this.reviewer = reviewer;
    }

    public int getDuration() {
        return duration;
    }

    public void setDuration(int duration) {
        this.duration = duration;
    }

    public int getEndTime() {
        return endTime;
    }

    public void setEndTime(int endTime) {
        this.endTime = endTime;
    }

    public String getCreatorStrRid() {
        return creatorStrRid;
    }

    public void setCreatorStrRid(String creatorStrRid) {
        this.creatorStrRid = creatorStrRid;
    }

    public String getRoomStrRid() {
        return roomStrRid;
    }

    public void setRoomStrRid(String roomStrRid) {
        this.roomStrRid = roomStrRid;
    }
}
