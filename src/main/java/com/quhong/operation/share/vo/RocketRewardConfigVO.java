package com.quhong.operation.share.vo;

import com.quhong.mongo.data.RocketRewardConfigData;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2022/12/23
 */
public class RocketRewardConfigVO {

    /**
     * 房间火箭开关0关闭 1开启
     */
    private Integer status;

    /**
     * 火箭等级
     */
    private int rocketLevel;

    /**
     * 火箭发射限制
     */
    private int rocketLaunchLimit;

    private List<RocketRewardConfigData.RewardConfigDetail> detailList;

    public Integer getStatus() {
        return status;
    }

    public void setStatus(Integer status) {
        this.status = status;
    }

    public int getRocketLevel() {
        return rocketLevel;
    }

    public void setRocketLevel(int rocketLevel) {
        this.rocketLevel = rocketLevel;
    }

    public int getRocketLaunchLimit() {
        return rocketLaunchLimit;
    }

    public void setRocketLaunchLimit(int rocketLaunchLimit) {
        this.rocketLaunchLimit = rocketLaunchLimit;
    }

    public List<RocketRewardConfigData.RewardConfigDetail> getDetailList() {
        return detailList;
    }

    public void setDetailList(List<RocketRewardConfigData.RewardConfigDetail> detailList) {
        this.detailList = detailList;
    }
}
