package com.quhong.operation.share.vo.reports.room;

import com.quhong.operation.share.vo.AbstractDateVO;

public class RoomMusicNewStatVO extends AbstractDateVO {

    /**
     * 总新增用户数
     */
    private int addUserNum;

    /**
     * 进房人数
     */
    private int enterRoomUserNum;

    /**
     * 进房次数
     */
    private int enterRoomCount;

    /**
     * 点歌人数
     */
    private int playMusicUserNum;

    /**
     * 点歌次数
     */
    private int playMusicCount;

    /**
     * 进房留存
     */
    private String enterRemain;

    /**
     * 点歌留存
     */
    private String playRemain;

    public int getAddUserNum() {
        return addUserNum;
    }

    public void setAddUserNum(int addUserNum) {
        this.addUserNum = addUserNum;
    }

    public int getEnterRoomUserNum() {
        return enterRoomUserNum;
    }

    public void setEnterRoomUserNum(int enterRoomUserNum) {
        this.enterRoomUserNum = enterRoomUserNum;
    }

    public int getEnterRoomCount() {
        return enterRoomCount;
    }

    public void setEnterRoomCount(int enterRoomCount) {
        this.enterRoomCount = enterRoomCount;
    }

    public int getPlayMusicUserNum() {
        return playMusicUserNum;
    }

    public void setPlayMusicUserNum(int playMusicUserNum) {
        this.playMusicUserNum = playMusicUserNum;
    }

    public int getPlayMusicCount() {
        return playMusicCount;
    }

    public void setPlayMusicCount(int playMusicCount) {
        this.playMusicCount = playMusicCount;
    }

    public String getEnterRemain() {
        return enterRemain;
    }

    public void setEnterRemain(String enterRemain) {
        this.enterRemain = enterRemain;
    }

    public String getPlayRemain() {
        return playRemain;
    }

    public void setPlayRemain(String playRemain) {
        this.playRemain = playRemain;
    }

    @Override
    public String toString() {
        return "RoomMusicNewStatVO{" +
                "addUserNum=" + addUserNum +
                ", enterRoomUserNum=" + enterRoomUserNum +
                ", enterRoomCount=" + enterRoomCount +
                ", playMusicUserNum=" + playMusicUserNum +
                ", playMusicCount=" + playMusicCount +
                ", enterRemain=" + enterRemain +
                ", playRemain=" + playRemain +
                ", date='" + date + '\'' +
                '}';
    }
}
