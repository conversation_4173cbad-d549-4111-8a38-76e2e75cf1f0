package com.quhong.operation.share.vo;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2023/9/11
 */
public class UserDetailInfoVO {

    private String uid; // 用户uid
    private String token; // 用户token
    private String head; // 头像
    private String name; // 昵称
    private int gender; // 性别 1男 2女
    private int rid; // 用户原始ID
    private String strRid; // 用户ID
    private int originalId; // 原始id
    private String country; // 国家
    private String phone; // 联系方式
    private String registerTime; // 注册时间
    private int registerDays; // 注册天数
    private String lastLoginTime; // 最近登录时间
    private String vipLevel; // 贵族等级
    private String svipLevel; // svip等级
    private int wealthLevel; // 财富等级
    private int charmLevel; // 魅力等级
    private String identity; // 账号身份
    private long diamondsBalance; // 钻石余额
    private long coinsBalance; // 金币余额
    private String firstRechargeTime; // 首次充值时间
    private String lastRechargeTime; // 最近充值时间
    private String rechargeChannel; // 充值渠道
    private String accountStatus; // 账号状态
    private String tnId; // 图灵盾ID
    private List<String> bindTnIdList; // 账号绑定的设备ID
    private List<String> tnIdList; // 该账号登录过的所有设备ID
    private int familyId; // 公会id
    private String familyRid; // 公会ID
    private String familyName; // 公会名字
    private String familyHead; // 公会头像
    private long weekCharmSum; // 本周收魅力值
    private long charmBalance; // 魅力值余额
    private int usdBalance; // 美金余额
    private long virtualDiamondBalance; // 虚拟钻石余额
    private String familyManager; // 运营负责人
    private String familyArea; // 所属大区
    private String ip; // ip地址
    private List<String> ipList; // 历史ip地址
    private String lastMonthRechargeDiamonds; // 上个月充值钻石数
    private String thisMonthRechargeDiamonds; // 本月充值钻石数
    private String thisWeekRechargeDiamonds; // 本周充值钻石数
    private String coinSellerDiamondsBalance; // 币商账户余额
    private String rechargeDiamondsSum; // 累计充值钻石数
    private int thisWeekWorkDays; // 本周工作天数
    private int lastMonthWithdrawalAmount; // 上个月提现金额
    private int thisMonthWithdrawalAmount; // 本月提现金额
    private int thisWeekWithdrawalAmount; // 本周提现金额
    private int withdrawalAmount; // 累计提现金额
    private int familyRole; // 公会角色 1公会长 2管理员 3主播

    private String desc; // 个人说明
    private String roomName; // 房间名称
    private String roomHead; // 房间头像
    private List<String> bannerList; // 主页轮播图
    private String roomAnnounce; // 房间公告
    private String familyAnnounce; // 家族公告
    private List<String> tnRisk; // 风险标签
    private int canSeeGameData; // 可以看到游戏数据模块 0否 1是
    private String liveRoomName; // 直播房名称
    private String liveRoomHead; // 直播房头像
    private String liveRoomAnnounce; // 直播房公告
    private List<String> roomBackgroundList; // 房间背景

    private String registerMethod; // 注册方式
    private String bindLoginMethod; // 绑定的登录方式
    private String phoneCountry; // 手机号国家
    private int os; // 系统 0安卓 1ios
    private int versionCode; // 版本号

    public String getUid() {
        return uid;
    }

    public void setUid(String uid) {
        this.uid = uid;
    }

    public String getToken() {
        return token;
    }

    public void setToken(String token) {
        this.token = token;
    }

    public String getHead() {
        return head;
    }

    public void setHead(String head) {
        this.head = head;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public int getGender() {
        return gender;
    }

    public void setGender(int gender) {
        this.gender = gender;
    }

    public int getRid() {
        return rid;
    }

    public void setRid(int rid) {
        this.rid = rid;
    }

    public String getStrRid() {
        return strRid;
    }

    public void setStrRid(String strRid) {
        this.strRid = strRid;
    }

    public int getOriginalId() {
        return originalId;
    }

    public void setOriginalId(int originalId) {
        this.originalId = originalId;
    }

    public String getCountry() {
        return country;
    }

    public void setCountry(String country) {
        this.country = country;
    }

    public String getPhone() {
        return phone;
    }

    public void setPhone(String phone) {
        this.phone = phone;
    }

    public String getRegisterTime() {
        return registerTime;
    }

    public void setRegisterTime(String registerTime) {
        this.registerTime = registerTime;
    }

    public int getRegisterDays() {
        return registerDays;
    }

    public void setRegisterDays(int registerDays) {
        this.registerDays = registerDays;
    }

    public String getLastLoginTime() {
        return lastLoginTime;
    }

    public void setLastLoginTime(String lastLoginTime) {
        this.lastLoginTime = lastLoginTime;
    }

    public String getVipLevel() {
        return vipLevel;
    }

    public void setVipLevel(String vipLevel) {
        this.vipLevel = vipLevel;
    }

    public String getSvipLevel() {
        return svipLevel;
    }

    public void setSvipLevel(String svipLevel) {
        this.svipLevel = svipLevel;
    }

    public int getWealthLevel() {
        return wealthLevel;
    }

    public void setWealthLevel(int wealthLevel) {
        this.wealthLevel = wealthLevel;
    }

    public int getCharmLevel() {
        return charmLevel;
    }

    public void setCharmLevel(int charmLevel) {
        this.charmLevel = charmLevel;
    }

    public String getIdentity() {
        return identity;
    }

    public void setIdentity(String identity) {
        this.identity = identity;
    }

    public long getDiamondsBalance() {
        return diamondsBalance;
    }

    public void setDiamondsBalance(long diamondsBalance) {
        this.diamondsBalance = diamondsBalance;
    }

    public long getCoinsBalance() {
        return coinsBalance;
    }

    public void setCoinsBalance(long coinsBalance) {
        this.coinsBalance = coinsBalance;
    }

    public String getFirstRechargeTime() {
        return firstRechargeTime;
    }

    public void setFirstRechargeTime(String firstRechargeTime) {
        this.firstRechargeTime = firstRechargeTime;
    }

    public String getLastRechargeTime() {
        return lastRechargeTime;
    }

    public void setLastRechargeTime(String lastRechargeTime) {
        this.lastRechargeTime = lastRechargeTime;
    }

    public String getRechargeChannel() {
        return rechargeChannel;
    }

    public void setRechargeChannel(String rechargeChannel) {
        this.rechargeChannel = rechargeChannel;
    }

    public String getAccountStatus() {
        return accountStatus;
    }

    public void setAccountStatus(String accountStatus) {
        this.accountStatus = accountStatus;
    }

    public String getTnId() {
        return tnId;
    }

    public void setTnId(String tnId) {
        this.tnId = tnId;
    }

    public int getFamilyId() {
        return familyId;
    }

    public void setFamilyId(int familyId) {
        this.familyId = familyId;
    }

    public String getFamilyRid() {
        return familyRid;
    }

    public void setFamilyRid(String familyRid) {
        this.familyRid = familyRid;
    }

    public String getFamilyName() {
        return familyName;
    }

    public void setFamilyName(String familyName) {
        this.familyName = familyName;
    }

    public String getFamilyHead() {
        return familyHead;
    }

    public void setFamilyHead(String familyHead) {
        this.familyHead = familyHead;
    }

    public long getWeekCharmSum() {
        return weekCharmSum;
    }

    public void setWeekCharmSum(long weekCharmSum) {
        this.weekCharmSum = weekCharmSum;
    }

    public long getCharmBalance() {
        return charmBalance;
    }

    public void setCharmBalance(long charmBalance) {
        this.charmBalance = charmBalance;
    }

    public int getUsdBalance() {
        return usdBalance;
    }

    public void setUsdBalance(int usdBalance) {
        this.usdBalance = usdBalance;
    }

    public String getFamilyManager() {
        return familyManager;
    }

    public void setFamilyManager(String familyManager) {
        this.familyManager = familyManager;
    }

    public String getFamilyArea() {
        return familyArea;
    }

    public void setFamilyArea(String familyArea) {
        this.familyArea = familyArea;
    }

    public String getIp() {
        return ip;
    }

    public void setIp(String ip) {
        this.ip = ip;
    }

    public String getLastMonthRechargeDiamonds() {
        return lastMonthRechargeDiamonds;
    }

    public void setLastMonthRechargeDiamonds(String lastMonthRechargeDiamonds) {
        this.lastMonthRechargeDiamonds = lastMonthRechargeDiamonds;
    }

    public String getThisMonthRechargeDiamonds() {
        return thisMonthRechargeDiamonds;
    }

    public void setThisMonthRechargeDiamonds(String thisMonthRechargeDiamonds) {
        this.thisMonthRechargeDiamonds = thisMonthRechargeDiamonds;
    }

    public String getThisWeekRechargeDiamonds() {
        return thisWeekRechargeDiamonds;
    }

    public void setThisWeekRechargeDiamonds(String thisWeekRechargeDiamonds) {
        this.thisWeekRechargeDiamonds = thisWeekRechargeDiamonds;
    }

    public String getCoinSellerDiamondsBalance() {
        return coinSellerDiamondsBalance;
    }

    public void setCoinSellerDiamondsBalance(String coinSellerDiamondsBalance) {
        this.coinSellerDiamondsBalance = coinSellerDiamondsBalance;
    }

    public String getRechargeDiamondsSum() {
        return rechargeDiamondsSum;
    }

    public void setRechargeDiamondsSum(String rechargeDiamondsSum) {
        this.rechargeDiamondsSum = rechargeDiamondsSum;
    }

    public int getThisWeekWorkDays() {
        return thisWeekWorkDays;
    }

    public void setThisWeekWorkDays(int thisWeekWorkDays) {
        this.thisWeekWorkDays = thisWeekWorkDays;
    }

    public int getLastMonthWithdrawalAmount() {
        return lastMonthWithdrawalAmount;
    }

    public void setLastMonthWithdrawalAmount(int lastMonthWithdrawalAmount) {
        this.lastMonthWithdrawalAmount = lastMonthWithdrawalAmount;
    }

    public int getThisMonthWithdrawalAmount() {
        return thisMonthWithdrawalAmount;
    }

    public void setThisMonthWithdrawalAmount(int thisMonthWithdrawalAmount) {
        this.thisMonthWithdrawalAmount = thisMonthWithdrawalAmount;
    }

    public int getThisWeekWithdrawalAmount() {
        return thisWeekWithdrawalAmount;
    }

    public void setThisWeekWithdrawalAmount(int thisWeekWithdrawalAmount) {
        this.thisWeekWithdrawalAmount = thisWeekWithdrawalAmount;
    }

    public int getWithdrawalAmount() {
        return withdrawalAmount;
    }

    public void setWithdrawalAmount(int withdrawalAmount) {
        this.withdrawalAmount = withdrawalAmount;
    }

    public String getDesc() {
        return desc;
    }

    public void setDesc(String desc) {
        this.desc = desc;
    }

    public String getRoomName() {
        return roomName;
    }

    public void setRoomName(String roomName) {
        this.roomName = roomName;
    }

    public List<String> getBannerList() {
        return bannerList;
    }

    public void setBannerList(List<String> bannerList) {
        this.bannerList = bannerList;
    }

    public String getRoomAnnounce() {
        return roomAnnounce;
    }

    public void setRoomAnnounce(String roomAnnounce) {
        this.roomAnnounce = roomAnnounce;
    }

    public String getFamilyAnnounce() {
        return familyAnnounce;
    }

    public void setFamilyAnnounce(String familyAnnounce) {
        this.familyAnnounce = familyAnnounce;
    }

    public List<String> getTnRisk() {
        return tnRisk;
    }

    public void setTnRisk(List<String> tnRisk) {
        this.tnRisk = tnRisk;
    }

    public String getRoomHead() {
        return roomHead;
    }

    public void setRoomHead(String roomHead) {
        this.roomHead = roomHead;
    }

    public List<String> getIpList() {
        return ipList;
    }

    public void setIpList(List<String> ipList) {
        this.ipList = ipList;
    }

    public List<String> getTnIdList() {
        return tnIdList;
    }

    public void setTnIdList(List<String> tnIdList) {
        this.tnIdList = tnIdList;
    }

    public List<String> getBindTnIdList() {
        return bindTnIdList;
    }

    public void setBindTnIdList(List<String> bindTnIdList) {
        this.bindTnIdList = bindTnIdList;
    }

    public int getCanSeeGameData() {
        return canSeeGameData;
    }

    public void setCanSeeGameData(int canSeeGameData) {
        this.canSeeGameData = canSeeGameData;
    }

    public long getVirtualDiamondBalance() {
        return virtualDiamondBalance;
    }

    public void setVirtualDiamondBalance(long virtualDiamondBalance) {
        this.virtualDiamondBalance = virtualDiamondBalance;
    }

    public String getLiveRoomName() {
        return liveRoomName;
    }

    public void setLiveRoomName(String liveRoomName) {
        this.liveRoomName = liveRoomName;
    }

    public String getLiveRoomHead() {
        return liveRoomHead;
    }

    public void setLiveRoomHead(String liveRoomHead) {
        this.liveRoomHead = liveRoomHead;
    }

    public String getLiveRoomAnnounce() {
        return liveRoomAnnounce;
    }

    public void setLiveRoomAnnounce(String liveRoomAnnounce) {
        this.liveRoomAnnounce = liveRoomAnnounce;
    }

    public int getFamilyRole() {
        return familyRole;
    }

    public void setFamilyRole(int familyRole) {
        this.familyRole = familyRole;
    }

    public String getRegisterMethod() {
        return registerMethod;
    }

    public void setRegisterMethod(String registerMethod) {
        this.registerMethod = registerMethod;
    }

    public String getBindLoginMethod() {
        return bindLoginMethod;
    }

    public void setBindLoginMethod(String bindLoginMethod) {
        this.bindLoginMethod = bindLoginMethod;
    }

    public String getPhoneCountry() {
        return phoneCountry;
    }

    public void setPhoneCountry(String phoneCountry) {
        this.phoneCountry = phoneCountry;
    }

    public int getOs() {
        return os;
    }

    public void setOs(int os) {
        this.os = os;
    }

    public int getVersionCode() {
        return versionCode;
    }

    public void setVersionCode(int versionCode) {
        this.versionCode = versionCode;
    }

    public List<String> getRoomBackgroundList() {
        return roomBackgroundList;
    }

    public void setRoomBackgroundList(List<String> roomBackgroundList) {
        this.roomBackgroundList = roomBackgroundList;
    }
}
