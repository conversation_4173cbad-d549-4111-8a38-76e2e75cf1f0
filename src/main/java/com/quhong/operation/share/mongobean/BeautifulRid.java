package com.quhong.operation.share.mongobean;

import org.bson.types.ObjectId;
import org.springframework.data.mongodb.core.mapping.Document;
import org.springframework.data.mongodb.core.mapping.Field;

import java.io.Serializable;

/**
 * 靓号对象
 * <AUTHOR>
 * @date 2020/6/13
 */
@Document(collection = "beautiful_rid")
public class BeautifulRid implements Serializable {

    private static final long serialVersionUID = 1741972725111274724L;

    // id
    private ObjectId _id;
    // 用户uid
    private String uid;
    // 用户真实的rid
    @Field("real_rid")
    private Integer realRid;
    // 靓号
    @Field("beautiful_rid")
    private Integer beautifulRid;
    // 记录时间
    @Field("c_time")
    private Long cTime;
    // 到期时间
    @Field("end_time")
    private Long endTime;

    public ObjectId get_id() {
        return _id;
    }

    public void set_id(ObjectId _id) {
        this._id = _id;
    }

    public String getUid() {
        return uid;
    }

    public void setUid(String uid) {
        this.uid = uid;
    }

    public Integer getRealRid() {
        return realRid;
    }

    public void setRealRid(Integer realRid) {
        this.realRid = realRid;
    }

    public Integer getBeautifulRid() {
        return beautifulRid;
    }

    public void setBeautifulRid(Integer beautifulRid) {
        this.beautifulRid = beautifulRid;
    }

    public Long getcTime() {
        return cTime;
    }

    public void setcTime(Long cTime) {
        this.cTime = cTime;
    }

    public Long getEndTime() {
        return endTime;
    }

    public void setEndTime(Long endTime) {
        this.endTime = endTime;
    }

    @Override
    public String toString() {
        return "BeautifulRid{" +
                "_id=" + _id +
                ", uid='" + uid + '\'' +
                ", realRid=" + realRid +
                ", beautifulRid=" + beautifulRid +
                ", cTime=" + cTime +
                ", endTime=" + endTime +
                '}';
    }

}
