package com.quhong.operation.share.condition;

/**
 * <AUTHOR>
 * @date 2024/11/19
 */
public class PrivateMsgCondition {

    private String search_id;
    private String start;
    private String end;
    private Integer page;
    private Integer page_size;

    public PrivateMsgCondition() {
    }

    public PrivateMsgCondition(String search_id, String start, String end, Integer page, Integer page_size) {
        this.search_id = search_id;
        this.start = start;
        this.end = end;
        this.page = page;
        this.page_size = page_size;
    }

    public String getStart() {
        return start;
    }

    public void setStart(String start) {
        this.start = start;
    }

    public String getEnd() {
        return end;
    }

    public void setEnd(String end) {
        this.end = end;
    }

    public Integer getPage() {
        return page;
    }

    public void setPage(Integer page) {
        this.page = page;
    }

    public Integer getPage_size() {
        return page_size;
    }

    public void setPage_size(Integer page_size) {
        this.page_size = page_size;
    }

    public String getSearch_id() {
        return search_id;
    }

    public void setSearch_id(String search_id) {
        this.search_id = search_id;
    }

    @Override
    public String toString() {
        return "PrivateMsgCondition{" +
                "start='" + start + '\'' +
                ", end='" + end + '\'' +
                ", page=" + page +
                ", page_size=" + page_size +
                ", search_id='" + search_id + '\'' +
                '}';
    }
}
