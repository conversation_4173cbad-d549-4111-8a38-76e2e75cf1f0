package com.quhong.operation.share.condition;

public class RidRecordCondition {

    private int rid; // 原始rid
    private String alphaRid; // 靓号id
    private Integer status; // 操作状态 0 普通账号换靓号 1靓号换靓号 2靓号被移除 3靓号过期
    private String adminName; // admin操作名字
    private String startDate; // 选择的日期
    private String endDate; // 选择的日期
    private Integer page;  //第几页
    private Integer pageSize;  // 每页数量

    public int getRid() {
        return rid;
    }

    public void setRid(int rid) {
        this.rid = rid;
    }

    public String getAlphaRid() {
        return alphaRid;
    }

    public void setAlphaRid(String alphaRid) {
        this.alphaRid = alphaRid;
    }

    public Integer getStatus() {
        return status;
    }

    public void setStatus(Integer status) {
        this.status = status;
    }

    public String getAdminName() {
        return adminName;
    }

    public void setAdminName(String adminName) {
        this.adminName = adminName;
    }

    public String getStartDate() {
        return startDate;
    }

    public void setStartDate(String startDate) {
        this.startDate = startDate;
    }

    public String getEndDate() {
        return endDate;
    }

    public void setEndDate(String endDate) {
        this.endDate = endDate;
    }

    public Integer getPage() {
        return page;
    }

    public void setPage(Integer page) {
        this.page = page;
    }

    public Integer getPageSize() {
        return pageSize;
    }

    public void setPageSize(Integer pageSize) {
        this.pageSize = pageSize;
    }
}
