package com.quhong.operation.share.condition;

/**
 * 加入/退出公会记录查询条件类
 *
 * <AUTHOR>
 * @date 2025/6/6 11:47
 */

public class JoinOrExitFamilyRecordReqConditon extends BaseCondition {

    /**
     * 公会rid
     */
    private Integer familyRid;

    /**
     * 公会长uid,id,rid
     */
    private String ownerStrRid;

    /**
     * 类型 0加入 1退出
     */
    private Integer action;

    /**
     * 退出状态
     * 退出公会长移除主播
     * 公会长同意主播退出申请
     * 主播主动退出公会
     * 运营平台移除主播
     * 公会被禁用
     * 运营平台修改公会长
     * 公会被自动解散
     * 强制回归原公会
     * 删除账号同步退出公会
     * 公会成员转会
     */
    private String exitStatus;

    @Override
    public String toString() {
        return "JoinOrExitFamilyRecordReqConditon{" +
                ", familyRid=" + familyRid +
                ", ownerStrRid='" + ownerStrRid + '\'' +
                ", action=" + action +
                ", exitStatus='" + exitStatus + '\'' +
                '}';
    }

    public String getOwnerStrRid() {
        return ownerStrRid;
    }

    public void setOwnerStrRid(String ownerStrRid) {
        this.ownerStrRid = ownerStrRid;
    }

    public Integer getFamilyRid() {
        return familyRid;
    }

    public void setFamilyRid(Integer familyRid) {
        this.familyRid = familyRid;
    }

    public Integer getAction() {
        return action;
    }

    public void setAction(Integer action) {
        this.action = action;
    }

    public String getExitStatus() {
        return exitStatus;
    }

    public void setExitStatus(String exitStatus) {
        this.exitStatus = exitStatus;
    }
}
