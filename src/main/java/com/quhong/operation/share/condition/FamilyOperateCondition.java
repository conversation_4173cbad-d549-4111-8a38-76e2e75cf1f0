package com.quhong.operation.share.condition;

/**
 * <AUTHOR>
 * @date 2023/11/10
 */
public class FamilyOperateCondition extends BaseCondition{

    private Integer optType;

    private String optAccountUid;

    private Integer startTime;

    private Integer endTime;

    public Integer getOptType() {
        return optType;
    }

    public void setOptType(Integer optType) {
        this.optType = optType;
    }

    public String getOptAccountUid() {
        return optAccountUid;
    }

    public void setOptAccountUid(String optAccountUid) {
        this.optAccountUid = optAccountUid;
    }

    public Integer getStartTime() {
        return startTime;
    }

    public void setStartTime(Integer startTime) {
        this.startTime = startTime;
    }

    public Integer getEndTime() {
        return endTime;
    }

    public void setEndTime(Integer endTime) {
        this.endTime = endTime;
    }
}
