package com.quhong.operation.share.condition;

/**
 * <AUTHOR>
 * @date 2023/11/30
 */
public class QuitRequestCondition extends BaseCondition {

    private Integer startTime; // 开始时间
    private Integer endTime; // 结束时间
    private Integer familyRid; // 公会rid
    private String rid; // 主播rid

    private Integer id;
    private Integer familyId;
    private String uid;
    private int status; // 1待公会长审核 2公会长已同意 3公会长已拒绝待处理 4公会长拒绝后官方同意 5公会长拒绝后官方拒绝

    @Override
    public int getStatus() {
        return status;
    }

    public void setStatus(int status) {
        this.status = status;
    }

    public Integer getId() {
        return id;
    }

    public void setId(Integer id) {
        this.id = id;
    }

    public Integer getFamilyRid() {
        return familyRid;
    }

    public void setFamilyRid(Integer familyRid) {
        this.familyRid = familyRid;
    }

    public String getRid() {
        return rid;
    }

    public void setRid(String rid) {
        this.rid = rid;
    }

    public Integer getFamilyId() {
        return familyId;
    }

    public void setFamilyId(Integer familyId) {
        this.familyId = familyId;
    }

    public String getUid() {
        return uid;
    }

    public void setUid(String uid) {
        this.uid = uid;
    }

    public Integer getStartTime() {
        return startTime;
    }

    public void setStartTime(Integer startTime) {
        this.startTime = startTime;
    }

    public Integer getEndTime() {
        return endTime;
    }

    public void setEndTime(Integer endTime) {
        this.endTime = endTime;
    }
}
