package com.quhong.operation.share.dto;

import java.util.List;

/**
 * @date 2022/10/19
 */
public class BlindBoxDTO {
    private int rid;
    private ZipInfoVO zipInfoVO;

    public static class ZipInfoVO {
        private int ztype; // 1 恶魔礼物 2 天使礼物 3 新版整蛊礼物 4 带礼物介绍的礼物 5 幸运礼物 6变声礼物 7盲盒礼物
        private Integer showDetail; // 是否展示礼物详情 0不展示 1展示普通礼物banner 2表示展示盲盒礼物banner
        private String descUrl; // h5介绍页
        private String desc; // 礼物介绍英语
        private String descAr; // 礼物介绍阿语
        private String propIcon; // 道具说明图片

        private Integer webType; // 0=跳转、1=全屏、2=半屏
        private Integer width; // 礼物详情宽度，字段可能不存在
        private Integer height; // 礼物详情高度，字段可能不存在

        // 盲盒礼物相关
        private Integer randomPoolSize; // 盲盒奖池大小
        private List<RandomGift> randomGiftList; // 盲盒中奖礼物配置

        public int getZtype() {
            return ztype;
        }

        public void setZtype(int ztype) {
            this.ztype = ztype;
        }

        public Integer getShowDetail() {
            return showDetail;
        }

        public void setShowDetail(Integer showDetail) {
            this.showDetail = showDetail;
        }

        public String getDescUrl() {
            return descUrl;
        }

        public void setDescUrl(String descUrl) {
            this.descUrl = descUrl;
        }

        public String getDesc() {
            return desc;
        }

        public void setDesc(String desc) {
            this.desc = desc;
        }

        public String getDescAr() {
            return descAr;
        }

        public void setDescAr(String descAr) {
            this.descAr = descAr;
        }

        public String getPropIcon() {
            return propIcon;
        }

        public void setPropIcon(String propIcon) {
            this.propIcon = propIcon;
        }

        public Integer getWebType() {
            return webType;
        }

        public void setWebType(Integer webType) {
            this.webType = webType;
        }

        public Integer getWidth() {
            return width;
        }

        public void setWidth(Integer width) {
            this.width = width;
        }

        public Integer getHeight() {
            return height;
        }

        public void setHeight(Integer height) {
            this.height = height;
        }

        public Integer getRandomPoolSize() {
            return randomPoolSize;
        }

        public void setRandomPoolSize(Integer randomPoolSize) {
            this.randomPoolSize = randomPoolSize;
        }

        public List<RandomGift> getRandomGiftList() {
            return randomGiftList;
        }

        public void setRandomGiftList(List<RandomGift> randomGiftList) {
            this.randomGiftList = randomGiftList;
        }


        @Override
        public String toString() {
            return "ZipInfoVO{" +
                    "ztype=" + ztype +
                    ", showDetail=" + showDetail +
                    ", descUrl='" + descUrl + '\'' +
                    ", desc='" + desc + '\'' +
                    ", descAr='" + descAr + '\'' +
                    ", propIcon='" + propIcon + '\'' +
                    ", webType=" + webType +
                    ", width=" + width +
                    ", height=" + height +
                    ", randomPoolSize=" + randomPoolSize +
                    ", randomGiftList=" + randomGiftList +
                    '}';
        }
    }

    public static class RandomGift {
        private Integer giftId;
        private Double prob; // 获取概率

        public Integer getGiftId() {
            return giftId;
        }

        public void setGiftId(Integer giftId) {
            this.giftId = giftId;
        }

        public Double getProb() {
            return prob;
        }

        public void setProb(Double prob) {
            this.prob = prob;
        }
    }

    public int getRid() {
        return rid;
    }

    public void setRid(int rid) {
        this.rid = rid;
    }

    public ZipInfoVO getZipInfoVO() {
        return zipInfoVO;
    }

    public void setZipInfoVO(ZipInfoVO zipInfoVO) {
        this.zipInfoVO = zipInfoVO;
    }

    @Override
    public String toString() {
        return "BlindBoxDTO{" +
                "rid=" + rid +
                ", zipInfoVO=" + zipInfoVO +
                '}';
    }
}
