package com.quhong.operation.share.dto;

import com.quhong.enums.ResTypeEnum;
import com.quhong.mongo.data.ActivityTemplateData;
import com.quhong.mongo.data.RankingActivity;

import java.util.List;
import java.util.Set;

/**
 * <AUTHOR>
 * @date 2024/1/11
 */
public class ActivityTemplateDTO {

    private String activityId; // 活动id
    private String acNameEn; // 英文活动名
    private String acNameAr; // 阿语活动名
    private Integer templateType;   // 模板样式类型
    private String musicUrl; // 背景音乐
    private String acUrl; // h5地址(自动生成)
    private Integer startTime; // 活动开始时间
    private Integer endTime; // 活动结束时间
    private Integer timePeriodType; // 时间周期类型 0总榜 1日榜 2周榜 3月榜
    private Integer statType; //  统计类型 0按钻石统计 1钻石按比例转换成积分统计
    private String prop; // 统计比例 (按积分统计)
    private String propIcon; // 比例图标
    private int activityType; // 活动类型 0礼物活动 1游戏活动
    private int gameNumType; // 游戏数量类型 1单个游戏 2多个游戏 3全部游戏
    private Set<String> gameIdSet; // 游戏id集合
    private ActivityTemplateData.ActivityConfig config; // 活动配置
    private Integer giftConfigType; // 礼物配置类型 0指定礼物 1所以钻石礼物
    private Integer roomType; // 统计房间类型 0全房间 1语聊房 2直播房
    private List<ActivityTemplateData.ActivityGift> activityGiftList; // 活动礼物，都是按照钻石数来进行排行
    private List<ActivityTemplateData.RankingConfig> rankingConfigList; // 排行榜奖励配置
    private Integer status; // 0待激活 1已激活 2进行中 3已结束
    private Integer isTestActivity; // 是测试活动 0否 1是 （测试活动仅针对测试id有效）
    private String testStrRid; // 测试人员rid

    public String getActivityId() {
        return activityId;
    }

    public void setActivityId(String activityId) {
        this.activityId = activityId;
    }

    public String getAcNameEn() {
        return acNameEn;
    }

    public void setAcNameEn(String acNameEn) {
        this.acNameEn = acNameEn;
    }

    public String getAcNameAr() {
        return acNameAr;
    }

    public void setAcNameAr(String acNameAr) {
        this.acNameAr = acNameAr;
    }

    public Integer getTemplateType() {
        return templateType;
    }

    public void setTemplateType(Integer templateType) {
        this.templateType = templateType;
    }

    public String getAcUrl() {
        return acUrl;
    }

    public void setAcUrl(String acUrl) {
        this.acUrl = acUrl;
    }

    public Integer getStartTime() {
        return startTime;
    }

    public void setStartTime(Integer startTime) {
        this.startTime = startTime;
    }

    public Integer getEndTime() {
        return endTime;
    }

    public void setEndTime(Integer endTime) {
        this.endTime = endTime;
    }

    public Integer getTimePeriodType() {
        return timePeriodType;
    }

    public void setTimePeriodType(Integer timePeriodType) {
        this.timePeriodType = timePeriodType;
    }

    public Integer getStatType() {
        return statType;
    }

    public void setStatType(Integer statType) {
        this.statType = statType;
    }

    public String getProp() {
        return prop;
    }

    public void setProp(String prop) {
        this.prop = prop;
    }

    public String getPropIcon() {
        return propIcon;
    }

    public void setPropIcon(String propIcon) {
        this.propIcon = propIcon;
    }

    public ActivityTemplateData.ActivityConfig getConfig() {
        return config;
    }

    public void setConfig(ActivityTemplateData.ActivityConfig config) {
        this.config = config;
    }

    public Integer getGiftConfigType() {
        return giftConfigType;
    }

    public void setGiftConfigType(Integer giftConfigType) {
        this.giftConfigType = giftConfigType;
    }

    public List<ActivityTemplateData.ActivityGift> getActivityGiftList() {
        return activityGiftList;
    }

    public void setActivityGiftList(List<ActivityTemplateData.ActivityGift> activityGiftList) {
        this.activityGiftList = activityGiftList;
    }

    public List<ActivityTemplateData.RankingConfig> getRankingConfigList() {
        return rankingConfigList;
    }

    public void setRankingConfigList(List<ActivityTemplateData.RankingConfig> rankingConfigList) {
        this.rankingConfigList = rankingConfigList;
    }

    public Integer getStatus() {
        return status;
    }

    public void setStatus(Integer status) {
        this.status = status;
    }

    public Integer getIsTestActivity() {
        return isTestActivity;
    }

    public void setIsTestActivity(Integer isTestActivity) {
        this.isTestActivity = isTestActivity;
    }

    public String getTestStrRid() {
        return testStrRid;
    }

    public void setTestStrRid(String testStrRid) {
        this.testStrRid = testStrRid;
    }

    public String getMusicUrl() {
        return musicUrl;
    }

    public void setMusicUrl(String musicUrl) {
        this.musicUrl = musicUrl;
    }

    public Integer getRoomType() {
        return roomType;
    }

    public void setRoomType(Integer roomType) {
        this.roomType = roomType;
    }

    public int getActivityType() {
        return activityType;
    }

    public void setActivityType(int activityType) {
        this.activityType = activityType;
    }

    public int getGameNumType() {
        return gameNumType;
    }

    public void setGameNumType(int gameNumType) {
        this.gameNumType = gameNumType;
    }

    public Set<String> getGameIdSet() {
        return gameIdSet;
    }

    public void setGameIdSet(Set<String> gameIdSet) {
        this.gameIdSet = gameIdSet;
    }
}
