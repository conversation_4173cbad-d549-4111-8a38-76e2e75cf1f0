package com.quhong.operation.share.dto;

import com.alibaba.fastjson.annotation.JSONField;

public class StartPageDto {



    // 列表筛选
    private Integer valid;
    private String search;
    private Integer page;  //第几页

    @JSONField(name = "page_size")
    private Integer pageSize;  // 每页数量

    // 新增、 更新及删除
    @JSONField(name = "flash_id")
    private Integer flashId;

    private Integer skip;  // 等待时长
    private String title;  // 标题

    @JSONField(name = "title_ar")
    private String titleAr;  // 标题阿语

    private String link;  // 跳转链接


    @JSONField(name = "room_rid")
    private String roomRid;

    @J<PERSON>NField(name = "jump_rid")
    private String jumpRid;

    private Integer atype;  // 跳转类型

    private String url;

    @JSONField(name = "url_ar")
    private String urlAr;

    private String iphonexUrl;
    private String iphonexUrlAr;

    private Integer validTime;

    public Integer getValid() {
        return valid;
    }

    public void setValid(Integer valid) {
        this.valid = valid;
    }

    public String getSearch() {
        return search;
    }

    public void setSearch(String search) {
        this.search = search;
    }

    public Integer getPage() {
        return page;
    }

    public void setPage(Integer page) {
        this.page = page;
    }

    public Integer getPageSize() {
        return pageSize;
    }

    public void setPageSize(Integer pageSize) {
        this.pageSize = pageSize;
    }

    public Integer getFlashId() {
        return flashId;
    }

    public void setFlashId(Integer flashId) {
        this.flashId = flashId;
    }

    public Integer getSkip() {
        return skip;
    }

    public void setSkip(Integer skip) {
        this.skip = skip;
    }

    public String getTitle() {
        return title;
    }

    public void setTitle(String title) {
        this.title = title;
    }

    public String getTitleAr() {
        return titleAr;
    }

    public void setTitleAr(String titleAr) {
        this.titleAr = titleAr;
    }

    public String getLink() {
        return link;
    }

    public void setLink(String link) {
        this.link = link;
    }

    public String getRoomRid() {
        return roomRid;
    }

    public void setRoomRid(String roomRid) {
        this.roomRid = roomRid;
    }

    public String getJumpRid() {
        return jumpRid;
    }

    public void setJumpRid(String jumpRid) {
        this.jumpRid = jumpRid;
    }

    public Integer getAtype() {
        return atype;
    }

    public void setAtype(Integer atype) {
        this.atype = atype;
    }

    public String getUrl() {
        return url;
    }

    public void setUrl(String url) {
        this.url = url;
    }

    public String getUrlAr() {
        return urlAr;
    }

    public void setUrlAr(String urlAr) {
        this.urlAr = urlAr;
    }

    public String getIphonexUrl() {
        return iphonexUrl;
    }

    public void setIphonexUrl(String iphonexUrl) {
        this.iphonexUrl = iphonexUrl;
    }

    public String getIphonexUrlAr() {
        return iphonexUrlAr;
    }

    public void setIphonexUrlAr(String iphonexUrlAr) {
        this.iphonexUrlAr = iphonexUrlAr;
    }

    public Integer getValidTime() {
        return validTime;
    }

    public void setValidTime(Integer validTime) {
        this.validTime = validTime;
    }

    @Override
    public String toString() {
        return "StartPageDto{" +
                "valid=" + valid +
                ", search='" + search + '\'' +
                ", page=" + page +
                ", pageSize=" + pageSize +
                ", flashId=" + flashId +
                ", skip=" + skip +
                ", title='" + title + '\'' +
                ", titleAr='" + titleAr + '\'' +
                ", link='" + link + '\'' +
                ", roomRid='" + roomRid + '\'' +
                ", jumpRid='" + jumpRid + '\'' +
                ", atype=" + atype +
                ", url='" + url + '\'' +
                ", urlAr='" + urlAr + '\'' +
                '}';
    }
}
