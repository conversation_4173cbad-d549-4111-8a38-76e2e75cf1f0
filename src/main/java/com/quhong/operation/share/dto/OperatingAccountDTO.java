package com.quhong.operation.share.dto;

/**
 * <AUTHOR>
 * @date 2023/9/6
 */
public class OperatingAccountDTO {

    /**
     * 账号id
     */
    private String id;

    /**
     * 账号
     */
    private String account;

    /**
     * 密码
     */
    private String pwd;

    /**
     * 权限等级
     */
    private int role;

    /**
     * 运营平台角色 0非家族管理员 1家族运营负责人 2家族超管 3家族总运营账号
     */
    private int familyManageRole;

    /**
     * 上级用户
     */
    private String superior;

    /**
     * 备注
     */
    private String remark;

    /**
     * 客服id
     */
    private int serviceRid;

    /**
     * 角色id
     */
    private int roleId;


    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public String getAccount() {
        return account;
    }

    public void setAccount(String account) {
        this.account = account;
    }

    public String getPwd() {
        return pwd;
    }

    public void setPwd(String pwd) {
        this.pwd = pwd;
    }

    public int getRole() {
        return role;
    }

    public void setRole(int role) {
        this.role = role;
    }

    public void setFamilyManageRole(int familyManageRole) {
        this.familyManageRole = familyManageRole;
    }

    public Integer getFamilyManageRole() {
        return familyManageRole;
    }

    public String getSuperior() {
        return superior;
    }

    public void setSuperior(String superior) {
        this.superior = superior;
    }

    public String getRemark() {
        return remark;
    }

    public void setRemark(String remark) {
        this.remark = remark;
    }

    public int getServiceRid() {
        return serviceRid;
    }

    public void setServiceRid(int serviceRid) {
        this.serviceRid = serviceRid;
    }

    public int getRoleId() {
        return roleId;
    }

    public void setRoleId(int roleId) {
        this.roleId = roleId;
    }
}
