package com.quhong.operation.share.elasticsearch.data;

import org.springframework.data.annotation.Id;
import org.springframework.data.elasticsearch.annotations.Document;

import java.io.Serializable;

/**
 * elasticsearch流水对象
 */
@Document(indexName = "money_detail2_es")
public class MoneyDetailEs implements Serializable {
    private static final long serialVersionUID = 7876841480290031598L;

    @Id
    private String id;
    // 用户uid
    private String uid;
    // 记录钻石
    private Long changed;
    // 当前余额钻石
    private Long balance;
    // 记录标题
    private String title;
    // 记录描述
    private String desc;
    // 记录类型
    private Integer atype;
    // 更新时间
    private Integer mtime;

    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public String getUid() {
        return uid;
    }

    public void setUid(String uid) {
        this.uid = uid;
    }

    public Long getChanged() {
        return changed;
    }

    public void setChanged(Long changed) {
        this.changed = changed;
    }

    public Long getBalance() {
        return balance;
    }

    public void setBalance(Long balance) {
        this.balance = balance;
    }

    public String getTitle() {
        return title;
    }

    public void setTitle(String title) {
        this.title = title;
    }

    public String getDesc() {
        return desc;
    }

    public void setDesc(String desc) {
        this.desc = desc;
    }

    public Integer getAtype() {
        return atype;
    }

    public void setAtype(Integer atype) {
        this.atype = atype;
    }

    public Integer getMtime() {
        return mtime;
    }

    public void setMtime(Integer mtime) {
        this.mtime = mtime;
    }

    @Override
    public String toString() {
        return "MoneyDetail{" +
                "id='" + id + '\'' +
                ", uid='" + uid + '\'' +
                ", changed=" + changed +
                ", balance=" + balance +
                ", title='" + title + '\'' +
                ", desc='" + desc + '\'' +
                ", atype=" + atype +
                ", mtime=" + mtime +
                '}';
    }
}
