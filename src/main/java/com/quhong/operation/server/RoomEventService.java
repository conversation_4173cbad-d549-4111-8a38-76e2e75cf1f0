package com.quhong.operation.server;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.CollectionUtils;
import com.quhong.analysis.BackendReviewRecordEvent;
import com.quhong.analysis.EventDTO;
import com.quhong.analysis.EventReport;
import com.quhong.constant.MoneyActionType;
import com.quhong.core.distribution.DistributeLock;
import com.quhong.core.utils.DateHelper;
import com.quhong.data.ActorData;
import com.quhong.data.dto.MoneyDetailReq;
import com.quhong.enums.HttpCode;
import com.quhong.enums.SLangType;
import com.quhong.exception.CommonH5Exception;
import com.quhong.mongo.dao.ActorDao;
import com.quhong.mq.MqSenderService;
import com.quhong.mysql.dao.RoomEventDao;
import com.quhong.mysql.data.RoomEventData;
import com.quhong.operation.dao.ManagerDao;
import com.quhong.operation.share.condition.RoomEventCondition;
import com.quhong.operation.share.mongobean.Manager;
import com.quhong.operation.share.vo.PageResultVO;
import com.quhong.operation.share.vo.RoomEventVO;
import com.quhong.service.OfficialMsgService;
import com.quhong.utils.RoomUtils;
import jakarta.annotation.Resource;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;

import java.util.ArrayList;
import java.util.List;

@Service
public class RoomEventService {

    public final Logger logger = LoggerFactory.getLogger(getClass());

    @Resource
    private RoomEventDao roomEventDao;
    @Resource
    private ActorDao actorDao;
    @Resource
    private ManagerDao managerDao;
    @Resource
    private MqSenderService mqSenderService;
    @Resource
    private EventReport eventReport;
    @Resource
    private OfficialMsgService officialMsgService;

    public void checkRefundFee() {
        List<RoomEventData> needRefundFeeList = roomEventDao.getNeedRefundFeeList();
        if (CollectionUtils.isEmpty(needRefundFeeList)) {
            return;
        }
        for (RoomEventData eventData : needRefundFeeList) {
            try (DistributeLock lock = new DistributeLock(RoomEventDao.ROOM_EVENT_LOCK_KEY + eventData.getId())) {
                lock.lock();
                RoomEventData data = roomEventDao.selectById(eventData.getId());
                if (data == null || data.getEndTime() > DateHelper.getNowSeconds() || data.getStatus() == 1 || data.getRefundBeans() > 0) {
                    continue;
                }
                data.setRefundBeans(RoomEventDao.COST_BEAN);
                roomEventDao.updateRefundBeans(data);
                refundFee(data.getCreator(), data.getRoomId(), data.getId());
                logger.info("roomEventRefundFee eventId={} uid={} refundBeans={}.", data.getId(), data.getCreator(), data.getRefundBeans());
            }
        }
    }

    public void autoEventReview() {
        List<RoomEventData> needReviewList = roomEventDao.getNeedReviewList();
        if (CollectionUtils.isEmpty(needReviewList)) {
            return;
        }
        for (RoomEventData eventData : needReviewList) {
            try (DistributeLock lock = new DistributeLock(RoomEventDao.ROOM_EVENT_LOCK_KEY + eventData.getId())) {
                lock.lock();
                RoomEventData data = roomEventDao.selectById(eventData.getId());
                if (data == null || data.getStatus() != 0) {
                    continue;
                }
                data.setStatus(1);
                data.setReviewerUid("system");
                data.setMtime(DateHelper.getNowSeconds());
                roomEventDao.updateStatus(data);
                sendEventReviewResultMsg(data.getCreator(), data.getName(), data.getStatus());
                doEventReport(data);
            }
        }
    }

    private void refundFee(String uid, String roomId, int eventId) {
        MoneyActionType actionType = MoneyActionType.REFUND_CREATE_ROOM_EVENT_FEE;
        MoneyDetailReq moneyDetailReq = new MoneyDetailReq();
        moneyDetailReq.setId("refund_create_room_event_fee_" + eventId);
        moneyDetailReq.setRoomId(roomId);
        moneyDetailReq.setUid(uid);
        moneyDetailReq.setAtype(actionType.actionType);
        moneyDetailReq.setChanged(RoomEventDao.COST_BEAN);
        moneyDetailReq.setTitle(actionType.title);
        moneyDetailReq.setDesc(actionType.desc);
        mqSenderService.asyncChargeDiamonds(moneyDetailReq);
    }

    public PageResultVO<RoomEventVO> selectPage(RoomEventCondition condition) {
        int page = condition.getPage() == 0 ? 1 : condition.getPage();
        int pageSize = condition.getPageSize() == 0 ? 10 : condition.getPageSize();
        int startTime = 0;
        if (StringUtils.hasLength(condition.getStartTime())) {
            startTime = DateHelper.ARABIAN.stringTimeToStampSecond(condition.getStartTime());
        }
        int endTime = 0;
        if (StringUtils.hasLength(condition.getEndTime())) {
            endTime = DateHelper.ARABIAN.stringTimeToStampSecond(condition.getEndTime());
        }
        String creator = "";
        if (StringUtils.hasLength(condition.getRid())) {
            ActorData actorData = actorDao.getActorByRidOrAlphaRid(condition.getRid());
            creator = actorData != null ? actorData.getUid() : "---";
        }
        String roomId = "";
        if (StringUtils.hasLength(condition.getRoomRid())) {
            ActorData actorData = actorDao.getActorByRidOrAlphaRid(condition.getRoomRid());
            roomId = actorData != null ? RoomUtils.getRoomId(actorData.getUid()) : "---";
        }
        String reviewer = "";
        if (StringUtils.hasLength(condition.getReviewer())) {
            if (condition.getReviewer().equals("system")) {
                reviewer = "system";
            } else {
                Manager manager = managerDao.getManager(condition.getReviewer());
                reviewer = manager != null ? manager.get_id().toString() : "---";
            }
        }
        IPage<RoomEventData> iPage = roomEventDao.selectPage(condition.getStatus(), startTime, endTime, creator, roomId, reviewer, page, pageSize);
        List<RoomEventVO> list = new ArrayList<>();
        if (!CollectionUtils.isEmpty(iPage.getRecords())) {
            for (RoomEventData data : iPage.getRecords()) {
                RoomEventVO vo = new RoomEventVO();
                BeanUtils.copyProperties(data, vo);
                vo.setEventId(data.getId());
                ActorData roomOwner = actorDao.getActorDataFromCache(RoomUtils.getRoomHostId(data.getRoomId()));
                vo.setRoomRid(roomOwner.getRid() + "");
                vo.setRoomStrRid(roomOwner.getShowRid());
                ActorData actorData = actorDao.getActorDataFromCache(data.getCreator());
                vo.setCreatorRid(actorData.getRid() + "");
                vo.setCreatorStrRid(actorData.getShowRid());
                if (StringUtils.hasLength(data.getReviewerUid())) {
                    if (data.getReviewerUid().equals("system")) {
                        vo.setReviewer("系统审核");
                    } else {
                        Manager manager = managerDao.getDataByUid(data.getReviewerUid());
                        vo.setReviewer(manager != null ? manager.getName() : data.getReviewerUid());
                    }
                }
                vo.setStatus(data.getEndTime() < DateHelper.getNowSeconds() ? -1 : data.getStatus());
                list.add(vo);
            }
        }
        return new PageResultVO<>(iPage.getTotal(), list);
    }

    public Object updateStatus(String uid, int eventId, int status, String reason) {
        try (DistributeLock lock = new DistributeLock(RoomEventDao.ROOM_EVENT_LOCK_KEY + eventId)) {
            lock.lock();
            RoomEventData data = roomEventDao.selectById(eventId);
            if (data == null || data.getStatus() != 0) {
                throw new CommonH5Exception(HttpCode.PARAM_ERROR);
            }
            if (data.getEndTime() < DateHelper.getNowSeconds()) {
                throw new CommonH5Exception(new HttpCode(1, "活动已结束，无法再修改"));
            }
            data.setStatus(status);
            data.setReviewerUid(uid);
            data.setReviewReason(reason);
            roomEventDao.updateStatus(data);
            sendEventReviewResultMsg(data.getCreator(), data.getName(), data.getStatus());
            doEventReport(data);
            return null;
        }
    }

    public void doEventReport(RoomEventData data) {
        BackendReviewRecordEvent event = new BackendReviewRecordEvent();
        event.setUid(data.getCreator());
        event.setCreate_ctime(data.getCtime());
        event.setModify_ctime(data.getMtime());
        event.setScene("room_event");
        event.setScene_desc(data.getId() + "");
        event.setSub_scene(data.getRoomId());
        event.setOperater(data.getReviewerUid());
        event.setReview_status(data.getStatus());
        eventReport.track(new EventDTO(event));
    }
    
    public void sendEventReviewResultMsg(String uid, String eventName, int status) {
        ActorData actorData = actorDao.getActorDataFromCache(uid);
        boolean isEn = actorData.getSlang() == SLangType.ENGLISH;
        String title = isEn ? "Event Review Result" : "نتيجة مراجعة الأنشطة";
        String body;
        if (status == 1) {
            body = isEn ? "The activity \"%s\" has been approved" : "Your event has been reviewed and approved.";
        } else {
            body = isEn ? "The banner for the activity \"%s\" violates the rules. Please modify and resubmit. [Waho Review Team]" : "بانر الأنشطة \"%s\" مخالف للقواعد، يرجى التعديل وإعادة التقديم. [فريق مراجعة واهو";
            body = body.formatted(eventName);
        }
        officialMsgService.officialMsgPush(uid, title, body, 0);
    }
}
