package com.quhong.operation.server;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.google.common.collect.Interner;
import com.google.common.collect.Interners;
import com.quhong.analysis.EventDTO;
import com.quhong.analysis.EventReport;
import com.quhong.analysis.StarGiftGrantRecordEvent;
import com.quhong.core.concurrency.BaseTaskFactory;
import com.quhong.core.concurrency.tasks.Task;
import com.quhong.core.utils.DateHelper;
import com.quhong.data.ActorData;
import com.quhong.data.condition.GiftCondition;
import com.quhong.enums.HttpCode;
import com.quhong.exception.CommonException;
import com.quhong.exception.CommonH5Exception;
import com.quhong.mongo.dao.ActorDao;
import com.quhong.mysql.dao.FamilyDao;
import com.quhong.mysql.dao.GiftDao;
import com.quhong.mysql.dao.GiftSortDao;
import com.quhong.mysql.data.FamilyData;
import com.quhong.mysql.data.GiftData;
import com.quhong.mysql.data.GiftSortData;
import com.quhong.operation.share.condition.BaseCondition;
import com.quhong.operation.share.condition.ResourceCondition;
import com.quhong.operation.share.dto.BlindBoxDTO;
import com.quhong.operation.share.dto.GiftDTO;
import com.quhong.operation.share.dto.GiftPanelDTO;
import com.quhong.operation.share.vo.*;
import com.quhong.operation.utils.CollectionUtil;
import com.quhong.operation.utils.ZipUtil;
import com.quhong.utils.WalletUtils;
import com.quhong.vo.PageVO;
import jakarta.annotation.Resource;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;
import org.springframework.util.ObjectUtils;
import org.springframework.util.StringUtils;

import java.sql.Timestamp;
import java.util.*;
import java.util.stream.Collectors;

@Component
public class GiftService implements ResourceService {

    private static final Logger logger = LoggerFactory.getLogger(GiftService.class);

    private final static String FILE_BUCKET_PATH = "gift/";
    private final static String ZIP_PREFIX = "gift_";
    private static final Interner<String> stringPool = Interners.newWeakInterner();

    @Resource
    private GiftDao giftDao;
    @Resource
    private GiftSortDao giftSortDao;
    @Resource
    private FamilyDao familyDao;
    @Resource
    private ActorDao actorDao;
    @Resource
    private EventReport eventReport;

    public PageResultVO<GiftVO> giftList(GiftCondition condition) {
        PageResultVO<GiftVO> pageVO = new PageResultVO<>();
        IPage<GiftData> pageGift = giftDao.selectPageList(condition);
        List<GiftVO> voList = new ArrayList<>();
        for (GiftData data : pageGift.getRecords()) {
            GiftVO vo = new GiftVO();
            BeanUtils.copyProperties(data, vo, "giftNumOptions");
            if (StringUtils.hasLength(data.getGiftNumOptions())) {
                vo.setGiftNumOptions(Arrays.stream(data.getGiftNumOptions().split(",")).map(Integer::parseInt).collect(Collectors.toList()));
            } else {
                vo.setGiftNumOptions(Collections.emptyList());
            }
            if (StringUtils.hasLength(data.getPanelType())) {
                vo.setGpTypeList(Arrays.stream(data.getPanelType().replace("'", "").split(",")).map(Integer::parseInt).collect(Collectors.toList()));
            } else {
                vo.setGpTypeList(Collections.emptyList());
            }
            if (StringUtils.hasLength(data.getShowArea())) {
                vo.setShowAreaList(Arrays.stream(data.getShowArea().replace("'", "").split(",")).map(Integer::parseInt).collect(Collectors.toList()));
            } else {
                vo.setShowAreaList(Collections.emptyList());
            }
            long ctime = data.getCtime().getTime();
            vo.setCtime(ctime / 1000L);
            vo.setZipInfoVO(JSONObject.parseObject(data.getZipInfo(), GiftVO.ZipInfoVO.class));
            vo.setLevel(vo.getZipInfoVO() != null && vo.getZipInfoVO().getLevel() != null ? vo.getZipInfoVO().getLevel() : 0);
            vo.setCachePrice(data.getPrice());
            vo.setPrice(WalletUtils.diamondsForDisplay(vo.getPrice()));
            if (data.getIsFusionAnimation() == GiftDao.FUSION_MODE_4) {
                FamilyData familyData = familyDao.selectById(Integer.valueOf(data.getFusionId()));
                vo.setFusionId(String.valueOf(familyData.getRid()));
            } else if (StringUtils.hasLength(data.getFusionId())) {
                ActorData actorData = actorDao.getActorDataFromCache(data.getFusionId());
                vo.setFusionId(actorData != null ? actorData.getRid() + "" : "");
            }
            voList.add(vo);
        }

        pageVO.setList(voList);
        pageVO.setTotal(pageGift.getTotal());
        return pageVO;
    }


    private List<Map<String, String>> convertAddZip(GiftDTO dto) {
        List<Map<String, String>> urlList = new ArrayList<>();
        if (!StringUtils.isEmpty(dto.getActionUrl())) {
            Map<String, String> actMap = new HashMap<>();
            actMap.put("fileName", "act");
            actMap.put("fileUrl", dto.getActionUrl());
            urlList.add(actMap);
        }

        if (!StringUtils.isEmpty(dto.getAudioUrl())) {
            Map<String, String> videoMap = new HashMap<>();
            videoMap.put("fileName", "voice");
            videoMap.put("fileUrl", dto.getAudioUrl());
            urlList.add(videoMap);
        }

        if (!StringUtils.isEmpty(dto.getFusionUrl())) {
            Map<String, String> fusionMap = new HashMap<>();
            fusionMap.put("fileName", "fusion");
            fusionMap.put("fileUrl", dto.getFusionUrl());
            urlList.add(fusionMap);
        }
        return urlList;
    }


    private List<Map<String, String>> convertUpdateZip(GiftDTO dto, GiftData giftData) {
        List<Map<String, String>> urlList = new ArrayList<>();
        if (!Objects.equals(dto.getActionUrl(), giftData.getActionUrl()) ||
                !Objects.equals(dto.getAudioUrl(), giftData.getAudioUrl()) ||
                !Objects.equals(dto.getFusionUrl(), giftData.getFusionUrl())) {
            if (!StringUtils.isEmpty(dto.getActionUrl())) {
                Map<String, String> actMap = new HashMap<>();
                actMap.put("fileName", "act");
                actMap.put("fileUrl", dto.getActionUrl());
                urlList.add(actMap);
            }

            if (!StringUtils.isEmpty(dto.getAudioUrl())) {
                Map<String, String> videoMap = new HashMap<>();
                videoMap.put("fileName", "voice");
                videoMap.put("fileUrl", dto.getAudioUrl());
                urlList.add(videoMap);
            }

            if (!StringUtils.isEmpty(dto.getFusionUrl())) {
                Map<String, String> fusionMap = new HashMap<>();
                fusionMap.put("fileName", "fusion");
                fusionMap.put("fileUrl", dto.getFusionUrl());
                urlList.add(fusionMap);
            }
        }

        return urlList;
    }

    private void asyncUpdateSource(GiftData giftData, List<Map<String, String>> urlList) {
        if (!urlList.isEmpty()) {
            String zipPath = ZipUtil.loadUrlZipUploadFile(ZIP_PREFIX, urlList);
            Map<String, Object> zipFileMeta = ZipUtil.calculateZipFileMD5(zipPath);
            String zipFileUrl = ZipUtil.uploadZipFile(zipPath, FILE_BUCKET_PATH);

            JSONObject jsonObject = JSON.parseObject(giftData.getZipInfo());
            if (jsonObject == null) {
                jsonObject = new JSONObject();
            }

            jsonObject.put("url", zipFileUrl);
            jsonObject.put("md5", zipFileMeta.get("fileMd5"));
            giftData.setZipSize((Long) zipFileMeta.getOrDefault("fileSize", 0));
            giftData.setZipInfo(jsonObject.toJSONString());
            giftDao.updateOne(giftData);
        }
    }


    public void addGiftData(String uid, GiftDTO dto) {
        if (StringUtils.isEmpty(dto.getGicon())) {
            throw new CommonH5Exception(new HttpCode(1, "礼物图标不能为空"));
        }
        if (dto.getPrice() <= 0) {
            throw new CommonH5Exception(HttpCode.PARAM_ERROR.getCode(), "礼物价格不正确");
        }

        if (dto.getGatype() <= 0) {
            throw new CommonH5Exception(HttpCode.PARAM_ERROR.getCode(), "礼物播放类型不正确");
        }

        String internKey = uid + dto.getGname();
        synchronized (stringPool.intern(internKey)) {
            GiftData data = new GiftData();
            GiftData lastData = giftDao.selectLastIdOne();

            BeanUtils.copyProperties(dto, data, "giftNumOptions");
            if (data.getStarGift() == 1) {
                dto.setGpTypeList(Collections.singletonList(3));
            }
            if (!CollectionUtils.isEmpty(dto.getGiftNumOptions())) {
                data.setGiftNumOptions(dto.getGiftNumOptions().stream().map(String::valueOf).collect(Collectors.joining(",")));
            }
            if (!CollectionUtils.isEmpty(dto.getGpTypeList())) {
                data.setPanelType(dto.getGpTypeList().stream().map(o -> "'" + o + "'").collect(Collectors.joining(",")));
            }
            if (!CollectionUtils.isEmpty(dto.getShowAreaList())) {
                data.setShowArea(dto.getShowAreaList().stream().map(o -> "'" + o + "'").collect(Collectors.joining(",")));
            }
            data.setRid(lastData == null ? 0 : lastData.getRid() + 1);
            data.setCtime(new Timestamp(System.currentTimeMillis()));
            JSONObject zipInfo = new JSONObject();

            if (dto.getGptype() == 5) {
                zipInfo.put("level", dto.getLevel());
            }
            if (!ObjectUtils.isEmpty(dto.getGpTypeList()) && dto.getGpTypeList().contains(8)) {
                zipInfo.put("level", dto.getLevel());
            }
            zipInfo.put("hot", dto.getHot());
            data.setZipInfo(zipInfo.toJSONString());
            String ownerUid = "";
            Integer ownerRid = null;
            if (data.getIsFusionAnimation() == GiftDao.FUSION_MODE_3 || (data.getIsFusionAnimation() == 0 && StringUtils.hasLength(data.getFusionId()))) {
                ActorData actorData = actorDao.getActorByRidOrAlphaRidFromDb(data.getFusionId());
                if (actorData == null) {
                    throw new CommonException(HttpCode.PARAM_ERROR.getCode(), "融合礼物指定用户ID有误");
                }
                data.setFusionId(actorData.getUid());
                ownerUid = actorData.getUid();
                ownerRid = actorData.getRid();
            }

            if (data.getIsFusionAnimation() == GiftDao.FUSION_MODE_4) {
                FamilyData familyData = familyDao.selectByFamilyRid(Integer.valueOf(data.getFusionId()));
                if (familyData == null) {
                    throw new CommonException(HttpCode.PARAM_ERROR.getCode(), "融合礼物指定家族ID有误");
                }
                data.setFusionId(String.valueOf(familyData.getId()));
                ownerUid = familyData.getOwnerUid();
            }
            giftDao.insertOne(data);
            if (data.getStarGift() == 1 && StringUtils.hasLength(ownerUid)) {
                doReportEvent(data.getRid(), ownerUid, ownerRid, data.getGname(), data.getIsFusionAnimation());
            }
            List<Map<String, String>> urlList = convertAddZip(dto);
            if (!urlList.isEmpty()) {
                BaseTaskFactory.getFactory().addSlow(new Task() {
                    @Override
                    protected void execute() {
                        asyncUpdateSource(data, urlList);
                    }
                });
            }

        }
    }


    public void updateGiftData(GiftDTO dto) {

        if (dto.getPrice() <= 0) {
            throw new CommonH5Exception(HttpCode.PARAM_ERROR.getCode(), "礼物价格不正确");
        }

        if (dto.getGatype() <= 0) {
            throw new CommonH5Exception(HttpCode.PARAM_ERROR.getCode(), "礼物播放类型不正确");
        }

        GiftData data = giftDao.selectOne(dto.getRid());
        if (data == null) {
            throw new CommonH5Exception(HttpCode.PARAM_ERROR);
        }
        String oldFusionId = data.getFusionId();
        List<Map<String, String>> urlList = convertUpdateZip(dto, data);
        BeanUtils.copyProperties(dto, data, "giftNumOptions");
        if (data.getStarGift() == 1) {
            dto.setGpTypeList(Collections.singletonList(3));
        }
        if (!CollectionUtils.isEmpty(dto.getGiftNumOptions())) {
            data.setGiftNumOptions(dto.getGiftNumOptions().stream().map(String::valueOf).collect(Collectors.joining(",")));
        }
        if (!CollectionUtils.isEmpty(dto.getGpTypeList())) {
            data.setPanelType(dto.getGpTypeList().stream().map(o -> "'" + o + "'").collect(Collectors.joining(",")));
        }
        if (!CollectionUtils.isEmpty(dto.getShowAreaList())) {
            data.setShowArea(dto.getShowAreaList().stream().map(o -> "'" + o + "'").collect(Collectors.joining(",")));
        }
        JSONObject zipInfo = JSON.parseObject(data.getZipInfo());

        if (dto.getGptype() == 5) {
            zipInfo.put("level", dto.getLevel());
        }
        if (!ObjectUtils.isEmpty(dto.getGpTypeList()) && dto.getGpTypeList().contains(8)) {
            zipInfo.put("level", dto.getLevel());
        }
        zipInfo.put("hot", dto.getHot());
        data.setZipInfo(zipInfo.toJSONString());
        String ownerUid = "";
        Integer ownerRid = null;
        boolean updateStarGiftOwner = false;
        if (dto.getIsFusionAnimation() == GiftDao.FUSION_MODE_3 || (data.getIsFusionAnimation() == 0 && StringUtils.hasLength(data.getFusionId()))) {
            ActorData actorData = actorDao.getActorByRidOrAlphaRidFromDb(dto.getFusionId());
            if (actorData == null) {
                throw new CommonException(HttpCode.PARAM_ERROR.getCode(), "融合礼物指定用户ID有误");
            }
            if (!actorData.getUid().equals(oldFusionId)) {
                updateStarGiftOwner = true;
            }
            data.setFusionId(actorData.getUid());
            ownerUid = actorData.getUid();
            ownerRid = actorData.getRid();
        }

        if (dto.getIsFusionAnimation() == GiftDao.FUSION_MODE_4) {
            FamilyData familyData = familyDao.selectByFamilyRid(Integer.valueOf(dto.getFusionId()));
            if (familyData == null) {
                throw new CommonException(HttpCode.PARAM_ERROR.getCode(), "融合礼物指定家族ID有误");
            }
            if (!String.valueOf(familyData.getId()).equals(oldFusionId)) {
                updateStarGiftOwner = true;
            }
            data.setFusionId(String.valueOf(familyData.getId()));
            ownerUid = familyData.getOwnerUid();
        }
        giftDao.updateOne(data);
        if (data.getStarGift() == 1 && updateStarGiftOwner && StringUtils.hasLength(ownerUid)) {
            doReportEvent(data.getRid(), ownerUid, ownerRid, data.getGname(), data.getIsFusionAnimation());
        }
        if (!urlList.isEmpty()) {
            BaseTaskFactory.getFactory().addSlow(new Task() {
                @Override
                protected void execute() {
                    asyncUpdateSource(data, urlList);
                }
            });
        }
    }

    private void doReportEvent(int giftId, String ownerUid, Integer ownerRid, String giftName, int fusionMode) {
        StarGiftGrantRecordEvent event = new StarGiftGrantRecordEvent();
        event.setUid(ownerUid);
        event.setRid(ownerRid != null ? ownerRid : actorDao.getActorDataFromCache(ownerUid).getRid());
        event.setGift_id(giftId);
        event.setGift_name(giftName);
        event.setGift_class(fusionMode == GiftDao.FUSION_MODE_4 ? 2 : 1);
        event.setCtime(DateHelper.getNowSeconds());
        eventReport.track(new EventDTO(event));
    }


    // 盲盒礼物配置
    public PageResultVO<BlindBoxVO> blindBoxList(BaseCondition condition) {
        PageResultVO<BlindBoxVO> pageVO = new PageResultVO<>();
        int page = condition.getPage();
        int pageSize = condition.getPageSize();
        int status = condition.getStatus();

        IPage<GiftData> pageGift = giftDao.selectBlindBoxPageList(status, page, pageSize);

        List<BlindBoxVO> voList = new ArrayList<>();
        for (GiftData data : pageGift.getRecords()) {
            BlindBoxVO vo = new BlindBoxVO();
            BeanUtils.copyProperties(data, vo);

            long ctime = data.getCtime().getTime();
            vo.setPrice(WalletUtils.diamondsForDisplay(vo.getPrice()));
            vo.setCtime(ctime / 1000L);
            vo.setZipInfoVO(JSONObject.parseObject(data.getZipInfo(), BlindBoxVO.ZipInfoVO.class));
            voList.add(vo);
        }

        pageVO.setList(voList);
        pageVO.setTotal(pageGift.getTotal());
        return pageVO;
    }

    public void blindBoxAdd(BlindBoxDTO dto) {

        if (StringUtils.isEmpty(dto.getRid())) {
            throw new CommonH5Exception(HttpCode.PARAM_ERROR);
        }

        GiftData giftData = giftDao.selectOne(dto.getRid());
        BlindBoxDTO.ZipInfoVO zipInfoVO = dto.getZipInfoVO();

        if (giftData == null || zipInfoVO == null) {
            throw new CommonH5Exception(HttpCode.PARAM_ERROR);
        }

        List<BlindBoxDTO.RandomGift> randomGiftList = zipInfoVO.getRandomGiftList();
        if (randomGiftList == null || randomGiftList.isEmpty()) {
            throw new CommonH5Exception(HttpCode.PARAM_ERROR);
        }

        double totalProb = 0;
        for (BlindBoxDTO.RandomGift randomGift : randomGiftList) {
            totalProb += randomGift.getProb();
            int giftId = randomGift.getGiftId();

            GiftData tempGiftData = giftDao.getGiftFromDb(giftId);


            if (tempGiftData == null) {
                logger.error("giftId not find error: {}", randomGift.getGiftId());
                throw new CommonH5Exception(HttpCode.PARAM_ERROR);
            }

            GiftDTO giftDTO = new GiftDTO();
            BeanUtils.copyProperties(tempGiftData, giftDTO);
            giftDTO.setHot(1);
            giftDTO.setInsertExtraGift(true);
            this.updateGiftData(giftDTO);
            giftDao.addGiftIdInBlindBox(giftId);
        }

        if (totalProb != 100.0) {
            logger.error("totalProb error: {}", totalProb);
            throw new CommonH5Exception(HttpCode.PARAM_ERROR);
        }

        JSONObject jsonObject = JSONObject.parseObject(giftData.getZipInfo());

        jsonObject.put("ztype", 7);
        jsonObject.put("showDetail", 2);
        jsonObject.put("descUrl", zipInfoVO.getDescUrl());
        jsonObject.put("desc", zipInfoVO.getDesc());
        jsonObject.put("descAr", zipInfoVO.getDescAr());
        jsonObject.put("propIcon", zipInfoVO.getPropIcon());
        jsonObject.put("webType", zipInfoVO.getWebType());
        jsonObject.put("width", zipInfoVO.getWidth());
        jsonObject.put("height", zipInfoVO.getHeight());
        jsonObject.put("randomPoolSize", zipInfoVO.getRandomPoolSize());
        jsonObject.put("randomGiftList", zipInfoVO.getRandomGiftList());
        giftData.setZipInfo(JSON.toJSONString(jsonObject));
        giftData.setBlindBox(1);
        giftDao.updateOne(giftData);
    }


    public void blindBoxDelete(BlindBoxDTO dto) {

        if (StringUtils.isEmpty(dto.getRid())) {
            throw new CommonH5Exception(HttpCode.PARAM_ERROR);
        }
        GiftData giftData = giftDao.selectOne(dto.getRid());

        JSONObject jsonObject = JSONObject.parseObject(giftData.getZipInfo());
        List<BlindBoxDTO.RandomGift> randomGiftList = jsonObject.getJSONArray("randomGiftList").toJavaList(BlindBoxDTO.RandomGift.class);
        if (randomGiftList != null && !randomGiftList.isEmpty()) {
            for (BlindBoxDTO.RandomGift randomGift : randomGiftList) {
                int giftRid = randomGift.getGiftId();
                GiftData tempGiftData = giftDao.getGiftFromCache(giftRid);
                GiftDTO giftDTO = new GiftDTO();
                BeanUtils.copyProperties(tempGiftData, giftDTO);
                giftDTO.setHot(0);
                this.updateGiftData(giftDTO);
            }
        }

        JSONObject commonZipInfo = new JSONObject();
        commonZipInfo.put("url", jsonObject.getOrDefault("url", ""));
        commonZipInfo.put("md5", jsonObject.getOrDefault("md5", ""));
        giftData.setZipInfo(JSON.toJSONString(commonZipInfo));
        giftData.setBlindBox(0);
        giftDao.updateOne(giftData);
    }


    public PageResultVO<GiftSelectListVO> selectList(GiftCondition condition) {
        PageResultVO<GiftSelectListVO> result = new PageResultVO<>();
        int page = condition.getPage() > 0 ? condition.getPage() : 1;
        int pageSize = condition.getPageSize() > 0 ? condition.getPageSize() : 20;
        String search = condition.getSearch();
        IPage<GiftData> iPage;
        try {
            int rid = Integer.parseInt(search);
            iPage = giftDao.selectPageList("", rid, page, pageSize);
        } catch (Exception e) {
            iPage = giftDao.selectPageList(search, null, page, pageSize);
        }
        List<GiftSelectListVO> list = new ArrayList<>();
        for (GiftData giftData : iPage.getRecords()) {
            GiftSelectListVO vo = new GiftSelectListVO();
            vo.setRid(giftData.getRid());
            vo.setName(giftData.getGname());
            vo.setNameAr(giftData.getGnamear());
            vo.setIcon(giftData.getGicon());
            list.add(vo);
        }
        result.setList(list);
        result.setTotal(iPage.getTotal());
        return result;
    }

    public PageVO<GiftPanelVO> panelShow(int showType, int gpType) {
        List<GiftData> dataList = giftDao.getGiftPanelData(showType, gpType);
        List<GiftPanelVO> list = new ArrayList<>();
        if (!CollectionUtils.isEmpty(dataList)) {
            List<GiftSortData> giftSortList = giftSortDao.selectList(showType, gpType);
            Map<Integer, Integer> orderMap = Collections.emptyMap();
            if (!CollectionUtils.isEmpty(giftSortList)) {
                orderMap = giftSortList.stream().collect(Collectors.toMap(GiftSortData::getGiftId, GiftSortData::getPanelOrder));
            }
            Map<Integer, Integer> finalOrderMap = orderMap;
            Comparator<GiftData> orderAsc = Comparator.comparingInt(o -> finalOrderMap.getOrDefault(o.getRid(), 1000));
            dataList.sort(orderAsc.thenComparing(GiftData::getRid));
            int order = 0;
            for (GiftData data : dataList) {
                order++;
                list.add(new GiftPanelVO(data.getRid(), data.getGicon(), data.getGname(), data.getGtype(), data.getPrice(), order));
            }
        }
        return new PageVO<>(list);
    }

    public void updatePanelOrder(GiftPanelDTO dto) {
        List<GiftSortData> giftSortList = giftSortDao.selectList(dto.getShowType(), dto.getGpType());
        Map<Integer, GiftSortData> giftSortMap = CollectionUtil.listToKeyMap(giftSortList, GiftSortData::getGiftId);
        if (!CollectionUtils.isEmpty(dto.getGiftList())) {
            for (GiftPanelDTO.Gift gift : dto.getGiftList()) {
                GiftSortData giftSortData = giftSortMap.get(gift.getGiftId());
                if (giftSortData == null) {
                    giftSortData = new GiftSortData();
                    giftSortData.setShowType(dto.getShowType());
                    giftSortData.setPanelType(dto.getGpType());
                    giftSortData.setGiftId(gift.getGiftId());
                    giftSortData.setPanelOrder(gift.getPanelOrder());
                    giftSortDao.insert(giftSortData);
                } else {
                    giftSortDao.update(dto.getShowType(), dto.getGpType(), gift.getGiftId(), gift.getPanelOrder());
                }
            }
        }
    }

    @Override
    public PageResultVO<ResourceVO> getGoodsList(ResourceCondition condition) {
        PageResultVO<ResourceVO> pageVO = new PageResultVO<>();

        GiftCondition giftCondition = new GiftCondition();
        BeanUtils.copyProperties(condition, giftCondition);
        IPage<GiftData> pageGift = giftDao.selectPageList(giftCondition);
        List<ResourceVO> voList = new ArrayList<>();
        for (GiftData data : pageGift.getRecords()) {
            ResourceVO vo = new ResourceVO();
            vo.setResourceNameEn(data.getGname());
            vo.setResourceNameAr(data.getGnamear());
            vo.setResourceIcon(data.getGicon());
            vo.setResourceId(data.getRid());
            vo.setResourcePrice(data.getPrice());
            vo.setResourceType(condition.getResourceType());
            voList.add(vo);
        }

        pageVO.setList(voList);
        pageVO.setTotal(pageGift.getTotal());
        return pageVO;
    }
}
