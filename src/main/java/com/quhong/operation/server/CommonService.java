package com.quhong.operation.server;

import com.quhong.config.CaffeineCacheConfig;
import com.quhong.mysql.dao.CountriesDao;
import com.quhong.mysql.data.CountriesData;
import com.quhong.operation.share.vo.CountriesVO;
import com.quhong.vo.PageVO;
import jakarta.annotation.Resource;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2024/5/7
 */
@Service
public class CommonService {

    private final Logger logger = LoggerFactory.getLogger(getClass());

    @Resource
    private CountriesDao countriesDao;

    @Cacheable(value = "cache", cacheManager = CaffeineCacheConfig.EXPIRE_10M_AFTER_WRITE, key = "#root.method.name+T(org.springframework.util.StringUtils).arrayToDelimitedString(args,'-')")
    public PageVO<CountriesVO> countryList() {
        List<CountriesData> countriesList = countriesDao.getCountriesList();
        List<CountriesVO> list = new ArrayList<>();
        if (!CollectionUtils.isEmpty(countriesList)) {
            countriesList.forEach(k -> list.add(new CountriesVO(k.getCode(), k.getName())));
        }
        return new PageVO<>(list);
    }
}
