package com.quhong.operation.server;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.google.common.collect.Interner;
import com.google.common.collect.Interners;
import com.quhong.api.UserService;
import com.quhong.core.distribution.DistributeLock;
import com.quhong.core.utils.DateHelper;
import com.quhong.data.ActorData;
import com.quhong.data.dto.ResourcesDTO;
import com.quhong.enums.BaseDataResourcesConstant;
import com.quhong.enums.HttpCode;
import com.quhong.enums.VipNameEnum;
import com.quhong.exception.CommonH5Exception;
import com.quhong.mongo.dao.*;
import com.quhong.mongo.data.*;
import com.quhong.mq.MqSenderService;
import com.quhong.mysql.dao.*;
import com.quhong.mysql.data.*;
import com.quhong.operation.share.condition.ItemCondition;
import com.quhong.operation.share.condition.ResourceCondition;
import com.quhong.operation.share.condition.UserResCondition;
import com.quhong.operation.share.dto.MallDiscountConfigDTO;
import com.quhong.operation.share.dto.ResourceConfigDTO;
import com.quhong.operation.share.dto.UserResDTO;
import com.quhong.operation.share.vo.PageResultVO;
import com.quhong.operation.share.vo.ResourceConfigVO;
import com.quhong.operation.share.vo.ResourceVO;
import com.quhong.operation.share.vo.UserResRecordVO;
import com.quhong.operation.utils.StringUtil;
import com.quhong.operation.utils.ZipUtil;
import com.quhong.redis.GoodsListHomeRedis;
import com.quhong.utils.RoomUtils;
import com.quhong.utils.WalletUtils;
import jakarta.annotation.Resource;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.BeanUtils;
import org.springframework.data.domain.Sort;
import org.springframework.data.mongodb.core.query.Criteria;
import org.springframework.data.mongodb.core.query.Query;
import org.springframework.data.mongodb.core.query.Update;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import java.util.*;
import java.util.concurrent.TimeUnit;

/**
 * <AUTHOR>
 * @date 2023/4/24
 */
@Service
public class ResourceConfigService implements ResourceService {

    private static final Logger logger = LoggerFactory.getLogger(ResourceConfigService.class);

    private final static String FILE_PATH = "resource/";
    private final static String ZIP_PREFIX = "resource_";

    private final static List<Integer> VIP_VALID_DAY_LIST = List.of(1, 3, 7, 10, 15, 30);

    private final static int ITEM_TYPE_STORE_BUY = 5;

    private static final Interner<String> STRING_POOL = Interners.newWeakInterner();

    @Resource
    private ResourceConfigDao resourceConfigDao;
    @Resource
    private UserResourceDao userResourceDao;
    @Resource
    private GoodsListHomeRedis goodsListHomeRedis;
    @Resource
    private GiftDao giftDao;
    @Resource
    private MicFrameSourceDao micFrameSourceDao;
    @Resource
    private JoinSourceDao joinSourceDao;
    @Resource
    private BuddleSourceDao buddleSourceDao;
    @Resource
    private FloatScreenSourceDao floatScreenSourceDao;
    @Resource
    private BadgeListDao badgeListDao;
    @Resource
    private RippleSourceDao rippleSourceDao;
    @Resource
    private MongoThemeDao mongoThemeDao;
    @Resource
    private UserService userService;
    @Resource
    private MqSenderService mqSenderService;
    @Resource
    private ActorDao actorDao;
    @Resource
    private CommonDao commonDao;
    @Resource
    private MineBackgroundDao mineBackgroundDao;
    @Resource
    private VipInfoDao vipInfoDao;
    @Resource
    private MongoRoomDao roomDao;
    @Resource
    private MallDiscountConfigDao mallDiscountConfigDao;

    /**
     * 分页查询资源列表
     */
    public PageResultVO<ResourceConfigVO> getSourceList(ItemCondition condition){
        PageResultVO<ResourceConfigVO> pageVO = new PageResultVO<>();
        int page = condition.getPage() == null ? 1 : condition.getPage();
        int pageSize = condition.getPageSize() == null ? 30 : condition.getPageSize();
        int start = (page - 1) * pageSize;
        List<ResourceConfigData> sourceList = resourceConfigDao.selectResourcePage(condition.getResType(), condition.getItemType(), condition.getStatus(), condition.getSearch(), start, pageSize);
        List<ResourceConfigVO> voList = new ArrayList<>();
        for(ResourceConfigData data: sourceList){
            ResourceConfigVO vo = new ResourceConfigVO();
            BeanUtils.copyProperties(data, vo);
            vo.setDocId(data.get_id().toString());
            vo.setPrice(WalletUtils.diamondsForDisplay(data.getPrice()));
            voList.add(vo);
        }
        pageVO.setList(voList);
        pageVO.setTotal(resourceConfigDao.selectResourceCount(condition.getResType(), condition.getItemType(), condition.getStatus(), condition.getSearch()));
        return pageVO;
    }

    public void addResourceData(String uid, ResourceConfigDTO dto){
        if(StringUtils.isEmpty(dto.getResourceId())){
            throw new CommonH5Exception(HttpCode.PARAM_ERROR);
        }
        if (dto.getResourceType() == BaseDataResourcesConstant.TYPE_ENTRY_EFFECT) {
            if (StringUtils.isEmpty(dto.getAnimationUrl())) {
                throw new CommonH5Exception(HttpCode.PARAM_ERROR);
            }
        }
        String internKey = uid + dto.getName();
        synchronized (STRING_POOL.intern(internKey)) {
            ResourceConfigData data = new ResourceConfigData();
            BeanUtils.copyProperties(dto, data);
            ResourceConfigData lastData = resourceConfigDao.getLastResourceData();
            int nextId = lastData != null ? lastData.getResourceId() + 1 : 1;
            data.setResourceId(nextId);
            data.setCtime(DateHelper.getNowSeconds());
            data.setResourceMd5("");
            if (dto.getResourceType() == BaseDataResourcesConstant.TYPE_ENTRY_EFFECT) {
                data.setResourceUrl(data.getAnimationUrl());
                data.setResourceUrlAr(data.getAnimationUrlAr());
            } else {
                data.setResourceUrl(dto.getResourceUrl());
                data.setResourceUrlAr(dto.getResourceUrlAr());
            }
            resourceConfigDao.save(data);
            if(data.getItemType() == ITEM_TYPE_STORE_BUY && data.getIsNew() == 1 && data.getStatus() == 1){
                goodsListHomeRedis.addNewGoodsRankingScore(data.getResourceType(), nextId);
            }
        }
    }

    public void updateResourceData(ResourceConfigDTO dto) {
        ResourceConfigData data = resourceConfigDao.getResourceDataById(dto.getDocId());
        if(data == null){
            throw new CommonH5Exception(HttpCode.PARAM_ERROR);
        }
        if(data.getItemType() != dto.getItemType()){
            throw new CommonH5Exception(HttpCode.PARAM_ERROR.getCode(), "已上传的资源不能修改类型");
        }
        Update update = new Update();
        update.set("name", dto.getName() != null ? dto.getName() : "");
        update.set("nameAr", dto.getNameAr() != null ? dto.getNameAr() : "");
        update.set("icon", dto.getIcon() != null ? dto.getIcon() : "");
        update.set("iconAr", dto.getIconAr() != null ? dto.getIconAr() : "");
        update.set("desc", dto.getDesc() != null ? dto.getDesc() : "");
        update.set("descAr", dto.getDescAr() != null ? dto.getDescAr() : "");
        update.set("itemType", dto.getItemType());
        update.set("status", dto.getStatus());
        update.set("animationType", dto.getAnimationType());
        update.set("currencyType", dto.getCurrencyType());
        update.set("price", dto.getPrice());
        update.set("days", dto.getDays());
        update.set("order", dto.getOrder());
        update.set("isNew", dto.getIsNew());
        update.set("fontColor", dto.getFontColor());
        update.set("resourceUrl", dto.getResourceUrl());
        update.set("resourceUrlAr", dto.getResourceUrlAr());
        if(!StringUtil.isEmpty(dto.getAnimationUrl()) && !dto.getAnimationUrl().equals(data.getAnimationUrl())){
            update.set("animationUrl", dto.getAnimationUrl());
            if (dto.getResourceType() == BaseDataResourcesConstant.TYPE_ENTRY_EFFECT) {
                update.set("resourceUrl", dto.getAnimationUrl());
            }
        }
        if(!StringUtil.isEmpty(dto.getAnimationUrlAr()) && !dto.getAnimationUrlAr().equals(data.getAnimationUrlAr())){
            update.set("animationUrlAr", dto.getAnimationUrlAr());
            if (dto.getResourceType() == BaseDataResourcesConstant.TYPE_ENTRY_EFFECT) {
                update.set("resourceUrlAr", dto.getAnimationUrlAr());
            }
        }
        if(!StringUtil.isEmpty(dto.getVideoUrl()) && !dto.getVideoUrl().equals(data.getVideoUrl())){
            update.set("videoUrl", dto.getVideoUrl());
        }
        resourceConfigDao.updateData(dto.getDocId(), update);
        // List<Map<String, String>> urlList = convertUpdateZip(dto, data);
        // if (!urlList.isEmpty()) {
        //     BaseTaskFactory.getFactory().addSlow(new Task() {
        //         @Override
        //         protected void execute() {
        //             asyncUpdateSource(data.getResourceId(), data.getResourceType(), urlList);
        //         }
        //     });
        // }
        if(data.getItemType() == ITEM_TYPE_STORE_BUY){
            if(dto.getIsNew() == 1 && dto.getStatus() == 1){
                goodsListHomeRedis.addNewGoodsRankingScore(data.getResourceType(), data.getResourceId());
            }
            if(dto.getIsNew() == 0 || dto.getStatus() == 0){
                goodsListHomeRedis.deleteItemNewGoodsRanking(data.getResourceType(), data.getResourceId());
            }
            if(dto.getStatus() == 0){
                goodsListHomeRedis.deleteItemNewGoodsRanking(data.getResourceType(), data.getResourceId());
                goodsListHomeRedis.deleteItemHotGoodsRanking(data.getResourceType(), data.getResourceId());
            }
        }
    }

    public void removeResourceData(ResourceConfigDTO dto) {
        ResourceConfigData data = resourceConfigDao.getResourceDataById(dto.getDocId());
        if(data == null){
            throw new CommonH5Exception(HttpCode.PARAM_ERROR);
        }
        if(data.getStatus() != 0){
            throw new CommonH5Exception(HttpCode.PARAM_ERROR.getCode(), "有效的资源无法直接移除，请先修改成无效");
        }
        resourceConfigDao.deleteData(data);
    }

    private void asyncUpdateSource(int resId, int resType, List<Map<String, String>> urlList){
        ResourceConfigData resource = resourceConfigDao.getResourceDataFromDb(resId, resType);
        if(resource == null){
            return;
        }
        String zipPath = ZipUtil.loadUrlZipUploadFile(ZIP_PREFIX, urlList);
        Map<String, Object> zipFileMeta = ZipUtil.calculateZipFileMD5(zipPath);
        String zipFileUrl = ZipUtil.uploadZipFile(zipPath, FILE_PATH);
        Update update = new Update();
        update.set("resourceUrl", zipFileUrl);
        update.set("resourceMd5", zipFileMeta.get("fileMd5"));
        resourceConfigDao.updateData(resource.get_id().toString(), update);
    }

    private List<Map<String, String>> convertAddZip(ResourceConfigDTO dto){
        List<Map<String, String>> urlList = new ArrayList<>();
        if (!StringUtils.isEmpty(dto.getAnimationUrl())) {
            Map<String, String> actMap = new HashMap<>(2);
            actMap.put("fileName", "act");
            actMap.put("fileUrl", dto.getAnimationUrl());
            urlList.add(actMap);
        }
        if(!StringUtils.isEmpty(dto.getVideoUrl())){
            Map<String, String> videoMap = new HashMap<>(2);
            videoMap.put("fileName", "voice");
            videoMap.put("fileUrl", dto.getVideoUrl());
            urlList.add(videoMap);
        }
        return urlList;
    }

    private List<Map<String, String>> convertUpdateZip(ResourceConfigDTO dto, ResourceConfigData data){
        List<Map<String, String>> urlList = new ArrayList<>();
        if((!StringUtils.isEmpty(dto.getAnimationUrl()) && !Objects.equals(dto.getAnimationUrl(), data.getAnimationUrl()))){
            Map<String, String> actMap = new HashMap<>(2);
            actMap.put("fileName", "act");
            actMap.put("fileUrl", dto.getAnimationUrl());
            urlList.add(actMap);
            if(!StringUtils.isEmpty(dto.getVideoUrl())) {
                Map<String, String> videoMap = new HashMap<>(2);
                videoMap.put("fileName", "voice");
                videoMap.put("fileUrl", dto.getVideoUrl());
                urlList.add(videoMap);
            }
        }
        return urlList;
    }

    public String getIcon(int resourceType, int resourceId, Integer wealthLevel) {
        String icon = "";
        switch (resourceType) {
            case BaseDataResourcesConstant.TYPE_BADGE:
                BadgeListData badgeData = badgeListDao.findData(resourceId);
                icon = badgeData != null ? badgeData.getIcon() : "";
                break;
            case BaseDataResourcesConstant.TYPE_MIC:
                MicFrameSourceData micFrameData = micFrameSourceDao.findData(resourceId);
                icon = micFrameData != null ? micFrameData.getMic_icon() : "";
                break;
            case BaseDataResourcesConstant.TYPE_RIDE:
                JoinSourceData joinData = joinSourceDao.findSourceData(resourceId);
                icon = joinData != null ? joinData.getJoin_icon() : "";
                break;
            case BaseDataResourcesConstant.TYPE_BAG_GIFT:
                GiftData bagGiftData = giftDao.getBagGiftOne(resourceId);
                icon = bagGiftData != null ? bagGiftData.getGicon() : "";
                break;
            case BaseDataResourcesConstant.TYPE_BUDDLE:
                BuddleSourceData bubbleData = buddleSourceDao.findData(resourceId);
                icon = bubbleData != null ? bubbleData.getBuddle_icon() : "";
                break;
            case BaseDataResourcesConstant.TYPE_RIPPLE:
                RippleSourceData rippleData = rippleSourceDao.findData(resourceId);
                icon = rippleData != null ? rippleData.getRipple_icon() : "";
                break;
            case BaseDataResourcesConstant.TYPE_FLOAT_SCREEN:
                FloatScreenSourceData screenData = floatScreenSourceDao.findData(resourceId);
                icon = screenData != null ? screenData.getScreen_icon() : "";
                break;
            case BaseDataResourcesConstant.TYPE_MINE_BACKGROUND:
                MongoThemeData themeData = mongoThemeDao.findData(resourceId);
                icon = themeData != null ? themeData.getPreview() : "";
                break;
            case BaseDataResourcesConstant.TYPE_ENTRY_EFFECT:
            case BaseDataResourcesConstant.TYPE_HONOR_TITLE:
            case BaseDataResourcesConstant.TYPE_TICKET:
                ResourceConfigData resourceData = resourceConfigDao.getResourceDataFromDb(resourceId, resourceType);
                icon = resourceData != null ? resourceData.getIcon() : "";
                break;
            case 100:
                // 金币
                icon = "https://cloudcdn.waho.live/resource/op_sys_1701328621_icon.png";
                break;
            case 101:
                // 财富等级
                icon = wealthLevel == null ? "" : userService.getBadgeIconByWealthLevel(wealthLevel);
                break;
            case 102:
                // vip
                icon = VipNameEnum.getIconEnByCode(resourceId);
                break;
            case 777:
                // 虚拟钻石
                icon = "https://cloudcdn.waho.live/resource/op_sys_1717056984_virtual_diamonds.png";
                break;
            case 888:
                icon = "https://cloudcdn.waho.live/resource/op_sys_1715064024_9999charm.png";
                break;
            case 999:
                // 钻石
                icon = "https://cloudcdn.waho.live/resource/op_sys_1715061693_4.png";
                break;
            default:
                break;
        }
        return icon;
    }

    public void sendReward(UserResDTO dto) {
        checkParam(dto);
        checkResExist(dto.getResType(), dto.getResId());
        String[] rids = dto.getStrRid().trim().replace("，", ",").split(",");
        StringBuilder errorMsgSb = new StringBuilder();
        for (String strRid : rids) {
            ActorData actorData = actorDao.getActorByRidOrAlphaRid(strRid);
            if (null == actorData) {
                errorMsgSb.append("rid:").append(strRid).append("找不到对应的用户 \n");
            }
        }
        String errorMsg = errorMsgSb.toString();
        if (StringUtils.hasLength(errorMsg)) {
            throw new CommonH5Exception(new HttpCode(1, errorMsg));
        }
        int actionType = dto.getResType() == BaseDataResourcesConstant.TYPE_HONOR_TITLE ? 2 : 1;
        for (String strRid : rids) {
            ActorData actorData = actorDao.getActorByRidOrAlphaRid(strRid);
            if (null == actorData) {
                throw new CommonH5Exception(new HttpCode(1, "rid:" +strRid + "找不到对应的用户"));
            }
            mqSenderService.asyncHandleResources(getResourcesDTO(actorData.getUid(), dto.getResId(), dto.getResType(), dto.getNum(), dto.getDays(), actionType));
        }
    }

    private void checkResExist(int resType, int resId) {
        switch (resType) {
            case BaseDataResourcesConstant.TYPE_BADGE:
                BadgeListData badgeData = badgeListDao.findData(resId);
                if (null == badgeData || badgeData.getValid() != 1) {
                    throw new CommonH5Exception(new HttpCode(1, "该资源不存在或者无效"));
                }
                break;
            case BaseDataResourcesConstant.TYPE_MIC:
                MicFrameSourceData micFrameData = micFrameSourceDao.findData(resId);
                if (null == micFrameData || micFrameData.getStatus() != 1) {
                    throw new CommonH5Exception(new HttpCode(1, "该资源不存在或者无效"));
                }
                break;
            case BaseDataResourcesConstant.TYPE_RIDE:
                JoinSourceData joinData = joinSourceDao.findSourceData(resId);
                if (null == joinData || joinData.getStatus() != 1) {
                    throw new CommonH5Exception(new HttpCode(1, "该资源不存在或者无效"));
                }
                break;
            case BaseDataResourcesConstant.TYPE_BAG_GIFT:
                GiftData bagGift = giftDao.getBagGiftOne(resId);
                if (bagGift == null) {
                    throw new CommonH5Exception(new HttpCode(1, "礼物要先设置成背包礼物才能下发"));
                }
                break;
            case BaseDataResourcesConstant.TYPE_BUDDLE:
                BuddleSourceData bubbleData = buddleSourceDao.findData(resId);
                if (null == bubbleData || bubbleData.getStatus() != 1) {
                    throw new CommonH5Exception(new HttpCode(1, "该资源不存在或者无效"));
                }
                break;
            case BaseDataResourcesConstant.TYPE_RIPPLE:
                RippleSourceData rippleData = rippleSourceDao.findData(resId);
                if (null == rippleData || rippleData.getStatus() != 1) {
                    throw new CommonH5Exception(new HttpCode(1, "该资源不存在或者无效"));
                }
                break;
            case BaseDataResourcesConstant.TYPE_FLOAT_SCREEN:
                FloatScreenSourceData screenData = floatScreenSourceDao.findData(resId);
                if (null == screenData || screenData.getStatus() != 1) {
                    throw new CommonH5Exception(new HttpCode(1, "该资源不存在或者无效"));
                }
                break;
            case BaseDataResourcesConstant.TYPE_MINE_BACKGROUND:
                MongoThemeData themeData = mongoThemeDao.findData(resId);
                if (null == themeData || themeData.getStatus() != 1) {
                    throw new CommonH5Exception(new HttpCode(1, "该资源不存在或者无效"));
                }
                break;
            case BaseDataResourcesConstant.TYPE_ENTRY_EFFECT:
            case BaseDataResourcesConstant.TYPE_HONOR_TITLE:
            case BaseDataResourcesConstant.TYPE_TICKET:
                ResourceConfigData resourceData = resourceConfigDao.getResourceDataFromDb(resId, resType);
                if (null == resourceData || resourceData.getStatus() != 1) {
                    throw new CommonH5Exception(new HttpCode(1, "该资源不存在或者无效"));
                }
                break;
            case 102:
                break;
            default:{
                throw new CommonH5Exception(HttpCode.PARAM_ERROR);
            }
        }
    }

    private static ResourcesDTO getResourcesDTO(String aid, int resId, int resType, Integer num, Integer days, int actionType) {
        ResourcesDTO dto = new ResourcesDTO();
        dto.setUid(aid);
        dto.setResId(String.valueOf(resId));
        dto.setResType(resType);
        dto.setLockType(0);
        if (BaseDataResourcesConstant.TYPE_BAG_GIFT == resType || BaseDataResourcesConstant.TYPE_TICKET == resType) {
            dto.setNum(num);
            dto.setDays(days);
        } else if (BaseDataResourcesConstant.TYPE_VIP_LEVEL == resType) {
            dto.setNum(1);
            dto.setDays(days);
            dto.setResLevel(resId);
            dto.setLockType(2);
        } else {
            dto.setNum(1);
            dto.setDays(days);
        }
        if (BaseDataResourcesConstant.TYPE_MINE_BACKGROUND == resType) {
            dto.setRoomId(RoomUtils.formatRoomId(aid));
        }
        dto.setGetWay(BaseDataResourcesConstant.TYPE_OPERATION_SET_GET);
        dto.setActionType(actionType);
        dto.setmTime(DateHelper.getNowSeconds());
        dto.setItemsSourceDetail("operation send");
        // 自然天过期
        dto.setGainType(0);
        return dto;
    }

    private void checkParam(UserResDTO dto) {
        if (!StringUtils.hasLength(dto.getStrRid())) {
            throw new CommonH5Exception(new HttpCode(1, "rid不能为空"));
        }
        if (dto.getResType() == null || dto.getResType() == 0) {
            throw new CommonH5Exception(new HttpCode(1, "资源类型不能为空"));
        }
        if (dto.getResType() == BaseDataResourcesConstant.TYPE_BAG_GIFT || dto.getResType() == BaseDataResourcesConstant.TYPE_TICKET) {
            if (dto.getNum() < 0) {
                throw new CommonH5Exception(new HttpCode(1, "数量不能为负数"));
            }
            if (dto.getResId() == null || dto.getResId() == 0) {
                throw new CommonH5Exception(new HttpCode(1, "资源id不能为空"));
            }
        } else if (dto.getResType() == BaseDataResourcesConstant.TYPE_BADGE) {
            if (dto.getResId() == null || dto.getResId() == 0) {
                throw new CommonH5Exception(new HttpCode(1, "资源id不能为空"));
            }
            if (dto.getDays() == null || dto.getDays() < -1 || dto.getDays() == 0) {
                throw new CommonH5Exception(new HttpCode(1, "勋章奖励有效天数无效"));
            }
        } else if (dto.getResType() == 102) {
            if (dto.getResId() == null) {
                throw new CommonH5Exception(new HttpCode(1, "vip等级不能为空"));
            }
            if (dto.getResId() < 1 || dto.getResId() > 6) {
                throw new CommonH5Exception(new HttpCode(1, "vip等级只能是1~6"));
            }
            if (dto.getDays() == null || !VIP_VALID_DAY_LIST.contains(dto.getDays())) {
                throw new CommonH5Exception(new HttpCode(1, "vip奖励有效天数无效"));
            }
        } else {
            if (dto.getResId() == null || dto.getResId() == 0) {
                throw new CommonH5Exception(new HttpCode(1, "资源id不能为空"));
            }
        }
    }

    public void removeReward(UserResDTO dto) {
        checkParam(dto);
        if(StringUtils.isEmpty(dto.getAid())){
            throw new CommonH5Exception(HttpCode.PARAM_ERROR);
        }
        mqSenderService.asyncHandleResources(getResourcesDTO(dto.getAid(), dto.getResId(), dto.getResType(), 0, 0, 4));
    }

    public PageResultVO<UserResRecordVO> userResRecord(UserResCondition condition) {
        ActorData actorData = null;
        String aid = "";
        if (StringUtils.hasLength(condition.getStrRid())) {
            actorData = actorDao.getActorByRidOrAlphaRid(condition.getStrRid());
            aid = actorData != null ? actorData.getUid() : "---";
        }

        int page = condition.getPage() != null ? Math.max(1, condition.getPage()) : 1;
        int pageSize = condition.getPageSize() != null ? Math.max(10, condition.getPageSize()) : 10;
        int start = (page - 1) * pageSize;
        PageResultVO<UserResRecordVO> resultVO = new PageResultVO<>();
        List<UserResRecordVO> list = new ArrayList<>();
        long total = 0;
        switch (condition.getResType()) {
            case BaseDataResourcesConstant.TYPE_BADGE -> {
                for (BadgeData userResData : selectList(aid, "badge_id", condition.getResId(), start, pageSize, BadgeData.class)) {
                    UserResRecordVO vo = new UserResRecordVO();
                    fillUserInfo(vo, userResData.getUid(), actorData);
                    BadgeListData resData = selectOne("badge_id", userResData.getBadge_id(), BadgeListData.class);
                    vo.setResId(userResData.getBadge_id());
                    vo.setResIcon(resData != null ? resData.getIcon() : "");
                    vo.setResName(resData != null ? resData.getName() : "");
                    vo.setWearStatus(userResData.getStatus());
                    vo.setCtime(userResData.getGet_time());
                    vo.setValidDay(getStrValidDay(userResData.getGet_time(), userResData.getEnd_time()));
                    vo.setLeftTime(getStrLeftTime(userResData.getEnd_time()));
                    list.add(vo);
                }
                total = selectCount(aid, "badge_id", condition.getResId(), BadgeData.class);
            }
            case BaseDataResourcesConstant.TYPE_MIC -> {
                for (ExtraMicFrameData userResData : selectList(aid, "mic_id", condition.getResId(), start, pageSize, ExtraMicFrameData.class)) {
                    UserResRecordVO vo = new UserResRecordVO();
                    fillUserInfo(vo, userResData.getUid(), actorData);
                    MicFrameSourceData resData = selectOne("mic_id", userResData.getMic_id(), MicFrameSourceData.class);
                    vo.setResId(userResData.getMic_id());
                    vo.setResIcon(resData != null ? resData.getMic_icon() : "");
                    vo.setResName(resData != null ? resData.getName() : "");
                    vo.setWearStatus(userResData.getStatus());
                    vo.setCtime((int) userResData.getC_time());
                    vo.setValidDay(getStrValidDay(userResData.getC_time(), userResData.getEnd_time()));
                    vo.setLeftTime(getStrLeftTime(userResData.getEnd_time()));
                    list.add(vo);
                }
                total = selectCount(aid, "mic_id", condition.getResId(), ExtraMicFrameData.class);
            }
            case BaseDataResourcesConstant.TYPE_RIDE -> {
                for (JoinCartonData userResData : selectList(aid, "join_carton_id", condition.getResId(), start, pageSize, JoinCartonData.class)) {
                    UserResRecordVO vo = new UserResRecordVO();
                    fillUserInfo(vo, userResData.getUid(), actorData);
                    JoinSourceData resData = selectOne("join_carton_id", userResData.getJoin_carton_id(), JoinSourceData.class);
                    vo.setResId(userResData.getJoin_carton_id());
                    vo.setResIcon(resData != null ? resData.getJoin_icon() : "");
                    vo.setResName(resData != null ? resData.getName() : "");
                    vo.setWearStatus(userResData.getStatus());
                    vo.setCtime((int) userResData.getC_time());
                    vo.setValidDay(getStrValidDay(userResData.getC_time(), userResData.getEnd_time()));
                    vo.setLeftTime(getStrLeftTime(userResData.getEnd_time()));
                    list.add(vo);
                }
                total = selectCount(aid, "join_carton_id", condition.getResId(), JoinCartonData.class);
            }
            case BaseDataResourcesConstant.TYPE_BAG_GIFT -> {
                for (GiftBagData userResData : selectList(aid, "gift_id", condition.getResId(), start, pageSize, GiftBagData.class)) {
                    UserResRecordVO vo = new UserResRecordVO();
                    fillUserInfo(vo, userResData.getUid(), actorData);
                    GiftData resData = giftDao.selectOne(userResData.getGift_id());
                    vo.setResId(userResData.getGift_id());
                    vo.setResIcon(resData != null ? resData.getGicon() : "");
                    vo.setResName(resData != null ? resData.getGname() : "");
                    vo.setWearStatus(0);
                    vo.setCtime(userResData.getGet_time());
                    vo.setValidDay(getStrValidDay(userResData.getGet_time(), userResData.getEnd_time()));
                    vo.setLeftTime(getStrLeftTime(userResData.getEnd_time()));
                    list.add(vo);
                }
                total = selectCount(aid, "gift_id", condition.getResId(), GiftBagData.class);
            }
            case BaseDataResourcesConstant.TYPE_BUDDLE -> {
                for (BubbleData userResData : selectList(aid, "buddle_id", condition.getResId(), start, pageSize, BubbleData.class)) {
                    UserResRecordVO vo = new UserResRecordVO();
                    fillUserInfo(vo, userResData.getUid(), actorData);
                    BuddleSourceData resData = selectOne("buddle_id", userResData.getBuddle_id(), BuddleSourceData.class);
                    vo.setResId(userResData.getBuddle_id());
                    vo.setResIcon(resData != null ? resData.getBuddle_icon() : "");
                    vo.setResName(resData != null ? resData.getName() : "");
                    vo.setWearStatus(userResData.getStatus());
                    vo.setCtime((int) userResData.getC_time());
                    vo.setValidDay(getStrValidDay(userResData.getC_time(), userResData.getEnd_time()));
                    vo.setLeftTime(getStrLeftTime(userResData.getEnd_time()));
                    list.add(vo);
                }
                total = selectCount(aid, "buddle_id", condition.getResId(), BubbleData.class);
            }
            case BaseDataResourcesConstant.TYPE_RIPPLE -> {
                for (RippleData userResData : selectList(aid, "ripple_id", condition.getResId(), start, pageSize, RippleData.class)) {
                    UserResRecordVO vo = new UserResRecordVO();
                    fillUserInfo(vo, userResData.getUid(), actorData);
                    RippleSourceData resData = selectOne("ripple_id", userResData.getRipple_id(), RippleSourceData.class);
                    vo.setResId(userResData.getRipple_id());
                    vo.setResIcon(resData != null ? resData.getRipple_icon() : "");
                    vo.setResName(resData != null ? resData.getName() : "");
                    vo.setWearStatus(userResData.getStatus());
                    vo.setCtime((int) userResData.getC_time());
                    vo.setValidDay(getStrValidDay(userResData.getC_time(), userResData.getEnd_time()));
                    vo.setLeftTime(getStrLeftTime(userResData.getEnd_time()));
                    list.add(vo);
                }
                total = selectCount(aid, "ripple_id", condition.getResId(), RippleData.class);
            }
            case BaseDataResourcesConstant.TYPE_FLOAT_SCREEN -> {
                for (FloatScreenData userResData : selectList(aid, "screen_id", condition.getResId(), start, pageSize, FloatScreenData.class)) {
                    UserResRecordVO vo = new UserResRecordVO();
                    fillUserInfo(vo, userResData.getUid(), actorData);
                    FloatScreenSourceData resData = selectOne("screen_id", userResData.getScreen_id(), FloatScreenSourceData.class);
                    vo.setResId(userResData.getScreen_id());
                    vo.setResIcon(resData != null ? resData.getScreen_icon() : "");
                    vo.setResName(resData != null ? resData.getName() : "");
                    vo.setWearStatus(userResData.getStatus());
                    vo.setCtime((int) userResData.getC_time());
                    vo.setValidDay(getStrValidDay(userResData.getC_time(), userResData.getEnd_time()));
                    vo.setLeftTime(getStrLeftTime(userResData.getEnd_time()));
                    list.add(vo);
                }
                total = selectCount(aid, "screen_id", condition.getResId(), FloatScreenData.class);
            }
            case BaseDataResourcesConstant.TYPE_MINE_BACKGROUND -> {
                IPage<MineBackgroundData> iPage = mineBackgroundDao.selectPageByUidAndBgId(aid, condition.getResId(), page, pageSize);
                for (MineBackgroundData userResData : iPage.getRecords()) {
                    UserResRecordVO vo = new UserResRecordVO();
                    fillUserInfo(vo, userResData.getUid(), actorData);
                    MongoThemeData resData = selectOne("tid", userResData.getBgId(), MongoThemeData.class);
                    vo.setResId(userResData.getBgId());
                    vo.setResIcon(resData != null ? resData.getPreview() : "");
                    vo.setResName(resData != null ? resData.getTitle() : "");
                    MongoRoomData roomData = roomDao.getDataFromCache(userResData.getRoomId());
                    vo.setWearStatus(roomData != null && roomData.getTheme() == userResData.getBgId() ? 1 : 0);
                    vo.setCtime(userResData.getCtime());
                    vo.setValidDay(getStrValidDay(userResData.getCtime(), userResData.getEndTime()));
                    vo.setLeftTime(getStrLeftTime(userResData.getEndTime()));
                    list.add(vo);
                }
                total = iPage.getTotal();
            }
            case BaseDataResourcesConstant.TYPE_ENTRY_EFFECT, BaseDataResourcesConstant.TYPE_HONOR_TITLE, BaseDataResourcesConstant.TYPE_TICKET -> {
                List<UserResourceData> userResDataList = userResourceDao.selectList(aid, condition.getResType(), condition.getResId(), start, pageSize);
                for (UserResourceData userResData : userResDataList) {
                    UserResRecordVO vo = new UserResRecordVO();
                    fillUserInfo(vo, userResData.getUid(), actorData);
                    ResourceConfigData resData = selectOne("resourceId", userResData.getResourceId(), ResourceConfigData.class);
                    vo.setResId(userResData.getResourceId());
                    vo.setResIcon(resData != null ? resData.getIcon() : "");
                    vo.setResName(resData != null ? resData.getName() : "");
                    vo.setWearStatus(userResData.getStatus());
                    vo.setCtime(userResData.getCtime());
                    if (condition.getResType() == BaseDataResourcesConstant.TYPE_TICKET) {
                        vo.setResNum(userResData.getResourceNumber());
                    }
                    vo.setValidDay(getStrValidDay(userResData.getCtime(), userResData.getEndTime()));
                    vo.setLeftTime(getStrLeftTime(userResData.getEndTime()));
                    list.add(vo);
                }
                total = userResourceDao.selectCount(aid, condition.getResType(), condition.getResId());
            }
            case BaseDataResourcesConstant.TYPE_VIP_LEVEL -> {
                List<VipInfoData> vipInfoDataList = vipInfoDao.selectList(aid, condition.getResId(), start, pageSize);
                for (VipInfoData userResData : vipInfoDataList) {
                    UserResRecordVO vo = new UserResRecordVO();
                    fillUserInfo(vo, userResData.getUid(), actorData);
                    vo.setResId(userResData.getVipLevel());
                    vo.setResIcon(VipNameEnum.getIconEnByCode(userResData.getVipLevel()));
                    vo.setResName(VipNameEnum.getNameEnByCode(userResData.getVipLevel()));
                    vo.setWearStatus(1);
                    vo.setCtime((int)(userResData.getVipBuyTime().getTime() / 1000));
                    vo.setValidDay(getStrValidDay(userResData.getVipBuyTime().getTime() / 1000, userResData.getVipEndTime().getTime() / 1000));
                    vo.setLeftTime(getStrLeftTime(userResData.getVipEndTime().getTime() / 1000));
                    list.add(vo);
                }
                total = vipInfoDao.selectCount(aid, condition.getResId());
            }
            default -> {

            }
        }
        resultVO.setList(list);
        resultVO.setTotal(total);
        return resultVO;
    }

    private String getStrValidDay(long starTime, long endTime) {
        if (endTime == Integer.MAX_VALUE) {
            return "永久";
        }
        return (endTime - starTime) / TimeUnit.DAYS.toSeconds(1) + "天";
    }

    private String getStrLeftTime(long endTime) {
        if (endTime == Integer.MAX_VALUE) {
            return "永久";
        }
        return (endTime - DateHelper.getNowSeconds()) / TimeUnit.DAYS.toSeconds(1) + "天";
    }

    private void fillUserInfo(UserResRecordVO vo, String uid, ActorData actorData) {
        if (actorData == null || !uid.equals(actorData.getUid())) {
            actorData = actorDao.getActorDataFromCache(uid);
            if (actorData == null) {
                return;
            }
        }
        vo.setUid(actorData.getUid());
        vo.setRid(actorData.getRid());
        vo.setStrRid(actorData.getShowRid());
        vo.setGender(actorData.getFb_gender());
    }

    public <T> T selectOne(String resIdField, Integer resId, Class<T> entityClass) {
        try {
            Criteria criteria = Criteria.where(resIdField).is(resId);
            return commonDao.findOne(new Query(criteria), entityClass);
        } catch (Exception e) {
            logger.error("selectOne error. resIdField={} resId={} class={} {} ", resIdField, resId, entityClass.getName(), e.getMessage(), e);
            return null;
        }
    }

    public <T> List<T> selectList(String uid, String resIdField, Integer resId, int start, int pageSize, Class<T> entityClass) {
        try {
            Query query = new Query(buildCriteria(uid, resIdField, resId));
            Sort sort = Sort.by(Sort.Direction.DESC, "_id");
            query.with(sort);
            query.skip(start).limit(pageSize);
            List<T> list = commonDao.findList(query, entityClass);
            return !CollectionUtils.isEmpty(list) ? list : Collections.emptyList();
        } catch (Exception e) {
            logger.error("selectList error. uid={} resIdField={} resId={} start={} pageSize={} class={} {} ", uid, resIdField, resId, start, pageSize, entityClass.getName(), e.getMessage(), e);
            return Collections.emptyList();
        }
    }

    public <T> int selectCount(String uid, String resIdField, Integer resId, Class<T> entityClass) {
        try {
            return commonDao.findCount(new Query(buildCriteria(uid, resIdField, resId)), entityClass);
        } catch (Exception e) {
            logger.error("selectCount error. uid={} resIdField={} resId={} class={} {} ", uid, resIdField, resId, entityClass.getName(), e.getMessage(), e);
            return 0;
        }
    }

    private Criteria buildCriteria(String uid, String resIdField, Integer resId) {
        Criteria criteria = new Criteria();
        if (StringUtils.hasLength(uid)) {
            criteria = criteria.and("uid").is(uid);
        }
        if (resId != null && resId > 0) {
            criteria = criteria.and(resIdField).is(resId);
        }
        return criteria;
    }

    @Override
    public PageResultVO<ResourceVO> getGoodsList(ResourceCondition condition) {
        PageResultVO<ResourceVO> pageVO = new PageResultVO<>();
        int page = condition.getPage();
        int pageSize = condition.getPageSize();
        int start = (page - 1) * pageSize;
        List<ResourceConfigData> sourceList = resourceConfigDao.selectResourcePage(condition.getResourceType(), -1, condition.getStatus(), condition.getSearch(), start, pageSize);
        List<ResourceVO> voList = new ArrayList<>();
        for(ResourceConfigData data: sourceList){
            if(data.getItemType() == 5){
                continue;
            }
            ResourceVO vo = new ResourceVO();
            vo.setResourceId(data.getResourceId());
            vo.setResourceIcon(data.getIcon());
            vo.setResourceNameEn(data.getName());
            vo.setResourceNameAr(data.getNameAr());
            vo.setResourcePrice(data.getPrice());
            vo.setResourceType(condition.getResourceType());
            voList.add(vo);
        }
        pageVO.setList(voList);
        pageVO.setTotal(resourceConfigDao.selectResourceCount(condition.getResourceType(), -1, condition.getStatus(), condition.getSearch()));
        return pageVO;
    }

    public void saveMallDiscountConfig(MallDiscountConfigDTO dto) {
        if (!StringUtils.hasLength(dto.getStartTime()) || !StringUtils.hasLength(dto.getEndTime())) {
            throw new CommonH5Exception(new HttpCode(1, "折扣时间不能为空"));
        }
        if (dto.getDiscount() <= 0 || dto.getDiscount() > 100) {
            throw new CommonH5Exception(new HttpCode(1, "折扣只能是1~100"));
        }
        int startTime = DateHelper.ARABIAN.stringTimeToStampSecond(dto.getStartTime());
        int endTime = DateHelper.ARABIAN.stringTimeToStampSecond(dto.getEndTime());
        if (startTime >= endTime) {
            throw new CommonH5Exception(new HttpCode(1, "折扣开始时间不能大于结束时间"));
        }
        try (DistributeLock lock = new DistributeLock("saveMallDiscountConfig")) {
            lock.lock();
            if (isTimeConflict(startTime, endTime, dto.getId())) {
                throw new CommonH5Exception(new HttpCode(1, "折扣时间与正在进行中的折扣时间重合，无法创建，请另选时间范围。"));
            }
            int nowSeconds = DateHelper.getNowSeconds();
            if (StringUtils.hasLength(dto.getId())) {
                MallDiscountConfigData data = mallDiscountConfigDao.findById(dto.getId());
                if (data == null) {
                    throw new CommonH5Exception(HttpCode.PARAM_ERROR);
                }
                data.setDiscount(dto.getDiscount());
                data.setStatus(dto.getStatus());
                data.setStartTime(startTime);
                data.setEndTime(endTime);
                data.setMtime(nowSeconds);
                mallDiscountConfigDao.save(data);
            } else {
                MallDiscountConfigData data = new MallDiscountConfigData();
                data.setDiscount(dto.getDiscount());
                data.setStatus(dto.getStatus());
                data.setStartTime(startTime);
                data.setEndTime(endTime);
                data.setMtime(nowSeconds);
                data.setCtime(nowSeconds);
                mallDiscountConfigDao.save(data);
            }
        }
    }

    /**
     * 校验要保存的配置生效时间是否与已存在的配置生效时间重叠
     * 是否冲突：true是 false否
     */
    private boolean isTimeConflict(int startTime, int endTime, String id) {
        List<MallDiscountConfigData> configDataList = mallDiscountConfigDao.selectAll();
        if (CollectionUtils.isEmpty(configDataList)) {
            return false;
        }
        for (MallDiscountConfigData configData : configDataList) {
            if (StringUtils.hasLength(id) && id.equals(configData.get_id().toString())) {
                continue;
            }
            if (startTime > configData.getStartTime() && startTime < configData.getEndTime()) {
                return true;
            }
            if (endTime > configData.getStartTime() && endTime < configData.getEndTime()) {
                return true;
            }
            if (startTime >= configData.getStartTime() && endTime <= configData.getEndTime()) {
                return true;
            }
            if (startTime <= configData.getStartTime() && endTime >= configData.getEndTime()) {
                return true;
            }
        }
        return false;
    }

    public PageResultVO<MallDiscountConfigDTO> mallDiscountConfigList(MallDiscountConfigDTO dto) {
        Integer startTime = null;
        if (StringUtils.hasLength(dto.getStartTime())) {
            startTime = DateHelper.ARABIAN.stringTimeToStampSecond(dto.getStartTime());
        }
        Integer endTime = null;
        if (StringUtils.hasLength(dto.getEndTime())) {
            endTime = DateHelper.ARABIAN.stringTimeToStampSecond(dto.getEndTime());
        }
        int count = mallDiscountConfigDao.selectCount(startTime, endTime, dto.getStatus());
        if (count == 0) {
            return new PageResultVO<>(count, Collections.emptyList());
        }
        int page = dto.getPage() == null ? 1 : dto.getPage();
        int pageSize = dto.getPageSize() == null ? 10 : dto.getPageSize();
        int start = (page - 1) * pageSize;
        List<MallDiscountConfigData> dataList = mallDiscountConfigDao.selectList(startTime, endTime, dto.getStatus(), start, pageSize);
        List<MallDiscountConfigDTO> list = new ArrayList<>();
        for (MallDiscountConfigData data : dataList) {
            MallDiscountConfigDTO vo = new MallDiscountConfigDTO();
            vo.setId(data.get_id().toString());
            vo.setDiscount(data.getDiscount());
            vo.setStatus(data.getStatus());
            vo.setStartTime(DateHelper.ARABIAN.formatDateTime(new Date(data.getStartTime() * 1000L)));
            vo.setEndTime(DateHelper.ARABIAN.formatDateTime(new Date(data.getEndTime() * 1000L)));
            list.add(vo);
        }
        return new PageResultVO<>(count, list);
    }
}
