package com.quhong.operation.server;

import com.quhong.enums.HttpCode;
import com.quhong.exception.CommonH5Exception;
import com.quhong.mongo.dao.MonitorCostConfigDao;
import com.quhong.mongo.data.MonitorCostConfigData;
import com.quhong.operation.share.condition.MonitorWarnCondition;
import com.quhong.operation.share.vo.MonitorCostVO;
import com.quhong.operation.share.vo.PageResultVO;
import jakarta.annotation.Resource;
import org.springframework.beans.BeanUtils;
import org.springframework.data.mongodb.core.query.Update;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import java.util.ArrayList;
import java.util.List;

@Service
public class MonitorCostService {

    @Resource
    private MonitorCostConfigDao monitorCostConfigDao;

    public PageResultVO<MonitorCostVO> getDataList(MonitorWarnCondition condition) {
        PageResultVO<MonitorCostVO> pageVO = new PageResultVO<>();
        String search = condition.getSearch();
        int status = condition.getStatus();
        int warnType = condition.getWarnType();
        int page = condition.getPage();
        int pageSize = condition.getPageSize();
        int start = (page - 1) * pageSize;

        List<MonitorCostConfigData> dataList = monitorCostConfigDao.selectPage(status, search, warnType, start, pageSize);
        List<MonitorCostVO> list = new ArrayList<>();
        for (MonitorCostConfigData data : dataList) {
            MonitorCostVO vo = new MonitorCostVO();
            BeanUtils.copyProperties(data, vo);
            vo.setDocId(data.get_id().toString());
            list.add(vo);
        }
        pageVO.setList(list);
        pageVO.setTotal(monitorCostConfigDao.selectCount(status, search));
        return pageVO;
    }

    private void paramCheck(MonitorCostVO dto) {
        if(!StringUtils.hasLength(dto.getWarnName()) || !StringUtils.hasLength(dto.getBeanTitle())){
            throw new CommonH5Exception(HttpCode.PARAM_ERROR.getCode(), "告警标题或钻石标题不能为空");
        }

        if(CollectionUtils.isEmpty(dto.getTypeList())){
            throw new CommonH5Exception(HttpCode.PARAM_ERROR.getCode(), "钻石类型不能为空");
        }
        for (Integer beanType : dto.getTypeList()){
            if(beanType == null || beanType <= 0){
                throw new CommonH5Exception(HttpCode.PARAM_ERROR.getCode(), "告警钻石类型配置有误");
            }
        }
        if (!StringUtils.hasLength(dto.getActivityId())) {
            throw new CommonH5Exception(HttpCode.PARAM_ERROR.getCode(), "活动id不能为空");
        }
        if (dto.getMaxLimit() < 0) {
            throw new CommonH5Exception(HttpCode.PARAM_ERROR.getCode(), "总支出告警额不能小于0");
        }
        if (dto.getMinLimit() > 0) {
            throw new CommonH5Exception(HttpCode.PARAM_ERROR.getCode(), "总收入告警额不能大于0");
        }
    }

    public void addData(MonitorCostVO dto) {
        paramCheck(dto);
        monitorCostConfigDao.save(dto);
    }

    public void updateData(MonitorCostVO dto) {
        String docId = dto.getDocId();
        MonitorCostConfigData data = monitorCostConfigDao.getDataByID(docId);
        if (data == null) {
            throw new CommonH5Exception(HttpCode.PARAM_ERROR);
        }
        paramCheck(dto);
        Update update = new Update();
        update.set("warnName", dto.getWarnName());
        update.set("beanTitle", dto.getBeanTitle());
        update.set("activityId", dto.getActivityId());
        update.set("warnType", dto.getWarnType());
        update.set("typeList", dto.getTypeList());
        update.set("maxLimit", dto.getMaxLimit());
        update.set("minLimit", dto.getMinLimit());
        update.set("status", dto.getStatus());
        monitorCostConfigDao.updateData(data, update);
    }

    public void deleteData(MonitorCostVO dto) {
        monitorCostConfigDao.delete(dto.getDocId());
    }
}
