package com.quhong.operation.server;

import com.quhong.core.distribution.DistributeLock;
import com.quhong.core.utils.DateHelper;
import com.quhong.data.ActorData;
import com.quhong.data.condition.OfficialPushCondition;
import com.quhong.enums.ClientOS;
import com.quhong.enums.HttpCode;
import com.quhong.enums.SLangType;
import com.quhong.enums.UserLevelConstant;
import com.quhong.exception.CommonH5Exception;
import com.quhong.mongo.dao.*;
import com.quhong.mongo.data.MongoActorData;
import com.quhong.mongo.data.NoticeNewData;
import com.quhong.mongo.data.OfficialData;
import com.quhong.mongo.data.OfficialPushData;
import com.quhong.msg.chat.OfficialPushMsg;
import com.quhong.mysql.dao.*;
import com.quhong.mysql.data.CoinSellerData;
import com.quhong.mysql.data.FamilyData;
import com.quhong.mysql.data.FamilyMemberData;
import com.quhong.mysql.data.WithdrawalTraderData;
import com.quhong.operation.share.dto.OfficialPushDTO;
import com.quhong.operation.share.vo.PageResultVO;
import com.quhong.redis.UserOnlineRedis;
import com.quhong.redis.WhitelistRedis;
import com.quhong.room.RoomWebSender;
import com.quhong.service.redis.RechargeRedis;
import com.quhong.utils.AsyncUtils;
import com.quhong.utils.K8sUtils;
import com.quhong.utils.RoomUtils;
import jakarta.annotation.Resource;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.concurrent.TimeUnit;

/**
 * <AUTHOR>
 * @date 2023/11/22
 */
@Service
public class OfficialPushService {

    private final Logger logger = LoggerFactory.getLogger(getClass());

    @Resource
    private OfficialPushDao officialPushDao;
    @Resource
    private ActorDao actorDao;
    @Resource
    private MongoRoomDao roomDao;
    @Resource
    private OfficialDao officialDao;
    @Resource
    private NoticeNewDao noticeNewDao;
    @Resource
    private RoomWebSender roomWebSender;
    @Resource
    private RechargeRedis rechargeRedis;
    @Resource
    private DAUDao dauDao;
    @Resource
    private FamilyDao familyDao;
    @Resource
    private FamilyMemberDao familyMemberDao;
    @Resource
    private CoinSellerDao coinSellerDao;
    @Resource
    private RechargeDailyInfoDao rechargeDailyInfoDao;
    @Resource
    private UserOnlineRedis userOnlineRedis;
    @Resource
    private WhitelistRedis whitelistRedis;
    @Resource
    private UserLevelDao userLevelDao;
    @Resource
    private WelcomePackLogDao welcomePackLogDao;
    @Resource
    private AuditAccountDao auditAccountDao;
    @Resource
    private WithdrawalTraderDao withdrawalTraderDao;
    @Resource
    private K8sUtils k8sUtils;

    public void pushOfficialMsg() {
        List<OfficialPushData> list = officialPushDao.selectNotPushedList();
        if (CollectionUtils.isEmpty(list)) {
            return;
        }
        for (OfficialPushData data : list) {
            long timeMillis = System.currentTimeMillis();
            try (DistributeLock lock = new DistributeLock("officialPush_" + data.get_id().toString())) {
                lock.lock();
                data = officialPushDao.selectById(data.get_id().toString());
                if (data.getFinish() != 0) {
                    return;
                }
                if (data.getStart_time() > DateHelper.getNowSeconds() || data.getEnd_time() < DateHelper.getNowSeconds()) {
                    return;
                }
                data.setFinish(1);
                officialPushDao.save(data);
            }
            switch (data.getPush_user()) {
                case 0 -> pushMsgPayUser(data); // 付费用户(限过去30天日活用户)
                case 1 -> pushMsgNotPayUser(data); // 非付费用户(仅限过去30天日活用户)
                case 2 -> pushMsgFamilyMember(data); // 主播
                case 3 -> pushMsgNewRegisterUser(data, 0); // 当天注册用户
                case 4 -> pushMsgNewRegisterUser(data, 6); // 最近7天注册用户
                case 5 -> pushMsgActiveUser(data, 7); // 过去7天日活用户
                case 6 -> pushMsgActiveUser(data, 30); // 过去30天日活用户
                case 7 -> pushMsgSpecifyUser(data); // 给指定ID推送
                case 8 -> pushMsgFamilyOwner(data); // 公会长
                case 9 -> pushMsgFamilyManager(data);  // 公会管理员
                case 10 -> pushMsgCoinSeller(data); // 币商
                case 11 -> pushMsgRechargeUsdUser(data); // 充值大于50美金用户
                case 12 -> pushMsgOnlineUser(data); // 在线用户
                case 13 -> pushMsgBcGameUser(data); // bc游戏用户
                case 14 -> pushMsgWithdrawalTrader(data); // 推送给提现币商
                default -> {
                }
            }
            logger.info("push official end. officialPushId={} cost={}", data.get_id().toString(), System.currentTimeMillis() - timeMillis);
        }
    }

    public void saveConfig(OfficialPushDTO dto) {
        String roomId;
        if (dto.getAtype() == 99) {
            // 指定房间
            String ownerUid = "";
            try {
                ActorData owner = actorDao.getActorByRid(Integer.parseInt(dto.getRoom_rid()));
                ownerUid = owner != null ? owner.getUid() : "";
            } catch (NumberFormatException ignore) {
            }
            if (!StringUtils.hasLength(ownerUid)) {
                throw new CommonH5Exception(new HttpCode(1, "room_rid is error"));
            }
            roomId = "r:" + ownerUid;
            if (roomDao.findData(roomId) == null) {
                throw new CommonH5Exception(new HttpCode(1, "room_rid is error"));
            }
        }
        if (dto.getEnd_time() - dto.getStart_time() < TimeUnit.MINUTES.toSeconds(2)) {
            throw new CommonH5Exception(new HttpCode(1, "开始和结束时间的间隔不能小于2分钟"));
        }
        OfficialPushData data;
        if (StringUtils.hasLength(dto.getOfficial_id())) {
            data = officialPushDao.selectById(dto.getOfficial_id());
        } else {
            data = new OfficialPushData();
        }
        BeanUtils.copyProperties(dto, data);
        officialPushDao.save(data);
    }

    public PageResultVO<OfficialPushDTO> selectList(OfficialPushCondition condition) {
        List<OfficialPushData> dataList = officialPushDao.selectPage(condition);
        List<OfficialPushDTO> list = new ArrayList<>();
        if (!CollectionUtils.isEmpty(dataList)) {
            for (OfficialPushData data : dataList) {
                OfficialPushDTO dto = new OfficialPushDTO();
                BeanUtils.copyProperties(data, dto);
                dto.setOfficial_id(data.get_id().toString());
                if (data.getAtype() == 99 && StringUtils.hasLength(data.getRoom_id())) {
                    String aid = RoomUtils.getRoomHostId(data.getRoom_id());
                    ActorData actorData = actorDao.getActorDataFromCache(aid);
                    dto.setRoom_rid(actorData != null ? actorData.getRid() + "" : "");
                }
                dto.setMsg_user(officialDao.getPushUserCount(dto.getOfficial_id()));
                list.add(dto);
            }
        }
        PageResultVO<OfficialPushDTO> resultVO = new PageResultVO<>();
        resultVO.setTotal(officialPushDao.selectCount(condition));
        resultVO.setList(list);
        return resultVO;
    }

    public void remove(String officialId) {
        OfficialPushData data = officialPushDao.selectById(officialId);
        if (data == null) {
            throw new CommonH5Exception(HttpCode.PARAM_ERROR);
        }
        AsyncUtils.execute(() -> {
            String pushId = data.get_id().toString();
            List<OfficialData> officialDataList = officialDao.selectListByPushId(pushId);
            if (!CollectionUtils.isEmpty(officialDataList)) {
                for (OfficialData officialData : officialDataList) {
                    noticeNewDao.deleteByOfficialId(officialData.get_id().toString());
                }
                officialDao.deleteByPushId(pushId);
            }
        });
        officialPushDao.remove(data);
    }

    private void pushMsgOnlineUser(OfficialPushData data) {
        Set<String> allOnlineSet = userOnlineRedis.getAllUserOnline();
        for (String aid : allOnlineSet) {
            ActorData actorData = actorDao.getActorDataFromCache(aid);
            if (actorData == null) {
                logger.error("can not find actor data. uid={}", aid);
                continue;
            }
            pushOfficialMsg(data, actorData.getUid(), actorData.getSlang());
        }
    }

    private void pushMsgBcGameUser(OfficialPushData data) {
        Set<String> activeUserSet = dauDao.getActiveUserSet(30, 20000);
        if (CollectionUtils.isEmpty(activeUserSet)) {
            logger.info("activeUserSet is empty.");
            return;
        }
        Set<String> whitelistSet = whitelistRedis.getWhitelistSet(WhitelistRedis.BC_GAME_ENTRANCE);
        Set<String> rechargeUserSet = rechargeRedis.getAllRechargeUserFromCache();
        Set<String> receivedPackUidSet = welcomePackLogDao.getReceivedUidSet();
        Set<String> auditAccountSet = auditAccountDao.getAuditAccountSet();
        for (String uid : activeUserSet) {
            ActorData actorData = actorDao.getActorDataFromCache(uid);
            if (actorData == null) {
                logger.error("can not find actor data . uid={}", uid);
                continue;
            }
            if (canSeeGamblingGame(uid, actorData.getIntOs(), whitelistSet, rechargeUserSet, receivedPackUidSet, auditAccountSet)) {
                pushOfficialMsg(data, actorData.getUid(), actorData.getSlang());
            }
        }
    }

    public boolean canSeeGamblingGame(String uid, int os, Set<String> whitelistSet, Set<String> rechargeUserSet, Set<String> receivedPackUidSet, Set<String> auditAccountSet) {
        // 白名单用户可见
        if (whitelistSet.contains(uid)) {
            return true;
        }
        // 审核人员不可见
        if (os == ClientOS.IOS && auditAccountSet.contains(uid)) {
            return false;
        }
        // 接收欢迎礼包用户可见
        if (receivedPackUidSet.contains(uid)) {
            return true;
        }
        // 充值用户可见
        if (rechargeUserSet.contains(uid)) {
            return true;
        }
        // 财富等级≥20级可见
        return userLevelDao.getUserLevel(uid, UserLevelConstant.WEALTH_LEVEL) >= 20;
    }

    private void pushMsgSpecifyUser(OfficialPushData data) {
        if (StringUtils.hasLength(data.getUser_rid())) {
            String userRidStr = data.getUser_rid().replace(" ", "").replace("，", ",");
            for (String strRid : userRidStr.split(",")) {
                ActorData actorData = actorDao.getActorByRidOrAlphaRid(strRid);
                if (actorData == null) {
                    logger.error("can not find actor data. strRid={}", strRid);
                    continue;
                }
                pushOfficialMsg(data, actorData.getUid(), actorData.getSlang());
            }
        }
    }

    private void pushMsgNewRegisterUser(OfficialPushData data, int day) {
        List<MongoActorData> newRegisterUserList = actorDao.getNewRegisterUserList(day);
        if (CollectionUtils.isEmpty(newRegisterUserList)) {
            for (MongoActorData actorData : newRegisterUserList) {
                pushOfficialMsg(data, actorData.get_id().toString(), actorData.getSlang());
            }
        }
    }

    private void pushMsgRechargeUsdUser(OfficialPushData data) {
        List<String> rechargeUserList = rechargeDailyInfoDao.getRechargeUserList(45000);
        if (!CollectionUtils.isEmpty(rechargeUserList)) {
            for (String aid : rechargeUserList) {
                ActorData actorData = actorDao.getActorDataFromCache(aid);
                if (actorData == null) {
                    logger.error("can not find actor data. uid={}", aid);
                    continue;
                }
                pushOfficialMsg(data, actorData.getUid(), actorData.getSlang());
            }
        }
    }

    private void pushMsgCoinSeller(OfficialPushData data) {
        List<CoinSellerData> coinSellerList = coinSellerDao.selectAllList();
        for (CoinSellerData coinSellerData : coinSellerList) {
            ActorData actorData = actorDao.getActorDataFromCache(coinSellerData.getUid());
            if (actorData == null) {
                logger.error("can not find actor data. uid={}", coinSellerData.getUid());
                continue;
            }
            pushOfficialMsg(data, actorData.getUid(), actorData.getSlang());
        }
    }

    private void pushMsgFamilyManager(OfficialPushData data) {
        List<FamilyMemberData> familyMemberList = familyMemberDao.selectMemberListByRole(2);
        for (FamilyMemberData familyMemberData : familyMemberList) {
            ActorData actorData = actorDao.getActorDataFromCache(familyMemberData.getUid());
            if (actorData == null) {
                logger.error("can not find actor data. uid={}", familyMemberData.getUid());
                continue;
            }
            pushOfficialMsg(data, actorData.getUid(), actorData.getSlang());
        }
    }

    private void pushMsgFamilyOwner(OfficialPushData data) {
        List<FamilyData> allFamilyList = familyDao.getAllFamilyFromCache();
        if (CollectionUtils.isEmpty(allFamilyList)) {
            logger.error("allFamilyList is empty.");
            return;
        }
        for (FamilyData familyData : allFamilyList) {
            ActorData actorData = actorDao.getActorDataFromCache(familyData.getOwnerUid());
            if (actorData == null) {
                logger.error("can not find actor data. uid={}", familyData.getOwnerUid());
                continue;
            }
            pushOfficialMsg(data, actorData.getUid(), actorData.getSlang());
        }
    }

    private void pushMsgWithdrawalTrader(OfficialPushData data) {
        List<WithdrawalTraderData> allTraderList = withdrawalTraderDao.selectAllList();
        if (CollectionUtils.isEmpty(allTraderList)) {
            logger.error("allTraderList is empty.");
            return;
        }
        for (WithdrawalTraderData traderData : allTraderList) {
            ActorData actorData = actorDao.getActorDataFromCache(traderData.getUid());
            if (actorData == null) {
                logger.error("can not find actor data. uid={}", traderData.getUid());
                continue;
            }
            pushOfficialMsg(data, actorData.getUid(), actorData.getSlang());
        }
    }

    private void pushMsgFamilyMember(OfficialPushData data) {
        Map<String, Integer> familyMemberMap = familyMemberDao.selectAllMember();
        for (String aid : familyMemberMap.keySet()) {
            ActorData actorData = actorDao.getActorDataFromCache(aid);
            if (actorData == null) {
                logger.error("can not find actor data. uid={}", aid);
                continue;
            }
            pushOfficialMsg(data, actorData.getUid(), actorData.getSlang());
        }
    }

    private void pushMsgPayUser(OfficialPushData data) {
        int thirtyDays = 30 * 24 * 60 * 60;
        Set<String> rechargeUserSet = rechargeRedis.getAllRechargeUserFromCache();
        for (String aid : rechargeUserSet) {
            ActorData actorData = actorDao.getActorDataFromCache(aid);
            if (actorData == null) {
                logger.error("can not find actor data. uid={}", aid);
                continue;
            }
            int logoutTime = actorData.getLastLogin() != null && actorData.getLastLogin().getLogoutTime() != null ? actorData.getLastLogin().getLogoutTime().intValue() : 0;
            if (logoutTime > DateHelper.getNowSeconds() - thirtyDays) {
                pushOfficialMsg(data, actorData.getUid(), actorData.getSlang());
            }
        }
    }

    private void pushMsgNotPayUser(OfficialPushData data) {
        Set<String> activeUserSet = dauDao.getActiveUserSet(30, 3000);
        if (CollectionUtils.isEmpty(activeUserSet)) {
            logger.info("activeUserSet is empty.");
            return;
        }
        Set<String> rechargeUserSet = rechargeRedis.getAllRechargeUserFromCache();
        for (String uid : activeUserSet) {
            ActorData actorData = actorDao.getActorDataFromCache(uid);
            if (actorData == null) {
                logger.error("can not find actor data . uid={}", uid);
                continue;
            }
            if (actorData.getRobot() > 0 || rechargeUserSet.contains(actorData.getUid())) {
                continue;
            }
            pushOfficialMsg(data, actorData.getUid(), actorData.getSlang());
        }
    }

    private void pushMsgActiveUser(OfficialPushData data, int days) {
        Set<String> activeUserSet = dauDao.getActiveUserSet(days);
        if (CollectionUtils.isEmpty(activeUserSet)) {
            logger.info("activeUserSet is empty.");
            return;
        }
        for (String uid : activeUserSet) {
            ActorData actorData = actorDao.getActorDataFromCache(uid);
            if (actorData == null) {
                logger.error("can not find actor data . uid={}", uid);
                continue;
            }
            if (actorData.getRobot() > 0) {
                continue;
            }
            pushOfficialMsg(data, actorData.getUid(), actorData.getSlang());
        }
    }

    private void pushOfficialMsg(OfficialPushData data, String uid, int slang) {
        String officialPushId = data.get_id().toString();
        OfficialData officialData = officialDao.findByPushIdAndToUid(officialPushId, uid);
        if (officialData == null) {
            boolean isEnglish = SLangType.ENGLISH == slang;
            officialData = new OfficialData();
            officialData.setTitle(isEnglish ? data.getTitle() : data.getTitle_ar());
            officialData.setBody(isEnglish ? data.getBody() : data.getBody_ar());
            officialData.setPicture(isEnglish ? data.getPicture() : data.getPicture_ar());
            officialData.setWidth(data.getWidth());
            officialData.setHeight(data.getHeight());
            officialData.setSubTitle(data.getSubtitle());
            officialData.setAct(isEnglish ? data.getAct() : data.getAct_ar());
            officialData.setUrl(data.getUrl());
            officialData.setTo_uid(uid);
            officialData.setCtime(DateHelper.getNowSeconds());
            officialData.setOfficial_push_id(officialPushId);
            officialData.setAtype(data.getAtype());
            officialData.setNews_type(data.getNews_type() == 4 ? 4 : 0);
            officialData.setRoom_id(data.getRoom_id());
            officialData.setNtype(data.getNtype());
            officialDao.save(officialData);
            if (officialData.get_id() != null) {
                NoticeNewData noticeNewData = new NoticeNewData(uid, officialData.get_id().toString());
                noticeNewData.setNtype(data.getNtype());
                noticeNewDao.save(noticeNewData);
                OfficialPushMsg msg = new OfficialPushMsg();
                msg.setTitle(officialData.getTitle());
                msg.setBody(officialData.getBody());
                msg.setIcon(officialData.getPicture());
                msg.setMsg_type(officialData.getNtype());
                roomWebSender.sendPlayerWebMsg("", uid, uid, msg, false);
            }
        }
    }
}
