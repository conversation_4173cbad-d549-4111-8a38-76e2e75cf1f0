package com.quhong.operation.server;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.TypeReference;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.quhong.config.CaffeineCacheConfig;
import com.quhong.core.distribution.DistributeLock;
import com.quhong.data.ActorData;
import com.quhong.enums.HttpCode;
import com.quhong.enums.WithdrawReviewStatus;
import com.quhong.exception.CommonH5Exception;
import com.quhong.mongo.dao.ActorDao;
import com.quhong.mongo.dao.MineWithdrawMethodDao;
import com.quhong.mongo.dao.WithdrawMethodConfigDao;
import com.quhong.mongo.data.MineWithdrawMethodData;
import com.quhong.mongo.data.WithdrawMethodConfigData;
import com.quhong.mongo.data.WithdrawMethodField;
import com.quhong.mysql.dao.CountriesDao;
import com.quhong.mysql.dao.FamilyDao;
import com.quhong.mysql.dao.WithdrawReviewDao;
import com.quhong.mysql.data.CountriesData;
import com.quhong.mysql.data.FamilyData;
import com.quhong.mysql.data.PayoutLogData;
import com.quhong.mysql.data.WithdrawReviewData;
import com.quhong.operation.constant.CountryCodeToName;
import com.quhong.operation.share.condition.ReviewCondition;
import com.quhong.operation.share.dto.WithdrawReviewDTO;
import com.quhong.operation.share.vo.WithdrawReviewOptionVO;
import com.quhong.operation.share.vo.WithdrawReviewVO;
import com.quhong.operation.utils.DateHelper;
import com.quhong.service.AnchorWalletService;
import com.quhong.utils.MybatisPlusPageUtils;
import com.quhong.vo.H5PageVO;
import jakarta.annotation.Resource;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.BeanUtils;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import org.springframework.util.ObjectUtils;
import org.springframework.util.StringUtils;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * 美金提现审核
 *
 * <AUTHOR>
 * @date 2023/8/15
 */
@Service
public class WithdrawReviewService {

    private final Logger logger = LoggerFactory.getLogger(getClass());

    @Resource
    private WithdrawReviewDao withdrawReviewDao;
    @Resource
    private ActorDao actorDao;
    @Resource
    private FamilyDao familyDao;
    @Resource
    private AnchorWalletService anchorWalletService;
    @Resource
    private MineWithdrawMethodDao mineWithdrawMethodDao;
    @Resource
    private WithdrawMethodConfigDao withdrawMethodConfigDao;
    @Resource
    private CountriesDao countriesDao;
    @Resource
    private PayoutService payoutService;

    public H5PageVO<WithdrawReviewVO> selectPage(ReviewCondition condition) {
        List<WithdrawMethodConfigData> allMethodConfig = withdrawMethodConfigDao.findAll();
        Map<String, WithdrawMethodConfigData> methodConfigMap = allMethodConfig.stream().collect(Collectors.toMap(k -> k.get_id().toString(), Function.identity()));
        int page = condition.getPage() == null ? 1 : condition.getPage();
        int pageSize = condition.getPageSize() == null ? 10 : condition.getPageSize();
        Map<String, Map<String, WithdrawMethodField>> methodFieldMap = getMethodFieldMap(allMethodConfig);
        return MybatisPlusPageUtils.getVOPage(withdrawReviewDao.getMapper(), getQuery(condition, methodConfigMap), page, pageSize, data -> {
            WithdrawReviewVO vo = new WithdrawReviewVO();
            ActorData actorData = actorDao.getActorDataFromCache(data.getUid());
            BeanUtils.copyProperties(data, vo);
            vo.setId(data.getId() + "");
            vo.setRid(actorData != null ? actorData.getRid() + "" : "");
            vo.setStrRid(actorData != null ? actorData.getShowRid() : "");
            vo.setUserName(actorData != null ? actorData.getName() : "");
            FamilyData familyData = familyDao.selectByIdFromCache(data.getFamilyId());
            vo.setFamilyName(familyData != null ? familyData.getName() : "");
            WithdrawMethodConfigData configData = methodConfigMap.get(data.getMethodId());
            CountriesData country = countriesDao.getCountryByCode(data.getCountry().toUpperCase());
            vo.setCountry(country != null ? country.getName() : "");
            vo.setMethodType(configData != null ? configData.getName() : "");
            vo.setPaymentAmount(calcPaymentAmount(data));
            vo.setStatus(data.getStatus());
            vo.setAutoPayment(data.getAutoPayment());
            vo.setReviewTime(DateHelper.ARABIAN.timestampToDatetimeStr2(data.getReviewTime() * 1000L));
            vo.setRequestTime(DateHelper.ARABIAN.timestampToDatetimeStr2(data.getRequestTime() * 1000L));
            if (null != data.getAutoPayment() && 1 == data.getAutoPayment()) {
                vo.setPayoutId(payoutService.buildPayoutId(data.getId()));
                vo.setCallbackTime(DateHelper.ARABIAN.timestampToDatetimeStr2(data.getCallbackTime() * 1000L));
                vo.setPayChannel(configData == null ? "" : configData.getAutoPayChannel());
                PayoutLogData payoutLogData = payoutService.getPayoutLogData(data);
                if (payoutLogData != null) {
                    vo.setReason(JSON.toJSONString(payoutLogData));
                }
            }
            vo.setStrStatus(getReviewStatus(data.getStatus()));
            vo.setCtime(DateHelper.ARABIAN.timestampToDatetimeStr(data.getCtime() * 1000L));
            fillMethodDetail(data, methodFieldMap, vo);
            vo.setWithdrawAmount(data.getAmount());
            return vo;
        });
    }

    private QueryWrapper<WithdrawReviewData> getQuery(ReviewCondition condition, Map<String, WithdrawMethodConfigData> methodConfigMap) {
        String userId = "";
        if (StringUtils.hasLength(condition.getRid())) {
            ActorData actorData = actorDao.getActorByRidOrAlphaRid(condition.getRid());
            userId = actorData != null ? actorData.getUid() : "---";
        }
        Set<String> methodIdSet = null;
        if (!ObjectUtils.isEmpty(condition.getMethodId())) {
            methodIdSet = new HashSet<>();
            methodIdSet.add(condition.getMethodId());
        } else if (StringUtils.hasLength(condition.getMethod())) {
            methodIdSet = methodConfigMap.values().stream().filter(k -> k.getName().equals(condition.getMethod())).map(o -> o.get_id().toString()).collect(Collectors.toSet());
        }
        Integer startTime = null;
        Integer endTime = null;
        if (StringUtils.hasLength(condition.getStartTime()) || StringUtils.hasLength(condition.getEndTime())) {
            Integer[] timeArr = DateHelper.ARABIAN.getStartOrEndSeconds(condition.getStartTime(), condition.getEndTime());
            startTime = timeArr[0];
            endTime = --timeArr[1];
        }
        QueryWrapper<WithdrawReviewData> query = new QueryWrapper<>();
        if (null != condition.getId() && condition.getId() != -1) {
            query.eq("id", condition.getId());
        }
        if (!ObjectUtils.isEmpty(userId)) {
            query.eq("uid", userId);
        }
        if (!ObjectUtils.isEmpty(condition.getCountry())) {
            query.eq("country", condition.getCountry());
        }
        if (null != condition.getStatus() && condition.getStatus() != -1) {
            query.eq("status", condition.getStatus());
        }
        if (null != condition.getAutoPayment() && condition.getAutoPayment() != -1) {
            query.eq("auto_payment", condition.getAutoPayment());
        }
        if (!ObjectUtils.isEmpty(methodIdSet)) {
            query.in("method_id", methodIdSet);
        }
        if (!ObjectUtils.isEmpty(condition.getConfig())) {
            query.like("`config`", "%" + condition.getConfig() + "%");
        }
        String descColumn = "ctime";
        // 0提交时间、1审核时间、2请求付款时间、3付款时间。
        if (null != startTime && startTime > 0 && endTime > 0) {
            if (condition.getTimeType() == 0) {
                query.ge("ctime", startTime);
                query.lt("ctime", endTime);
            } else if (condition.getTimeType() == 1) {
                query.ge("review_time", startTime);
                query.lt("review_time", endTime);
                descColumn = "review_time";
            } else if (condition.getTimeType() == 2) {
                query.ge("request_time", startTime);
                query.lt("request_time", endTime);
                descColumn = "request_time";
            } else {
                query.ge("callback_time", startTime);
                query.lt("callback_time", endTime);
                descColumn = "callback_time";
            }
        }
        query.orderByDesc(descColumn);
        return query;
    }

    private String calcPaymentAmount(WithdrawReviewData data) {
        if (!WithdrawMethodConfigDao.AUTO_PAYMENT_LOCAL_MAP.containsKey(data.getMethodId())) {
            // 非本地币种体现的直接显示USD
            return new BigDecimal(data.getAmount()).subtract(data.getHandlingFee()).setScale(2, RoundingMode.HALF_UP) + "USD";
        }
        if (!ObjectUtils.isEmpty(data.getCurrency()) && !ObjectUtils.isEmpty(data.getUsdExchange())) {
            return new BigDecimal(data.getAmount()).subtract(data.getHandlingFee()).multiply(new BigDecimal(data.getUsdExchange())).setScale(2, RoundingMode.HALF_UP) + data.getCurrency();
        }
        return new BigDecimal(data.getAmount()).subtract(data.getHandlingFee()).setScale(2, RoundingMode.HALF_UP) + "USD";
    }

    private String getReviewStatus(int status) {
        String strStatus;
        switch (status) {
            case WithdrawReviewStatus.NONE -> strStatus = "待审核";
            case WithdrawReviewStatus.PENDING_PAYMENT -> strStatus = "待付款";
            case WithdrawReviewStatus.REJECTED -> strStatus = "审核拒绝";
            case WithdrawReviewStatus.PAYMENT_SUCCESS -> strStatus = "付款成功";
            case WithdrawReviewStatus.PAYMENT_FAIL -> strStatus = "付款失败";
            case WithdrawReviewStatus.AUTO_PAYMENT_PENDING -> strStatus = "自动付款中";
            case WithdrawReviewStatus.AUTO_PAYMENT_SUCCESS -> strStatus = "自动付款成功";
            case WithdrawReviewStatus.AUTO_PAYMENT_FAIL -> strStatus = "自动付款失败";
            case WithdrawReviewStatus.AUTO_PAYMENT_WAITING -> strStatus = "待自动付款";
            default -> strStatus = "未知状态";
        }
        return strStatus;
    }

    public void updateStatus(WithdrawReviewDTO dto) {
        List<WithdrawReviewData> reviewDataList = withdrawReviewDao.selectByIds(dto.getIds());
        if (CollectionUtils.isEmpty(reviewDataList)) {
            throw new CommonH5Exception(new HttpCode(1, "没有找到对应的审核单"));
        }
        Set<Integer> statusSet = reviewDataList.stream().map(WithdrawReviewData::getStatus).collect(Collectors.toSet());
        if (statusSet.size() != 1) {
            throw new CommonH5Exception(new HttpCode(1, "状态不统一的订单无法批量操作"));
        }
        for (WithdrawReviewData reviewData : reviewDataList) {
            try (DistributeLock lock = new DistributeLock("updateWithdrawReviewStatus:" + reviewData.getId())) {
                lock.lock();
                if (reviewData.getStatus() == WithdrawReviewStatus.REJECTED || reviewData.getStatus() == WithdrawReviewStatus.PAYMENT_SUCCESS
                        || reviewData.getStatus() == WithdrawReviewStatus.PAYMENT_FAIL || reviewData.getStatus() == WithdrawReviewStatus.AUTO_PAYMENT_SUCCESS
                        || reviewData.getStatus() == WithdrawReviewStatus.AUTO_PAYMENT_FAIL || reviewData.getStatus() == WithdrawReviewStatus.AUTO_PAYMENT_PENDING) {
                    throw new CommonH5Exception(new HttpCode(1, "审核流程已结束，无法再操作"));
                }
                switch (dto.getStatus()) {
                    case WithdrawReviewStatus.PENDING_PAYMENT -> {
                        // 审核通过,状态改为待付款
                        if (reviewData.getStatus() != WithdrawReviewStatus.NONE) {
                            throw new CommonH5Exception(HttpCode.PARAM_ERROR);
                        }
                        withdrawReviewDao.updateStatusAndReviewTime(reviewData.getId(), WithdrawReviewStatus.PENDING_PAYMENT, "");
                    }
                    case WithdrawReviewStatus.REJECTED -> {
                        // 审核不通过，状态改为审核拒绝
                        if (reviewData.getStatus() != WithdrawReviewStatus.NONE && reviewData.getStatus() != WithdrawReviewStatus.AUTO_PAYMENT_WAITING) {
                            throw new CommonH5Exception(HttpCode.PARAM_ERROR);
                        }
                        // 退还主播美金余额
                        anchorWalletService.refundUsd(reviewData.getUid(), reviewData.getAmount(), reviewData.getFamilyId());
                        // 审核状态流转
                        withdrawReviewDao.updateStatusAndReviewTime(reviewData.getId(), WithdrawReviewStatus.REJECTED, dto.getReason());
                    }
                    case WithdrawReviewStatus.PAYMENT_SUCCESS -> {
                        // 打款成功后操作，状态改为打款成功
                        if (reviewData.getStatus() != WithdrawReviewStatus.PENDING_PAYMENT) {
                            throw new CommonH5Exception(HttpCode.PARAM_ERROR);
                        }
                        withdrawReviewDao.updateStatus(reviewData.getId(), WithdrawReviewStatus.PAYMENT_SUCCESS, "");
                        mineWithdrawMethodDao.updateVerify(reviewData.getMineMethodId(), 1);
                    }
                    case WithdrawReviewStatus.PAYMENT_FAIL -> {
                        // 打款失败后操作，状态改为打款失败
                        if (reviewData.getStatus() != WithdrawReviewStatus.PENDING_PAYMENT) {
                            throw new CommonH5Exception(HttpCode.PARAM_ERROR);
                        }
                        // 退还主播美金余额
                        anchorWalletService.refundUsd(reviewData.getUid(), reviewData.getAmount(), reviewData.getFamilyId());
                        // 审核状态流转
                        withdrawReviewDao.updateStatus(reviewData.getId(), WithdrawReviewStatus.PAYMENT_FAIL, dto.getReason());
                        mineWithdrawMethodDao.updateVerify(reviewData.getMineMethodId(), 1 == dto.getBlock() ? 2 : 0);
                    }
                    case WithdrawReviewStatus.AUTO_PAYMENT_WAITING -> {
                        // 审核通过,状态改为待付款
                        if (reviewData.getStatus() != WithdrawReviewStatus.NONE) {
                            throw new CommonH5Exception(HttpCode.PARAM_ERROR);
                        }
                        if (reviewData.getAutoPayment() == null || reviewData.getAutoPayment() != 1) {
                            throw new CommonH5Exception(1, "不支持自动提现：" + reviewData.getId());
                        }
                        withdrawReviewDao.updateStatusAndReviewTime(reviewData.getId(), WithdrawReviewStatus.AUTO_PAYMENT_WAITING, "");
                    }
                    case WithdrawReviewStatus.AUTO_PAYMENT_PENDING -> {
                        // 打款成功后操作，状态改为打款成功
                        if (reviewData.getStatus() != WithdrawReviewStatus.AUTO_PAYMENT_WAITING) {
                            throw new CommonH5Exception(HttpCode.PARAM_ERROR);
                        }
                        if (reviewData.getAutoPayment() == null || reviewData.getAutoPayment() != 1) {
                            throw new CommonH5Exception(1, "自动提现已关闭");
                        }
//                        if (true) {
//                            throw new CommonH5Exception(1, "自动提现已关闭");
//                        }
                        // api自动打款
                        payoutService.withdrawPayout(reviewData);
                        withdrawReviewDao.updateStatusAndRequestTime(reviewData.getId(), WithdrawReviewStatus.AUTO_PAYMENT_PENDING, "");
                    }
                    default -> {
                        logger.error("status param error. status={}", dto.getStatus());
                        throw new CommonH5Exception(HttpCode.PARAM_ERROR);
                    }
                }
            }
        }
    }

    private void fillMethodDetail(WithdrawReviewData data, Map<String, Map<String, WithdrawMethodField>> methodFieldMap, WithdrawReviewVO vo) {
        List<WithdrawReviewVO.MethodDetail> list = new ArrayList<>();
        Map<String, String> content;
        if (!ObjectUtils.isEmpty(data.getConfig())) {
            try {
                content = JSONObject.parseObject(data.getConfig(), new TypeReference<>() {
                });
            } catch (Exception e) {
                logger.error("parse config error. config={} {}", data.getConfig(), e.getMessage());
                MineWithdrawMethodData mineMethodData2 = mineWithdrawMethodDao.findByIdAndUid(data.getMineMethodId(), data.getUid());
                content = null == mineMethodData2 ? null : mineMethodData2.getContent();
            }
        } else {
            MineWithdrawMethodData mineMethodData2 = mineWithdrawMethodDao.findByIdAndUid(data.getMineMethodId(), data.getUid());
            content = null == mineMethodData2 ? null : mineMethodData2.getContent();
        }
        if (!CollectionUtils.isEmpty(content)) {
            for (Map.Entry<String, String> entry : content.entrySet()) {
                if (CollectionUtils.isEmpty(methodFieldMap.get(data.getMethodId()))) {
                    continue;
                }
                WithdrawMethodField methodField = methodFieldMap.get(data.getMethodId()).get(entry.getKey());
                if (methodField == null) {
                    continue;
                }
                String fieldName = methodField.getName();
                WithdrawReviewVO.MethodDetail methodDetail;
                if (methodField.getInputType() == 1) {
                    Map<String, String> optionMap = methodField.getOptions().stream().collect(Collectors.toMap(WithdrawMethodField.Option::getKey, WithdrawMethodField.Option::getValue));
                    methodDetail = new WithdrawReviewVO.MethodDetail(entry.getKey(), fieldName, optionMap.get(entry.getValue()));
                } else {
                    methodDetail = new WithdrawReviewVO.MethodDetail(entry.getKey(), fieldName, entry.getValue());
                }
                if ("account_type".equals(entry.getKey()) && StringUtils.hasLength(entry.getValue())) {
                    methodDetail.setFieldValue(methodDetail.getFieldValue().toUpperCase());
                }
                list.add(methodDetail);
            }
        }
        vo.setMethodDetail(list);
    }

    private Map<String, Map<String, WithdrawMethodField>> getMethodFieldMap(List<WithdrawMethodConfigData> all) {
        Map<String, Map<String, WithdrawMethodField>> resultMap = new HashMap<>(8);
        if (!CollectionUtils.isEmpty(all)) {
            for (WithdrawMethodConfigData data : all) {
                if (!CollectionUtils.isEmpty(data.getFields())) {
                    Map<String, WithdrawMethodField> fieldMap = data.getFields().stream().collect(Collectors.toMap(WithdrawMethodField::getKey, Function.identity()));
                    resultMap.put(data.get_id().toString(), fieldMap);
                }
            }
        }
        return resultMap;
    }

    @Cacheable(value = "getWithdrawReviewOptionList", cacheManager = CaffeineCacheConfig.EXPIRE_1M_AFTER_WRITE)
    public WithdrawReviewOptionVO getOptionList() {
        WithdrawReviewOptionVO vo = new WithdrawReviewOptionVO();
        List<WithdrawReviewOptionVO.WithdrawMethod> methodList = new ArrayList<>();
        List<WithdrawReviewOptionVO.WithdrawMethod> channelList = new ArrayList<>();
        List<WithdrawMethodConfigData> methodConfigList = withdrawMethodConfigDao.findAll();
        List<WithdrawReviewOptionVO.CountryVO> countryList = new ArrayList<>();
        countryList.add(new WithdrawReviewOptionVO.CountryVO("", "ALL"));
        if (!CollectionUtils.isEmpty(methodConfigList)) {
            Set<String> countrySet = methodConfigList.stream().map(WithdrawMethodConfigData::getCountry).filter(country -> !"ALL".equals(country)).collect(Collectors.toSet());
            Set<String> methodNameSet = methodConfigList.stream().map(WithdrawMethodConfigData::getName).collect(Collectors.toSet());
            for (String methodName : methodNameSet) {
                methodList.add(new WithdrawReviewOptionVO.WithdrawMethod(methodName, methodName));
            }
            for (String countryCode : countrySet) {
                countryList.add(new WithdrawReviewOptionVO.CountryVO(countryCode, CountryCodeToName.MAP.get(countryCode)));
            }
            for (WithdrawMethodConfigData configData : methodConfigList) {
                if (!ObjectUtils.isEmpty(configData.getAutoPayChannel())) {
                    channelList.add(new WithdrawReviewOptionVO.WithdrawMethod(configData.get_id().toString(), configData.getAutoPayChannel()));
                }
            }
        }
        vo.setWithdrawMethodList(methodList);
        vo.setCountryList(countryList);
        vo.setPayCannelList(channelList);
        return vo;
    }

    public void updateRemarkUrl(Integer id, String remarkUrl) {
        withdrawReviewDao.updateRemarkUrl(id, remarkUrl);
    }
}
