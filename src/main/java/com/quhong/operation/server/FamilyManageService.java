package com.quhong.operation.server;

import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.quhong.analysis.EventDTO;
import com.quhong.analysis.EventReport;
import com.quhong.analysis.FamilyLogEvent;
import com.quhong.config.CaffeineCacheConfig;
import com.quhong.core.concurrency.BaseTaskFactory;
import com.quhong.core.concurrency.tasks.Task;
import com.quhong.core.utils.DateHelper;
import com.quhong.data.ActorData;
import com.quhong.data.condition.FamilyCondition;
import com.quhong.enums.HttpCode;
import com.quhong.enums.SLangType;
import com.quhong.exception.CommonH5Exception;
import com.quhong.mongo.dao.ActorDao;
import com.quhong.mongo.dao.FamilyDismissRecordDao;
import com.quhong.mongo.data.FamilyDismissRecordData;
import com.quhong.mongo.data.OfficialData;
import com.quhong.mysql.config.WahoMySQLBean;
import com.quhong.mysql.dao.*;
import com.quhong.mysql.data.*;
import com.quhong.operation.dao.ManagerDao;
import com.quhong.operation.share.condition.*;
import com.quhong.operation.share.dto.FamilyDTO;
import com.quhong.operation.share.dto.FamilyMemberDTO;
import com.quhong.operation.share.dto.FamilyOperateDTO;
import com.quhong.operation.share.enumerate.ActionEnum;
import com.quhong.operation.share.mongobean.Manager;
import com.quhong.operation.share.vo.*;
import com.quhong.redis.GenFamilyRidRedis;
import com.quhong.room.TestFamily;
import com.quhong.service.OfficialMsgService;
import com.quhong.utils.CollectionUtil;
import jakarta.annotation.Resource;
import org.bson.types.ObjectId;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.BeanUtils;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;
import org.springframework.util.ObjectUtils;
import org.springframework.util.StringUtils;

import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2023/5/29
 */
@Service
public class FamilyManageService {

    private static final Logger logger = LoggerFactory.getLogger(FamilyManageService.class);

    private static final List<String> REGION_LIST = Arrays.asList("大区1", "大区2", "大区3");
    private static final int MAX_MEMBER = 1000;

    public static final String DEFAULT_HEAD = "https://cloudcdn.waho.live/resource/op_sys_1693394734_default_family_icon.png";

    @Resource
    private ActorDao actorDao;
    @Resource
    private FamilyDao familyDao;
    @Resource
    private FamilyMemberDao familyMemberDao;
    @Resource
    private CountriesDao countriesDao;
    @Resource
    private GenFamilyRidRedis genFamilyRidRedis;
    @Resource
    private OfficialMsgService officialMsgService;
    @Resource
    private ManagerDao managerDao;
    @Resource
    private EventReport eventReport;
    @Resource
    private JoinFamilyRecordDao joinFamilyRecordDao;
    @Resource
    private FamilyOperateDao familyOperateDao;
    @Resource
    private FamilyMemberOperateDao familyMemberOperateDao;
    @Resource
    private QuitFamilyRequestDao quitFamilyRequestDao;
    @Resource
    private FamilyDismissRecordDao familyDismissRecordDao;

    /**
     * 新增或修改公会
     */
    @Transactional(value = WahoMySQLBean.WAHO_TRANSACTION, rollbackFor = {Exception.class, RuntimeException.class})
    public void saveFamilyData(String managerUid, FamilyDTO dto) {
        ActorData actorData = actorDao.getActorByRidOrAlphaRid(dto.getOwnerRid().trim());
        if (actorData == null) {
            logger.error("can not find actor data. ownerRid={}", dto.getOwnerRid());
            throw new CommonH5Exception(new HttpCode(1, "公会长id输入错误，不存在该id的用户"));
        }
        if (actorData.getSvipLevel() > 0 && !dto.isConformSvip()) {
            logger.info("create family actor has svip. uid={}", actorData.getUid());
            throw new CommonH5Exception(new HttpCode(123, "SVIP%s级用户，创建公会用户的SVIP等级将失效，确定创建吗？".formatted(actorData.getSvipLevel())));
        }
        Manager manager = managerDao.getManager(dto.getManager());
        if (manager == null) {
            logger.error("can not find manager data. managerName={}", dto.getManager());
            throw new CommonH5Exception(new HttpCode(1, "运营负责人输入错误"));
        }
        Manager superior = managerDao.getDataByUid(manager.getSuperior_uid());
        if (superior == null) {
            logger.error("can not find superior manager data. superiorUid={}", manager.getSuperior_uid());
            throw new CommonH5Exception(new HttpCode(1, "该运营负责人找不到上级超管"));
        }
        dto.setSuperManager(superior.getAccount());
        dto.setOwnerUid(actorData.getUid());
        FamilyData familyData;
        if (dto.getId() == null) {
            if (familyDao.isBannedCreateFamily(actorData.getUid())) {
                throw new CommonH5Exception(new HttpCode(1, "该用户被禁止创建公会"));
            }
            if (!familyMemberDao.isDeviceUniqueAnchorAccount(actorData.getUid(), actorData.getTn_id())) {
                logger.error("The user's device already has a host and cannot be added. uid={} tnId={}", actorData.getUid(), actorData.getTn_id());
                throw new CommonH5Exception(new HttpCode(1, "该公会长的设备已存在主播账号，无法添加"));
            }
            FamilyMemberData memberData = familyMemberDao.selectByUid(actorData.getUid());
            if (memberData != null) {
                throw new CommonH5Exception(new HttpCode(1, "该公会长已经是其他公会的公会成员了"));
            }
            // 新增公会
            familyData = new FamilyData();
            int rid = genFamilyRidRedis.getGenFamilyRid();
            if (rid == 0) {
                throw new CommonH5Exception(new HttpCode(1, "初始化公会rid错误，请联系后台开发人员"));
            }
            BeanUtils.copyProperties(dto, familyData, "status");
            familyData.setRid(rid);
            familyData.setHead(DEFAULT_HEAD);
            familyData.setStatus(1);
            familyData.setCtime(DateHelper.getNowSeconds());
            familyData.setServiceUid(manager.getService_uid());
            familyData.setSuperManager(superior.getAccount());
            // if (isFamilyManager(managerUid)) {
            //     // 超管、运营负责人的账户进行创建公会需要先提交到审核列表进行审核，审核通过，操作才算生效：
            //     submitFamilyOptReview(familyData, managerUid, 0);
            //     return;
            // }
            if (isFamilyManager(managerUid)) {
                familyData.setRewardLevel("");
                familyData.setForcedControl(0);
            }
            familyData.setRewardLevel(dto.getRewardLevel());
            familyDao.insert(familyData);
            insertFamilyPresident(familyData.getId(), actorData.getUid());
        } else {
            if (isFamilyManager(managerUid)) {
                throw new CommonH5Exception(new HttpCode(1, "你没有修改公会信息的权限"));
            }
            // 修改公会
            familyData = familyDao.selectById(dto.getId());
            if (familyData == null) {
                logger.error("can not find family data. id={}", dto.getId());
                throw new CommonH5Exception(new HttpCode(1, "修改公会信息参数错误，找不到id对应的公会"));
            }
            if (!familyData.getOwnerUid().equals(dto.getOwnerUid()) && !familyMemberDao.isDeviceUniqueAnchorAccount(actorData.getUid(), actorData.getTn_id())) {
                logger.error("The user's device already has a host and cannot be added. uid={} tnId={}", actorData.getUid(), actorData.getTn_id());
                throw new CommonH5Exception(1, "该公会长的设备已存在主播账号，无法添加");
            }
            if (familyData.getStatus() == 2) {
                logger.error("family has been disabled. id={}", dto.getId());
                throw new CommonH5Exception(new HttpCode(1, "已被禁用的公会不能再修改信息"));
            }
            if (null != familyData.getPid() && 0 != familyData.getPid() && !ObjectUtils.isEmpty(dto.getRewardLevel())) {
                logger.error("only first agent can set rewardLevel. id={}", dto.getId());
                throw new CommonH5Exception(new HttpCode(1, "仅一级代理可以设置佣金分成等级"));
            }
            String oldOwnerUid = familyData.getOwnerUid();
            if (!oldOwnerUid.equals(actorData.getUid())) {
                if (familyDao.isBannedCreateFamily(actorData.getUid())) {
                    throw new CommonH5Exception(new HttpCode(1, "该用户被禁止创建公会"));
                }
                FamilyMemberData data = familyMemberDao.selectByUid(actorData.getUid());
                if (data != null) {
                    throw new CommonH5Exception(1, "所填的公会长已是其他公会的成员");
                }
            }
            BeanUtils.copyProperties(dto, familyData, "rid", "head", "announce", "status", "ctime");
            familyData.setServiceUid(manager.getService_uid());
            familyData.setSuperManager(superior.getAccount());
            familyData.setRewardLevel(dto.getRewardLevel());
            familyDao.update(familyData);
            if (!oldOwnerUid.equals(actorData.getUid())) {
                familyMemberDao.deleteByFamilyIdAndUid(familyData.getId(), oldOwnerUid, "运营平台修改公会长");
                insertFamilyPresident(familyData.getId(), actorData.getUid());
            }
        }
        EventDTO eventDTO = new EventDTO(new FamilyLogEvent(familyData.getId(), familyData.getRid(), familyData.getOwnerUid(), familyData.getCtime()));
        eventDTO.setEventId("family:" + familyData.getId());
        eventReport.trackUpdate(eventDTO);
    }

    private boolean isFamilyManager(String managerUid) {
        Manager manager = managerDao.getDataByUid(managerUid);
        return manager.getFamily_manage_role() > 0 || manager.getRoleId() == 1 || manager.getRoleId() == 2;
    }

    private void submitFamilyOptReview(FamilyData familyData, String optManagerUid, int optType) {
        if (optType == 1 && familyOperateDao.selectCount(familyData.getId(), optManagerUid, optType, 0) > 0) {
            throw new CommonH5Exception(new HttpCode(1, "请勿重复提交申请"));
        }
        FamilyOperateData data = new FamilyOperateData();
        data.setFamilyId(familyData.getId());
        data.setName(familyData.getName());
        data.setOwnerUid(familyData.getOwnerUid());
        data.setCountry(familyData.getCountry());
        data.setRegion(familyData.getRegion());
        data.setAgentContact(familyData.getAgentContact());
        data.setManager(familyData.getManager());
        data.setSuperManager(familyData.getSuperManager());
        data.setOptType(optType);
        data.setStatus(0);
        data.setOptManagerUid(optManagerUid);
        int nowTime = DateHelper.getNowSeconds();
        data.setOptTime(nowTime);
        data.setCtime(nowTime);
        familyOperateDao.insert(data);
    }

    /**
     * 公会列表
     */
    public PageResultVO<FamilyVO> selectFamilyList(String uid, FamilyCondition condition) {
        PageResultVO<FamilyVO> pageVO = new PageResultVO<>();
        int page = condition.getPage() != null && condition.getPage() != 0 ? condition.getPage() : 1;
        int pageSize = condition.getPageSize() != null && condition.getPageSize() != 0 ? condition.getPageSize() : 20;
        handleFamilyCondition(uid, condition);
        logger.info("handleFamilyCondition after. condition={}", JSONObject.toJSONString(condition));
        IPage<FamilyData> iPage = familyDao.selectList(condition, page, pageSize);
        List<FamilyVO> list = new ArrayList<>();
        if (!CollectionUtils.isEmpty(iPage.getRecords())) {
            for (FamilyData data : iPage.getRecords()) {
                FamilyVO vo = new FamilyVO();
                BeanUtils.copyProperties(data, vo);
                ActorData actorData = actorDao.getActorDataFromCache(data.getOwnerUid());
                vo.setOwnerRid(actorData != null ? actorData.getRid() : null);
                vo.setOwnerStrRid(actorData != null ? actorData.getShowRid() : null);
                vo.setOwnerName(actorData != null ? actorData.getName() : "");
                vo.setSvipLevel(actorData != null ? actorData.getSvipLevel() : 0);
                vo.setCascadeLevel(data.getCascadeLevel());
                vo.setPid(null == data.getPid() || 0 == data.getPid() ? 0 : familyDao.selectByIdFromCache(data.getPid()).getRid());
                list.add(vo);
            }
        }
        pageVO.setList(list);
        pageVO.setTotal(iPage.getTotal());
        return pageVO;
    }

    /**
     * 禁用公会
     */
    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = {Exception.class}, transactionManager = WahoMySQLBean.WAHO_TRANSACTION)
    public void disabledFamily(String managerUid, FamilyDTO dto) {
        FamilyData familyData = familyDao.selectById(dto.getId());
        if (familyData == null) {
            logger.error("can not find family data. id={}", dto.getId());
            throw new CommonH5Exception(new HttpCode(1, "参数错误，没有找到id对应的公会"));
        }
        if (familyData.getStatus() != 1) {
            logger.info("family has been disabled. id={}", dto.getId());
            throw new CommonH5Exception(new HttpCode(1, "该公会已经被禁用"));
        }
        if (TestFamily.isTestFamily(familyData.getId())) {
            throw new CommonH5Exception(new HttpCode(1, "请勿禁用测试人员使用的公会"));
        }
        if (isFamilyManager(managerUid)) {
            // 超管、运营负责人的账户进行禁用公会需要先提交到审核列表进行审核，审核通过，操作才算生效：
            submitFamilyOptReview(familyData, managerUid, 1);
            return;
        }
        familyData.setStatus(2);
        familyDao.update(familyData);
        List<FamilyMemberData> memberList = familyMemberDao.selectMemberList(familyData.getId());
        int banStatus = 0;
        if (dto.isBlacklist()) {
            // 禁用公会的同时拉黑公会长
            familyDao.addBanCreateFamilyUser(familyData.getOwnerUid());
            banStatus = 1;
        }
        familyDismissRecordDao.save(new FamilyDismissRecordData(familyData.getId(), familyData.getOwnerUid(), familyData.getManager(), familyData.getSuperManager(), banStatus, 1, managerUid, memberList));
        // 给公会全部成员发送公会被禁用消息
        sendDisabledFamilyMsg(familyData.getId(), familyData.getName(), memberList);
    }

    public void sendDisabledFamilyMsg(int familyId, String familyName, List<FamilyMemberData> memberList) {
        if (CollectionUtils.isEmpty(memberList)) {
            logger.info("family member list is empty. familyId={}", familyId);
            return;
        }
        // 公会被禁用，则该公会下所有主播均自动被踢出，变成普通用户，同时主播收到app内通知提醒
        BaseTaskFactory.getFactory().addSlow(new Task() {
            @Override
            protected void execute() {
                for (FamilyMemberData data : memberList) {
                    ActorData actorData = actorDao.getActorDataFromCache(data.getUid());
                    if (null == actorData) {
                        logger.error("cannot find actor, uid={}", data.getUid());
                        return;
                    }
                    String title;
                    String noticeBody;
                    if (actorData.getSlang() == SLangType.ENGLISH) {
                        title = "Family Notification";
                        noticeBody = String.format("You have exited from the family %s as the family has been disbanded.", familyName);
                    } else {
                        title = "إشعار العائلة";
                        noticeBody = String.format("لقد انسحبت من العائلة  ”%s“بسبب حل العائلة الخاصة بك.", familyName);
                    }
                    familyMemberDao.deleteByFamilyIdAndUid(data.getFamilyId(), data.getUid(), "公会被禁用");
                    // 主播收到app内通知提醒
                    officialMsgService.officialMsgPush(actorData.getUid(), title, noticeBody, 20);
                }
            }
        });
    }

    /**
     * 公会成员列表
     */
    public PageResultVO<FamilyMemberVO> getMemberList(FamilyCondition condition) {
        PageResultVO<FamilyMemberVO> pageVO = new PageResultVO<>();
        int page = condition.getPage() != null && condition.getPage() != 0 ? condition.getPage() : 1;
        int pageSize = condition.getPageSize() != null && condition.getPageSize() != 0 ? condition.getPageSize() : 20;
        IPage<FamilyMemberData> iPage = familyMemberDao.selectMemberPage(condition.getId(), "", page, pageSize);
        List<FamilyMemberVO> list = new ArrayList<>();
        if (!CollectionUtils.isEmpty(iPage.getRecords())) {
            for (FamilyMemberData data : iPage.getRecords()) {
                ActorData actorData = actorDao.getActorDataFromCache(data.getUid());
                if (actorData == null) {
                    logger.error("can not find actor data. uid={}", data.getUid());
                    continue;
                }
                FamilyMemberVO vo = new FamilyMemberVO();
                vo.setUid(data.getUid());
                vo.setRole(data.getRole());
                vo.setCtime(data.getCtime());
                vo.setHead(actorData.getHead());
                vo.setSvipLevel(actorData.getSvipLevel());
                vo.setName(actorData.getName());
                vo.setRid(actorData.getRid());
                vo.setStrRid(actorData.getShowRid());
                list.add(vo);
            }
        }
        pageVO.setList(list);
        pageVO.setTotal(iPage.getTotal());
        return pageVO;
    }

    public PageResultVO<FamilyMemberVO> getSubAgentList(FamilyCondition condition) {
        FamilyData familyData = familyDao.selectById(condition.getFamilyId());
        if (familyData == null) {
            throw new CommonH5Exception(HttpCode.PARAM_ERROR);
        }
        PageResultVO<FamilyMemberVO> pageVO = new PageResultVO<>();
        int page = condition.getPage() != null && condition.getPage() != 0 ? condition.getPage() : 1;
        int pageSize = condition.getPageSize() != null && condition.getPageSize() != 0 ? condition.getPageSize() : 20;
        List<FamilyData> dataList = familyDao.selectSubAgentPage(familyData.getId(), page, pageSize, false);
        List<FamilyMemberVO> list = new ArrayList<>();
        if (!CollectionUtils.isEmpty(dataList)) {
            for (FamilyData data : dataList) {
                ActorData actorData = actorDao.getActorDataFromCache(data.getOwnerUid());
                if (actorData == null) {
                    logger.error("can not find actor data. uid={}", data.getOwnerUid());
                    continue;
                }
                FamilyMemberVO vo = new FamilyMemberVO();
                vo.setUid(data.getOwnerUid());
                vo.setHead(actorData.getHead());
                vo.setSvipLevel(actorData.getSvipLevel());
                vo.setName(actorData.getName());
                vo.setRid(actorData.getRid());
                vo.setStrRid(actorData.getShowRid());
                vo.setCtime(data.getBecomeAgentTime());
                list.add(vo);
            }
        }
        pageVO.setList(list);
        pageVO.setTotal(familyDao.selectSubAgentCount(familyData.getId(), false));
        return pageVO;
    }

    /**
     * 移除公会成员
     */
    @Transactional(value = WahoMySQLBean.WAHO_TRANSACTION, rollbackFor = {Exception.class, RuntimeException.class})
    public void removeMember(String managerUid, FamilyDTO dto) {
        FamilyMemberData memberData = familyMemberDao.selectByUid(dto.getUid());
        if (memberData == null) {
            logger.info("not find family member data. uid={}", dto.getUid());
            return;
        }
        if (memberData.getRole() == 1) {
            throw new CommonH5Exception(new HttpCode(1, "不能移除公会长"));
        }
        if (isFamilyManager(managerUid)) {
            // 超管、运营负责人的账户进行移除主播操作需要先提交到审核列表进行审核，审核通过，操作才算生效：
            submitAnchorOptReview(memberData, managerUid, 0);
            return;
        }
        ActorData actorData = actorDao.getActorData(dto.getUid());
        if (actorData.getSvipLevel() > 0 && !dto.isConformSvip()) {
            logger.info("removeMember actor has svip. uid={}", actorData.getUid());
            throw new CommonH5Exception(new HttpCode(123, "SVIP%s级主播，移出主播SVIP等级将失效，确定移出吗？".formatted(actorData.getSvipLevel())));
        }
        familyMemberDao.deleteByFamilyIdAndUid(memberData.getFamilyId(), memberData.getUid(), "运营平台移除主播");
    }

    private void submitAnchorOptReview(FamilyMemberData memberData, String managerUid, int optType) {
        if (familyMemberOperateDao.selectCount(memberData.getUid(), memberData.getFamilyId(), managerUid, optType, 0) > 0) {
            throw new CommonH5Exception(new HttpCode(1, "请勿重复提交申请"));
        }
        FamilyMemberOperateData data = new FamilyMemberOperateData();
        data.setAid(memberData.getUid());
        data.setFamilyId(memberData.getFamilyId());
        data.setOptManagerUid(managerUid);
        data.setOptType(optType);
        data.setStatus(0);
        int nowTime = DateHelper.getNowSeconds();
        data.setOptTime(nowTime);
        data.setCtime(nowTime);
        familyMemberOperateDao.insert(data);
    }

    /**
     * 新增公会成员
     */
    public void addMember(String optUid, FamilyMemberDTO dto) {
        if (isFamilyManager(optUid)) {
            throw new CommonH5Exception(new HttpCode(1, "没有权限操作"));
        }
        ActorData actorData = actorDao.getActorByRidOrAlphaRid(dto.getRid().trim());
        if (null == actorData) {
            logger.error("cannot find actor data. rid={}", dto.getRid());
            throw new CommonH5Exception(1, "所填的用户id找不到对于的用户");
        }
        if (actorData.getSvipLevel() > 0 && !dto.isConformSvip()) {
            logger.info("add family member actor has svip. uid={}", actorData.getUid());
            throw new CommonH5Exception(new HttpCode(123, "SVIP%s级用户，加入公会SVIP等级将失效，确定添加吗？".formatted(actorData.getSvipLevel())));
        }
        if (!familyMemberDao.isDeviceUniqueAnchorAccount(actorData.getUid(), actorData.getTn_id())) {
            logger.error("The user's device already has a host and cannot be added. uid={} tnId={}", actorData.getUid(), actorData.getTn_id());
            throw new CommonH5Exception(1, "该用户的设备已存在主播账号，无法添加");
        }
        FamilyData familyData = familyDao.selectByIdFromCache(dto.getFamilyId());
        if (null == familyData) {
            logger.error("cannot find family data. familyId={}", dto.getFamilyId());
            throw new CommonH5Exception(1, "参数错误，找不到对应的公会");
        }
        FamilyMemberData data = familyMemberDao.selectByUid(actorData.getUid());
        if (data != null) {
            throw new CommonH5Exception(1, "所填的用户已是其他公会的成员");
        }
        int memberCount = familyMemberDao.selectMemberCount(familyData.getId());
        if (memberCount >= MAX_MEMBER) {
            throw new CommonH5Exception(1, "该公会成员数量已达到上限");
        }
        data = new FamilyMemberData();
        data.setUid(actorData.getUid());
        data.setFamilyId(dto.getFamilyId());
        data.setRole(3);
        data.setCtime(DateHelper.getNowSeconds());
        familyMemberDao.insert(data);
        // 发送通知
        sendOfficialMsg(actorData, familyData);
    }

    private void sendOfficialData(String toUid, String title, String body, int familyRid, int slang) {
        OfficialData officialData = new OfficialData();
        officialData.setTo_uid(toUid);
        officialData.setTitle(title);
        officialData.setBody(body);
        officialData.setValid(1);
        officialData.setAtype(20);
        officialData.setNews_type(0);
        officialData.setCtime(DateHelper.getNowSeconds());
        officialData.setAct(SLangType.ENGLISH == slang ? "View" : "شاهد");
        // 借用roomId字段下发公会id
        officialData.setRoom_id(String.valueOf(familyRid));
        officialMsgService.officialMsgPush(officialData);
    }

    /**
     * 新增公会长
     */
    private void insertFamilyPresident(int familyId, String uid) {
        FamilyMemberData data = familyMemberDao.selectByUid(uid);
        if (data != null) {
            FamilyData familyData = familyDao.selectByIdFromCache(data.getFamilyId());
            throw new CommonH5Exception(1, String.format("所填的公会长已是%s公会的成员", familyData != null ? familyData.getRid() : "其他"));
        }
        data = new FamilyMemberData();
        data.setUid(uid);
        data.setFamilyId(familyId);
        data.setRole(1);
        data.setCtime(DateHelper.getNowSeconds());
        familyMemberDao.insert(data);
    }

    @Cacheable(value = "getFamilyOptionList", cacheManager = CaffeineCacheConfig.EXPIRE_1M_AFTER_WRITE)
    public FamilyOptionVO getOptionList() {
        FamilyOptionVO vo = new FamilyOptionVO();
        vo.setCountriesList(countriesDao.getCountriesList());
        vo.setRegionList(REGION_LIST);
        List<Manager> familyManagerList = managerDao.findFamilyManagerList();
        if (CollectionUtils.isEmpty(familyManagerList)) {
            familyManagerList = Collections.emptyList();
        }
        List<String> managerList = familyManagerList.stream().filter(p -> p.getFamily_manage_role() == 1).map(Manager::getAccount).collect(Collectors.toList());
        managerList.add("无运营负责人");
        vo.setManagerList(managerList);
        List<String> superManagerList = familyManagerList.stream().filter(p -> p.getFamily_manage_role() == 2).map(Manager::getAccount).collect(Collectors.toList());
        superManagerList.add("无超管");
        vo.setSuperManagerList(superManagerList);
        return vo;
    }

    private void handleFamilyCondition(String uid, FamilyCondition condition) {
        if (StringUtils.hasLength(condition.getOwnerRid())) {
            ActorData owner = actorDao.getActorByRidOrAlphaRid(condition.getOwnerRid());
            condition.setOwnerUid(owner != null ? owner.getUid() : "");
        }
        Manager manager = managerDao.getDataByUid(uid);
        if (manager.getFamily_manage_role() == 1) {
            condition.setManager(manager.getAccount());
        } else if (manager.getFamily_manage_role() == 2) {
            getManagerList(condition, manager);
            condition.setSuperManager(manager.getAccount());
        } else {
            if (StringUtils.hasLength(condition.getSuperManager())) {
                Manager superManager = managerDao.getManager(condition.getSuperManager());
                if (superManager != null) {
                    getManagerList(condition, superManager);
                }
            }
        }
    }

    public void getManagerList(FamilyCondition condition, Manager manager) {
        List<Manager> lowerLevelManager = managerDao.getLowerLevelManager(manager.get_id().toString());
        List<String> managerList = CollectionUtil.getPropertyList(lowerLevelManager, Manager::getAccount, "---");
        managerList = CollectionUtils.isEmpty(managerList) ? Collections.singletonList("---") : managerList;
        condition.setManagerList(managerList);
    }

    public PageResultVO<FamilyAnchorVO> getAnchorList(FamilyCondition condition) {
        PageResultVO<FamilyAnchorVO> pageVO = new PageResultVO<>();
        int page = condition.getPage() != null && condition.getPage() != 0 ? condition.getPage() : 1;
        int pageSize = condition.getPageSize() != null && condition.getPageSize() != 0 ? condition.getPageSize() : 20;
        Integer familyId = null;
        if (StringUtils.hasLength(condition.getFamilyRid())) {
            try {
                FamilyData familyData = familyDao.selectByFamilyRid(Integer.parseInt(condition.getFamilyRid()));
                familyId = familyData != null ? familyData.getId() : null;
            } catch (NumberFormatException ignore) {

            }
        }
        String memberUid = "";
        if (StringUtils.hasLength(condition.getMemberRid())) {
            ActorData actor = actorDao.getActorByRidOrAlphaRid(condition.getMemberRid());
            memberUid = actor != null ? actor.getUid() : "";
        }
        IPage<FamilyMemberData> iPage = familyMemberDao.selectMemberPage(familyId, memberUid, page, pageSize);
        List<FamilyAnchorVO> list = new ArrayList<>();
        if (!CollectionUtils.isEmpty(iPage.getRecords())) {
            for (FamilyMemberData data : iPage.getRecords()) {
                ActorData actorData = actorDao.getActorDataFromCache(data.getUid());
                if (actorData == null) {
                    logger.error("can not find actor data. uid={}", data.getUid());
                    continue;
                }
                FamilyData familyData = familyDao.selectByIdFromCache(data.getFamilyId());
                if (familyData == null) {
                    logger.error("can not find family data. familyId={}", data.getFamilyId());
                    continue;
                }
                FamilyAnchorVO vo = new FamilyAnchorVO();
                vo.setUid(actorData.getUid());
                vo.setStrRid(actorData.getShowRid());
                vo.setRid(actorData.getRid());
                vo.setRole(data.getRole());
                vo.setHead(actorData.getHead());
                vo.setName(actorData.getName());
                vo.setSvipLevel(actorData.getSvipLevel());
                vo.setFamilyRid(familyData.getRid());
                ActorData owner = actorDao.getActorDataFromCache(familyData.getOwnerUid());
                vo.setOwnerName(owner != null ? owner.getName() : "");
                vo.setOwnerRid(owner != null ? owner.getRid() : null);
                vo.setOwnerStrRid(owner != null ? owner.getShowRid() : "");
                vo.setManage(familyData.getManager());
                vo.setSuperManager(familyData.getSuperManager());
                long registerTime = new ObjectId(actorData.getUid()).getTimestamp() * 1000L;
                vo.setRegisterTime(com.quhong.operation.utils.DateHelper.ARABIAN.timestampToDatetimeStr(registerTime));
                list.add(vo);
            }
        }
        pageVO.setList(list);
        pageVO.setTotal(iPage.getTotal());
        return pageVO;
    }

    private String getSuperManager(Map<String, Manager> accountManagerMap, Map<String, Manager> uidManagerMap, String managerName) {
        Manager manager = accountManagerMap.get(managerName);
        String superManagerName = "";
        if (null != manager) {
            Manager superManager = uidManagerMap.get(manager.getSuperior_uid());
            superManagerName = superManager != null ? superManager.getAccount() : "";
        }
        return superManagerName;
    }

    public void moveMember(String optUid, FamilyDTO dto) {
        if (isFamilyManager(optUid)) {
            throw new CommonH5Exception(new HttpCode(1, "没有权限操作"));
        }
        if (dto.getFamilyRid() == null || dto.getFamilyRid() == 0) {
            throw new CommonH5Exception(new HttpCode(1, "转入公会id不能为空"));
        }
        ActorData actorData = actorDao.getActorData(dto.getUid());
        if (null == actorData) {
            logger.error("cannot find actor data. uid={}", dto.getUid());
            throw new CommonH5Exception(HttpCode.PARAM_ERROR);
        }
        FamilyMemberData memberData = familyMemberDao.selectByUid(dto.getUid());
        if (memberData == null) {
            throw new CommonH5Exception(new HttpCode(1, "该用户不是主播"));
        }
        if (memberData.getRole() == 1) {
            throw new CommonH5Exception(new HttpCode(1, "公会长不能转会"));
        }
        FamilyData familyData = familyDao.selectByFamilyRid(dto.getFamilyRid());
        if (null == familyData || familyData.getStatus() != 1) {
            logger.error("cannot find family data. familyRid={}", dto.getFamilyRid());
            throw new CommonH5Exception(1, "找不到对应的公会或者公会已经被禁用");
        }
        int memberCount = familyMemberDao.selectMemberCount(familyData.getId());
        if (memberCount >= MAX_MEMBER) {
            throw new CommonH5Exception(1, "转入公会的成员数量已达到上限");
        }
        int beforeFamilyId = memberData.getFamilyId();
        int nowTime = DateHelper.getNowSeconds();
        memberData.setFamilyId(familyData.getId());
        memberData.setDevote(0L);
        memberData.setRole(3);
        memberData.setCtime(nowTime);
        familyMemberDao.update(memberData);
        JoinFamilyRecordData recordData = new JoinFamilyRecordData(actorData.getUid(), actorData.getTn_id(), familyData.getId(), 0, "公会成员转会");
        joinFamilyRecordDao.insert(recordData);
        recordData.setId(null);
        recordData.setFamilyId(beforeFamilyId);
        recordData.setAction(1);
        joinFamilyRecordDao.insert(recordData);
        actorDao.updateField(actorData.getUid(), "familyId", familyData.getId());
        // 发送通知
        sendOfficialMsg(actorData, familyData);
    }

    public void sendOfficialMsg(ActorData actorData, FamilyData familyData) {
        String title;
        String noticeBody;
        if (actorData.getSlang() == SLangType.ENGLISH) {
            title = "Join Family Notification";
            noticeBody = String.format("Congratulations! You have successfully joined the family 【%s】!", familyData.getName());
        } else {
            title = "إشعار الانضمام للعائلة";
            noticeBody = String.format("تهانينا! لقد انضممت بنجاح إلى العائلة 【%s】!", familyData.getName());
        }
        sendOfficialData(actorData.getUid(), title, noticeBody, familyData.getRid(), actorData.getSlang());
    }

    public PageResultVO<FamilyOperateVO> getFamilyOperateList(FamilyOperateCondition condition) {
        PageResultVO<FamilyOperateVO> pageVO = new PageResultVO<>();
        IPage<FamilyOperateData> iPage = familyOperateDao.selectPage(condition);
        List<FamilyOperateVO> list = new ArrayList<>();
        if (!CollectionUtils.isEmpty(iPage.getRecords())) {
            List<Manager> managers = managerDao.selectAll();
            Map<String, Manager> accountManagerMap = CollectionUtil.listToKeyMap(managers, Manager::getAccount);
            Map<String, Manager> uidManagerMap = CollectionUtil.listToKeyMap(managers, k -> k.get_id().toString());
            for (FamilyOperateData data : iPage.getRecords()) {
                FamilyOperateVO vo = new FamilyOperateVO();
                vo.setId(data.getId());
                Manager opt = managerDao.getDataByUid(data.getOptManagerUid());
                vo.setOptAccount(opt != null ? opt.getAccount() : "");
                vo.setOptAccountType(opt != null ? opt.getFamily_manage_role() : 0);
                vo.setOptType(data.getOptType());
                vo.setOptTime(data.getOptTime());
                vo.setStatus(data.getStatus());
                if (data.getFamilyId() != null) {
                    FamilyData familyData = familyDao.selectById(data.getFamilyId());
                    vo.setRid(familyData != null ? familyData.getRid() : null);
                } else {
                    vo.setRid(null);
                }
                vo.setName(data.getName());
                ActorData owner = actorDao.getActorDataFromCache(data.getOwnerUid());
                vo.setOwnerName(owner != null ? owner.getName() : "");
                vo.setOwnerRid(owner != null ? owner.getRid() : null);
                vo.setOwnerStrRid(owner != null ? owner.getShowRid() : "");
                vo.setManager(data.getManager());
                vo.setSuperManager(getSuperManager(accountManagerMap, uidManagerMap, data.getManager()));
                vo.setCtime(data.getCtime());
                list.add(vo);
            }
        }
        pageVO.setList(list);
        pageVO.setTotal(iPage.getTotal());
        return pageVO;
    }

    public PageResultVO<AnchorOperateVO> getAnchorOperateList(FamilyOperateCondition condition) {
        PageResultVO<AnchorOperateVO> pageVO = new PageResultVO<>();
        IPage<FamilyMemberOperateData> iPage = familyMemberOperateDao.selectPage(condition);
        List<AnchorOperateVO> list = new ArrayList<>();
        if (!CollectionUtils.isEmpty(iPage.getRecords())) {
            for (FamilyMemberOperateData data : iPage.getRecords()) {
                AnchorOperateVO vo = new AnchorOperateVO();
                vo.setId(data.getId());
                Manager opt = managerDao.getDataByUid(data.getOptManagerUid());
                vo.setOptAccount(opt != null ? opt.getAccount() : "");
                vo.setOptAccountType(opt != null ? opt.getFamily_manage_role() : 0);
                vo.setOptType(data.getOptType());
                vo.setOptTime(data.getOptTime());
                vo.setStatus(data.getStatus());
                ActorData actorData = actorDao.getActorDataFromCache(data.getAid());
                vo.setUid(actorData != null ? actorData.getUid() : "");
                vo.setRid(actorData != null ? actorData.getRid() : null);
                vo.setStrRid(actorData != null ? actorData.getShowRid() : "");
                vo.setName(actorData != null ? actorData.getName() : "");
                vo.setHead(actorData != null ? actorData.getHead() : "");
                FamilyMemberData memberData = familyMemberDao.selectByFamilyIdAndUid(data.getFamilyId(), data.getAid());
                vo.setRole(memberData != null ? memberData.getRole() : 3);
                FamilyData familyData = familyDao.selectById(data.getFamilyId());
                vo.setFamilyRid(familyData != null ? familyData.getRid() : null);
                vo.setFamilyName(familyData != null ? familyData.getName() : "");
                if (familyData != null) {
                    ActorData owner = actorDao.getActorDataFromCache(familyData.getOwnerUid());
                    vo.setOwnerName(owner != null ? owner.getName() : "");
                    vo.setOwnerRid(owner != null ? owner.getRid() : null);
                    vo.setOwnerStrRid(owner != null ? owner.getShowRid() : "");
                    vo.setManager(familyData.getManager());
                    vo.setSuperManager(familyData.getSuperManager());
                }
                vo.setCtime(data.getCtime());
                list.add(vo);
            }
        }
        pageVO.setList(list);
        pageVO.setTotal(iPage.getTotal());
        return pageVO;
    }

    @Transactional(value = WahoMySQLBean.WAHO_TRANSACTION, rollbackFor = {Exception.class, RuntimeException.class})
    public void familyOperateReview(FamilyOperateDTO dto) {
        FamilyOperateData data = familyOperateDao.selectById(dto.getId());
        if (data == null || data.getStatus() != 0) {
            throw new CommonH5Exception(HttpCode.PARAM_ERROR);
        }
        if (dto.getOpt() == 1) {
            data.setStatus(2);
        } else {
            if (data.getOptType() == 1) {
                // 禁用公会
                FamilyData familyData = familyDao.selectById(data.getFamilyId());
                if (familyData == null) {
                    logger.error("can not find family data. id={}", data.getFamilyId());
                    throw new CommonH5Exception(new HttpCode(1, "没有找到id对应的公会"));
                }
                if (familyData.getStatus() != 1) {
                    logger.info("family has been disabled. id={}", data.getFamilyId());
                    throw new CommonH5Exception(new HttpCode(1, "该公会已经被禁用"));
                }
                familyData.setStatus(2);
                familyDao.update(familyData);
                // 给公会全部成员发送公会被禁用消息
                List<FamilyMemberData> memberList = familyMemberDao.selectMemberList(familyData.getId());
                sendDisabledFamilyMsg(familyData.getId(), familyData.getName(), memberList);
            } else {
                // 创建公会
                if (familyDao.isBannedCreateFamily(data.getOwnerUid())) {
                    throw new CommonH5Exception(new HttpCode(1, "该用户被禁止创建公会"));
                }
                FamilyData familyData = new FamilyData();
                int rid = genFamilyRidRedis.getGenFamilyRid();
                if (rid == 0) {
                    throw new CommonH5Exception(new HttpCode(1, "初始化公会rid错误，请联系后台开发人员"));
                }
                familyData.setRid(rid);
                familyData.setName(data.getName());
                familyData.setHead(DEFAULT_HEAD);
                familyData.setStatus(1);
                familyData.setOwnerUid(data.getOwnerUid());
                familyData.setCountry(data.getCountry());
                familyData.setRegion(data.getRegion());
                familyData.setAgentContact(data.getAgentContact());
                Manager manager = managerDao.getManager(data.getManager());
                familyData.setServiceUid(null == manager ? "" : manager.getService_uid());
                familyData.setManager(data.getManager());
                String superManagerName = "";
                if (null != manager && StringUtils.hasLength(manager.getSuperior_uid())) {
                    Manager superManager = managerDao.getDataByUid(manager.getSuperior_uid());
                    superManagerName = superManager != null ? superManager.getAccount() : "";
                }
                familyData.setSuperManager(superManagerName);
                familyData.setCtime(DateHelper.getNowSeconds());
                familyDao.insert(familyData);
                data.setFamilyId(familyData.getId());
                insertFamilyPresident(familyData.getId(), data.getOwnerUid());
                EventDTO eventDTO = new EventDTO(new FamilyLogEvent(familyData.getId(), familyData.getRid(), familyData.getOwnerUid(), familyData.getCtime()));
                eventDTO.setEventId("family:" + familyData.getId());
                eventReport.trackUpdate(eventDTO);
            }
            data.setStatus(1);
        }
        data.setOptTime(DateHelper.getNowSeconds());
        familyOperateDao.update(data);
    }

    @Transactional(value = WahoMySQLBean.WAHO_TRANSACTION, rollbackFor = {Exception.class, RuntimeException.class})
    public void anchorOperateReview(FamilyOperateDTO dto) {
        FamilyMemberOperateData data = familyMemberOperateDao.selectById(dto.getId());
        if (data == null || data.getStatus() != 0) {
            throw new CommonH5Exception(HttpCode.PARAM_ERROR);
        }
        if (dto.getOpt() == 1) {
            data.setStatus(2);
        } else {
            FamilyMemberData memberData = familyMemberDao.selectByUid(data.getAid());
            if (memberData == null) {
                logger.info("not find family member data. uid={}", data.getAid());
                return;
            }
            if (memberData.getRole() == 1) {
                throw new CommonH5Exception(new HttpCode(1, "不能移除公会长"));
            }
            familyMemberDao.deleteByFamilyIdAndUid(memberData.getFamilyId(), memberData.getUid(), "运营平台移除主播操作审核通过");
            data.setStatus(1);
        }
        data.setOptTime(DateHelper.getNowSeconds());
        familyMemberOperateDao.update(data);
    }

    public ManagerAccountOptionVO getManagerAccountOptionList() {
        ManagerAccountOptionVO vo = new ManagerAccountOptionVO();
        List<ManagerAccountOptionVO.Option> list = new ArrayList<>();
        List<Manager> familyManagerList = managerDao.findFamilyManagerList();
        if (!CollectionUtils.isEmpty(familyManagerList)) {
            for (Manager manager : familyManagerList) {
                list.add(new ManagerAccountOptionVO.Option(manager.get_id().toString(), manager.getAccount()));
            }
        }
        vo.setList(list);
        return vo;
    }

    public PageResultVO<QuitRequestRecordVO> getQuitRequestRecordList(QuitRequestCondition condition) {
        PageResultVO<QuitRequestRecordVO> resultVO = new PageResultVO<>();
        handleQuitRequestCondition(condition);
        IPage<QuitFamilyRequestData> iPage = quitFamilyRequestDao.selectPage(condition.getFamilyId(), condition.getUid(), condition.getStatus() == 0 ? null : Collections.singletonList(condition.getStatus()),
                condition.getStartTime(), condition.getEndTime(), condition.getPage(), condition.getPageSize());
        List<QuitRequestRecordVO> list = new ArrayList<>();
        if (!CollectionUtils.isEmpty(iPage.getRecords())) {
            List<Manager> managers = managerDao.selectAll();
            Map<String, Manager> accountManagerMap = CollectionUtil.listToKeyMap(managers, Manager::getAccount);
            Map<String, Manager> uidManagerMap = CollectionUtil.listToKeyMap(managers, k -> k.get_id().toString());
            for (QuitFamilyRequestData data : iPage.getRecords()) {
                QuitRequestRecordVO vo = buildQuitRequestRecordVO(data);
                vo.setStatus(getStrStatus(data.getStatus()));
                vo.setIntStatus(data.getStatus());
                list.add(vo);
            }
        }
        resultVO.setList(list);
        resultVO.setTotal(iPage.getTotal());
        return resultVO;
    }

    public PageResultVO<QuitRequestRecordVO> getQuitRequestDisputeRecordList(QuitRequestCondition condition) {
        PageResultVO<QuitRequestRecordVO> resultVO = new PageResultVO<>();
        handleQuitRequestCondition(condition);
        IPage<QuitFamilyRequestData> iPage = quitFamilyRequestDao.selectPage(condition.getFamilyId(), condition.getUid(),
                condition.getStatus() == 0 ? List.of(3, 4, 5) : Collections.singletonList(condition.getStatus()),
                condition.getStartTime(), condition.getEndTime(), condition.getPage(), condition.getPageSize());
        List<QuitRequestRecordVO> list = new ArrayList<>();
        if (!CollectionUtils.isEmpty(iPage.getRecords())) {
            for (QuitFamilyRequestData data : iPage.getRecords()) {
                QuitRequestRecordVO vo = buildQuitRequestRecordVO(data);
                vo.setStatus(getStrStatus(data.getStatus()));
                vo.setIntStatus(data.getStatus());
                list.add(vo);
            }
        }
        resultVO.setList(list);
        resultVO.setTotal(iPage.getTotal());
        return resultVO;
    }

    public QuitRequestRecordVO buildQuitRequestRecordVO(QuitFamilyRequestData data) {
        QuitRequestRecordVO vo = new QuitRequestRecordVO();
        ActorData actorData = actorDao.getActorDataFromCache(data.getUid());
        vo.setId(data.getId());
        vo.setUid(data.getUid());
        vo.setRid(actorData != null ? actorData.getRid() : null);
        vo.setStrRid(actorData != null ? actorData.getShowRid() : "");
        vo.setName(actorData != null ? actorData.getName() : "");
        FamilyData familyData = familyDao.selectByIdFromCache(data.getFamilyId());
        if (familyData != null) {
            vo.setFamilyRid(familyData.getRid());
            vo.setFamilyName(familyData.getName());
            vo.setManager(familyData.getManager());
            vo.setSuperManager(familyData.getSuperManager());
            ActorData owner = actorDao.getActorDataFromCache(familyData.getOwnerUid());
            vo.setOwnerUid(familyData.getOwnerUid());
            vo.setOwnerRid(owner != null ? owner.getRid() : null);
            vo.setOwnerStrRid(owner != null ? owner.getShowRid() : "");
            vo.setOwnerName(owner != null ? owner.getName() : "");
            int joinFamilyTime = joinFamilyRecordDao.getJoinFamilyTime(familyData.getId(), data.getUid());
            vo.setJoinTime(DateHelper.ARABIAN.formatDateTime(new Date(joinFamilyTime * 1000L)));
        }
        vo.setCtime(DateHelper.ARABIAN.formatDateTime(new Date(data.getCtime() * 1000L)));
        vo.setMtime(DateHelper.ARABIAN.formatDateTime(new Date(data.getMtime() * 1000L)));
        return vo;
    }

    public void changeStatus(int id, int status) {
        QuitFamilyRequestData requestData = quitFamilyRequestDao.getById(id);
        if (requestData.getStatus() != 3) {
            return;
        }
        if (status == 4) {
            // 4公会长拒绝后官方同意
            FamilyMemberData data = familyMemberDao.selectByFamilyIdAndUid(requestData.getFamilyId(), requestData.getUid());
            if (data != null) {
                familyMemberDao.deleteByFamilyIdAndUid(data.getFamilyId(), requestData.getUid(), "运营平台操作退出公会申请");
            }
        }
        quitFamilyRequestDao.changeStatus(id, status);
    }

    private String getStrStatus(Integer status) {
        if (status == 1) {
            return "待公会长审核";
        } else if (status == 2) {
            return "公会长已同意";
        } else if (status == 3) {
            return "公会长已拒绝待处理";
        } else if (status == 4) {
            return "公会长拒绝后官方同意";
        } else if (status == 5) {
            return "公会长拒绝后官方拒绝";
        } else {
            return "未知";
        }
    }

    private void handleQuitRequestCondition(QuitRequestCondition condition) {
        if (StringUtils.hasLength(condition.getRid())) {
            ActorData actorData = actorDao.getActorByRidOrAlphaRid(condition.getRid());
            condition.setUid(actorData != null ? actorData.getUid() : "---");
        }
        if (condition.getFamilyRid() != null) {
            FamilyData familyData = familyDao.selectByFamilyRid(condition.getFamilyRid());
            condition.setFamilyId(familyData != null ? familyData.getId() : 0);
        }
        if (StringUtils.hasLength(condition.getStartDate()) && StringUtils.hasLength(condition.getEndDate())) {
            Integer[] timeArr = DateHelper.ARABIAN.getStartOrEndSeconds(condition.getStartDate(), condition.getEndDate());
            condition.setStartTime(timeArr[0]);
            condition.setEndTime(--timeArr[1]);
        }
    }

    public PageResultVO<FamilyDismissRecordVO> getFamilyDismissRecords(FamilyDismissCondition condition) {
        String ownerUid = "";
        if (StringUtils.hasLength(condition.getRid())) {
            ActorData actorData = actorDao.getActorByRidOrAlphaRid(condition.getRid());
            ownerUid = actorData != null ? actorData.getUid() : "---";
        }
        Integer familyId = null;
        if (condition.getFamilyRid() != null) {
            FamilyData familyData = familyDao.selectByFamilyRid(condition.getFamilyRid());
            familyId = familyData != null ? familyData.getId() : 0;
        }
        int count = familyDismissRecordDao.selectCount(familyId, ownerUid, condition.getManager(), condition.getSuperManager(), condition.getBanStatus(), condition.getType());
        if (count == 0) {
            return new PageResultVO<>(count, Collections.emptyList());
        }
        List<FamilyDismissRecordData> dataList = familyDismissRecordDao.selectPage(familyId, ownerUid, condition.getManager(), condition.getSuperManager(), condition.getBanStatus(), condition.getType(), condition.getStart(), condition.getPageSize());
        List<FamilyDismissRecordVO> list = new ArrayList<>();
        if (!CollectionUtils.isEmpty(dataList)) {
            for (FamilyDismissRecordData data : dataList) {
                FamilyDismissRecordVO vo = new FamilyDismissRecordVO();
                FamilyData familyData = familyDao.selectById(data.getFamilyId());
                vo.setId(data.get_id().toString());
                vo.setFamilyRid(familyData.getRid() + "");
                vo.setFamilyName(familyData.getName());
                vo.setOwnerUid(data.getOwnerUid());
                ActorData actorData = actorDao.getActorDataFromCache(data.getOwnerUid());
                vo.setOwnerRid(actorData.getRid() + "");
                vo.setOwnerStrRid(actorData.getShowRid());
                vo.setOwnerName(actorData.getName());
                vo.setBanStatus(data.getBanStatus());
                vo.setType(data.getType());
                vo.setManager(data.getManager());
                vo.setSuperManager(data.getSuperManager());
                vo.setCountry(familyData.getCountry());
                vo.setCtime(familyData.getCtime());
                vo.setDismissTime(data.getCtime());
                vo.setMemberList(getMemberList(data.getMemberList()));
                if (StringUtils.hasLength(data.getOptUid()) && !"system".equals(data.getOptUid())) {
                    vo.setOptName(managerDao.getAccountByUid(data.getOptUid()));
                } else {
                    vo.setOptName("系统");
                }
                list.add(vo);
            }
        }
        return new PageResultVO<>(count, list);
    }

    private List<FamilyMemberVO> getMemberList(List<FamilyMemberData> dataList) {
        if (CollectionUtils.isEmpty(dataList)) {
            return Collections.emptyList();
        }
        List<FamilyMemberVO> list = new ArrayList<>();
        for (FamilyMemberData data : dataList) {
            ActorData actorData = actorDao.getActorDataFromCache(data.getUid());
            FamilyMemberVO vo = new FamilyMemberVO();
            vo.setUid(data.getUid());
            vo.setRole(data.getRole());
            vo.setCtime(data.getCtime());
            vo.setHead(actorData.getHead());
            vo.setSvipLevel(actorData.getSvipLevel());
            vo.setName(actorData.getName());
            vo.setRid(actorData.getRid());
            vo.setStrRid(actorData.getShowRid());
            list.add(vo);
        }
        return list;
    }

    public void updateBanStatus(String aid, int banStatus) {
        if (actorDao.getActorDataFromCache(aid) == null) {
            throw new CommonH5Exception(HttpCode.PARAM_ERROR);
        }
        if (banStatus == 0) {
            familyDao.removeBanCreateFamilyUser(aid);
        } else if (banStatus == 1) {
            familyDao.addBanCreateFamilyUser(aid);
        } else {
            throw new CommonH5Exception(HttpCode.PARAM_ERROR);
        }
        familyDismissRecordDao.updateBanStatus(aid, banStatus);
    }

    public void addSubAgent(String optUid, FamilyMemberDTO dto) {
        if (isFamilyManager(optUid)) {
            throw new CommonH5Exception(HttpCode.PARAM_ERROR);
        }
        FamilyData supAgent = familyDao.selectById(dto.getFamilyId());
        if (supAgent == null) {
            throw new CommonH5Exception(HttpCode.PARAM_ERROR);
        }
        ActorData actorData = actorDao.getActorByRidOrAlphaRid(dto.getRid().trim());
        if (actorData == null) {
            logger.error("can not find actor data. rid={}", dto.getRid());
            throw new CommonH5Exception(new HttpCode(1, "用户id输入错误，不存在该id的用户"));
        }
        FamilyMemberData memberData = familyMemberDao.selectByUid(actorData.getUid());
        if (memberData == null) {
            if (familyDao.isBannedCreateFamily(actorData.getUid())) {
                throw new CommonH5Exception(new HttpCode(1, "该用户被禁止创建公会"));
            }
            FamilyData subAgent = new FamilyData();
            int rid = genFamilyRidRedis.getGenFamilyRid();
            if (rid == 0) {
                throw new CommonH5Exception(HttpCode.SERVER_ERROR);
            }
            subAgent.setRid(rid);
            subAgent.setPid(supAgent.getId());
            String fName = "Family" + rid;
            subAgent.setName(fName);
            subAgent.setHead(DEFAULT_HEAD);
            subAgent.setOwnerUid(actorData.getUid());
            subAgent.setCountry(supAgent.getCountry());
            subAgent.setRegion(supAgent.getRegion());
            subAgent.setAgentContact(supAgent.getAgentContact());
            subAgent.setManager(supAgent.getManager());
            subAgent.setSuperManager(supAgent.getSuperManager());
            subAgent.setServiceUid(supAgent.getServiceUid());
            subAgent.setStatus(1);
            subAgent.setCascadeLevel(supAgent.getCascadeLevel() + 1);
            subAgent.setBecomeAgentTime(DateHelper.getNowSeconds());
            subAgent.setCtime(DateHelper.getNowSeconds());
            familyDao.insert(subAgent);
            EventDTO eventDTO = new EventDTO(new FamilyLogEvent(subAgent.getId(), subAgent.getRid(), subAgent.getOwnerUid(), subAgent.getCtime()));
            eventDTO.setEventId("family:" + subAgent.getId());
            eventReport.trackUpdate(eventDTO);
            FamilyMemberData data = new FamilyMemberData();
            data.setUid(subAgent.getOwnerUid());
            data.setFamilyId(subAgent.getId());
            data.setRole(1);
            data.setCtime(DateHelper.getNowSeconds());
            familyMemberDao.insert(data);
        } else {
            FamilyData familyData = familyDao.selectById(memberData.getFamilyId());
            if (!familyData.getOwnerUid().equals(actorData.getUid())) {
                throw new CommonH5Exception(new HttpCode(1, "该用户是%s公会的成员".formatted(familyData.getRid())));
            }
            if (familyData.getId().equals(dto.getFamilyId())) {
                throw new CommonH5Exception(HttpCode.PARAM_ERROR);
            }
            if (familyData.getPid() != null && familyData.getPid() != 0) {
                throw new CommonH5Exception(new HttpCode(1, "该用户已存在上级代理"));
            }
            familyDao.updatePid(familyData.getId(), supAgent.getId(), supAgent.getCascadeLevel() + 1);
        }
    }

    public void removeSubAgent(String optUid, String aid) {
        if (isFamilyManager(optUid)) {
            throw new CommonH5Exception(HttpCode.PARAM_ERROR);
        }
        FamilyData agent = familyDao.selectByOwnerUid(aid);
        if (agent == null || agent.getPid() == 0) {
            return;
        }
        familyDao.updatePid(agent.getId(), 0, 0);
    }

    /**
     * 加入/退出公会记录查询
     * @param condition
     * @return com.quhong.operation.share.vo.PageResultVO<com.quhong.operation.share.vo.JoinOrExitFamilyRecordVO>
     * @description TODO
     */

    public PageResultVO<JoinOrExitFamilyRecordVO> getJoinOrExitRecordQuery(JoinOrExitFamilyRecordReqConditon condition) {
        PageResultVO<JoinOrExitFamilyRecordVO> resultVO = new PageResultVO<>();
        //1.构造条件
        LambdaQueryWrapper<JoinFamilyRecordData> wrapper = new LambdaQueryWrapper<>();
        handleJoinOrExitRecordQuery(condition,wrapper);
        //2.查询加入公会记录表
        IPage<JoinFamilyRecordData> page = joinFamilyRecordDao.selectPage(new Page<>(condition.getPage(), condition.getPageSize()), wrapper);
        //3.构造完整Vo数据
        List<JoinOrExitFamilyRecordVO> list = new ArrayList<>();
        if (!CollectionUtils.isEmpty(page.getRecords())) {
            for(JoinFamilyRecordData record : page.getRecords()){
                JoinOrExitFamilyRecordVO vo = new JoinOrExitFamilyRecordVO();
                ActorData actorData = actorDao.getActorDataFromCache(record.getUid());
                vo.setId(record.getId());
                vo.setUid(record.getUid());
                vo.setRid(actorData != null ? actorData.getRid() : null);
                vo.setStrRid(actorData != null ? actorData.getShowRid() : "");
                vo.setName(actorData != null ? actorData.getName() : "");
                FamilyData familyData = familyDao.selectByIdFromCache(record.getFamilyId());
                if (familyData != null) {
                    vo.setFamilyRid(familyData.getRid());
                    vo.setFamilyName(familyData.getName());
                    vo.setManager(familyData.getManager());
                    vo.setSuperManager(familyData.getSuperManager());
                    ActorData owner = actorDao.getActorDataFromCache(familyData.getOwnerUid());
                    vo.setOwnerUid(familyData.getOwnerUid());
                    vo.setOwnerRid(owner != null ? owner.getRid() : null);
                    vo.setOwnerStrRid(owner != null ? owner.getShowRid() : "");
                    vo.setOwnerName(owner != null ? owner.getName() : "");
                }
                //如果该记录是退出还需要查询显示加入时间
                if (record.getAction() == ActionEnum.EXIT.getValue()){
                    int joinFamilyTime = joinFamilyRecordDao.getJoinFamilyTime(record.getFamilyId(), record.getUid(), record.getCtime());
                    vo.setJoinTime(DateHelper.ARABIAN.formatDateTime(new Date(joinFamilyTime * 1000L)));
                    vo.setExitTime(DateHelper.ARABIAN.formatDateTime(new Date(record.getCtime() * 1000L)));
                }else {
                    vo.setJoinTime(DateHelper.ARABIAN.formatDateTime(new Date(record.getCtime() * 1000L)));
                }
                vo.setActionStr(ActionEnum.JOIN.getName());
                if (record.getAction() == ActionEnum.EXIT.getValue()){
                    vo.setActionStr(ActionEnum.EXIT.getName());
                    vo.setExitStatus(record.getRemark());
                }
                list.add(vo);
            }
            resultVO.setList(list);
            resultVO.setTotal(page.getTotal());
        }
        return resultVO;
    }

    /**
     * 构造条件
     * @param condition
     * @param wrapper
     * @return com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper
     */

    private void handleJoinOrExitRecordQuery(JoinOrExitFamilyRecordReqConditon condition,
                                             LambdaQueryWrapper<JoinFamilyRecordData> wrapper) {
        // 公会长rid不为空,构造公会id条件
        if (StringUtils.hasLength(condition.getOwnerStrRid())) {
            ActorData actorData = null;
            //判断id是不是uid
            if (StringUtils.hasLength(condition.getOwnerStrRid()) && condition.getOwnerStrRid().length() == 24) {
                actorData = actorDao.getActorData(condition.getOwnerStrRid());
            } else {
                actorData = actorDao.getActorByRidOrAlphaRidFromDb(condition.getOwnerStrRid());
            }
            if (Objects.nonNull(actorData) && Objects.nonNull(actorData.getUid())) {
                List<FamilyData> familyDataList = familyDao.selectListByOwnerUid(actorData.getUid());
                if (!CollectionUtils.isEmpty(familyDataList)) {
                    wrapper.in(JoinFamilyRecordData::getFamilyId, familyDataList.stream().map(FamilyData::getId).collect(Collectors.toList()));
                }
            } else {
                wrapper.eq(JoinFamilyRecordData::getFamilyId, -1);
            }
        }

        // 主播rid不为空,构造主播id条件
        if (StringUtils.hasLength(condition.getRid())){
            ActorData actorData = actorDao.getActorByRidOrAlphaRid(condition.getRid());
            if (actorData != null) {
                wrapper.eq(JoinFamilyRecordData::getUid,actorData.getUid());
            }else {
                wrapper.eq(JoinFamilyRecordData::getUid, -1);
            }
        }
        // 构造公会id条件
        if (Objects.nonNull(condition.getFamilyRid())) {
            FamilyData familyData = familyDao.selectByFamilyRid(condition.getFamilyRid());
            if (familyData != null) {
                wrapper.eq(JoinFamilyRecordData::getFamilyId,familyData.getId());
            }else {
                wrapper.eq(JoinFamilyRecordData::getFamilyId, -1);
            }
        }
        // 构造退出状态条件 退出状态有值且类型不为加入
        if (StringUtils.hasLength(condition.getExitStatus())) {
            Integer action = condition.getAction();
            if (action == null || action != ActionEnum.JOIN.getValue()) {
                wrapper.eq(JoinFamilyRecordData::getRemark, condition.getExitStatus());
            }
        }
        // 构造类型条件 0加入 1退出
        wrapper.eq(Objects.nonNull(condition.getAction()),
                JoinFamilyRecordData::getAction, condition.getAction());
        // 构造开始时间，结束时间条件
        if (StringUtils.hasLength(condition.getStartDate()) && StringUtils.hasLength(condition.getEndDate())) {
            Integer[] timeArr = DateHelper.ARABIAN.getStartOrEndSeconds(condition.getStartDate(), condition.getEndDate());
            wrapper.ge(JoinFamilyRecordData::getCtime, timeArr[0]);
            wrapper.lt(JoinFamilyRecordData::getCtime, --timeArr[1]);
        }
        wrapper.orderByDesc(JoinFamilyRecordData::getCtime);
    }
}
