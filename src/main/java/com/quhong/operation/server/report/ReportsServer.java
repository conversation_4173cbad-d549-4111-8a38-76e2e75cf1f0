package com.quhong.operation.server.report;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.quhong.core.date.DateSupport;
import com.quhong.data.ActorData;
import com.quhong.datas.DayTimeData;
import com.quhong.enums.ClientOS;
import com.quhong.enums.ReportContentType;
import com.quhong.mongo.config.MongoBean;
import com.quhong.mongo.dao.ActorDao;
import com.quhong.mysql.dao.*;
import com.quhong.mysql.data.*;
import com.quhong.mysql.slave_mapper.waho_log.ReportSlaveMapper;
import com.quhong.mysql.slave_mapper.waho_room.RoomMsgMapper;
import com.quhong.operation.common.ApiResult;
import com.quhong.operation.common.HttpResult;
import com.quhong.operation.constant.CountryCodeToName;
import com.quhong.operation.country.CountryQuery;
import com.quhong.operation.dao.*;
import com.quhong.operation.enums.MsgType;
import com.quhong.operation.server.EnterRoomServer;
import com.quhong.operation.share.data.AggStatData;
import com.quhong.operation.share.data.CountryData;
import com.quhong.operation.share.el.MoneyDetailES;
import com.quhong.operation.share.mongobean.*;
import com.quhong.operation.share.mysql.Dau;
import com.quhong.operation.share.mysql.EnterRoom;
import com.quhong.operation.share.mysql.MysqlMsgRecordData;
import com.quhong.operation.share.mysql.RoomMsg;
import com.quhong.operation.share.tool.TotalVO;
import com.quhong.operation.share.vo.*;
import com.quhong.operation.share.vo.reports.*;
import com.quhong.operation.share.vo.reports.money.MoneyDetailTypeVO;
import com.quhong.operation.share.vo.reports.money.MoneyDetailVO;
import com.quhong.operation.share.vo.reports.money.detail.AllMoneyDetailVO;
import com.quhong.operation.share.vo.reports.money.detail.ConsumeMoneyDetailVO;
import com.quhong.operation.share.vo.reports.money.detail.InMoneyDetailVO;
import com.quhong.operation.share.vo.reports.money.detail.UserBeansWaterVO;
import com.quhong.operation.share.vo.reports.msg.MsgGiftVO;
import com.quhong.operation.share.vo.user.ColumnChartVO;
import com.quhong.operation.utils.CollectionUtil;
import com.quhong.operation.utils.DateHelper;
import com.quhong.operation.utils.ExcelUtils;
import com.quhong.operation.utils.StringUtil;
import com.quhong.service.redis.RechargeRedis;
import jakarta.annotation.Resource;
import org.bson.types.ObjectId;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.data.mongodb.core.query.Criteria;
import org.springframework.data.mongodb.core.query.Query;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import org.springframework.util.DigestUtils;
import org.springframework.util.StringUtils;

import java.math.BigDecimal;
import java.nio.charset.StandardCharsets;
import java.text.NumberFormat;
import java.time.LocalDate;
import java.util.*;
import java.util.regex.Pattern;

/**
 * <AUTHOR>
 * @date 2020/6/11
 */
@Service
public class ReportsServer {

    private final static Logger logger = LoggerFactory.getLogger(ReportsServer.class);

    // event 右滑(喜欢)1、超级喜欢2、匹配成功3、发消息4、回复消息5、不喜欢6
    public static final int ALL = -1;

    @Autowired
    private OperationActorDao actorDao;
    @Autowired
    private RoomTimeDao roomTimeDao;
    @Autowired
    private RoomMsgDao roomMsgDao;
    @Autowired
    private RoomMicStatDao roomMicDao;
    @Autowired
    private FollowRoomOpDao followRoomOpDao;
    @Autowired
    private EnterRoomServer enterRoomServer;
    @Autowired
    private GiftRecordMgDao giftRecordMgDao;
    @Autowired
    private AdminDauDao dauDao;
    @Autowired
    private FriendshipDao friendshipDao;
    @Resource(name = MongoBean.MOVIES)
    private MongoTemplate mongoTemp1;
    @Resource(name = "elasticsearchDao")
    private ElasticsearchDao esServer;
    @Autowired
    private OperationFollowDao operationFollowDao;
    @Autowired
    private EnterRoomDao enterRoomDao;
    @Autowired
    private ActorStatDao actorStatDao;
    @Autowired
    private NewUserHonorStatDao newUserHonorDao;
    @Autowired
    private MoneyDetailStatDao moneyDetailStatDao;
    @Autowired
    private RoomOnlineDao roomOnlineDao;
    @Autowired
    private RoomUserStatDao roomUserStatDao;
    @Autowired
    private RoomUserOnlineSlaveDao roomUserOnlineSlaveDao;
    @Autowired
    private BeautifulRidChangeLogOPDao beautifulRidChangeLogOPDao;
    @Resource
    private MsgRecordDao msgRecordDao;
    @Resource
    private GiftDao giftDao;
    @Resource
    private ReportsServer reportsServer; // 解决缓存失效
    @Resource
    private ReportLogStatDao reportLogStatDao;
    @Resource
    private FeedbackHandleDao feedbackHandleDao;
    @Resource
    private ManagerDao managerDao;
    @Resource
    private ReportSlaveMapper reportSlaveMapper;
    @Autowired
    private VipInfoDao vipInfoDao;
    @Autowired
    private CountryQuery countryQuery;
    @Resource
    private RoomUserOnlineDao roomUserOnlineDao;
    @Resource
    private ActorDao actorCoreDao;
    @Resource
    private RechargeDailyInfoDao rechargeDailyInfoDao;
    @Resource
    private DetectUserRecordDao detectUserRecordDao;
    @Resource
    private DiscernImageDao discernImageDao;

    private static final Pattern intPattern = Pattern.compile("[^0-9]");

    /**
     * 获取用户基本信息
     *
     * @param start   开始时间
     * @param end     结束时间
     * @param ridList 哪些用户
     * @return 填充数据
     */
    public ApiResult<List<ActorBaseInfoVO>> listActorInfoByRid(Integer start, Integer end, List<String> ridList) {
        ApiResult<List<ActorBaseInfoVO>> result = new ApiResult<>();
        List<ActorBaseInfoVO> lists = new LinkedList<>();

        // 获取时间段内的日期数组
        String[] dateDiffArray = DateHelper.ARABIAN.getDateDiffArray(start, end);
        List<Integer> rids = new ArrayList<>();
        for (String rid : ridList) {
            String s = rid.trim();
            //去掉非数字的所有字符串
            s = intPattern.matcher(s).replaceAll("");
            if (StringUtils.isEmpty(s)) {
                logger.info("rid is not a number, rid={}", rid);
                continue;
            }
            rids.add(Integer.parseInt(s));
        }
        List<Actor> actorList = actorStatDao.listByRid(rids);
        Map<String, Actor> actorMap = new HashMap<>(actorList.size());
        Set<String> uidSet = new HashSet<>(actorList.size());
        for (Actor actor : actorList) {
            String rid = actor.getRid() + "";
            actorMap.put(rid, actor);
            uidSet.add(actor.get_id().toString());
        }
        //获取靓号信息
        Map<Integer, Actor> beautifulActorMap = listBeautifulActorMap(rids);
        logger.info("actor info beautiful user size = {}", beautifulActorMap.size());
        // 用户等级
        List<UserLevel> userLevels = mongoTemp1.find(new Query(Criteria.where("uid").in(uidSet)), UserLevel.class);
        Map<String, UserLevel> userLevelMap = CollectionUtil.listToKeyMap(userLevels, UserLevel::getUid);
        // 获取用户在房间内时长
        // Map<String, Integer> totalInRoomTimeMap = totalInRoomTime(uidSet, dateDiffArray);
        Map<String, Integer> totalInRoomTimeMap = roomUserOnlineDao.userOnRoomTimeByUidSet(start, end, uidSet);
        // 获取用户时间段内在房消费
        Map<String, Integer> roomDevoteSumMap = roomDevoteSum(uidSet, start, end);
        // 获取时间段内充值合计
        Map<String, Integer> rechargeCountMap = rechargeCount(uidSet, start, end);
        // 用户上麦时长
        Map<String, Integer> UpMicTimeMap = roomMicDao.actorAddUpMicTimeByUidSet(start, end, uidSet);
        // 用户粉丝数
        List<AggStatData> fansList = operationFollowDao.listStatByAid(uidSet);
        Map<String, AggStatData> fansStatMap = new HashMap<>();
        for (AggStatData aggStatData : fansList) {
            fansStatMap.put(aggStatData.getUid(), aggStatData);
        }
        logger.info("actor info fans size = {}", fansList.size());
        // 荣耀积分
        List<NewUserHonor> honorList = newUserHonorDao.findHonorByUid(uidSet);
        Map<String, NewUserHonor> honorMap = new HashMap<>();
        for (NewUserHonor newUserHonor : honorList) {
            honorMap.put(newUserHonor.get_id(), newUserHonor);
        }
        logger.info("actor info honor size = {}", honorList.size());
        // 获取房间关注人数
        Set<String> roomIdSet = new HashSet<>();
        for (String uid : uidSet) {
            String roomId = "r:" + uid;
            roomIdSet.add(roomId);
        }
        List<AggStatData> followRoomActorList = followRoomOpDao.roomFollowActorStat(start, end, roomIdSet);
        Map<String, AggStatData> followRoomActorMap = new HashMap<>();
        for (AggStatData aggStatData : followRoomActorList) {
            followRoomActorMap.put(aggStatData.getUid(), aggStatData);
        }
        logger.info("actor info follow room actor size = {}", followRoomActorList.size());
        // 获取房间送礼人数
//        Map<String, Integer> roomSendGiftUserMap = giftRecordMgDao.getRoomSendGiftUserStat(start, end, roomIdSet);
//        logger.info("actor info get room send gift user stat size = {}",roomSendGiftUserMap.size());
        for (Integer fromRid : rids) {
            String fromRidStr = fromRid + "";
            ActorBaseInfoVO actorBaseInfoVO = new ActorBaseInfoVO();
            actorBaseInfoVO.setFromRid(fromRidStr);
            Actor actor = actorMap.get(fromRidStr);
            // 通过字符串fromRid获取actor
            if (actor == null) {
                actor = beautifulActorMap.get(fromRidStr);
            }
            // 如果actor是空的，则跳过此次循环
            if (null == actor || null == actor.get_id()) {
                logger.error("actor not exist;before rid = {}", fromRidStr);
                continue;
            }

            String uid = actor.get_id().toString();
            actorBaseInfoVO.setAfterRid(actor.getRid());// 返回用户最新rid

            // 获取用户等级
            UserLevel uLevel = userLevelMap.get(uid);
            int level = null == uLevel || 1 > uLevel.getLevel() ? -1 : uLevel.getLevel();
            //是否是安卓用户
            String isAndroid = StringUtil.isEmptyOrBlank(actor.getOs()) ? "Yes" : "no";

            int inRoomTime = totalInRoomTimeMap.getOrDefault(uid, 0);
            int roomDevoteSum = roomDevoteSumMap.getOrDefault(uid, 0);
            int rechargeCount = rechargeCountMap.getOrDefault(uid, 0);

            // 是mongo数据生成的时间视为注册时间
            int registerTime = actor.get_id().getTimestamp();
            if (0 != registerTime) {
                actorBaseInfoVO.setRegisteredTime(DateHelper.ARABIAN.dateToStr(new Date(registerTime * 1000L)));
            }
            // 最后一次登录时间
            LastLogin lastLogin = actor.getLastLogin();
            if (null != lastLogin && null != lastLogin.getLogoutTime() && 0 != lastLogin.getLogoutTime()) {
                actorBaseInfoVO.setLogoutTime(DateHelper.ARABIAN.dateToStr(new Date(lastLogin.getLogoutTime() * 1000L)));
            }
            actorBaseInfoVO.setUserLevel(level);
            actorBaseInfoVO.setIsAndroid(isAndroid);
            actorBaseInfoVO.setCountry(actor.getCountry());
            actorBaseInfoVO.setAllRoomTimeCount(inRoomTime);
            actorBaseInfoVO.setFbGender(actor.getFbGender());
            actorBaseInfoVO.setNowBeans(actor.getBeans());
            actorBaseInfoVO.setRoomDevote(roomDevoteSum);
            actorBaseInfoVO.setIp(actor.getIp());
            actorBaseInfoVO.setTnId(actor.getTn_id());
            CountryData countryData = countryQuery.find(actor.getIp());
            if (countryData != null) {
                actorBaseInfoVO.setIpCountry(countryData.getCountry());// IP国家
            }
            actorBaseInfoVO.setImei(actor.getImei());
            actorBaseInfoVO.setAndroidId(actor.getAndroidId());
            actorBaseInfoVO.setRechargeCount(rechargeCount);
            actorBaseInfoVO.setFriendNum(friendshipDao.getFriendsNum(uid));
            actorBaseInfoVO.setAge(actor.getAge());
            Integer vipLevel = vipInfoDao.getVipLevel(uid);
            actorBaseInfoVO.setVipLevel(vipLevel != null ? vipLevel : 0);
            actorBaseInfoVO.setRechargeAmount(BigDecimal.valueOf(rechargeDailyInfoDao.getRechargeMoneyAmount(uid)));
            //set 粉丝数
            AggStatData fansData = fansStatMap.get(uid);
            if (fansData != null) {
                actorBaseInfoVO.setFansNum(fansData.getUserCount());
            }
            //set 用户在麦时长
            Integer upMicTime = UpMicTimeMap.getOrDefault(uid, 0);
            actorBaseInfoVO.setOnMicTime(upMicTime);
            if (upMicTime > inRoomTime) {
                //上麦时间大于再房总时间时打印
                logger.error("mic time can not gt all time start = {},end = {},uid = {},all time = {},mic time = {}", start, end, uid, inRoomTime, upMicTime);
            }
            //set 用户第一次进入房间id
//            long currentTime = new Date().getTime() / 1000;
//            int roomEnd = (int) currentTime;
//            List<EnterRoom> periodRooms = enterRoomDao.findPeriodRooms(registerTime, roomEnd, uid);
//            if (periodRooms.size() >= 1) {
//                EnterRoom startEnterRoom = periodRooms.get(0);
//                String startRoomId = startEnterRoom.getRoomId();
//                // 获取真实的roomId;即room_id对应的用户rid
//                String startUid = startRoomId.split(":")[1];
//                ApiResult<Actor> startActorResult = actorDao.getActorByUid(startUid);
//                if (startActorResult.isOK()) {
//                    //set 用户第一次进入房间id
//                    actorBaseInfoVO.setFirstInRoomId(startActorResult.getData().getRid());
//                    //set 第一次进房的时间
//                    actorBaseInfoVO.setFirstInRoomTime(DateHelper.ARABIAN.timestampToDatetimeStr(startEnterRoom.getCtime() * 1000L));
//                }
//            }
            //set 用户最后一次进入房间id
//            if (periodRooms.size() >= 1) {
//                int maxEnterTime = 0;
//                Map<Integer, EnterRoom> enterRoomMap = new HashMap<>();
//                for (EnterRoom periodRoom : periodRooms) {
//                    int ctime = periodRoom.getCtime() == null ? 0 : periodRoom.getCtime();
//                    int onlineTime = periodRoom.getOnlineTime() == null ? 0 : periodRoom.getOnlineTime();
//                    int time = ctime + onlineTime;
//                    if (time > maxEnterTime) {
//                        maxEnterTime = time;
//                        enterRoomMap.put(maxEnterTime, periodRoom);
//                    }
//                }
//                EnterRoom endEnterRoom = enterRoomMap.get(maxEnterTime);
//                String endRoomId = endEnterRoom.getRoomId();
//                //获取真实的roomId;即room_id对应的用户rid
//                String endUid = endRoomId.split(":")[1];
//                ApiResult<Actor> endActorResult = actorDao.getActorByUid(endUid);
//                if (endActorResult.isOK()) {
//                    //用户最后一次进入房间id
//                    actorBaseInfoVO.setLastInRoomId(endActorResult.getData().getRid());
//                    //set 最后一次进入房间时间
//                    actorBaseInfoVO.setLastInRoomTime(DateHelper.ARABIAN.timestampToDatetimeStr(maxEnterTime * 1000L));
//                }
//            }
            //设置用户荣耀积分
            NewUserHonor newUserHonor = honorMap.get(uid);
            if (newUserHonor != null && newUserHonor.getBeans() != null) {
                actorBaseInfoVO.setHonorBeans(newUserHonor.getBeans());
            }
            //设置房间关注人数
            String roomId = "r:" + uid;
            AggStatData followRoomActorData = followRoomActorMap.get(roomId);
            if (followRoomActorData != null) {
                actorBaseInfoVO.setRoomFollowUserCount(followRoomActorData.getUserCount());
            }
            //设置发送礼物人数
//            int roomSendGiftUsers = giftRecordMgDao.getRoomSendGiftUsers(start, end, roomId);
//            actorBaseInfoVO.setRoomSendGiftCount(roomSendGiftUsers);
            lists.add(actorBaseInfoVO);
        }
        return result.ok(lists);
    }

    /**
     * 获取用户重复数据
     *
     * @param ridList 用户rid
     * @param start   开始时间戳
     * @return
     */
    public ApiResult<List<RepeatAccountsVO>> getRepeatAccounts(List<String> ridList, Integer start) {
        ApiResult<List<RepeatAccountsVO>> result = new ApiResult<>();
        List<RepeatAccountsVO> lists = new LinkedList<>();
        for (String fromRid : ridList) {
            RepeatAccountsVO repeatAccountsVO = new RepeatAccountsVO();
            repeatAccountsVO.setFromRid(fromRid);
            // 通过字符串fromRid获取actor
            Actor actor = ridExistActor(fromRid);
            // 如果actor是空的，则跳过此次循环
            if (null == actor || null == actor.get_id()) continue;
            repeatAccountsVO.setAfterRid(actor.getRid());//返回用户最新rid
            //set 重复账号数
            repeatAccountsVO.setAccountCount(actorStatDao.findAccounts(actor, start));
            lists.add(repeatAccountsVO);
        }
        return result.ok(lists);
    }

    /**
     * 通过rid获取用户信息
     * rid在actor中没有找到会去靓号中找
     *
     * @param fromRid rid
     * @return actor
     */
    private Actor ridExistActor(String fromRid) {
        try {
            int rid = Integer.parseInt(fromRid);
            ApiResult<Actor> apiResult = actorDao.getActorByRidOrBeforeRid(rid);
            if (!apiResult.isOK() || null == apiResult.getData()) {
                logger.info("actor not exist rid={} ", fromRid);
                return null;
            }
            return apiResult.getData();
        } catch (Exception e) {
            logger.info("fromRid transform rid error fromRid={} not is number", fromRid);
            return null;
        }
    }

    /**
     * 获取用户一段时间内的总流水，收入流水，消费流水
     *
     * @param start   开始时间
     * @param end     结束时间
     * @param ridList 哪些用户
     * @return 总数据
     */
    public ApiResult<MoneyDetailVO> moneyDetailTotal(Integer start, Integer end, List<String> ridList) {
        ApiResult<MoneyDetailVO> result = new ApiResult<>();
        // 批量用户的流水统计
        List<AllMoneyDetailVO> allList = new ArrayList<>();
        // 批量用户的收入明细统计
        List<InMoneyDetailVO> inList = new ArrayList<>();
        // 批量用户的消费明细统计
        List<ConsumeMoneyDetailVO> outList = new ArrayList<>();
        // 不存在的rid用户
        List<String> notActorList = new LinkedList<>();
        for (String rid : ridList) {
            ApiResult<Actor> apiResult = actorDao.getActorByRidOrBeforeRid(Integer.parseInt(rid));
            if (!apiResult.isOK() || null == apiResult.getData()) {
                notActorList.add(rid);
                logger.info("rid={} actor not exist", rid);
                continue;
            }
            Actor actor = apiResult.getData();

            Map<String, Integer> map = new HashMap<>();
            map.put("fromRid", Integer.parseInt(rid));
            map.put("afterRid", actor.getRid());

            int size = 10000; // 分页大小
            Pageable page = PageRequest.of(0, size);
            ApiResult<List<MoneyDetailES>> listResult;
            // 将某个用户的收入/消费明细统计
            MoneyDetailTypeVO moneyDetailTypeVO = getCountDetailData(map);
            allList.add(moneyDetailTypeVO.getAllMoneyDetailVO());
            inList.add(moneyDetailTypeVO.getInMoneyDetailVO());
            outList.add(moneyDetailTypeVO.getConsumeMoneyDetailVO());
        }
        logger.info("not check rid actor list:{}", JSON.toJSONString(notActorList));
        // 设置返参
        MoneyDetailVO moneyDetailVO = new MoneyDetailVO();
        moneyDetailVO.setAllMoneyDetailVoList(allList);
        moneyDetailVO.setInMoneyDetailVoList(inList);
        moneyDetailVO.setConsumeMoneyDetailVoList(outList);
        return result.ok(moneyDetailVO);
    }

    /**
     * 获取用户一段时间的流水
     *
     * @param start 开始时间
     * @param end   结束时间
     * @param rid   用户
     * @return 流水
     */
    public ApiResult<List<UserBeansWaterVO>> moneyDetail(Integer start, Integer end, Integer rid) {
        ApiResult<List<UserBeansWaterVO>> result = new ApiResult<>();
        List<UserBeansWaterVO> list = new ArrayList<>();
        ApiResult<Actor> apiResult = actorDao.getActorByRidOrBeforeRid(rid);
        if (!apiResult.isOK()) {
            logger.info("rid={} actor not exist", rid);
            return result.error(apiResult.getMsg());
        }
        Actor actor = apiResult.getData();
        Integer esResultListSize = 0;
        Integer size = 10000; // 一页大小
        Pageable page = PageRequest.of(0, size);
        return result.ok(list);
    }

    /**
     * 房间活跃信息
     *
     * @param startTime 开始时间
     * @param endTime   结束时间
     * @return 报表数据
     */
    public List<CompereRoomInfoVO> roomActiveInfo(Integer startTime, Integer endTime) {
        List<CompereRoomInfoVO> list = new ArrayList<>();
        // 获取迎新房主持人列表
        Query query = new Query(Criteria.where("status").is(1));
        query.fields().include("room_id");
        List<RookieRoom> rookieRooms = mongoTemp1.find(query, RookieRoom.class);
        for (RookieRoom rookieRoom : rookieRooms) {
            if (null == rookieRoom || StringUtil.isEmptyOrBlank(rookieRoom.getRoomId())) {
                continue;
            }
            String roomId = rookieRoom.getRoomId();
            String uid = StringUtil.getUidByRoomId(roomId);
            list.add(getCompereRoomInfo(startTime, endTime, roomId, uid));
        }
        return list;
    }

    /**
     * 房间内用户的行为情况
     *
     * @param rid       迎新房主持人rid
     * @param startTime 开始时间
     * @param endTime   结尾时间
     * @return 行为情况
     */
    public ApiResult<List<ActorActivityVO>> roomActorAction(Integer rid, Integer startTime, Integer endTime) {
        ApiResult<List<ActorActivityVO>> result = new ApiResult<>();
        Query query = new Query(Criteria.where("rid").is(rid));
        query.fields().include("_id");
        Actor actor = mongoTemp1.findOne(query, Actor.class);
        if (null == actor || null == actor.get_id()) {
            logger.info("roomActorAction rid={} actor not exist!", rid);
            return result.error("rid=" + rid + " actor not exist!");
        }
        String rooId = StringUtil.getRoomIdByUid(actor.get_id().toString());
        ApiResult<List<String>> apiResult = enterRoomServer.getRoomActorList(startTime, endTime, rooId);
        if (!apiResult.isOK() || CollectionUtils.isEmpty(apiResult.getData())) {
            logger.info("roomActorAction get room actor error roomId={} {}", rooId, apiResult.getMsg());
        }
        List<ActorActivityVO> lists = new ArrayList<>();
        for (String uid : apiResult.getData()) {
            ActorActivityVO actorActivityVO = actorActionTotal(startTime, endTime, uid);
            lists.add(actorActivityVO);
        }
        logger.info("room rid={}  roomActorAction count={}", rid, lists.size());

        return result.ok(lists);
    }

    /**
     * 将某actor用户在es消费明细区分统计
     *
     * @param map 某个actor用户es查出来的数据
     * @return
     */
    private MoneyDetailTypeVO getCountDetailData(Map<String, Integer> map) {
        MoneyDetailTypeVO moneyDetailVo = new MoneyDetailTypeVO();
        AllMoneyDetailVO all = new AllMoneyDetailVO();
        all.setFromRid(map.get("fromRid").toString());
        all.setAfterRid(map.get("afterRid"));
        InMoneyDetailVO in = new InMoneyDetailVO();
        in.setFromRid(map.get("fromRid").toString());
        in.setAfterRid(map.get("afterRid"));
        ConsumeMoneyDetailVO consume = new ConsumeMoneyDetailVO();
        consume.setFromRid(map.get("fromRid").toString());
        consume.setAfterRid(map.get("afterRid"));

        Iterator<String> iterator = map.keySet().iterator();
        while (iterator.hasNext()) {
            String key = iterator.next();
            Integer value = map.get(key);
            if (null == value) value = 0;
            switch (key) {
                case "1":
                case "5":
                case "700":
                    // 充值
                    all.setTotalRecharge(all.getTotalRecharge() + value);
                    break;
                case "2":
                    // admin 减钻
                    all.setAdminSubBeans(all.getAdminSubBeans() + value);
                    consume.setAdminSubBeans(consume.getAdminSubBeans() + value);
                    break;
                case "40":
                    // 转盘游戏
                    all.setDialGame(all.getDialGame() + value);
                    break;
                case "51":
                case "52":
                case "53":
                case "101":
                    // 购买消费
                    all.setBuyConsume(all.getBuyConsume() + value);
                    consume.setBuyConsume(consume.getBuyConsume() + value);
                    break;
                case "61":
                case "62":
                    // pk游戏
                    all.setPkGame(all.getPkGame() + value);
                    break;
                case "67":
                case "68":
                case "69":
                case "70":
                case "71":
                    // 猜拳游戏
                    all.setFingerGame(all.getFingerGame() + value);
                    if ("67".equals(key) || "68".equals(key)) consume.setFingerGame(consume.getFingerGame() + value);
                    else in.setFingerGame(in.getFingerGame() + value);
                    break;
                case "105":
                    // 签到
                    all.setSignIn(all.getSignIn() + value);
                    in.setSignIn(in.getSignIn() + value);
                    break;
                case "100":
                    // vip 充值
                    all.setVipRecharge(all.getVipRecharge() + value);
                    consume.setVipRecharge(consume.getVipRecharge() + value);
                    break;
                case "104":
                    // vip奖励
                    all.setVipAward(all.getVipAward() + value);
                    in.setVipAward(in.getVipAward() + value);
                    break;
                case "200":
                    // 个人消费返钻
                    all.setConsumeFeedback(all.getConsumeFeedback() + value);
                    in.setConsumeFeedback(in.getConsumeFeedback() + value);
                    break;
                case "202":
                    // 房间消费返钻
                    all.setRoomConsumeFeedback(all.getRoomConsumeFeedback() + value);
                    in.setRoomConsumeFeedback(in.getRoomConsumeFeedback() + value);
                    break;
                case "204":
                case "205":
                    // 老虎机
                    all.setTigerMachineGame(all.getTigerMachineGame() + value);
                    if ("204".equals(key)) consume.setTigerMachineGame(consume.getTigerMachineGame() + value);
                    else in.setTigerMachineGame(in.getTigerMachineGame() + value);
                    break;
                case "206":
                    // 新色子游戏
                    all.setNewDiceGame(all.getNewDiceGame() + value);
                    consume.setNewDiceGame(consume.getNewDiceGame() + value);
                    break;
                case "207":
                    consume.setSudoKuGame(consume.getSudoKuGame() + value);
                    break;
                case "208":
                    in.setSudoKuGame(consume.getSudoKuGame() + value);
                    break;
                case "301":
                case "302":
                    // 收发礼物
                    all.setGetSendGift(all.getGetSendGift() + value);
                    if ("301".equals(key)) consume.setSendGift(consume.getSendGift() + value);
                    else in.setIncomeGift(in.getIncomeGift() + value);
                    break;
                case "800":
                case "801":
                case "802":
                    // 收发红包
                    all.setGetSendRedPacket(all.getGetSendRedPacket() + value);
                    if ("800".equals(key)) consume.setSendRedPacket(consume.getSendRedPacket() + value);
                    else in.setIncomeRedPacket(in.getIncomeRedPacket() + value);
                    break;
                case "901":
                case "902":
                case "903":
                case "904":
                case "905":
                case "906":
                case "907":
                    // 活动奖励收入
                    all.setActivityAward(all.getActivityAward() + value);
                    in.setActivityAward(in.getActivityAward() + value);
                    break;
                case "admin honor charge":
                    in.setAdminHonorCharge(in.getAdminHonorCharge() + value);
                    break;
                case "admin non honor charge":
                    in.setAdminNonHonorCharge(in.getAdminNonHonorCharge() + value);
                    break;
                case "fromRid":
                case "afterRid":
                    break;
                default:
                    all.setOther(all.getOther() + value);
                    break;
            }
        }

        all.setTotalConsume(all.sum());
        in.setTotalConsume(in.sum());
        consume.setTotalConsume(consume.sum());

        moneyDetailVo.setAllMoneyDetailVO(all);
        moneyDetailVo.setInMoneyDetailVO(in);
        moneyDetailVo.setConsumeMoneyDetailVO(consume);

        return moneyDetailVo;
    }

    /**
     * 对象转固定顺序的list
     *
     * @param mList 对象集合
     * @param rid   用户rid
     * @return 返参
     */
    private List<UserBeansWaterVO> moneyToList(List<MoneyDetailES> mList, int rid) {
        if (CollectionUtils.isEmpty(mList)) return new LinkedList<>();
        List<UserBeansWaterVO> lists = new ArrayList<>();
        for (MoneyDetailES m : mList) {
            UserBeansWaterVO userBeansWaterVo = new UserBeansWaterVO();
            userBeansWaterVo.setRid(rid);
            userBeansWaterVo.setChanged(m.getChanged());
            userBeansWaterVo.setBalance(m.getBalance());
            userBeansWaterVo.setTitle(m.getTitle());
            userBeansWaterVo.setDesc(m.getDesc());
            userBeansWaterVo.setaType(m.getAtype());
            userBeansWaterVo.setMtime(DateHelper.ARABIAN.datetimeToStr(new Date(m.getMtime() * 1000L)));
            lists.add(userBeansWaterVo);
        }
        return lists;
    }

    /**
     * 获取用户在房消费合计
     *
     * @param uidSet uidSet
     * @param start  开始合计时间
     * @param end    结束合计时间
     * @return 合计
     */
    private Map<String, Integer> roomDevoteSum(Set<String> uidSet, Integer start, Integer end) {
        return new HashMap<>();
    }

    /**
     * 用户在某时间段内充值次数
     *
     * @param uidSet uidSet
     * @param start  开始时间
     * @param end    结束时间
     * @return 次数
     */
    private Map<String, Integer> rechargeCount(Set<String> uidSet, Integer start, Integer end) {
        Map<String, Integer> result = new HashMap<>();
        return result;
    }

    private CompereRoomInfoVO getCompereRoomInfo(Integer startTime, Integer endTime, String roomId, String userId) {
        CompereRoomInfoVO vo = new CompereRoomInfoVO();

        // 主持人在麦时长
        ApiResult<Integer> result = roomMicDao.compereInMicTime(startTime, endTime, userId, roomId);
        if (result.isOK() && null != result.getData()) {
            vo.setMicTime(result.getData());
        }

        // 新用户进房人数
        result = enterRoomServer.newActorInPerson(startTime, endTime, roomId);
        if (result.isOK() && null != result.getData()) {
            vo.setNewActorJoinRoomPerson(result.getData());
        }

        // 新用户进房次数
        result = enterRoomServer.newActorInCount(startTime, endTime, roomId);
        if (result.isOK() && null != result.getData()) {
            vo.setNewActorJoinRoomCount(result.getData());
        }

        // 聊天人数
        result = roomMsgDao.roomChatPerson(startTime, endTime, roomId);
        if (result.isOK() && null != result.getData()) {
            vo.setChatPerson(result.getData());
        }

        // 聊天次数
        result = roomMsgDao.roomChatCount(startTime, endTime, roomId);
        if (result.isOK() && null != result.getData()) {
            vo.setChatCount(result.getData());
        }

        // 新用户上麦人数
        result = roomMicDao.newActorInMicPerson(startTime, endTime, roomId);
        if (result.isOK() && null != result.getData()) {
            vo.setNewActorMicPerson(result.getData());
        }

        // 新用户上麦次数
        result = roomMicDao.newActorInMicCount(startTime, endTime, roomId);
        if (result.isOK() && null != result.getData()) {
            vo.setNewActorMicCount(result.getData());
        }

        // 新用户平均在房时间
        ApiResult<Double> doubleApiResult = roomMicDao.newActorInMicTimeAvg(startTime, endTime, roomId);
        if (doubleApiResult.isOK() && null != doubleApiResult.getData()) {
            vo.setNewActorMicAvg(doubleApiResult.getData());
        }

        // 新用户在麦5分钟以上
        result = roomMicDao.newActorInMicTime5Minute(startTime, endTime, roomId);
        if (result.isOK() && null != result.getData()) {
            vo.setNewActorMic5Minute(result.getData());
        }

        // 房间关注人数
        result = followRoomOpDao.roomFollowActor(startTime, endTime, roomId);
        if (result.isOK() && null != result.getData()) {
            vo.setFollowRoomPerson(result.getData());
        }

        // 房间关注次数
//        result = roomFollowApi.roomFollowCount(startTime, endTime, roomId);
//        if (result.isOK() && null != result.getData()) {
//            vo.setFollowRoomCount(result.getData());
        vo.setFollowRoomCount(0);
//        }

        // 用户平均停留时长
        doubleApiResult = enterRoomServer.roomActorOnlineAvgTime(startTime, endTime, roomId);
        if (doubleApiResult.isOK() && null != doubleApiResult.getData()) {
            vo.setAvgInRoomTime(doubleApiResult.getData());
        }

        return vo;
    }

    private ActorActivityVO actorActionTotal(Integer startTime, Integer endTime, String userId) {
        ActorActivityVO actorActivityVO = new ActorActivityVO();

        // 进房间数量
        ApiResult<Integer> result = enterRoomServer.actorJoinRoomNum(startTime, endTime, userId);
        if (result.isOK() && null != result.getData()) {
            actorActivityVO.setJoinRoomNum(result.getData());
        }


        // 进房间次数
        result = enterRoomServer.actorJoinRoomCount(startTime, endTime, userId);
        if (result.isOK() && null != result.getData()) {
            actorActivityVO.setJoinRoomCount(result.getData());
        }

        // 累计房间停留时长
        ApiResult<TotalVO> voResult = roomTimeDao.actorInRoomTime(startTime, endTime, userId);
        if (voResult.isOK()) {
            actorActivityVO.setStayRoomDuration(voResult.getData().getSumNum().toString());// 累计在房时长
        }

        // 文字聊天次数
        result = roomMsgDao.actorChatCount(startTime, endTime, userId);
        if (result.isOK() && null != result.getData()) {
            actorActivityVO.setWordyChatCount(result.getData());
        }

        // 上麦次数
        result = roomMicDao.actorMicCount(startTime, endTime, userId);
        if (result.isOK() && null != result.getData()) {
            actorActivityVO.setOnMicCount(result.getData());
        }

        // 累计上麦时长
        result = roomMicDao.actorAddUpMicTime(startTime, endTime, userId);
        if (result.isOK() && null != result.getData()) {
            actorActivityVO.setOnMicDuration(result.getData().toString());
        }

        // 送礼人数和送礼次数
        ApiResult<Integer[]> apiResult = giftRecordMgDao.sendGiftCount(startTime, endTime, userId);
        if (apiResult.isOK()) {
            actorActivityVO.setSendGiftUserCount(apiResult.getData()[0]);
            actorActivityVO.setSendGiftCount(apiResult.getData()[1]);
        }

        // 收礼次数
        result = giftRecordMgDao.receivedGiftCount(startTime, endTime, userId);
        if (result.isOK() && null != result.getData()) {
            actorActivityVO.setReceiveGiftCount(result.getData());
        }

        // 房间关注次数
//        result = roomFollowApi.actorFollowRoomCount(startTime, endTime, userId);
//        if (result.isOK() && null != result.getData())
//            list.add(result.getData().toString());
//        else

        // 好友数
        actorActivityVO.setFriends(friendshipDao.getFriendsNum(userId));

        // 充值金额
        result = esServer.actorChargeBeans(startTime, endTime, userId);
        if (result.isOK() && null != result.getData()) {
            actorActivityVO.setPayMoney(result.getData().toString());
        }
        return actorActivityVO;
    }

    /**
     * 获取用户一段时间里消费的信息
     *
     * @param startTime 开始时间
     * @param endTime   结尾时间
     * @param uid       用户user_id
     * @return 消费信息
     */
    public ApiResult<ConsumeMoneyDetailVO> outConsumeInfo(Integer startTime, Integer endTime, String uid) {
        ApiResult<ConsumeMoneyDetailVO> result = new ApiResult<>();
        Map<String, Integer> map = new HashMap<>();
        MoneyDetailTypeVO moneyDetailTypeVO = getCountDetailData(map);
        ConsumeMoneyDetailVO consumeMoneyDetailVO = moneyDetailTypeVO.getConsumeMoneyDetailVO();
        return result.ok(consumeMoneyDetailVO);
    }

    /**
     * 获取日期时间段内的总迎新房数据报表
     *
     * @param dateArr 日期数组
     * @return 数据报表
     */
    public ApiResult<List<WelcomeNewRoomVO>> welcomeNewRoomDataInfo(String[] dateArr) {
        ApiResult<List<WelcomeNewRoomVO>> result = new ApiResult<>();
        List<WelcomeNewRoomVO> lists = new ArrayList<>();
        for (String date : dateArr) {
            WelcomeNewRoomVO vo = getDayWelcomeRoomDataInfo(date);

            if (null != vo) lists.add(vo);
        }

        return result.ok(lists);
    }

    /**
     * 获取某天的数据报表
     *
     * @param dateStr 某天
     * @return 数据报表
     */
    private WelcomeNewRoomVO getDayWelcomeRoomDataInfo(String dateStr) {
        if (StringUtil.isEmptyOrBlank(dateStr)) return null;

        List<EnterRoom> erList = getWelcomeNewRoomActorUid(dateStr);
        if (CollectionUtils.isEmpty(erList)) {
            return null;
        }
        WelcomeNewRoomVO vo = new WelcomeNewRoomVO();
        vo.setDate(dateStr);

        List<String> uidList = fillNewActorInfo(vo, erList);
        fillNewActorInRoomInfo(vo, dateStr, uidList);

        ApiResult<String> strResult = roomTimeDao.personAvgInRoomTime(dateStr, uidList);
        if (strResult.isOK()) {
            vo.setPerAvgRoomTime(strResult.getData());
        } else {
            logger.error("date={} personAvgInRoomTime error {}", dateStr, strResult.getMsg());
        }

        ApiResult<Integer> result = roomMsgDao.chatPersonNum(dateStr, uidList);
        if (result.isOK()) {
            vo.setChatPerNum(result.getData());
        } else {
            logger.error("date={} chatPersonNum error {}", dateStr, result.getMsg());
        }

        fillUpMicInfo(vo, dateStr, uidList);

        ApiResult<Integer[]> apiResult = giftRecordMgDao.sendGiftTotal(dateStr, uidList);
        if (!apiResult.isOK()) {
            logger.error("date={} sendGiftTotal error {}", dateStr, apiResult.getMsg());
        } else {
            vo.setSendGiftPerNum(apiResult.getData()[0]);
            vo.setSendGiftCount(apiResult.getData()[1]);
        }

        result = followRoomOpDao.partyActorFollowNum(dateStr, uidList);
        if (!result.isOK()) {
            logger.error("date={} partyActorFollowNum error {}", dateStr, result.getMsg());
        } else {
            vo.setFollowRoomPer(result.getData());
        }
        return vo;
    }

    /**
     * 获取注册当天进入迎新房的活跃用户
     *
     * @param dateStr 某天日期
     * @return 用户uid列表
     */
    public List<EnterRoom> getWelcomeNewRoomActorUid(String dateStr) {
        ApiResult<List<EnterRoom>> result = enterRoomServer.getEnterRoomUserId(dateStr, 1);
        if (!result.isOK()) {
            logger.error("get date={} welcome room uid list error msg={}", dateStr, result.getMsg());
        }
        if (null == result.getData()) {
            return new ArrayList<>();
        }
        return result.getData();
    }

    /**
     * 填充用户新增信息
     *
     * @param vo     待填充对象
     * @param erList 新增信息
     * @return 用户uid列表
     */
    private List<String> fillNewActorInfo(WelcomeNewRoomVO vo, List<EnterRoom> erList) {
        int ios = 0;
        int android = 0;
        List<String> uidList = new ArrayList<>();
        for (EnterRoom er : erList) {
            uidList.add(er.getUserId());
            if (1 == er.getOs()) {
                ios++;
            } else {
                android++;
            }
        }

        vo.setNewActor(uidList.size());
        vo.setIosNewActor(ios);
        vo.setAndroidNewActor(android);
        return uidList;
    }

    /**
     * 填充新用户进房间信息
     *
     * @param vo      待填充对象
     * @param dateStr 日期
     * @param uidList 一批用户
     */
    private void fillNewActorInRoomInfo(WelcomeNewRoomVO vo, String dateStr, List<String> uidList) {
        List<Integer> perList = personEnterRoom(dateStr, uidList);
        // 人均进入房间
        if (0 != perList.size()) {
            BigDecimal a = new BigDecimal(perList.size());
            BigDecimal personNum = new BigDecimal(uidList.size());
            double avg = a.divide(personNum, 2, BigDecimal.ROUND_HALF_DOWN).doubleValue();
            vo.setPerAvgInRoomNum(avg); // 人均进入房间数

            int totalNum = 0;
            for (Integer num : perList) {
                totalNum += num;
            }
            a = new BigDecimal(totalNum);
            double count = a.divide(personNum, 2, BigDecimal.ROUND_HALF_DOWN).doubleValue();
            vo.setPerAvgInRoomCount(count); // 人均进房次数
        }
    }

    /**
     * 填充上麦信息
     *
     * @param vo      待填充对象
     * @param dateStr 日期
     * @param uidList 批量用户
     */
    private void fillUpMicInfo(WelcomeNewRoomVO vo, String dateStr, List<String> uidList) {
        ApiResult<List<TotalVO>> result = roomMicDao.actorUpMicTotal(dateStr, uidList);
        if (!result.isOK() || CollectionUtils.isEmpty(result.getData())) {
            logger.error("get actor up mic total error {}", result.getMsg());
            return;
        }
        Long countNum = 0L;
        BigDecimal sumNum = BigDecimal.ZERO;
        for (TotalVO total : result.getData()) {
            countNum += total.getCountNum();
            sumNum = sumNum.add(total.getSumNum());
        }

        BigDecimal personNum = new BigDecimal(result.getData().size());
        vo.setUpMicPer(personNum.intValue());
        if (0 < countNum)
            vo.setPerAvgUpMicCount(new BigDecimal(countNum).divide(personNum, 2, BigDecimal.ROUND_HALF_DOWN).doubleValue());

        if (!BigDecimal.ZERO.equals(sumNum)) {
            Integer avgTime = sumNum.divide(personNum, 2, BigDecimal.ROUND_HALF_DOWN).intValue();
            vo.setPerAvgUpMicTime(DateHelper.ARABIAN.intToTimeString(avgTime));
        }
    }

    /**
     * 某天用户进迎新房的信息
     *
     * @param dateStr 日期
     * @param uidList 一批用户
     * @return 数据
     */
    private List<Integer> personEnterRoom(String dateStr, List<String> uidList) {
        ApiResult<List<Integer>> result = enterRoomServer.personEnterRoom(dateStr, uidList);
        if (!result.isOK()) {
            logger.error("date={} personEnterRoom error, info : {}", dateStr, result.getMsg());
            return new ArrayList<>();
        }
        return result.getData();
    }


    /**
     * 获取那些天的进入迎新房的用户的次留
     *
     * @param dateArr 那些天
     * @return 次留
     */
    public ApiResult<List<WelcomeNewRoomKeepVO>> welcomeNewRoomKeep(String[] dateArr) {
        ApiResult<List<WelcomeNewRoomKeepVO>> result = new ApiResult<>();
        List<WelcomeNewRoomKeepVO> lists = new ArrayList<>();
        for (String date : dateArr) {
            WelcomeNewRoomKeepVO welcomeNewRoomKeepVO = new WelcomeNewRoomKeepVO();
            welcomeNewRoomKeepVO.setDate(date);
            Double[] doubles = totalSomedayInRoomNum(date, -1);
            welcomeNewRoomKeepVO.setIosYesterdayKeep(doubles[0]);
            welcomeNewRoomKeepVO.setAndroidYesterdayKeep(doubles[1]);
            welcomeNewRoomKeepVO.setYesterdayKeep(doubles[2]);
            doubles = totalSomedayInRoomNum(date, -7);
            welcomeNewRoomKeepVO.setIosSevenKeep(doubles[0]);
            welcomeNewRoomKeepVO.setAndroidSevenKeep(doubles[1]);
            welcomeNewRoomKeepVO.setSevenKeep(doubles[2]);
            doubles = totalSomedayInRoomNum(date, -30);
            welcomeNewRoomKeepVO.setIosThirtyKeep(doubles[0]);
            welcomeNewRoomKeepVO.setAndroidThirdKeep(doubles[1]);
            welcomeNewRoomKeepVO.setThirtyKeep(doubles[2]);
            lists.add(welcomeNewRoomKeepVO);
        }
        return result.ok(lists);
    }

    public ApiResult<Map<String, Map<String, Object>>> getRookieNewUserCount(String channel, String startDate, String endDate) {
        ApiResult<Map<String, Map<String, Object>>> result = new ApiResult<>();
        Integer[] times = DateHelper.ARABIAN.getStartOrEndSeconds(startDate, endDate);
        logger.info("start get rookie new user count start date = {},end date = {},start time = {},end time = {}", startDate, endDate, times[0], times[1]);
        Map<String, Map<String, Object>> rookieRoomUserCount = enterRoomServer.findRookieRoomUserCount(channel, times[0], times[1]);
        return result.ok(rookieRoomUserCount);
    }

    /**
     * 统计某天注册的现在进房数
     *
     * @param dateStr 现在日期
     * @param num     前某(N)天
     * @return 数组
     */
    private Double[] totalSomedayInRoomNum(String dateStr, int num) {
        Date date = DateHelper.ARABIAN.stringToDate(dateStr);
        Date keepDate = DateHelper.ARABIAN.dateAddDay(date, num);
        String keepDateStr = DateHelper.ARABIAN.dateToStr(keepDate);

        int newIos = 0;
        int newAndroid = 0;
        List<EnterRoom> erList = getWelcomeNewRoomActorUid(keepDateStr);
        List<String> uidList = new ArrayList<>();
        for (EnterRoom er : erList) {
            if (null == er) continue;
            if (1 == er.getOs()) {
                newIos++;
            } else {
                newAndroid++;
            }
            uidList.add(er.getUserId());
        }
        int newAll = newIos + newAndroid;


        int ios = 0;
        int android = 0;
        ApiResult<List<Dau>> result = dauDao.getActiveActorByUid(dateStr, uidList);
        if (!result.isOK()) {
            logger.error("totalSomedayInRoomNum getActiveActorByUid error msg {}", result.getMsg());
            // 防止下面那行报空指针
            result.ok(new ArrayList<>());
        }
        for (Dau dau : result.getData()) {
            if (null == dau) continue;
            if (1 == dau.getOs()) {
                ios++;
            } else {
                android++;
            }
        }
        int all = ios + android;
        logger.info("num {} {} {} new num {} {} {}", ios, android, all, newIos, newAndroid, newAll);
        return new Double[]{comparison(ios, newIos), comparison(android, newAndroid), comparison(all, newAll)};
    }

    /**
     * 计算两个数的比例
     *
     * @param a 被除数
     * @param b 除数
     * @return 比例%
     */
    private Double comparison(Integer a, Integer b) {
        if (a == 0 || b == 0) return 0.00;

        BigDecimal bigDecimal = new BigDecimal(a).divide(new BigDecimal(b), 4, BigDecimal.ROUND_DOWN);
        bigDecimal = bigDecimal.multiply(new BigDecimal(100));
        return bigDecimal.doubleValue();
    }

    /**
     * @param type 数据类型
     *             所有、0
     *             日活和新增、 1
     *             创建房间人数次数、2
     *             进房人数次数、3
     *             上麦人数次数、4
     *             发言人数次数、5
     *             送礼物人数次数、6
     *             停留时长和上麦时长、7
     */
    public List<RoomUserStatVO> roomUserStat(int startTime, int endTime, Integer os, int type) {
        List<RoomUserStatVO> list = new ArrayList<>();
        String[] dateArr = DateHelper.ARABIAN.getDateDiffArray(startTime, endTime);
        RoomUserStatVO vo;
        for (String dateStr : dateArr) {
            Integer[] time = DateHelper.ARABIAN.getTimeWhereRange(dateStr);
            vo = new RoomUserStatVO();
            vo.setDate(dateStr);
            if (type == 0 || type == 1) {
                Set<String> dauUidSet = dauDao.getActiveActorByWhere(time[0], --time[1], os, null, null, -1).getData();
                vo.setDau(dauUidSet.size());
                Set<String> newUserUidSet = actorDao.totalNewActorUidSet(time[0], --time[1], null == os ? -1 : os, -1);
                vo.setNewUser(newUserUidSet.size());
            }
            if (type == 0 || type == 3 || type == 7) {
                EnterRoomStatVO enterRoomStat = enterRoomDao.getEnterRoomStat(time[0], --time[1], os);
                vo.setEnterRoomUser(enterRoomStat.getEnterRoomUser());
                vo.setEnterRoomCount(enterRoomStat.getEnterRoomCount());
                if (enterRoomStat.getEnterRoomUser() > 0 && enterRoomStat.getEnterRoomCount() > 0) {
                    vo.setPerCapitaEnterRoom(enterRoomStat.getEnterRoomCount() / enterRoomStat.getEnterRoomUser());
                }
                if (enterRoomStat.getTotalOnlineTime() > 0 && enterRoomStat.getEnterRoomUser() > 0) {
                    vo.setRoomStayMinute(enterRoomStat.getTotalOnlineTime() / enterRoomStat.getEnterRoomUser() / 60);
                }
            }
            if (type == 0 || type == 4 || type == 7) {
                RoomMicStatVO roomMicStat = roomMicDao.getRoomMicStat(time[0], --time[1], os);
                vo.setUpMicUser(roomMicStat.getUpMicUser());
                vo.setUpMicCount(roomMicStat.getUpMicCount());
                if (roomMicStat.getTotalMicTime() > 0 && roomMicStat.getUpMicUser() > 0) {
                    vo.setUpMicMinute(roomMicStat.getTotalMicTime() / roomMicStat.getUpMicUser() / 60);
                }
            }
            if (type == 0 || type == 5) {
                RoomMsgStatVO roomMsgStat = roomMsgDao.getRoomMsgStat(time[0], --time[1], os);
                vo.setChatUser(roomMsgStat.getChatUser());
                vo.setChatCount(roomMsgStat.getChatCount());
            }
            if (type == 0 || type == 6) {
                List<Integer> giftTotal = giftRecordMgDao.sendGiftTotal(time[0], --time[1], os);
                vo.setSentGiftUser(giftTotal.get(0));
                vo.setSentGiftCount(giftTotal.get(1));
            }
            list.add(vo);
        }
        return list;
    }

    public List<FirstChargeStatVO> firstChargeStat(int startTime, int endTime, Integer os) {
        List<FirstChargeStatVO> list = new ArrayList<>();
        String[] dateArr = DateHelper.ARABIAN.getDateDiffArray(startTime, endTime);
        FirstChargeStatVO vo;
        for (String dateStr : dateArr) {
            Integer[] time = DateHelper.ARABIAN.getTimeWhereRange(dateStr);
            vo = moneyDetailStatDao.firstChargeStat(time[0], time[1], os);
            vo.setDate(dateStr);
            list.add(vo);
        }
        return list;
    }

    public RoomOnlineStatVO roomOnlineStat(Integer startTime, Integer endTime) {
        return roomOnlineDao.roomOnlineStat(startTime, endTime);
    }

    public List<RoomUserOnlineVO> roomUserOnlineByDay(Integer startTime, Integer endTime) {
        return roomUserStatDao.roomUserOnlineByDay(startTime, endTime);
    }

    public RoomOnlineStatVO roomUserOnlineStat(Integer startTime, Integer endTime, String os, String userType) {
        return roomUserOnlineSlaveDao.roomUserOnlineStat(startTime, endTime, os, userType);
    }

    public RoomOnlineStatVO userUpMicStat(Integer startTime, Integer endTime, String os, String userType) {
        return roomMicDao.userUpMicStat(startTime, endTime, os, userType);
    }

    public List<HuaWeiPayChargeVO> huaWeiPayChargeReports(Integer startTime, Integer endTime) {
        List<HuaWeiPayChargeVO> list = new ArrayList<>();
        String[] dateArr = DateHelper.ARABIAN.getDateDiffArray(startTime, endTime);
        for (String dateStr : dateArr) {
            Integer[] time = DateHelper.ARABIAN.getTimeWhereRange(dateStr);
            HuaWeiPayChargeVO vo = new HuaWeiPayChargeVO();
            vo.setDate(dateStr);
            Set<String> uidSet = actorDao.huaWeiNewActorUidSet(time[0], time[1], -1, -1);
            vo.setNewUserCount(String.valueOf(uidSet.size()));
            TotalVO totalVOMap = moneyDetailStatDao.statHuaWeiCharge(dateStr, uidSet);
            vo.setChargePerson(String.valueOf(totalVOMap.getPersonNum()));
            vo.setChargeSum(String.valueOf(totalVOMap.getSumNum()));
            list.add(vo);
        }
        return list;
    }

    /**
     * 通过rid获取靓号数据
     *
     * @param ridList
     * @return
     */
    private Map<Integer, Actor> listBeautifulActorMap(List<Integer> ridList) {
        List<BeautifulRidChangeLogOP> beautifulRidChangeLogOPS = beautifulRidChangeLogOPDao.listByBeforeRids(ridList);
        Set<String> beforeUid = new HashSet<>();
        Map<String, List<BeautifulRidChangeLogOP>> beautifulRidChangeLogMap = new HashMap<>();
        for (BeautifulRidChangeLogOP beautifulRidChangeLogOP : beautifulRidChangeLogOPS) {
            String uid = beautifulRidChangeLogOP.getUid();
            if (!StringUtils.isEmpty(uid)) {
                beforeUid.add(uid);
                List<BeautifulRidChangeLogOP> list = beautifulRidChangeLogMap.get(uid);
                if (list == null) {
                    list = new ArrayList<>();
                }
                list.add(beautifulRidChangeLogOP);
                beautifulRidChangeLogMap.put(uid, list);
            }
        }
        List<Actor> beautifulActorList = actorStatDao.listByUid(beforeUid);
        Map<Integer, Actor> beautifulActorMap = new HashMap<>();
        for (Actor actor : beautifulActorList) {
            String uid = actor.get_id().toString();
            List<BeautifulRidChangeLogOP> value = beautifulRidChangeLogMap.get(uid);
            if (!CollectionUtils.isEmpty(value)) {
                for (BeautifulRidChangeLogOP beautifulRidChangeLogOP : value) {
                    beautifulActorMap.put(beautifulRidChangeLogOP.getBeforeRid(), actor);
                }
            }
        }
        return beautifulActorMap;
    }

    private String getRate(Integer divisor, Integer dividend) {
        if (dividend == null || divisor == null) return null;
        if (divisor == 0 || dividend == 0) {
            return "0%";
        }
        NumberFormat numberFormat = NumberFormat.getInstance();
        numberFormat.setMaximumFractionDigits(2);
        String result = numberFormat.format((float) divisor / (float) dividend * 100);
        return result + "%";
    }


    /**
     * 过滤用户
     *
     * @param user 新1、老2、-1全部
     */
    private boolean filterUser(String uid, Integer user, String dateStr) {
        // 过滤新老用户
        if (-1 != user) {
            boolean newRegisterActor = com.quhong.utils.ActorUtils.isNewRegisterActor(uid, dateStr);
            if (1 == user && !newRegisterActor) {
                return false;
            } else return 2 != user || !newRegisterActor;
        }
        return true;
    }

    private String getAppPackage(Integer app) {

        return null;
    }

    private List<String> filterPersonByCount(List<String> personList, int count) {
        if (-1 == count || CollectionUtils.isEmpty(personList)) {
            return personList;
        }
        Map<String, Integer> countMap = new HashMap<>();
        for (String uid : personList) {
            countMap.compute(uid, (k, v) -> {
                if (null == v) {
                    v = 0;
                }
                return v + 1;
            });
        }
        personList = new ArrayList<>();
        for (String uid : countMap.keySet()) {
            if (countMap.get(uid) >= count) {
                personList.add(uid);
            }
        }
        return personList;
    }

    public List<FriendshipVO> friendship(Integer startTime, Integer endTime, Integer user) {
        List<FriendshipVO> list = new ArrayList<>();
        String[] dateArr = DateHelper.ARABIAN.getDateDiffArray(startTime, endTime);
        for (String dateStr : dateArr) {
            FriendshipVO vo = new FriendshipVO();
            vo.setDate(dateStr);
            list.add(vo);
        }
        return list;
    }

    public List<RelationshipNewVO> relationshipNew(Integer startTime, Integer endTime) {
        return Collections.emptyList();
    }

    public List<RelationshipReturnVO> relationshipReturn(Integer startTime, Integer endTime) {
        return Collections.emptyList();
    }

    public MsgGiftVO getDayMsgGift(DayTimeData dayTimeData, int os, int isNew) {
        MsgGiftVO msgGiftVO = new MsgGiftVO();
        msgGiftVO.setDate(dayTimeData.getDate());
        Integer osType = null;
        Integer isNewType = null;
        if (os != -1) {
            osType = os;
        }
        if (isNew != -1) {
            isNewType = isNew;
        }
        //set dau
        Integer[] time = DateHelper.ARABIAN.getTimeWhereRange(dayTimeData.getDate());
        ApiResult<Set<String>> dauOnlineActor = dauDao.getActiveActorByWhere(time[0], --time[1], osType, isNewType, null, -1);
        Set<String> dauUidSet = dauOnlineActor.getData();
        if (CollectionUtils.isEmpty(dauUidSet)) {
            return msgGiftVO;
        }
        int dau = dauUidSet.size();
        msgGiftVO.setDau(dau);
        //set 新增用户数
        List<Actor> newActors = actorDao.totalNewActorList(dayTimeData.getTime(), dayTimeData.getEndTime(), os, -1);
        int addUserNum = newActors.size();
        msgGiftVO.setAddUserNum(addUserNum);
        //set 发送私信礼物相关数据
        List<MysqlMsgRecordData> beanGiftMsgList = msgRecordDao.listMsgRecord(dayTimeData.getTime() * 1000L, dayTimeData.getEndTime() * 1000L, MsgType.GIFT, dauUidSet);
        List<MysqlMsgRecordData> heartGiftMsgList = msgRecordDao.listMsgRecord(dayTimeData.getTime() * 1000L, dayTimeData.getEndTime() * 1000L, MsgType.HEART_GIFT, dauUidSet);
        List<MysqlMsgRecordData> totalGiftMsgList = new ArrayList<>();
        totalGiftMsgList.addAll(beanGiftMsgList);
        totalGiftMsgList.addAll(heartGiftMsgList);
        Set<String> totalGiftUidSet = new HashSet<>();
        for (MysqlMsgRecordData mysqlMsgRecordData : totalGiftMsgList) {
            totalGiftUidSet.add(mysqlMsgRecordData.getFromUid());
        }
        int totalGiftUserNum = totalGiftUidSet.size();
        int totalGiftCount = totalGiftMsgList.size();
        msgGiftVO.setSendMsgUserNum(totalGiftUserNum);
        msgGiftVO.setSendMsgCount(totalGiftCount);
        //set 心心礼物相关数据
        Set<String> heartUidSet = new HashSet<>();
        int heartNum = 0;
        for (MysqlMsgRecordData mysqlMsgRecordData : heartGiftMsgList) {
            heartUidSet.add(mysqlMsgRecordData.getFromUid());
            String msgInfo = mysqlMsgRecordData.getMsgInfo();
            JSONObject info = JSON.parseObject(msgInfo);
            heartNum = heartNum + info.getInteger("sendNum");
        }
        int heartUserNum = heartUidSet.size();
        msgGiftVO.setSendHeartUserNum(heartUserNum);
        msgGiftVO.setSendHeartCount(heartGiftMsgList.size());
        msgGiftVO.setSendHeartNum(heartNum);
        //set 普通钻石礼物
        List<GiftData> giftList = giftDao.listMsgGiftByType(1);
        Map<Integer, Integer> giftMap = new HashMap<>();
        for (GiftData giftData : giftList) {
            Integer rid = giftData.getRid();
            giftMap.put(rid, giftData.getPrice());
        }
        Set<String> beanUidSet = new HashSet<>();
        int giftBeans = 0;
        for (MysqlMsgRecordData mysqlMsgRecordData : beanGiftMsgList) {
            beanUidSet.add(mysqlMsgRecordData.getFromUid());
            String msgInfo = mysqlMsgRecordData.getMsgInfo();
            JSONObject info = JSON.parseObject(msgInfo);
            Integer sendNum = info.getInteger("sendNum");
            Integer giftId = info.getInteger("giftId");
            Integer price = giftMap.get(giftId);
            if (price == null) {
                continue;
            }
            int sendBean = sendNum * price;
            giftBeans = giftBeans + sendBean;
        }
        msgGiftVO.setSendBeanUserNum(beanUidSet.size());
        msgGiftVO.setSendBeanNum(beanGiftMsgList.size());
        msgGiftVO.setSendBeanNum(giftBeans);
        //平台消耗钻石总数
        int costBeans = moneyDetailStatDao.getCostBeans(dayTimeData.getTime(), dayTimeData.getEndTime(), null, null);
        msgGiftVO.setTotalCostBean(costBeans);
        return msgGiftVO;
    }

    public PageResultVO<FeedBackReportVO> getFeedbackReport(String start, String end, int page, int status, String username, int replyStatus) {
        Integer[] timeList = DateHelper.ARABIAN.getStartOrEndSeconds(start, end);
        PageResultVO<FeedBackReportVO> pageResultVO = new PageResultVO<>();
        int startTime = timeList[0];
        int endTime = timeList[1];
        List<String> reportIds = null;
        List<FeedbackHandleData> handleList = null;
        boolean fromHandle = false;
        if ((status != -1 && status != 0) || !StringUtils.isEmpty(username) || (replyStatus != -1 && replyStatus != 0)) {
            //查询处理记录
            fromHandle = true;
            if (page != -1) {
                PageResultVO<FeedbackHandleData> handlePage = feedbackHandleDao.pageHandle(startTime, endTime, username, status, replyStatus, page, 10);
                handleList = handlePage.getList();
                if (CollectionUtils.isEmpty(handleList)) {
                    return pageResultVO;
                }
                pageResultVO.setTotal(handlePage.getTotal());
            } else {
                handleList = feedbackHandleDao.listHandle(startTime, endTime, username, status, replyStatus);
                if (CollectionUtils.isEmpty(handleList)) {
                    return pageResultVO;
                }
                pageResultVO.setTotal(handleList.size());
            }
        } else {
            //直接查询report表
            if (page != -1) {
                reportIds = reportLogStatDao.listReportId(startTime, endTime, page);
                if (CollectionUtils.isEmpty(reportIds)) {
                    return pageResultVO;
                }
            }
        }
        if (fromHandle) {
            List<FeedBackReportVO> feedBackReportVOS = fillFeedbackReportVO(null, handleList);
            for (FeedBackReportVO feedBackReportVO : feedBackReportVOS) {
                String reportId = feedBackReportVO.getReportId();
                List<ReportLogData> logList = reportLogStatDao.listReport(reportId);
                getFBKReportVOByReports(feedBackReportVO, logList);
            }
            pageResultVO.setList(feedBackReportVOS);
        } else {
            List<ReportLogData> reportList = reportLogStatDao.listReport(startTime, endTime, reportIds);
            List<FeedBackReportVO> feedBackReportVOS = fillFeedbackReportVO(reportList);
            handleList = feedbackHandleDao.listHandle(reportIds);
            feedBackReportVOS = fillFeedbackReportVO(feedBackReportVOS, handleList);
            pageResultVO.setList(feedBackReportVOS);
        }
        return pageResultVO;
    }

    public void updateFeedBackHandle(int status, String username, String remark, String reportId, int replyStatus) {
        FeedbackHandleData handleData = feedbackHandleDao.selectHandle(reportId);
        Manager manager = managerDao.getManager(username);
        if (manager == null) {
            logger.error("manager is empty. username={}", username);
            return;
        }
        if (handleData == null) {
            int currentTime = com.quhong.core.utils.DateHelper.getNowSeconds();
            handleData = new FeedbackHandleData();
            handleData.set_id(new ObjectId());
            handleData.setCtime(currentTime);
            handleData.setMtime(currentTime);
            handleData.setRemark(remark);
            handleData.setReplyStatus(replyStatus);
            handleData.setReportId(reportId);
            handleData.setStatus(status);
            handleData.setUid(manager.get_id().toString());
            handleData.setUsername(manager.getAccount());
            feedbackHandleDao.saveOne(handleData);
        } else {
            handleData.setRemark(remark);
            handleData.setReplyStatus(replyStatus);
            handleData.setReportId(reportId);
            handleData.setStatus(status);
            handleData.setUid(manager.get_id().toString());
            handleData.setUsername(manager.getAccount());
            feedbackHandleDao.updateById(handleData, handleData.get_id().toString());
        }
    }

    private List<FeedBackReportVO> fillFeedbackReportVO(List<FeedBackReportVO> feedBackReportVOS, List<FeedbackHandleData> handleList) {
        if (CollectionUtils.isEmpty(handleList)) {
            return feedBackReportVOS;
        }
        if (feedBackReportVOS == null) {
            feedBackReportVOS = new ArrayList<>();
            for (FeedbackHandleData feedbackHandleData : handleList) {
                FeedBackReportVO feedBackReportVO = new FeedBackReportVO();
                feedBackReportVO.setReportId(feedbackHandleData.getReportId());
                feedBackReportVO.setStatus(feedbackHandleData.getStatus());
                feedBackReportVO.setManageUser(feedbackHandleData.getUsername());
                feedBackReportVO.setRemark(feedbackHandleData.getRemark());
                feedBackReportVO.setReplayStatus(feedbackHandleData.getReplyStatus());
                feedBackReportVOS.add(feedBackReportVO);
            }
            return feedBackReportVOS;
        }
        Map<String, FeedbackHandleData> handleMap = new HashMap<>();
        for (FeedbackHandleData feedbackHandleData : handleList) {
            handleMap.put(feedbackHandleData.getReportId(), feedbackHandleData);
        }
        for (FeedBackReportVO feedBackReportVO : feedBackReportVOS) {
            FeedbackHandleData feedbackHandleData = handleMap.get(feedBackReportVO.getReportId());
            if (feedbackHandleData != null) {
                feedBackReportVO.setStatus(feedbackHandleData.getStatus());
                feedBackReportVO.setManageUser(feedbackHandleData.getUsername());
                feedBackReportVO.setRemark(feedbackHandleData.getRemark());
                feedBackReportVO.setReplayStatus(feedbackHandleData.getReplyStatus());
            }
        }
        return feedBackReportVOS;
    }

    private List<FeedBackReportVO> fillFeedbackReportVO(List<ReportLogData> reportList) {
        Map<String, List<ReportLogData>> reportMap = new HashMap<>();
        for (ReportLogData reportLogData : reportList) {
            String reportId = reportLogData.getReportId();
            List<ReportLogData> list = reportMap.get(reportId);
            if (list == null) {
                list = new ArrayList<>();
            }
            list.add(reportLogData);
            reportMap.put(reportId, list);
        }
        List<FeedBackReportVO> voList = new ArrayList<>();
        for (String key : reportMap.keySet()) {
            List<ReportLogData> logList = reportMap.get(key);
            FeedBackReportVO feedBackReportVO = getFBKReportVOByReports(null, logList);
            voList.add(feedBackReportVO);
        }
        return voList;
    }

    private FeedBackReportVO getFBKReportVOByReports(FeedBackReportVO feedBackReportVO, List<ReportLogData> logList) {
        if (feedBackReportVO == null) {
            feedBackReportVO = new FeedBackReportVO();
        }
        if (CollectionUtils.isEmpty(logList)) {
            return feedBackReportVO;
        }
        List<String> urls = new ArrayList<>();
        for (ReportLogData reportLogData : logList) {
            int contentType = reportLogData.getContentType();
            if (contentType == ReportContentType.TEXT) {
                feedBackReportVO.setContent(reportLogData.getContent());
                String dateTime = com.quhong.core.utils.DateHelper.ARABIAN.formatDateTime(new Date(reportLogData.getCtime() * 1000L));
                feedBackReportVO.setCommitTime(dateTime);
                feedBackReportVO.setCreateTime(dateTime);
                feedBackReportVO.setInformation(reportLogData.getInformation());
                feedBackReportVO.setError(reportLogData.getError());
                feedBackReportVO.setReportId(reportLogData.getReportId());

                String reportUid = reportLogData.getUid();

                if (reportUid.length() == 24) {
                    ActorData actorData = actorCoreDao.getActorDataFromCache(reportUid);
                    feedBackReportVO.setReportRid(actorData == null ? "" : String.valueOf(actorData.getRid()));
                    feedBackReportVO.setUid(reportUid);
                    feedBackReportVO.setRegisterTime(new ObjectId(reportUid).getTimestamp());
                    feedBackReportVO.setRechargeMoney(BigDecimal.valueOf(rechargeDailyInfoDao.getRechargeMoneyAmount(reportUid)));
                }
                String osName = "Android";
                int os = reportLogData.getOs();
                if (os == ClientOS.IOS) {
                    osName = "ios";
                }
                feedBackReportVO.setOs(osName);
            }
            if (contentType == ReportContentType.IMG) {
                if (!StringUtils.isEmpty(reportLogData.getContent())) {
                    urls.add(reportLogData.getContent());
                }
            }
        }
        feedBackReportVO.setUrls(urls);
        return feedBackReportVO;
    }

    public NoviceReportVO getNoviceReport(DayTimeData dayTimeData, int os, int app, int isNew) {
        return new NoviceReportVO();
    }

    private Set<String> filterUsers(int os, int app, int isNew, String date, Set<String> uidSet) {
        if (CollectionUtils.isEmpty(uidSet)) {
            return uidSet;
        }
        if (isNew == 1) {
            Set<String> newUidSet = new HashSet<>();
            for (String uid : uidSet) {
                if (filterUser(uid, isNew, date)) {
                    newUidSet.add(uid);
                }
            }
            if (CollectionUtils.isEmpty(newUidSet)) {
                return new HashSet<>();
            }
            return actorDao.filterActors(newUidSet, -1, getAppPackage(app), os, null);
        }
        return actorDao.filterActors(uidSet, -1, getAppPackage(app), os, null);
    }

    //    @PostConstruct
    private void downloadLoginExcel() {
        String date = "2021-10-25";
        DayTimeData daytimeData = com.quhong.core.utils.DateHelper.ARABIAN.getContinuesDays(date);
        List<String> uidList = dauDao.totalCount(daytimeData.getTime(), daytimeData.getEndTime(), null, null, null, -1);
        List<ActorLoginVO> loginList = new ArrayList<>();
        for (String uid : uidList) {
            Actor actor = actorDao.getActor(uid);
            ActorLoginVO loginVO = new ActorLoginVO();
            loginVO.setRid(actor.getRid());
            loginVO.setName(actor.getNameFromOperation());
            loginVO.setBeans(actor.getBeans());
            loginList.add(loginVO);
        }
        //下载报表
        ExcelUtils.exportExcel(loginList, ActorLoginVO.class, "login_" + date + ".xlsx", "user");
    }

    public List<StaffWelcomeVO> staffWelcome(Integer startTime, Integer endTime) {
        List<StaffWelcomeVO> result = new ArrayList<>();
        return result;
    }

    public JSONObject apiReportList(Integer startTime, Integer endTime, String rid, Integer type, Integer os, String name, Integer page, Integer pageSize) {
        JSONObject jsonObject = new JSONObject();
        List<ApiReportListVO> result = new ArrayList<>();
        String[] dateArr = DateHelper.ARABIAN.getDateDiffArray(startTime, endTime);
        String uid = null;
        if (StringUtils.hasLength(rid)) {
            ApiResult<Actor> actor = actorDao.getActorByRidOrAlphaRid(rid);
            if (actor.isOK()) {
                uid = actor.getData().get_id().toString();
            }
        }
        for (String dateStr : dateArr) {
            List<ApiReportListVO> statReportList = reportsServer.getStatReportList(dateStr, uid, type, os, name, page, pageSize);
            jsonObject.put("total", reportsServer.getStatReportListTotal(dateStr, uid, type, os, name));
            // 获取上一天的数据
            String yesterdayStr = com.quhong.core.utils.DateHelper.ARABIAN.getYesterdayStr(DateHelper.ARABIAN.stringToDate(dateStr));
            List<ApiReportListVO> yesterdayReportList = reportsServer.getStatReportList(yesterdayStr, uid, type, os, name, page, pageSize);
            Map<String, ApiReportListVO> map = CollectionUtil.listToKeyMap(yesterdayReportList, ApiReportListVO::getName);
            for (ApiReportListVO vo : statReportList) {
                ApiReportListVO reportListVO = map.getOrDefault(vo.getName(), null);
                if (null != reportListVO) {
                    vo.setChange(getRate((new BigDecimal(vo.getRespTime()).intValue() - new BigDecimal(reportListVO.getRespTime()).intValue()), new BigDecimal(vo.getRespTime()).intValue()));
                }
                vo.setScene(vo.getName());
                if (2 == type) {
                    vo.setScene(getMarsSceneName(vo.getName()));
                }
            }
            // 仅返回第一天数据
            jsonObject.put("result", statReportList);
            return jsonObject;
        }
        jsonObject.put("result", result);
        return jsonObject;
    }

    private String getMarsSceneName(String name) {
        if (name.contains("18.196.57.207")) {
            return "法兰克福-AWS";
        } else if (name.contains("13.248.153.103")) {
            return "泛播(GA)01-AWS";
        } else if (name.contains("15.184.30.63")) {
            return "巴林-AWS";
        } else if (name.contains("3.109.170.149")) {
            return "孟买(服务器01直连)-AWS";
        } else if (name.contains("3.6.233.113")) {
            return "孟买(服务器02直连)-AWS";
        } else if (name.contains("47.91.115.39")) {
            return "迪拜-ALI";
        } else if (name.contains("47.91.105.228")) {
            return "中东-FPA";
        } else if (name.contains("154.57.32.31")) {
            return "北非-FPA";
        }
        return name;
    }

    public List<ApiReportListVO> getStatReportList(String dateStr, String uid, Integer type, Integer os, String name, Integer page, Integer pageSize) {
        String tableSuffix = com.quhong.core.utils.DateHelper.ARABIAN.getDayTableSuffix(DateHelper.ARABIAN.stringToDate(dateStr));
        if (reportSlaveMapper.checkExists("t_report_" + tableSuffix) != null) {
            return reportSlaveMapper.getStatReportList(tableSuffix, uid, type, os, name, (page - 1) * pageSize, pageSize);
        }
        return new ArrayList<>();
    }

    public int getStatReportListTotal(String dateStr, String uid, Integer type, Integer os, String name) {
        String tableSuffix = com.quhong.core.utils.DateHelper.ARABIAN.getDayTableSuffix(DateHelper.ARABIAN.stringToDate(dateStr));
        if (reportSlaveMapper.checkExists("t_report_" + tableSuffix) != null) {
            return reportSlaveMapper.getStatReportListTotal(tableSuffix, uid, type, os, name);
        }
        return 0;
    }

    public ApiReportStatVO apiReportStat(Integer startTime, Integer endTime, Integer rid, Integer type, Integer os, String name, String countryCode) {
        ApiReportStatVO result = new ApiReportStatVO();
        List<ReportCountryVO> countryList = new ArrayList<>();
        List<ReportStatVO> statList = new ArrayList<>();
        String[] dateArr = DateHelper.ARABIAN.getDateDiffArray(startTime, endTime);
        String uid = null;
        if (null != rid && 0 != rid) {
            ApiResult<Actor> actor = actorDao.getActorByRid(rid);
            if (actor.isOK()) {
                uid = actor.getData().get_id().toString();
            }
        }
        // 只有1天的数据时显示当天的实施数据
        if (dateArr.length == 1) {
            String tableSuffix = com.quhong.core.utils.DateHelper.ARABIAN.getDayTableSuffix(DateHelper.ARABIAN.stringToDate(dateArr[0]));
            if (reportSlaveMapper.checkExists("t_report_" + tableSuffix) != null) {
                statList = reportSlaveMapper.statInDay(tableSuffix, uid, type, os, name, countryCode);
                countryList = reportSlaveMapper.statCountry(tableSuffix, uid, type, os, name, countryCode);
            }
            fillPercentage(countryList);
        } else {
            for (String date : dateArr) {
                // 每天统计
                String tableSuffix = com.quhong.core.utils.DateHelper.ARABIAN.getDayTableSuffix(DateHelper.ARABIAN.stringToDate(date));
                ReportStatVO reportStatVO = new ReportStatVO();
                if (reportSlaveMapper.checkExists("t_report_" + tableSuffix) != null) {
                    reportStatVO = reportSlaveMapper.statByDay(tableSuffix, uid, type, os, name, countryCode);
                    if (null == reportStatVO) {
                        reportStatVO = new ReportStatVO();
                    }
                }
                reportStatVO.setStatTime(date);
                statList.add(reportStatVO);
            }
            if (dateArr.length > 0) {
                String tableSuffix = com.quhong.core.utils.DateHelper.ARABIAN.getDayTableSuffix(DateHelper.ARABIAN.stringToDate(dateArr[0]));
                if (reportSlaveMapper.checkExists("t_report_" + tableSuffix) != null) {
                    countryList = reportSlaveMapper.statCountry(tableSuffix, uid, type, os, name, countryCode);
                }
            }
            fillPercentage(countryList);
        }
        result.setStatList(statList);
        result.setCountryList(countryList);
        return result;
    }

    private void fillPercentage(List<ReportCountryVO> countryList) {
        int sum = countryList.stream().mapToInt(ReportCountryVO::getResultCount).sum();
        for (ReportCountryVO vo : countryList) {
            vo.setPercentage(getRate(vo.getResultCount(), sum).replace("%", ""));
            // 处理转换国家名字
            vo.setCountry(CountryCodeToName.MAP.getOrDefault(vo.getCountryCode(), vo.getCountryCode()));
            try {
                vo.setRespTime(new BigDecimal(vo.getRespTime()).intValue() + "");
            } catch (Exception ignored) {
            }
        }
    }

    @Resource
    private RoomMsgMapper roomMsgMapper;
    @Resource
    private ActorDao coreActorDao;

    public List<RoomMsgVO> roomMsgReports(int start, int end, String roomId) {
        List<RoomMsgVO> voList = new ArrayList<>();
        List<String> tableSuffixArr = DateHelper.ARABIAN.getTableSuffixArr(start, end);
        List<RoomMsg> roomMsgList = roomMsgMapper.selectByRoomId(tableSuffixArr.get(0), start, end, roomId);
        for (RoomMsg roomMsg : roomMsgList) {
            RoomMsgVO vo = new RoomMsgVO();
            ActorData actorData = coreActorDao.getActorDataFromCache(roomMsg.getFromUid());
            if (null == actorData) {
                logger.error("cannot find actor data uid={}", roomMsg.getFromUid());
                continue;
            }
            String dateTime = DateHelper.ARABIAN.datetimeToStr(new Date(roomMsg.getCtime() * 1000L));
            int rid = actorData.getRid();
            Integer atRid = null;
            String content = roomMsg.getMsgBody();
            if (roomMsg.getMsyType() == 222) {
                try {
                    JSONObject jsonObject = JSONObject.parseObject(roomMsg.getMsgBody());
                    String aid = jsonObject.getString("aid");
                    content = jsonObject.getString("content");
                    ActorData atActorData = coreActorDao.getActorDataFromCache(aid);
                    if (null == atActorData) {
                        logger.error("cannot find actor data uid={}", roomMsg.getFromUid());
                        continue;
                    }
                    atRid = atActorData.getRid();
                } catch (Exception e) {
                    logger.error("parse aid error, error msg={}", e.getMessage());
                }
            }
            vo.setDateTime(dateTime);
            vo.setRid(rid);
            vo.setAtRid(atRid);
            vo.setContent(content);
            voList.add(vo);
        }
        return voList;
    }

    @Resource
    private RechargeRedis rechargeRedis;

    public List<EnterRoomUserVO> enterRoomReports(int start, int end, String roomId) {
        List<EnterRoomUserVO> voList = new ArrayList<>();
        List<EnterRoom> enterRoomList = enterRoomDao.selectByRoomId(start, end, roomId);
        for (EnterRoom enterRoom : enterRoomList) {
            EnterRoomUserVO vo = new EnterRoomUserVO();
            ActorData actorData = coreActorDao.getActorDataFromCache(enterRoom.getUserId());
            if (null == actorData) {
                logger.error("cannot find actor data uid={}", enterRoom.getUserId());
                continue;
            }
            String dateTime = DateHelper.ARABIAN.datetimeToStr(new Date(enterRoom.getCtime() * 1000L));
            int rid = actorData.getRid();
            vo.setDateTime(dateTime);
            vo.setRid(rid);
            vo.setRmbUser(rechargeRedis.isRechargeUser(enterRoom.getUserId()));
            voList.add(vo);
        }
        return voList;
    }

    public ColumnChartVO rechargeDiamondReports() {
        List<ColumnChartVO.Data> list = new ArrayList<>();
        LocalDate startDate = DateSupport.ARABIAN.getDayOffset(-30);
        LocalDate today = DateSupport.ARABIAN.getToday();
        while (startDate.isBefore(today)) {
            String strDate = DateSupport.format(startDate);
            Integer[] timeArr = DateHelper.ARABIAN.getStartOrEndSeconds(strDate, strDate);
            List<RechargeDailyInfoData> rechargeList = rechargeDailyInfoDao.selectList(timeArr[0], --timeArr[1]);
            long rechargeAmount = 0;
            long coinSellerRechargeAmount = 0;
            long iosRechargeAmount = 0;
            long googleRechargeAmount = 0;
            long thirdPartyRechargeAmount = 0;
            if (!CollectionUtils.isEmpty(rechargeList)) {
                for (RechargeDailyInfoData data : rechargeList) {
                    if (data.getPayType() == 1) {
                        googleRechargeAmount += data.getRechargeDiamond();
                        rechargeAmount += data.getRechargeDiamond();
                    } else if (data.getPayType() == 2) {
                        iosRechargeAmount += data.getRechargeDiamond();
                        rechargeAmount += data.getRechargeDiamond();
                    } else if (data.getPayType() == 8) {
                        thirdPartyRechargeAmount += data.getRechargeDiamond();
                        rechargeAmount += data.getRechargeDiamond();
                    } else if (data.getPayType() == 9) {
                        coinSellerRechargeAmount += data.getRechargeDiamond();
                        rechargeAmount += data.getRechargeDiamond();
                    }
                }
            }
            ColumnChartVO.Data data = new ColumnChartVO.Data();
            data.setKey(strDate);
            data.setRechargeAmount(rechargeAmount);
            data.setCoinSellerRechargeAmount(coinSellerRechargeAmount);
            data.setIosRechargeAmount(iosRechargeAmount);
            data.setGoogleRechargeAmount(googleRechargeAmount);
            data.setThirdPartyRechargeAmount(thirdPartyRechargeAmount);
            list.add(data);
            startDate = startDate.plusDays(1);
        }
        return new ColumnChartVO(list);
    }

    public PageResultVO<DetectUserRecordVO> detectUserRecord(String rid, Integer detectType, Integer page, Integer pageSize) {
        PageResultVO<DetectUserRecordVO> resultVO = new PageResultVO<>();
        String queryUid = null;
        if(StringUtils.hasLength(rid)){
            ActorData actorData = actorCoreDao.getActorByRidOrAlphaRid(rid);
            if (actorData == null) {
                resultVO.setList(Collections.emptyList());
                return resultVO;
            }
            queryUid = actorData.getUid();
        }
        List<DetectUserRecordVO> list = new ArrayList<>();
        page = page != null && page != 0 ? page : 1;
        pageSize = pageSize != null && pageSize != 0 ? pageSize : 10;
        IPage<DetectUserRecordData> iPage = detectUserRecordDao.getRecords(queryUid, detectType, page, pageSize);
        for (DetectUserRecordData recordData : iPage.getRecords()) {
            DetectUserRecordVO vo = new DetectUserRecordVO();
            BeanUtils.copyProperties(recordData, vo);
            String recordUid = recordData.getUid();
            ActorData actorData = actorCoreDao.getActorDataFromCache(recordUid);
            vo.setRid(actorData.getRid());
            list.add(vo);
        }
        resultVO.setList(list);
        resultVO.setTotal(iPage.getTotal());
        return resultVO;
    }

    public HttpResult<Object> detectImgPass(Integer id) {
        HttpResult<Object> result = new HttpResult<>();
        if (id == null || id <= 0) {
            return result.error("param error");
        }
        DetectUserRecordData recordData = detectUserRecordDao.selectById(id);
        if (recordData == null || recordData.getDetectType() != 1) {
            return result.error("param error");
        }
        if (recordData.getStatus() == 1) {
            return result.error("已放行，请勿重复操作");
        }
        String imgUrl = recordData.getDetectInfo();
        String urlMD5 = DigestUtils.md5DigestAsHex(imgUrl.getBytes(StandardCharsets.UTF_8));
        DiscernImageData discernImageData = discernImageDao.getDiscernImageData(urlMD5);
        if (discernImageData == null) {
            return result.error("操作失败，请联系开发人员");
        }
        discernImageData.setIsSafe(1);
        discernImageDao.update(discernImageData);
        recordData.setStatus(1);
        detectUserRecordDao.update(recordData);
        return result.ok();
    }
}
