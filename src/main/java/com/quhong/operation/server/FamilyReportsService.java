package com.quhong.operation.server;

import com.alibaba.excel.EasyExcel;
import com.alibaba.excel.ExcelWriter;
import com.alibaba.excel.write.metadata.WriteSheet;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.google.common.collect.Interner;
import com.google.common.collect.Interners;
import com.quhong.config.AsyncConfig;
import com.quhong.config.CaffeineCacheConfig;
import com.quhong.core.date.DateSupport;
import com.quhong.data.ActorData;
import com.quhong.data.condition.FamilyCondition;
import com.quhong.enums.HttpCode;
import com.quhong.exception.CommonException;
import com.quhong.exception.CommonH5Exception;
import com.quhong.mongo.dao.ActorDao;
import com.quhong.mongo.dao.CharmStatDao;
import com.quhong.mongo.dao.FamilyDailyIncomeDao;
import com.quhong.mongo.dao.GameStatDao;
import com.quhong.mysql.dao.*;
import com.quhong.mysql.data.AnchorReportData;
import com.quhong.mysql.data.FamilyDailyDetailTotalData;
import com.quhong.mysql.data.FamilyData;
import com.quhong.mysql.data.FamilyMemberDetailData;
import com.quhong.operation.constant.AccountConstant;
import com.quhong.operation.dao.ManagerDao;
import com.quhong.operation.dao.RoomMicStatDao;
import com.quhong.operation.share.condition.EmployeeDataCondition;
import com.quhong.operation.share.elasticsearch.dao.CharmLogEsDao;
import com.quhong.operation.share.mongobean.Manager;
import com.quhong.operation.share.vo.*;
import com.quhong.operation.share.vo.reports.FamilyMemberReportVO;
import com.quhong.operation.share.vo.reports.FamilyReportVO;
import com.quhong.operation.utils.DateHelper;
import com.quhong.operation.utils.EmailUtils;
import com.quhong.operation.utils.OSSUploadUtils;
import com.quhong.utils.AsyncUtils;
import com.quhong.utils.CacheUtils;
import com.quhong.utils.CollectionUtil;
import com.quhong.utils.RoomUtils;
import com.quhong.vo.ListVO;
import jakarta.annotation.Resource;
import org.apache.http.entity.ContentType;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.BeanUtils;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.mock.web.MockMultipartFile;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;
import org.springframework.web.multipart.MultipartFile;

import java.io.ByteArrayInputStream;
import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.time.LocalDate;
import java.time.ZoneId;
import java.util.*;
import java.util.concurrent.Executor;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2023/6/5
 */
@Service
public class FamilyReportsService {

    private static final Logger logger = LoggerFactory.getLogger(FamilyReportsService.class);

    private static final Interner<String> stringPool = Interners.newWeakInterner();

    private static final SimpleDateFormat MONTH_FORMAT = new SimpleDateFormat("yyyy-MM");
    private static final SimpleDateFormat DATE_FORMAT = new SimpleDateFormat("yyyy-MM-dd");
    private static final ZoneId GMT3 = ZoneId.of("+3");

    private static final String REPORT_FILE_PATH = "reports/";

    private static final int PAGE_SIZE = 20;

    @Resource
    private FamilyDao familyDao;
    @Resource
    private FamilyMemberDao familyMemberDao;
    @Resource
    private ActorDao actorDao;
    @Resource
    private RoomMicStatDao roomMicStatDao;
    @Resource
    private ManagerDao managerDao;
    @Resource
    private JoinFamilyRecordDao joinFamilyRecordDao;
    @Resource
    private FamilyManageService familyManageService;
    @Resource
    private FamilyReportsService familyReportsService;
    @Resource(name = AsyncConfig.ASYNC_TASK)
    private Executor executor;
    @Resource
    private FamilyDailyDetailDao familyDailyDetailDao;
    @Resource
    private FamilyDailyIncomeDao familyDailyIncomeDao;
    @Resource
    private CharmStatDao charmStatDao;
    @Resource
    private GameStatDao gameStatDao;
    @Resource
    private MicTimeDao micTimeDao;
    @Resource
    private CharmLogEsDao charmLogEsDao;
    @Resource
    private DailySendGiftRecordDao dailySendGiftRecordDao;
    @Resource
    private AnchorReportDao anchorReportDao;

    /**
     * 公会维度报表
     */
    public PageResultVO<FamilyReportVO> familyReport(String uid, FamilyCondition condition) {
        handleFamilyCondition(uid, condition);
        PageResultVO<FamilyReportVO> pageResultVO = new PageResultVO<>();
        boolean isMonth = condition.getStartTime().length() == "yyyy-MM".length();
        String[] dateArr = getStrDateArray(condition.getStartTime(), condition.getEndTime(), isMonth);
        Collections.reverse(Arrays.asList(dateArr));
        int pageSize = condition.getPageSize() != null && condition.getPageSize() != 0 ? condition.getPageSize() / dateArr.length : PAGE_SIZE / dateArr.length;
        int page = condition.getPage() != null && condition.getPage() != 0 ? condition.getPage() : 1;
        condition.setStatus(1);
        IPage<FamilyData> iPage = familyDao.selectList(condition, page, pageSize);
        List<FamilyReportVO> list = new ArrayList<>();
        if (!CollectionUtils.isEmpty(iPage.getRecords())) {
            for (FamilyData familyData : iPage.getRecords()) {
                for (String dateStr : dateArr) {
                    Integer[] time = isMonth ? DateHelper.ARABIAN.getStartOrEndSeconds(dateStr + "-01", getMaxDateMonth(dateStr)) : DateHelper.ARABIAN.getTimeWhereRange(dateStr);
                    FamilyReportVO vo = new FamilyReportVO();
                    vo.setDate(dateStr.replace("-", "/"));
                    fillFamilyReportVO(vo, condition.getIncome(), familyData, time[0], --time[1]);
                    if (vo.getRid() == null) {
                        continue;
                    }
                    list.add(vo);
                }
            }
        }
        pageResultVO.setList(list);
        pageResultVO.setTotal(iPage.getTotal() * dateArr.length);
        return pageResultVO;
    }

    /**
     * 公会维度合计报表
     */
    public PageResultVO<FamilyReportVO> familyTotalReport(String uid, FamilyCondition condition) {
        handleFamilyCondition(uid, condition);
        PageResultVO<FamilyReportVO> pageResultVO = new PageResultVO<>();
        int pageSize = condition.getPageSize() != null && condition.getPageSize() != 0 ? condition.getPageSize() : 20;
        int page = condition.getPage() != null && condition.getPage() != 0 ? condition.getPage() : 1;
        condition.setStatus(1);
        IPage<FamilyData> iPage = familyDao.selectList(condition, page, pageSize);
        List<FamilyReportVO> list = new ArrayList<>();
        Integer[] timeArr = getTimeArray(condition.getStartTime(), condition.getEndTime());
        int startTime = timeArr[0];
        int endTime = --timeArr[1];
        if (!CollectionUtils.isEmpty(iPage.getRecords())) {
            for (FamilyData familyData : iPage.getRecords()) {
                FamilyReportVO vo = new FamilyReportVO();
                vo.setDate(condition.getStartTime() + " ~ " + condition.getEndTime());
                fillFamilyReportVO(vo, condition.getIncome(), familyData, startTime, endTime);
                if (vo.getRid() == null) {
                    continue;
                }
                list.add(vo);
            }
        }
        pageResultVO.setList(list);
        pageResultVO.setTotal(iPage.getTotal());
        return pageResultVO;
    }

    /**
     * 主播维度报表
     */
    public PageResultVO<FamilyMemberReportVO> familyMemberReport(String uid, FamilyCondition condition, boolean isDownload) {
        handleFamilyCondition(uid, condition);
        PageResultVO<FamilyMemberReportVO> pageResultVO = new PageResultVO<>();
        boolean isMonth = condition.getStartTime().length() == "yyyy-MM".length();
        String[] dateArr = getStrDateArray(condition.getStartTime(), condition.getEndTime(), isMonth);
        Collections.reverse(Arrays.asList(dateArr));
        int pageSize = condition.getPageSize() != null && condition.getPageSize() != 0 ? condition.getPageSize() / dateArr.length : 20 / dateArr.length;
        int page = condition.getPage() != null && condition.getPage() != 0 ? condition.getPage() : 1;
        condition.setOffset((page - 1) * pageSize);
        List<FamilyMemberDetailData> memberDetailList = familyMemberDao.selectDetailList(condition);
        int count = familyMemberDao.selectDetailCount(condition);
        List<FamilyMemberReportVO> list = new ArrayList<>();
        if (!CollectionUtils.isEmpty(memberDetailList)) {
            for (FamilyMemberDetailData memberDetailData : memberDetailList) {
                for (String dateStr : dateArr) {
                    ActorData actorData = actorDao.getActorDataFromCache(memberDetailData.getUid());
                    if (actorData == null) {
                        logger.error("can not find actor data. uid={}", memberDetailData.getUid());
                        continue;
                    }
                    Integer[] time = isMonth ? DateHelper.ARABIAN.getStartOrEndSeconds(dateStr + "-01", getMaxDateMonth(dateStr)) : DateHelper.ARABIAN.getTimeWhereRange(dateStr);
                    FamilyMemberReportVO vo = new FamilyMemberReportVO();
                    vo.setDate(dateStr.replace("-", "/"));
                    newFillFamilyMemberReportVO(vo, condition.getIncome(), memberDetailData, actorData, time[0], --time[1]);
                    // fillFamilyMemberReportVO(vo, condition.getIncome(), memberDetailData, actorData, time[0], --time[1]);
                    if (vo.getMemberRid() == null) {
                        continue;
                    }
                    if (isDownload) {
                        if (vo.getReceiveGiftCharm() == 0 && vo.getReceiveGiftUserNum() == 0 && vo.getValidBroadcastDays() == 0 &&
                                vo.getConsumeCharm() == 0 && vo.getConsumeCharmExchangeSalary() == 0 && vo.getRoomLuckyGiftBetsNum() == 0) {
                            continue;
                        }
                    }
                    list.add(vo);
                }
            }
        }
        pageResultVO.setList(list);
        pageResultVO.setTotal(count);
        return pageResultVO;
    }

    /**
     * 主播维度合计报表
     */
    public PageResultVO<FamilyMemberReportVO> familyMemberTotalReport(String uid, FamilyCondition condition, boolean isDownload) {
        handleFamilyCondition(uid, condition);
        PageResultVO<FamilyMemberReportVO> pageResultVO = new PageResultVO<>();
        int pageSize = condition.getPageSize() != null && condition.getPageSize() != 0 ? condition.getPageSize() : 20;
        int page = condition.getPage() != null && condition.getPage() != 0 ? condition.getPage() : 1;
        condition.setOffset((page - 1) * pageSize);
        List<FamilyMemberDetailData> memberDetailList = familyMemberDao.selectDetailList(condition);
        int count = familyMemberDao.selectDetailCount(condition);
        List<FamilyMemberReportVO> list = new ArrayList<>();
        Integer[] timeArr = getTimeArray(condition.getStartTime(), condition.getEndTime());
        int startTime = timeArr[0];
        int endTime = --timeArr[1];
        if (!CollectionUtils.isEmpty(memberDetailList)) {
            for (FamilyMemberDetailData memberDetailData : memberDetailList) {
                ActorData actorData = actorDao.getActorDataFromCache(memberDetailData.getUid());
                if (actorData == null) {
                    logger.error("can not find actor data. uid={}", memberDetailData.getUid());
                    continue;
                }
                FamilyMemberReportVO vo = new FamilyMemberReportVO();
                vo.setDate(condition.getStartTime() + " ~ " + condition.getEndTime());
                newFillFamilyMemberReportVO(vo, condition.getIncome(), memberDetailData, actorData, startTime, endTime);
                // fillFamilyMemberReportVO(vo, condition.getIncome(), memberDetailData, actorData, startTime, endTime);
                if (vo.getMemberRid() == null) {
                    continue;
                }
                if (isDownload) {
                    if (vo.getReceiveGiftCharm() == 0 && vo.getReceiveGiftUserNum() == 0 && vo.getValidBroadcastDays() == 0 &&
                            vo.getConsumeCharm() == 0 && vo.getConsumeCharmExchangeSalary() == 0 && vo.getRoomLuckyGiftBetsNum() == 0) {
                        continue;
                    }
                }
                list.add(vo);
            }
        }
        pageResultVO.setList(list);
        pageResultVO.setTotal(count);
        return pageResultVO;
    }

    private void handleFamilyCondition(String uid, FamilyCondition condition) {
        if (StringUtils.hasLength(condition.getOwnerRid())) {
            ActorData owner = actorDao.getActorByRidOrAlphaRid(condition.getOwnerRid());
            condition.setOwnerUid(owner != null ? owner.getUid() : "---");
        }
        if (StringUtils.hasLength(condition.getMemberRid())) {
            ActorData member = actorDao.getActorByRidOrAlphaRid(condition.getMemberRid());
            condition.setMemberUid(member != null ? member.getUid() : "---");
        }
        if (AccountConstant.HIGHEST_AUTHORITY_ADMIN.equals(uid)) {
            return;
        }
        Manager manager = managerDao.getDataByUid(uid);
        if (manager.getFamily_manage_role() == 1) {
            condition.setManager(manager.getAccount());
        } else if (manager.getFamily_manage_role() == 2) {
            condition.setSuperManager(manager.getAccount());
            familyManageService.getManagerList(condition, manager);
        } else {
            if (StringUtils.hasLength(condition.getSuperManager())) {
                Manager superManager = managerDao.getManager(condition.getSuperManager());
                if (superManager != null) {
                    familyManageService.getManagerList(condition, superManager);
                }
            }
        }
    }

    private Integer[] getTimeArray(String startTime, String endTime) {
        boolean isMonth = startTime.length() == "yyyy-MM".length();
        if (isMonth) {
            return DateHelper.ARABIAN.getStartOrEndSeconds(startTime + "-01", getMaxDateMonth(endTime));
        } else {
            return DateHelper.ARABIAN.getStartOrEndSeconds(startTime, endTime);
        }
    }

    private String[] getStrDateArray(String startTime, String endTime, boolean isMonth) {
        if (isMonth) {
            return getMonthBetweenDate(startTime + "-02", endTime + "-02").toArray(new String[0]);
        } else {
            Integer[] timeArr = DateHelper.ARABIAN.getStartOrEndSeconds(startTime, endTime);
            return DateHelper.ARABIAN.getDateDiffArray(timeArr[0], --timeArr[1]);
        }
    }

    public void fillFamilyMemberReportVO(FamilyMemberReportVO vo, Integer income, FamilyMemberDetailData memberDetailData, ActorData actorData, int startTime, int endTime) {
        long totalIncome = familyReportsService.getReceiveGiftCharm(memberDetailData.getFamilyId(), memberDetailData.getUid(), startTime, endTime);
        if (income != null && totalIncome < income) {
            return;
        }
        int startDate = DateSupport.ARABIAN.getIntDate(startTime);
        int endDate = DateSupport.ARABIAN.getIntDate(endTime);
        vo.setMemberRid(actorData.getRid());
        vo.setMemberStrRid(actorData.getShowRid());
        vo.setMemberName(actorData.getName());
        vo.setMemberSvipLevel(String.valueOf(actorData.getSvipLevel()));
        vo.setMemberType(memberDetailData.getRole());
        vo.setFamilyRid(memberDetailData.getFamilyRid());
        vo.setFamilyName(memberDetailData.getFamilyName());
        ActorData owner = actorDao.getActorDataFromCache(memberDetailData.getFamilyOwnerUid());
        vo.setOwnerRid(owner != null ? owner.getRid() : null);
        vo.setOwnerStrRid(owner != null ? owner.getShowRid() : null);
        vo.setOwnerName(owner != null ? owner.getName() : "");
        vo.setCreateTime(DateHelper.ARABIAN.datetimeToStr(new Date(memberDetailData.getFamilyCtime() * 1000L)));
        vo.setManager(memberDetailData.getFamilyManager());
        vo.setSuperManager(memberDetailData.getFamilySuperManager());
        vo.setJoinTime(DateHelper.ARABIAN.datetimeToStr(new Date(memberDetailData.getCtime() * 1000L)));
        long liveCharmIncome = getLiveCharmIncome(memberDetailData.getFamilyId(), memberDetailData.getUid(), startTime, endTime);
        vo.setReceiveGiftCharm(totalIncome - liveCharmIncome);
        vo.setLiveReceiveGiftCharm(liveCharmIncome);
        vo.setReceiveGiftUserNum(familyReportsService.getReceiveGiftUserNum(memberDetailData.getUid(), startTime, endTime));
        vo.setChatBroadcastDays(familyReportsService.getWorkDays(memberDetailData.getUid(), 0, startDate, endDate));
        vo.setLiveBroadcastDays(familyReportsService.getWorkDays(memberDetailData.getUid(), 1, startDate, endDate));
        vo.setValidBroadcastDays(familyReportsService.getWorkDays(memberDetailData.getUid(), 2, startDate, endDate));
        vo.setConsumeCharm(familyReportsService.getAnchorConsumeCharm(memberDetailData.getUid(), startTime, endTime));
        vo.setConsumeCharmExchangeSalary(familyReportsService.getAnchorExchangeSalary(memberDetailData.getUid(), startTime, endTime));
        if (owner != null) {
            int firstJoinTime = joinFamilyRecordDao.getFirstJoinFamilyTime(owner.getTn_id());
            vo.setFirstJoinTime(firstJoinTime != 0 ? DateHelper.ARABIAN.datetimeToStr(new Date(firstJoinTime * 1000L)) : "");
        }
        vo.setRoomLuckyGiftBetsNum(0L);
        vo.setGameCostBeans(gameStatDao.getUserGameTotalCost(memberDetailData.getUid(), startTime, endTime));
    }

    public void newFillFamilyMemberReportVO(FamilyMemberReportVO vo, Integer income, FamilyMemberDetailData memberDetailData, ActorData actorData, int startTime, int endTime) {
        int startDate = DateSupport.ARABIAN.getIntDate(startTime);
        int endDate = DateSupport.ARABIAN.getIntDate(endTime);
        AnchorReportData reportData = anchorReportDao.getSumData(memberDetailData.getFamilyId(), memberDetailData.getUid(), startDate, endDate);
        if (income != null && reportData.getReceiveGiftCharm() + reportData.getLiveReceiveGiftCharm() < income) {
            return;
        }
        BeanUtils.copyProperties(reportData, vo);
        vo.setChatBroadcastDays(familyReportsService.getWorkDays(memberDetailData.getUid(), 0, startDate, endDate));
        vo.setLiveBroadcastDays(familyReportsService.getWorkDays(memberDetailData.getUid(), 1, startDate, endDate));
        vo.setValidBroadcastDays(familyReportsService.getWorkDays(memberDetailData.getUid(), 2, startDate, endDate));
        vo.setMemberRid(actorData.getRid());
        vo.setMemberStrRid(actorData.getShowRid());
        vo.setMemberName(actorData.getName());
        vo.setMemberSvipLevel(String.valueOf(actorData.getSvipLevel()));
        vo.setMemberType(memberDetailData.getRole());
        vo.setFamilyRid(memberDetailData.getFamilyRid());
        vo.setFamilyName(memberDetailData.getFamilyName());
        ActorData owner = actorDao.getActorDataFromCache(memberDetailData.getFamilyOwnerUid());
        vo.setOwnerRid(owner != null ? owner.getRid() : null);
        vo.setOwnerStrRid(owner != null ? owner.getShowRid() : null);
        vo.setOwnerName(owner != null ? owner.getName() : "");
        vo.setCreateTime(DateHelper.ARABIAN.datetimeToStr(new Date(memberDetailData.getFamilyCtime() * 1000L)));
        vo.setManager(memberDetailData.getFamilyManager());
        vo.setSuperManager(memberDetailData.getFamilySuperManager());
        vo.setJoinTime(DateHelper.ARABIAN.datetimeToStr(new Date(memberDetailData.getCtime() * 1000L)));
        long totalIncome = familyReportsService.getReceiveGiftCharm(0, memberDetailData.getUid(), startTime, endTime);
        long liveCharmIncome = getLiveCharmIncome(0, memberDetailData.getUid(), startTime, endTime);
        vo.setReceiveGiftCharm(totalIncome - liveCharmIncome);
        vo.setLiveReceiveGiftCharm(liveCharmIncome);
        vo.setReceiveGiftUserNum(familyReportsService.getReceiveGiftUserNum(memberDetailData.getUid(), startTime, endTime));
        if (owner != null) {
            int firstJoinTime = joinFamilyRecordDao.getFirstJoinFamilyTime(owner.getTn_id());
            vo.setFirstJoinTime(firstJoinTime != 0 ? DateHelper.ARABIAN.datetimeToStr(new Date(firstJoinTime * 1000L)) : "");
        }
    }

    @Cacheable(value = "cache", cacheManager = CaffeineCacheConfig.EXPIRE_10M_AFTER_WRITE,
            key = "#root.method.name+T(org.springframework.util.StringUtils).arrayToDelimitedString(args,'-')")
    public long getReceiveGiftCharm(int familyId, String uid, int startTime, int endTime) {
        return charmStatDao.getUserTotalCharm(uid, familyId, 1, startTime, endTime);
    }

    @Cacheable(value = "cache", cacheManager = CaffeineCacheConfig.EXPIRE_10M_AFTER_WRITE,
            key = "#root.method.name+T(org.springframework.util.StringUtils).arrayToDelimitedString(args,'-')")
    public int getReceiveGiftUserNum(String uid, int startTime, int endTime) {
        return dailySendGiftRecordDao.getReceiveGiftUserNum(uid, DateSupport.ARABIAN.getIntDate(startTime), DateSupport.ARABIAN.getIntDate(endTime));
    }

    @Cacheable(value = "cache", cacheManager = CaffeineCacheConfig.EXPIRE_10M_AFTER_WRITE,
            key = "#root.method.name+T(org.springframework.util.StringUtils).arrayToDelimitedString(args,'-')")
    public int getValidBroadcastDays(String uid, int startTime, int endTime) {
        return roomMicStatDao.anchorUpMicTimeValidDays(startTime, endTime, uid, 2 * 60 * 60);
    }

    @Cacheable(value = "cache", cacheManager = CaffeineCacheConfig.EXPIRE_10M_AFTER_WRITE,
            key = "#root.method.name+T(org.springframework.util.StringUtils).arrayToDelimitedString(args,'-')")
    public int getValidBroadcastDays(String roomId, String uid, int startTime, int endTime) {
        return roomMicStatDao.getValidBroadcastDays(roomId, uid, startTime, endTime, 2 * 60 * 60);
    }

    @Cacheable(value = "cache", cacheManager = CaffeineCacheConfig.EXPIRE_10M_AFTER_WRITE,
            key = "#root.method.name+T(org.springframework.util.StringUtils).arrayToDelimitedString(args,'-')")
    public long getAnchorConsumeCharm(String uid, int startTime, int endTime) {
        return charmStatDao.getConsumeCharm(uid, startTime, endTime);
    }

    @Cacheable(value = "cache", cacheManager = CaffeineCacheConfig.EXPIRE_10M_AFTER_WRITE,
            key = "#root.method.name+T(org.springframework.util.StringUtils).arrayToDelimitedString(args,'-')")
    public long getAnchorExchangeSalary(String uid, int startTime, int endTime) {
        return charmStatDao.getUserTotalCharm(uid, null, -1, startTime, endTime);
    }

    private void fillFamilyReportVO(FamilyReportVO vo, Integer income, FamilyData familyData, int startTime, int endTime) {
        long receiveGiftCharm = familyReportsService.getReceiveGiftCharm(familyData.getId(), null, startTime, endTime);
        if (income != null && receiveGiftCharm < income) {
            return;
        }
        vo.setRid(familyData.getRid());
        vo.setName(familyData.getName());
        ActorData actorData = actorDao.getActorDataFromCache(familyData.getOwnerUid());
        vo.setOwnerRid(actorData != null ? actorData.getRid() : null);
        vo.setOwnerStrRid(actorData != null ? actorData.getShowRid() : null);
        vo.setOwnerName(actorData != null ? actorData.getName() : "");
        vo.setOwnerSvipLevel(actorData != null ? String.valueOf(actorData.getSvipLevel()) : "0");
        vo.setManager(familyData.getManager());
        vo.setSuperManager(familyData.getSuperManager());
        vo.setCreateTime(DateHelper.ARABIAN.datetimeToStr(new Date(familyData.getCtime() * 1000L)));
        List<String> memberUidList = familyMemberDao.selectMemberUidListFromCache(familyData.getId());
        vo.setMembersNum(memberUidList.size());
        vo.setNewMembersNum(familyReportsService.getNewMembersNum(familyData.getId(), startTime, endTime));
        vo.setReceiveGiftMembersNum(familyReportsService.getReceiveGiftMembersNum(null, startTime, endTime));
        vo.setReceiveGiftCharm(receiveGiftCharm);
        vo.setLiveCharmIncome(familyReportsService.getLiveCharmIncome(familyData.getId(), "", startTime, endTime));
        vo.setLiveBroadcastTime(familyReportsService.getLiveBroadcastTime(memberUidList, startTime, endTime));
        Map<String, Integer> upMicSumTimeMap = familyReportsService.getUpMicSumTimeMap(memberUidList, startTime, endTime);
        vo.setBroadcastMembersNum(familyReportsService.getBroadcastMembersNum(upMicSumTimeMap, 0));
        vo.setValidBroadcastMembersNum(familyReportsService.getBroadcastMembersNum(upMicSumTimeMap, 30 * 60));
        String ownerRoomId = RoomUtils.formatRoomId(familyData.getOwnerUid());
        vo.setRoomReceiveGiftMembersNum(familyReportsService.getRoomReceiveGiftMembersNum(familyData.getId(), ownerRoomId, null, startTime, endTime)); // todo
        vo.setRoomReceiveCoinsNum(charmLogEsDao.getRoomReceiveGiftIncome(null, familyData.getId(), Arrays.asList(ownerRoomId, RoomUtils.getLiveRoomId(ownerRoomId)), startTime, endTime));
        vo.setGameCostBeans(gameStatDao.getUsersGameTotalCost(memberUidList, startTime, endTime));
        vo.setAnchorCommission(charmStatDao.getUserTotalCharm(familyData.getOwnerUid(), null, 6, startTime, endTime));
    }

    @Cacheable(value = "cache", cacheManager = CaffeineCacheConfig.EXPIRE_10M_AFTER_WRITE,
            key = "#root.method.name+T(org.springframework.util.StringUtils).arrayToDelimitedString(args,'-')")
    public Integer getLiveBroadcastTime(List<String> memberUidList, int startTime, int endTime) {
        if (CollectionUtils.isEmpty(memberUidList)) {
            return 0;
        }
        return micTimeDao.getUsersLiveTotalTime(new HashSet<>(memberUidList), startTime, endTime) / 60;
    }


    @Cacheable(value = "cache", cacheManager = CaffeineCacheConfig.EXPIRE_10M_AFTER_WRITE,
            key = "#root.method.name+T(org.springframework.util.StringUtils).arrayToDelimitedString(args,'-')")
    public int getWorkDays(String uid, int type, int startDate, int endDate) {
        return micTimeDao.getWorkDayNew(uid, type, startDate, endDate, 2 * 60 * 60);
    }

    @Cacheable(value = "cache", cacheManager = CaffeineCacheConfig.EXPIRE_10M_AFTER_WRITE,
            key = "#root.method.name+T(org.springframework.util.StringUtils).arrayToDelimitedString(args,'-')")
    public long getLiveCharmIncome(Integer familyId, String uid, int startTime, int endTime) {
        return charmStatDao.getLiveCharmIncome(familyId, uid, startTime, endTime);
    }

    @Cacheable(value = "cache", cacheManager = CaffeineCacheConfig.EXPIRE_10M_AFTER_WRITE,
            key = "#root.method.name+T(org.springframework.util.StringUtils).arrayToDelimitedString(args,'-')")
    public int getNewMembersNum(int familyId, int startTime, int endTime) {
        return familyMemberDao.selectNewMemberCount(familyId, startTime, endTime);
    }

    @Cacheable(value = "cache", cacheManager = CaffeineCacheConfig.EXPIRE_10M_AFTER_WRITE,
            key = "#root.method.name+T(org.springframework.util.StringUtils).arrayToDelimitedString(args,'-')")
    public int getReceiveGiftMembersNum(List<String> memberUidList, int startTime, int endTime) {
        if (CollectionUtils.isEmpty(memberUidList)) {
            return 0;
        }
        return dailySendGiftRecordDao.getHasReceivedGiftUserNum(memberUidList, DateSupport.ARABIAN.getIntDate(startTime), DateSupport.ARABIAN.getIntDate(endTime));
    }

    @Cacheable(value = "cache", cacheManager = CaffeineCacheConfig.EXPIRE_10M_AFTER_WRITE,
            key = "#root.method.name+T(org.springframework.util.StringUtils).arrayToDelimitedString(args,'-')")
    public Map<String, Integer> getUpMicSumTimeMap(List<String> memberUidList, int startTime, int endTime) {
        if (CollectionUtils.isEmpty(memberUidList)) {
            return Collections.emptyMap();
        }
        return roomMicStatDao.getUpMicStat(startTime, endTime, Collections.emptyList(), memberUidList);
    }

    @Cacheable(value = "cache", cacheManager = CaffeineCacheConfig.EXPIRE_10M_AFTER_WRITE,
            key = "#root.method.name+T(org.springframework.util.StringUtils).arrayToDelimitedString(args,'-')")
    public int getRoomReceiveGiftMembersNum(int familyId, String ownerRoomId, List<String> memberUidList, int startTime, int endTime) {
        if (CollectionUtils.isEmpty(memberUidList)) {
            return 0;
        }
        if (CollectionUtils.isEmpty(memberUidList)) {
            return 0;
        }
        return dailySendGiftRecordDao.getHasReceivedGiftUserNum(memberUidList, DateSupport.ARABIAN.getIntDate(startTime), DateSupport.ARABIAN.getIntDate(endTime));
    }

    private Integer getBroadcastMembersNum(Map<String, Integer> upMicSumTimeMap, int validUpMicTime) {
        int broadcastMembersNum = 0;
        for (Map.Entry<String, Integer> entry : upMicSumTimeMap.entrySet()) {
            if (entry.getValue() > validUpMicTime) {
                broadcastMembersNum++;
            }
        }
        return broadcastMembersNum;
    }

    /**
     * 获取两个日期之间的所有月份 (年月)
     */
    private List<String> getMonthBetweenDate(String startTime, String endTime) {
        // 声明保存日期集合
        List<String> list = new ArrayList<>();
        // 转化成日期类型
        Date startDate = DateHelper.ARABIAN.stringToDate(startTime);
        Date endDate = DateHelper.ARABIAN.stringToDate(endTime);
        //用Calendar 进行日期比较判断
        Calendar calendar = Calendar.getInstance();
        while (startDate.getTime() <= endDate.getTime()) {
            // 把日期添加到集合
            list.add(MONTH_FORMAT.format(startDate));
            // 设置日期
            calendar.setTime(startDate);
            //把日期增加一天
            calendar.add(Calendar.MONTH, 1);
            // 获取增加后的日期
            startDate = calendar.getTime();
        }
        return list;
    }

    /**
     * 获取指定月份的最后一天
     * 例：2023-01 -> 2023-01-31
     */
    private String getMaxDateMonth(String month) {
        try {
            Date nowDate = MONTH_FORMAT.parse(month);
            Calendar calendar = Calendar.getInstance();
            calendar.setTime(nowDate);
            calendar.set(Calendar.DAY_OF_MONTH, calendar.getActualMaximum(Calendar.DAY_OF_MONTH));
            return DATE_FORMAT.format(calendar.getTime());
        } catch (ParseException e) {
            logger.error("getMaxDateMonth error. month={} {}", month, e.getMessage(), e);
        }
        return "";
    }

    public void asyncDownloadReport(FamilyCondition condition) {
        String key = "operationDownloadReport";
        synchronized (stringPool.intern(key)) {
            if (CacheUtils.hasKey(key)) {
                throw new CommonH5Exception(new HttpCode(1, "当前有报表正在下载，请稍后再试"));
            }
            CacheUtils.put(key, "running");
            executor.execute(() -> {
                Map<String, String> receiveAccountMap = new HashMap<>(1);
                Manager manager = managerDao.getDataByUid(condition.getUid());
                receiveAccountMap.put(condition.getEmail(), manager.getAccount());
                String fileName = "report_data_" + condition.getStartTime() + "_" + condition.getEndTime();
                String reportDownloadUrl = "";
                String subject = "";
                switch (condition.getDownloadType()) {
                    case 1:
                        subject = "公会维度报表";
                        PageResultVO<FamilyReportVO> pageResult1 = familyReport(condition.getUid(), condition);
                        // 生成Excel报表并上传oss
                        reportDownloadUrl = exportAndUpload(pageResult1.getList(), FamilyReportVO.class, fileName);
                        break;
                    case 2:
                        subject = "公会维度合计报表";
                        PageResultVO<FamilyReportVO> pageResult2 = familyTotalReport(condition.getUid(), condition);
                        // 生成Excel报表并上传oss
                        reportDownloadUrl = exportAndUpload(pageResult2.getList(), FamilyReportVO.class, fileName);
                        break;
                    case 3:
                        subject = "主播维度报表";
                        PageResultVO<FamilyMemberReportVO> pageResult3 = familyMemberReport(condition.getUid(), condition, true);
                        // 生成Excel报表并上传oss
                        reportDownloadUrl = exportAndUpload(pageResult3.getList(), FamilyMemberReportVO.class, fileName);
                        break;
                    case 4:
                        subject = "主播维度合计报表";
                        PageResultVO<FamilyMemberReportVO> pageResult4 = familyMemberTotalReport(condition.getUid(), condition, true);
                        // 生成Excel报表并上传oss
                        reportDownloadUrl = exportAndUpload(pageResult4.getList(), FamilyMemberReportVO.class, fileName);
                        break;
                    default:
                }
                logger.info("reportDownloadUrl={}", reportDownloadUrl);
                // 发送邮件
                EmailUtils.sendMail(subject, reportDownloadUrl, receiveAccountMap);
                CacheUtils.remove(key);
            });
        }
    }

    public String exportAndUpload(List<?> data, Class<?> head, String fileName) {
        try {
            ByteArrayOutputStream bos = new ByteArrayOutputStream();
            ExcelWriter excelWriter = EasyExcel.write(bos, head).build();
            WriteSheet writeSheet = EasyExcel.writerSheet().build();
            excelWriter.write(data, writeSheet);
            excelWriter.finish();
            return uploadOss(bos, fileName);
        } catch (IOException e) {
            logger.error("exportAndUpload error :{}", e.getMessage(), e);
            throw new CommonException(HttpCode.SERVER_ERROR);
        }
    }

    private String uploadOss(ByteArrayOutputStream bos, String fileName) throws IOException {
        byte[] bytes = bos.toByteArray();
        MultipartFile file = new MockMultipartFile(
                fileName,
                fileName + ".xlsx",
                ContentType.APPLICATION_OCTET_STREAM.toString(),
                new ByteArrayInputStream(bytes)
        );
        return OSSUploadUtils.upload(file, REPORT_FILE_PATH);
    }

    public PageResultVO<EmployeeDataVO> getEmployeeDataReport(EmployeeDataCondition condition) {
        PageResultVO<EmployeeDataVO> resultVO = new PageResultVO<>();
        List<Manager> managers = managerDao.selectList(condition.getAccountName(), condition.getType());
        if (CollectionUtils.isEmpty(managers)) {
            resultVO.setList(Collections.emptyList());
            return resultVO;
        }
        boolean isMonth = condition.getStartTime().length() == "yyyy-MM".length();
        String[] dateArr = getStrDateArray(condition.getStartTime(), condition.getEndTime(), isMonth);
        Collections.reverse(Arrays.asList(dateArr));
        List<EmployeeDataVO> list = new ArrayList<>();
        for (Manager manager : managers) {
            List<String> familyManagerList = new ArrayList<>();
            if (manager.getFamily_manage_role() == 1) {
                familyManagerList.add(manager.getAccount());
            } else if (manager.getFamily_manage_role() == 2) {
                List<Manager> lowerLevelManager = managerDao.getLowerLevelManager(manager.get_id().toString());
                familyManagerList = CollectionUtil.getPropertyList(lowerLevelManager, Manager::getAccount, "-");
            }
            List<FamilyData> familyList = familyDao.selectByManagerList(familyManagerList);
            for (String dateStr : dateArr) {
                Integer[] timeArr = isMonth ? DateHelper.ARABIAN.getStartOrEndSeconds(dateStr + "-01", getMaxDateMonth(dateStr)) : DateHelper.ARABIAN.getTimeWhereRange(dateStr);
                EmployeeDataVO vo = new EmployeeDataVO();
                vo.setStrDate(dateStr);
                vo.setAccountName(manager.getAccount());
                vo.setStrRole(manager.getFamily_manage_role() == 2 ? "超管" : "运营负责人");
                vo.setRemark(manager.getName());
                if (!CollectionUtils.isEmpty(familyList)) {
                    fillTotalFamilyData(vo, timeArr[0], --timeArr[1], familyList);
                }
                list.add(vo);
            }
        }
        resultVO.setList(list);
        return resultVO;
    }

    private void fillTotalFamilyData(EmployeeDataVO vo, int startTime, int endTime, List<FamilyData> familyList) {
        List<Integer> familyIdList = CollectionUtil.getPropertyList(familyList, FamilyData::getId, null);
        vo.setFamilyNum(familyList.size());
        vo.setNewFamilyNum((int) familyList.stream().filter(k -> k.getCtime() >= startTime && k.getCtime() < endTime).count());
        FamilyDailyDetailTotalData totalData = familyDailyDetailDao.getTotalData(familyIdList, startTime, endTime);
        vo.setActiveFamilyNum(familyDailyDetailDao.getActiveFamilyNumTotal(familyIdList, startTime, endTime));
        vo.setNewAnchorNum(totalData.getNewAnchorNumTotal());
        vo.setWorkingAnchorNum(totalData.getNewAnchorNumTotal());
        vo.setPayingAnchorNum(totalData.getPayingAnchorNumTotal());
        vo.setCharmIncome(totalData.getRoomCharmIncomeTotal());
        vo.setGameCostDiamonds(totalData.getGameCostDiamondsTotal());
    }

    public PerformanceDashboardVO performanceDashboard(String uid) {
        List<FamilyData> familyList = familyReportsService.getSubFamilyList(uid);
        PerformanceDashboardVO vo = new PerformanceDashboardVO();
        int newFamilyNum = 0;
        long totalCharmIncome = 0;
        long newFamilyTotalIncome = 0;
        long newAnchorTotalIncome = 0;
        Date monthFirstDay = com.quhong.core.utils.DateHelper.ARABIAN.getFirstMonthDay(new Date());
        int monthStartTime = (int) (monthFirstDay.getTime() / 1000);
        int nowTime = (int) (System.currentTimeMillis() / 1000);
        if (!CollectionUtils.isEmpty(familyList)) {
            // 本月新公会数
            newFamilyNum = (int) familyList.stream().filter(k -> k.getCtime() >= monthStartTime).count();
            // 本月公会共计收入美金数
            Set<Integer> familyIds = familyList.stream().map(FamilyData::getId).collect(Collectors.toSet());
            int startDate = Integer.parseInt(com.quhong.core.utils.DateHelper.ARABIAN.formatDateInDay2(monthFirstDay));
            int endDate = Integer.parseInt(com.quhong.core.utils.DateHelper.ARABIAN.formatDateInDay2());
            totalCharmIncome = familyDailyIncomeDao.getFamilySetIncome(familyIds, startDate, endDate);
            // 60天内新公会收入美金数
            LocalDate dayOffset = DateSupport.ARABIAN.getDayOffset(-59);
            int startTime = (int) (dayOffset.atStartOfDay(GMT3).toInstant().toEpochMilli() / 1000);
            startDate = Integer.parseInt(DateSupport.yyyyMMdd(dayOffset));
            Set<Integer> newFamilyIds = familyList.stream().filter(k -> k.getCtime() >= startTime).map(FamilyData::getId).collect(Collectors.toSet());
            if (!CollectionUtils.isEmpty(newFamilyIds)) {
                newFamilyTotalIncome = familyDailyIncomeDao.getFamilyIncome(newFamilyIds, startDate, endDate);
            }
            // 60天内注册主播收入美金数
            Map<String, Integer> allMember = familyMemberDao.getAllMemberFromCache();
            Set<String> newAnchorSet = allMember.entrySet().stream()
                    .filter(entry -> familyIds.contains(entry.getValue()) && com.quhong.utils.ActorUtils.isNewRegisterActor(entry.getKey(), 60))
                    .map(Map.Entry::getKey)
                    .collect(Collectors.toSet());
            if (!CollectionUtils.isEmpty(newAnchorSet)) {
                newAnchorTotalIncome = charmStatDao.getUsersTotalCharm(newAnchorSet, null, null, startTime, nowTime);
            }
        }
        vo.setNewFamilyNum(newFamilyNum);
        vo.setTotalUsdIncome(charmToUsd(totalCharmIncome));
        vo.setNewFamilyTotalUsdIncome(charmToUsd(newFamilyTotalIncome));
        vo.setNewAnchorTotalUsdIncome(charmToUsd(newAnchorTotalIncome));
        return vo;
    }

    public ListVO<StatisticsVO> getIncomeDetail(String uid) {
        List<StatisticsVO> list = new ArrayList<>();
        List<FamilyData> familyList = familyReportsService.getSubFamilyList(uid);
        Set<Integer> familyIds = !CollectionUtils.isEmpty(familyList) ? familyList.stream().map(FamilyData::getId).collect(Collectors.toSet()) : Collections.emptySet();
        LocalDate startDate = DateSupport.ARABIAN.getDayOffset(-60);
        LocalDate today = DateSupport.ARABIAN.getToday();
        while (startDate.isBefore(today)) {
            int intDate = DateSupport.ARABIAN.getIntDate(startDate);
            long familyIncome = !CollectionUtils.isEmpty(familyList) ? familyDailyIncomeDao.getFamilySetIncome(familyIds, intDate, intDate) : 0;
            list.add(new StatisticsVO(DateSupport.format(startDate), charmToUsd(familyIncome)));
            startDate = startDate.plusDays(1);
        }
        return new ListVO<>(list);
    }

    @Cacheable(value = "cache", cacheManager = CaffeineCacheConfig.EXPIRE_10M_AFTER_WRITE,
            key = "#root.method.name+T(org.springframework.util.StringUtils).arrayToDelimitedString(args,'-')")
    public List<FamilyData> getSubFamilyList(String uid) {
        Manager manager = managerDao.getDataByUid(uid);
        if (manager == null) {
            throw new CommonH5Exception(HttpCode.PARAM_ERROR);
        }
        List<String> familyManagerList = new ArrayList<>();
        if (manager.getFamily_manage_role() == 1) {
            familyManagerList.add(manager.getAccount());
        } else if (manager.getFamily_manage_role() == 2) {
            List<Manager> lowerLevelManager = managerDao.getLowerLevelManager(manager.get_id().toString());
            familyManagerList = CollectionUtil.getPropertyList(lowerLevelManager, Manager::getAccount, "-");
        }
        return familyDao.selectByManagerList(familyManagerList);
    }

    private BigDecimal charmToUsd(long charm) {
        return new BigDecimal(charm).divide(new BigDecimal(14000), 2, RoundingMode.FLOOR);
    }

    public void commonReportAsyncDownload() {
        AsyncUtils.execute(() -> {
            List<CommonExportVO> list = new ArrayList<>();
            List<FamilyData> familyList = familyDao.selectAll();
            for (FamilyData familyData : familyList) {
                if (familyData.getStatus() != 1) {
                    continue;
                }
                ActorData actorData = actorDao.getActorDataFromCache(familyData.getOwnerUid());
                if (actorData == null) {
                    continue;
                }
                CommonExportVO vo = new CommonExportVO();
                vo.setRid(actorData.getRid() + "");
                vo.setStrRid(actorData.getShowRid());
                vo.setParam1(familyData.getRid() + "");
                vo.setParam2(charmLogEsDao.getTotalCharm(null, familyData.getId(), Arrays.asList(1, 2), **********, 1733048705) + "");
                vo.setParam3(charmLogEsDao.getTotalCharm(actorData.getUid(), null, Collections.singletonList(6), **********, 1733048705) + "");
                vo.setParam4(charmLogEsDao.getTotalCharm(actorData.getUid(), null, Collections.singletonList(7), **********, 1733048705) + "");

                vo.setParam5(charmLogEsDao.getTotalCharm(null, familyData.getId(), Arrays.asList(1, 2), ********** - (int) TimeUnit.DAYS.toSeconds(7), **********) + "");
                vo.setParam6(charmLogEsDao.getTotalCharm(actorData.getUid(), null, Collections.singletonList(6), ********** - (int) TimeUnit.DAYS.toSeconds(7), **********) + "");
                vo.setParam7(charmLogEsDao.getTotalCharm(actorData.getUid(), null, Collections.singletonList(7), ********** - (int) TimeUnit.DAYS.toSeconds(7), **********) + "");
                list.add(vo);
            }
            // 发送邮件
            Map<String, String> receiveAccountMap = new HashMap<>(1);
            receiveAccountMap.put("<EMAIL>", "lyq");
            String reportDownloadUrl = exportAndUpload(list, CommonExportVO.class, "data");
            logger.info("reportDownloadUrl={}", reportDownloadUrl);
            EmailUtils.sendMail("公会佣金数据", reportDownloadUrl, receiveAccountMap);
        });
    }
}
