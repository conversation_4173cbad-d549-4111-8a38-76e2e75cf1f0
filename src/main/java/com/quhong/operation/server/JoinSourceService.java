package com.quhong.operation.server;

import com.google.common.collect.Interner;
import com.google.common.collect.Interners;
import com.quhong.core.concurrency.BaseTaskFactory;
import com.quhong.core.concurrency.tasks.Task;
import com.quhong.core.utils.DateHelper;
import com.quhong.enums.HttpCode;
import com.quhong.exception.CommonH5Exception;
import com.quhong.mongo.dao.JoinSourceDao;
import com.quhong.mongo.data.JoinSourceData;
import com.quhong.operation.share.condition.ItemCondition;
import com.quhong.operation.share.condition.ResourceCondition;
import com.quhong.operation.share.dto.JoinSourceDTO;
import com.quhong.operation.share.vo.JoinSourceVO;
import com.quhong.operation.share.vo.PageResultVO;
import com.quhong.operation.share.vo.ResourceVO;
import com.quhong.operation.utils.Md5Utils;
import com.quhong.operation.utils.StringUtil;
import com.quhong.operation.utils.ZipUtil;
import com.quhong.redis.GoodsListHomeRedis;
import com.quhong.utils.WalletUtils;
import jakarta.annotation.Resource;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.BeanUtils;
import org.springframework.data.mongodb.core.query.Update;
import org.springframework.stereotype.Component;
import org.springframework.util.StringUtils;

import java.util.*;

@Component
public class JoinSourceService implements ResourceService {
    private static final Logger logger = LoggerFactory.getLogger(JoinSourceService.class);
    public static final int TYPE_RIDE = 3; // 入场动画
    private final static String filePath = "join/";
    private final static String ZIP_PREFIX = "join_";
    private static final Interner<String> stringPool = Interners.newWeakInterner();

    @Resource
    private JoinSourceDao joinSourceDao;
    @Resource
    private GoodsListHomeRedis goodsListHomeRedis;


    public PageResultVO<JoinSourceVO> joinSourceList(ItemCondition condition) {
        PageResultVO<JoinSourceVO> pageVO = new PageResultVO<>();
        Integer status = condition.getStatus();
        Integer itemType = condition.getItemType();
        String search = condition.getSearch();
        int page = condition.getPage() == null ? 1 : condition.getPage();
        int pageSize = condition.getPageSize() == null ? 30 : condition.getPageSize();
        int start = (page - 1) * pageSize;

        List<JoinSourceData> joinSourceList = joinSourceDao.selectJoinSourcePage(itemType, status, search, start, pageSize,
                condition.getHot(), condition.getFusion());
        List<JoinSourceVO> voList = new ArrayList<>();
        for (JoinSourceData data : joinSourceList) {
            JoinSourceVO vo = new JoinSourceVO();
            BeanUtils.copyProperties(data, vo);
            vo.setDocId(data.get_id().toString());
            vo.setBeans(WalletUtils.diamondsForDisplay(vo.getBeans()));
            voList.add(vo);
        }

        pageVO.setList(voList);
        pageVO.setTotal(joinSourceDao.selectCount(itemType, status, search));
        return pageVO;
    }


    private List<Map<String, String>> convertAddZip(JoinSourceDTO dto) {
        List<Map<String, String>> urlList = new ArrayList<>();
        Map<String, String> actMap = new HashMap<>();
        actMap.put("fileName", "act");
        actMap.put("fileUrl", dto.getAction_url());
        urlList.add(actMap);

        if (!StringUtils.isEmpty(dto.getVideo_url())) {
            Map<String, String> videoMap = new HashMap<>();
            videoMap.put("fileName", "voice");
            videoMap.put("fileUrl", dto.getVideo_url());
            urlList.add(videoMap);
        }
        if (!StringUtils.isEmpty(dto.getFusionUrl())) {
            Map<String, String> fusionMap = new HashMap<>();
            fusionMap.put("fileName", "fusion");
            fusionMap.put("fileUrl", dto.getFusionUrl());
            urlList.add(fusionMap);
        }
        return urlList;
    }


    private List<Map<String, String>> convertUpdateZip(JoinSourceDTO dto, JoinSourceData joinSource) {
        List<Map<String, String>> oldUrlList = new ArrayList<>();
        List<Map<String, String>> urlList = new ArrayList<>();

        // 设置旧的zip
        Map<String, String> actMap = new HashMap<>();
        actMap.put("fileName", "act");
        actMap.put("fileUrl", joinSource.getAction_url());
        oldUrlList.add(actMap);

        if (!StringUtils.isEmpty(joinSource.getVideo_url())) {
            Map<String, String> videoMap = new HashMap<>();
            videoMap.put("fileName", "voice");
            videoMap.put("fileUrl", joinSource.getVideo_url());
            oldUrlList.add(videoMap);
        }


        // 新的zip
        Map<String, String> actionMap = new HashMap<>();
        actionMap.put("fileName", "act");
        actionMap.put("fileUrl", dto.getAction_url());
        urlList.add(actionMap);

        if (!StringUtils.isEmpty(dto.getVideo_url())) {
            Map<String, String> videoNewMap = new HashMap<>();
            videoNewMap.put("fileName", "voice");
            videoNewMap.put("fileUrl", dto.getVideo_url());
            urlList.add(videoNewMap);
        }

        if (!StringUtils.isEmpty(dto.getFusionUrl())) {
            Map<String, String> fusionMap = new HashMap<>();
            fusionMap.put("fileName", "fusion");
            fusionMap.put("fileUrl", dto.getFusionUrl());
            urlList.add(fusionMap);
        }

        String oldResourceMd5 = Md5Utils.getStringMD5String(oldUrlList.toString());
        String newResourceMd5 = Md5Utils.getStringMD5String(urlList.toString());

        logger.info("convertUpdateZip oldUrlList: {}, urlList: {}, oldResourceMd5: {},  newResourceMd5:{}", oldUrlList, urlList, oldResourceMd5, newResourceMd5);
        if(oldResourceMd5.equals(newResourceMd5)){
            return Collections.emptyList();
        }

        return urlList;
    }

    private void asyncUpdateSource(int joinCartonId, List<Map<String, String>> urlList) {

        JoinSourceData joinSource = joinSourceDao.findSourceData(joinCartonId);
        if (joinSource == null) {
            return;
        }

        Update update = new Update();
        if (!urlList.isEmpty()) {
            String zipPath = ZipUtil.loadUrlZipUploadFile(ZIP_PREFIX, urlList);
            Map<String, Object> zipFileMeta = ZipUtil.calculateZipFileMD5(zipPath);
            String zipFileUrl = ZipUtil.uploadZipFile(zipPath, filePath);
            update.set("source_url", zipFileUrl);
            update.set("source_md5", zipFileMeta.get("fileMd5"));
            update.set("ios_url", zipFileUrl);
            update.set("ios_md5", zipFileMeta.get("fileMd5"));
            joinSourceDao.updateData(joinSource.get_id().toString(), update);
        }
    }


    public void addJoinSourceData(String uid, JoinSourceDTO dto) {

        if (StringUtils.isEmpty(dto.getJoin_icon()) || StringUtils.isEmpty(dto.getAction_url())) {
            throw new CommonH5Exception(HttpCode.PARAM_ERROR.getCode(), "图标或动画资源未上传");
        }

        String internKey = uid + dto.getName();
        synchronized (stringPool.intern(internKey)) {
            JoinSourceData data = new JoinSourceData();
            BeanUtils.copyProperties(dto, data);
            JoinSourceData lastData = joinSourceDao.getLastJoinSourceData();
            int nextId = lastData != null ? lastData.getJoin_carton_id() + 1 : 1;
            data.setSmall_icon(dto.getJoin_icon());
            data.setJoin_carton_id(nextId);
            data.setC_time(DateHelper.getNowSeconds());

            data.setSource_md5("");
            data.setSource_url("");
            data.setIos_md5("");
            data.setIos_url("");

            joinSourceDao.insert(data);
            List<Map<String, String>> urlList = convertAddZip(dto);
            BaseTaskFactory.getFactory().addSlow(new Task() {
                @Override
                protected void execute() {
                    asyncUpdateSource(data.getJoin_carton_id(), urlList);
                }
            });

            if (data.getItem_type() == 5 && data.getIs_new() == 1 && data.getStatus() == 1) {
                goodsListHomeRedis.addNewGoodsRankingScore(TYPE_RIDE, nextId);
            }
        }


    }

    public void updateJoinSourceData(JoinSourceDTO dto) {

        JoinSourceData data = joinSourceDao.getJoinSourceDataByID(dto.getDocId());
        if(data == null){
            throw new CommonH5Exception(HttpCode.PARAM_ERROR);
        }

        if (data.getItem_type() == 5 && data.getItem_type() != dto.getItem_type()) {
            throw new CommonH5Exception(HttpCode.PARAM_ERROR.getCode(), "已上传的资源不能修改类型");
        }

        if (StringUtils.isEmpty(dto.getJoin_icon()) || StringUtils.isEmpty(dto.getAction_url())) {
            throw new CommonH5Exception(HttpCode.PARAM_ERROR.getCode(), "图标或动画资源未上传");
        }

        Update update = new Update();
        update.set("name", dto.getName() != null ? dto.getName() : "");
        update.set("name_ar", dto.getName_ar() != null ? dto.getName_ar() : "");
        update.set("item_type", dto.getItem_type());
        update.set("status", dto.getStatus());
        update.set("buy_type", dto.getBuy_type());
        update.set("beans", dto.getBeans());
        update.set("days", dto.getDays());
        update.set("forder", dto.getForder());
        update.set("is_new", dto.getIs_new());
        update.set("show_time", dto.getShow_time());
        update.set("source_type", dto.getSource_type());
        update.set("fusion", dto.getFusion());
        update.set("fusionUrl", dto.getFusionUrl());

        update.set("resLevel", dto.getResLevel());

        if (!StringUtil.isEmpty(dto.getJoin_icon())) {
            update.set("join_icon", dto.getJoin_icon());
            update.set("small_icon", dto.getJoin_icon());
        }

        update.set("action_url", dto.getAction_url());
        update.set("video_url", dto.getVideo_url());

        joinSourceDao.updateData(dto.getDocId(), update);

        List<Map<String, String>> urlList = convertUpdateZip(dto, data);
        BaseTaskFactory.getFactory().addSlow(new Task() {
            @Override
            protected void execute() {
                asyncUpdateSource(data.getJoin_carton_id(), urlList);
            }
        });

        if (data.getItem_type() == 5) {
            if (dto.getIs_new() == 1 && dto.getStatus() == 1) {
                goodsListHomeRedis.addNewGoodsRankingScore(TYPE_RIDE, data.getJoin_carton_id());
            }

            if (dto.getIs_new() == 0 || dto.getStatus() == 0) {
                goodsListHomeRedis.deleteItemNewGoodsRanking(TYPE_RIDE, data.getJoin_carton_id());
            }

            if (dto.getStatus() == 0) {
                goodsListHomeRedis.deleteItemNewGoodsRanking(TYPE_RIDE, data.getJoin_carton_id());
                goodsListHomeRedis.deleteItemHotGoodsRanking(TYPE_RIDE, data.getJoin_carton_id());
            }
        }
    }

    @Override
    public PageResultVO<ResourceVO> getGoodsList(ResourceCondition condition) {
        PageResultVO<ResourceVO> pageVO = new PageResultVO<>();
        String search = condition.getSearch();
        int page = condition.getPage();
        int pageSize = condition.getPageSize(30);
        int start = (page - 1) * pageSize;
        List<JoinSourceData>  joinSourceList = joinSourceDao.selectJoinSourcePage(-1, condition.getStatus(), search, start, pageSize, -1, -1);
        List<ResourceVO> voList = new ArrayList<>();
        for(JoinSourceData data: joinSourceList){
            if(data.getItem_type() == 5){
                continue;
            }
            ResourceVO vo = new ResourceVO();
            vo.setResourceId(data.getJoin_carton_id());
            vo.setResourceIcon(data.getJoin_icon());
            vo.setResourceNameEn(data.getName());
            vo.setResourceNameAr(data.getName_ar());
            vo.setResourcePrice(data.getBeans());
            vo.setResourceType(condition.getResourceType());
            voList.add(vo);
        }
        pageVO.setList(voList);
        pageVO.setTotal(joinSourceDao.selectCount(-1, condition.getStatus(), search));
        return pageVO;
    }
}
