package com.quhong.operation.server;

import com.quhong.api.ApiUserInfoService;
import com.quhong.core.utils.DateHelper;
import com.quhong.data.ActorData;
import com.quhong.enums.HttpCode;
import com.quhong.exception.CommonH5Exception;
import com.quhong.mongo.dao.ActorDao;
import com.quhong.mongo.dao.FaceAuthRecordDao;
import com.quhong.mongo.data.FaceAuthRecordData;
import com.quhong.operation.share.condition.FaceAuthCondition;
import com.quhong.operation.share.vo.FaceAuthRecordVO;
import com.quhong.operation.share.vo.PageResultVO;
import com.quhong.redis.WhitelistRedis;
import jakarta.annotation.Resource;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2024/4/17
 */
@Service
public class FaceAuthService {

    private static final int PAGE_SIZE = 10;

    @Resource
    private ActorDao actorDao;
    @Resource
    private FaceAuthRecordDao faceAuthRecordDao;
    @Resource
    private ApiUserInfoService apiUserInfoService;
    @Resource
    private WhitelistRedis whitelistRedis;

    public PageResultVO<FaceAuthRecordVO> selectPage(FaceAuthCondition condition) {
        PageResultVO<FaceAuthRecordVO> resultVO = new PageResultVO<>();
        String remark = getRemarkByFailureType(condition.getFailureType());
        int count = faceAuthRecordDao.selectCount(condition.getRid(), condition.getFaceId(), condition.getUserType(), condition.getStatus(), remark);
        resultVO.setList(new ArrayList<>());
        resultVO.setTotal(count);
        if (count > 0) {
            int page = condition.getPage() != null && condition.getPage() > 0 ? condition.getPage() : 1;
            int pageSize = condition.getPageSize() != null && condition.getPageSize() > 0 ? condition.getPageSize() : PAGE_SIZE;
            int start = (page - 1) * pageSize;
            List<FaceAuthRecordData> recordList = faceAuthRecordDao.selectPage(condition.getRid(), condition.getFaceId(), condition.getUserType(), condition.getStatus(), remark, start, pageSize);
            if (!CollectionUtils.isEmpty(recordList)) {
                for (FaceAuthRecordData recordData : recordList) {
                    ActorData actorData = actorDao.getActorDataFromCache(recordData.getUid());
                    FaceAuthRecordVO vo = new FaceAuthRecordVO();
                    BeanUtils.copyProperties(recordData, vo);
                    vo.setId(recordData.get_id().toString());
                    vo.setRid(actorData.getRid());
                    vo.setStrRid(actorData.getShowRid());
                    vo.setImg(recordData.getImgList().get(0));
                    vo.setRemark(getRemark(recordData.getRemark(), recordData.getFaceId()));
                    vo.setInWhitelist(whitelistRedis.inWhitelist(WhitelistRedis.FACE_AUTH, recordData.getUid()) ? 1 : 0);
                    resultVO.getList().add(vo);
                }
            }
        }
        return resultVO;
    }

    private String getRemark(String remark, String faceId) {
        switch (remark) {
            case "server error" -> {
                return "服务器错误";
            }
            case "No face detected" -> {
                return "未检测到人脸";
            }
            case "No right to operate" -> {
                return "调用第三方接口失败";
            }
            case "Minors are not allowed to use." -> {
                return "未成年进行认证";
            }
            case "This person has already authenticated other accounts and cannot be authenticated again." -> {
                FaceAuthRecordData recordData = faceAuthRecordDao.selectByFaceId(faceId);
                String strRid = "";
                if (recordData != null) {
                    ActorData actorData = actorDao.getActorDataFromCache(recordData.getUid());
                    strRid = actorData != null ? actorData.getRid() + "" : "";
                }
                strRid = StringUtils.hasLength(strRid) ? strRid : "其他";
                return "已在" + strRid + "账号验证过";
            }
            default -> {
                return "";
            }
        }
    }

    private String getRemarkByFailureType(Integer failureType) {
        if (failureType == null || failureType == 0) {
            return "";
        }
        switch (failureType) {
            case 1 -> {
                return "server error";
            }
            case 2 -> {
                return "No face detected";
            }
            case 3 -> {
                return "No right to operate";
            }
            case 4 -> {
                return "This person has already authenticated other accounts and cannot be authenticated again.";
            }
            case 5 -> {
                return "Minors are not allowed to use.";
            }
            default -> {
                return "";
            }
        }
    }

    public Object delete(String id) {
        FaceAuthRecordData recordData = faceAuthRecordDao.findById(id);
        if (recordData == null || recordData.getStatus() != 1) {
            throw new CommonH5Exception(HttpCode.PARAM_ERROR);
        }
        if (apiUserInfoService.deleteFaceAuth(recordData.getUid())) {
            recordData.setStatus(2);
            recordData.setMtime(DateHelper.getNowSeconds());
            faceAuthRecordDao.save(recordData);
        }
        return null;
    }
}
