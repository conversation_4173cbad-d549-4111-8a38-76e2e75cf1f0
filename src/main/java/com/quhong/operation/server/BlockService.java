package com.quhong.operation.server;

import com.quhong.core.config.ServerConfig;
import com.quhong.core.utils.DateHelper;
import com.quhong.data.ActorData;
import com.quhong.enums.HttpCode;
import com.quhong.enums.RoomConstant;
import com.quhong.enums.UserLevelConstant;
import com.quhong.enums.UserMonitorState;
import com.quhong.exception.CommonH5Exception;
import com.quhong.mongo.dao.*;
import com.quhong.mongo.data.BLockRoomLogData;
import com.quhong.mongo.data.LiveScreenshotRecordData;
import com.quhong.mongo.data.UserMonitorData;
import com.quhong.msg.room.LiveRoomForceCloseMsg;
import com.quhong.msg.room.RoomChangeMsg;
import com.quhong.mysql.dao.FamilyMemberDao;
import com.quhong.mysql.dao.RechargeDailyInfoDao;
import com.quhong.mysql.dao.VipInfoDao;
import com.quhong.mysql.data.FamilyMemberData;
import com.quhong.operation.dao.AdminUserDao;
import com.quhong.operation.dao.ManagerDao;
import com.quhong.operation.share.condition.BlockCondition;
import com.quhong.operation.share.condition.BlockDTO;
import com.quhong.operation.share.mongobean.AdminUser;
import com.quhong.operation.share.mongobean.Manager;
import com.quhong.operation.share.mongobean.UserMonitorLog;
import com.quhong.operation.share.vo.BLockRoomLogVO;
import com.quhong.operation.share.vo.BlockRecordVO;
import com.quhong.operation.share.vo.PageResultVO;
import com.quhong.redis.LiveRoomRedis;
import com.quhong.room.RoomStreamService;
import com.quhong.room.RoomWebSender;
import com.quhong.service.RoomMonitorService;
import com.quhong.utils.AppVersionUtils;
import com.quhong.utils.RoomUtils;
import jakarta.annotation.Resource;
import org.bson.types.ObjectId;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.*;
import java.util.concurrent.TimeUnit;


@Service
public class BlockService {

    private final static Logger logger = LoggerFactory.getLogger(BlockService.class);
    public static final Set<String> BLOCK_POWER_SET = new HashSet<>(Arrays.asList("admin", "Guoxiaojuan", "xiaojuanguoguo", "fangfang", "mabo1234", "Operationhasan", "fahdop", "yincui","laozhao", "fawa", "Qiying", "nawa", "Liushaodong", "amali", "nawal", "hasan", "Jihad"));

    @Resource
    private RoomMonitorService roomMonitorService;
    @Resource
    private ActorDao actorDao;
    @Resource
    private RoomStreamService roomStreamService;
    @Resource
    private CommonDao commonDao;
    @Resource
    private BlockRoomLogDao blockRoomLogDao;
    @Resource
    private LiveRoomRedis liveRoomRedis;
    @Resource
    private ManagerDao managerDao;
    @Resource
    private RoomWebSender roomWebSender;
    @Resource
    private LiveScreenshotRecordDao liveScreenshotRecordDao;
    @Resource
    private UserMonitorLogDao userMonitorLogDao;
    @Resource
    private AdminUserDao adminUserDao;
    @Resource
    private VipInfoDao vipInfoDao;
    @Resource
    private FamilyMemberDao familyMemberDao;
    @Resource
    private UserLevelDao userLevelDao;
    @Resource
    private RechargeDailyInfoDao rechargeDailyInfoDao;
    @Resource
    private UserMonitorDao userMonitorDao;

    private Manager permissionsCheck(String opUid) {
        Manager manager = managerDao.getDataByUidFromCache(opUid);
        if (ServerConfig.isNotProduct()) {
            return manager;
        }
        if (manager == null || !BLOCK_POWER_SET.contains(manager.getAccount())) {
            throw new CommonH5Exception(HttpCode.AUTH_ERROR);
        }
        return manager;
    }

    public ActorData getActorData(String reqRid) {
        if (!StringUtils.hasLength(reqRid)) {
            logger.error("rid is empty.");
            throw new CommonH5Exception(HttpCode.PARAM_ERROR);
        }
        ActorData actorData = actorDao.getActorByRidOrAlphaRidFromDb(reqRid);
        if (actorData == null) {
            logger.info("can not find actor data. rid={}", reqRid);
            throw new CommonH5Exception(1, "can not find actor data.");
        }
        return actorData;
    }

    private void saveUserMonitorLog(String opUid, String aid, String name, String blockTerm, String reason) {
        UserMonitorLog log = new UserMonitorLog();
        log.setOptUser(opUid);
        log.setOperator(opUid);
        log.setOptTime(DateHelper.getNowSeconds());
        log.setOperation("");
        log.setUid(aid);
        log.setName(name);
        log.setCode(UserMonitorState.BAN_LIVE_ROOM);
        log.setBlock_term(blockTerm);
        log.setReason(reason);
        commonDao.save(log);
    }

    public PageResultVO<BLockRoomLogVO> blockLiveList(BlockCondition dto) {
        int pageSize = dto.getPageSize() != 0 ? dto.getPageSize() : 50;
        int start = Math.max(dto.getPage() - 1, 0) * pageSize;
        String aid = null;
        if (StringUtils.hasLength(dto.getRid())) {
            ActorData userData = actorDao.getActorByRidOrAlphaRid(dto.getRid());
            aid = userData != null ? userData.getUid() : null;
        }
        List<BLockRoomLogVO> list = new ArrayList<>();
        List<BLockRoomLogData> dataList = blockRoomLogDao.selectList(aid, dto.getOpName(), dto.getHost(), dto.getBlockTerm(), start, pageSize);
        if (!CollectionUtils.isEmpty(dataList)) {
            for (BLockRoomLogData data : dataList) {
                BLockRoomLogVO vo = new BLockRoomLogVO();
                ActorData actorData = StringUtils.hasLength(data.getUid()) ? actorDao.getActorDataFromCache(data.getUid()) : null;
                vo.setRid(actorData != null ? actorData.getRid() + "" : "");
                vo.setStrRid(actorData != null ? actorData.getShowRid() : "");
                vo.setName(actorData != null ? actorData.getName() : "");
                vo.setWarnType(data.getWarnType());
                vo.setBlockTerm(data.getBlockTerm());
                vo.setReason(data.getReason());
                vo.setScreenshotList(data.getScreenshotList());
                vo.setOpName(data.getOpName());
                vo.setHost(data.getFamilyId() > 0 ? 1 : 0);
                vo.setCtime(data.getCtime());
                vo.setBlockTime(DateHelper.ARABIAN.formatDateTime(new Date(data.getCtime() * 1000L)));
                vo.setRegisterTime(actorData != null ? DateHelper.ARABIAN.formatDateTime(new Date(new ObjectId(actorData.getUid()).getTimestamp() * 1000L)) : "");
                if (dto.getPageSize() == 10000) {
                    List<LiveScreenshotRecordData> recordList = liveScreenshotRecordDao.getDataList(data.getUid(), (int) (data.getCtime() - TimeUnit.DAYS.toSeconds(1)), (int) data.getCtime());
                    vo.setLiveTime(CollectionUtils.isEmpty(recordList)? "" : recordList.get(0).getLiveTime() / 60 + "分钟");
                }
                list.add(vo);
            }
        }
        return new PageResultVO<>(blockRoomLogDao.selectCount(aid, dto.getOpName(), dto.getHost(), dto.getBlockTerm()), list);
    }

    public Object blockLiveRoom(String opUid, BlockDTO dto) {
        Manager manager = permissionsCheck(opUid);
        ActorData actorData = getActorData(dto.getRid());
        roomMonitorService.blockLiveRoom(actorData, dto.getBlockTerm(), dto.getReason(), 5);
        saveUserMonitorLog(actorData.getUid(), actorData.getUid(), actorData.getName(), dto.getBlockTerm(), dto.getReason());
        // 记录封禁日志
        String roomId = RoomUtils.formatLiveRoomId(actorData.getUid());
        BLockRoomLogData data = new BLockRoomLogData();
        data.setRoomId(roomId);
        data.setUid(actorData.getUid());
        data.setReason(dto.getReason());
        data.setBlockTerm(dto.getBlockTerm());
        data.setFamilyId(actorData.getFamilyId());
        data.setScreenshotList(liveRoomRedis.getLiveRoomScreenshot(roomStreamService.genLiveStreamId(roomId)));
        data.setOpUid(opUid);
        data.setOpName(manager.getAccount());
        data.setCtime(DateHelper.getNowSeconds());
        blockRoomLogDao.save(data);
        return null;
    }

    public Object unblockLiveRoom(String opUid, BlockDTO dto) {
        permissionsCheck(opUid);
        roomMonitorService.unblockLiveRoom(getActorData(dto.getRid()));
        return null;
    }

    public Object warnLiveRoom(String opUid, BlockDTO dto) {
        Manager manager = permissionsCheck(opUid);
        ActorData actorData = getActorData(dto.getRid());
        LiveRoomForceCloseMsg msg = new LiveRoomForceCloseMsg();
        msg.setReason(dto.getReason());
        msg.setType(AppVersionUtils.versionCheck(132, actorData.getVersion_code(), actorData.getIntOs()) ? 2 : 1);
        String liveRoomId = RoomUtils.formatLiveRoomId(actorData.getUid());
        roomWebSender.sendPlayerWebMsg(liveRoomId, "", actorData.getUid(), msg, true);
        // 直播房，房主退出时关闭直播
        roomWebSender.sendRoomWebMsg(liveRoomId, actorData.getUid(), new RoomChangeMsg(liveRoomId, RoomConstant.LIVE_ROOM_MODE, 2), false);
        // 记录警告日志
        BLockRoomLogData data = new BLockRoomLogData();
        data.setRoomId(liveRoomId);
        data.setUid(actorData.getUid());
        data.setWarnType(dto.getWarnType());
        data.setBlockTerm("0");
        data.setReason(dto.getReason());
        data.setBlockTerm(dto.getBlockTerm());
        data.setFamilyId(actorData.getFamilyId());
        data.setScreenshotList(liveRoomRedis.getLiveRoomScreenshot(roomStreamService.genLiveStreamId(liveRoomId)));
        data.setOpUid(opUid);
        data.setOpName(manager.getAccount());
        data.setCtime(DateHelper.getNowSeconds());
        blockRoomLogDao.save(data);
        return null;
    }

    public PageResultVO<BlockRecordVO> blockRecordList(BlockCondition dto) {
        String aid = null;
        if (StringUtils.hasLength(dto.getRid())) {
            ActorData userData = actorDao.getActorByRidOrAlphaRid(dto.getRid());
            aid = userData != null ? userData.getUid() : "---";
        }
        int startTime = 0;
        int endTime = 0;
        if (StringUtils.hasLength(dto.getStartDate()) && StringUtils.hasLength(dto.getEndDate())) {
            Integer[] timeArr = DateHelper.ARABIAN.getStartAndEndTime(dto.getStartDate(), dto.getEndDate());
            startTime = timeArr[0];
            endTime = timeArr[1];
        }
        String optUid = "";
        if (StringUtils.hasLength(dto.getOpName())) {
            optUid = getOptUid(dto.getOpName());
        }
        int count = userMonitorLogDao.selectCount(aid, startTime, endTime, optUid, dto.getOptType());
        if (count == 0) {
            return new PageResultVO<>(0, Collections.emptyList());
        }
        int pageSize = dto.getPageSize() != 0 ? dto.getPageSize() : 30;
        int start = Math.max(dto.getPage() - 1, 0) * pageSize;
        List<UserMonitorLog> dataList = userMonitorLogDao.selectList(aid, startTime, endTime, optUid, dto.getOptType(), start, pageSize);
        List<BlockRecordVO> list = new ArrayList<>();
        if (!CollectionUtils.isEmpty(dataList)) {
            for (UserMonitorLog data : dataList) {
                BlockRecordVO vo = new BlockRecordVO();
                ActorData actorData = actorDao.getActorDataFromCache(data.getUid());
                if (actorData == null) {
                    continue;
                }
                vo.setRid(actorData.getRid() + "");
                vo.setStrRid(actorData.getRidData().getLevel() > 0 ? actorData.getRidData().getRid() : "-");
                vo.setUserName(actorData.getName());
                vo.setGender(actorData.getFb_gender() == 1 ? "男" : "女");
                vo.setAccountType(getUserType(actorData.getUid()));
                vo.setCountry(actorData.getCountry());
                vo.setWealthLevel(userLevelDao.getUserLevel(actorData.getUid(), UserLevelConstant.WEALTH_LEVEL));
                vo.setSvipLevel(actorData.getSvipLevel());
                vo.setVipLevel(vipInfoDao.getIntVipLevelFromCache(actorData.getUid()));
                vo.setRegisterTime(DateHelper.ARABIAN.formatDateTime(new Date(new ObjectId(actorData.getUid()).getTimestamp() * 1000L)));
                long totalRechargeBeans = rechargeDailyInfoDao.getRechargeDiamondsAmount(actorData.getUid());
                vo.setIsPaidUser(totalRechargeBeans > 0 ? "是" : "否");
                vo.setTotalRechargeAmount(totalRechargeBeans != 0 ? BigDecimal.valueOf(totalRechargeBeans / 10000f).setScale(2, RoundingMode.HALF_UP) + "美金" : "0");
                vo.setOptType(getStrOptType(data.getCode()));
                vo.setFunctionName(data.getCode() == 5 ? "直播" : "其他");
                int loginTime = actorData.getLastLogin() != null && actorData.getLastLogin().getLoginTime() != null ? actorData.getLastLogin().getLoginTime().intValue() : 0;
                vo.setLastActiveTime(DateHelper.ARABIAN.formatDateTime(new Date(loginTime * 1000L)));
                vo.setOptTime(DateHelper.ARABIAN.formatDateTime(new Date(data.getOptTime() * 1000L)));
                vo.setOptPeriod(getStrOptPeriod(data.getBlock_term()));
                vo.setOptName(getOptName(data.getOperator()));
                vo.setReason(data.getReason());
                UserMonitorData monitorData = userMonitorDao.findDataByUid(data.getUid());
                vo.setCurrentStatus(getStrCurrentStatus(monitorData));
                list.add(vo);
            }
        }
        return new PageResultVO<>(count, list);
    }

    private String getStrCurrentStatus(UserMonitorData monitorData) {
        if (monitorData == null) {
            return "正常";
        }
        return getStrOptType(monitorData.getCode());
    }

    private String getStrOptPeriod(String blockTerm) {
        return switch (blockTerm) {
            case "1" -> "24小时";
            case "2" -> "7天";
            case "3" -> "永久";
            case "4" -> "3分钟";
            case "5" -> "30分钟";
            case "60" -> "1小时";
            case "120" -> "2小时";
            default -> "";
        };
    }

    private String getStrOptType(int code) {
        return switch (code) {
            case 0 -> "解封";
            case 1 -> "警告";
            case 2 -> "冻结功能";
            case 3 -> "封禁账号";
            case 4 -> "封设备";
            case 5 -> "封禁直播间";
            case 6 -> "封禁语聊房";
            default -> "";
        };
    }

    private String getUserType(String uid) {
        FamilyMemberData familyMemberData = familyMemberDao.selectByUid(uid);
        if (familyMemberData != null) {
            return familyMemberData.isOwner() ? "公会长" : "主播";
        }
        return "普通用户";
    }

    private String getOptUid(String account) {
        if (account.equals("system")) {
            return "system";
        }
        Manager manager = managerDao.getManager(account);
        if (manager != null) {
            return manager.get_id().toString();
        }
        AdminUser adminUser = adminUserDao.findByAccount(account);
        if (adminUser != null) {
            return adminUser.getUid();
        }
        return account;
    }

    private String getOptName(String optUid) {
        if (optUid.equals("system")) {
            return "system";
        }
        Manager manager = managerDao.getDataByUidFromCache(optUid);
        if (manager != null) {
            return manager.getAccount();
        }
        AdminUser adminUser = adminUserDao.getAdminFromCache(optUid);
        if (adminUser != null) {
            return adminUser.getAccount();
        }
        return optUid;
    }
}
