package com.quhong.operation.task;

import com.quhong.config.AsyncConfig;
import com.quhong.operation.server.OfficialPushService;
import com.quhong.operation.server.RoomEventService;
import com.quhong.utils.K8sUtils;
import jakarta.annotation.Resource;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.scheduling.annotation.Async;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

@Component
public class CycleTask {

    public final Logger logger = LoggerFactory.getLogger(getClass());

    @Resource
    private K8sUtils k8sUtils;

    @Resource
    private RoomEventService roomEventService;
    @Resource
    private OfficialPushService officialPushService;

    @Async(AsyncConfig.ASYNC_TASK)
    @Scheduled(cron = "0 0/1 * * * ?")
    public void roomEventCycleTask() {
        if (!k8sUtils.isMasterFromCache()) {
            return;
        }
        roomEventService.checkRefundFee();
        roomEventService.autoEventReview();
    }

    @Async(AsyncConfig.ASYNC_TASK)
    @Scheduled(cron = "10 0/2 * * * ?")
    public void pushOfficialMsg() {
        if (!k8sUtils.isMasterFromCache()) {
            return;
        }
        officialPushService.pushOfficialMsg();
    }
}
