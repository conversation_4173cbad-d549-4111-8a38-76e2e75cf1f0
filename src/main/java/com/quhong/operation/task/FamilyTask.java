package com.quhong.operation.task;

import com.alibaba.fastjson.JSONObject;
import com.quhong.config.AsyncConfig;
import com.quhong.core.config.ServerConfig;
import com.quhong.core.date.DateSupport;
import com.quhong.core.utils.DateHelper;
import com.quhong.data.ActorData;
import com.quhong.data.condition.FamilyCondition;
import com.quhong.enums.CharmLogTypeEnum;
import com.quhong.enums.SLangType;
import com.quhong.mongo.dao.*;
import com.quhong.mongo.data.FamilyDismissRecordData;
import com.quhong.mysql.dao.*;
import com.quhong.mysql.data.FamilyDailyDetailData;
import com.quhong.mysql.data.FamilyData;
import com.quhong.mysql.data.FamilyMemberData;
import com.quhong.mysql.data.JoinFamilyRecordData;
import com.quhong.operation.server.FamilyManageService;
import com.quhong.operation.server.FamilyReportsService;
import com.quhong.operation.share.elasticsearch.dao.CharmLogEsDao;
import com.quhong.operation.utils.CollectionUtil;
import com.quhong.service.OfficialMsgService;
import com.quhong.utils.AsyncUtils;
import com.quhong.utils.K8sUtils;
import jakarta.annotation.PostConstruct;
import jakarta.annotation.Resource;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.scheduling.annotation.Async;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import java.io.Serializable;
import java.time.LocalDate;
import java.time.YearMonth;
import java.util.*;
import java.util.concurrent.TimeUnit;

/**
 * <AUTHOR>
 * @date 2023/12/21
 */
@Component
public class FamilyTask {

    public final Logger logger = LoggerFactory.getLogger(getClass());

    private static final int OFFICIAL_FAMILY_ID = ServerConfig.isProduct() ? 0 : 1 ;

    @Resource
    private K8sUtils k8sUtils;
    @Resource
    private FamilyDao familyDao;
    @Resource
    private FamilyMemberDao familyMemberDao;
    @Resource
    private FamilyDailyDetailDao familyDailyDetailDao;
    @Resource
    private RoomMicLogDao roomMicLogDao;
    @Resource
    private RechargeDailyInfoDao rechargeDailyInfoDao;
    @Resource
    private FamilyReportsService familyReportsService;
    @Resource
    private GameStatDao gameStatDao;
    @Resource
    private CharmLogEsDao charmLogEsDao;
    @Resource
    private FamilyDailyIncomeDao familyDailyIncomeDao;
    @Resource
    private JoinFamilyRecordDao joinFamilyRecordDao;
    @Resource
    private ActorDao actorDao;
    @Resource
    private FamilyManageService familyManageService;
    @Resource
    private FamilyDismissRecordDao familyDismissRecordDao;
    @Resource
    private OfficialMsgService officialMsgService;

    @Async(AsyncConfig.ASYNC_TASK)
    @Scheduled(cron = "0 5 21 * * ?")
    public void saveFamilyDailyDetail() {
        if (!k8sUtils.isMasterFromCache()) {
            return;
        }
        long timeMillis = System.currentTimeMillis();
        String yesterday = DateSupport.ARABIAN.getStrYesterday();
        List<FamilyData> familyList = familyDao.getAllFamily();
        if (CollectionUtils.isEmpty(familyList)) {
            logger.info("familyList is empty. date={}", yesterday);
            return;
        }
        logger.info("saveFamilyDailyDetail start. date={} familyList.size={}", yesterday, familyList.size());
        for (FamilyData familyData : familyList) {
            saveFamilyDailyDetail(familyData, yesterday);
        }
        logger.info("saveFamilyDailyDetail end.  date={} cost={}", yesterday, System.currentTimeMillis() - timeMillis);
    }


    /**
     * 每月1号6点(UTC+3时区)将数据报表下载后发送到邮箱
     */
    @Async(AsyncConfig.ASYNC_TASK)
    @Scheduled(cron = "0 0 3 1 * ?")
    public void downloadReportAndSendEmail() {
        if (!k8sUtils.isMasterFromCache()) {
            return;
        }
        FamilyCondition condition = new FamilyCondition();
        condition.setUid("649d37dbac570000c9000455");
        condition.setEmail("<EMAIL>");
        condition.setDownloadType(4);
        String[] monthFirstAndLastDay = DateHelper.ARABIAN.getMonthFirstAndLastDay();
        condition.setStartTime(monthFirstAndLastDay[0]);
        condition.setEndTime(monthFirstAndLastDay[1]);
        condition.setPage(1);
        condition.setPageSize(100000);
        logger.info("downloadReportAndSendEmail. condition={}", JSONObject.toJSONString(condition));
        familyReportsService.asyncDownloadReport(condition);
    }

    /**
     * 每月1号3点(UTC+3时区)执行公会自动解散任务
     */
     @Async(AsyncConfig.ASYNC_TASK)
     @Scheduled(cron = "10 0 0 1 * ?")
    public void familyAutoDismiss() {
        if (!k8sUtils.isMasterFromCache()) {
            return;
        }
        logger.info("familyAutoDismiss start.");
        long timeMillis = System.currentTimeMillis();
        List<FamilyData> familyList = familyDao.getAllFamily();
        int familyNum = 0;
        int anchorNum = 0;
        if (CollectionUtils.isEmpty(familyList)) {
            return;
        }
        Set<Integer> hasIncomeFamilySet = new HashSet<>();
        LocalDate yesterday = DateSupport.ARABIAN.getYesterday();
        for (int i = 0; i < 3; i++) {
            YearMonth yearMonth = YearMonth.from(yesterday.minusMonths(i));
            LocalDate startDate = yearMonth.atDay(1);
            LocalDate endDate = yearMonth.atEndOfMonth();
            Map<Integer, Long> familyIncomeMap = familyDailyIncomeDao.getFamilyIncomeMap(DateSupport.getIntDate(startDate), DateSupport.getIntDate(endDate));
            for (FamilyData familyData : familyList) {
                logger.info("familyIncome. familyId={} familyRid={} yearMonth={} income={}", familyData.getId(), familyData.getRid(), yearMonth.toString(), familyIncomeMap.getOrDefault(familyData.getId(), 0L));
                if (familyIncomeMap.getOrDefault(familyData.getId(), 0L) >= 7000000) {
                    hasIncomeFamilySet.add(familyData.getId());
                }
            }
        }
        int nowTime = DateHelper.getNowSeconds();
        for (FamilyData familyData : familyList) {
            if (familyData.getCtime() > nowTime - TimeUnit.DAYS.toSeconds(90)) {
                continue;
            }
            if (hasIncomeFamilySet.contains(familyData.getId())) {
                continue;
            }
//            if (familyData.getId() == OFFICIAL_FAMILY_ID) {
//                continue;
//            }
            logger.info("autoDismissFamily. familyId={} familyRid={}", familyData.getId(), familyData.getRid());
            familyData.setStatus(2);
            familyDao.update(familyData);
            List<FamilyMemberData> memberList = familyMemberDao.selectMemberList(familyData.getId());
            // 给公会全部成员发送公会被禁用消息
            if (!CollectionUtils.isEmpty(memberList)) {
                for (FamilyMemberData memberData : memberList) {
                    ActorData actorData = actorDao.getActorDataFromCache(memberData.getUid());
                    if (null == actorData) {
                        logger.error("cannot find actor, uid={}", memberData.getUid());
                        return;
                    }
                    String title;
                    String noticeBody;
                    if (actorData.getSlang() == SLangType.ENGLISH) {
                        title = "Agency Dissolution Notice";
                        noticeBody = "Dear Waho host,\n" +
                                "\n" +
                                "Due to your agency's income  less than 10,000,000 charm points for three consecutive months, it has been automatically dissolved. You have been removed from the agency and are now a regular user. Thank you for your understanding and support!";
                    } else {
                        title = "إشعار إبلاغ الوكالة";
                        noticeBody = "مذيع واهو عزيزى\n" +
                                "\n" +
                                "نظرًا لأن دخل الوكالة التي تنتمي إليها أقل من 10,000,000 نقطة جاذبية لمدة ثلاثة أشهر متتالية، فقد تم إغلاق الوكالة تلقائيًا. لقد تم إخراجك من الوكالة وأصبحت مستخدمًا عاديًا. شكرًا لتفهمك ودعمك!";
                    }
                    familyMemberDao.deleteByFamilyIdAndUid(memberData.getFamilyId(), memberData.getUid(), "公会被自动解散");
                    // 主播收到app内通知提醒
                    officialMsgService.officialMsgPush(actorData.getUid(), title, noticeBody, 20);
                }
            }
            // List<FamilyMemberData> memberList = familyMemberDao.selectMemberList(familyData.getId());
            // if (!CollectionUtils.isEmpty(memberList)) {
            //     for (FamilyMemberData memberData : memberList) {
                    // if (memberData.getUid().equals(familyData.getOwnerUid())) {
                    //     // 公会长
                    //     familyMemberDao.deleteByFamilyIdAndUid(memberData.getFamilyId(), memberData.getUid(), "公会自动解散");
                    // } else {
                    //     // 公会成员
                    //     ActorData actorData = actorDao.getActorDataFromCache(memberData.getUid());
                    //     if (actorData == null) {
                    //         continue;
                    //     }
                    //     int beforeFamilyId = memberData.getFamilyId();
                    //     memberData.setFamilyId(OFFICIAL_FAMILY_ID);
                    //     memberData.setDevote(0L);
                    //     memberData.setRole(3);
                    //     memberData.setCtime(nowTime);
                    //     familyMemberDao.update(memberData);
                    //     JoinFamilyRecordData recordData = new JoinFamilyRecordData(actorData.getUid(), actorData.getTn_id(), beforeFamilyId, 0, nowTime);
                    //     joinFamilyRecordDao.insert(recordData);
                    //     recordData.setId(null);
                    //     recordData.setAction(1);
                    //     joinFamilyRecordDao.insert(recordData);
                    //     actorDao.updateField(actorData.getUid(), "familyId", familyData.getId());
                    //     // 发送通知
                    //     familyManageService.sendOfficialMsg(actorData, familyData);
                    // }
            //         anchorNum ++;
            //     }
            //     familyNum ++;
            // }
            // familyData.setStatus(2);
            // familyDao.update(familyData);
             familyDao.addBanCreateFamilyUser(familyData.getOwnerUid());
             familyDismissRecordDao.save(new FamilyDismissRecordData(familyData.getId(), familyData.getOwnerUid(), familyData.getManager(), familyData.getSuperManager(), 1, 0, "system", memberList));
        }
        logger.info("familyAutoDismiss end. familyNum={} anchorNum={} cost={}", familyNum, anchorNum, System.currentTimeMillis() - timeMillis);
    }

    public void fixFamilyData() {
        AsyncUtils.execute(() -> {
            logger.info("fixFamilyData start.");
            List<FamilyDismissRecordData> familyRecordList = familyDismissRecordDao.getALlList();
            if (CollectionUtils.isEmpty(familyRecordList)) {
                return;
            }
            Set<Integer> hasIncomeFamilySet = new HashSet<>();
            LocalDate yesterday = DateSupport.ARABIAN.getYesterday();
            for (int i = 0; i < 3; i++) {
                YearMonth yearMonth = YearMonth.from(yesterday.minusMonths(i));
                LocalDate startDate = yearMonth.atDay(1);
                LocalDate endDate = yearMonth.atEndOfMonth();
                Map<Integer, Long> familyIncomeMap = familyDailyIncomeDao.getFamilyIncomeMap(DateSupport.getIntDate(startDate), DateSupport.getIntDate(endDate));
                for (FamilyDismissRecordData recordData : familyRecordList) {
                    FamilyData familyData = familyDao.selectById(recordData.getFamilyId());
                    logger.info("familyIncome. familyId={} familyRid={} yearMonth={} income={}", familyData.getId(), familyData.getRid(), yearMonth.toString(), familyIncomeMap.getOrDefault(familyData.getId(), 0L));
                    if (familyIncomeMap.getOrDefault(familyData.getId(), 0L) >= 10000000) {
                        hasIncomeFamilySet.add(familyData.getId());
                    }
                }
            }
            hasIncomeFamilySet.forEach(k -> fixFamilyData(k));
            logger.info("需恢复的公会. hasIncomeFamilySet.size={} hasIncomeFamilySet={}", hasIncomeFamilySet.size(), Arrays.toString(hasIncomeFamilySet.toArray()));
        });
    }

    private void fixFamilyData(int familyId) {
         if (familyId == 3265) {
             return;
         }
        FamilyDismissRecordData recordData = familyDismissRecordDao.selectByFamilyId(familyId);
        if (recordData == null) {
            logger.error("找不到FamilyDismissRecordData. familyId={}", familyId);
            return;
        }
        FamilyData newFamilyData = familyDao.selectByOwnerUid(recordData.getOwnerUid());
        if (newFamilyData != null) {
            logger.error("公会长已创建其他公会. uid={} familyRid={} familyId={}", newFamilyData.getOwnerUid(), newFamilyData.getRid(), newFamilyData.getId());
            return;
        }
        FamilyData familyData = familyDao.selectById(familyId);
        if (familyData == null) {
            logger.error("找不到原来的公会. familyId={}", familyId);
            return;
        }
        familyData.setStatus(1);
        familyDao.update(familyData);
        familyDao.removeBanCreateFamilyUser(familyData.getOwnerUid());
        if (CollectionUtils.isEmpty(recordData.getMemberList())) {
            logger.error("公会成员数据为空. familyId={}", recordData.getFamilyId());
            return;
        }
        recordData.getMemberList().forEach(k -> {
            FamilyMemberData familyMember = familyMemberDao.selectByUid(k.getUid());
            if (familyMember != null) {
                familyMemberDao.deleteByFamilyIdAndUid(familyMember.getFamilyId(), familyMember.getUid(), "强制回归原公会");
            }
            familyMemberDao.insert(k);
        });
    }

//    @PostConstruct
    public void exportFamilyData() {
        AsyncUtils.execute(() -> {
            logger.info("fixFamilyData start.");
            List<FamilyDismissRecordData> familyRecordList = familyDismissRecordDao.getALlList();
            if (CollectionUtils.isEmpty(familyRecordList)) {
                return;
            }
            LocalDate yesterday = DateSupport.ARABIAN.getYesterday();
            Map<Integer, Long> familyIncomeMap2 = new HashMap<>();
            Map<Integer, Long> familyIncomeMap3 = new HashMap<>();
            Map<Integer, Long> familyIncomeMap4 = new HashMap<>();
            for (int i = 0; i < 3; i++) {
                YearMonth yearMonth = YearMonth.from(yesterday.minusMonths(i + 1));
                LocalDate startDate = yearMonth.atDay(1);
                LocalDate endDate = yearMonth.atEndOfMonth();
                Map<Integer, Long> familyIncomeMap = familyDailyIncomeDao.getFamilyIncomeMap(DateSupport.getIntDate(startDate), DateSupport.getIntDate(endDate));
                if (i == 0) {
                    familyIncomeMap4 = familyIncomeMap;
                } else if (i == 1) {
                    familyIncomeMap3 = familyIncomeMap;
                } else {
                    familyIncomeMap2 = familyIncomeMap;
                }
            }
            Set<Integer> familyIdSet = new HashSet<>();
            familyIdSet.addAll(familyIncomeMap2.keySet());
            familyIdSet.addAll(familyIncomeMap3.keySet());
            familyIdSet.addAll(familyIncomeMap4.keySet());
            List<FamilyReport> list = new ArrayList<>();
            for (Integer familyId: familyIdSet) {
                FamilyReport report = new FamilyReport();
                FamilyData familyData = familyDao.selectById(familyId);
                if (familyData == null) {
                    continue;
                }
                report.setRid(familyData.getRid() + "");
                report.setOwnerRid(actorDao.getActorDataFromCache(familyData.getOwnerUid()).getRid() + "");
                report.setIncome1(familyIncomeMap2.getOrDefault(familyId, 0L));
                report.setIncome2(familyIncomeMap3.getOrDefault(familyId, 0L));
                report.setIncome3(familyIncomeMap4.getOrDefault(familyId, 0L));
                list.add(report);
            }
            String reportDownloadUrl = familyReportsService.exportAndUpload(list, FamilyReport.class, "report_data");
            logger.info("报表：{}", reportDownloadUrl);
        });
    }

    public static class FamilyReport implements Serializable {

        private static final long serialVersionUID = 8863595669126461592L;

        private String rid;
        private String ownerRid;
        private Long income1;
        private Long income2;
        private Long income3;

        public String getRid() {
            return rid;
        }

        public void setRid(String rid) {
            this.rid = rid;
        }

        public String getOwnerRid() {
            return ownerRid;
        }

        public void setOwnerRid(String ownerRid) {
            this.ownerRid = ownerRid;
        }

        public Long getIncome1() {
            return income1;
        }

        public void setIncome1(Long income1) {
            this.income1 = income1;
        }

        public Long getIncome2() {
            return income2;
        }

        public void setIncome2(Long income2) {
            this.income2 = income2;
        }

        public Long getIncome3() {
            return income3;
        }

        public void setIncome3(Long income3) {
            this.income3 = income3;
        }
    }


    private void saveFamilyDailyDetail(FamilyData familyData, String strDate) {
        Integer[] timeArr = DateHelper.ARABIAN.getStartOrEndSeconds(strDate, strDate);
        int startTime = timeArr[0];
        int endTime = --timeArr[1];
        List<FamilyMemberData> memberList = familyMemberDao.selectMemberList(familyData.getId());
        Set<String> rechargeUserSet = rechargeDailyInfoDao.selectRechargeUserSet(startTime, endTime);
        int newAnchorNum = 0;
        int workingAnchorNum = 0;
        int payingAnchorNum = 0;
        int activeAnchorNum = 0;
        long roomCharmIncome = 0;
        long msgCharmIncome = 0;
        long gameCostDiamonds = 0;
        if (!CollectionUtils.isEmpty(memberList)) {
            for (FamilyMemberData memberData : memberList) {
                if (memberData.getCtime() >= startTime && memberData.getCtime() < endTime) {
                    newAnchorNum++;
                }
                int upMicTime = roomMicLogDao.actorAddUpMicTime(startTime, endTime, memberData.getUid());
                boolean isWorkingAnchor = upMicTime >= 2 * 60 * 60;
                if (isWorkingAnchor) {
                    workingAnchorNum++;
                }
                if (rechargeUserSet.contains(memberData.getUid())) {
                    payingAnchorNum++;
                }
                long anchorMsgCharmIncome = charmLogEsDao.getTotalCharm(memberData.getUid(), memberData.getFamilyId(), CharmLogTypeEnum.RECEIVE_MSG_GIFTS.logType, startTime, endTime);
                msgCharmIncome += anchorMsgCharmIncome;
                long anchorRoomCharmIncome = charmLogEsDao.getTotalCharm(memberData.getUid(), memberData.getFamilyId(), CharmLogTypeEnum.RECEIVE_ROOM_GIFTS.logType, startTime, endTime);
                roomCharmIncome += anchorRoomCharmIncome;
                if (isWorkingAnchor && anchorMsgCharmIncome + anchorRoomCharmIncome > 0) {
                    activeAnchorNum++;
                }
            }
            List<String> memberUidList = CollectionUtil.getPropertyList(memberList, FamilyMemberData::getUid, "");
            gameCostDiamonds = gameStatDao.getUsersGameTotalCost(memberUidList, startTime, endTime);
        }
        FamilyDailyDetailData data = new FamilyDailyDetailData();
        data.setFamilyId(familyData.getId());
        data.setDate(Integer.parseInt(strDate.replace("-", "")));
        data.setNewAnchorNum(newAnchorNum);
        data.setActiveAnchorNum(activeAnchorNum);
        data.setWorkingAnchorNum(workingAnchorNum);
        data.setPayingAnchorNum(payingAnchorNum);
        data.setMsgCharmIncome(msgCharmIncome);
        data.setRoomCharmIncome(roomCharmIncome);
        data.setGameCostDiamonds(gameCostDiamonds);
        data.setCtime(DateHelper.ARABIAN.stringTimeToStampSecond(strDate + " 12:00:00"));
        familyDailyDetailDao.insert(data);
    }
}