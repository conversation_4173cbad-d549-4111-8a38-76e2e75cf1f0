package com.quhong.hash;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.data.redis.core.RedisCallback;
import org.springframework.data.redis.core.RedisTemplate;

import java.util.ArrayList;
import java.util.BitSet;
import java.util.List;


@SuppressWarnings("all")
public class RedisBitArray extends AbstractBitArray implements BitArray {
    private static final Logger logger = LoggerFactory.getLogger(RedisBitArray.class);

    private BitSet bitSet;
    // 是否将bitmap转换为bitSet操作，以便减少Redis ops
    private boolean localGet;
    private long expireSecond;
    private RedisTemplate redisTemplate;

    private RedisBitArray() {
        super(null);
    }

    public RedisBitArray(RedisTemplate redisTemplate, String key, long expireSecond) {
        super(key);
        this.redisTemplate = redisTemplate;
        this.expireSecond = expireSecond;
    }

    public RedisBitArray(RedisTemplate redisTemplate, String key, long expireSecond, boolean localGet) {
        super(key);
        this.redisTemplate = redisTemplate;
        this.expireSecond = expireSecond;
        this.localGet = localGet;
    }

    public BitSet fromByteArrayReverse(final byte[] bytes) {
        final BitSet bits = new BitSet();
        for (int i = 0; i < bytes.length * 8; i++) {
            if ((bytes[i / 8] & (1 << (7 - (i % 8)))) != 0) {
                bits.set(i);
            }
        }
        return bits;
    }

    @Override
    public List<Boolean> batchSet(List<Long> indexList) {
        return (List<Boolean>) redisTemplate.executePipelined((RedisCallback<Object>) connection -> {
            byte[] key = this.key.getBytes();
            indexList.forEach(index -> connection.setBit(key, index, true));
            connection.expire(key, expireSecond);
            return null;
        });
    }

    @Override
    public List<Boolean> batchGet(List<Long> indexList) {
        if (localGet) {
            if (null == bitSet) {
                redisTemplate.execute((RedisCallback<Object>) connection -> {
                    byte[] bytes = connection.get(this.key.getBytes());
                    if (null == bytes) {
                        return null;
                    }
                    bitSet = fromByteArrayReverse(bytes);
                    // 数据修正，cardinality达到length的55%时重新记录
                    int count = bitSet.cardinality();
                    int length = bitSet.length();
                    if (length > 0 && 0.55 < (double) count / length) {
                        logger.info("redis bit array. cardinality reach 55%. delete redis key={}", this.key);
                        connection.del(this.key.getBytes());
                    }
                    return null;
                });
            }
            List<Boolean> result = new ArrayList<>(indexList.size());
            indexList.forEach(i -> result.add(null == bitSet ? false : bitSet.get(Math.toIntExact(i))));
            return result;
        } else {
            return (List<Boolean>) redisTemplate.executePipelined((RedisCallback<Object>) connection -> {
                byte[] key = this.key.getBytes();
                indexList.forEach(index -> connection.getBit(key, index));
                return null;
            });
        }
    }
}
