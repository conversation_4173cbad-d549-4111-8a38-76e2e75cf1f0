package com.quhong.gate.clusters;

import com.quhong.core.clusters.ClientCluster;
import com.quhong.core.clusters.clients.ClusterSocketClient;
import com.quhong.enums.ClusterEnum;
import org.springframework.stereotype.Component;

@Component
public class DataCluster extends ClientCluster {
    public DataCluster() {
        super(ClusterEnum.DATA, false);
    }

    @Override
    public void initSocketClient(ClusterSocketClient client){
        client.init(new GateClientConnector(cluster, client.getServerData(), messageGroup, executorGroup));
    }
}
