package com.quhong.gate.executors;

import com.quhong.core.annotation.MsgExecutor;
import com.quhong.core.clusters.clients.ClusterClientConnector;
import com.quhong.core.executors.AbstractMsgExecutor;
import com.quhong.core.msg.server.GlobalMsg;
import com.quhong.enums.BaseServerCmd;
import com.quhong.gate.net.GateConnector;
import com.quhong.gate.net.SessionMgr;
import com.quhong.msg.GateMsg;
import com.quhong.server.codec.mars.mars.MarsMsgEncoder;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.Map;

/**
 * 全服消息
 */
@MsgExecutor
public class GlobalMsgExecutor extends AbstractMsgExecutor<ClusterClientConnector, GlobalMsg> {
    private static final Logger logger = LoggerFactory.getLogger(GlobalMsgExecutor.class);

    public GlobalMsgExecutor() {
        super(BaseServerCmd.GLOBAL_MSG);
    }

    @Override
    public void execute(ClusterClientConnector connector, GlobalMsg msg) {
        GateMsg gMsg = new GateMsg();
        gMsg.getHeader().setSeq(msg.getSeq());
        gMsg.getHeader().setCmdId(msg.getMsgCmd());
        gMsg.setBody(msg.getBody());
        MarsMsgEncoder.encodeCache(gMsg);
        Map<String, GateConnector> uidMap = SessionMgr.instance.getUidMap();
        logger.info("execute global msg. cmd={}  uidSize={} msgId={} sessionId={}", msg.getMsgCmd(), uidMap.size(), msg.getMsgId(), connector.getSessionId());
        for (GateConnector gateConnector : uidMap.values()) {
            if (gateConnector != null && !gateConnector.getUid().equals(msg.getFromUid())) {
                gateConnector.sendMsg(gMsg);
            }
        }
    }
}
