package com.quhong.gate.login;

import com.quhong.cache.CacheMap;
import com.quhong.core.clusters.ClusterGroup;
import com.quhong.core.config.ServerConfig;
import com.quhong.core.logback.PlayerLogger;
import com.quhong.core.msg.server.PlayerKickOutMsg;
import com.quhong.core.msg.server.PlayerLoginMsg;
import com.quhong.core.timers.DelayTask;
import com.quhong.core.timers.TimerService;
import com.quhong.datas.PlayerData;
import com.quhong.enums.ClusterEnum;
import com.quhong.enums.Cmd;
import com.quhong.enums.PlayerStatus;
import com.quhong.enums.ResponseCode;
import com.quhong.gate.executors.GateMsgExecutor;
import com.quhong.gate.logback.GateLogger;
import com.quhong.gate.msg.HandShakeMsg;
import com.quhong.gate.msg.HeartBeatMsg;
import com.quhong.gate.msg.LoginMsg;
import com.quhong.gate.msg.ResponseAck;
import com.quhong.gate.net.GateConnector;
import com.quhong.gate.net.SessionMgr;
import com.quhong.gate.redis.PlayerRedis;
import com.quhong.msg.MarsMsg;
import io.netty.channel.ChannelHandlerContext;
import jakarta.annotation.PostConstruct;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.util.StringUtils;

/**
 * 登录校验模块
 */
@Component
public class LoginService {
    private static final Logger logger = LoggerFactory.getLogger(LoginService.class);

    private static final int TOKEN_CACHE_TIME = 120 * 1000;

    @Autowired
    private PlayerRedis playerRedis;
    private CacheMap<String, String> tokenMap;


    public LoginService() {
        this.tokenMap = new CacheMap<>(TOKEN_CACHE_TIME);
    }

    @PostConstruct
    public void postInit() {
        this.tokenMap.start();
    }

    public boolean login(GateConnector connector, MarsMsg msg) {
        if (connector.isLogin()) {
            return true;
        }
        String uid = msg.getProtoHeader().getFromUid();
        String reqToken = msg.getProtoHeader().getToken();
        logger.info("check login. cmd={} uid={} reqToken={} class={}", msg.getCmd(), uid, reqToken, msg.getClass().getSimpleName());
        if (!connector.isConnected()) {
            // 如果connector已经关闭，则不再处理
//            logger.error("connector has closed. cmd={} uid={} sessionId={}", msg.getCmd(), uid, connector.getSessionId());
            return false;
        }
        if (StringUtils.isEmpty(uid)) {
            logger.error("uid is empty. cmd={} uid={} sessionId={}", msg.getCmd(), uid, connector.getSessionId());
            return false;
        }
        if (uid.length() != 24) {
            logger.error("uid is invalid. cmd={} uid={} sessionId={}", msg.getCmd(), uid, connector.getSessionId());
            return false;
        }
        if (!checkToken(connector, msg, uid)) {
            return false;
        }
        GateConnector old = SessionMgr.instance.getConnectorByUid(uid);
        if (old != null && old != connector) {
            boolean sendKickOut = false;
            if (msg instanceof HandShakeMsg) {
//                if(!old.isDispose() && old.isLogin()) {
//                    if (StringUtils.isEmpty(reqToken) && (!connector.isClientFront())) {
//                        logger.error("actor in back. can not kick out early session from login. uid={} sessionId={}", uid, connector.getSessionId());
//                        return false;
//                    }
//                }
                String deviceId = ((HandShakeMsg) msg).getDeviceId();
                if (!StringUtils.isEmpty(deviceId)) {
                    if (old.getPlayerData() != null) {
                        if (!deviceId.equals(old.getPlayerData().getDeviceId())) {
                            sendKickOut = true;
                        }
                    }
                }
            }
//            else if(msg instanceof HeartBeatMsg){
//                if(!old.isDispose() && old.isLogin()) {
//                    connector.clientBack();
//                    // 如果是心跳消息，且用户处于后台，不允许替换之前的链接
//                    HeartBeatMsg heartBeatMsg = (HeartBeatMsg) msg;
//                    if (StringUtils.isEmpty(reqToken) && heartBeatMsg.getScene() == -1) {
//                        logger.error("actor in back. can not kick out early session from heart. uid={} sessionId={}", uid, connector.getSessionId());
//                        return false;
//                    }
//                }
//            }
            // 玩家在其他地点登录,3秒后关闭先前的账号
            kickOut(old, sendKickOut);
        }
        // PlayerKickOutMsg消息room没处理
//        PlayerData oldPlayerData = playerRedis.getPlayerDataFromRedis(uid);
//        if (oldPlayerData != null && oldPlayerData.getSessionId() != -1) {
//            int serverId = ClusterGroup.fetchServerIdFromSessionId(oldPlayerData.getSessionId());
//            if (serverId != ServerConfig.getServerID()) {
//                // 如果该玩家在其他服，则发送远程关闭消息
//                TimerService.getService().addDelay(new DelayTask(null, 100) {
//                    @Override
//                    protected void execute() {
//                        remoteKickOut(serverId, oldPlayerData);
//                    }
//                });
//            }
//        }
        // 创建用户数据并赋值
        PlayerData playerData = createPlayerData(connector, uid, msg);
        connector.setPlayerData(playerData);
        connector.updateHeartExpireTime(0);
        // 设置loginFlag.避免随后的第一次心跳被拦截
        connector.setLoginFlag(true);

        if (!SessionMgr.instance.addConnectorToUidMap(connector)) {
            // 将connector与uid挂钩
            // 如果添加失败，表明连接已经断开
            logger.info("connector has closed. login invalid. uid={} sessionId={}", uid, connector.getSessionId());
            GateMsgExecutor.sendLoginAck(connector, msg, ResponseCode.SERVER_RRROR);
            return false;
        }
        // 将玩家在线状态写入redis
        playerRedis.online(playerData);
        // 设置登录状态
        connector.setLogin(true);
        ChannelHandlerContext session = connector.getSession();
        String remoteAddress = "";
        if (session != null) {
            remoteAddress = session.channel().remoteAddress().toString();
        }
        logger.info("login success. cmd={} uid={} sessionId={} remoteIp={}", msg.getCmd(), playerData.getUid(), playerData.getSessionId(), remoteAddress);
        // 发送在线消息给聊天服
        sendOnline(playerData);
        return true;
    }

    private void check(String uid) {
        PlayerData oldPlayerData = playerRedis.getPlayerDataFromRedis(uid);
        if (oldPlayerData != null && oldPlayerData.getSessionId() != -1) {
            int serverId = ClusterGroup.fetchServerIdFromSessionId(oldPlayerData.getSessionId());
            if (serverId != ServerConfig.getServerID()) {
                // 如果该玩家在其他服，则发送远程关闭消息
                TimerService.getService().addDelay(new DelayTask(null, 100) {
                    @Override
                    protected void execute() {
                        remoteKickOut(serverId, oldPlayerData);
                    }
                });
            }
        }
    }

    private boolean checkToken(GateConnector connector, MarsMsg msg, String uid) {
        String reqToken = msg.getProtoHeader().getToken();
        // 只对握手进行判断
        if (Cmd.HAND_SHAKE == msg.getCmd()) {
            if (StringUtils.isEmpty(reqToken)) {
                logger.info("reqToken is empty. cmd={} uid={} sessionId={}", msg.getCmd(), uid, connector.getSessionId());
                // GateMsgExecutor.sendLoginAck(connector, msg, ResponseCode.TOKEN_VERIFICATION_FAILED);
                return false;
            }
        }
        String token = tokenMap.getData(uid);
        boolean fromCache = false;
        if (StringUtils.isEmpty(token)) {
            token = playerRedis.getToken(uid);
            if (StringUtils.isEmpty(token)) {
                logger.info("can not find token. cmd={} uid={} sessionId={}", msg.getCmd(), uid, connector.getSessionId());
                // GateMsgExecutor.sendLoginAck(connector, msg, ResponseCode.TOKEN_VERIFICATION_FAILED);
                return false;
            }
            tokenMap.cacheData(uid, token);
        } else {
            fromCache = true;
        }
        if (!StringUtils.isEmpty(reqToken)) {
            if (!token.equals(reqToken)) {
                if (fromCache) {
                    token = playerRedis.getToken(uid);
                    if (token.equals(reqToken)) {
                        return true;
                    }
                }
                logger.info("verify token failed. cmd={} uid={} sessionId={} reqToken={} token={}", msg.getCmd(), uid, connector.getSessionId(), reqToken, token);
                // GateMsgExecutor.sendLoginAck(connector, msg, ResponseCode.TOKEN_VERIFICATION_FAILED);
                return false;
            }
        }
        return true;
    }

    private PlayerData createPlayerData(GateConnector connector, String uid, MarsMsg msg) {
        PlayerData playerData = new PlayerData();
        playerData.setUid(uid);
        playerData.setSessionId(connector.getSessionId());
        playerData.setStatus(PlayerStatus.ONLINE);
        if (msg instanceof LoginMsg) {
            playerData.setSlang(((LoginMsg) msg).getSlang());
        } else if (msg instanceof HeartBeatMsg) {
            playerData.setSlang(((HeartBeatMsg) msg).getSlang());
        }
        return playerData;
    }

    private void sendOnline(PlayerData playerData) {
        PlayerLoginMsg msg = new PlayerLoginMsg();
        msg.getHeader().setUid(playerData.getUid());
        msg.getHeader().setSessionId(playerData.getSessionId());
        msg.setPlayerUid(playerData.getUid());
        byte[] msgBody = msg.toBody();
        // 发送给聊天服
        ClusterGroup.sendCacheMsgByClusterId(ClusterEnum.ROOM, msg, msgBody);
        ClusterGroup.sendCacheMsgByClusterId(ClusterEnum.DATA, msg, msgBody);
    }

    private void kickOut(GateConnector connector, boolean sendKickOut) {
        SessionMgr.instance.remove(connector);
        // 发送kick out的消息
        if (sendKickOut) {
            GateLogger.info(logger, connector, "kick out player. will delay 3s to close the session. sendKickOut={}", sendKickOut);
            sendKickOut(connector);
            TimerService.getService().addDelay(new DelayTask(10000) {
                @Override
                protected void execute() {
                    GateLogger.info(logger, connector, "kick out player. close the session");
                    connector.close(true);
                }
            });
        } else {
            GateLogger.info(logger, connector, "kick out player. close the session");
            connector.close(true);
        }
    }

    private void sendKickOut(GateConnector connector) {
        ResponseAck ack = new ResponseAck();
        ack.getHeader().setCmdId(Cmd.LOGIN_PUSH);
        ack.getHeader().setSeq(0);
        ack.setCode(ResponseCode.KICK_OUT.getCode());
        ack.setMsg(ResponseCode.KICK_OUT.getMsg());
        connector.sendMsg(ack);
    }

    private void remoteKickOut(int remoteServerId, PlayerData playerData) {
        PlayerLogger.info(logger, playerData, "kick out player. the player in remote serverId. remoteServerId={}", remoteServerId);
        ResponseCode responseCode = ResponseCode.KICK_OUT;
        PlayerKickOutMsg msg = new PlayerKickOutMsg();
        msg.setKickUid(playerData.getUid());
        msg.setKickSessionId(playerData.getSessionId());
        msg.setResultId(responseCode.getCode());
        msg.setResultMsg(responseCode.getMsg());
        ClusterGroup.getGroup().getCluster(ClusterEnum.ROOM).sendMsg(msg);
    }

    public static Logger getLogger() {
        return logger;
    }

    public PlayerRedis getPlayerRedis() {
        return playerRedis;
    }

    public void setPlayerRedis(PlayerRedis playerRedis) {
        this.playerRedis = playerRedis;
    }
}
