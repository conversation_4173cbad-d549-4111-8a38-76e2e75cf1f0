package com.quhong.gate.msg;

import com.quhong.core.annotation.Message;
import com.quhong.enums.Cmd;
import com.quhong.msg.MarsMsg;
import com.quhong.proto.WahoProtoBase;

@Message(cmd = Cmd.CHANGE_SCENE, isGate = true)
public class ChangeSceneMsg extends MarsMsg {
    private int oldScene;
    private int newScene;

    @Override
    protected void doFromBody(byte[] bytes) throws Exception {
        WahoProtoBase.ChangeScene msg = WahoProtoBase.ChangeScene.parseFrom(bytes);
        this.oldScene = msg.getOldScene();
        this.newScene = msg.getNewScene();
    }

    @Override
    protected byte[] doToBody() throws Exception {
        WahoProtoBase.ChangeScene.Builder builder = WahoProtoBase.ChangeScene.newBuilder();
        builder.setOldScene(oldScene);
        builder.setNewScene(newScene);
        return builder.build().toByteArray();
    }

    public int getOldScene() {
        return oldScene;
    }

    public void setOldScene(int oldScene) {
        this.oldScene = oldScene;
    }

    public int getNewScene() {
        return newScene;
    }

    public void setNewScene(int newScene) {
        this.newScene = newScene;
    }
}
