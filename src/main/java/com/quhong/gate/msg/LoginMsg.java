package com.quhong.gate.msg;

import com.quhong.core.annotation.Message;
import com.quhong.enums.Cmd;
import com.quhong.msg.MarsMsg;
import com.quhong.proto.WahoProtoBase;

@Message(cmd = Cmd.LOGIN, isGate = true)
public class LoginMsg extends MarsMsg {
    private int clientVersion;
    private int clientSystem; // 0 android 1 ios
    private String deviceId = "";
    private long roomMsgId; //房间内上次获取的msgId
    private int micVersion; // 麦位版本信息
    private int musicVersion;
    private int slang; // app语言 1 英语 2 阿语 3 土耳其语

    @Override
    protected void doFromBody(byte[] bytes) throws Exception {
        WahoProtoBase.Login msg = WahoProtoBase.Login.parseFrom(bytes);
        protoHeader.doFromBody(msg.getHeader());
        clientVersion = msg.getClientVersion();
        clientSystem = msg.getClientSystem();
        deviceId = msg.getDeviceId();
        roomMsgId = msg.getRoomMsgId();
        micVersion = msg.getMicVersion();
        musicVersion = msg.getMusicVersion();
        slang = msg.getSlang();
    }

    @Override
    protected byte[] doToBody() throws Exception {
        WahoProtoBase.Login.Builder builder = WahoProtoBase.Login.newBuilder();
        builder.setHeader(this.protoHeader.doToBody());
        builder.setClientVersion(clientVersion);
        builder.setClientSystem(clientSystem);
        builder.setDeviceId(deviceId);
        builder.setRoomMsgId(roomMsgId);
        builder.setMicVersion(micVersion);
        builder.setMusicVersion(musicVersion);
        builder.setSlang(slang);
        return builder.build().toByteArray();
    }

    public int getClientVersion() {
        return clientVersion;
    }

    public void setClientVersion(int clientVersion) {
        this.clientVersion = clientVersion;
    }

    public int getClientSystem() {
        return clientSystem;
    }

    public void setClientSystem(int clientSystem) {
        this.clientSystem = clientSystem;
    }

    public String getDeviceId() {
        return deviceId;
    }

    public void setDeviceId(String deviceId) {
        this.deviceId = deviceId;
    }

    public long getRoomMsgId() {
        return roomMsgId;
    }

    public void setRoomMsgId(long roomMsgId) {
        this.roomMsgId = roomMsgId;
    }

    public int getMicVersion() {
        return micVersion;
    }

    public void setMicVersion(int micVersion) {
        this.micVersion = micVersion;
    }

    public int getMusicVersion() {
        return musicVersion;
    }

    public void setMusicVersion(int musicVersion) {
        this.musicVersion = musicVersion;
    }

    public int getSlang() {
        return slang;
    }

    public void setSlang(int slang) {
        this.slang = slang;
    }
}
