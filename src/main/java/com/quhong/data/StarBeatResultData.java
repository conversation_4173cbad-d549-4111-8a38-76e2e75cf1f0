package com.quhong.data;

import com.alibaba.fastjson.JSON;
import com.quhong.mongo.data.CarnivalGameData;

import java.util.List;
import java.util.Map;

public class StarBeatResultData {
    private StarBeatPoolRewardData starBeatPoolRewardData;
    private int nowStar; // 当前用户累积的幸运值
    private List<AwardBean> awardBeanList; //  中奖开出的列表
    private Map<String, Integer> lowSourceMap; //  低价值
    private Map<String, Integer> highSourceMap; //  高价值
    private Map<String, Integer> allSourceMap; //   全部
    private Map<String, CarnivalGameData.PrizeConfig> allPrizeConfig; //  全奖品映射
    private Map<String, List<CarnivalGameData.PrizeConfig>> allBoxListMap; // 全礼盒奖品映射
    private int addTicket; // 需要增加的游戏抽奖券
    private CarnivalGameData.PrizeConfig maxPrizeConfig; // 价值最大的奖品

    public static class AwardBean {
        private String awardId;
        private int awardType; // 0 普通抽奖中出 1幸运一击

        public AwardBean() {
        }

        public AwardBean(String awardId, int awardType) {
            this.awardId = awardId;
            this.awardType = awardType;
        }

        public String getAwardId() {
            return awardId;
        }

        public void setAwardId(String awardId) {
            this.awardId = awardId;
        }

        public int getAwardType() {
            return awardType;
        }

        public void setAwardType(int awardType) {
            this.awardType = awardType;
        }

        @Override
        public String toString() {
            return JSON.toJSONString(this);
        }
    }

    public StarBeatResultData() {
    }

    public StarBeatPoolRewardData getStarBeatPoolRewardData() {
        return starBeatPoolRewardData;
    }

    public void setStarBeatPoolRewardData(StarBeatPoolRewardData starBeatPoolRewardData) {
        this.starBeatPoolRewardData = starBeatPoolRewardData;
    }

    public int getNowStar() {
        return nowStar;
    }

    public void setNowStar(int nowStar) {
        this.nowStar = nowStar;
    }

    public List<AwardBean> getAwardBeanList() {
        return awardBeanList;
    }

    public void setAwardBeanList(List<AwardBean> awardBeanList) {
        this.awardBeanList = awardBeanList;
    }

    public Map<String, Integer> getLowSourceMap() {
        return lowSourceMap;
    }

    public void setLowSourceMap(Map<String, Integer> lowSourceMap) {
        this.lowSourceMap = lowSourceMap;
    }

    public Map<String, Integer> getHighSourceMap() {
        return highSourceMap;
    }

    public void setHighSourceMap(Map<String, Integer> highSourceMap) {
        this.highSourceMap = highSourceMap;
    }

    public Map<String, Integer> getAllSourceMap() {
        return allSourceMap;
    }

    public void setAllSourceMap(Map<String, Integer> allSourceMap) {
        this.allSourceMap = allSourceMap;
    }

    public Map<String, CarnivalGameData.PrizeConfig> getAllPrizeConfig() {
        return allPrizeConfig;
    }

    public void setAllPrizeConfig(Map<String, CarnivalGameData.PrizeConfig> allPrizeConfig) {
        this.allPrizeConfig = allPrizeConfig;
    }

    public Map<String, List<CarnivalGameData.PrizeConfig>> getAllBoxListMap() {
        return allBoxListMap;
    }

    public void setAllBoxListMap(Map<String, List<CarnivalGameData.PrizeConfig>> allBoxListMap) {
        this.allBoxListMap = allBoxListMap;
    }

    public int getAddTicket() {
        return addTicket;
    }

    public void setAddTicket(int addTicket) {
        this.addTicket = addTicket;
    }

    public CarnivalGameData.PrizeConfig getMaxPrizeConfig() {
        return maxPrizeConfig;
    }

    public void setMaxPrizeConfig(CarnivalGameData.PrizeConfig maxPrizeConfig) {
        this.maxPrizeConfig = maxPrizeConfig;
    }

    @Override
    public String toString() {
        return JSON.toJSONString(this);
    }
}
