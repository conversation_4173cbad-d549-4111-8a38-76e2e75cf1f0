package com.quhong.data;

import com.quhong.elasticsearch.HomeVisitorEs;
import org.springframework.data.annotation.Id;
import org.springframework.data.elasticsearch.annotations.Document;

import java.io.Serializable;
import java.util.List;


public class HomeVisitorList {

   private  long totalVisitor;

    private List<HomeVisitorEs> esList ;


    public long getTotalVisitor() {
        return totalVisitor;
    }

    public void setTotalVisitor(long totalVisitor) {
        this.totalVisitor = totalVisitor;
    }

    public List<HomeVisitorEs> getEsList() {
        return esList;
    }

    public void setEsList(List<HomeVisitorEs> esList) {
        this.esList = esList;
    }
}
