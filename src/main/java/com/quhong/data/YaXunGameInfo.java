package com.quhong.data;

/**
 * <AUTHOR>
 * @date 2023/8/9
 */
public class YaXunGameInfo {

    private String gameId;

    private String gameName;

    private int reduceBeansActType;

    private String reduceBeansTitle;

    private int incBeansActType;

    private String incBeansTitle;

    public YaXunGameInfo() {
    }

    public YaXunGameInfo(String gameId, String gameName, int reduceBeansActType, String reduceBeansTitle, int incBeansActType, String incBeansTitle) {
        this.gameId = gameId;
        this.gameName = gameName;
        this.reduceBeansActType = reduceBeansActType;
        this.reduceBeansTitle = reduceBeansTitle;
        this.incBeansActType = incBeansActType;
        this.incBeansTitle = incBeansTitle;
    }

    public int getReduceBeansActType() {
        return reduceBeansActType;
    }

    public void setReduceBeansActType(int reduceBeansActType) {
        this.reduceBeansActType = reduceBeansActType;
    }

    public String getReduceBeansTitle() {
        return reduceBeansTitle;
    }

    public void setReduceBeansTitle(String reduceBeansTitle) {
        this.reduceBeansTitle = reduceBeansTitle;
    }

    public int getIncBeansActType() {
        return incBeansActType;
    }

    public void setIncBeansActType(int incBeansActType) {
        this.incBeansActType = incBeansActType;
    }

    public String getIncBeansTitle() {
        return incBeansTitle;
    }

    public void setIncBeansTitle(String incBeansTitle) {
        this.incBeansTitle = incBeansTitle;
    }

    public String getGameId() {
        return gameId;
    }

    public void setGameId(String gameId) {
        this.gameId = gameId;
    }

    public String getGameName() {
        return gameName;
    }

    public void setGameName(String gameName) {
        this.gameName = gameName;
    }
}
