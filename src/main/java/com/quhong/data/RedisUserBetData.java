package com.quhong.data;

import java.util.List;

public class RedisUserBetData {
    private String uid;
    private List<BetBean> betBeanList;

    public String getUid() {
        return uid;
    }

    public void setUid(String uid) {
        this.uid = uid;
    }

    public List<BetBean> getBetBeanList() {
        return betBeanList;
    }

    public void setBetBeanList(List<BetBean> betBeanList) {
        this.betBeanList = betBeanList;
    }

    public static class BetBean {
        private int type;
        private int amount;

        public BetBean() {
        }

        public BetBean(int type, int amount) {
            this.type = type;
            this.amount = amount;
        }

        public int getType() {
            return type;
        }

        public void setType(int type) {
            this.type = type;
        }

        public int getAmount() {
            return amount;
        }

        public void setAmount(int amount) {
            this.amount = amount;
        }

        @Override
        public String toString() {
            return "BetBean{" +
                    "type=" + type +
                    ", amount=" + amount +
                    '}';
        }
    }
}
