package com.quhong.data.vo;

import com.quhong.data.RidData;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2022/7/12
 */
public class RoomInfoVO {
    private RidData ridInfo; // 新版本rid对象
    private String aid; // 房主uid
    private int rid; // 用户rid
    private String name; // 房主名
    private String head; // 房主头像
    private String micFrame; // 房主麦位框
    private int gender; // 房主性别
    private int age; // 房主年龄
    private int vipLevel; // 房主vip等级
    private int identify; // vip信息
    private int activeLevel; // 活跃等级
    private int wealthLevel; // 财富等级
    private int charmLevel; // 魅力等级
    private List<String> badgeList; // 佩戴的勋章
    private int isBeautifulRid; // 房主是否是靓号
    private String alphaRid; // 房主靓号id
    private int alphaType; // 房主靓号类型
    private int members; // 房间会员数量

    private int tag; // 房间标签，0为默认值，表示从未设置过房间标签
    private String tagName; // 房间标签名
    private String topic; //房间主题
    private String announce; // 房间公告
    private int privi; // 1所有人都可以上麦 2要花钱才能上
    private int fee; // 房间会员费用
    private int feetype = 2;
    private int utype; //
    private int comp; // 是否为竞赛房
    private int pwd; // 房间密码
    private String roomId; // 房间id
    private String country; // 房间国家
    private int online;   // 当前在线人数
    private String room_name; // 房间名
    private String room_head; // 房间头像
    private int room_type; // 房间类型
    private int room_pk; //1为默认开启状态，2 为不开启状态
    private int chat_locked; // 房间是否禁止公屏聊天 1为禁止
    private int pic_locked; // 房间是否禁止发图片 1为禁止
    private int textLimit; // 房间发消息，限制用户等级
    private List<Integer> memberFeeList; //会员费用可选择钻石数

    public RidData getRidInfo() {
        return ridInfo;
    }

    public void setRidInfo(RidData ridInfo) {
        this.ridInfo = ridInfo;
    }

    public String getTopic() {
        return topic;
    }

    public void setTopic(String topic) {
        this.topic = topic;
    }

    public String getAnnounce() {
        return announce;
    }

    public void setAnnounce(String announce) {
        this.announce = announce;
    }

    public int getPrivi() {
        return privi;
    }

    public void setPrivi(int privi) {
        this.privi = privi;
    }

    public int getFee() {
        return fee;
    }

    public void setFee(int fee) {
        this.fee = fee;
    }

    public int getFeetype() {
        return feetype;
    }

    public void setFeetype(int feetype) {
        this.feetype = feetype;
    }

    public int getComp() {
        return comp;
    }

    public void setComp(int comp) {
        this.comp = comp;
    }

    public int getPwd() {
        return pwd;
    }

    public void setPwd(int pwd) {
        this.pwd = pwd;
    }

    public String getRoomId() {
        return roomId;
    }

    public void setRoomId(String roomId) {
        this.roomId = roomId;
    }

    public String getCountry() {
        return country;
    }

    public void setCountry(String country) {
        this.country = country;
    }

    public int getOnline() {
        return online;
    }

    public void setOnline(int online) {
        this.online = online;
    }

    public String getRoom_name() {
        return room_name;
    }

    public void setRoom_name(String room_name) {
        this.room_name = room_name;
    }

    public String getRoom_head() {
        return room_head;
    }

    public void setRoom_head(String room_head) {
        this.room_head = room_head;
    }

    public int getRoom_type() {
        return room_type;
    }

    public void setRoom_type(int room_type) {
        this.room_type = room_type;
    }

    public int getUtype() {
        return utype;
    }

    public void setUtype(int utype) {
        this.utype = utype;
    }

    public String getAid() {
        return aid;
    }

    public void setAid(String aid) {
        this.aid = aid;
    }

    public int getRid() {
        return rid;
    }

    public void setRid(int rid) {
        this.rid = rid;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getHead() {
        return head;
    }

    public void setHead(String head) {
        this.head = head;
    }

    public String getMicFrame() {
        return micFrame;
    }

    public void setMicFrame(String micFrame) {
        this.micFrame = micFrame;
    }

    public int getGender() {
        return gender;
    }

    public void setGender(int gender) {
        this.gender = gender;
    }

    public int getAge() {
        return age;
    }

    public void setAge(int age) {
        this.age = age;
    }

    public int getVipLevel() {
        return vipLevel;
    }

    public void setVipLevel(int vipLevel) {
        this.vipLevel = vipLevel;
    }

    public int getTag() {
        return tag;
    }

    public void setTag(int tag) {
        this.tag = tag;
    }

    public int getIdentify() {
        return identify;
    }

    public void setIdentify(int identify) {
        this.identify = identify;
    }

    public List<String> getBadgeList() {
        return badgeList;
    }

    public void setBadgeList(List<String> badgeList) {
        this.badgeList = badgeList;
    }

    public int getIsBeautifulRid() {
        return isBeautifulRid;
    }

    public void setIsBeautifulRid(int isBeautifulRid) {
        this.isBeautifulRid = isBeautifulRid;
    }

    public String getAlphaRid() {
        return alphaRid;
    }

    public void setAlphaRid(String alphaRid) {
        this.alphaRid = alphaRid;
    }

    public int getAlphaType() {
        return alphaType;
    }

    public void setAlphaType(int alphaType) {
        this.alphaType = alphaType;
    }

    public int getMembers() {
        return members;
    }

    public void setMembers(int members) {
        this.members = members;
    }

    public int getRoom_pk() {
        return room_pk;
    }

    public void setRoom_pk(int room_pk) {
        this.room_pk = room_pk;
    }

    public int getChat_locked() {
        return chat_locked;
    }

    public void setChat_locked(int chat_locked) {
        this.chat_locked = chat_locked;
    }

    public int getPic_locked() {
        return pic_locked;
    }

    public void setPic_locked(int pic_locked) {
        this.pic_locked = pic_locked;
    }

    public String getTagName() {
        return tagName;
    }

    public void setTagName(String tagName) {
        this.tagName = tagName;
    }

    public int getTextLimit() {
        return textLimit;
    }

    public void setTextLimit(int textLimit) {
        this.textLimit = textLimit;
    }

    public int getActiveLevel() {
        return activeLevel;
    }

    public void setActiveLevel(int activeLevel) {
        this.activeLevel = activeLevel;
    }

    public int getWealthLevel() {
        return wealthLevel;
    }

    public void setWealthLevel(int wealthLevel) {
        this.wealthLevel = wealthLevel;
    }

    public int getCharmLevel() {
        return charmLevel;
    }

    public void setCharmLevel(int charmLevel) {
        this.charmLevel = charmLevel;
    }

    public List<Integer> getMemberFeeList() {
        return memberFeeList;
    }

    public void setMemberFeeList(List<Integer> memberFeeList) {
        this.memberFeeList = memberFeeList;
    }
}
