package com.quhong.data.vo;

import java.util.List;

/**
 * <AUTHOR>
 */
public class BadgeListVO {
    private List<BadgeInfo> list;
    public static class BadgeInfo{
        private int bid;
        private String icon;
        private String name;
        private String desc;
        private int status;
        private String svga_icon;

        public int getBid() {
            return bid;
        }

        public void setBid(int bid) {
            this.bid = bid;
        }

        public String getIcon() {
            return icon;
        }

        public void setIcon(String icon) {
            this.icon = icon;
        }

        public String getName() {
            return name;
        }

        public void setName(String name) {
            this.name = name;
        }

        public String getDesc() {
            return desc;
        }

        public void setDesc(String desc) {
            this.desc = desc;
        }

        public int getStatus() {
            return status;
        }

        public void setStatus(int status) {
            this.status = status;
        }

        public String getSvga_icon() {
            return svga_icon;
        }

        public void setSvga_icon(String svga_icon) {
            this.svga_icon = svga_icon;
        }
    }

    public List<BadgeInfo> getList() {
        return list;
    }

    public void setList(List<BadgeInfo> list) {
        this.list = list;
    }
}
