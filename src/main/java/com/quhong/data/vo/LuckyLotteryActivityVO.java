package com.quhong.data.vo;

import com.quhong.mongo.data.LuckyLotteryActivity;

import java.util.List;

public class LuckyLotteryActivityVO {
    private String acNameEn; // 英文活动名
    private String acNameAr; // 阿语活动名
    private Integer startTime; // 活动开始时间
    private Integer endTime; // 活动结束时间

    private Integer joinNums; // 参与人数

    private Long remainNums; // 钻石、心心、免费次数剩余数量
    private LuckyLotteryActivity.ActivityConfig activityConfig; // 活动配置
    private LuckyLotteryActivity.PartakeConfig partakeConfig; // 参与条件配置
    private List<LuckyLotteryActivity.RewardConfigDetail> rewardConfigList; // 奖励配置

    public String getAcNameEn() {
        return acNameEn;
    }

    public void setAcNameEn(String acNameEn) {
        this.acNameEn = acNameEn;
    }

    public String getAcNameAr() {
        return acNameAr;
    }

    public void setAcNameAr(String acNameAr) {
        this.acNameAr = acNameAr;
    }

    public Integer getStartTime() {
        return startTime;
    }

    public void setStartTime(Integer startTime) {
        this.startTime = startTime;
    }

    public Integer getEndTime() {
        return endTime;
    }

    public void setEndTime(Integer endTime) {
        this.endTime = endTime;
    }

    public Integer getJoinNums() {
        return joinNums;
    }

    public void setJoinNums(Integer joinNums) {
        this.joinNums = joinNums;
    }

    public Long getRemainNums() {
        return remainNums;
    }

    public void setRemainNums(Long remainNums) {
        this.remainNums = remainNums;
    }

    public LuckyLotteryActivity.ActivityConfig getActivityConfig() {
        return activityConfig;
    }

    public void setActivityConfig(LuckyLotteryActivity.ActivityConfig activityConfig) {
        this.activityConfig = activityConfig;
    }

    public LuckyLotteryActivity.PartakeConfig getPartakeConfig() {
        return partakeConfig;
    }

    public void setPartakeConfig(LuckyLotteryActivity.PartakeConfig partakeConfig) {
        this.partakeConfig = partakeConfig;
    }

    public List<LuckyLotteryActivity.RewardConfigDetail> getRewardConfigList() {
        return rewardConfigList;
    }

    public void setRewardConfigList(List<LuckyLotteryActivity.RewardConfigDetail> rewardConfigList) {
        this.rewardConfigList = rewardConfigList;
    }
}
