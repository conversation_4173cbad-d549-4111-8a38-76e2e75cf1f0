package com.quhong.data.vo;

import com.quhong.service.data.RoomBanner;
import com.quhong.service.data.RoomFunction;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2022/8/2
 */
public class RoomBannerVO {
    private String guess_icon;
    private int dice2_option;
    private int conquer;
    private int ludo; // ludo 游戏开关
    private int umo_switch; // UMO游戏开关
    private int monster_crush_switch; // 消消乐游戏开关
    private int domino_switch; // 多米诺游戏开关
    private int lucky_wheel_switch; // 幸运转盘开关
    private int lucky_bag_switch; // 红包开关，新版字段
    private int lucky_box_switch; // 红包开关，旧版字段
    private int turntable;
    private int game_status; // 房间游戏状态 0没有游戏 1碰碰 2ludo 3UMO
    private int finger_guess;
    private int lucky_box;
    private int luckyNumFunction; // 1: 第一个版本   2： 第二个版本
    private int rocket_switch; // 房间火箭开关
    private LuckyNumConfig luckyNumV2Config; // 幸运数字V2相关配置
    private int roomEventNum; // 房间活动数量
    private int room_vote; // 房间投票开关
    private int teamPk; // 团战pk开关

    private List<RoomBanner> list;
    private List<RoomBanner> sidebarBannerList; // 房间侧边栏banner
    private List<RoomBanner> hotGameList; // 热门游戏
    private List<RoomBanner> advancedGameList; // 高级场游戏
    private List<RoomBanner> functionList; // 互动功能

    public static class LuckyNumConfig {
        private int enableSetting;
        private int luckyNumCost;
        private int luckyNumRange;
        private int luckyNumSwitch;
        private int luckyNumData;
        private int luckyNumAdmin;

        public int getEnableSetting() {
            return enableSetting;
        }

        public void setEnableSetting(int enableSetting) {
            this.enableSetting = enableSetting;
        }

        public int getLuckyNumCost() {
            return luckyNumCost;
        }

        public void setLuckyNumCost(int luckyNumCost) {
            this.luckyNumCost = luckyNumCost;
        }

        public int getLuckyNumRange() {
            return luckyNumRange;
        }

        public void setLuckyNumRange(int luckyNumRange) {
            this.luckyNumRange = luckyNumRange;
        }

        public int getLuckyNumAdmin() {
            return luckyNumAdmin;
        }

        public void setLuckyNumAdmin(int luckyNumAdmin) {
            this.luckyNumAdmin = luckyNumAdmin;
        }

        public int getLuckyNumSwitch() {
            return luckyNumSwitch;
        }

        public void setLuckyNumSwitch(int luckyNumSwitch) {
            this.luckyNumSwitch = luckyNumSwitch;
        }

        public int getLuckyNumData() {
            return luckyNumData;
        }

        public void setLuckyNumData(int luckyNumData) {
            this.luckyNumData = luckyNumData;
        }
    }

    public String getGuess_icon() {
        return guess_icon;
    }

    public void setGuess_icon(String guess_icon) {
        this.guess_icon = guess_icon;
    }

    public int getDice2_option() {
        return dice2_option;
    }

    public void setDice2_option(int dice2_option) {
        this.dice2_option = dice2_option;
    }

    public int getConquer() {
        return conquer;
    }

    public void setConquer(int conquer) {
        this.conquer = conquer;
    }

    public int getLudo() {
        return ludo;
    }

    public void setLudo(int ludo) {
        this.ludo = ludo;
    }

    public int getUmo_switch() {
        return umo_switch;
    }

    public void setUmo_switch(int umo_switch) {
        this.umo_switch = umo_switch;
    }

    public int getLucky_wheel_switch() {
        return lucky_wheel_switch;
    }

    public void setLucky_wheel_switch(int lucky_wheel_switch) {
        this.lucky_wheel_switch = lucky_wheel_switch;
    }

    public int getLucky_bag_switch() {
        return lucky_bag_switch;
    }

    public void setLucky_bag_switch(int lucky_bag_switch) {
        this.lucky_bag_switch = lucky_bag_switch;
    }

    public int getLucky_box_switch() {
        return lucky_box_switch;
    }

    public void setLucky_box_switch(int lucky_box_switch) {
        this.lucky_box_switch = lucky_box_switch;
    }

    public int getTurntable() {
        return turntable;
    }

    public void setTurntable(int turntable) {
        this.turntable = turntable;
    }

    public int getGame_status() {
        return game_status;
    }

    public void setGame_status(int game_status) {
        this.game_status = game_status;
    }

    public int getFinger_guess() {
        return finger_guess;
    }

    public void setFinger_guess(int finger_guess) {
        this.finger_guess = finger_guess;
    }

    public int getLucky_box() {
        return lucky_box;
    }

    public void setLucky_box(int lucky_box) {
        this.lucky_box = lucky_box;
    }

    public int getLuckyNumFunction() {
        return luckyNumFunction;
    }

    public void setLuckyNumFunction(int luckyNumFunction) {
        this.luckyNumFunction = luckyNumFunction;
    }

    public int getRocket_switch() {
        return rocket_switch;
    }

    public void setRocket_switch(int rocket_switch) {
        this.rocket_switch = rocket_switch;
    }

    public LuckyNumConfig getLuckyNumV2Config() {
        return luckyNumV2Config;
    }

    public void setLuckyNumV2Config(LuckyNumConfig luckyNumV2Config) {
        this.luckyNumV2Config = luckyNumV2Config;
    }

    public int getRoomEventNum() {
        return roomEventNum;
    }

    public void setRoomEventNum(int roomEventNum) {
        this.roomEventNum = roomEventNum;
    }

    public int getRoom_vote() {
        return room_vote;
    }

    public void setRoom_vote(int room_vote) {
        this.room_vote = room_vote;
    }

    public int getTeamPk() {
        return teamPk;
    }

    public void setTeamPk(int teamPk) {
        this.teamPk = teamPk;
    }

    public List<RoomBanner> getList() {
        return list;
    }

    public void setList(List<RoomBanner> list) {
        this.list = list;
    }

    public List<RoomBanner> getSidebarBannerList() {
        return sidebarBannerList;
    }

    public void setSidebarBannerList(List<RoomBanner> sidebarBannerList) {
        this.sidebarBannerList = sidebarBannerList;
    }

    public int getMonster_crush_switch() {
        return monster_crush_switch;
    }

    public void setMonster_crush_switch(int monster_crush_switch) {
        this.monster_crush_switch = monster_crush_switch;
    }

    public int getDomino_switch() {
        return domino_switch;
    }

    public void setDomino_switch(int domino_switch) {
        this.domino_switch = domino_switch;
    }

    public List<RoomBanner> getHotGameList() {
        return hotGameList;
    }

    public void setHotGameList(List<RoomBanner> hotGameList) {
        this.hotGameList = hotGameList;
    }

    public List<RoomBanner> getFunctionList() {
        return functionList;
    }

    public void setFunctionList(List<RoomBanner> functionList) {
        this.functionList = functionList;
    }

    public List<RoomBanner> getAdvancedGameList() {
        return advancedGameList;
    }

    public void setAdvancedGameList(List<RoomBanner> advancedGameList) {
        this.advancedGameList = advancedGameList;
    }
}
