package com.quhong.data.vo;


import java.util.List;

public class CamelRankingVO {
    // 配置类排行榜数据
    private Integer startTime;
    private Integer endTime;
    private String currentDay;
    private Integer dailySendNum;
    private List<DailyData> dailyDataList;
    private List<GloryConfig> allFinishList;
    private List<OtherRankingListVO> rankingList;
    private CamelMyRankVO myRank;
    private Integer totalLength;

    public static class DailyData {
        private String strDate;
        private long score;

        public DailyData(String strDate, long score) {
            this.strDate = strDate;
            this.score = score;
        }

        public String getStrDate() {
            return strDate;
        }

        public void setStrDate(String strDate) {
            this.strDate = strDate;
        }

        public long getScore() {
            return score;
        }

        public void setScore(long score) {
            this.score = score;
        }
    }

    // 荣誉堂完成全部节点
    public static class GloryConfig{
        private String dateStr;
        private List<OtherRankingListVO> finishRankList;

        public String getDateStr() {
            return dateStr;
        }

        public void setDateStr(String dateStr) {
            this.dateStr = dateStr;
        }

        public List<OtherRankingListVO> getFinishRankList() {
            return finishRankList;
        }

        public void setFinishRankList(List<OtherRankingListVO> finishRankList) {
            this.finishRankList = finishRankList;
        }
    }

    public List<GloryConfig> getAllFinishList() {
        return allFinishList;
    }

    public void setAllFinishList(List<GloryConfig> allFinishList) {
        this.allFinishList = allFinishList;
    }

    public List<OtherRankingListVO> getRankingList() {
        return rankingList;
    }

    public void setRankingList(List<OtherRankingListVO> rankingList) {
        this.rankingList = rankingList;
    }

    public CamelMyRankVO getMyRank() {
        return myRank;
    }

    public void setMyRank(CamelMyRankVO myRank) {
        this.myRank = myRank;
    }

    public String getCurrentDay() {
        return currentDay;
    }

    public void setCurrentDay(String currentDay) {
        this.currentDay = currentDay;
    }

    public Integer getDailySendNum() {
        return dailySendNum;
    }

    public void setDailySendNum(Integer dailySendNum) {
        this.dailySendNum = dailySendNum;
    }

    public Integer getStartTime() {
        return startTime;
    }

    public void setStartTime(Integer startTime) {
        this.startTime = startTime;
    }

    public Integer getEndTime() {
        return endTime;
    }

    public void setEndTime(Integer endTime) {
        this.endTime = endTime;
    }

    public Integer getTotalLength() {
        return totalLength;
    }

    public void setTotalLength(Integer totalLength) {
        this.totalLength = totalLength;
    }

    public List<DailyData> getDailyDataList() {
        return dailyDataList;
    }

    public void setDailyDataList(List<DailyData> dailyDataList) {
        this.dailyDataList = dailyDataList;
    }
}
