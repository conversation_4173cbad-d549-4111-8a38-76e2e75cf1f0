package com.quhong.data.vo;

import com.quhong.room.RoomTags;

import java.util.List;

public class RoomSettingsVO {

    private String name; // 房间名字
    private String head; // 房间头像
    private String announce; // 房间公告
    private int privi; // 1所有人都可以上麦 2要花钱才能上
    private int fee; // 房间会员费用
    private int feetype;  //  房间会员费用类型，金币或钻石
    private int tag; // 房间标签，0为默认值，表示从未设置过房间标签
    private int room_locked; // 房间是否上锁 1未上锁
    private int chat_locked; // 房间是否禁止公屏聊天 1为禁止
    private int pic_locked; // 房间是否禁止发图片 1为禁止
    private int room_pk; // 1为默认开启状态，2 为不开启状态
    private int greet_switch; // 欢迎语是否开启 1开启 2 关闭
    private List<RoomTags.RoomTag> tagList; // 房间tag列表

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getHead() {
        return head;
    }

    public void setHead(String head) {
        this.head = head;
    }

    public String getAnnounce() {
        return announce;
    }

    public void setAnnounce(String announce) {
        this.announce = announce;
    }

    public int getPrivi() {
        return privi;
    }

    public void setPrivi(int privi) {
        this.privi = privi;
    }

    public int getFee() {
        return fee;
    }

    public void setFee(int fee) {
        this.fee = fee;
    }

    public int getFeetype() {
        return feetype;
    }

    public void setFeetype(int feetype) {
        this.feetype = feetype;
    }

    public int getTag() {
        return tag;
    }

    public void setTag(int tag) {
        this.tag = tag;
    }

    public int getRoom_locked() {
        return room_locked;
    }

    public void setRoom_locked(int room_locked) {
        this.room_locked = room_locked;
    }

    public int getChat_locked() {
        return chat_locked;
    }

    public void setChat_locked(int chat_locked) {
        this.chat_locked = chat_locked;
    }

    public int getPic_locked() {
        return pic_locked;
    }

    public void setPic_locked(int pic_locked) {
        this.pic_locked = pic_locked;
    }

    public int getRoom_pk() {
        return room_pk;
    }

    public void setRoom_pk(int room_pk) {
        this.room_pk = room_pk;
    }

    public int getGreet_switch() {
        return greet_switch;
    }

    public void setGreet_switch(int greet_switch) {
        this.greet_switch = greet_switch;
    }

    public List<RoomTags.RoomTag> getTagList() {
        return tagList;
    }

    public void setTagList(List<RoomTags.RoomTag> tagList) {
        this.tagList = tagList;
    }
}
