package com.quhong.data.vo;

import com.alibaba.fastjson.annotation.JSONField;

import java.util.List;

public class GiftPanelVO {
    private int rid; // 礼物id
    private String gicon; // 礼物图标
    private ZipInfoVO zipInfo; // 礼物资源
    private int gatype; // 前端可能会通过该字段来展示动画
    private int price; // 礼物价格
    private int gtype; // 1: diamond 2: silver 3:vip
    private int comp; // ??
    private int gift_num; // vip礼物数量，背包礼物才有
    private long gtime; // 礼物播放时长
    private String giftNameEn; // 礼物名英语
    private String gname; // 礼物名字
    private String gnamear; // 阿语礼物名字
    private List<Integer> tags; // 角标
    private int ctime; // 客户端通过该字段判断new标签
    private int blindBox; // 是否盲盒礼物
    private List<Integer> giftNumOptions; // 礼物数量选择
    private Integer gstatus;
    @JSONField(name = "fusion_name")
    private String fusionName;
    @JSONField(name = "fusion_head")
    private String fusionHead;
    @JSONField(name = "fusion_id")
    private String fusionId;
    private String g_country; // 专属礼物所属国家
    private int starGift; // 名人礼物
    private int starGiftType; // 名人礼物类型 3用户专属 4家族专属
    private String starGiftFromAid; // 名人礼物所属用户uid或家族id

    public int getRid() {
        return rid;
    }

    public void setRid(int rid) {
        this.rid = rid;
    }

    public String getGicon() {
        return gicon;
    }

    public void setGicon(String gicon) {
        this.gicon = gicon;
    }

    public ZipInfoVO getZipInfo() {
        return zipInfo;
    }

    public void setZipInfo(ZipInfoVO zipInfo) {
        this.zipInfo = zipInfo;
    }

    public int getGatype() {
        return gatype;
    }

    public void setGatype(int gatype) {
        this.gatype = gatype;
    }

    public List<Integer> getTags() {
        return tags;
    }

    public void setTags(List<Integer> tags) {
        this.tags = tags;
    }

    public int getPrice() {
        return price;
    }

    public void setPrice(int price) {
        this.price = price;
    }

    public int getGtype() {
        return gtype;
    }

    public void setGtype(int gtype) {
        this.gtype = gtype;
    }

    public int getComp() {
        return comp;
    }

    public void setComp(int comp) {
        this.comp = comp;
    }

    public int getGift_num() {
        return gift_num;
    }

    public void setGift_num(int gift_num) {
        this.gift_num = gift_num;
    }

    public long getGtime() {
        return gtime;
    }

    public void setGtime(long gtime) {
        this.gtime = gtime;
    }

    public String getGname() {
        return gname;
    }

    public void setGname(String gname) {
        this.gname = gname;
    }

    public String getGnamear() {
        return gnamear;
    }

    public void setGnamear(String gnamear) {
        this.gnamear = gnamear;
    }

    public int getCtime() {
        return ctime;
    }

    public void setCtime(int ctime) {
        this.ctime = ctime;
    }

    public int getBlindBox() {
        return blindBox;
    }

    public void setBlindBox(int blindBox) {
        this.blindBox = blindBox;
    }

    public List<Integer> getGiftNumOptions() {
        return giftNumOptions;
    }

    public void setGiftNumOptions(List<Integer> giftNumOptions) {
        this.giftNumOptions = giftNumOptions;
    }

    public Integer getGstatus() {
        return gstatus;
    }

    public void setGstatus(Integer gstatus) {
        this.gstatus = gstatus;
    }

    public String getFusionName() {
        return fusionName;
    }

    public void setFusionName(String fusionName) {
        this.fusionName = fusionName;
    }

    public String getFusionHead() {
        return fusionHead;
    }

    public void setFusionHead(String fusionHead) {
        this.fusionHead = fusionHead;
    }

    public String getFusionId() {
        return fusionId;
    }

    public void setFusionId(String fusionId) {
        this.fusionId = fusionId;
    }

    public String getGiftNameEn() {
        return giftNameEn;
    }

    public void setGiftNameEn(String giftNameEn) {
        this.giftNameEn = giftNameEn;
    }

    public String getG_country() {
        return g_country;
    }

    public void setG_country(String g_country) {
        this.g_country = g_country;
    }

    public String getStarGiftFromAid() {
        return starGiftFromAid;
    }

    public void setStarGiftFromAid(String starGiftFromAid) {
        this.starGiftFromAid = starGiftFromAid;
    }

    public int getStarGiftType() {
        return starGiftType;
    }

    public void setStarGiftType(int starGiftType) {
        this.starGiftType = starGiftType;
    }

    public int getStarGift() {
        return starGift;
    }

    public void setStarGift(int starGift) {
        this.starGift = starGift;
    }
}
