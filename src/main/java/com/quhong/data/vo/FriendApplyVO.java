package com.quhong.data.vo;

import com.quhong.data.UserBasicInfo;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2022/6/7
 */
public class FriendApplyVO {
    /**
     * 好友申请列表
     */
    private List<UserInfo> list;
    /**
     * 下一页页数
     */
    private String nextUrl;

    public List<UserInfo> getList() {
        return list;
    }

    public void setList(List<UserInfo> list) {
        this.list = list;
    }

    public String getNextUrl() {
        return nextUrl;
    }

    public void setNextUrl(String nextUrl) {
        this.nextUrl = nextUrl;
    }

    public static class UserInfo extends UserBasicInfo {

        private String os;
        private int opt_type;  // 0未处理 1已经同意
        private int ctime;
        private String msg;
        private int identify;

        public String getOs() { return os; }

        public void setOs(String os) { this.os = os; }

        public int getOpt_type() { return opt_type; }

        public void setOpt_type(int opt_type) { this.opt_type = opt_type; }

        public int getCtime() {
            return ctime;
        }

        public void setCtime(int ctime) {
            this.ctime = ctime;
        }

        public String getMsg() {
            return msg;
        }

        public void setMsg(String msg) {
            this.msg = msg;
        }

        public int getIdentify() {
            return identify;
        }

        public void setIdentify(int identify) {
            this.identify = identify;
        }
    }
}
