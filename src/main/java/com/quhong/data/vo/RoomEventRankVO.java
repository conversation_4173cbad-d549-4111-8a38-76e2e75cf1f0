package com.quhong.data.vo;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2022/12/9
 */
public class RoomEventRankVO {

    /**
     * 活动期间发礼物人数
     */
    private Integer senders;

    /**
     * 活动期间礼物消耗总钻石，钻石数超过千显示K，保留1位小数
     */
    private String contribution;

    /**
     * 排行榜类型 0发礼物钻石榜 1收礼物魅力值榜
     */
    private Integer rankingType;

    /**
     * 送礼物的排行榜
     */
    private List<EventActorVO> rankList;

    /**
     * 下一页
     */
    private String nextUrl;

    public Integer getSenders() {
        return senders;
    }

    public void setSenders(Integer senders) {
        this.senders = senders;
    }

    public String getContribution() {
        return contribution;
    }

    public Integer getRankingType() {
        return rankingType;
    }

    public void setRankingType(Integer rankingType) {
        this.rankingType = rankingType;
    }

    public void setContribution(String contribution) {
        this.contribution = contribution;
    }

    public List<EventActorVO> getRankList() {
        return rankList;
    }

    public void setRankList(List<EventActorVO> rankList) {
        this.rankList = rankList;
    }

    public String getNextUrl() {
        return nextUrl;
    }

    public void setNextUrl(String nextUrl) {
        this.nextUrl = nextUrl;
    }
}
