package com.quhong.data.vo;

import com.quhong.data.UserBasicInfo;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2022/6/17
 */
public class MergeListVO {

    private int listType;
    /**
     * 好友列表
     */
    private List<UserInfo> list;
    /**
     * 数量
     */
    private int num;
    /**
     * 下一页页数
     */
    private String nextUrl;
    private int friendLimit; // 好友上限数量

    public static class UserInfo extends UserBasicInfo {

        private int os;
        private int inRoom;
        /**
         * 0相互未关注 1已关注,对方未关注 2未关注,对方已关注 3相互都关注了
         */
        private int followStatus;

        private List<String> badgeList;

        public int getOs() {
            return os;
        }

        public void setOs(int os) {
            this.os = os;
        }

        public int getInRoom() {
            return inRoom;
        }

        public void setInRoom(int inRoom) {
            this.inRoom = inRoom;
        }

        public List<String> getBadgeList() {
            return badgeList;
        }

        public void setBadgeList(List<String> badgeList) {
            this.badgeList = badgeList;
        }

        public int getFollowStatus() {
            return followStatus;
        }

        public void setFollowStatus(int followStatus) {
            this.followStatus = followStatus;
        }
    }

    public int getListType() {
        return listType;
    }

    public void setListType(int listType) {
        this.listType = listType;
    }

    public List<UserInfo> getList() {
        return list;
    }

    public void setList(List<UserInfo> list) {
        this.list = list;
    }

    public String getNextUrl() {
        return nextUrl;
    }

    public void setNextUrl(String nextUrl) {
        this.nextUrl = nextUrl;
    }

    public int getNum() {
        return num;
    }

    public void setNum(int num) {
        this.num = num;
    }

    public int getFriendLimit() {
        return friendLimit;
    }

    public void setFriendLimit(int friendLimit) {
        this.friendLimit = friendLimit;
    }
}
