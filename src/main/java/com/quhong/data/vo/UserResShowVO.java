package com.quhong.data.vo;

import com.alibaba.fastjson.annotation.JSONField;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2024/6/24
 */
public class UserResShowVO {

    private int sumNum;  // 总数量

    private List<Resource> list;

    private String nextUrl;

    public UserResShowVO() {
    }

    public UserResShowVO(int sumNum, List<Resource> list, String nextUrl) {
        this.sumNum = sumNum;
        this.list = list;
        this.nextUrl = nextUrl;
    }

    public static class Resource {
        private int resId; // 资源id
        private String icon; // 资源图标
        private String name; // 资源名称
        private String resUrl; // 资源url
        private String resMd5; // 资源md5值
        private Integer sourceType; // 资源类型
        private int resLevel; // 资源等级 0普通 1珍贵 2稀有 3罕见 4史诗
        private String ownDesc; // 占比描述
        @JSONField(serialize = false)
        private long intGetTime; // 获取时间

        private String smallIcon; // 小图标
        private String desc; // 描述
        private String getTime; // 勋章获得时间
        private String getTimeDesc; // 勋章获得时间描述

        public int getResId() {
            return resId;
        }

        public void setResId(int resId) {
            this.resId = resId;
        }

        public String getIcon() {
            return icon;
        }

        public void setIcon(String icon) {
            this.icon = icon;
        }

        public String getName() {
            return name;
        }

        public void setName(String name) {
            this.name = name;
        }

        public String getResUrl() {
            return resUrl;
        }

        public void setResUrl(String resUrl) {
            this.resUrl = resUrl;
        }

        public int getResLevel() {
            return resLevel;
        }

        public void setResLevel(int resLevel) {
            this.resLevel = resLevel;
        }

        public String getOwnDesc() {
            return ownDesc;
        }

        public void setOwnDesc(String ownDesc) {
            this.ownDesc = ownDesc;
        }

        public long getIntGetTime() {
            return intGetTime;
        }

        public void setIntGetTime(long intGetTime) {
            this.intGetTime = intGetTime;
        }

        public String getSmallIcon() {
            return smallIcon;
        }

        public void setSmallIcon(String smallIcon) {
            this.smallIcon = smallIcon;
        }

        public String getDesc() {
            return desc;
        }

        public void setDesc(String desc) {
            this.desc = desc;
        }

        public String getGetTime() {
            return getTime;
        }

        public void setGetTime(String getTime) {
            this.getTime = getTime;
        }

        public String getGetTimeDesc() {
            return getTimeDesc;
        }

        public void setGetTimeDesc(String getTimeDesc) {
            this.getTimeDesc = getTimeDesc;
        }

        public String getResMd5() {
            return resMd5;
        }

        public void setResMd5(String resMd5) {
            this.resMd5 = resMd5;
        }

        public Integer getSourceType() {
            return sourceType;
        }

        public void setSourceType(Integer sourceType) {
            this.sourceType = sourceType;
        }
    }

    public int getSumNum() {
        return sumNum;
    }

    public void setSumNum(int sumNum) {
        this.sumNum = sumNum;
    }

    public List<Resource> getList() {
        return list;
    }

    public void setList(List<Resource> list) {
        this.list = list;
    }

    public String getNextUrl() {
        return nextUrl;
    }

    public void setNextUrl(String nextUrl) {
        this.nextUrl = nextUrl;
    }
}
