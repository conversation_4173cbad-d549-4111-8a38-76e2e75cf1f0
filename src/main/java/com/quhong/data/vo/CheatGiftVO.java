package com.quhong.data.vo;

import org.springframework.util.StringUtils;

public class CheatGiftVO {
    // 发送者麦位图标
    private String send_mic;
    // 接收者麦位图标
    private String recv_mic;
    // 结束时间
    private int mic_end_time;

    public boolean checkValue() {
        return !StringUtils.isEmpty(send_mic) && !StringUtils.isEmpty(recv_mic) && mic_end_time > 0;
    }

    public String getSend_mic() {
        return send_mic;
    }

    public void setSend_mic(String send_mic) {
        this.send_mic = send_mic;
    }

    public String getRecv_mic() {
        return recv_mic;
    }

    public void setRecv_mic(String recv_mic) {
        this.recv_mic = recv_mic;
    }

    public int getMic_end_time() {
        return mic_end_time;
    }

    public void setMic_end_time(int mic_end_time) {
        this.mic_end_time = mic_end_time;
    }
}
