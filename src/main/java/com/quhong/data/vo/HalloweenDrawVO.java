package com.quhong.data.vo;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2024/10/16
 */
public class HalloweenDrawVO {

    private List<PrizeConfigVO> rewardList;
    private Integer giftHammerNum; // 礼物锤子数
    private Integer gameHammerNum; // 游戏锤子数

    public List<PrizeConfigVO> getRewardList() {
        return rewardList;
    }

    public void setRewardList(List<PrizeConfigVO> rewardList) {
        this.rewardList = rewardList;
    }

    public Integer getGiftHammerNum() {
        return giftHammerNum;
    }

    public void setGiftHammerNum(Integer giftHammerNum) {
        this.giftHammerNum = giftHammerNum;
    }

    public Integer getGameHammerNum() {
        return gameHammerNum;
    }

    public void setGameHammerNum(Integer gameHammerNum) {
        this.gameHammerNum = gameHammerNum;
    }
}
