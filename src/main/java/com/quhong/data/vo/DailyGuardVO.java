package com.quhong.data.vo;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2023/4/19
 */
public class DailyGuardVO {

    /**
     * 守护值
     */
    private String guardValue;

    /**
     * 守护等级
     */
    private Integer guardLevel;

    /**
     * 守护等级图标
     */
    private String guardLevelIcon;

    /**
     * 守护排行榜
     */
    private List<GuardRankUserVO> list;

    public DailyGuardVO() {
    }

    public DailyGuardVO(String guardValue, Integer guardLevel, String guardLevelIcon, List<GuardRankUserVO> list) {
        this.guardValue = guardValue;
        this.guardLevel = guardLevel;
        this.guardLevelIcon = guardLevelIcon;
        this.list = list;
    }

    public String getGuardValue() {
        return guardValue;
    }

    public void setGuardValue(String guardValue) {
        this.guardValue = guardValue;
    }

    public Integer getGuardLevel() {
        return guardLevel;
    }

    public void setGuardLevel(Integer guardLevel) {
        this.guardLevel = guardLevel;
    }

    public String getGuardLevelIcon() {
        return guardLevelIcon;
    }

    public void setGuardLevelIcon(String guardLevelIcon) {
        this.guardLevelIcon = guardLevelIcon;
    }

    public List<GuardRankUserVO> getList() {
        return list;
    }

    public void setList(List<GuardRankUserVO> list) {
        this.list = list;
    }
}
