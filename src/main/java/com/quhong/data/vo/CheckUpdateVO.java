package com.quhong.data.vo;

import java.util.Map;

/**
 * <AUTHOR>
 * @date 2022/8/22
 */
public class CheckUpdateVO {

    private String version;
    private Integer force;
    private String market;
    private String md5;
    private String url;
    private Integer size;
    private String title;
    private String desc;
    private Map<String, String> text;

    public String getVersion() { return version; }

    public void setVersion(String version) {
        this.version = version;
    }

    public Integer getForce() {
        return force;
    }

    public void setForce(Integer force) {
        this.force = force;
    }

    public String getMarket() {
        return market;
    }

    public void setMarket(String market) {
        this.market = market;
    }

    public String getMd5() {
        return md5;
    }

    public void setMd5(String md5) {
        this.md5 = md5;
    }

    public String getUrl() {
        return url;
    }

    public void setUrl(String url) {
        this.url = url;
    }

    public Integer getSize() {
        return size;
    }

    public void setSize(Integer size) {
        this.size = size;
    }

    public String getTitle() {
        return title;
    }

    public void setTitle(String title) {
        this.title = title;
    }

    public String getDesc() {
        return desc;
    }

    public void setDesc(String desc) {
        this.desc = desc;
    }

    public Map<String, String> getText() {
        return text;
    }

    public void setText(Map<String, String> text) {
        this.text = text;
    }

    @Override
    public String toString() {
        return "CheckUpdateVO{" +
                "version=" + version +
                ", force=" + force +
                ", market='" + market + '\'' +
                ", md5='" + md5 + '\'' +
                ", url='" + url + '\'' +
                ", size=" + size +
                ", title='" + title + '\'' +
                ", desc='" + desc + '\'' +
                ", text='" + text + '\'' +
                '}';
    }
}
