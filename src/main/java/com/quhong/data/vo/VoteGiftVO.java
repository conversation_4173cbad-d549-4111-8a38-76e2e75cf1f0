package com.quhong.data.vo;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2023/3/3
 */
public class VoteGiftVO {

    private List<Gift> list;

    public VoteGiftVO() {
    }

    public VoteGiftVO(List<Gift> list) {
        this.list = list;
    }

    public static class Gift {

        /**
         * 礼物id
         */
        private Integer gid;
        /**
         * 礼物图标
         */
        private String gIcon;
        /**
         * 礼物名称
         */
        private String gName;
        /**
         * 礼物名称阿语
         */
        private String gNameAr;
        /**
         * 1 钻石礼物，2 金币礼物，3 vip礼物
         */
        private int gType;
        /**
         * 礼物价格
         */
        private Integer price;

        public Gift() {
        }

        public Gift(Integer gid, String gIcon, String gName, String gNameAr, int gType, Integer price) {
            this.gid = gid;
            this.gIcon = gIcon;
            this.gName = gName;
            this.gNameAr = gNameAr;
            this.gType = gType;
            this.price = price;
        }

        public Integer getGid() {
            return gid;
        }

        public void setGid(Integer gid) {
            this.gid = gid;
        }

        public String getgIcon() {
            return gIcon;
        }

        public void setgIcon(String gIcon) {
            this.gIcon = gIcon;
        }

        public String getgName() {
            return gName;
        }

        public void setgName(String gName) {
            this.gName = gName;
        }

        public String getgNameAr() {
            return gNameAr;
        }

        public void setgNameAr(String gNameAr) {
            this.gNameAr = gNameAr;
        }

        public int getgType() {
            return gType;
        }

        public void setgType(int gType) {
            this.gType = gType;
        }

        public Integer getPrice() {
            return price;
        }

        public void setPrice(Integer price) {
            this.price = price;
        }
    }

    public List<Gift> getList() {
        return list;
    }

    public void setList(List<Gift> list) {
        this.list = list;
    }
}
