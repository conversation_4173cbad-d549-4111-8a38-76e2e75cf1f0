package com.quhong.data.vo;


import com.alibaba.fastjson.annotation.JSONField;

public class PyLabelInfoBeanVO extends LabelInfoBeanVO{

    @JSONField(name = "label_id")
    int labelId;
    @JSONField(name = "label_name")
    String labelName;
    @JSONField(name = "label_arname")
    String labelArName;
    @JSONField(name = "label_color")
    String labelColor;

    public int getLabelId() {
        return labelId;
    }

    public void setLabelId(int labelId) {
        this.labelId = labelId;
    }

    public String getLabelName() {
        return labelName;
    }

    public void setLabelName(String labelName) {
        this.labelName = labelName;
    }

    public String getLabelArName() {
        return labelArName;
    }

    public void setLabelArName(String labelArName) {
        this.labelArName = labelArName;
    }

    public String getLabelColor() {
        return labelColor;
    }

    public void setLabelColor(String labelColor) {
        this.labelColor = labelColor;
    }
}
