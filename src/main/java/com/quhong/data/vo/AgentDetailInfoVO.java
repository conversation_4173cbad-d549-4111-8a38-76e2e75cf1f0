package com.quhong.data.vo;

import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @date 2024/3/29
 */
public class AgentDetailInfoVO {

    /**
     * 公会id
     */
    private int familyId;

    /**
     * 当前佣金比例
     */
    private float currentProp;

    /**
     * 当前佣金等级
     */
    private String currentLevel;

    /**
     * 下一级佣金比例
     */
    private float nextProp;

    /**
     * 下一级佣金等级
     */
    private String nextLevel;

    /**
     * 到达下一级还需要的魅力值数
     */
    private long nextLevelNeedBeans;

    /**
     * 进度
     */
    private float progressRate;

    /**
     * 说明页H5地址
     */
    private String helpPageUrl;

    /**
     * 总收入
     */
    private long totalIncome;

    /**
     * 总佣金
     */
    private int myCommission;

    /**
     * 旗下主播收益的魅力值
     */
    private long anchorTotalIncome;

    /**
     * 我的主播魅力值佣金
     */
    private int myAnchorCommission;

    /**
     * 活跃主播数
     */
    private int activeAnchorNum;

    /**
     * 有收入的主播数
     */
    private int incomeAnchorNum;

    /**
     * 主播直播总时长
     */
    private int anchorLiveSumTime;

    /**
     * 旗下代理收益的魅力值
     */
    private long agentTotalIncome;

    /**
     * 我的代理魅力值佣金
     */
    private int myAgentCommission;

    /**
     * 有收入的代理数
     */
    private int incomeAgentNum;

    public int getFamilyId() {
        return familyId;
    }

    public void setFamilyId(int familyId) {
        this.familyId = familyId;
    }

    public float getCurrentProp() {
        return currentProp;
    }

    public void setCurrentProp(float currentProp) {
        this.currentProp = currentProp;
    }

    public String getCurrentLevel() {
        return currentLevel;
    }

    public void setCurrentLevel(String currentLevel) {
        this.currentLevel = currentLevel;
    }

    public float getNextProp() {
        return nextProp;
    }

    public void setNextProp(float nextProp) {
        this.nextProp = nextProp;
    }

    public String getNextLevel() {
        return nextLevel;
    }

    public void setNextLevel(String nextLevel) {
        this.nextLevel = nextLevel;
    }

    public long getNextLevelNeedBeans() {
        return nextLevelNeedBeans;
    }

    public void setNextLevelNeedBeans(long nextLevelNeedBeans) {
        this.nextLevelNeedBeans = nextLevelNeedBeans;
    }

    public float getProgressRate() {
        return progressRate;
    }

    public void setProgressRate(float progressRate) {
        this.progressRate = progressRate;
    }

    public String getHelpPageUrl() {
        return helpPageUrl;
    }

    public void setHelpPageUrl(String helpPageUrl) {
        this.helpPageUrl = helpPageUrl;
    }

    public long getTotalIncome() {
        return totalIncome;
    }

    public void setTotalIncome(long totalIncome) {
        this.totalIncome = totalIncome;
    }

    public int getMyCommission() {
        return myCommission;
    }

    public void setMyCommission(int myCommission) {
        this.myCommission = myCommission;
    }

    public long getAnchorTotalIncome() {
        return anchorTotalIncome;
    }

    public void setAnchorTotalIncome(long anchorTotalIncome) {
        this.anchorTotalIncome = anchorTotalIncome;
    }

    public int getMyAnchorCommission() {
        return myAnchorCommission;
    }

    public void setMyAnchorCommission(int myAnchorCommission) {
        this.myAnchorCommission = myAnchorCommission;
    }

    public int getActiveAnchorNum() {
        return activeAnchorNum;
    }

    public void setActiveAnchorNum(int activeAnchorNum) {
        this.activeAnchorNum = activeAnchorNum;
    }

    public int getIncomeAnchorNum() {
        return incomeAnchorNum;
    }

    public void setIncomeAnchorNum(int incomeAnchorNum) {
        this.incomeAnchorNum = incomeAnchorNum;
    }

    public int getAnchorLiveSumTime() {
        return anchorLiveSumTime;
    }

    public void setAnchorLiveSumTime(int anchorLiveSumTime) {
        this.anchorLiveSumTime = anchorLiveSumTime;
    }

    public long getAgentTotalIncome() {
        return agentTotalIncome;
    }

    public void setAgentTotalIncome(long agentTotalIncome) {
        this.agentTotalIncome = agentTotalIncome;
    }

    public int getMyAgentCommission() {
        return myAgentCommission;
    }

    public void setMyAgentCommission(int myAgentCommission) {
        this.myAgentCommission = myAgentCommission;
    }

    public int getIncomeAgentNum() {
        return incomeAgentNum;
    }

    public void setIncomeAgentNum(int incomeAgentNum) {
        this.incomeAgentNum = incomeAgentNum;
    }
}
