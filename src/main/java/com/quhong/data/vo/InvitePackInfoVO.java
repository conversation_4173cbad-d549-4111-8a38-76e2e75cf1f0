package com.quhong.data.vo;

import com.quhong.mongo.data.PackData;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2024/7/1
 */
public class InvitePackInfoVO {

    /**
     * 礼包名称
     */
    private String name;

    /**
     * 礼包标题
     */
    private String title;

    /**
     * 礼包标题阿语
     */
    private String titleAr;

    /**
     * 礼包描述
     */
    private String desc;

    /**
     * 礼包描述阿语
     */
    private String descAr;

    /**
     * 奖励
     */
    private List<PackData> packList;

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getTitle() {
        return title;
    }

    public void setTitle(String title) {
        this.title = title;
    }

    public String getTitleAr() {
        return titleAr;
    }

    public void setTitleAr(String titleAr) {
        this.titleAr = titleAr;
    }

    public String getDesc() {
        return desc;
    }

    public void setDesc(String desc) {
        this.desc = desc;
    }

    public String getDescAr() {
        return descAr;
    }

    public void setDescAr(String descAr) {
        this.descAr = descAr;
    }

    public List<PackData> getPackList() {
        return packList;
    }

    public void setPackList(List<PackData> packList) {
        this.packList = packList;
    }
}
