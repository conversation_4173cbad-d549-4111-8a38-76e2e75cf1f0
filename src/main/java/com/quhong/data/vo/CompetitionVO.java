package com.quhong.data.vo;


public class CompetitionVO {
    private String player1Name;
    private String player1Head;
    private String player1Uid;
    private int player1Bean;
    private int player1Rank;

    private String player2Name;
    private String player2Head;
    private String player2Uid;
    private int player2Bean;
    private int player2Rank;

    public String getPlayer1Name() {
        return player1Name;
    }

    public void setPlayer1Name(String player1Name) {
        this.player1Name = player1Name;
    }

    public String getPlayer1Head() {
        return player1Head;
    }

    public void setPlayer1Head(String player1Head) {
        this.player1Head = player1Head;
    }

    public int getPlayer1Bean() {
        return player1Bean;
    }

    public void setPlayer1Bean(int player1Bean) {
        this.player1Bean = player1Bean;
    }

    public String getPlayer2Name() {
        return player2Name;
    }

    public void setPlayer2Name(String player2Name) {
        this.player2Name = player2Name;
    }

    public String getPlayer2Head() {
        return player2Head;
    }

    public void setPlayer2Head(String player2Head) {
        this.player2Head = player2Head;
    }

    public int getPlayer2Bean() {
        return player2Bean;
    }

    public void setPlayer2Bean(int player2Bean) {
        this.player2Bean = player2Bean;
    }

    public int getPlayer1Rank() {
        return player1Rank;
    }

    public void setPlayer1Rank(int player1Rank) {
        this.player1Rank = player1Rank;
    }

    public int getPlayer2Rank() {
        return player2Rank;
    }

    public void setPlayer2Rank(int player2Rank) {
        this.player2Rank = player2Rank;
    }

    public String getPlayer1Uid() {
        return player1Uid;
    }

    public void setPlayer1Uid(String player1Uid) {
        this.player1Uid = player1Uid;
    }

    public String getPlayer2Uid() {
        return player2Uid;
    }

    public void setPlayer2Uid(String player2Uid) {
        this.player2Uid = player2Uid;
    }
}
