package com.quhong.data.vo;

import java.util.List;

public class RoomActorListVO<T> {

    private int next_page;
    private int page_size;
    private int total_actors;
    private List<T> list;

    public RoomActorListVO() {
    }

    public RoomActorListVO(int next_page, int page_size, int total_actors, List<T> list) {
        this.next_page = next_page;
        this.page_size = page_size;
        this.total_actors = total_actors;
        this.list = list;
    }

    public int getNext_page() {
        return next_page;
    }

    public void setNext_page(int next_page) {
        this.next_page = next_page;
    }

    public int getPage_size() {
        return page_size;
    }

    public void setPage_size(int page_size) {
        this.page_size = page_size;
    }

    public int getTotal_actors() {
        return total_actors;
    }

    public void setTotal_actors(int total_actors) {
        this.total_actors = total_actors;
    }

    public List<T> getList() {
        return list;
    }

    public void setList(List<T> list) {
        this.list = list;
    }
}
