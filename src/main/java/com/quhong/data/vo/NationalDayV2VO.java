package com.quhong.data.vo;

import com.quhong.mongo.data.NationalDayV2Data;

import java.util.List;

public class NationalDayV2VO {
    private String acNameEn; // 英文活动名
    private String acNameAr; // 阿语活动名
    private String acUrl; // h5地址(自动生成)
    private Integer startTime; // 活动开始时间
    private Integer endTime; // 活动结束时间
    private Integer homePageType; // 大头页类型 1静态 2动态 3静态动态
    private String homePagePicture; // 大头页静态图
    private String homePagePictureAr; // 大头页静态图ar
    private String homePagePictureDt; // 大头页动态图
    private String homePagePictureDtAr; // 大头页动态图ar
    private String backgroundHome; // 首页背景图
    private String backgroundHelp; // 助力页背景图
    private String backgroundDraw; // 抽奖背景图
    private String backgroundRule; // 规则页面背景图
    private String soundPlayIcon; // 背景音乐播放图标
    private String soundPauseIcon; // 背景音乐静音图标
    private String soundUrl; // 背景音乐url
    private String gamePlayButtonEn; // 玩法按钮英语图标
    private String gamePlayButtonAr; // 玩法按钮阿语图标
    private String questionButton; // 问卷图标
    private String questionUrl; // 问卷url
    private String drawButtonEn; // 抽奖按钮英语图标
    private String drawButtonAr; // 抽奖按钮阿语图标
    private String pageHomeColor; // 页面背景色
    private String joinButton; // 参与/助力图标
    private String joinButtonBlack; // 参与/助力图标灰色
    private String flagIcon; // 国旗图标
    private String processIcon; // 进度条图标
    private String fontColor; // 字体颜色图标
    private String textColor; // 正文颜色
    private String countDownColor; // 倒计时颜色
    private String ruleColor; // 规则页面字体颜色
    private String noticeBarColor; // 通知栏颜色
    private Integer giftId; // 活动礼物id
    private int totalJoin; // 参加总人数
    private boolean joined; // 是否已加入
    private int flagProcess; // 国旗进度
    private List<HelpUser> helpUserList; // 助力用户
    private Integer rid; // 用户rid
    private int sendNum; // 999礼物发送次数

    private boolean drew;  // 是否已抽奖
    private String awardIcon;  // 抽奖中奖图片
    private String rewardType; // 奖励类型
    private String rewardNameEn; // 奖励名称
    private String rewardNameAr; // 奖励名称阿语
    private boolean like;  // 是否已点赞
    private int likeNum;  // 点赞数量

    private NationalDayV2Data.AdvanceConfig advanceConfig; // 进阶配置
    private NationalDayV2Data.RewardConfig rewardConfig; // 奖励配置
    private List<NationalDayV2Data.DrawRewardConfig> drawRewardConfigList; // 抽奖奖励配置
    private List<RecordData> recordList; // 中奖记录

    private Integer status; // 1 是否已结束
    private Integer ctime; // 创建时间
    private Integer mtime; // 更新时间

    public static class RecordData{
        private String name; // 用户名称
        private String rewardNameEn; // 奖励名称
        private String rewardNameAr; // 奖励名称阿语

        public String getName() {
            return name;
        }

        public void setName(String name) {
            this.name = name;
        }

        public String getRewardNameEn() {
            return rewardNameEn;
        }

        public void setRewardNameEn(String rewardNameEn) {
            this.rewardNameEn = rewardNameEn;
        }

        public String getRewardNameAr() {
            return rewardNameAr;
        }

        public void setRewardNameAr(String rewardNameAr) {
            this.rewardNameAr = rewardNameAr;
        }
    }

    public static class HelpUser{
        private String name;
        private String head;

        public String getName() {
            return name;
        }

        public void setName(String name) {
            this.name = name;
        }

        public String getHead() {
            return head;
        }

        public void setHead(String head) {
            this.head = head;
        }
    }

    public String getAcNameEn() {
        return acNameEn;
    }

    public void setAcNameEn(String acNameEn) {
        this.acNameEn = acNameEn;
    }

    public String getAcNameAr() {
        return acNameAr;
    }

    public void setAcNameAr(String acNameAr) {
        this.acNameAr = acNameAr;
    }

    public String getAcUrl() {
        return acUrl;
    }

    public void setAcUrl(String acUrl) {
        this.acUrl = acUrl;
    }

    public Integer getStartTime() {
        return startTime;
    }

    public void setStartTime(Integer startTime) {
        this.startTime = startTime;
    }

    public Integer getEndTime() {
        return endTime;
    }

    public void setEndTime(Integer endTime) {
        this.endTime = endTime;
    }

    public Integer getHomePageType() {
        return homePageType;
    }

    public void setHomePageType(Integer homePageType) {
        this.homePageType = homePageType;
    }

    public String getHomePagePicture() {
        return homePagePicture;
    }

    public void setHomePagePicture(String homePagePicture) {
        this.homePagePicture = homePagePicture;
    }

    public String getHomePagePictureAr() {
        return homePagePictureAr;
    }

    public void setHomePagePictureAr(String homePagePictureAr) {
        this.homePagePictureAr = homePagePictureAr;
    }

    public String getHomePagePictureDt() {
        return homePagePictureDt;
    }

    public void setHomePagePictureDt(String homePagePictureDt) {
        this.homePagePictureDt = homePagePictureDt;
    }

    public String getHomePagePictureDtAr() {
        return homePagePictureDtAr;
    }

    public void setHomePagePictureDtAr(String homePagePictureDtAr) {
        this.homePagePictureDtAr = homePagePictureDtAr;
    }

    public String getBackgroundHome() {
        return backgroundHome;
    }

    public void setBackgroundHome(String backgroundHome) {
        this.backgroundHome = backgroundHome;
    }

    public String getBackgroundHelp() {
        return backgroundHelp;
    }

    public void setBackgroundHelp(String backgroundHelp) {
        this.backgroundHelp = backgroundHelp;
    }

    public String getBackgroundDraw() {
        return backgroundDraw;
    }

    public void setBackgroundDraw(String backgroundDraw) {
        this.backgroundDraw = backgroundDraw;
    }

    public String getBackgroundRule() {
        return backgroundRule;
    }

    public void setBackgroundRule(String backgroundRule) {
        this.backgroundRule = backgroundRule;
    }

    public String getSoundPlayIcon() {
        return soundPlayIcon;
    }

    public void setSoundPlayIcon(String soundPlayIcon) {
        this.soundPlayIcon = soundPlayIcon;
    }

    public String getSoundPauseIcon() {
        return soundPauseIcon;
    }

    public void setSoundPauseIcon(String soundPauseIcon) {
        this.soundPauseIcon = soundPauseIcon;
    }

    public String getSoundUrl() {
        return soundUrl;
    }

    public void setSoundUrl(String soundUrl) {
        this.soundUrl = soundUrl;
    }

    public String getGamePlayButtonEn() {
        return gamePlayButtonEn;
    }

    public void setGamePlayButtonEn(String gamePlayButtonEn) {
        this.gamePlayButtonEn = gamePlayButtonEn;
    }

    public String getGamePlayButtonAr() {
        return gamePlayButtonAr;
    }

    public void setGamePlayButtonAr(String gamePlayButtonAr) {
        this.gamePlayButtonAr = gamePlayButtonAr;
    }

    public String getQuestionButton() {
        return questionButton;
    }

    public void setQuestionButton(String questionButton) {
        this.questionButton = questionButton;
    }

    public String getQuestionUrl() {
        return questionUrl;
    }

    public void setQuestionUrl(String questionUrl) {
        this.questionUrl = questionUrl;
    }

    public String getDrawButtonEn() {
        return drawButtonEn;
    }

    public void setDrawButtonEn(String drawButtonEn) {
        this.drawButtonEn = drawButtonEn;
    }

    public String getDrawButtonAr() {
        return drawButtonAr;
    }

    public void setDrawButtonAr(String drawButtonAr) {
        this.drawButtonAr = drawButtonAr;
    }

    public String getPageHomeColor() {
        return pageHomeColor;
    }

    public void setPageHomeColor(String pageHomeColor) {
        this.pageHomeColor = pageHomeColor;
    }

    public String getJoinButton() {
        return joinButton;
    }

    public void setJoinButton(String joinButton) {
        this.joinButton = joinButton;
    }

    public String getJoinButtonBlack() {
        return joinButtonBlack;
    }

    public void setJoinButtonBlack(String joinButtonBlack) {
        this.joinButtonBlack = joinButtonBlack;
    }

    public String getFlagIcon() {
        return flagIcon;
    }

    public void setFlagIcon(String flagIcon) {
        this.flagIcon = flagIcon;
    }

    public String getProcessIcon() {
        return processIcon;
    }

    public void setProcessIcon(String processIcon) {
        this.processIcon = processIcon;
    }

    public String getFontColor() {
        return fontColor;
    }

    public void setFontColor(String fontColor) {
        this.fontColor = fontColor;
    }


    public String getTextColor() {
        return textColor;
    }

    public void setTextColor(String textColor) {
        this.textColor = textColor;
    }

    public String getCountDownColor() {
        return countDownColor;
    }

    public void setCountDownColor(String countDownColor) {
        this.countDownColor = countDownColor;
    }

    public String getRuleColor() {
        return ruleColor;
    }

    public void setRuleColor(String ruleColor) {
        this.ruleColor = ruleColor;
    }

    public String getNoticeBarColor() {
        return noticeBarColor;
    }

    public void setNoticeBarColor(String noticeBarColor) {
        this.noticeBarColor = noticeBarColor;
    }

    public Integer getGiftId() {
        return giftId;
    }

    public void setGiftId(Integer giftId) {
        this.giftId = giftId;
    }

    public int getTotalJoin() {
        return totalJoin;
    }

    public void setTotalJoin(int totalJoin) {
        this.totalJoin = totalJoin;
    }

    public boolean isJoined() {
        return joined;
    }

    public void setJoined(boolean joined) {
        this.joined = joined;
    }

    public int getFlagProcess() {
        return flagProcess;
    }

    public void setFlagProcess(int flagProcess) {
        this.flagProcess = flagProcess;
    }

    public List<HelpUser> getHelpUserList() {
        return helpUserList;
    }

    public void setHelpUserList(List<HelpUser> helpUserList) {
        this.helpUserList = helpUserList;
    }

    public Integer getRid() {
        return rid;
    }

    public void setRid(Integer rid) {
        this.rid = rid;
    }

    public int getSendNum() {
        return sendNum;
    }

    public void setSendNum(int sendNum) {
        this.sendNum = sendNum;
    }

    public boolean isDrew() {
        return drew;
    }

    public void setDrew(boolean drew) {
        this.drew = drew;
    }

    public String getAwardIcon() {
        return awardIcon;
    }

    public void setAwardIcon(String awardIcon) {
        this.awardIcon = awardIcon;
    }

    public String getRewardType() {
        return rewardType;
    }

    public void setRewardType(String rewardType) {
        this.rewardType = rewardType;
    }

    public String getRewardNameEn() {
        return rewardNameEn;
    }

    public void setRewardNameEn(String rewardNameEn) {
        this.rewardNameEn = rewardNameEn;
    }

    public String getRewardNameAr() {
        return rewardNameAr;
    }

    public void setRewardNameAr(String rewardNameAr) {
        this.rewardNameAr = rewardNameAr;
    }

    public boolean isLike() {
        return like;
    }

    public void setLike(boolean like) {
        this.like = like;
    }

    public int getLikeNum() {
        return likeNum;
    }

    public void setLikeNum(int likeNum) {
        this.likeNum = likeNum;
    }

    public NationalDayV2Data.AdvanceConfig getAdvanceConfig() {
        return advanceConfig;
    }

    public void setAdvanceConfig(NationalDayV2Data.AdvanceConfig advanceConfig) {
        this.advanceConfig = advanceConfig;
    }

    public NationalDayV2Data.RewardConfig getRewardConfig() {
        return rewardConfig;
    }

    public void setRewardConfig(NationalDayV2Data.RewardConfig rewardConfig) {
        this.rewardConfig = rewardConfig;
    }

    public List<NationalDayV2Data.DrawRewardConfig> getDrawRewardConfigList() {
        return drawRewardConfigList;
    }

    public void setDrawRewardConfigList(List<NationalDayV2Data.DrawRewardConfig> drawRewardConfigList) {
        this.drawRewardConfigList = drawRewardConfigList;
    }

    public List<RecordData> getRecordList() {
        return recordList;
    }

    public void setRecordList(List<RecordData> recordList) {
        this.recordList = recordList;
    }

    public Integer getStatus() {
        return status;
    }

    public void setStatus(Integer status) {
        this.status = status;
    }

    public Integer getCtime() {
        return ctime;
    }

    public void setCtime(Integer ctime) {
        this.ctime = ctime;
    }

    public Integer getMtime() {
        return mtime;
    }

    public void setMtime(Integer mtime) {
        this.mtime = mtime;
    }
}
