package com.quhong.data.vo;

import com.quhong.data.HonorTitleData;
import com.quhong.data.ProfileCardFrameData;
import com.quhong.data.RidData;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2022/5/26
 */
public class UserCardVO {
    private RidData ridInfo; // 新版本rid对象
    private String name;
    private String nameV2;
    private String head;
    private String desc;
    private int gender;
    private int age;
    private String country;
    private int rid;
    private int valid;
    private String aid;
    private String alphaRid;
    private int alphaType;
    private int activeLevel;
    private int wealthLevel;
    private int charmLevel;
    private List<String> badge;
    private String micFrame;
    private int role;
    private int roleData;
    private int forbidEnTime;
    private int isMutedChat; // 是否被禁言 0否 1是
    private int micForbidEnTime;
    private int isFollowed;
    private int friends;
    private boolean block;
    private int vipLevelV2;
    private int slang;
    private int familyRid; // 公会rid
    private String familyName; // 公会名字
    private int coinSeller; // 1币商
    private int bd; // 1bd白名单
    private int bdAdminLevel; // bd admin等级
    private List<HonorTitleData> honorTitleList; // 荣誉称号列表
    private List<GuardRankUserVO> guardianTopThree; // 守护者前三名
    private Integer starSign; //星座 1-12 水瓶座-魔羯座
    private List<LabelInfoBeanVO> interestTags; // 兴趣标签
    private int badgeNum; // 勋章数量
    private int followerNum; // 粉丝数量
    private List<BadgeDetailVO> badgeList; // 勋章列表（新版本）
    // newbie字段
    private int platform;   // 1Apple标签 0 安卓
    private int topUp;   // 充值用户标签
    private int richPlace;   // RichPlace标签
    private int newbie;   // Newbie标签
    private int svipLevel;
    private ProfileCardFrameData cardFrame; // 资料卡边框


    public RidData getRidInfo() {
        return ridInfo;
    }

    public void setRidInfo(RidData ridInfo) {
        this.ridInfo = ridInfo;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getHead() {
        return head;
    }

    public void setHead(String head) {
        this.head = head;
    }

    public String getDesc() {
        return desc;
    }

    public void setDesc(String desc) {
        this.desc = desc;
    }

    public int getGender() {
        return gender;
    }

    public void setGender(int gender) {
        this.gender = gender;
    }

    public int getAge() {
        return age;
    }

    public void setAge(int age) {
        this.age = age;
    }

    public String getCountry() {
        return country;
    }

    public void setCountry(String country) {
        this.country = country;
    }

    public int getRid() {
        return rid;
    }

    public void setRid(int rid) {
        this.rid = rid;
    }

    public int getValid() {
        return valid;
    }

    public void setValid(int valid) {
        this.valid = valid;
    }

    public String getAid() {
        return aid;
    }

    public void setAid(String aid) {
        this.aid = aid;
    }

    public String getAlphaRid() {
        return alphaRid;
    }

    public void setAlphaRid(String alphaRid) {
        this.alphaRid = alphaRid;
    }

    public int getAlphaType() {
        return alphaType;
    }

    public void setAlphaType(int alphaType) {
        this.alphaType = alphaType;
    }

    public int getActiveLevel() {
        return activeLevel;
    }

    public void setActiveLevel(int activeLevel) {
        this.activeLevel = activeLevel;
    }

    public List<String> getBadge() {
        return badge;
    }

    public void setBadge(List<String> badge) {
        this.badge = badge;
    }

    public String getMicFrame() {
        return micFrame;
    }

    public void setMicFrame(String micFrame) {
        this.micFrame = micFrame;
    }

    public int getRole() {
        return role;
    }

    public void setRole(int role) {
        this.role = role;
    }

    public int getRoleData() {
        return roleData;
    }

    public void setRoleData(int roleData) {
        this.roleData = roleData;
    }

    public int getForbidEnTime() {
        return forbidEnTime;
    }

    public void setForbidEnTime(int forbidEnTime) {
        this.forbidEnTime = forbidEnTime;
    }

    public int getMicForbidEnTime() {
        return micForbidEnTime;
    }

    public void setMicForbidEnTime(int micForbidEnTime) {
        this.micForbidEnTime = micForbidEnTime;
    }

    public int getIsFollowed() {
        return isFollowed;
    }

    public void setIsFollowed(int isFollowed) {
        this.isFollowed = isFollowed;
    }

    public int getFriends() {
        return friends;
    }

    public void setFriends(int friends) {
        this.friends = friends;
    }

    public boolean getBlock() {
        return block;
    }

    public void setBlock(boolean block) {
        this.block = block;
    }

    public int getSlang() {
        return slang;
    }

    public void setSlang(int slang) {
        this.slang = slang;
    }

    public String getNameV2() {
        return nameV2;
    }

    public void setNameV2(String nameV2) {
        this.nameV2 = nameV2;
    }

    public boolean isBlock() {
        return block;
    }

    public int getVipLevelV2() {
        return vipLevelV2;
    }

    public void setVipLevelV2(int vipLevelV2) {
        this.vipLevelV2 = vipLevelV2;
    }

    public int getWealthLevel() {
        return wealthLevel;
    }

    public void setWealthLevel(int wealthLevel) {
        this.wealthLevel = wealthLevel;
    }

    public int getCharmLevel() {
        return charmLevel;
    }

    public void setCharmLevel(int charmLevel) {
        this.charmLevel = charmLevel;
    }

    public int getFamilyRid() {
        return familyRid;
    }

    public void setFamilyRid(int familyRid) {
        this.familyRid = familyRid;
    }

    public String getFamilyName() {
        return familyName;
    }

    public void setFamilyName(String familyName) {
        this.familyName = familyName;
    }

    public int getCoinSeller() {
        return coinSeller;
    }

    public void setCoinSeller(int coinSeller) {
        this.coinSeller = coinSeller;
    }

    public int getBd() {
        return bd;
    }

    public void setBd(int bd) {
        this.bd = bd;
    }

    public List<HonorTitleData> getHonorTitleList() {
        return honorTitleList;
    }

    public void setHonorTitleList(List<HonorTitleData> honorTitleList) {
        this.honorTitleList = honorTitleList;
    }

    public List<GuardRankUserVO> getGuardianTopThree() {
        return guardianTopThree;
    }

    public void setGuardianTopThree(List<GuardRankUserVO> guardianTopThree) {
        this.guardianTopThree = guardianTopThree;
    }

    public Integer getStarSign() {
        return starSign;
    }

    public void setStarSign(Integer starSign) {
        this.starSign = starSign;
    }

    public List<LabelInfoBeanVO> getInterestTags() {
        return interestTags;
    }

    public void setInterestTags(List<LabelInfoBeanVO> interestTags) {
        this.interestTags = interestTags;
    }

    public int getBadgeNum() {
        return badgeNum;
    }

    public void setBadgeNum(int badgeNum) {
        this.badgeNum = badgeNum;
    }

    public int getFollowerNum() {
        return followerNum;
    }

    public void setFollowerNum(int followerNum) {
        this.followerNum = followerNum;
    }

    public List<BadgeDetailVO> getBadgeList() {
        return badgeList;
    }

    public void setBadgeList(List<BadgeDetailVO> badgeList) {
        this.badgeList = badgeList;
    }

    public int getIsMutedChat() {
        return isMutedChat;
    }

    public void setIsMutedChat(int isMutedChat) {
        this.isMutedChat = isMutedChat;
    }

    public int getBdAdminLevel() {
        return bdAdminLevel;
    }

    public void setBdAdminLevel(int bdAdminLevel) {
        this.bdAdminLevel = bdAdminLevel;
    }

    public int getPlatform() {
        return platform;
    }

    public void setPlatform(int platform) {
        this.platform = platform;
    }

    public int getTopUp() {
        return topUp;
    }

    public void setTopUp(int topUp) {
        this.topUp = topUp;
    }

    public int getRichPlace() {
        return richPlace;
    }

    public void setRichPlace(int richPlace) {
        this.richPlace = richPlace;
    }

    public int getNewbie() {
        return newbie;
    }

    public void setNewbie(int newbie) {
        this.newbie = newbie;
    }

    public int getSvipLevel() {
        return svipLevel;
    }

    public void setSvipLevel(int svipLevel) {
        this.svipLevel = svipLevel;
    }

    public ProfileCardFrameData getCardFrame() {
        return cardFrame;
    }

    public void setCardFrame(ProfileCardFrameData cardFrame) {
        this.cardFrame = cardFrame;
    }

}
