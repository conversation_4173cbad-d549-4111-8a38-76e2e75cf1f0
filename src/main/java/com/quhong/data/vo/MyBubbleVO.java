package com.quhong.data.vo;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2022/9/16
 */
public class MyBubbleVO {

    private List<BubbleVO> list;

    private String nextUrl;

    public static class BubbleVO {

        private Integer buddle_id;

        private String buddle_name;

        private String buddle_icon;

        private String source_url;

        private Integer is_activity;

        private String buddle_color;

        private Integer end_days;

        private Integer status;

        private String buddle_tl;

        private String buddle_tr;

        private String buddle_bl;

        private String buddle_br;

        private Integer top;

        private Integer bottom;

        private Integer left;

        private Integer right;

        private int item_type;

        public Integer getBuddle_id() {
            return buddle_id;
        }

        public void setBuddle_id(Integer buddle_id) {
            this.buddle_id = buddle_id;
        }

        public String getBuddle_icon() {
            return buddle_icon;
        }

        public void setBuddle_icon(String buddle_icon) {
            this.buddle_icon = buddle_icon;
        }

        public String getSource_url() {
            return source_url;
        }

        public void setSource_url(String source_url) {
            this.source_url = source_url;
        }

        public Integer getIs_activity() {
            return is_activity;
        }

        public void setIs_activity(Integer is_activity) {
            this.is_activity = is_activity;
        }

        public String getBuddle_color() {
            return buddle_color;
        }

        public void setBuddle_color(String buddle_color) {
            this.buddle_color = buddle_color;
        }

        public Integer getEnd_days() {
            return end_days;
        }

        public void setEnd_days(Integer end_days) {
            this.end_days = end_days;
        }

        public Integer getStatus() {
            return status;
        }

        public void setStatus(Integer status) {
            this.status = status;
        }

        public String getBuddle_name() {
            return buddle_name;
        }

        public void setBuddle_name(String buddle_name) {
            this.buddle_name = buddle_name;
        }

        public String getBuddle_tl() {
            return buddle_tl;
        }

        public void setBuddle_tl(String buddle_tl) {
            this.buddle_tl = buddle_tl;
        }

        public String getBuddle_tr() {
            return buddle_tr;
        }

        public void setBuddle_tr(String buddle_tr) {
            this.buddle_tr = buddle_tr;
        }

        public String getBuddle_bl() {
            return buddle_bl;
        }

        public void setBuddle_bl(String buddle_bl) {
            this.buddle_bl = buddle_bl;
        }

        public String getBuddle_br() {
            return buddle_br;
        }

        public void setBuddle_br(String buddle_br) {
            this.buddle_br = buddle_br;
        }

        public Integer getTop() {
            return top;
        }

        public void setTop(Integer top) {
            this.top = top;
        }

        public Integer getBottom() {
            return bottom;
        }

        public void setBottom(Integer bottom) {
            this.bottom = bottom;
        }

        public Integer getLeft() {
            return left;
        }

        public void setLeft(Integer left) {
            this.left = left;
        }

        public Integer getRight() {
            return right;
        }

        public void setRight(Integer right) {
            this.right = right;
        }

        public int getItem_type() {
            return item_type;
        }

        public void setItem_type(int item_type) {
            this.item_type = item_type;
        }
    }

    public List<BubbleVO> getList() {
        return list;
    }

    public void setList(List<BubbleVO> list) {
        this.list = list;
    }

    public String getNextUrl() {
        return nextUrl;
    }

    public void setNextUrl(String nextUrl) {
        this.nextUrl = nextUrl;
    }
}
