package com.quhong.data.vo;

import com.quhong.mongo.data.ResourceKeyConfigData;

import java.util.List;

public class CatchFishVO extends OtherRankConfigVO{

    private String popularRoomId;       // 房间id
    private Integer times;       // 抽奖次数  日更新
    private Integer earnBeans;   // 获得钻石数
    private Integer seaMeter;    // 海洋深度
    private List<ResourceKeyConfigData> resourceKeyDataList;

    private List<PrizeConfigV2VO> seaUserList;   // 开启海的用户数量

    // 抽奖记录
    private List<PrizeConfigV2VO> drawRecordList;   // 抽奖奖品
    private Integer nextUrl;

    // 榜单
    private List<OtherRankingListVO> catchRankList;
    private OtherRankingListVO myCatchRank;

    public String getPopularRoomId() {
        return popularRoomId;
    }

    public void setPopularRoomId(String popularRoomId) {
        this.popularRoomId = popularRoomId;
    }

    public Integer getTimes() {
        return times;
    }

    public void setTimes(Integer times) {
        this.times = times;
    }

    public Integer getEarnBeans() {
        return earnBeans;
    }

    public void setEarnBeans(Integer earnBeans) {
        this.earnBeans = earnBeans;
    }

    public Integer getSeaMeter() {
        return seaMeter;
    }

    public void setSeaMeter(Integer seaMeter) {
        this.seaMeter = seaMeter;
    }

    public List<ResourceKeyConfigData> getResourceKeyDataList() {
        return resourceKeyDataList;
    }

    public void setResourceKeyDataList(List<ResourceKeyConfigData> resourceKeyDataList) {
        this.resourceKeyDataList = resourceKeyDataList;
    }

    public List<PrizeConfigV2VO> getSeaUserList() {
        return seaUserList;
    }

    public void setSeaUserList(List<PrizeConfigV2VO> seaUserList) {
        this.seaUserList = seaUserList;
    }

    public List<PrizeConfigV2VO> getDrawRecordList() {
        return drawRecordList;
    }

    public void setDrawRecordList(List<PrizeConfigV2VO> drawRecordList) {
        this.drawRecordList = drawRecordList;
    }

    public Integer getNextUrl() {
        return nextUrl;
    }

    public void setNextUrl(Integer nextUrl) {
        this.nextUrl = nextUrl;
    }

    public List<OtherRankingListVO> getCatchRankList() {
        return catchRankList;
    }

    public void setCatchRankList(List<OtherRankingListVO> catchRankList) {
        this.catchRankList = catchRankList;
    }

    public OtherRankingListVO getMyCatchRank() {
        return myCatchRank;
    }

    public void setMyCatchRank(OtherRankingListVO myCatchRank) {
        this.myCatchRank = myCatchRank;
    }
}
