package com.quhong.data.vo;

import com.quhong.data.RidData;

/**
 * <AUTHOR>
 * @date 2022/12/7
 */
public class RoomEventVO {

    /**
     * 活动id
     */
    private Integer eventId;

    /**
     * 活动房间id
     */
    private String roomId;

    /**
     * 房间人数
     */
    private Integer roomUserNum;

    /**
     * 用户rid
     */
    private Integer rid;

    /**
     * 用户靓号
     */
    private RidData ridData;

    /**
     * 创建者uid
     */
    private String aid;

    /**
     * 房间名
     */
    private String roomName;

    /**
     * 房间封面
     */
    private String roomHead;

    /**
     * 活动名称
     */
    private String name;

    /**
     * 活动类型
     */
    private Integer type;

    /**
     * 活动类型名称
     */
    private String typeName;

    /**
     * 活动描述
     */
    private String desc;

    /**
     * 活动类型图标
     */
    private String typeIcon;

    /**
     * 活动开始时间
     */
    private Integer startTime;

    /**
     * 活动结束时间
     */
    private Integer endTime;

    /**
     * 活动海报url
     */
    private String posterUrl;

    /**
     * 活动海报缩小图
     */
    private String posterThumbnails;

    /**
     * 活动封面
     */
    private String eventCoverUrl;

    /**
     * 订阅人数
     */
    private Integer subNum;

    /**
     * 是否已订阅 0否 1是
     */
    private Integer isSubscribed;

    /**
     * 0未开始 1正在进行
     */
    private Integer status;

    /**
     * 活动状态 0审核中 1审核通过 2审核不通过
     */
    private int eventStatus;

    /**
     * 活动期间发礼物人数
     */
    private Integer sendGiftUserNum;

    /**
     * 活动期间礼物消耗总钻石，钻石数超过千显示K，保留1位小数
     */
    private String totalCost;

    /**
     * 创建人
     */
    private EventActorVO creator;

    /**
     * 审核原因
     */
    private String reviewReason;

    public String getDesc() {
        return desc;
    }

    public void setDesc(String desc) {
        this.desc = desc;
    }

    public Integer getEventId() {
        return eventId;
    }

    public void setEventId(Integer eventId) {
        this.eventId = eventId;
    }

    public String getRoomId() {
        return roomId;
    }

    public void setRoomId(String roomId) {
        this.roomId = roomId;
    }

    public String getRoomName() {
        return roomName;
    }

    public void setRoomName(String roomName) {
        this.roomName = roomName;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getTypeName() {
        return typeName;
    }

    public void setTypeName(String typeName) {
        this.typeName = typeName;
    }

    public String getTypeIcon() {
        return typeIcon;
    }

    public void setTypeIcon(String typeIcon) {
        this.typeIcon = typeIcon;
    }

    public Integer getStartTime() {
        return startTime;
    }

    public void setStartTime(Integer startTime) {
        this.startTime = startTime;
    }

    public Integer getEndTime() {
        return endTime;
    }

    public void setEndTime(Integer endTime) {
        this.endTime = endTime;
    }

    public String getPosterUrl() {
        return posterUrl;
    }

    public void setPosterUrl(String posterUrl) {
        this.posterUrl = posterUrl;
    }

    public Integer getSubNum() {
        return subNum;
    }

    public void setSubNum(Integer subNum) {
        this.subNum = subNum;
    }

    public Integer getIsSubscribed() {
        return isSubscribed;
    }

    public void setIsSubscribed(Integer isSubscribed) {
        this.isSubscribed = isSubscribed;
    }

    public Integer getStatus() {
        return status;
    }

    public void setStatus(Integer status) {
        this.status = status;
    }

    public Integer getRoomUserNum() {
        return roomUserNum;
    }

    public void setRoomUserNum(Integer roomUserNum) {
        this.roomUserNum = roomUserNum;
    }

    public Integer getRid() {
        return rid;
    }

    public void setRid(Integer rid) {
        this.rid = rid;
    }

    public Integer getSendGiftUserNum() {
        return sendGiftUserNum;
    }

    public void setSendGiftUserNum(Integer sendGiftUserNum) {
        this.sendGiftUserNum = sendGiftUserNum;
    }

    public String getTotalCost() {
        return totalCost;
    }

    public void setTotalCost(String totalCost) {
        this.totalCost = totalCost;
    }

    public String getRoomHead() {
        return roomHead;
    }

    public void setRoomHead(String roomHead) {
        this.roomHead = roomHead;
    }

    public String getEventCoverUrl() {
        return eventCoverUrl;
    }

    public void setEventCoverUrl(String eventCoverUrl) {
        this.eventCoverUrl = eventCoverUrl;
    }

    public String getAid() {
        return aid;
    }

    public void setAid(String aid) {
        this.aid = aid;
    }

    public Integer getType() {
        return type;
    }

    public void setType(Integer type) {
        this.type = type;
    }

    public String getPosterThumbnails() {
        return posterThumbnails;
    }

    public void setPosterThumbnails(String posterThumbnails) {
        this.posterThumbnails = posterThumbnails;
    }

    public int getEventStatus() {
        return eventStatus;
    }

    public void setEventStatus(int eventStatus) {
        this.eventStatus = eventStatus;
    }

    public EventActorVO getCreator() {
        return creator;
    }

    public void setCreator(EventActorVO creator) {
        this.creator = creator;
    }

    public String getReviewReason() {
        return reviewReason;
    }

    public void setReviewReason(String reviewReason) {
        this.reviewReason = reviewReason;
    }

    public RidData getRidData() {
        return ridData;
    }

    public void setRidData(RidData ridData) {
        this.ridData = ridData;
    }
}
