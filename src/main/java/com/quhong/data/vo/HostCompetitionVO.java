package com.quhong.data.vo;


import com.quhong.mongo.data.ResourceKeyConfigData;

import java.util.List;

public class HostCompetitionVO extends OtherRankConfigVO{
    private String team;     // 1: 老虎队  2:老鹰队  3:狮子队
    private ResourceKeyConfigData joinReward;
    private List<HostRankConfig> totalRankList;  // 总榜
    private List<DailyTeamConfig> dailyReceiveList; // 主播争霸接收日榜
    private List<DailyTeamConfig> dailySendList;    // 主播争霸发送日榜

    public static class DailyTeamConfig {
        private String dateStr;
        private List<OtherRankingListVO> poolDiamondList;   // 奖池进度条
        private List<HostRankConfig> dailyRankConfigList;  // 排行榜

        public String getDateStr() {
            return dateStr;
        }

        public void setDateStr(String dateStr) {
            this.dateStr = dateStr;
        }

        public List<OtherRankingListVO> getPoolDiamondList() {
            return poolDiamondList;
        }

        public void setPoolDiamondList(List<OtherRankingListVO> poolDiamondList) {
            this.poolDiamondList = poolDiamondList;
        }

        public List<HostRankConfig> getDailyRankConfigList() {
            return dailyRankConfigList;
        }

        public void setDailyRankConfigList(List<HostRankConfig> dailyRankConfigList) {
            this.dailyRankConfigList = dailyRankConfigList;
        }
    }

    public static class HostRankConfig {
        private String luckyAnchorHead;
        private String luckyAnchorName;
        private String team;
        private Long teamScore;
        private List<OtherRankingListVO> rankingList;       // 当天日榜
        private OtherRankingListVO myRank;                  // 我的排名

        public String getLuckyAnchorHead() {
            return luckyAnchorHead;
        }

        public void setLuckyAnchorHead(String luckyAnchorHead) {
            this.luckyAnchorHead = luckyAnchorHead;
        }

        public String getLuckyAnchorName() {
            return luckyAnchorName;
        }

        public void setLuckyAnchorName(String luckyAnchorName) {
            this.luckyAnchorName = luckyAnchorName;
        }

        public String getTeam() {
            return team;
        }

        public void setTeam(String team) {
            this.team = team;
        }

        public Long getTeamScore() {
            return teamScore;
        }

        public void setTeamScore(Long teamScore) {
            this.teamScore = teamScore;
        }

        public List<OtherRankingListVO> getRankingList() {
            return rankingList;
        }

        public void setRankingList(List<OtherRankingListVO> rankingList) {
            this.rankingList = rankingList;
        }

        public OtherRankingListVO getMyRank() {
            return myRank;
        }

        public void setMyRank(OtherRankingListVO myRank) {
            this.myRank = myRank;
        }
    }

    public String getTeam() {
        return team;
    }

    public void setTeam(String team) {
        this.team = team;
    }

    public ResourceKeyConfigData getJoinReward() {
        return joinReward;
    }

    public void setJoinReward(ResourceKeyConfigData joinReward) {
        this.joinReward = joinReward;
    }

    public List<HostRankConfig> getTotalRankList() {
        return totalRankList;
    }

    public void setTotalRankList(List<HostRankConfig> totalRankList) {
        this.totalRankList = totalRankList;
    }

    public List<DailyTeamConfig> getDailyReceiveList() {
        return dailyReceiveList;
    }

    public void setDailyReceiveList(List<DailyTeamConfig> dailyReceiveList) {
        this.dailyReceiveList = dailyReceiveList;
    }

    public List<DailyTeamConfig> getDailySendList() {
        return dailySendList;
    }

    public void setDailySendList(List<DailyTeamConfig> dailySendList) {
        this.dailySendList = dailySendList;
    }
}
