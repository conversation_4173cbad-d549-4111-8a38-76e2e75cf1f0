package com.quhong.data.vo;

/**
 * <AUTHOR>
 * @date 2022/12/30
 */
public class QuizGainRewardVO {

    /**
     * 活动id
     */
    private Integer activityId;

    /**
     * 所属关卡（0表示非闯关答题奖励）
     */
    private Integer checkpointNo;

    /**
     * 资源id
     */
    private Integer sourceId;

    /**
     * 奖励类型
     */
    private String rewardType;

    /**
     * 奖励名称
     */
    private String name;

    /**
     * 奖励名称阿语
     */
    private String nameAr;

    /**
     * 奖励图片url
     */
    private String rewardIcon;

    /**
     * 奖励数量
     */
    private Integer rewardNum;

    /**
     * 奖励有效时间
     */
    private Integer rewardTimes;

    /**
     * 显示时间或天数 0显示时间 1显示天数
     */
    private Integer showTimeOrNum;

    public Integer getActivityId() {
        return activityId;
    }

    public void setActivityId(Integer activityId) {
        this.activityId = activityId;
    }

    public Integer getCheckpointNo() {
        return checkpointNo;
    }

    public void setCheckpointNo(Integer checkpointNo) {
        this.checkpointNo = checkpointNo;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getNameAr() {
        return nameAr;
    }

    public void setNameAr(String nameAr) {
        this.nameAr = nameAr;
    }

    public String getRewardIcon() {
        return rewardIcon;
    }

    public void setRewardIcon(String rewardIcon) {
        this.rewardIcon = rewardIcon;
    }

    public Integer getRewardNum() {
        return rewardNum;
    }

    public void setRewardNum(Integer rewardNum) {
        this.rewardNum = rewardNum;
    }

    public Integer getRewardTimes() {
        return rewardTimes;
    }

    public void setRewardTimes(Integer rewardTimes) {
        this.rewardTimes = rewardTimes;
    }

    public Integer getShowTimeOrNum() {
        return showTimeOrNum;
    }

    public void setShowTimeOrNum(Integer showTimeOrNum) {
        this.showTimeOrNum = showTimeOrNum;
    }

    public Integer getSourceId() {
        return sourceId;
    }

    public void setSourceId(Integer sourceId) {
        this.sourceId = sourceId;
    }

    public String getRewardType() {
        return rewardType;
    }

    public void setRewardType(String rewardType) {
        this.rewardType = rewardType;
    }
}
