package com.quhong.data.vo;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2024/6/25
 */
public class BadgeWallVO {

    private int ownBadgeNum; // 拥有的勋章数
    private int allBadgeNum; // 总的勋章数
    private List<BadgeDetailVO> list; // 勋章列表
    private String nextUrl; // 下一页

    public int getOwnBadgeNum() {
        return ownBadgeNum;
    }

    public void setOwnBadgeNum(int ownBadgeNum) {
        this.ownBadgeNum = ownBadgeNum;
    }

    public int getAllBadgeNum() {
        return allBadgeNum;
    }

    public void setAllBadgeNum(int allBadgeNum) {
        this.allBadgeNum = allBadgeNum;
    }

    public List<BadgeDetailVO> getList() {
        return list;
    }

    public void setList(List<BadgeDetailVO> list) {
        this.list = list;
    }

    public String getNextUrl() {
        return nextUrl;
    }

    public void setNextUrl(String nextUrl) {
        this.nextUrl = nextUrl;
    }
}
