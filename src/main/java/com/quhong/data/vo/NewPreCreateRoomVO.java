package com.quhong.data.vo;

import com.quhong.room.RoomTags;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2024/5/27
 */
public class NewPreCreateRoomVO {

    private Info chatRoomInfo;
    private Info liveRoomInfo;

    public Info getChatRoomInfo() {
        return chatRoomInfo;
    }

    public void setChatRoomInfo(Info chatRoomInfo) {
        this.chatRoomInfo = chatRoomInfo;
    }

    public Info getLiveRoomInfo() {
        return liveRoomInfo;
    }

    public void setLiveRoomInfo(Info liveRoomInfo) {
        this.liveRoomInfo = liveRoomInfo;
    }

    public static class Info {

        private String roomId; // 还未创建过房间id时，为空
        private String roomName; // 当前房间名字
        private String roomHead; // 当前房间头像
        private String head; // 用户头像
        private Integer tagId; // 标签id
        private String tagName; // 标签名
        private Integer beautySwitch; // 美颜开关 0关 1开 （直播房才有）
        private byte[] beautyFileBytecode; // 美颜文件字节码
        private Integer faceAuthStatus; // 人脸验证状态，0，未验证，1已验证
        private int liveAudit; // 直播审核 0不用审核 1需要审核 （直播房才有）
        private int showCameraSwitch; // 显示摄像头开关按钮入口  0 隐藏 1 显示 （直播房才有）
        private List<RoomTags.RoomTag> tags; // 标签列表

        public String getRoomId() {
            return roomId;
        }

        public void setRoomId(String roomId) {
            this.roomId = roomId;
        }

        public String getRoomName() {
            return roomName;
        }

        public void setRoomName(String roomName) {
            this.roomName = roomName;
        }

        public String getRoomHead() {
            return roomHead;
        }

        public void setRoomHead(String roomHead) {
            this.roomHead = roomHead;
        }

        public String getHead() {
            return head;
        }

        public void setHead(String head) {
            this.head = head;
        }

        public Integer getTagId() {
            return tagId;
        }

        public void setTagId(Integer tagId) {
            this.tagId = tagId;
        }

        public String getTagName() {
            return tagName;
        }

        public void setTagName(String tagName) {
            this.tagName = tagName;
        }

        public Integer getBeautySwitch() {
            return beautySwitch;
        }

        public void setBeautySwitch(Integer beautySwitch) {
            this.beautySwitch = beautySwitch;
        }

        public Integer getFaceAuthStatus() {
            return faceAuthStatus;
        }

        public void setFaceAuthStatus(Integer faceAuthStatus) {
            this.faceAuthStatus = faceAuthStatus;
        }

        public List<RoomTags.RoomTag> getTags() {
            return tags;
        }

        public void setTags(List<RoomTags.RoomTag> tags) {
            this.tags = tags;
        }

        public int getLiveAudit() {
            return liveAudit;
        }

        public void setLiveAudit(int liveAudit) {
            this.liveAudit = liveAudit;
        }

        public int getShowCameraSwitch() {
            return showCameraSwitch;
        }

        public void setShowCameraSwitch(int showCameraSwitch) {
            this.showCameraSwitch = showCameraSwitch;
        }

        public byte[] getBeautyFileBytecode() {
            return beautyFileBytecode;
        }

        public void setBeautyFileBytecode(byte[] beautyFileBytecode) {
            this.beautyFileBytecode = beautyFileBytecode;
        }
    }
}
