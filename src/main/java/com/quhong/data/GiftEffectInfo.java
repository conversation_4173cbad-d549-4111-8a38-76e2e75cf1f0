package com.quhong.data;

import com.alibaba.fastjson.annotation.JSO<PERSON>ield;

public class GiftEffectInfo {

    @JSONField(name = "other_gift_id")
    private int otherGiftId;
    @JSONField(name = "screen_msg")
    private String screenMsg;
    @JSONField(name = "screen_armsg")
    private String screenArmsg;

    public int getOtherGiftId() {
        return otherGiftId;
    }

    public void setOtherGiftId(int otherGiftId) {
        this.otherGiftId = otherGiftId;
    }

    public String getScreenMsg() {
        return screenMsg;
    }

    public void setScreenMsg(String screenMsg) {
        this.screenMsg = screenMsg;
    }

    public String getScreenArmsg() {
        return screenArmsg;
    }

    public void setScreenArmsg(String screenArmsg) {
        this.screenArmsg = screenArmsg;
    }
}
