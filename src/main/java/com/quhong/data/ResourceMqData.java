package com.quhong.data;

/**
 * 下发资源
 */
public class ResourceMqData {
    protected String uid;
    protected String stype;// 资源类型 gift、mic、buddle、ride、ripple、diamond
    private int source_id;// 资源id
    private int day;// 资源有效期天数
    private String badge_type;// user_level
    private int user_level;// 用户等级

    public ResourceMqData() {
    }

    /**
     * 等级勋章
     */
    public ResourceMqData(String uid, int user_level) {
        this.uid = uid;
        this.stype = "badge";
        this.badge_type = "user_level";
        this.user_level = user_level;
    }

    /**
     * 聊天气泡、麦位框等资源
     */
    public ResourceMqData(String uid, String type, int source_id, int day) {
        this.uid = uid;
        this.stype = type;
        this.source_id = source_id;
        this.day = day;
    }

    public String getUid() {
        return uid;
    }

    public void setUid(String uid) {
        this.uid = uid;
    }

    public String getStype() {
        return stype;
    }

    public void setStype(String stype) {
        this.stype = stype;
    }

    public int getSource_id() {
        return source_id;
    }

    public void setSource_id(int source_id) {
        this.source_id = source_id;
    }

    public int getDay() {
        return day;
    }

    public void setDay(int day) {
        this.day = day;
    }

    public String getBadge_type() {
        return badge_type;
    }

    public void setBadge_type(String badge_type) {
        this.badge_type = badge_type;
    }

    public int getUser_level() {
        return user_level;
    }

    public void setUser_level(int user_level) {
        this.user_level = user_level;
    }

    @Override
    public String toString() {
        return "ResourceMqData{" +
                "uid='" + uid + '\'' +
                ", stype='" + stype + '\'' +
                ", source_id=" + source_id +
                ", day=" + day +
                ", badge_type='" + badge_type + '\'' +
                ", user_level=" + user_level +
                '}';
    }
}
