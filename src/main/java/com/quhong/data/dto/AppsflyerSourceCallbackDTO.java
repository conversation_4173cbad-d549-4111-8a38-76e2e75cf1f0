package com.quhong.data.dto;


/**
 * <AUTHOR>
 * @date 2022/9/17
 */
public class AppsflyerSourceCallbackDTO {

    private String custom_data;
    private String device_category;
    private String customer_user_id;
    private String bundle_id;
    private String event_source;
    private String app_version;
    private String city;
    private String device_model;
    private String selected_currency;
    private String app_name;
    private String install_time_selected_timezone;
    private String postal_code;
    private Boolean wifi;
    private String install_time;
    private String operator;
    private String campaign_type;
    private String campaign;
    private String attributed_touch_time;
    private String device_download_time_selected_timezone;
    private String conversion_type;
    private String api_version;
    private Boolean is_retargeting;
    private String country_code;
    private String appsflyer_id;
    private String dma;
    private String event_revenue_currency;
    private String media_source;
    private String region;
    private String event_value;
    private String ip;
    private String event_time;
    private String state;
    private String device_download_time;
    private String language;
    private String app_id;
    private String carrier;
    private String event_name;
    private String advertising_id;
    private String os_version;
    private String platform;
    private String selected_timezone;
    private String user_agent;
    private Boolean is_primary_attribution;
    private String sdk_version;
    private String event_time_selected_timezone;
    private String gp_referrer;
    private String idfa;
    private String idfv;
    private String resp_body;
    private String oa_id;
    private String af_adset;

    public String getCustom_data() {
        return custom_data;
    }

    public void setCustom_data(String custom_data) {
        this.custom_data = custom_data;
    }

    public String getDevice_category() {
        return device_category;
    }

    public void setDevice_category(String device_category) {
        this.device_category = device_category;
    }

    public String getCustomer_user_id() {
        return customer_user_id;
    }

    public void setCustomer_user_id(String customer_user_id) {
        this.customer_user_id = customer_user_id;
    }

    public String getBundle_id() {
        return bundle_id;
    }

    public void setBundle_id(String bundle_id) {
        this.bundle_id = bundle_id;
    }

    public String getEvent_source() {
        return event_source;
    }

    public void setEvent_source(String event_source) {
        this.event_source = event_source;
    }

    public String getApp_version() {
        return app_version;
    }

    public void setApp_version(String app_version) {
        this.app_version = app_version;
    }

    public String getCity() {
        return city;
    }

    public void setCity(String city) {
        this.city = city;
    }

    public String getDevice_model() {
        return device_model;
    }

    public void setDevice_model(String device_model) {
        this.device_model = device_model;
    }

    public String getSelected_currency() {
        return selected_currency;
    }

    public void setSelected_currency(String selected_currency) {
        this.selected_currency = selected_currency;
    }

    public String getApp_name() {
        return app_name;
    }

    public void setApp_name(String app_name) {
        this.app_name = app_name;
    }

    public String getInstall_time_selected_timezone() {
        return install_time_selected_timezone;
    }

    public void setInstall_time_selected_timezone(String install_time_selected_timezone) {
        this.install_time_selected_timezone = install_time_selected_timezone;
    }

    public String getPostal_code() {
        return postal_code;
    }

    public void setPostal_code(String postal_code) {
        this.postal_code = postal_code;
    }

    public Boolean getWifi() {
        return wifi;
    }

    public void setWifi(Boolean wifi) {
        this.wifi = wifi;
    }

    public String getInstall_time() {
        return install_time;
    }

    public void setInstall_time(String install_time) {
        this.install_time = install_time;
    }

    public String getOperator() {
        return operator;
    }

    public void setOperator(String operator) {
        this.operator = operator;
    }

    public String getCampaign_type() {
        return campaign_type;
    }

    public void setCampaign_type(String campaign_type) {
        this.campaign_type = campaign_type;
    }

    public String getDevice_download_time_selected_timezone() {
        return device_download_time_selected_timezone;
    }

    public void setDevice_download_time_selected_timezone(String device_download_time_selected_timezone) {
        this.device_download_time_selected_timezone = device_download_time_selected_timezone;
    }

    public String getConversion_type() {
        return conversion_type;
    }

    public void setConversion_type(String conversion_type) {
        this.conversion_type = conversion_type;
    }

    public String getApi_version() {
        return api_version;
    }

    public void setApi_version(String api_version) {
        this.api_version = api_version;
    }

    public Boolean getIs_retargeting() {
        return is_retargeting;
    }

    public void setIs_retargeting(Boolean is_retargeting) {
        this.is_retargeting = is_retargeting;
    }

    public String getCountry_code() {
        return country_code;
    }

    public void setCountry_code(String country_code) {
        this.country_code = country_code;
    }

    public String getAppsflyer_id() {
        return appsflyer_id;
    }

    public void setAppsflyer_id(String appsflyer_id) {
        this.appsflyer_id = appsflyer_id;
    }

    public String getDma() {
        return dma;
    }

    public void setDma(String dma) {
        this.dma = dma;
    }

    public String getEvent_revenue_currency() {
        return event_revenue_currency;
    }

    public void setEvent_revenue_currency(String event_revenue_currency) {
        this.event_revenue_currency = event_revenue_currency;
    }

    public String getMedia_source() {
        return media_source;
    }

    public void setMedia_source(String media_source) {
        this.media_source = media_source;
    }

    public String getRegion() {
        return region;
    }

    public void setRegion(String region) {
        this.region = region;
    }

    public String getEvent_value() {
        return event_value;
    }

    public void setEvent_value(String event_value) {
        this.event_value = event_value;
    }

    public String getIp() {
        return ip;
    }

    public void setIp(String ip) {
        this.ip = ip;
    }

    public String getEvent_time() {
        return event_time;
    }

    public void setEvent_time(String event_time) {
        this.event_time = event_time;
    }

    public String getState() {
        return state;
    }

    public void setState(String state) {
        this.state = state;
    }

    public String getDevice_download_time() {
        return device_download_time;
    }

    public void setDevice_download_time(String device_download_time) {
        this.device_download_time = device_download_time;
    }

    public String getLanguage() {
        return language;
    }

    public void setLanguage(String language) {
        this.language = language;
    }

    public String getApp_id() {
        return app_id;
    }

    public void setApp_id(String app_id) {
        this.app_id = app_id;
    }

    public String getCarrier() {
        return carrier;
    }

    public void setCarrier(String carrier) {
        this.carrier = carrier;
    }

    public String getEvent_name() {
        return event_name;
    }

    public void setEvent_name(String event_name) {
        this.event_name = event_name;
    }

    public String getAdvertising_id() {
        return advertising_id;
    }

    public void setAdvertising_id(String advertising_id) {
        this.advertising_id = advertising_id;
    }

    public String getOs_version() {
        return os_version;
    }

    public void setOs_version(String os_version) {
        this.os_version = os_version;
    }

    public String getPlatform() {
        return platform;
    }

    public void setPlatform(String platform) {
        this.platform = platform;
    }

    public String getSelected_timezone() {
        return selected_timezone;
    }

    public void setSelected_timezone(String selected_timezone) {
        this.selected_timezone = selected_timezone;
    }

    public String getUser_agent() {
        return user_agent;
    }

    public void setUser_agent(String user_agent) {
        this.user_agent = user_agent;
    }

    public boolean isIs_primary_attribution() {
        return is_primary_attribution;
    }

    public void setIs_primary_attribution(boolean is_primary_attribution) {
        this.is_primary_attribution = is_primary_attribution;
    }

    public String getSdk_version() {
        return sdk_version;
    }

    public void setSdk_version(String sdk_version) {
        this.sdk_version = sdk_version;
    }

    public String getEvent_time_selected_timezone() {
        return event_time_selected_timezone;
    }

    public void setEvent_time_selected_timezone(String event_time_selected_timezone) {
        this.event_time_selected_timezone = event_time_selected_timezone;
    }

    public String getCampaign() {
        return campaign;
    }

    public void setCampaign(String campaign) {
        this.campaign = campaign;
    }

    public String getAttributed_touch_time() {
        return attributed_touch_time;
    }

    public void setAttributed_touch_time(String attributed_touch_time) {
        this.attributed_touch_time = attributed_touch_time;
    }

    public Boolean getIs_primary_attribution() {
        return is_primary_attribution;
    }

    public void setIs_primary_attribution(Boolean is_primary_attribution) {
        this.is_primary_attribution = is_primary_attribution;
    }

    public String getGp_referrer() {
        return gp_referrer;
    }

    public void setGp_referrer(String gp_referrer) {
        this.gp_referrer = gp_referrer;
    }

    public String getIdfa() {
        return idfa;
    }

    public void setIdfa(String idfa) {
        this.idfa = idfa;
    }

    public String getIdfv() {
        return idfv;
    }

    public void setIdfv(String idfv) {
        this.idfv = idfv;
    }

    public String getResp_body() {
        return resp_body;
    }

    public void setResp_body(String resp_body) {
        this.resp_body = resp_body;
    }

    public String getOa_id() {
        return oa_id;
    }

    public void setOa_id(String oa_id) {
        this.oa_id = oa_id;
    }

    public String getAf_adset() {
        return af_adset;
    }

    public void setAf_adset(String af_adset) {
        this.af_adset = af_adset;
    }
}
