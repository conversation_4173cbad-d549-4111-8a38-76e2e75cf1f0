package com.quhong.data.dto;

import com.quhong.handler.HttpEnvData;


public class LivePkDTO extends HttpEnvData {

    private int pkType; // 0:1V1PK，1:TeamPK(2V2)

    public int getPkType() {
        return pkType;
    }

    public void setPkType(int pkType) {
        this.pkType = pkType;
    }

    public static class Start extends LivePkDTO {

        private String pkId; // pkId
        private int pkTime; // pk时长(分钟)

        public String getPkId() {
            return pkId;
        }

        public void setPkId(String pkId) {
            this.pkId = pkId;
        }

        public int getPkTime() {
            return pkTime;
        }

        public void setPkTime(int pkTime) {
            this.pkTime = pkTime;
        }
    }

    public static class Invite extends LivePkDTO {
        private int type; // 1邀请，2取消，3接受，4普通邀请拒绝，5匹配邀请拒绝（不会触发拒绝消息），6匹配拒绝邀请并关闭自动匹配，7普通邀请拒绝并勿扰，8邀请加入队伍，9接受加入队伍，10拒绝加入队伍
        private String aid; // 被邀请用户，type=1、2时不能为空
        private String pkId; // type=8、9时不能为空

        public int getType() {
            return type;
        }

        public void setType(int type) {
            this.type = type;
        }

        public String getAid() {
            return aid;
        }

        public void setAid(String aid) {
            this.aid = aid;
        }

        public String getPkId() {
            return pkId;
        }

        public void setPkId(String pkId) {
            this.pkId = pkId;
        }
    }

    public static class Close extends LivePkDTO {
        private String pkId;

        public String getPkId() {
            return pkId;
        }

        public void setPkId(String pkId) {
            this.pkId = pkId;
        }
    }

    public static class Match extends LivePkDTO {
        private int type; // 1开始匹配，2取消匹配

        public int getType() {
            return type;
        }

        public void setType(int type) {
            this.type = type;
        }
    }

    public static class Setting extends LivePkDTO {
        private Integer pkTime; // 不为空时进行修改，pk时长(分钟)
        private Integer autoMatch; // 不为空时进行修改，是否开启自动匹配，1开启
        private Integer recommend; // 不为空时进行修改，允许被推荐给其他主播，1开启

        public Integer getPkTime() {
            return pkTime;
        }

        public void setPkTime(Integer pkTime) {
            this.pkTime = pkTime;
        }

        public Integer getAutoMatch() {
            return autoMatch;
        }

        public void setAutoMatch(Integer autoMatch) {
            this.autoMatch = autoMatch;
        }

        public Integer getRecommend() {
            return recommend;
        }

        public void setRecommend(Integer recommend) {
            this.recommend = recommend;
        }
    }

    public static class Contribution extends PageDTO {
        private String pkId;
        private String teamUid; // 本房间队伍传本房主uid 对方房间队伍传对方主播uid

        public String getPkId() {
            return pkId;
        }

        public void setPkId(String pkId) {
            this.pkId = pkId;
        }

        public String getTeamUid() {
            return teamUid;
        }

        public void setTeamUid(String teamUid) {
            this.teamUid = teamUid;
        }
    }

    public static class Mute extends HttpEnvData {
        private String pkId;
        private String aid; // 被操作主播
        private int opt; // 1静音 2取消静音

        public String getPkId() {
            return pkId;
        }

        public void setPkId(String pkId) {
            this.pkId = pkId;
        }

        public String getAid() {
            return aid;
        }

        public void setAid(String aid) {
            this.aid = aid;
        }

        public int getOpt() {
            return opt;
        }

        public void setOpt(int opt) {
            this.opt = opt;
        }
    }
}
