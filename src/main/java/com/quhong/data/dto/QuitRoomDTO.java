package com.quhong.data.dto;

import com.quhong.handler.HttpEnvData;

public class QuitRoomDTO extends HttpEnvData {
    private String ip;
    private long requestTime;
    /**
     * 1 Live-Follow页面 关注部分进入
     * 2 Live-Follow页面 推荐部分进入
     * 3 Live-Trend页面进入
     * 4 Live-New页面进入
     * 5 从用户个人主页进入
     * 6 搜索结果进入
     * 7 搜索页热度曝光进入
     * 8 从私聊页右上角进入房间
     * 9 退出直播间时的推荐弹窗进入
     * 10 直播间的分享链接进入
     * 11 通过礼物广播跳转进入直播间
     * 12 从其他直播间返回
     * 13 从朋友圈进去
     * 14 从 红包今日进入
     * 15 结束直播页面的推荐列表进入"
     */
    private int enterLiveSource;
    private int reason; // 1关闭退出直播间，2主播下播，3上下滑动切换，4点击广播，5杀app进程，6网络异常
    private int robot; // 机器人 0否 1是

    public String getIp() {
        return ip;
    }

    public void setIp(String ip) {
        this.ip = ip;
    }

    public long getRequestTime() {
        return requestTime;
    }

    public void setRequestTime(long requestTime) {
        this.requestTime = requestTime;
    }

    public int getEnterLiveSource() {
        return enterLiveSource;
    }

    public void setEnterLiveSource(int enterLiveSource) {
        this.enterLiveSource = enterLiveSource;
    }

    public int getReason() {
        return reason;
    }

    public void setReason(int reason) {
        this.reason = reason;
    }

    public int getRobot() {
        return robot;
    }

    public void setRobot(int robot) {
        this.robot = robot;
    }
}
