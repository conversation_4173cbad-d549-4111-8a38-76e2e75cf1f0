package com.quhong.data.dto;

import com.quhong.handler.HttpEnvData;

import java.util.List;
import java.util.Set;

public class SendGiftDTO extends HttpEnvData {

    private String aid; //sendType=2时，礼物接收方uid
    private int giftId; // 礼物id
    private int number; // 礼物数量
    private int sendType; // 1指定用户发送，2一对一礼物发送（私信礼物），3全房间发送
    private int isAllMic; // (sendType=1时区分) 1发送给全麦位用户 2单个用户 4多个用户
    private int singleType; // (sendType=2时区分) 0私信礼物 1朋友圈礼物打赏
    private int backpack; // 是否背包礼物 1是
    private Set<String> aidSet; // 麦位上面的人 可以为空(sendType=1时不能为空)
    private String mid; // sendType=1朋友圈礼物打赏时的朋友圈id
    private String ip; // 客户端请求的ip地址(后台赋值)
    private String batterId; // 连击id
    private String reqId;
    private List<Mystery> mysteryList; // 神秘人

    public static class Mystery {
        private String aid; // 神秘人uid
        private String mysteryName; // 神秘人名字

        public String getAid() {
            return aid;
        }

        public void setAid(String aid) {
            this.aid = aid;
        }

        public String getMysteryName() {
            return mysteryName;
        }

        public void setMysteryName(String mysteryName) {
            this.mysteryName = mysteryName;
        }
    }

    public String getAid() {
        return aid;
    }

    public void setAid(String aid) {
        this.aid = aid;
    }

    public int getGiftId() {
        return giftId;
    }

    public void setGiftId(int giftId) {
        this.giftId = giftId;
    }

    public int getNumber() {
        return number;
    }

    public void setNumber(int number) {
        this.number = number;
    }

    public int getSendType() {
        return sendType;
    }

    public void setSendType(int sendType) {
        this.sendType = sendType;
    }

    public int getIsAllMic() {
        return isAllMic;
    }

    public void setIsAllMic(int isAllMic) {
        this.isAllMic = isAllMic;
    }

    public int getSingleType() {
        return singleType;
    }

    public void setSingleType(int singleType) {
        this.singleType = singleType;
    }

    public int getBackpack() {
        return backpack;
    }

    public void setBackpack(int backpack) {
        this.backpack = backpack;
    }

    public Set<String> getAidSet() {
        return aidSet;
    }

    public void setAidSet(Set<String> aidSet) {
        this.aidSet = aidSet;
    }

    public String getMid() {
        return mid;
    }

    public void setMid(String mid) {
        this.mid = mid;
    }

    public String getIp() {
        return ip;
    }

    public void setIp(String ip) {
        this.ip = ip;
    }

    public String getBatterId() {
        return batterId;
    }

    public void setBatterId(String batterId) {
        this.batterId = batterId;
    }

    public String getReqId() {
        return reqId;
    }

    public void setReqId(String reqId) {
        this.reqId = reqId;
    }

    public List<Mystery> getMysteryList() {
        return mysteryList;
    }

    public void setMysteryList(List<Mystery> mysteryList) {
        this.mysteryList = mysteryList;
    }
}
