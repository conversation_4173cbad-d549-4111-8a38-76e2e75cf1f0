package com.quhong.data.dto;

import com.quhong.handler.HttpEnvData;
import com.quhong.mongo.data.MomentData;

import java.util.List;

public class CommentDTO extends HttpEnvData {
    private String mid; // 朋友圈id
    private String content;
    private String reply_to;
    private String comment_id;
    private int page; // 分页评论详情时不能为0
    private int opt; // 1点赞 0点赞
    private int sortBy; // 0按照热度 1按照时间降序
    private List<MomentData.AtUser> at_list; // @用户列表

    public String getMid() {
        return mid;
    }

    public void setMid(String mid) {
        this.mid = mid;
    }

    public String getContent() {
        return content;
    }

    public void setContent(String content) {
        this.content = content;
    }

    public String getReply_to() {
        return reply_to;
    }

    public void setReply_to(String reply_to) {
        this.reply_to = reply_to;
    }

    public String getComment_id() {
        return comment_id;
    }

    public void setComment_id(String comment_id) {
        this.comment_id = comment_id;
    }

    public int getPage() {
        return page;
    }

    public void setPage(int page) {
        this.page = page;
    }

    public int getOpt() {
        return opt;
    }

    public void setOpt(int opt) {
        this.opt = opt;
    }

    public int getSortBy() {
        return sortBy;
    }

    public void setSortBy(int sortBy) {
        this.sortBy = sortBy;
    }

    public List<MomentData.AtUser> getAt_list() {
        return at_list;
    }

    public void setAt_list(List<MomentData.AtUser> at_list) {
        this.at_list = at_list;
    }
}
