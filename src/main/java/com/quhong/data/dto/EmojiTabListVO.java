package com.quhong.data.dto;

import java.util.ArrayList;
import java.util.List;


public class EmojiTabListVO {
    private List<EmojiTabVO> list = new ArrayList<>();

    public static class EmojiTabVO {
        private int svip;// svip专属tab
        private String categoryIconUrl;     // 表情Tab图标
        private List<EmojiMsgPanel> emojiPanel = new ArrayList<>();     // 图片表情Tab图标

        public int getSvip() {
            return svip;
        }

        public void setSvip(int svip) {
            this.svip = svip;
        }

        public String getCategoryIconUrl() {
            return categoryIconUrl;
        }

        public void setCategoryIconUrl(String categoryIconUrl) {
            this.categoryIconUrl = categoryIconUrl;
        }

        public List<EmojiMsgPanel> getEmojiPanel() {
            return emojiPanel;
        }

        public void setEmojiPanel(List<EmojiMsgPanel> emojiPanel) {
            this.emojiPanel = emojiPanel;
        }
    }

    public static class EmojiMsgPanel {
        /**
         * 预览图
         */
        private String icon;

        /**
         * 表情文件
         */
        private String iconFile;

        /**
         * 次序
         */
        private int order;

        /**
         * 循环次数
         */
        private int cycles;

        /**
         * 自定义名称
         */
        private String name;

        private int type; // 分类 0小黄脸、1马、2鹦鹉、3狮子
        private int svipLevel; // 0免费，其他对应svip等级

        public String getIcon() {
            return icon;
        }

        public void setIcon(String icon) {
            this.icon = icon;
        }

        public String getIconFile() {
            return iconFile;
        }

        public void setIconFile(String iconFile) {
            this.iconFile = iconFile;
        }

        public int getOrder() {
            return order;
        }

        public void setOrder(int order) {
            this.order = order;
        }

        public int getCycles() {
            return cycles;
        }

        public void setCycles(int cycles) {
            this.cycles = cycles;
        }

        public String getName() {
            return name;
        }

        public void setName(String name) {
            this.name = name;
        }

        public int getType() {
            return type;
        }

        public void setType(int type) {
            this.type = type;
        }

        public int getSvipLevel() {
            return svipLevel;
        }

        public void setSvipLevel(int svipLevel) {
            this.svipLevel = svipLevel;
        }
    }

    public List<EmojiTabVO> getList() {
        return list;
    }

    public void setList(List<EmojiTabVO> list) {
        this.list = list;
    }
}
