package com.quhong.data.dto;

import com.quhong.handler.HttpEnvData;

public class CoinSellerDTO extends HttpEnvData {

    public static class Manage extends HttpEnvData {
        private String rid; // 用户id
        private String aid; // 充值用户uid
        private int page; // 页码，从第一页开始

        public String getRid() {
            return rid;
        }

        public void setRid(String rid) {
            this.rid = rid;
        }

        public String getAid() {
            return aid;
        }

        public void setAid(String aid) {
            this.aid = aid;
        }

        public int getPage() {
            return page;
        }

        public void setPage(int page) {
            this.page = page;
        }
    }

    public static class Search extends HttpEnvData {
        private String rid; // 搜索用户的rid

        public String getRid() {
            return rid;
        }

        public void setRid(String rid) {
            this.rid = rid;
        }
    }

    public static class Recharge extends HttpEnvData {

        private String aid; // 充值用户uid
        private int rechargeCoins; // 充值金币数
        private int type; // 1给用户充值 2转账给二级币商

        public String getAid() {
            return aid;
        }

        public void setAid(String aid) {
            this.aid = aid;
        }

        public int getRechargeCoins() {
            return rechargeCoins;
        }

        public void setRechargeCoins(int rechargeCoins) {
            this.rechargeCoins = rechargeCoins;
        }

        public int getType() {
            return type;
        }

        public void setType(int type) {
            this.type = type;
        }
    }

    public static class History extends HttpEnvData {
        private int page; // 页码，从第一页开始
        private int startTime;
        private int endTime;
        private String rid; // 搜索用户的rid

        public int getPage() {
            return page;
        }

        public void setPage(int page) {
            this.page = page;
        }

        public int getStartTime() {
            return startTime;
        }

        public void setStartTime(int startTime) {
            this.startTime = startTime;
        }

        public int getEndTime() {
            return endTime;
        }

        public void setEndTime(int endTime) {
            this.endTime = endTime;
        }

        public String getRid() {
            return rid;
        }

        public void setRid(String rid) {
            this.rid = rid;
        }
    }
}
