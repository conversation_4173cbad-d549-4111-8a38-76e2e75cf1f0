package com.quhong.data.dto;

import com.quhong.handler.HttpEnvData;
import org.springframework.util.ObjectUtils;

public class FixedPageDTO extends HttpEnvData {

    @Deprecated
    private String room_id;
    private int page;

    @Deprecated
    public String getRoom_id() {
        return !ObjectUtils.isEmpty(roomId) ? roomId : room_id;
    }

    @Deprecated
    public void setRoom_id(String room_id) {
        this.room_id = room_id;
        this.roomId = room_id;
    }

    public int getPage() {
        return page;
    }

    public void setPage(int page) {
        this.page = page;
    }
}
