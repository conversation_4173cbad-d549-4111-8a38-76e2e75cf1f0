package com.quhong.data.dto;

import com.quhong.handler.HttpEnvData;

public class PartyListDTO extends HttpEnvData {
    private int page;
    private String lang;
    /**
     * partyList通用接口使用，new、live、game:[gameType]、family:[familyRid]等
     *
     * @see com.quhong.constant.PartyList
     */
    private String key;

    private String token;

    public int getPage() {
        return page;
    }

    public void setPage(int page) {
        this.page = page;
    }

    public String getLang() {
        return lang;
    }

    public void setLang(String lang) {
        this.lang = lang;
    }

    public String getKey() {
        return key;
    }

    public void setKey(String key) {
        this.key = key;
    }

    public String getToken() {
        return token;
    }

    public void setToken(String token) {
        this.token = token;
    }
}
