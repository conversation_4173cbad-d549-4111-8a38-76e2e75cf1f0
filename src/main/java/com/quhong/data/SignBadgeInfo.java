package com.quhong.data;


import com.quhong.core.config.ServerConfig;

public class SignBadgeInfo {

    private Integer signCount;
    private Integer lastSignCount;
    private Integer badgeProdId;
    private Integer badgeTestId;

    public Integer getSignCount() {
        return signCount;
    }

    public void setSignCount(Integer signCount) {
        this.signCount = signCount;
    }

    public Integer getLastSignCount() {
        return lastSignCount;
    }

    public void setLastSignCount(Integer lastSignCount) {
        this.lastSignCount = lastSignCount;
    }

    public Integer getBadgeId() {
        if(ServerConfig.isProduct()) {
            return badgeProdId;
        } else {
            return badgeTestId;
        }
    }

    public Integer getBadgeProdId() {
        return badgeProdId;
    }

    public void setBadgeProdId(Integer badgeProdId) {
        this.badgeProdId = badgeProdId;
    }

    public Integer getBadgeTestId() {
        return badgeTestId;
    }

    public void setBadgeTestId(Integer badgeTestId) {
        this.badgeTestId = badgeTestId;
    }
}
