package com.quhong.vo;

import com.alibaba.fastjson.annotation.JSONField;

/**
 * <AUTHOR>
 * @date 2023/1/13
 */
public class SendLuckyBoxVO {

    @JSONField(name = "box_id")
    private String boxId;

    private Integer beans;

    @JSO<PERSON>ield(name = "box_num")
    private Integer boxNum;

    @JSONField(name = "valid_box")
    private Integer validBox;

    private CheckRoomLuckyBoxVO.BoxInfo boxInfo; // 钻石红包
    private String fromLabel; // 发送者公屏发言标签，例如：d-1,w-2,m-3

    public SendLuckyBoxVO() {
    }

    public SendLuckyBoxVO(String boxId, Integer beans, Integer boxNum, Integer validBox, CheckRoomLuckyBoxVO.BoxInfo boxInfo, String msgLabel) {
        this.boxId = boxId;
        this.beans = beans;
        this.boxNum = boxNum;
        this.validBox = validBox;
        this.boxInfo = boxInfo;
        this.fromLabel = msgLabel;
    }

    public String getBoxId() {
        return boxId;
    }

    public void setBoxId(String boxId) {
        this.boxId = boxId;
    }

    public Integer getBeans() {
        return beans;
    }

    public void setBeans(Integer beans) {
        this.beans = beans;
    }

    public Integer getBoxNum() {
        return boxNum;
    }

    public void setBoxNum(Integer boxNum) {
        this.boxNum = boxNum;
    }

    public Integer getValidBox() {
        return validBox;
    }

    public void setValidBox(Integer validBox) {
        this.validBox = validBox;
    }

    public CheckRoomLuckyBoxVO.BoxInfo getBoxInfo() {
        return boxInfo;
    }

    public void setBoxInfo(CheckRoomLuckyBoxVO.BoxInfo boxInfo) {
        this.boxInfo = boxInfo;
    }

    public String getFromLabel() {
        return fromLabel;
    }

    public void setFromLabel(String fromLabel) {
        this.fromLabel = fromLabel;
    }
}
