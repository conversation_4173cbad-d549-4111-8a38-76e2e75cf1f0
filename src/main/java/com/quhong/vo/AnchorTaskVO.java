package com.quhong.vo;

import com.quhong.data.AnchorTaskData;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2024/4/11
 */
public class AnchorTaskVO {

    /**
     * 任务列表
     */
    private List<AnchorTaskData> taskList;

    /**
     * 新主播任务
     */
    private AnchorTaskData newAnchorTask;

    /**
     * 直播任务列表
     */
    private List<AnchorTaskData> liveTaskList;

    /**
     * 新主播直播任务
     */
    private List<AnchorTaskData> newAnchorLiveTask;

    public List<AnchorTaskData> getTaskList() {
        return taskList;
    }

    public void setTaskList(List<AnchorTaskData> taskList) {
        this.taskList = taskList;
    }

    public AnchorTaskData getNewAnchorTask() {
        return newAnchorTask;
    }

    public void setNewAnchorTask(AnchorTaskData newAnchorTask) {
        this.newAnchorTask = newAnchorTask;
    }

    public List<AnchorTaskData> getLiveTaskList() {
        return liveTaskList;
    }

    public void setLiveTaskList(List<AnchorTaskData> liveTaskList) {
        this.liveTaskList = liveTaskList;
    }

    public List<AnchorTaskData> getNewAnchorLiveTask() {
        return newAnchorLiveTask;
    }

    public void setNewAnchorLiveTask(List<AnchorTaskData> newAnchorLiveTask) {
        this.newAnchorLiveTask = newAnchorLiveTask;
    }
}
