package com.quhong.vo;


import com.alibaba.fastjson.JSONObject;
import com.quhong.mongo.data.CarnivalGameData;

import java.util.List;

public class StarBeatGameVO {
    private Integer isShowTicket; // 1 显示抽奖卷
    private Integer currencyTicket; // 当前抽奖卷
    private Integer currencyLuckyStar; // 当前幸运值
    private Integer maxLuckyStar; // 当前最大幸运值 为0不展示幸运值相关
    // 奖品配置
    private List<PrizeConfigVO> prizeConfigList;
    // 抽奖费用配置 ，从小到大排序
    private List<CarnivalGameData.PrizeFree> prizeFreeList;
    // 滚屏
    private List<UserVO> rollList;

    // 本周榜单结束时间，rank接口返回
    private Integer rankEndTime;
    // 排行榜
    private List<UserVO> rankList;
    // 我的排行数据
    private UserVO myRank;

    // 中奖记录
    private List<UserVO> historyList;

    // 抽奖卷icon url
    private String ticketUrl;

    // 用户头像
    private String head ;

    // 中出最大展示价值奖品id
    private String maxValuePrizeId;

    public static class PrizeConfigVO {
        private String parentId;             // 父id, 礼盒使用 0: 普通奖品 0
        private String prizeId;              // 自增配置id
        private Integer prizeValue;           // 奖品价值
        private String resourceNameEn;    // 奖品名称英语
        private String resourceNameAr;    // 奖品名称阿语
        private String resourceIcon;      // 奖品图标
        private String resourcePreview;      // 奖品预览图
        private Integer resourceId;           // 奖品资源id
        private Integer resourceType;         // 奖品类型  -1:礼盒类型 其余type见BaseDataResourcesConstant
        private Integer resourceTime;         // 奖品奖励时长
        private Integer resourceNum;          // 奖品奖励数量
        private Integer markType;             // 角标 0: 无 1: 新品 2: 稀有
        private Integer orderNum;             // 排序
        private Integer awardNum;            // 奖励数量 抽奖接口返回
        private Integer awardType;          // 奖励类型 0 普通 1 幸运一击
        private List<PrizeConfigVO> prizeConfigBoxList; // 礼盒明细 基本信息接口返回

        public String getParentId() {
            return parentId;
        }

        public void setParentId(String parentId) {
            this.parentId = parentId;
        }

        public String getPrizeId() {
            return prizeId;
        }

        public void setPrizeId(String prizeId) {
            this.prizeId = prizeId;
        }

        public Integer getPrizeValue() {
            return prizeValue;
        }

        public void setPrizeValue(Integer prizeValue) {
            this.prizeValue = prizeValue;
        }

        public String getResourceNameEn() {
            return resourceNameEn;
        }

        public void setResourceNameEn(String resourceNameEn) {
            this.resourceNameEn = resourceNameEn;
        }

        public String getResourceNameAr() {
            return resourceNameAr;
        }

        public void setResourceNameAr(String resourceNameAr) {
            this.resourceNameAr = resourceNameAr;
        }

        public String getResourceIcon() {
            return resourceIcon;
        }

        public void setResourceIcon(String resourceIcon) {
            this.resourceIcon = resourceIcon;
        }

        public Integer getResourceId() {
            return resourceId;
        }

        public void setResourceId(Integer resourceId) {
            this.resourceId = resourceId;
        }

        public Integer getResourceType() {
            return resourceType;
        }

        public void setResourceType(Integer resourceType) {
            this.resourceType = resourceType;
        }

        public Integer getResourceTime() {
            return resourceTime;
        }

        public void setResourceTime(Integer resourceTime) {
            this.resourceTime = resourceTime;
        }

        public Integer getResourceNum() {
            return resourceNum;
        }

        public void setResourceNum(Integer resourceNum) {
            this.resourceNum = resourceNum;
        }

        public Integer getMarkType() {
            return markType;
        }

        public void setMarkType(Integer markType) {
            this.markType = markType;
        }

        public Integer getOrderNum() {
            return orderNum;
        }

        public void setOrderNum(Integer orderNum) {
            this.orderNum = orderNum;
        }

        public String getResourcePreview() {
            return resourcePreview;
        }

        public void setResourcePreview(String resourcePreview) {
            this.resourcePreview = resourcePreview;
        }

        public Integer getAwardNum() {
            return awardNum;
        }

        public void setAwardNum(Integer awardNum) {
            this.awardNum = awardNum;
        }

        public Integer getAwardType() {
            return awardType;
        }

        public void setAwardType(Integer awardType) {
            this.awardType = awardType;
        }

        public List<PrizeConfigVO> getPrizeConfigBoxList() {
            return prizeConfigBoxList;
        }

        public void setPrizeConfigBoxList(List<PrizeConfigVO> prizeConfigBoxList) {
            this.prizeConfigBoxList = prizeConfigBoxList;
        }

        @Override
        public String toString() {
            return JSONObject.toJSONString(this);
        }
    }

    public static class UserVO {
        // 滚屏
        private String name; // 用户名称
        private String prizeNameEn; // 奖品名称
        private String prizeNameAr; // 奖品名称
        private Integer prizePrice; // 价格

        // 排行榜
        private String uid;
        private String head;
        private Integer score;
        private Integer rank; // 30名之外为-1


        // 中奖历史记录
        private Integer ctime;
        private String prizeIcon; // 奖品图标
        private String prizeId;
        private String drawId; // 本次抽奖id，10连抽用一个id
        private Integer useTicket; // 本次抽奖使用的卷数
        private Integer useBeans; // 本次抽奖使用的钻石数
        private String ticketUrl;

        public String getName() {
            return name;
        }

        public void setName(String name) {
            this.name = name;
        }

        public String getPrizeNameEn() {
            return prizeNameEn;
        }

        public void setPrizeNameEn(String prizeNameEn) {
            this.prizeNameEn = prizeNameEn;
        }

        public String getPrizeNameAr() {
            return prizeNameAr;
        }

        public void setPrizeNameAr(String prizeNameAr) {
            this.prizeNameAr = prizeNameAr;
        }

        public Integer getPrizePrice() {
            return prizePrice;
        }

        public void setPrizePrice(Integer prizePrice) {
            this.prizePrice = prizePrice;
        }

        public String getUid() {
            return uid;
        }

        public void setUid(String uid) {
            this.uid = uid;
        }

        public String getHead() {
            return head;
        }

        public void setHead(String head) {
            this.head = head;
        }

        public Integer getScore() {
            return score;
        }

        public void setScore(Integer score) {
            this.score = score;
        }

        public Integer getRank() {
            return rank;
        }

        public void setRank(Integer rank) {
            this.rank = rank;
        }

        public Integer getCtime() {
            return ctime;
        }

        public void setCtime(Integer ctime) {
            this.ctime = ctime;
        }

        public String getPrizeIcon() {
            return prizeIcon;
        }

        public void setPrizeIcon(String prizeIcon) {
            this.prizeIcon = prizeIcon;
        }

        public String getPrizeId() {
            return prizeId;
        }

        public void setPrizeId(String prizeId) {
            this.prizeId = prizeId;
        }

        public String getDrawId() {
            return drawId;
        }

        public void setDrawId(String drawId) {
            this.drawId = drawId;
        }

        public Integer getUseTicket() {
            return useTicket;
        }

        public void setUseTicket(Integer useTicket) {
            this.useTicket = useTicket;
        }

        public Integer getUseBeans() {
            return useBeans;
        }

        public void setUseBeans(Integer useBeans) {
            this.useBeans = useBeans;
        }

        public String getTicketUrl() {
            return ticketUrl;
        }

        public void setTicketUrl(String ticketUrl) {
            this.ticketUrl = ticketUrl;
        }

        @Override
        public String toString() {
            return JSONObject.toJSONString(this);
        }
    }


    public Integer getIsShowTicket() {
        return isShowTicket;
    }

    public void setIsShowTicket(Integer isShowTicket) {
        this.isShowTicket = isShowTicket;
    }

    public Integer getCurrencyTicket() {
        return currencyTicket;
    }

    public void setCurrencyTicket(Integer currencyTicket) {
        this.currencyTicket = currencyTicket;
    }

    public Integer getCurrencyLuckyStar() {
        return currencyLuckyStar;
    }

    public void setCurrencyLuckyStar(Integer currencyLuckyStar) {
        this.currencyLuckyStar = currencyLuckyStar;
    }

    public Integer getMaxLuckyStar() {
        return maxLuckyStar;
    }

    public void setMaxLuckyStar(Integer maxLuckyStar) {
        this.maxLuckyStar = maxLuckyStar;
    }

    public List<PrizeConfigVO> getPrizeConfigList() {
        return prizeConfigList;
    }

    public void setPrizeConfigList(List<PrizeConfigVO> prizeConfigList) {
        this.prizeConfigList = prizeConfigList;
    }

    public List<CarnivalGameData.PrizeFree> getPrizeFreeList() {
        return prizeFreeList;
    }

    public void setPrizeFreeList(List<CarnivalGameData.PrizeFree> prizeFreeList) {
        this.prizeFreeList = prizeFreeList;
    }

    public List<UserVO> getRollList() {
        return rollList;
    }

    public void setRollList(List<UserVO> rollList) {
        this.rollList = rollList;
    }

    public Integer getRankEndTime() {
        return rankEndTime;
    }

    public void setRankEndTime(Integer rankEndTime) {
        this.rankEndTime = rankEndTime;
    }

    public List<UserVO> getRankList() {
        return rankList;
    }

    public void setRankList(List<UserVO> rankList) {
        this.rankList = rankList;
    }

    public UserVO getMyRank() {
        return myRank;
    }

    public void setMyRank(UserVO myRank) {
        this.myRank = myRank;
    }

    public List<UserVO> getHistoryList() {
        return historyList;
    }

    public void setHistoryList(List<UserVO> historyList) {
        this.historyList = historyList;
    }

    public String getTicketUrl() {
        return ticketUrl;
    }

    public void setTicketUrl(String ticketUrl) {
        this.ticketUrl = ticketUrl;
    }

    public String getHead() {
        return head;
    }

    public void setHead(String head) {
        this.head = head;
    }

    public String getMaxValuePrizeId() {
        return maxValuePrizeId;
    }

    public void setMaxValuePrizeId(String maxValuePrizeId) {
        this.maxValuePrizeId = maxValuePrizeId;
    }
}
