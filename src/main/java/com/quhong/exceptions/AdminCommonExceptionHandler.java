package com.quhong.exceptions;

import com.alibaba.fastjson.JSONObject;
import com.quhong.datas.HttpResult;
import com.quhong.enums.SLangType;
import com.quhong.enums.WebConstant;
import com.quhong.handler.DesController;
import com.quhong.utils.DESEncoderUtils;
import jakarta.annotation.Resource;
import jakarta.servlet.http.HttpServletRequest;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.context.MessageSource;
import org.springframework.util.StringUtils;
import org.springframework.web.bind.annotation.ControllerAdvice;
import org.springframework.web.bind.annotation.ExceptionHandler;
import org.springframework.web.bind.annotation.ResponseBody;

import java.util.Arrays;
import java.util.Locale;

/**
 * 统一封装异常信息输出到前端
 */
@ControllerAdvice
@ResponseBody
public class AdminCommonExceptionHandler {

    private final static Logger logger = LoggerFactory.getLogger(AdminCommonExceptionHandler.class);

    @Resource
    private MessageSource message;

    private Locale getLocal(HttpServletRequest request) {
        String localeValue = (String) request.getAttribute(WebConstant.CLIENT_LANG);
        if (null == localeValue) {
            localeValue = "ar";
            logger.info("clientLang is null. path={}", request.getRequestURI());
        }
        return StringUtils.parseLocale(localeValue);
    }


    @ExceptionHandler(AdminCommonException.class)
    public String handleAdminCommonH5Exception(AdminCommonException e, HttpServletRequest request) {
        String msg = e.getHttpCode().getMsg();
        logger.info("throw common exception. reqUri={} msg={} args={}", request.getRequestURI(), msg, Arrays.toString(e.getArgs()));
        HttpResult<Object> result = new HttpResult<>();
        result.setCode(e.getHttpCode().getCode());
        result.setData(null);
        try {
            result.setMsg(message.getMessage(msg, e.getArgs(), getLocal(request)));
        } catch (Exception ex) {
            result.setMsg(e.getHttpCode().getMsg("ar".equals(getLocal(request).getLanguage()) ? SLangType.ARABIC : SLangType.ENGLISH));
        }
        String encryptParam = "";
        try {
            encryptParam = DESEncoderUtils.encryptParam(JSONObject.toJSONString(result));
        } catch (Exception exc) {
            logger.error("param des encoder error. {}", e.getMessage(), e);
        }
        DesController.TotalData totalData = new DesController.TotalData();
        totalData.setTotalData(encryptParam);
        return JSONObject.toJSONString(totalData);
    }

}
