package com.quhong.managers;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.quhong.analysis.EventDTO;
import com.quhong.analysis.EventReport;
import com.quhong.analysis.MsgRecordEvent;
import com.quhong.constant.DetectOriginConstant;
import com.quhong.constant.MoneyActionType;
import com.quhong.constant.SvipConstant;
import com.quhong.core.concurrency.BaseTaskFactory;
import com.quhong.core.concurrency.tasks.Task;
import com.quhong.core.distribution.DistributeLock;
import com.quhong.core.utils.DateHelper;
import com.quhong.dailyTask.DailyTaskService;
import com.quhong.dailyTask.UserLevelTaskService;
import com.quhong.data.ActorData;
import com.quhong.data.QuoteData;
import com.quhong.data.UserLevelTaskData;
import com.quhong.data.dto.AsyncCharmDTO;
import com.quhong.data.dto.MoneyDetailReq;
import com.quhong.data.dto.MsgSendDTO;
import com.quhong.data.vo.SendMsgVO;
import com.quhong.dto.ImageDTO;
import com.quhong.enums.*;
import com.quhong.exception.CommonException;
import com.quhong.exception.CommonH5Exception;
import com.quhong.fcm.FCMPusher;
import com.quhong.feign.DataCenterService;
import com.quhong.feign.IDetectService;
import com.quhong.image.ImageUrlGenerator;
import com.quhong.mongo.dao.ActorDao;
import com.quhong.mongo.dao.BlackListDao;
import com.quhong.mongo.dao.MsgListDao;
import com.quhong.mongo.dao.UserMonitorDao;
import com.quhong.mongo.data.Actor;
import com.quhong.mongo.data.MsgListData;
import com.quhong.mq.MqSenderService;
import com.quhong.mysql.dao.FriendshipDao;
import com.quhong.mysql.dao.MsgRecordDao;
import com.quhong.mysql.dao.VipInfoDao;
import com.quhong.mysql.dao.WithdrawalOrderDao;
import com.quhong.mysql.data.AnchorCharmLogData;
import com.quhong.mysql.data.FriendshipData;
import com.quhong.mysql.data.MysqlMsgRecordData;
import com.quhong.redis.BlockRedis;
import com.quhong.redis.MsgRedis;
import com.quhong.redis.SvipMsgPrivilegeRedis;
import com.quhong.service.ActorService;
import com.quhong.service.SvipLevelService;
import com.quhong.service.TextDetectionService;
import com.quhong.service.mysql.BadWordService;
import com.quhong.utils.MsgUtils;
import jakarta.annotation.Resource;
import org.bson.types.ObjectId;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.util.ObjectUtils;
import org.springframework.util.StringUtils;

import java.math.BigDecimal;
import java.util.HashMap;
import java.util.HashSet;
import java.util.Map;
import java.util.Set;

@Component
public class SendChatMsgProcessor {
    private static final Logger logger = LoggerFactory.getLogger(SendChatMsgProcessor.class);

    public static final int IMG_LIMIT_SIZE = 100 * 1024;
    public static final int THREE_DAY_TIME = 3 * 24 * 3600;

    public static final int SEND_MSG_FEE = 10; // 发送私信的钻石费用
    public static final int RECEIVE_MSG_CHARM = 5; // 收到私信的魅力值奖励

    private static final String ADD_FRIEND_MSG_EN = "We are friends now. Let's chat!";
    private static final String ADD_FRIEND_MSG_AR = "نحن أصدقاء الآن. دعنا نتحدث!";


    @Autowired
    private UserMonitorDao userMonitorDao;
    @Autowired
    private DailyTaskService dailyTaskService;
    @Autowired
    private UserLevelTaskService userLevelTaskService;
    @Autowired
    private ChatMsgMgr chatMsgMgr;
    @Autowired
    private MsgRecordDao msgRecordDao;
    @Autowired
    private MsgListDao msgListDao;
    @Resource
    private BlackListDao blackListDao;
    @Resource
    private FriendshipDao friendshipDao;
    @Resource
    private ActorDao actorDao;
    @Resource
    private ActorService actorService;
    @Resource
    private BlockRedis blockRedis;
    @Autowired(required = false)
    private EventReport eventReport;
    @Resource
    private BadWordService badWordService;
    @Resource
    private FCMPusher fcmPusher;
    @Resource
    private IDetectService detectService;
    @Resource
    private VipInfoDao vipInfoDao;
    @Resource
    private TextDetectionService textDetectionService;
    @Resource
    private WithdrawalOrderDao withdrawalOrderDao;
    @Resource
    private SvipLevelService svipLevelService;
    @Resource
    private SvipMsgPrivilegeRedis svipMsgPrivilegeRedis;
    @Resource
    private DataCenterService dataCenterService;
    @Resource
    private MqSenderService mqSenderService;
    @Resource
    private MsgRedis msgRedis;

    /**
     * 历史原因。返回异常时不能走加密，需要使用CommonH5Exception
     */
    public SendMsgVO send(MsgSendDTO reqData) {
        String uid = reqData.getUid();
        int msgType = reqData.getMsg_type();
        if (reqData.getMsg_info() == null) {
            reqData.setMsg_info(new JSONObject());
        }
        logger.info("start send chat msg. msgType={} msgBody={} msgInfo={} aid={} uid={} versioncode={}", reqData.msg_type, reqData.msg_body, reqData.msg_info, reqData.aid, reqData.getUid(), reqData.getVersioncode());
        if (reqData.getMsg_body().length() >= 1024) {
            logger.info("send chat msg. msg too long. uid={}", uid);
            reqData.setMsg_body(reqData.getMsg_body().substring(0, 1023) + "...");
        }
        checkBlock(reqData.getUid());
        boolean sendFlag = msgType == MsgType.TEXT || msgType == MsgType.AUDIO || msgType == MsgType.IMAGE;
        // 被运营人员禁言不能发送消息
        if (sendFlag && blockRedis.getBlockUserPrivateMsgStatus(uid) != 0) {
            logger.info("send msg getBlockUserPrivateMsgStatus. uid={} aid={} msg_body={}", reqData.getUid(), reqData.getAid(), reqData.getMsg_body());
            throw new CommonH5Exception(MsgHttpCode.ACCOUNT_HAS_DISABLED);
        }
        if (!userMonitorDao.isSendChatMsg(uid)) {
            logger.info("send chat msg. actor has been frozen. or banned. uid={}", uid);
            throw new CommonH5Exception(HttpCode.USER_MONITOR_FREEZE);
        }
        if (!userMonitorDao.isSendChatMsg(reqData.aid)) {
            logger.info("send chat msg. to user has been frozen. or banned. aid={} uid={}", reqData.aid, uid);
            throw new CommonH5Exception(MsgHttpCode.TO_USER_FREEZE);
        }
        if (blackListDao.isBlock(reqData.aid, uid)) {
            logger.info("send chat msg. they are blocked. aid={} uid={}", reqData.aid, uid);
            throw new CommonH5Exception(MsgHttpCode.BLACK);
        }
        if (reqData.getMsg_type() == MsgType.TEXT && badWordService.isContainBadWord(reqData.getMsg_body(), BadWordService.MIN_MATCH_TYPE)) {
            logger.info("send msg contain dirty text. can not send text msg. uid={} aid={} msg_body={}", reqData.getUid(), reqData.getAid(), reqData.getMsg_body());
            throw new CommonH5Exception(MsgHttpCode.MSG_CONTAINS_BAD_WORD);
        }
        if (!CustomerService.customerSet.contains(reqData.getAid())
                && !CustomerService.customerSet.contains(reqData.getUid())
                && !friendshipDao.isFriend(reqData.getUid(), reqData.getAid())
                && !withdrawalOrderDao.hasWithdrawRecord(reqData.getUid(), reqData.getAid())) {
            // SVIP用户给陌生人发私信
            int svipLevel = svipLevelService.getSvipLevel(reqData.getUid());
            if (svipLevel >= 10 && !reqData.batchSend) {
                int sendPersonCount = svipMsgPrivilegeRedis.getSendPersonCount(reqData.getUid(), svipLevel);
                if (sendPersonCount >= SvipConstant.STRANGER_PERSON.getOrDefault(svipLevel, 0)) {
                    if (!svipMsgPrivilegeRedis.isMember(reqData.getUid(), reqData.getAid(), svipLevel)) {
                        throw new CommonH5Exception(MsgHttpCode.STRANGER_LIMIT);
                    }
                }
                int sendCount = svipMsgPrivilegeRedis.incrSendCount(reqData.getUid(), reqData.getAid(), svipLevel);
                if (sendCount > SvipConstant.STRANGER_MSG_COUNT) {
                    throw new CommonH5Exception(MsgHttpCode.STRANGER_PERSON_LIMIT);
                }
                reqData.setSendStrangerRemain(Math.max(1, SvipConstant.STRANGER_PERSON.getOrDefault(svipLevel, 0) - svipMsgPrivilegeRedis.getSendPersonCount(reqData.getUid(), svipLevel)));
                reqData.setStrangerMsgRemain(SvipConstant.STRANGER_MSG_COUNT - sendCount);
            } else {
                logger.info("send chat msg. they are not friend. aid={} uid={}", reqData.aid, uid);
                throw new CommonH5Exception(MsgHttpCode.NOT_FRIEND);
            }
        }
        boolean payMsg = MsgRedis.ENABLE_PAID_MSG && isPayMsg(reqData);
        reqData.setPayMsg(payMsg);
        if (payMsg) {
            // 扣除钻石
            String msgId = new ObjectId().toString();
            sendMsgCost(uid, reqData.getAid(), msgId, reqData.getMsg_type());
            reqData.setMsgId(msgId);
        }
        // 私聊消息的埋点数据
        MsgRecordEvent event = new MsgRecordEvent();
        event.setUid(reqData.getUid());
        event.setFrom_uid(reqData.getUid());
        event.setTo_uid(reqData.getAid());
        event.setMsg_type(reqData.getMsg_type());
        event.setCtime(DateHelper.getNowSeconds());
        eventReport.track(new EventDTO(event));
        return doSendMsg(reqData);
    }

    private boolean isPayMsg(MsgSendDTO dto) {
        if (CustomerService.customerSet.contains(dto.getUid()) || CustomerService.customerSet.contains(dto.getAid())) {
            // 给官方客服/VIP客服发送消息不需要扣费
            return false;
        }
        if (dto.getMsg_type() == MsgType.GIFT) {
            // 发礼物消息不需要扣费
            return false;
        }
        if (dto.getMsg_type() == MsgType.SHARE_ROOM) {
            // 分享房间消息不需要扣费
            return false;
        }
        if (dto.getMsg_type() == MsgType.SHARE_LIVE_ROOM) {
            // 分享直播消息不需要扣费
            return false;
        }
        if (dto.getMsg_type() == MsgType.SHARE_ACTIVITY) {
            // 分享活动消息不需要扣费
            return false;
        }
        if (dto.getMsg_type() == MsgType.SHARE_FAMILY) {
            // 分享公会消息不需要扣费
            return false;
        }
        if (ADD_FRIEND_MSG_EN.equals(dto.getMsg_body()) || ADD_FRIEND_MSG_AR.equals(dto.getMsg_body())) {
            // 添加好友自动发送的消息不扣费
            return false;
        }
        // SVIP用户前3条消息免费消息
        return actorDao.getActorData(dto.getUid()).getSvipLevel() <= 0 || msgRedis.incSvipMsgCount(dto.getUid()) > 3;
    }

    private void sendMsgCost(String uid, String aid, String msgId, int msgType) {
        MoneyDetailReq moneyDetailReq = new MoneyDetailReq();
        MoneyActionType typeEnum = MoneyActionType.SEND_MSG_FEE;
        moneyDetailReq.setId(msgId);
        moneyDetailReq.setUid(uid);
        moneyDetailReq.setAtype(typeEnum.actionType);
        moneyDetailReq.setChanged(-SEND_MSG_FEE);
        moneyDetailReq.setTitle(getSendMsgTitle(msgType));
        moneyDetailReq.setDesc(typeEnum.desc.formatted(actorDao.getActorDataFromCache(aid).getRid()));
        ApiResult<String> result = dataCenterService.reduceBeans(moneyDetailReq);
        if (!result.isOk()) {
            if (1 == result.getCode().getCode()) {
                throw new CommonException(MsgHttpCode.NOT_ENOUGH_DIAMOND);
            }
            logger.error("reduce beans error, uid={} msg={}", uid, result.getCode().getMsg());
            throw new CommonException(HttpCode.SERVER_ERROR);
        }
        if (msgRedis.incPayMsg(msgId) != 1) {
            logger.error("incPayMsg error. uid={} aid={} msgId={}", uid, aid, msgId);
        }
    }

    private String getSendMsgTitle(int msgType) {
        return switch (msgType) {
            case MsgType.AUDIO -> "Send voice message";
            case MsgType.IMAGE -> "Send photo message";
            case MsgType.VIDEO -> "Send video message";
            default -> "Send text message";
        };
    }

    /**
     * 提供给内部使用
     */
    public SendMsgVO doSendMsg(MsgSendDTO reqData) {
        if (MsgType.TEXT == reqData.getMsg_type()) {
            if (!ObjectUtils.isEmpty(reqData.getQuoteMsgId())) {
                return sendQuoteMsg(reqData);
            }
        }
        return switch (reqData.msg_type) {
            case MsgType.IMAGE -> sendImage(reqData);
            case MsgType.VIDEO -> sendVideo(reqData);
            case MsgType.AUDIO -> sendAudio(reqData);
            default -> doSend(reqData);
        };
    }

    private SendMsgVO sendQuoteMsg(MsgSendDTO reqData) {
        if (ObjectUtils.isEmpty(reqData.getQuoteMsgId())) {
            throw new CommonH5Exception(MsgHttpCode.PARAM_ERROR);
        }
        MysqlMsgRecordData recordData = msgRecordDao.getData(reqData.getQuoteMsgId());
        if (recordData == null) {
            throw new CommonH5Exception(MsgHttpCode.PARAM_ERROR);
        }
        reqData.setMsg_info(JSON.parseObject(JSON.toJSONString(toQuoteData(recordData))));
        return doSend(reqData);
    }

    private QuoteData toQuoteData(MysqlMsgRecordData recordData) {
        QuoteData quoteData = new QuoteData();
        quoteData.setFromUid(recordData.getFromUid());
        quoteData.setType(recordData.getMsgType());
        switch (recordData.getMsgType()) {
            case MsgType.TEXT, MsgType.GIFT, MsgType.IMAGE -> quoteData.setContent(recordData.getMsg());
            case MsgType.AUDIO -> {
                JSONObject jsonObject = JSON.parseObject(recordData.getMsgInfo());
                quoteData.setContent(recordData.getMsg());
                quoteData.setTime(jsonObject.getString("time"));
            }
            default -> throw new CommonH5Exception(MsgHttpCode.PARAM_ERROR);
        }
        return quoteData;
    }

    private SendMsgVO sendAudio(MsgSendDTO reqData) {
        String uid = reqData.getUid();
        String url = getUploadOssUrl(reqData);
        logger.info("send audio. url={}  uid={}", url, uid);
        reqData.setMsg_body(url);
        return doSend(reqData);
    }

    private SendMsgVO doSend(MsgSendDTO reqData) {
        String uid = reqData.getUid();
        int msgType = reqData.getMsg_type();
        MysqlMsgRecordData recordData = saveToDB(reqData);
        BaseTaskFactory.getFactory().addSlow(new Task() {
            @Override
            protected void execute() {
                // 每日任务
                dailyTaskService.sendChatMsg(uid);
                if (msgType != MsgType.GIFT) {
                    // 用户等级任务：给不同好友发送消息
                    userLevelTaskService.sendTaskDataToMq(new UserLevelTaskData(uid, UserLevelConstant.SEND_MSG, reqData.getAid()));
                }
                if (msgType == MsgType.TEXT) {
                    textDetectionService.textAlertCheck(uid, reqData.getAid(), TextDetectionService.PRIVATE_MSG, reqData.getMsg_body(), recordData.getMsgId());
                }
            }
        });
        // 更新msgList
        int unread = updateMsgList(recordData, uid);
        // im发送消息
        chatMsgMgr.sendMsg(recordData, unread, reqData.getSlang());
        if (!reqData.isBatchSend()) {
            sendToFCM(recordData);
        }
        if (reqData.isPayMsg()) {
            sendReceiveCharm(reqData);
        }
        return getMsgRsp(reqData, recordData);
    }

    private void sendReceiveCharm(MsgSendDTO reqData) {
        // 发送收到消息魅力值奖励
        if (msgRedis.decPayMsg(reqData.getMsgId()) == 0) {
            int familyId = actorDao.getActorDataFromCache(reqData.getAid()).getFamilyId();
            String senderRid = actorDao.getActorDataFromCache(reqData.getUid()).getRid() + "";
            AnchorCharmLogData logData = CharmLogTypeEnum.getLogData(familyId, reqData.getAid(), CharmLogTypeEnum.RECEIVE_MSG, senderRid);
            logData.setGameId(reqData.getMsgId());
            logData.setTitle(getReceiveMsgTitle(reqData.getMsg_type()));
            // 异步加魅力值
            mqSenderService.asyncCharm(new AsyncCharmDTO(reqData.getAid(), BigDecimal.valueOf(RECEIVE_MSG_CHARM), logData));
        } else {
            logger.error("decPayMsg error. uid={} aid={} msg={}", reqData.getUid(), reqData.getAid(), reqData.getMsgId());
        }
    }

    private String getReceiveMsgTitle(int msgType) {
        return switch (msgType) {
            case MsgType.AUDIO -> "Receive voice message";
            case MsgType.IMAGE -> "Receive photo message";
            case MsgType.VIDEO -> "Receive video message";
            default -> "Receive text message";
        };
    }

    private SendMsgVO getMsgRsp(MsgSendDTO reqData, MysqlMsgRecordData recordData) {
        SendMsgVO sendMsgVO = new SendMsgVO(recordData.getMsgId());
        if (reqData.getMsg_type() == MsgType.GIFT || reqData.getMsg_type() == MsgType.HEART_GIFT) {
            Actor actor = actorService.getActor(reqData.getUid());
            int beans = actor.getBeans();
            int heart = actor.getHeartGot();
            sendMsgVO.setBeans(beans);
            sendMsgVO.setHeart(heart);
        }
        sendMsgVO.setContent(recordData.getMsg());
        sendMsgVO.setSendStrangerRemain(reqData.getSendStrangerRemain());
        sendMsgVO.setStrangerMsgRemain(reqData.getStrangerMsgRemain());
        return sendMsgVO;
    }

    private int updateMsgList(MysqlMsgRecordData recordData, String uid) {
        int unread = 0;
        try (DistributeLock lock = new DistributeLock(recordData.getMsgIndex())) {
            lock.lock();
            MsgListData msgListData = msgListDao.findMsgListData(recordData.getMsgIndex());
            if (msgListData == null) {
                msgListData = new MsgListData();
                msgListData.setMsg_index(recordData.getMsgIndex());
                msgListData.setA_id(recordData.getFromUid());
                msgListData.setB_id(recordData.getToUid());
            }
            String aid = msgListData.getA_id().equals(uid) ? msgListData.getB_id() : msgListData.getA_id();
            if (msgListData.getA_last_msg() != null && msgListData.getA_last_msg().getFromUid().equals(aid)) {
                replyNewFriendTask(uid, aid);
            }
            // 取消会话删除状态
            msgListData.setA_delete(0);
            msgListData.setB_delete(0);
            if (recordData.getMsgType() == MsgType.AGENT_INVITATION) {
                // 代理邀请主播，邀请消息只有主播收到，代理不应该展示
                int msgCount = msgListDao.getMsgCount(uid, aid);
                if (msgListData.getA_id().equals(uid)) {
                    msgListData.setB_last_msg(recordData);
                    msgListData.setA_delete(msgCount == 0 ? 1 : 0);
                } else {
                    msgListData.setA_last_msg(recordData);
                    msgListData.setB_delete(msgCount == 0 ? 1 : 0);
                }
            } else {
                msgListData.setA_last_msg(recordData);
                msgListData.setB_last_msg(recordData);
            }
            msgListData.setMtime(System.currentTimeMillis());
            if (msgListData.getA_id().equals(recordData.getToUid())) {
                msgListData.setA_unread(msgListData.getA_unread() + 1);
            } else {
                msgListData.setB_unread(msgListData.getB_unread() + 1);
            }
            if (msgListData.getA_id().equals(uid)) {
                unread = msgListData.getB_unread();
            } else {
                unread = msgListData.getA_unread();
            }
            msgListDao.save(msgListData);
        } catch (Exception e) {
            logger.error("update msg list error. msgIndex={} uid={} {}", recordData.getMsgIndex(), recordData.getFromUid(), e.getMessage(), e);
        }
        return unread;
    }

    private void replyNewFriendTask(String uid, String aid) {
        BaseTaskFactory.getFactory().addSlow(new Task() {
            @Override
            protected void execute() {
                FriendshipData friendData = friendshipDao.getFriendDataFromCache(uid, aid);
                if (friendData != null && friendData.getCtime() >= DateHelper.getNowSeconds() - 24 * 60 * 60) {
                    // 用户等级任务：回复新好友消息
                    userLevelTaskService.sendTaskDataToMq(new UserLevelTaskData(uid, UserLevelConstant.REPLY_NEW_FRIEND, aid));
                }
            }

        });
    }

    private boolean isCustomServiceMsg(String uid, String aid) {
        return CustomerService.customerSet.contains(uid) || CustomerService.customerSet.contains(aid);
    }

    private SendMsgVO sendImage(MsgSendDTO reqData) {
        if (!checkAndRepairMsgInfo(reqData)) {
            throw new CommonH5Exception(HttpCode.PARAM_ERROR);
        }
        String uid = reqData.getUid();
        String aid = reqData.getAid();
        String originalUrl = getUploadOssUrl(reqData);
        FriendshipData friendData = friendshipDao.getFriendDataFromCache(uid, aid);
        boolean isSafe = true;
        if (!isCustomServiceMsg(uid, aid) && (friendData == null || (DateHelper.getNowSeconds() - friendData.getCtime() <= THREE_DAY_TIME
                && vipInfoDao.getIntVipLevel(uid) < 2 && vipInfoDao.getIntVipLevel(aid) < 2))) {
            try {
                isSafe = detectService.detectImage(new ImageDTO(originalUrl, DetectOriginConstant.PRIVATE_PICTURE, uid)).getData().getIsSafe() == 1;
            } catch (Exception e) {
                logger.info("");
            }
        }
        if (!isSafe) {
            logger.info("unsafe image isSafe:{} uid:{} friendData:{} originalUrl:{} ", isSafe, uid, friendData, originalUrl);
            throw new CommonH5Exception(MsgHttpCode.MSG_CONTAINS_BAD_IMAGE);
        }
        reqData.setMsg_body(originalUrl);
        int size = reqData.getMsg_info().getIntValue("size");
        if (size > 0 && size <= IMG_LIMIT_SIZE) {
            reqData.msg_info.put("thumbnailUrl", originalUrl);
            logger.info("send image. size={} uid={}", size, uid);
            return doSend(reqData);
        }
        try {
            String url = ImageUrlGenerator.generateMsgChatUrl(reqData.getMsg_body());
            logger.info("send chat image. size={} url={} originalUrl={} uid={}", size, url, reqData.getMsg_body(), uid);
            reqData.setMsg_body(reqData.getMsg_body());
            reqData.msg_info.put("thumbnailUrl", url);
            return doSend(reqData);

        } catch (Exception e) {
            logger.error(e.getMessage(), e);
            reqData.msg_info.put("thumbnailUrl", reqData.msg_body);
            return doSend(reqData);
        }
    }

    private SendMsgVO sendVideo(MsgSendDTO reqData) {
        String originalUrl = getUploadOssUrl(reqData);
        reqData.setMsg_body(originalUrl);
        // 格式化首帧图片
        reqData.msg_info.put("thumbnailUrl", ImageUrlGenerator.generateSnapshotUrl(originalUrl, 0, 0));
        logger.info("send chat video. originalUrl={} uid={}", reqData.getMsg_body(), reqData.getUid());
        return doSend(reqData);
    }

    private boolean checkAndRepairMsgInfo(MsgSendDTO reqData) {
        try {
            JSONObject msgInfo = reqData.getMsg_info();
            msgInfo.put("width", msgInfo.getIntValue("width"));
            msgInfo.put("height", msgInfo.getIntValue("height"));
            return true;
        } catch (Exception e) {
            logger.error("check and repair msg info error. {}", e.getMessage(), e);
        }
        return false;
    }

    private MysqlMsgRecordData saveToDB(MsgSendDTO reqData) {
        String uid = reqData.getUid();
        MysqlMsgRecordData recordData = new MysqlMsgRecordData();
        recordData.setFromUid(uid);
        recordData.setToUid(reqData.aid);
        recordData.setMsgIndex(MsgUtils.generateMsgIndex(uid, reqData.aid));
        recordData.setMsg(reqData.msg_body);
        recordData.setMsgInfo(reqData.msg_info.toJSONString());
        recordData.setMsgId(StringUtils.hasLength(reqData.getMsgId()) ? reqData.getMsgId() : new ObjectId().toString());
        recordData.setTimestamp(System.currentTimeMillis());
        recordData.setMsgType(reqData.msg_type);
        recordData.setFromLike("");
        recordData.setToLike("");
        recordData.setFromDelete(recordData.getMsgType() == MsgType.AGENT_INVITATION ? 1 : 0);
        msgRecordDao.insert(recordData);
        return recordData;
    }

    private void sendToFCM(MysqlMsgRecordData recordData) {
        ActorData toActor = actorDao.getActorFromPersistentCache(recordData.getToUid());
        String fromName;
        String fromHead;
        CustomerService fromCustomer = CustomerService.getByUid(recordData.getFromUid());
        if (null != fromCustomer) {
            fromName = fromCustomer.getName(toActor.getSlang());
            fromHead = fromCustomer.head;
        } else {
            ActorData fromActor = actorDao.getActorFromPersistentCache(recordData.getFromUid());
            fromName = fromActor.getName();
            fromHead = ImageUrlGenerator.generateRoomUserUrl(fromActor);
        }
        String body = getFcmMsgBody(recordData.getMsg(), recordData.getMsgType(), toActor.getSlang());
        Map<String, String> paramMap = new HashMap<>();
        paramMap.put("aid", recordData.getFromUid());
        paramMap.put("actionType", MsgType.FCM_JUMP_MSG);
        fcmPusher.pushSingle(recordData.getToUid(), paramMap, fromName, body, fromHead);
    }

    public void batchSendToFCM(String fromUid, Set<String> aidSet, String msg, int msgType) {
        if (ObjectUtils.isEmpty(aidSet)) {
            return;
        }
        ActorData fromActor = actorDao.getActorFromPersistentCache(fromUid);
        String fromName = fromActor.getName();
        String fromHead = ImageUrlGenerator.generateRoomUserUrl(fromActor);
        Set<String> enTokenSet = new HashSet<>();
        Set<String> arTokenSet = new HashSet<>();
        for (String aid : aidSet) {
            ActorData toActor = actorDao.getActorFromPersistentCache(aid);
            if (toActor == null) {
                continue;
            }
            String firebaseToken = fcmPusher.getFirebaseToken(aid);
            if (!ObjectUtils.isEmpty(firebaseToken)) {
                if (toActor.getSlang() == SLangType.ENGLISH) {
                    enTokenSet.add(firebaseToken);
                } else {
                    arTokenSet.add(firebaseToken);
                }
            }
        }

        Map<String, String> paramMap = new HashMap<>();
        paramMap.put("aid", fromUid);
        paramMap.put("actionType", MsgType.FCM_JUMP_MSG);
        fcmPusher.pushBatch(enTokenSet, paramMap, fromName, getFcmMsgBody(msg, msgType, SLangType.ENGLISH), fromHead);
        fcmPusher.pushBatch(arTokenSet, paramMap, fromName, getFcmMsgBody(msg, msgType, SLangType.ARABIC), fromHead);
    }

    private String getFcmMsgBody(String msg, int msgType, int slang) {
        String body;
        switch (msgType) {
            case MsgType.TEXT -> body = msg;
            case MsgType.AUDIO -> body = SLangType.ENGLISH == slang ? "Sent a voice message" : "تم ارسال رسالة صوتية";
            case MsgType.IMAGE -> body = SLangType.ENGLISH == slang ? "Sent a photo" : "ارسل صورة";
            case MsgType.GIFT -> body = SLangType.ENGLISH == slang ? "Sent a gift" : "تم ارسال هدية";
            case MsgType.REPOST_MOMENT -> body = SLangType.ENGLISH == slang ? "Shared a Moment" : "شارك معك غرفة";
            case MsgType.SHARE_ROOM ->
                    body = SLangType.ENGLISH == slang ? "Shared to you a room" : "تم مشاركة معك في الغرفة";
            case MsgType.SHARE_LIVE_ROOM -> body = SLangType.ENGLISH == slang ? "Shared the live" : "شارك البث المباشر";
            case MsgType.SEND_RESOURCES -> body = "Send you a resource as a gift.";
            default -> body = SLangType.ENGLISH == slang ? "Sent a message" : "تم ارسال الرسالة";
        }
        return body;
    }

    private String getUploadOssUrl(MsgSendDTO req) {
        return ImageUrlGenerator.createCdnUrl(req.getMsg_body());
    }

    private void checkBlock(String uid) {
        ActorData actorData = actorDao.getActorDataFromCache(uid);
        String tn_id = actorData.getTn_id();
        String blockTime = blockRedis.checkBlock(tn_id, BlockTnConstant.BLOCK_MSG);
        if (!ObjectUtils.isEmpty(blockTime)) {
            HttpCode blockError = HttpCode.BLOCK_ERROR;
            String msgEn = String.format(BlockTnConstant.MSG_BLOCK_MSG_EN, blockTime);
            String msgAr = String.format(BlockTnConstant.MSG_BLOCK_MSG_AR, blockTime);
            HttpCode newError = new HttpCode(blockError.getCode(), msgEn, msgAr);
            logger.info("send chat msg. actor has been blocked. uid={}", uid);
            throw new CommonH5Exception(newError);
        }
    }
}
