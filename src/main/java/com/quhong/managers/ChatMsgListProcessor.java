package com.quhong.managers;

import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.annotation.JSONField;
import com.quhong.config.CaffeineCacheConfig;
import com.quhong.constant.AccountConstant;
import com.quhong.data.ActorData;
import com.quhong.data.MsgRecordRsp;
import com.quhong.data.dto.MsgRecordDTO;
import com.quhong.data.vo.FriendListVO;
import com.quhong.data.vo.FriendSearchVO;
import com.quhong.data.vo.MsgListVO;
import com.quhong.data.vo.OfficialChatVO;
import com.quhong.enums.*;
import com.quhong.exception.CommonException;
import com.quhong.feign.IFriendService;
import com.quhong.handler.HttpEnvData;
import com.quhong.image.ImageUrlGenerator;
import com.quhong.mongo.dao.*;
import com.quhong.mongo.data.MomentNotice;
import com.quhong.mongo.data.MsgListData;
import com.quhong.mysql.dao.FamilyMemberDao;
import com.quhong.mysql.dao.FriendshipDao;
import com.quhong.mysql.dao.RechargeDailyInfoDao;
import com.quhong.mysql.dao.VipInfoDao;
import com.quhong.mysql.data.FriendshipData;
import com.quhong.mysql.data.MysqlMsgRecordData;
import com.quhong.redis.FriendsListRedis;
import com.quhong.redis.MomentUnreadRedis;
import com.quhong.room.cache.RoomActorCache;
import com.quhong.room.data.RoomActorDetailData;
import com.quhong.room.redis.MicFrameRedis;
import com.quhong.utils.AppVersionUtils;
import com.quhong.utils.MsgUtils;
import jakarta.annotation.Resource;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.data.domain.Sort;
import org.springframework.data.mongodb.core.query.Criteria;
import org.springframework.data.mongodb.core.query.Query;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;
import org.springframework.util.ObjectUtils;

import java.util.*;

@Component
public class ChatMsgListProcessor {
    private static final Logger logger = LoggerFactory.getLogger(ChatMsgListProcessor.class);
    private static final MsgItemRsp customServiceMsg = new MsgItemRsp(CustomerService.CUSTOMER_SERVICE, false);
    private static final MsgItemRsp customServiceMsgAr = new MsgItemRsp(CustomerService.CUSTOMER_SERVICE, true);
    private static final MsgItemRsp vipServiceMsg = new MsgItemRsp(CustomerService.VIP_SERVICE, false);
    private static final MsgItemRsp vipServiceMsgAr = new MsgItemRsp(CustomerService.VIP_SERVICE, true);
    private static final MsgItemRsp momentMsg = new MsgItemRsp("moment", "Moment", "https://cloudcdn.waho.live/resource/op_sys_1688544280_icon_12.png");
    private static final MsgItemRsp momentMsgAr = new MsgItemRsp("moment", "لحظات", "https://cloudcdn.waho.live/resource/op_sys_1688544280_icon_12.png");
    private static final int PAGE_SIZE = 10;
    private static final int FRIEND_LIST_SIZE = 10;


    @Autowired
    private MsgListDao msgListDao;
    @Autowired
    private RoomActorCache actorCache;
    @Autowired
    private FriendApplyDao friendApplyDao;
    @Autowired
    private NoticeNewDao noticeNewDao;
    @Resource
    private FriendshipDao friendshipDao;
    @Resource
    private IFriendService friendService;
    @Resource
    private ActorDao actorDao;
    @Resource
    private VipInfoDao vipInfoDao;
    @Resource
    private MicFrameRedis micFrameRedis;
    @Resource
    private FriendsListRedis friendsListRedis;
    @Resource
    private RechargeDailyInfoDao rechargeDailyInfoDao;
    @Resource
    private FamilyMemberDao familyMemberDao;
    @Resource
    private MomentUnreadRedis momentUnreadRedis;
    @Resource
    private CommonDao commonDao;

    public ApiResult<MsgListRsp> getList(ChatMsgListReq reqData) {
        reqData.page = reqData.page - 1;
        if (reqData.page < 0) {
            reqData.page = 0;
        }
        if (reqData.pageSize <= 0) {
            reqData.pageSize = PAGE_SIZE;
        }
        MsgListRsp msgListRsp = new MsgListRsp();
        fillMsgList(reqData, msgListRsp);
        return ApiResult.getOk(msgListRsp);
    }

    public List<OfficialChatVO> getOfficialChatList(MsgRecordDTO dto) {
        ChatMsgListReq reqData = new ChatMsgListReq();
        reqData.setUid(dto.getUid());
        reqData.setPage(dto.getPage());
        reqData.setPageSize(dto.getPageSize());
        reqData.page = reqData.page - 1;
        if (reqData.page < 0) {
            reqData.page = 0;
        }
        if (reqData.pageSize <= 0) {
            reqData.pageSize = PAGE_SIZE * 3;
        }
        List<OfficialChatVO> officialChatList = new ArrayList<>();
        List<MsgItemRsp> sortMsgList = getSortMsgList(reqData, new MsgListRsp());
        for (MsgItemRsp msgItemRsp : sortMsgList) {
            OfficialChatVO vo = new OfficialChatVO();
            vo.setRid(msgItemRsp.getRid());
            vo.setUid(msgItemRsp.getAid());
            vo.setName(msgItemRsp.getName());
            vo.setHead(msgItemRsp.getHead());
            vo.setGender(msgItemRsp.getGender());
            vo.setUnreadMsgCount(msgItemRsp.getUnread());
            if (null != msgItemRsp.getActorDetailData()) {
                vo.setActiveLevel(msgItemRsp.getActorDetailData().getActiveLevel());
                vo.setCharmLevel(msgItemRsp.getActorDetailData().getCharmLevel());
                vo.setWealthLevel(msgItemRsp.getActorDetailData().getWealthLevel());
                vo.setVipLevel(msgItemRsp.getActorDetailData().getVipLevel());
            }
            officialChatList.add(vo);
        }
        return officialChatList;
    }

    public int getUnread(HttpEnvData envData) {
        String uid = envData.getUid();
        int unreadCount = msgListDao.getUnreadCount(uid);
        int friendApply = friendApplyDao.findCount(uid);
        int officialCount = noticeNewDao.findNotificationCount(uid);
        int activityCount = noticeNewDao.findActivityCount(uid);
        if (AppVersionUtils.versionCheck(120, envData)) {
            int momentUnread = momentUnreadRedis.getMomentNoticeCount(uid);
            return unreadCount + friendApply + officialCount + activityCount + momentUnread;
        } else {
            return unreadCount + friendApply + officialCount + activityCount;
        }
    }

    private void fillMsgList(ChatMsgListReq reqData, MsgListRsp msgListRsp) {
        msgListRsp.list = getSortMsgList(reqData, msgListRsp);
        if (reqData.page == 0) {
            int unreadMsgCount = msgListDao.getUnreadCount(reqData.getUid());
            msgListRsp.notification = noticeNewDao.findNotificationCount(reqData.getUid());
            msgListRsp.friendRequests = friendApplyDao.findCount(reqData.getUid());
            msgListRsp.activity = noticeNewDao.findActivityCount(reqData.getUid());
            if (AppVersionUtils.versionCheck(120, reqData)) {
                msgListRsp.moment = momentUnreadRedis.getMomentNoticeCount(reqData.getUid());
                msgListRsp.unread_num = unreadMsgCount + msgListRsp.notification + msgListRsp.friendRequests + msgListRsp.activity + msgListRsp.moment;
            } else {
                msgListRsp.unread_num = unreadMsgCount + msgListRsp.notification + msgListRsp.friendRequests + msgListRsp.activity;
            }
            boolean hasFollowUnread = momentUnreadRedis.hasFollowUnread(reqData.getUid());
            msgListRsp.momentUnread = hasFollowUnread ? 1 : 0;
            msgListRsp.followMomentUnread = hasFollowUnread ? 1 : 0;
        }
    }

    private List<MsgItemRsp> getSortMsgList(ChatMsgListReq reqData, MsgListRsp msgListRsp) {
        String uid = reqData.getUid();
        boolean bigCustomer = rechargeDailyInfoDao.getRechargeMoneyAmountFromCache(uid) >= 5000;
        List<MsgItemRsp> retList = new ArrayList<>();
        List<MsgListVO> msgList = msgListDao.findMsgList(uid, reqData.page * reqData.pageSize, reqData.pageSize, bigCustomer);
        // 数据不足即为最后一页
        msgListRsp.nextUrl = msgList.size() != reqData.pageSize ? "" : String.valueOf((reqData.page + 2));
        for (MsgListVO sortData : msgList) {
            String aid = sortData.getA_id().equals(uid) ? sortData.getB_id() : sortData.getA_id();
            if (aid.equals(uid)) {
                continue;
            }
            MsgItemRsp item = new MsgItemRsp();
            item.unread = sortData.getUnread();
            // 查询阶段插入了官方客服账户sticky为9999
            item.sticky = sortData.getSticky() == 1 ? 1 : 0;
            item.last_time = sortData.getMtime();
            //设置最近一条消息
            MysqlMsgRecordData myLastMsg = sortData.getMy_last_msg();
            if (myLastMsg == null) {
                myLastMsg = sortData.getLast_msg();
            }
            if (myLastMsg != null) {
                MsgRecordRsp msgRecordRsp = new MsgRecordRsp();
                msgRecordRsp.fillFrom(myLastMsg);
                item.msg = msgRecordRsp;
            } else {
                logger.info("can not find last msg. msgIndex={} uid={}", sortData.getMsg_index(), uid);
            }
            RoomActorDetailData detailData = actorCache.getData("", aid, false, false);
            // 过滤已删除的账号
            if (detailData.getAccountStatus() == AccountConstant.DELETED) {
                continue;
            }
            item.fillFrom(detailData);
            CustomerService customerService = CustomerService.getByUid(aid);
            if (null != customerService) {
                item.setName(customerService.getName(reqData.getSlang()));
                item.setHead(customerService.head);
                item.setOfficial(1);
                item.setMicFrame(null);
                item.setVipLevel(0);
                item.setBadge(Collections.emptyList());
            }
            item.setActorDetailData(detailData);
            retList.add(item);
        }
        // 客服账户置顶特殊处理
        if (reqData.page == 0 && !CustomerService.customerSet.contains(uid)) {
            boolean empty = retList.isEmpty();
            if (AppVersionUtils.versionCheck(120, reqData)) {
                fillMomentNotice(retList, uid, reqData.getSlang());
            }
            // 增加客服账号
            retList.add(0, SLangType.ENGLISH == reqData.getSlang() ? customServiceMsg : customServiceMsgAr);
            if (bigCustomer) {
                retList.add(0, SLangType.ENGLISH == reqData.getSlang() ? vipServiceMsg : vipServiceMsgAr);
            }
            // 客服号去重，移除手动插入在头部的数据
            if (!empty) {
                Map<String, MsgItemRsp> distinctMap = new LinkedHashMap<>();
                for (MsgItemRsp msg : retList) {
                    distinctMap.put(msg.getAid(), msg);
                }
                retList = new ArrayList<>(distinctMap.values());
            }
        }
        return retList;
    }

    private void fillMomentNotice(List<MsgItemRsp> retList, String uid, int slang) {
        MomentNotice lastNotice = getLastMoment(uid);
        if (lastNotice != null) {
            MsgItemRsp momentItem = SLangType.ENGLISH == slang ? momentMsg : momentMsgAr;
            momentItem.unread = momentUnreadRedis.getMomentNoticeCount(uid);
            momentItem.type = 1;
            MsgRecordRsp msgRecord = new MsgRecordRsp();
            ActorData actorData = actorDao.getActorDataFromCache(lastNotice.getAid());
            if (lastNotice.getAction_atype() == 1 || lastNotice.getAction_atype() == 5) {
                msgRecord.setMsg(SLangType.ENGLISH == slang ? "%s liked the post".formatted(actorData.getName()) : "أعجب %s بالمنشور".formatted(actorData.getName()));
            } else if (lastNotice.getAction_atype() == 8) {
                msgRecord.setMsg(SLangType.ENGLISH == slang ? "Your post has been pinned" : "تم تثبيت منشورك");
            } else if (lastNotice.getAction_atype() == 9) {
                msgRecord.setMsg(SLangType.ENGLISH == slang ? "Your post has been unpinned" : "تم إلغاء تثبيت منشورك");
            } else if (lastNotice.getAction_atype() == 10 || lastNotice.getAction_atype() == 12) {
                msgRecord.setMsg(SLangType.ENGLISH == slang ? "The content you posted violates community guidelines and has been deleted" : "المحتوى الذي نشرته يخالف قواعد المجتمع، تم حذفه");
            } else if (lastNotice.getAction_atype() == 11) {
                msgRecord.setMsg(SLangType.ENGLISH == slang ? "The post has been restored" : "تم استعادة المنشور");
            } else if (lastNotice.getAction_atype() == 13) {
                msgRecord.setMsg(SLangType.ENGLISH == slang ? "The comment has been restored" : "تم استعادة التعليق");
            } else {
                msgRecord.setMsg(SLangType.ENGLISH == slang ? "%s commented on the post".formatted(actorData.getName()) : "علق %s على المنشور".formatted(actorData.getName()));
            }
            msgRecord.setTimestamp(lastNotice.getC_time() * 1000L);
            momentItem.msg = msgRecord;
            momentItem.last_time = lastNotice.getC_time() * 1000L;
            retList.add(0, momentItem);
        }
    }

    public ApiResult<Object> sticky(FriendshipReq reqData) {
        String uid = reqData.getUid();
        String aid = reqData.getAid();
        FriendshipData friendData = friendshipDao.getFriendData(uid, aid);
        if (null == friendData) {
            throw new CommonException(MsgHttpCode.NOT_FRIEND);
        }
        msgListDao.updateSticky(uid, aid, reqData.getType());
        JSONObject jsonObject = new JSONObject();
        jsonObject.put("sticky", reqData.getType());
        return ApiResult.getOk(jsonObject);
    }

    public ApiResult<Object> stickyStatus(FriendshipReq reqData) {
        // 置顶状态
        int sticky = 0;
        MsgListData msgListData = msgListDao.findMsgListData(MsgUtils.generateMsgIndex(reqData.getUid(), reqData.aid));
        if (null != msgListData) {
            if (msgListData.getA_id().equals(reqData.getUid()) && msgListData.getA_sticky() == 1) {
                sticky = 1;
            } else if (msgListData.getB_id().equals(reqData.getUid()) && msgListData.getB_sticky() == 1) {
                sticky = 1;
            }
        }
        JSONObject jsonObject = new JSONObject();
        jsonObject.put("sticky", sticky);
        return ApiResult.getOk(jsonObject);
    }

    @Cacheable(value = "getFriendsList", key = "#p0.uid+'-'+#p0.page", cacheManager = CaffeineCacheConfig.EXPIRE_1M_AFTER_WRITE)
    public FriendListVO getFriendsList(ChatMsgListReq req) {
        FriendListVO vo = new FriendListVO();
        int page = req.getPage() <= 0 ? 1 : req.getPage();
        int start = (page - 1) * FRIEND_LIST_SIZE;
        ApiResult<List<String>> pageFriendList = friendService.getPageFriendList(req.getUid(), start, start + FRIEND_LIST_SIZE);
        if (pageFriendList.isOk()) {
            List<String> friendListData = pageFriendList.getData();
            if (req.getPage() == 1) {
                List<String> friendsList = msgListDao.getRecentlyChat(req.getUid(), 20);
                for (int i = friendsList.size() - 1; i >= 0; i--) {
                    String aid = friendsList.get(i);
                    if (friendsListRedis.isFriend(req.getUid(), aid)) {
                        friendListData.add(0, aid);
                    }
                }
                vo.setNums(friendService.getFriendCount(req.getUid()).getData());
            }
            vo.setList(getActorVOList(friendListData));
        }
        vo.setNextUrl(vo.getList().isEmpty() ? "" : req.getPage() + 1 + "");
        return vo;
    }

    public Map<String, Integer> getMsgUnreadNum(MsgRecordDTO dto) {
        Map<String, Integer> unreadNumMap = new HashMap<>();
        if (!CollectionUtils.isEmpty(dto.getAidSet())) {
            Set<String> msgIndexSet = new HashSet<>();
            dto.getAidSet().forEach(k -> {
                msgIndexSet.add(MsgUtils.generateMsgIndex(dto.getUid(), k));
            });
            List<MsgListData> unreadMsgList = msgListDao.getListByMsgIndex(dto.getUid(), msgIndexSet);
            for (MsgListData msgListData : unreadMsgList) {
                if (msgListData.getA_id().equals(dto.getUid()) && msgListData.getA_delete() != 1) {
                    unreadNumMap.put(msgListData.getB_id(), msgListData.getA_unread());
                } else if (msgListData.getB_id().equals(dto.getUid()) && msgListData.getB_delete() != 1) {
                    unreadNumMap.put(msgListData.getA_id(), msgListData.getB_unread());
                }
            }
        }
        return unreadNumMap;
    }


    private List<FriendListVO.ActorVO> getActorVOList(Collection<String> aidList) {
        List<FriendListVO.ActorVO> friendVOList = new ArrayList<>();
        Set<String> distinctSet = new HashSet<>();
        for (String aid : aidList) {
            if (distinctSet.contains(aid) || CustomerService.customerSet.contains(aid)) {
                continue;
            }
            distinctSet.add(aid);
            ActorData actorData = actorDao.getActorDataFromCache(aid);
            if (null == actorData || AccountConstant.DELETED == actorData.getAccountStatus()) {
                logger.info("getActorVOList cannot find actor uid={} accountStatus={}", aid, actorData != null ? actorData.getAccountStatus() : 0);
                continue;
            }
            friendVOList.add(copyActorVO(actorData));
        }
        return friendVOList;
    }

    private FriendListVO.ActorVO copyActorVO(ActorData actorData) {
        FriendListVO.ActorVO actorVO = new FriendListVO.ActorVO();
        actorVO.setRidInfo(actorData.getRidData());
        actorVO.setAid(actorData.getUid());
        actorVO.setRid(actorData.getRid());
        actorVO.setName(actorData.getName());
        actorVO.setHead(ImageUrlGenerator.generateRoomUserUrl(actorData));
        actorVO.setGender(actorData.getFb_gender());
        actorVO.setViplevel(vipInfoDao.getIntVipLevelFromCache(actorData.getUid()));
        actorVO.setSvipLevel(actorData.getSvipLevel());
        actorVO.setHead(ImageUrlGenerator.generateRoomUserUrl(actorData));
        actorVO.setMicFrame(micFrameRedis.getMicSourceFromCache(actorData.getUid()));
        return actorVO;
    }

    @Cacheable(value = "search", key = "#p0.uid+'-'+#p0.key", cacheManager = CaffeineCacheConfig.EXPIRE_1M_AFTER_WRITE)
    public FriendSearchVO searchFriendsList(SearchReq req) {
        FriendSearchVO vo = new FriendSearchVO();
        if (null == req.getKey() || ObjectUtils.isEmpty(req.getKey().trim())) {
            throw new CommonException(HttpCode.PARAM_ERROR);
        }
        String key = req.getKey().trim();
        ActorData actorData = actorDao.getActorByRidOrAlphaRid(key);
        if (null != actorData && friendsListRedis.isFriend(req.getUid(), actorData.getUid())) {
            vo.getUsers().add(copyActorVO(actorData));
        }
        return vo;
    }

    private MomentNotice getLastMoment(String uid) {
        Criteria criteria = Criteria.where("uid").is(uid).and("action_atype").in(Arrays.asList(1, 5, 2, 6, 8, 9, 10, 11, 12, 13));
        Query query = new Query(criteria);
        query.with(Sort.by(Sort.Direction.DESC, "c_time"));
        query.limit(1);
        return commonDao.findOne(query, MomentNotice.class);
    }

    public static class ChatMsgListReq extends HttpEnvData {
        private int page;
        private int pageSize;

        public int getPage() {
            return page;
        }

        public void setPage(int page) {
            this.page = page;
        }

        public int getPageSize() {
            return pageSize;
        }

        public void setPageSize(int pageSize) {
            this.pageSize = pageSize;
        }
    }

    public static class SearchReq extends HttpEnvData {
        private String key;

        public String getKey() {
            return key;
        }

        public void setKey(String key) {
            this.key = key;
        }
    }

    public static class FriendshipReq extends HttpEnvData {
        private String aid;
        private String background; // 聊天背景路径
        private int type; // 1顶置，0取消顶置；1自定义，0普通

        public String getAid() {
            return aid;
        }

        public void setAid(String aid) {
            this.aid = aid;
        }

        public String getBackground() {
            return background;
        }

        public void setBackground(String background) {
            this.background = background;
        }

        public int getType() {
            return type;
        }

        public void setType(int type) {
            this.type = type == 1 ? 1 : 0;
        }
    }

    public static class MsgListRsp {
        private List<MsgItemRsp> list;
        private String nextUrl;
        private int unread_num; // 总未读数
        private int notification; // 总未读数
        private int friendRequests; // 总未读数
        private int moment; // 朋友圈总未读数
        private int activity; // 总未读数

        private int momentUnread; // 朋友圈有未读 0否 1是
        private int followMomentUnread; // 朋友圈关注列表有消息未读 0否 1是

        public List<MsgItemRsp> getList() {
            return list;
        }

        public void setList(List<MsgItemRsp> list) {
            this.list = list;
        }

        public String getNextUrl() {
            return nextUrl;
        }

        public void setNextUrl(String nextUrl) {
            this.nextUrl = nextUrl;
        }

        public int getUnread_num() {
            return unread_num;
        }

        public void setUnread_num(int unread_num) {
            this.unread_num = unread_num;
        }

        public int getNotification() {
            return notification;
        }

        public void setNotification(int notification) {
            this.notification = notification;
        }

        public int getFriendRequests() {
            return friendRequests;
        }

        public void setFriendRequests(int friendRequests) {
            this.friendRequests = friendRequests;
        }

        public int getMoment() {
            return moment;
        }

        public void setMoment(int moment) {
            this.moment = moment;
        }

        public int getActivity() {
            return activity;
        }

        public void setActivity(int activity) {
            this.activity = activity;
        }

        public int getMomentUnread() {
            return momentUnread;
        }

        public void setMomentUnread(int momentUnread) {
            this.momentUnread = momentUnread;
        }

        public int getFollowMomentUnread() {
            return followMomentUnread;
        }

        public void setFollowMomentUnread(int followMomentUnread) {
            this.followMomentUnread = followMomentUnread;
        }
    }

    public static class MsgItemRsp {
        private int unread;
        private MsgRecordRsp msg;
        private long last_time;
        private String name;
        private String head;
        private String micFrame;
        private int gender;
        private int age;
        private String country;
        private int rid;
        private int valid;
        private String aid;
        private int vipLevel;
        private int svipLevel;
        private List<String> badge;
        private int sticky; // 1 置顶
        private int official; // 1官方消息
        private int type; // 0聊天消息 1朋友圈消息
        @JSONField(serialize = false)
        private RoomActorDetailData actorDetailData;

        public MsgItemRsp() {
        }

        public MsgItemRsp(CustomerService customerService, boolean ar) {
            this.setRid(customerService.rid);
            this.setAid(customerService.uid);
            this.setName(ar ? customerService.nameAr : customerService.name);
            this.setHead(customerService.head);
            this.setOfficial(1);
        }

        public MsgItemRsp(String uid, String name, String head) {
            this.setAid(uid);
            this.setName(name);
            this.setHead(head);
        }

        public void fillFrom(RoomActorDetailData src) {
            this.name = src.getName();
            this.head = src.getHead();
            this.micFrame = src.getMicFrame();
            this.gender = src.getGender();
            this.age = src.getAge();
            this.country = src.getCountry();
            this.rid = src.getRid();
            this.valid = src.getValid();
            this.aid = src.getAid();
            this.vipLevel = src.getVipLevel();
            this.svipLevel = src.getSvipLevel();
            this.badge = src.getBadgeList();
        }

        public int getUnread() {
            return unread;
        }

        public void setUnread(int unread) {
            this.unread = unread;
        }

        public MsgRecordRsp getMsg() {
            return msg;
        }

        public void setMsg(MsgRecordRsp msg) {
            this.msg = msg;
        }

        public long getLast_time() {
            return last_time;
        }

        public void setLast_time(long last_time) {
            this.last_time = last_time;
        }

        public String getName() {
            return name;
        }

        public void setName(String name) {
            this.name = name;
        }

        public String getHead() {
            return head;
        }

        public void setHead(String head) {
            this.head = head;
        }

        public String getMicFrame() {
            return micFrame;
        }

        public void setMicFrame(String micFrame) {
            this.micFrame = micFrame;
        }

        public int getGender() {
            return gender;
        }

        public void setGender(int gender) {
            this.gender = gender;
        }

        public int getAge() {
            return age;
        }

        public void setAge(int age) {
            this.age = age;
        }

        public String getCountry() {
            return country;
        }

        public void setCountry(String country) {
            this.country = country;
        }

        public int getRid() {
            return rid;
        }

        public void setRid(int rid) {
            this.rid = rid;
        }

        public int getValid() {
            return valid;
        }

        public void setValid(int valid) {
            this.valid = valid;
        }

        public String getAid() {
            return aid;
        }

        public void setAid(String aid) {
            this.aid = aid;
        }

        public int getVipLevel() {
            return vipLevel;
        }

        public void setVipLevel(int vipLevel) {
            this.vipLevel = vipLevel;
        }

        public int getSvipLevel() {
            return svipLevel;
        }

        public void setSvipLevel(int svipLevel) {
            this.svipLevel = svipLevel;
        }

        public List<String> getBadge() {
            return badge;
        }

        public void setBadge(List<String> badge) {
            this.badge = badge;
        }

        public int getSticky() {
            return sticky;
        }

        public void setSticky(int sticky) {
            this.sticky = sticky;
        }

        public int getOfficial() {
            return official;
        }

        public void setOfficial(int official) {
            this.official = official;
        }

        public int getType() {
            return type;
        }

        public void setType(int type) {
            this.type = type;
        }

        public RoomActorDetailData getActorDetailData() {
            return actorDetailData;
        }

        public void setActorDetailData(RoomActorDetailData actorDetailData) {
            this.actorDetailData = actorDetailData;
        }
    }
}
