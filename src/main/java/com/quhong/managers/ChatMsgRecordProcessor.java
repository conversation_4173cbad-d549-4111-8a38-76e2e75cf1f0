package com.quhong.managers;

import com.quhong.config.BackgroundConfig;
import com.quhong.constant.AccountConstant;
import com.quhong.constant.SvipConstant;
import com.quhong.data.ActorData;
import com.quhong.data.MsgPageData;
import com.quhong.data.MsgRecordRsp;
import com.quhong.data.RidData;
import com.quhong.data.dto.MsgRecordDTO;
import com.quhong.enums.*;
import com.quhong.handler.HttpEnvData;
import com.quhong.mongo.dao.ActorDao;
import com.quhong.mongo.dao.BlackListDao;
import com.quhong.mongo.dao.MsgListDao;
import com.quhong.mysql.dao.*;
import com.quhong.mysql.data.MysqlMsgRecordData;
import com.quhong.redis.MsgRecordRedis;
import com.quhong.redis.MsgRedis;
import com.quhong.redis.PlayerStatusRedis;
import com.quhong.redis.SvipMsgPrivilegeRedis;
import com.quhong.room.cache.RoomActorCache;
import com.quhong.room.data.RoomActorDetailData;
import com.quhong.room.redis.RoomPlayerRedis;
import com.quhong.service.MysteryService;
import com.quhong.utils.AppVersionUtils;
import com.quhong.utils.MsgUtils;
import com.quhong.utils.RoomUtils;
import jakarta.annotation.Resource;
import org.bson.types.ObjectId;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.util.ObjectUtils;
import org.springframework.util.StringUtils;

import java.util.ArrayList;
import java.util.List;

@Component
public class ChatMsgRecordProcessor {
    private static final Logger logger = LoggerFactory.getLogger(ChatMsgRecordProcessor.class);
    public static final int PAGE_SIZE = 20;

    @Autowired
    private MsgRecordDao msgRecordDao;
    @Autowired
    private RoomActorCache actorCache;
    @Autowired
    private RoomPlayerRedis roomPlayerRedis;
    @Autowired
    private MsgListDao msgListDao;
    @Autowired
    private PlayerStatusRedis statusRedis;
    @Autowired
    private ActorDao actorDao;
    @Autowired
    private FriendshipDao friendshipDao;
    @Autowired
    private BlackListDao blackListDao;
    @Resource
    private SvipMsgPrivilegeRedis svipMsgPrivilegeRedis;
    @Resource
    private BackgroundConfig backgroundConfig;
    @Resource
    private WhiteTestDao whiteTestDao;
    @Resource
    private WithdrawalOrderDao withdrawalOrderDao;
    @Autowired
    private MysteryService mysteryService;
    @Resource
    private CoinSellerUserRecordDao coinSellerUserRecordDao;
    @Resource
    private MsgRedis msgRedis;
    @Autowired
    private MsgRecordRedis msgRecordRedis;

    public ApiResult<MsgRecordListRsp> getList(ChatMsgRecordReq reqData) {
        reqData.pageSize = PAGE_SIZE;
        reqData.page = reqData.page - 1;
        if (reqData.page < 0) {
            reqData.page = 0;
        }
        MsgRecordListRsp recordListRsp = new MsgRecordListRsp();
        if (MsgRedis.ENABLE_PAID_MSG) {
            recordListRsp.sendMsgDiamond = SendChatMsgProcessor.SEND_MSG_FEE;
            recordListRsp.sendMsgCharm = SendChatMsgProcessor.RECEIVE_MSG_CHARM;
        }
        recordListRsp.block = blackListDao.anyBlock(reqData.getUid(), reqData.aid) ? 1 : 0;
        long time = System.currentTimeMillis();
        fillUnread(recordListRsp, reqData);
        long time1 = System.currentTimeMillis();
        fillRecordList(recordListRsp, reqData);
        long time2 = System.currentTimeMillis();
        fillActors(recordListRsp, reqData);
        long time3 = System.currentTimeMillis();
        logger.info("get records list cost. {} {} {} aid={} uid={} page={}", time1 - time, time2 - time1, time3 - time2, reqData.aid, reqData.getUid(), reqData.getPage());
        return ApiResult.getOk(recordListRsp);
    }

    private void fillUnread(MsgRecordListRsp recordListRsp, ChatMsgRecordReq reqData) {
        recordListRsp.unread = msgListDao.cleanUnread(reqData.getUid(), reqData.aid);
    }

    private void fillRecordList(MsgRecordListRsp recordListRsp, ChatMsgRecordReq reqData) {
        String uid = reqData.getUid();
        String aid = reqData.aid;
        List<MsgRecordRsp> retList = new ArrayList<>();
        if (recordListRsp.block > 0) {
            recordListRsp.list = retList;
            return;
        }
        // 过滤已删除的账号
        if (actorCache.getData("", reqData.aid, false).getAccountStatus() == AccountConstant.DELETED) {
            recordListRsp.list = retList;
            return;
        }
        String msgIndex = MsgUtils.generateMsgIndex(uid, aid);
        int page = reqData.page;
        List<MysqlMsgRecordData> recordList = msgRecordDao.getList(msgIndex, uid, aid, new MsgPageData(page, reqData.pageSize));
        if (recordList != null) {
            if (page == 0 && MsgRedis.ENABLE_PAID_MSG && AppVersionUtils.versionCheck(140, reqData)
             && !CustomerService.customerSet.contains(uid) && !CustomerService.customerSet.contains(aid)) {
                addDeductTipMsg(uid, aid, recordList);
            }
            for (MysqlMsgRecordData recordData : recordList) {
                MsgRecordRsp recordRsp = new MsgRecordRsp();
                recordRsp.fillFrom(recordData);
                if (recordData.getMsgType() == MsgType.SHARE_LIVE_ROOM && !AppVersionUtils.versionCheck(120, reqData)) {
                    fillLowVersionMsg(reqData, recordRsp);
                } else if (recordData.getMsgType() == MsgType.DEDUCT_TIP_MSG && !AppVersionUtils.versionCheck(140, reqData)) {
                    fillLowVersionMsg(reqData, recordRsp);
                }
                retList.add(recordRsp);
            }
        }
        recordListRsp.list = retList;
        if (retList.size() < PAGE_SIZE) {
            recordListRsp.nextUrl = "";
        } else {
            recordListRsp.nextUrl = String.valueOf(page + 2);
        }
        // logger.info("record list. page{} pageSize={} nextUrl={} aid={} uid={}", reqData.page, reqData.pageSize, recordListRsp.nextUrl, aid, uid);
    }

    private void addDeductTipMsg(String uid, String aid, List<MysqlMsgRecordData> recordList) {
        if (msgRedis.hasTipMsgRecord(uid, aid)) {
            return;
        }
        MysqlMsgRecordData recordData = new MysqlMsgRecordData();
        recordData.setFromUid(uid);
        recordData.setToUid(aid);
        recordData.setMsgIndex(MsgUtils.generateMsgIndex(uid, aid));
        recordData.setMsg("");
        recordData.setMsgInfo("");
        recordData.setMsgId(new ObjectId().toString());
        recordData.setTimestamp(System.currentTimeMillis());
        recordData.setMsgType(MsgType.DEDUCT_TIP_MSG);
        recordData.setFromLike("");
        recordData.setToLike("");
        recordData.setToDelete(1);
        msgRecordDao.insert(recordData);
        msgRedis.setTipMsgRecord(uid, aid);
        recordList.add(0, recordData);
    }

    private void fillLowVersionMsg(ChatMsgRecordReq reqData, MsgRecordRsp recordRsp) {
        String versionMsg = getLowVersionMsg(reqData.getSlang());
        if (reqData.getOs() != ClientOS.IOS) {
            recordRsp.setMsgType(0);
        }
        recordRsp.setMsg(versionMsg);
        recordRsp.setInfo(null);
    }

    private String getLowVersionMsg(int slang) {
        if (slang == SLangType.ENGLISH) {
            return "System notification: Unable to view this message, please upgrade your APP version.";
        }
        return "إشعار النظام: غير قادر على عرض هذه الرسالة ، يرجى ترقية إصدار التطبيق الخاص بك";
    }

    private void fillActors(MsgRecordListRsp recordListRsp, ChatMsgRecordReq reqData) {
        RoomActorDetailData fromActor = actorCache.getData("", reqData.getUid(), false, false);
        recordListRsp.fromviplevel = fromActor.getVipLevel();
        recordListRsp.fromidentify = fromActor.getIdentify();
        RoomActorDetailData toActor = actorCache.getData("", reqData.aid, false, false);
        recordListRsp.fromSvipLevel = fromActor.getSvipLevel();
        recordListRsp.svipLevel = toActor.getSvipLevel();
        recordListRsp.toviplevel = toActor.getVipLevel();
        recordListRsp.toidentify = toActor.getIdentify();
        String toActorRoomId = roomPlayerRedis.getActorRoomStatus(reqData.aid);
        if ((!ObjectUtils.isEmpty(toActorRoomId) && whiteTestDao.isMemberByType(toActorRoomId, WhiteTestDao.WHITE_TYPE_ROOM_ID))
                || !ObjectUtils.isEmpty(mysteryService.getMysteryId(reqData.getAid()))) {
            toActorRoomId = "";
        }
        recordListRsp.sstatus = getStatus(toActorRoomId, reqData.aid);
        recordListRsp.friends = friendshipDao.isFriend(reqData.getUid(), reqData.aid) ? 1 : 0;
        if (recordListRsp.friends == 0) {
            if (CustomerService.customerSet.contains(fromActor.getAid()) || CustomerService.customerSet.contains(toActor.getAid())) {
                // 与客服聊天，这个字段都是1
                recordListRsp.friends = 1;
            } else if (withdrawalOrderDao.hasWithdrawRecord(fromActor.getAid(), toActor.getAid())) {
                // 用户之间有提现记录的，这个字段都是1
                recordListRsp.friends = 1;
            } else if (coinSellerUserRecordDao.hasRechargeRecord(fromActor.getAid(), toActor.getAid())) {
                // 用户之间有充值记录的，这个字段都是1
                recordListRsp.friends = 1;
            }
        }
        if (recordListRsp.friends == 0 && fromActor.getSvipLevel() >= 10
                && !CustomerService.customerSet.contains(reqData.getAid())
                && !withdrawalOrderDao.hasWithdrawRecord(reqData.getUid(), reqData.getAid())
                && !coinSellerUserRecordDao.hasRechargeRecord(reqData.getUid(), reqData.getAid())) {
            recordListRsp.setSendStrangerRemain(Math.max(-1, SvipConstant.STRANGER_PERSON.getOrDefault(fromActor.getSvipLevel(), 0) - svipMsgPrivilegeRedis.getSendPersonCount(reqData.getUid(), fromActor.getSvipLevel())));
            if (recordListRsp.sendStrangerRemain == -1 && svipMsgPrivilegeRedis.isMember(reqData.getUid(), reqData.getAid(), fromActor.getSvipLevel())) {
                recordListRsp.setSendStrangerRemain(1);
            }
            recordListRsp.setStrangerMsgRemain(Math.max(0, SvipConstant.STRANGER_MSG_COUNT - svipMsgPrivilegeRedis.getSendMsgCount(reqData.getUid(), reqData.getAid(), fromActor.getSvipLevel())));
        }
        recordListRsp.from_bubble_id = fromActor.getBubbleId();
        recordListRsp.fromMicFrame = fromActor.getMicFrame();
        recordListRsp.to_bubble_id = toActor.getBubbleId();
        recordListRsp.roomId = toActorRoomId;
        recordListRsp.rid = toActor.getRid();

        if (reqData.page == 0) {
            recordListRsp.toUid = reqData.getAid();
            recordListRsp.toHead = toActor.getHead();
            recordListRsp.toMicFrame = toActor.getMicFrame();
            recordListRsp.toName = toActor.getName();
            recordListRsp.toAge = toActor.getAge();
            recordListRsp.toGender = toActor.getGender();
            recordListRsp.toRidData = toActor.getRidData();
            recordListRsp.toWealthLevel = toActor.getWealthLevel();
            recordListRsp.toCharmLevel = toActor.getCharmLevel();
            recordListRsp.toBadgeList = toActor.getBadgeList();
            CustomerService customerService = CustomerService.getByUid(reqData.getAid());
            if (null != customerService) {
                recordListRsp.toHead = customerService.head;
                recordListRsp.toName = customerService.getName(reqData.getSlang());
                recordListRsp.official = 1;
                recordListRsp.sendVideo = 1;
                recordListRsp.sstatus = 0;
                recordListRsp.toMicFrame = null;
                recordListRsp.roomId = null;
            }
        }
        recordListRsp.background = true;
        recordListRsp.custom = true;
        recordListRsp.defaultBackground = backgroundConfig.getDefaultBackground();
    }

    private int getStatus(String roomId, String uid) {
        ActorData toActor = actorDao.getActorDataFromCache(uid);
        if (null == toActor || toActor.getAccept_talk() == 0) {
            return UserMsgStatus.TALK_SHOW_OFFLINE;
        }
        if (!ObjectUtils.isEmpty(roomId)) {
            if (RoomUtils.formatRoomId(uid).equals(roomId)) {
                return UserMsgStatus.TALK_SHOW_SELF_ROOM;
            } else {
                return UserMsgStatus.TALK_SHOW_OTHER_ROOM;
            }
        }
        int status = statusRedis.getPlayerStatus(uid);
        if (status > 0) {
            return UserMsgStatus.TALK_SHOW_ONLINE;
        } else {
            return UserMsgStatus.TALK_SHOW_OFFLINE;
        }
    }

    public static class ChatMsgRecordReq extends HttpEnvData {
        private String aid;
        private int page;
        private int pageSize;

        public void copyFrom(MsgRecordDTO dto) {
            this.uid = dto.getUid();
            this.aid = dto.getAid();
            this.page = dto.getPage();
            this.pageSize = dto.getPageSize();
        }

        public String getAid() {
            return aid;
        }

        public void setAid(String aid) {
            this.aid = aid;
        }

        public int getPage() {
            return page;
        }

        public void setPage(int page) {
            this.page = page;
        }

        public int getPageSize() {
            return pageSize;
        }

        public void setPageSize(int pageSize) {
            this.pageSize = pageSize;
        }
    }

    public static class MsgRecordListRsp {
        private List<MsgRecordRsp> list;
        private String nextUrl;
        private int from_bubble_id;
        private String fromMicFrame;
        private int to_bubble_id;
        private int fromviplevel;
        private int toviplevel;
        private int rid;
        private String roomId;
        private int unread;
        private int block;
        private int sstatus;
        private int friends;
        private int fromidentify;
        private int toidentify;
        // svip相关
        private int fromSvipLevel; // svip等级
        private int svipLevel; // svip等级
        private int strangerMsgSvipLevel = 10; // 发送默认人消息所需svip等级
        private int sendStrangerRemain = -1; // 本月还可以给x个陌生人发消息，不显示文案
        private int strangerMsgRemain = -1; // 本月还可以这个陌生人发x条消息，不显示文案，0=发送受限
        private int strangerMsgCount = SvipConstant.STRANGER_MSG_COUNT; // 发送陌生人消息次数，每发送一次提示一次“陌生人只能发送3条消息”

        private String toUid;
        private String toHead;
        private String toMicFrame;
        private String toName;
        private Integer toAge;
        private Integer toGender;
        private RidData toRidData;
        private boolean background;
        private boolean custom;
        private String defaultBackground; // 默认背景
        private Integer official; // 是否是官方账户 1是
        private int sendVideo; // 1可以发送视频
        private int toWealthLevel;
        private int toCharmLevel;
        private List<String> toBadgeList;

        private int sendMsgDiamond; // 发消息扣钻石数
        private int sendMsgCharm; // 发消息扣魅力数

        public List<MsgRecordRsp> getList() {
            return list;
        }

        public void setList(List<MsgRecordRsp> list) {
            this.list = list;
        }

        public String getNextUrl() {
            return nextUrl;
        }

        public void setNextUrl(String nextUrl) {
            this.nextUrl = nextUrl;
        }

        public int getFrom_bubble_id() {
            return from_bubble_id;
        }

        public void setFrom_bubble_id(int from_bubble_id) {
            this.from_bubble_id = from_bubble_id;
        }

        public String getFromMicFrame() {
            return fromMicFrame;
        }

        public void setFromMicFrame(String fromMicFrame) {
            this.fromMicFrame = fromMicFrame;
        }

        public int getTo_bubble_id() {
            return to_bubble_id;
        }

        public void setTo_bubble_id(int to_bubble_id) {
            this.to_bubble_id = to_bubble_id;
        }

        public int getFromviplevel() {
            return fromviplevel;
        }

        public void setFromviplevel(int fromviplevel) {
            this.fromviplevel = fromviplevel;
        }

        public int getToviplevel() {
            return toviplevel;
        }

        public void setToviplevel(int toviplevel) {
            this.toviplevel = toviplevel;
        }

        public int getRid() {
            return rid;
        }

        public void setRid(int rid) {
            this.rid = rid;
        }

        public String getRoomId() {
            return roomId;
        }

        public void setRoomId(String roomId) {
            this.roomId = roomId;
        }

        public int getUnread() {
            return unread;
        }

        public void setUnread(int unread) {
            this.unread = unread;
        }

        public int getBlock() {
            return block;
        }

        public void setBlock(int block) {
            this.block = block;
        }

        public int getSstatus() {
            return sstatus;
        }

        public void setSstatus(int sstatus) {
            this.sstatus = sstatus;
        }

        public int getFriends() {
            return friends;
        }

        public void setFriends(int friends) {
            this.friends = friends;
        }

        public int getFromidentify() {
            return fromidentify;
        }

        public void setFromidentify(int fromidentify) {
            this.fromidentify = fromidentify;
        }

        public int getToidentify() {
            return toidentify;
        }

        public void setToidentify(int toidentify) {
            this.toidentify = toidentify;
        }

        public int getSvipLevel() {
            return svipLevel;
        }

        public void setSvipLevel(int svipLevel) {
            this.svipLevel = svipLevel;
        }

        public int getFromSvipLevel() {
            return fromSvipLevel;
        }

        public void setFromSvipLevel(int fromSvipLevel) {
            this.fromSvipLevel = fromSvipLevel;
        }

        public int getStrangerMsgSvipLevel() {
            return strangerMsgSvipLevel;
        }

        public void setStrangerMsgSvipLevel(int strangerMsgSvipLevel) {
            this.strangerMsgSvipLevel = strangerMsgSvipLevel;
        }

        public int getSendStrangerRemain() {
            return sendStrangerRemain;
        }

        public void setSendStrangerRemain(int sendStrangerRemain) {
            this.sendStrangerRemain = sendStrangerRemain;
        }

        public int getStrangerMsgCount() {
            return strangerMsgCount;
        }

        public void setStrangerMsgCount(int strangerMsgCount) {
            this.strangerMsgCount = strangerMsgCount;
        }

        public Integer getStrangerMsgRemain() {
            return strangerMsgRemain;
        }

        public void setStrangerMsgRemain(Integer strangerMsgRemain) {
            this.strangerMsgRemain = strangerMsgRemain;
        }

        public String getToUid() {
            return toUid;
        }

        public void setToUid(String toUid) {
            this.toUid = toUid;
        }

        public String getToHead() {
            return toHead;
        }

        public void setToHead(String toHead) {
            this.toHead = toHead;
        }

        public Integer getToAge() {
            return toAge;
        }

        public void setToAge(Integer toAge) {
            this.toAge = toAge;
        }

        public Integer getToGender() {
            return toGender;
        }

        public void setToGender(Integer toGender) {
            this.toGender = toGender;
        }

        public String getToMicFrame() {
            return toMicFrame;
        }

        public void setToMicFrame(String toMicFrame) {
            this.toMicFrame = toMicFrame;
        }

        public String getToName() {
            return toName;
        }

        public void setToName(String toName) {
            this.toName = toName;
        }

        public boolean isBackground() {
            return background;
        }

        public void setBackground(boolean background) {
            this.background = background;
        }

        public boolean isCustom() {
            return custom;
        }

        public void setCustom(boolean custom) {
            this.custom = custom;
        }

        public String getDefaultBackground() {
            return defaultBackground;
        }

        public void setDefaultBackground(String defaultBackground) {
            this.defaultBackground = defaultBackground;
        }

        public Integer getOfficial() {
            return official;
        }

        public void setOfficial(Integer official) {
            this.official = official;
        }

        public int getSendVideo() {
            return sendVideo;
        }

        public void setSendVideo(int sendVideo) {
            this.sendVideo = sendVideo;
        }

        public RidData getToRidData() {
            return toRidData;
        }

        public void setToRidData(RidData toRidData) {
            this.toRidData = toRidData;
        }

        public void setStrangerMsgRemain(int strangerMsgRemain) {
            this.strangerMsgRemain = strangerMsgRemain;
        }

        public int getToWealthLevel() {
            return toWealthLevel;
        }

        public void setToWealthLevel(int toWealthLevel) {
            this.toWealthLevel = toWealthLevel;
        }

        public int getToCharmLevel() {
            return toCharmLevel;
        }

        public void setToCharmLevel(int toCharmLevel) {
            this.toCharmLevel = toCharmLevel;
        }

        public List<String> getToBadgeList() {
            return toBadgeList;
        }

        public void setToBadgeList(List<String> toBadgeList) {
            this.toBadgeList = toBadgeList;
        }

        public int getSendMsgDiamond() {
            return sendMsgDiamond;
        }

        public void setSendMsgDiamond(int sendMsgDiamond) {
            this.sendMsgDiamond = sendMsgDiamond;
        }

        public int getSendMsgCharm() {
            return sendMsgCharm;
        }

        public void setSendMsgCharm(int sendMsgCharm) {
            this.sendMsgCharm = sendMsgCharm;
        }
    }
}
