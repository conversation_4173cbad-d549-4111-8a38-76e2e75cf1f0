package com.quhong.managers;

import com.quhong.constant.MsgItemConstant;
import com.quhong.core.concurrency.tasks.Task;
import com.quhong.data.OpDeleteAllMsgReq;
import com.quhong.data.OpMsgReq;
import com.quhong.enums.ApiResult;
import com.quhong.mongo.dao.FriendApplyDao;
import com.quhong.mongo.dao.MsgListDao;
import com.quhong.mongo.dao.NoticeNewDao;
import com.quhong.mongo.dao.OfficialDao;
import com.quhong.mongo.data.MsgListData;
import com.quhong.mysql.dao.MsgRecordDao;
import com.quhong.mysql.data.MysqlMsgRecordData;
import com.quhong.task.TaskFactory;
import com.quhong.utils.MsgUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

@Component
public class ChatMsgDeleteProcessor {
    private static final Logger logger = LoggerFactory.getLogger(ChatMsgDeleteProcessor.class);

    @Autowired
    private MsgRecordDao msgRecordDao;
    @Autowired
    private MsgListDao msgListDao;
    @Autowired
    private OfficialDao officialDao;
    @Autowired
    private NoticeNewDao noticeNewDao;
    @Autowired
    private FriendApplyDao friendApplyDao;

    public ApiResult<Object> delete(OpMsgReq reqData) {
        if (reqData.getDelType() == 0) {
            // 删除聊天消息
            return deleteMsgRecord(reqData);
        }
        String uid = reqData.getUid();
        switch (reqData.getItemType()) {
            case MsgItemConstant.ITEM_TYPE_OFFICIAL -> {
                // 删除官方通知
                noticeNewDao.deleteNotificationMsg(uid);
                officialDao.deleteNotificationMsg(uid);
            }
            case MsgItemConstant.ITEM_TYPE_NEW_FRIENDS ->
                // 删除新加好友通知
                    friendApplyDao.remove(uid);
            case MsgItemConstant.ITEM_TYPE_ACTIVITY -> {
                // 删除活动通知
                noticeNewDao.deleteActivityMsg(uid);
                officialDao.deleteActivityMsg(uid);
            }
            default -> {
            }
        }
        return ApiResult.getOk();
    }

    /**
     * 删除聊天消息
     *
     * @param reqData
     * @return
     */
    public ApiResult<Object> deleteMsgRecord(OpMsgReq reqData) {
        return deleteMsgRecord(reqData.getMsgId(), reqData.getUid());
    }


    public ApiResult<Object> deleteMsgRecord(String msgId, String uid) {
        MysqlMsgRecordData recordData = msgRecordDao.getData(msgId);
        if (recordData == null) {
            logger.error("can not find msg record data. msgId={} uid={}", msgId, uid);
            return ApiResult.getOk();
        }
        if (recordData.getFromUid().equals(uid)) {
            recordData.setFromDelete(1);
        } else {
            recordData.setToDelete(1);
        }
        msgRecordDao.update(recordData);
        MysqlMsgRecordData lastRecordData = msgRecordDao.getLastData(recordData.getMsgIndex(), uid);
        MsgListData msgListData = msgListDao.findMsgListData(recordData.getMsgIndex());
        if (msgListData != null) {
            if (msgListData.getA_id().equals(uid)) {
                msgListData.setA_last_msg(lastRecordData);
            } else {
                msgListData.setB_last_msg(lastRecordData);
            }
            msgListDao.save(msgListData);
        }
        return ApiResult.getOk();
    }

    public ApiResult<Object> deleteAll(OpDeleteAllMsgReq reqData) {
        return deleteAll(reqData.getUid(), reqData.getAid());
    }

    public ApiResult<Object> deleteAll(String uid, String aid) {
        TaskFactory.getFactory().addSlow(new Task() {
            @Override
            protected void execute() {
                String msgIndex = MsgUtils.generateMsgIndex(uid, aid);
                msgRecordDao.deleteAll(msgIndex, uid, aid);
                MsgListData msgListData = msgListDao.findMsgListData(msgIndex);
                if (msgListData != null) {
                    if (msgListData.getA_id().equals(uid)) {
                        msgListData.setA_delete(1);
                        msgListData.setA_unread(0);
                    } else {
                        msgListData.setB_delete(1);
                        msgListData.setB_unread(0);
                    }
                    msgListDao.save(msgListData);
                }
            }
        });
        return ApiResult.getOk();
    }

    public ApiResult<Object> deleteBothMsg(String uid, String aid) {
        String msgIndex = MsgUtils.generateMsgIndex(uid, aid);
        msgRecordDao.deleteAll(msgIndex, uid, aid);
        msgRecordDao.deleteAll(msgIndex, aid, uid);
        MsgListData msgListData = msgListDao.findMsgListData(msgIndex);
        if (msgListData != null) {
            msgListData.setA_delete(1);
            msgListData.setA_unread(0);
            msgListData.setB_delete(1);
            msgListData.setB_unread(0);
            msgListDao.save(msgListData);
        }
        msgListDao.updateSticky(uid, aid, -1);
        return ApiResult.getOk();
    }
}
