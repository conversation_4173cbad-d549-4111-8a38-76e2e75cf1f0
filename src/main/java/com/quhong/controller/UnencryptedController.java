package com.quhong.controller;

import com.quhong.core.config.ServerConfig;
import com.quhong.data.ActorData;
import com.quhong.mongo.dao.ActorDao;
import com.quhong.service.ApplePayService;
import com.quhong.service.FirstRechargeService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

@RestController
@RequestMapping("${baseUrl}")
public class UnencryptedController {

    private final static Logger logger = LoggerFactory.getLogger(UnencryptedController.class);

    @Autowired
    private ApplePayService applePayService;
    @Autowired
    private ActorDao actorDao;
    @Autowired
    private FirstRechargeService firstRechargeService;


    /**
     * 苹果手动补单
     */
    @GetMapping("/findLostOrder")
    public String findLostOrder(@RequestParam(value = "rid") Integer rid) {
        logger.info("find lost order rid={}", rid);
        ActorData actorData = actorDao.getActorByRid(rid);
        if (null == actorData) {
            return "cannot find actor.";
        }
        return applePayService.findLostOrder(actorData.getUid());
    }

    @GetMapping("/revertPackage")
    public String revertPackage(@RequestParam(value = "rid") Integer rid) {
        if (ServerConfig.isProduct()) {
            return "Failure";
        }
        ActorData actorData = actorDao.getActorByRid(rid);
        if (null == actorData) {
            return "cannot find actor.";
        }
        return firstRechargeService.revertPackage(actorData.getUid()) ? "Ok" : "Failure";
    }
}
