package com.quhong.controller;

import com.quhong.core.config.ServerConfig;
import com.quhong.core.utils.DateHelper;
import com.quhong.data.dto.IncomeDTO;
import com.quhong.data.dto.PageDTO;
import com.quhong.data.dto.UsdSellDiamondDTO;
import com.quhong.data.dto.WithdrawMethodDTO;
import com.quhong.data.vo.*;
import com.quhong.datas.HttpResult;
import com.quhong.enums.HttpCode;
import com.quhong.exception.CommonException;
import com.quhong.handler.HttpEnvData;
import com.quhong.mysql.data.MoneyDetailStatData;
import com.quhong.service.IncomeService;
import com.quhong.utils.WalletUtils;
import com.quhong.vo.PageVO;
import jakarta.annotation.Resource;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * 支付模块/收入V3
 */
@RestController
@RequestMapping("/pay/income/v3")
public class IncomeV3Controller {
    private final static Logger logger = LoggerFactory.getLogger(IncomeV3Controller.class);

    @Resource
    private IncomeService incomeService;

    /**
     * 收入详情
     */
    @PostMapping("info")
    public HttpResult<IncomeVO> info(@RequestBody HttpEnvData dto) {
        logger.info("income info uid={}", dto.getUid());
        return HttpResult.getOk(incomeService.incomeInfo(dto));
    }

    /**
     * 美金转账
     */
    @PostMapping("transfer_usd")
    public HttpResult<IncomeVO> transferUsd(@RequestBody IncomeDTO dto) {
        logger.info("transfer usd. uid={} amount={}", dto.getUid(), dto.getAmount());
        return HttpResult.getOk(incomeService.transferUsd(dto));
    }

    /**
     * 美金兑换钻石
     */
    @PostMapping("usd_to_diamonds")
    public HttpResult<IncomeVO> usdToDiamonds(@RequestBody IncomeDTO dto) {
        logger.info("usd to diamonds. uid={} amount={}", dto.getUid(), dto.getAmount());
        if (WalletUtils.accessControl()) {
            throw new CommonException(HttpCode.SERVER_UPGRADING);
        }
        return HttpResult.getOk(incomeService.usdToDiamonds(dto.getUid(), dto.getAmount()));
    }

    /**
     * 魅力值流水记录
     */
    @PostMapping("charm_detail")
    public HttpResult<CharmDetailListVO> charmDetail(@RequestBody IncomeDTO.PageDTO dto) {
//        if (ServerConfig.isNotProduct()) {
//            return HttpResult.getOk(incomeService.charmDetailFromEs(dto));
//        }
        long start = System.currentTimeMillis();
        CharmDetailListVO charmDetailListVO = incomeService.charmDetailFromEs(dto);
        logger.info("charm detail. uid={} startDate={} endDate={} page={} cost={}", dto.getUid(), dto.getStartDate(), dto.getEndDate(), dto.getPage(), System.currentTimeMillis() - start);
        return HttpResult.getOk(charmDetailListVO);
    }

    /**
     * 美金流水记录
     */
    @Deprecated
    @PostMapping("usd_detail")
    public HttpResult<PageVO<MoneyDetailStatData>> usdDetail(@RequestBody PageDTO dto) {
        logger.info("usd detail. uid={} page={}", dto.getUid(), dto.getPage());
        return HttpResult.getOk(incomeService.usdDetail(dto.getUid(), dto.getPage()));
    }

    /**
     * 美金提现
     */
    @RequestMapping("withdraw_usd")
    private HttpResult<IncomeVO> withdrawUsd(@RequestBody IncomeDTO dto) {
        logger.info("usd withdraw uid={} mineMethodId={} withdrawAmount={}", dto.getUid(), dto.getMineMethodId(), dto.getWithdrawAmount());
        return HttpResult.getOk(incomeService.withdrawUsd(dto));
    }

    /**
     * 美金提现记录
     */
    @PostMapping("withdraw_detail")
    public HttpResult<PageVO<WithdrawDetailVO>> withdrawDetail(@RequestBody PageDTO dto) {
        logger.info("usd withdraw detail. uid={} page={}", dto.getUid(), dto.getPage());
        return HttpResult.getOk(incomeService.withdrawDetail(dto.getUid(), dto.getPage()));
    }

    /**
     * 美金提现方式
     */
    @PostMapping("withdraw_method")
    public HttpResult<WithdrawMethodVO> withdrawMethod(@RequestBody PageDTO dto) {
        long timeMillis = System.currentTimeMillis();
        logger.info("withdraw method. uid={} slang={}", dto.getUid(), dto.getSlang());
        WithdrawMethodVO vo = incomeService.withdrawMethod(dto);
        logger.info("withdraw method. uid={} cost={}", dto.getUid(), System.currentTimeMillis() - timeMillis);
        return HttpResult.getOk(vo);
    }

    /**
     * 提现方式保存
     */
    @PostMapping("withdraw_method_save")
    public HttpResult<Object> saveWithdrawMethod(@RequestBody WithdrawMethodDTO dto) {
        logger.info("save withdraw method. uid={} methodId={} mineMethodId={}", dto.getUid(), dto.getMethodId(), dto.getMineMethodId());
        incomeService.saveWithdrawMethod(dto);
        return HttpResult.getOk();
    }

    /**
     * 计算提现手续费
     */
    @PostMapping("withdraw_handling_fee")
    public HttpResult<WithdrawHandlingFeeVO> withdrawHandlingFee(@RequestBody IncomeDTO dto) {
        logger.info("get withdraw handling fee. uid={} mineMethodId={} withdrawAmount={}", dto.getUid(), dto.getMineMethodId(), dto.getWithdrawAmount());
        return HttpResult.getOk(incomeService.withdrawHandlingFee(dto));
    }

    /**
     * 主播美金给用户或币商充值
     */
    @RequestMapping("usd_sell_diamonds")
    private HttpResult<IncomeVO> usdSellDiamonds(@RequestBody UsdSellDiamondDTO dto) {
        logger.info("usd sell diamonds uid={} aid={} amount={}", dto.getUid(), dto.getAid(), dto.getAmount());
        if (WalletUtils.accessControl()) {
            throw new CommonException(HttpCode.SERVER_UPGRADING);
        }
        return HttpResult.getOk(incomeService.usdSellDiamonds(dto));
    }

    /**
     * 底薪主播任务
     */
    @RequestMapping("basic_salary_task")
    private HttpResult<BasicSalaryTaskVO> basicSalaryTask(@RequestBody IncomeDTO dto) {
        logger.info("get basic salary task info. uid={} month={}", dto.getUid(), dto.getMonth());
        return HttpResult.getOk(incomeService.basicSalaryTaskNew(dto));
    }

    /**
     * 底薪任务魅力值奖励流水
     */
    @RequestMapping("basic_salary_task/charm_detail")
    private HttpResult<CharmDetailListVO> basicSalaryTaskCharmDetail(@RequestBody IncomeDTO.PageDTO dto) {
        logger.info("get basic salary task charm detail. uid={}", dto.getUid());
        return HttpResult.getOk(incomeService.basicSalaryTaskCharmDetail(dto));
    }

    /**
     * 冻结魅力值详情
     */
    @RequestMapping("freeze_charm/detail")
    private HttpResult<FreezeCharmVO> freezeCharmDetail(@RequestBody IncomeDTO.PageDTO dto) {
        logger.info("get freeze charm detail. uid={} charmType={}  startDate={} endDate={}", dto.getUid(), dto.getCharmType(), dto.getStartDate(), dto.getEndDate());
        return HttpResult.getOk(incomeService.freezeCharmDetail(dto));
    }
}
