package com.quhong.constant;

import com.quhong.mongo.data.MomentData;
import com.quhong.mongo.data.MomentNotice;

public class MomentConstant {

    /**
     * 1点赞 2评论 3发布朋友圈@别人 4评论区@别人 5评论点赞 6回复评论 7礼物打赏 8朋友圈被置顶 9朋友圈被拉黑 10评论被拉黑
     *
     * @see MomentNotice action_atype
     */
    public static final int NOTICE_LIKE = 1;
    public static final int NOTICE_COMMENT = 2;
    public static final int NOTICE_PUBLISH_AT = 3;
    public static final int NOTICE_COMMENT_AT = 4;
    public static final int NOTICE_COMMENT_LIKE = 5;
    public static final int NOTICE_COMMENT_REPLAY = 6;
    public static final int NOTICE_MOMENT_REWARD = 7;
    public static final int NOTICE_MOMENT_PINED = 8;
    public static final int NOTICE_MOMENT_UNPIN = 9;
    public static final int NOTICE_MOMENT_BLOCKED = 10;
    public static final int NOTICE_MOMENT_UNBLOCK = 11;
    public static final int NOTICE_COMMENT_BLOCKED = 12;
    public static final int NOTICE_COMMENT_UNBLOCK = 13;

    /**
     * 浏览权限  1 公开，2 朋友可见，3 仅自己可见
     *
     * @see MomentData show
     */
    public static final int MOMENT_PUBLIC = 1;
    public static final int MOMENT_FRIENDS = 2;
    public static final int MOMENT_PRIVATE = 3;

    public static final int QUOTE_REPOST = 1; // 转发
    public static final int QUOTE_LINK = 2; // 链接
    public static final int QUOTE_YOUTUBE_LINK = 3; // YouTube链接
    public static final int QUOTE_OFFICIAL_LINK = 4; // 官方链接
    public static final int QUOTE_SHARE_ROOM = 5; // 房间分享
    public static final int QUOTE_SHARE_ACTIVITY = 6; // 活动分享
}
