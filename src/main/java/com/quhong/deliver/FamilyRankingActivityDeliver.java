package com.quhong.deliver;

import com.mongodb.client.result.UpdateResult;
import com.quhong.constant.ActivityConstant;
import com.quhong.constant.FamilyActivityConstant;
import com.quhong.constant.MoneyActionType;
import com.quhong.constant.ResourceConstant;
import com.quhong.core.config.ServerConfig;
import com.quhong.core.utils.DateHelper;
import com.quhong.data.dto.MoneyDetailReq;
import com.quhong.enums.ActivityRewardTypeEnum;
import com.quhong.image.ImageUrlGenerator;
import com.quhong.mongo.dao.FamilyRankingActivityDao;
import com.quhong.mongo.data.FamilyRankingActivity;
import com.quhong.monitor.MonitorSender;
import com.quhong.mq.MqSenderService;
import com.quhong.mq.ResourceDeliveryService;
import com.quhong.msg.obj.RankInfoListObject;
import com.quhong.msg.obj.RankInfoObject;
import com.quhong.msg.room.RankNotificationPushMsg;
import com.quhong.mysql.dao.FamilyDao;
import com.quhong.mysql.dao.HeartRecordDao;
import com.quhong.mysql.data.FamilyData;
import com.quhong.redis.FamilyRankingActivityRedis;
import com.quhong.room.RoomWebSender;
import com.quhong.room.TestRoomService;
import com.quhong.service.ResourceDistributionService;
import com.quhong.vo.GiftsMqVo;
import jakarta.annotation.Resource;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.data.mongodb.core.query.Update;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.List;

@Component
public class FamilyRankingActivityDeliver {

    private final static Logger logger = LoggerFactory.getLogger(FamilyRankingActivityDeliver.class);
    private final static String RESOURCE_HANDLE_NAME = "FamilyRanking";

    @Resource
    private MonitorSender monitorSender;
    @Resource
    private RoomWebSender roomWebSender;
    @Resource
    private MqSenderService mqSenderService;
    @Resource
    private FamilyRankingActivityDao familyRankingActivityDao;
    @Resource
    private FamilyRankingActivityRedis familyRankingActivityRedis;
    @Resource
    private ResourceDeliveryService resourceDeliveryService;
    @Resource
    private HeartRecordDao heartRecordDao;
    @Resource
    private FamilyDao familyDao;
    @Resource
    private ResourceDistributionService distributionService;

    /**
     * 家族冲榜活动结算
     */
    public void deliver() {
        int nowSeconds = DateHelper.getNowSeconds();
        List<FamilyRankingActivity> initActivities = familyRankingActivityDao.getInitActivities();
        logger.info("ranking activity deliver check. initActivities={}", initActivities.size());
        for (FamilyRankingActivity activity : initActivities) {
            try {
                // 活动已到期结束
                if (activity.getEndTime() < nowSeconds) {
                    logger.info("ranking activity deliver activityId={}", activity.get_id().toString());
                    UpdateResult updateResult = familyRankingActivityDao.updateData(activity, new Update().set("status", ActivityConstant.STATUS_DONE));
                    if (null == updateResult || updateResult.getModifiedCount() == 0) {
                        if (ServerConfig.isProduct()) {
                            monitorSender.info("waho_activity_warn", "家族冲榜活动结束异常", "活动更新失败");
                        }
                        continue;
                    }
                    RankNotificationPushMsg msg = new RankNotificationPushMsg();
                    msg.setTitle_en(activity.getAcNameEn());
                    msg.setTitle_ar(activity.getAcNameAr());
                    msg.setRankList(new ArrayList<>());
                    doSendReward(activity, msg);
                    if (ServerConfig.isProduct()) {
                        monitorSender.info("waho_activity_warn", "家族冲榜活动成功结束", "活动名：" + activity.getAcNameEn());
                    }
                    // 活动结束消息，
                    boolean test = activity.getAcNameEn().startsWith("test");
                    roomWebSender.sendRoomWebMsg(test ? TestRoomService.getTestRoom() : "all", null, msg, false);
                }
            } catch (Exception e) {
                logger.error("rankingActivityDeliver error.", e);
                if (ServerConfig.isProduct()) {
                    monitorSender.info("waho_activity_warn", "家族冲榜活动结束异常", e.getMessage());
                }
            }
        }
    }

    private void doSendReward(FamilyRankingActivity activity, RankNotificationPushMsg msg) {
        if (1 == activity.getConfig().getNoRankReward()) {
            logger.info("doSendReward activityId={} no rankReward", activity.get_id().toString());
            return;
        }
        List<FamilyRankingActivity.RankingConfig> rankingConfigList = activity.getRankingConfigList();
        // 遍历每种奖励类型
        for (FamilyRankingActivity.RankingConfig rankingConfig : rankingConfigList) {
            // 奖励配置
            List<FamilyRankingActivity.RankingRewardConfig> rewardConfigList = rankingConfig.getRankingRewardConfigList();
            // 指定礼物的排行榜
            int rankGiftId = 0;
            if (null != rankingConfig.getGiftId() && 0 != rankingConfig.getGiftId()) {
                rankGiftId = rankingConfig.getGiftId();
            }
            List<Integer> rankingList = familyRankingActivityRedis
                        .getRankingList(activity.get_id().toString(), rankingConfig.getRankingAttribute(), rankGiftId, 10);
            // 排行榜消息
            fillRankMsg(rankingConfig, rankingList, msg);
            for (FamilyRankingActivity.RankingRewardConfig rankingRewardConfig : rewardConfigList) {
                // 奖励对象
                for (Integer rank : rankingRewardConfig.getRewardFamily()) {
                    if (rankingList.size() < rank) {
                        logger.error("cannot find rank activityId={} rank={}", activity.get_id().toString(), rank);
                        continue;
                    }
                    int familyId = rankingList.get(rank - 1);
                    List<String> rewardObjectList = getRewardObjectList(rankingRewardConfig, activity.get_id().toString(), familyId, rankGiftId);
                    for (String aid : rewardObjectList) {
                        // 可能有多个奖励
                        for (FamilyRankingActivity.RewardConfigDetail reward : rankingRewardConfig.getRewardConfigDetailList()) {
                            logger.info("ranking reward activityId={} familyId={} uid={} rewardType={} sourceId={} rewardTime={} num={} rank={} rankType={}",
                                    activity.get_id().toString(), familyId, aid, reward.getRewardType(), reward.getSourceId(), reward.getRewardTime(),
                                    reward.getRewardNum(), rank, rankingConfig.getRankingAttribute());
                            if (ResourceConstant.DIAMOND.equals(reward.getRewardType())) {
                                doAsyncChargeDiamonds(activity, aid, reward.getRewardNum(), rank);
                            } else if (ResourceConstant.HEART.equals(reward.getRewardType())) {
                                String recordTitle = activity.getAcNameEn() + " Competition Win";
                                String recordDesc = String.format("Top %d in the %s Competition Win", rank, activity.getAcNameEn());
                                heartRecordDao.changeHeart(aid, reward.getRewardNum(), recordTitle, recordDesc);
                            } else {
                                if (ResourceConstant.OTHER.equals(reward.getRewardType())) {
                                    continue;
                                }

                                distributionService.sendRewardResource(aid, reward.getSourceId(),
                                        ActivityRewardTypeEnum.getEnumByName(reward.getRewardType()), reward.getRewardTime(), null == reward.getRewardNum() ? 1 : reward.getRewardNum(),
                                        RESOURCE_HANDLE_NAME, RESOURCE_HANDLE_NAME, 0, reward.getLockType());
                            }
                        }
                    }
                }
            }
        }
    }

    private List<String> getRewardObjectList(FamilyRankingActivity.RankingRewardConfig rankingRewardConfig, String activityId, int familyId, int rankGiftId) {
        List<String> aidList = new ArrayList<>();
        if (rankingRewardConfig.getRewardRole() == 0) {
            // 公会长
            FamilyData familyData = familyDao.selectByIdFromCache(familyId);
            if (familyData == null) {
                logger.error("can not find family data. familyId={}", familyId);
            } else {
                aidList.add(familyData.getOwnerUid());
            }
        } else if (rankingRewardConfig.getRewardRole() == 1) {
            // 接收榜主播
            List<String> anchorReceiveRankingList = familyRankingActivityRedis.getRankingInFamilyList(activityId, familyId, FamilyActivityConstant.ANCHOR_RECEIVE_RANK, rankGiftId, 10);

            for (Integer rank : rankingRewardConfig.getRewardObject()) {
                if (anchorReceiveRankingList.size() < rank) {
                    logger.error("cannot find rank rank={}", rank);
                    continue;
                }
                String aid = anchorReceiveRankingList.get(rank - 1);
                aidList.add(aid);
            }
        } else if (rankingRewardConfig.getRewardRole() == 2) {
            // 发送榜用户
            List<String> userSendRankingList = familyRankingActivityRedis.getRankingInFamilyList(activityId, familyId, FamilyActivityConstant.USER_SEND_RANK, rankGiftId, 10);
            for (Integer rank : rankingRewardConfig.getRewardObject()) {
                if (userSendRankingList.size() < rank) {
                    logger.error("cannot find rank rank={}", rank);
                    continue;
                }
                String aid = userSendRankingList.get(rank - 1);
                aidList.add(aid);
            }
        }
        return aidList;
    }

    /**
     * 排行列表消息
     */
    private void fillRankMsg(FamilyRankingActivity.RankingConfig rankingConfig, List<Integer> rankingList, RankNotificationPushMsg msg) {
        // 排行列表消息
        RankInfoListObject rankInfoListObject = new RankInfoListObject();
        rankInfoListObject.setTagType(rankingConfig.getRankingAttribute());
        rankInfoListObject.setTitle_en(rankingConfig.getRankingTitleEn());
        rankInfoListObject.setTitle_ar(rankingConfig.getRankingTitleAr());
        List<RankInfoObject> rankInfoObjects = new ArrayList<>();
        rankInfoListObject.setRankInfo(rankInfoObjects);
        for (int i = 0; i < rankingList.size(); i++) {
            int familyId = rankingList.get(i);
            RankInfoObject rankInfoObject = new RankInfoObject();
            rankInfoObject.setRank(i + 1);
            rankInfoObject.setTagType(rankingConfig.getRankingAttribute());
            FamilyData familyData = familyDao.selectById(familyId);
            rankInfoObject.setRoom_id(familyId + "");
            rankInfoObject.setHead(familyData != null ? ImageUrlGenerator.generateRoomUserUrl(familyData.getHead()) : "");
            rankInfoObject.setName(familyData != null ? familyData.getName() : "");
            rankInfoObject.setRid(familyData != null ? familyData.getRid() : 0);
            rankInfoObjects.add(rankInfoObject);
        }
        msg.getRankList().add(rankInfoListObject);
    }

    /**
     * 异步打钻
     */
    private void doAsyncChargeDiamonds(FamilyRankingActivity activity, String uid, int changed, int rank) {
        MoneyDetailReq moneyDetailReq = new MoneyDetailReq();
        moneyDetailReq.setRandomId();
        moneyDetailReq.setUid(uid);
        moneyDetailReq.setAtype(MoneyActionType.ACTIVITY_COMPETITION.actionType);
        moneyDetailReq.setChanged(changed);
        moneyDetailReq.setTitle(String.format(MoneyActionType.ACTIVITY_COMPETITION.title, activity.getAcNameEn()));
        moneyDetailReq.setDesc(String.format(MoneyActionType.ACTIVITY_COMPETITION.desc, rank, activity.getAcNameEn()));
        moneyDetailReq.setActivityId(activity.get_id().toString());
        mqSenderService.asyncChargeDiamonds(moneyDetailReq);
    }
}
