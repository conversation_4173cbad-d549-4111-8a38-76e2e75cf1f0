package com.quhong.deliver;

import com.mongodb.client.result.UpdateResult;
import com.quhong.constant.ActivityConstant;
import com.quhong.constant.MoneyActionType;
import com.quhong.core.config.ServerConfig;
import com.quhong.core.utils.DateHelper;
import com.quhong.data.ActorData;
import com.quhong.data.ResKeyRewardData;
import com.quhong.data.dto.MoneyDetailReq;
import com.quhong.image.ImageUrlGenerator;
import com.quhong.mongo.dao.ActorDao;
import com.quhong.mongo.dao.MongoRoomDao;
import com.quhong.mongo.dao.RankingActivityDao;
import com.quhong.mongo.data.ConquerActivity;
import com.quhong.mongo.data.MongoRoomData;
import com.quhong.mongo.data.RankingActivity;
import com.quhong.mongo.data.ResourceKeyConfigData;
import com.quhong.monitor.MonitorSender;
import com.quhong.mq.MqSenderService;
import com.quhong.msg.obj.RankInfoListObject;
import com.quhong.msg.obj.RankInfoObject;
import com.quhong.msg.room.RankNotificationPushMsg;
import com.quhong.mysql.dao.FamilyDao;
import com.quhong.mysql.data.FamilyData;
import com.quhong.redis.RankingActivityRedis;
import com.quhong.room.RoomWebSender;
import com.quhong.room.TestRoomService;
import com.quhong.service.OtherActivityService;
import com.quhong.service.RankActivityService;
import com.quhong.service.ResourceKeyHandlerService;
import com.quhong.utils.RoomUtils;
import jakarta.annotation.Resource;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.data.mongodb.core.query.Update;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;

@Component
public class RankingActivityDeliver {

    private final static Logger logger = LoggerFactory.getLogger(RankingActivityDeliver.class);

    private final static String RESOURCE_HANDLE_NAME = "RankingActivity";

    @Resource
    private ActorDao actorDao;
    @Resource
    private MongoRoomDao mongoRoomDao;
    @Resource
    private MonitorSender monitorSender;
    @Resource
    private RoomWebSender roomWebSender;
    @Resource
    private MqSenderService mqSenderService;
    @Resource
    private RankingActivityDao rankingActivityDao;
    @Resource
    private RankingActivityRedis rankingActivityRedis;
    @Resource
    private RankActivityService rankActivityService;
    @Resource
    private ResourceKeyHandlerService resourceKeyHandlerService;
    @Resource
    private FamilyDao familyDao;
    @Resource
    private OtherActivityService otherActivityService;

    /**
     * 冲榜活动结算
     */
    public void deliver() {
        int nowSeconds = DateHelper.getNowSeconds();
        List<RankingActivity> initActivities = rankingActivityDao.getInitActivities();
        logger.info("ranking activity deliver check. initActivities={}", initActivities.size());
        for (RankingActivity activity : initActivities) {
            try {
                // 活动已到期结束
                if (activity.getEndTime() < nowSeconds) {
                    logger.info("ranking activity deliver activityId={}", activity.get_id().toString());
                    UpdateResult updateResult = rankingActivityDao.updateData(activity, new Update().set("status", ActivityConstant.STATUS_DONE));
                    if (null == updateResult || updateResult.getModifiedCount() == 0) {
                        if (ServerConfig.isProduct()) {
                            monitorSender.info("waho_activity_warn", "冲榜活动结束异常", "活动更新失败");
                        }
                        continue;
                    }
                    RankNotificationPushMsg msg = new RankNotificationPushMsg();
                    msg.setTitle_en(activity.getAcNameEn());
                    msg.setTitle_ar(activity.getAcNameAr());
                    msg.setRankList(new ArrayList<>());
                    doSendReward(activity, msg);
                    if (ServerConfig.isProduct()) {
                        monitorSender.info("waho_activity_warn", "冲榜活动成功结束", "活动名：" + activity.getAcNameEn());
                    }
                    // 活动结束消息，
                    boolean test = activity.getAcNameEn().startsWith("test");
                    roomWebSender.sendRoomWebMsg(test ? TestRoomService.getTestRoom() : "all", null, msg, false);
                }
            } catch (Exception e) {
                logger.error("rankingActivityDeliver error.", e);
                if (ServerConfig.isProduct()) {
                    monitorSender.info("waho_activity_warn", "冲榜活动结束异常", e.getMessage());
                }
            }
        }
    }

    private void doSendReward(RankingActivity activity, RankNotificationPushMsg msg) {
        if (1 == activity.getConfig().getNoRankReward()) {
            logger.info("doSendReward activityId={} no rankReward", activity.get_id().toString());
            return;
        }
        List<RankingActivity.RankingConfig> rankingConfigList = activity.getRankingConfigList();
        // 遍历每种奖励类型
        for (RankingActivity.RankingConfig rankingConfig : rankingConfigList) {
            List<ResKeyRewardData> diamondsRewardList = new ArrayList<>();
            // 奖励配置
            List<RankingActivity.RankingRewardConfig> rewardConfigList = rankingConfig.getRankingRewardConfigList();
            // 指定礼物的排行榜
            int rankGiftId = 0;
            if (null != rankingConfig.getGiftId() && 0 != rankingConfig.getGiftId()) {
                rankGiftId = rankingConfig.getGiftId();
            }
            List<String> rankingList;
            ConquerActivity conquerActivity = null;
            if (rankingConfig.getRankingAttribute() == ActivityConstant.CONQUER_RANK) {
                conquerActivity = rankActivityService.getConquerCache();
                if (conquerActivity == null) {
                    monitorSender.info("waho_activity_warn", "征服活动结束异常，无法找到征服活动活动id", "活动名：" + activity.getAcNameEn());
                    continue;
                }
                String conquerId = conquerActivity.get_id().toString();
                rankingList = rankActivityService.getConquerRankingList(activity.get_id().toString(), rankingConfig.getRankingAttribute(), rankGiftId, conquerId);
            } else {
                rankingList = rankingActivityRedis
                        .getRankingList(activity.get_id().toString(), rankingConfig.getRankingAttribute(), rankGiftId, 10, null);
            }
            // 排行榜消息
            fillRankMsg(rankingConfig, rankingList, msg);
            for (RankingActivity.RankingRewardConfig rankingRewardConfig : rewardConfigList) {
                // 奖励对象
                for (Integer rank : rankingRewardConfig.getRewardObject()) {
                    if (rankingList.size() < rank) {
                        logger.error("cannot find rank activityId={} rank={}", activity.get_id().toString(), rank);
                        continue;
                    }
                    String aid = rankingList.get(rank - 1);
                    int rankScore;
                    if (rankingConfig.getRankingAttribute() == ActivityConstant.CONQUER_RANK) {
                        String conquerId = conquerActivity.get_id().toString();
                        rankScore = rankingActivityRedis.getScore(conquerId, aid, rankingConfig.getRankingAttribute(), rankGiftId);
                    } else {
                        rankScore = rankingActivityRedis.getScore(activity.get_id().toString(), aid, rankingConfig.getRankingAttribute(), rankGiftId);
                    }
                    if (ActivityConstant.ROOM_RANK == rankingConfig.getRankingAttribute()) {
                        aid = RoomUtils.getRoomHostId(aid);
                    }
                    if (ActivityConstant.FAMILY_RECEIVE_RANK == rankingConfig.getRankingAttribute() || ActivityConstant.FAMILY_SEND_RANK == rankingConfig.getRankingAttribute()) {
                        int familyId = Integer.parseInt(aid);
                        FamilyData familyData = familyDao.selectByIdFromCache(familyId);
                        if (familyData == null) {
                            logger.error("cannot find family data. familyId={}", aid);
                            continue;
                        }
                        aid = familyData.getOwnerUid();
                    }
                    if (StringUtils.hasLength(rankingRewardConfig.getResourceKey())){
                        ResourceKeyConfigData configData = resourceKeyHandlerService.getConfigData(rankingRewardConfig.getResourceKey());
                        if(configData != null && !CollectionUtils.isEmpty(configData.getResourceMetaList())){
                            for (ResourceKeyConfigData.ResourceMeta resource: configData.getResourceMetaList()) {
                                if (resource.getResourceType() == -2 || resource.getResourceType() == -4) {
                                    if (rankScore >= rankingConfig.getDiamondRewardLimit()) {
                                        if (resource.getResourceType() == -2) {
                                            diamondsRewardList.add(new ResKeyRewardData(activity.get_id().toString(), aid, resource, 905, RESOURCE_HANDLE_NAME, RESOURCE_HANDLE_NAME));
                                        } else if (resource.getResourceType() == -4) {
                                            resourceKeyHandlerService.sendOneResourceData(aid, resource,
                                                    905,
                                                    RESOURCE_HANDLE_NAME,
                                                    RESOURCE_HANDLE_NAME,
                                                    RESOURCE_HANDLE_NAME,
                                                    RESOURCE_HANDLE_NAME,
                                                    "", "", 1, activity.get_id().toString());
                                        }
                                    }
                                } else {
                                    resourceKeyHandlerService.sendOneResourceData(aid, resource,
                                            905,
                                            RESOURCE_HANDLE_NAME,
                                            RESOURCE_HANDLE_NAME,
                                            RESOURCE_HANDLE_NAME,
                                            RESOURCE_HANDLE_NAME,
                                            "", "", 1, activity.get_id().toString());
                                }

                            }
                        }
                    }
                }
            }
            Map<String, Integer> rankingMap;
            if (rankingConfig.getRankingAttribute() == ActivityConstant.CONQUER_RANK) {
                String conquerId = conquerActivity.get_id().toString();
                rankingMap = rankingActivityRedis.getRankingMap(activity.get_id().toString(), rankingConfig.getRankingAttribute(), rankGiftId, 10, conquerId);
            } else {
                rankingMap = rankingActivityRedis.getRankingMap(activity.get_id().toString(), rankingConfig.getRankingAttribute(), rankGiftId, 10, null);
            }
            otherActivityService.sendDiamondsReward(activity.get_id().toString(), activity.getAcNameEn(), ActivityConstant.RANKING_TYPE_MAP.getOrDefault(rankingConfig.getRankingAttribute(), ""), diamondsRewardList, otherActivityService.convertToLongMap(rankingMap));
        }
    }

    /**
     * 排行列表消息
     */
    private void fillRankMsg(RankingActivity.RankingConfig rankingConfig, List<String> rankingList, RankNotificationPushMsg msg) {
        // 排行列表消息
        RankInfoListObject rankInfoListObject = new RankInfoListObject();
        rankInfoListObject.setTagType(rankingConfig.getRankingAttribute());
        rankInfoListObject.setTitle_en(rankingConfig.getRankingTitleEn());
        rankInfoListObject.setTitle_ar(rankingConfig.getRankingTitleAr());
        List<RankInfoObject> rankInfoObjects = new ArrayList<>();
        rankInfoListObject.setRankInfo(rankInfoObjects);
        for (int i = 0; i < rankingList.size(); i++) {
            String aid = rankingList.get(i);
            RankInfoObject rankInfoObject = new RankInfoObject();
            rankInfoObject.setRank(i + 1);
            rankInfoObject.setTagType(rankingConfig.getRankingAttribute());
            // 消息兼容，征服房间榜转换为发送榜
            if (ActivityConstant.CONQUER_RANK == rankingConfig.getRankingAttribute()) {
                rankInfoObject.setTagType(ActivityConstant.SEND_RANK);
            }
            if (ActivityConstant.ROOM_RANK == rankingConfig.getRankingAttribute()) {
                MongoRoomData roomData = mongoRoomDao.getDataFromCache(aid);
                rankInfoObject.setRoom_id(aid);
                rankInfoObject.setHead(ImageUrlGenerator.generateRoomUserUrl(roomData.getHead()));
                rankInfoObject.setName(roomData.getName());
                rankInfoObject.setRid(actorDao.getActorDataFromCache(RoomUtils.getRoomHostId(aid)).getRid());
            } else if (ActivityConstant.FAMILY_SEND_RANK == rankingConfig.getRankingAttribute() || ActivityConstant.FAMILY_RECEIVE_RANK == rankingConfig.getRankingAttribute()) {
                int familyId = Integer.parseInt(aid);
                FamilyData familyData = familyDao.selectByIdFromCache(familyId);
                rankInfoObject.setRoom_id(aid);
                rankInfoObject.setHead(ImageUrlGenerator.generateRoomUserUrl(familyData.getHead()));
                rankInfoObject.setName(familyData.getName());
                rankInfoObject.setRid(familyData.getRid());
            } else  {
                ActorData actorData = actorDao.getActorDataFromCache(aid);
                rankInfoObject.setRoom_id(aid);
                rankInfoObject.setHead(ImageUrlGenerator.generateRoomUserUrl(actorData));
                rankInfoObject.setName(actorData.getName());
                rankInfoObject.setRid(actorData.getRid());
            }
            rankInfoObjects.add(rankInfoObject);
        }
        msg.getRankList().add(rankInfoListObject);
    }
}
