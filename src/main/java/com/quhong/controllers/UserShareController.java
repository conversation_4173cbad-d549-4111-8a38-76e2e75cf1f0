package com.quhong.controllers;

import com.quhong.data.dto.UserShareDTO;
import com.quhong.data.vo.UserShareVO;
import com.quhong.datas.HttpResult;
import com.quhong.enums.HttpCode;
import com.quhong.enums.ShareModeEnum;
import com.quhong.exception.CommonException;
import com.quhong.service.UserShareService;
import jakarta.annotation.Resource;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * 用户信息/分享
 */
@RestController
@RequestMapping(value = "/user_info/")
public class UserShareController {

    private static final Logger logger = LoggerFactory.getLogger(UserShareController.class);

    @Resource
    private UserShareService userShareService;

    /**
     * 生成分享链接
     */
    @RequestMapping("share")
    public HttpResult<UserShareVO> share(@RequestBody UserShareDTO req) {
        long millis = System.currentTimeMillis();
        logger.info("Generate Shared links. uid={} mode={}", req.getUid(), req.getMode());
        if (!checkParam(req)) {
            throw new CommonException(HttpCode.PARAM_ERROR);
        }
        UserShareVO vo = userShareService.share(req);
        logger.info("Generate Shared links. uid={} timeMillis={}", req.getUid(), System.currentTimeMillis() - millis);
        return HttpResult.getOk(vo);
    }

    private boolean checkParam(UserShareDTO req) {
        return req.getMode() != null && ShareModeEnum.check(req.getMode());
    }


}
