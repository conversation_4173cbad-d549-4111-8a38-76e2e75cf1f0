package com.quhong.controllers;

import com.quhong.constant.ActivityHttpCode;
import com.quhong.data.vo.InviteFriendVO;
import com.quhong.data.vo.PrizeConfigVO;
import com.quhong.data.vo.ShootingVO;
import com.quhong.data.vo.WeeklyShootingVO;
import com.quhong.datas.HttpResult;
import com.quhong.enums.HttpCode;
import com.quhong.exception.CommonH5Exception;
import com.quhong.service.WeeklyShootingService;
import com.quhong.vo.ListVO;
import com.quhong.vo.PageVO;
import jakarta.annotation.Resource;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.util.StringUtils;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

/**
 *  每周射门活动
 *
 * <AUTHOR>
 * @date 2024/9/9
 */
@RestController
@RequestMapping("${baseUrl}weeklyShooting")
public class WeeklyShootingController {

    private static final Logger logger = LoggerFactory.getLogger(WeeklyShootingController.class);

    @Resource
    private WeeklyShootingService weeklyShootingService;

    /**
     * 获取活动详情
     */
    @GetMapping("info")
    private HttpResult<WeeklyShootingVO> getInfo(@RequestParam String uid,
                                                 @RequestParam(required = false, defaultValue = "") String fromUid,
                                                 @RequestParam(required = false, defaultValue = "2") int slang) {
        long timeMillis = System.currentTimeMillis();
        WeeklyShootingVO vo = weeklyShootingService.getInfo(uid, fromUid, slang);
        logger.info("getInfo. uid={} fromUid={} cost={}", uid, fromUid, System.currentTimeMillis() - timeMillis);
        return HttpResult.getOk(vo);
    }

    /**
     * 射门
     */
    @GetMapping("shooting")
    private HttpResult<ShootingVO> shooting(@RequestParam String uid,
                                            @RequestParam(defaultValue = "0") int type,
                                            @RequestParam(defaultValue = "0") int zone,
                                            @RequestParam Integer num) {
        logger.info("shooting. uid={} type={} zone={} num={}", uid, type, zone, num);
        return HttpResult.getOk(weeklyShootingService.shooting(uid, type, zone, num));
    }

    /**
     * 邀请好友
     */
    @GetMapping("inviteFriend")
    private HttpResult<ListVO<InviteFriendVO>> inviteFriend(@RequestParam String uid, @RequestParam String aid, @RequestParam(required = false, defaultValue = "2") int slang) {
        logger.info("inviteFriend. uid={} aid={}", uid, aid);
        if (!StringUtils.hasLength(aid)) {
            throw new CommonH5Exception(HttpCode.PARAM_ERROR);
        }
        weeklyShootingService.inviteFriend(uid, aid);
        return new HttpResult<>(slang, ActivityHttpCode.ALREADY_INVITE_PLEASE_WAIT, null);
    }

    /**
     * 抽奖记录
     */
    @GetMapping("drawRecord")
    private HttpResult<PageVO<PrizeConfigVO>> drawRecord(@RequestParam String uid, @RequestParam(defaultValue = "1") int page) {
        logger.info("drawRecord. uid={} page={}", uid, page);
        return HttpResult.getOk(weeklyShootingService.drawRecord(uid, page));
    }

    /**
     * 队长喊话
     */
    @GetMapping("shout")
    private HttpResult<Object> shout(@RequestParam String uid, String aid) {
        logger.info("shout. uid={} aid={}", uid, aid);
        weeklyShootingService.shout(uid, aid);
        return HttpResult.getOk();
    }
}
