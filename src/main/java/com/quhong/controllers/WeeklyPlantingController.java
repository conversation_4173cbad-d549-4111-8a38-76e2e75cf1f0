package com.quhong.controllers;

import com.quhong.constant.ActivityHttpCode;
import com.quhong.data.vo.InviteFriendVO;
import com.quhong.data.vo.PlantingSignVO;
import com.quhong.data.vo.WateringVO;
import com.quhong.data.vo.WeeklyPlantingVO;
import com.quhong.datas.HttpResult;
import com.quhong.enums.HttpCode;
import com.quhong.exception.CommonH5Exception;
import com.quhong.service.WeeklyPlantingService;
import com.quhong.vo.ListVO;
import jakarta.annotation.Resource;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.util.StringUtils;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

/**
 *  种植枣椰树活动
 *
 * <AUTHOR>
 * @date 2024/9/9
 */
@RestController
@RequestMapping("${baseUrl}weeklyPlanting")
public class WeeklyPlantingController {

    private static final Logger logger = LoggerFactory.getLogger(WeeklyPlantingController.class);

    @Resource
    private WeeklyPlantingService plantingService;

    /**
     * 获取活动详情
     */
    @GetMapping("info")
    private HttpResult<WeeklyPlantingVO> getInfo(@RequestParam String uid) {
        long timeMillis = System.currentTimeMillis();
        WeeklyPlantingVO vo = plantingService.getInfo(uid);
        logger.info("getInfo. uid={} cost={}", uid, System.currentTimeMillis() - timeMillis);
        return HttpResult.getOk(vo);
    }

    /**
     * 浇水
     */
    @GetMapping("watering")
    private HttpResult<WateringVO> watering(@RequestParam String uid, @RequestParam Integer num) {
        logger.info("watering. uid={} num={}", uid, num);
        if (num == null) {
            throw new CommonH5Exception(HttpCode.PARAM_ERROR);
        }
        return HttpResult.getOk(plantingService.watering(uid, num));
    }

    /**
     * 邀请好友
     */
    @GetMapping("inviteFriend")
    private HttpResult<ListVO<InviteFriendVO>> inviteFriend(@RequestParam String uid, @RequestParam String aid, @RequestParam Integer position, @RequestParam(required = false, defaultValue = "2") int slang) {
        logger.info("inviteFriend. uid={} aid={}", uid, aid);
        if (!StringUtils.hasLength(aid) || position == null || position < 1 || position > 5) {
            throw new CommonH5Exception(HttpCode.PARAM_ERROR);
        }
        return new HttpResult<>(slang, ActivityHttpCode.INVITATION_SUCCESSFUL, plantingService.inviteFriend(uid, aid, position));
    }

    /**
     * 移除已邀请的好友
     */
    @GetMapping("removeInviteFriend")
    private HttpResult<ListVO<InviteFriendVO>> removeInviteFriend(@RequestParam String uid, @RequestParam String aid) {
        logger.info("removeInviteFriend. uid={} aid={}", uid, aid);
        if (!StringUtils.hasLength(aid)) {
            throw new CommonH5Exception(HttpCode.PARAM_ERROR);
        }
        return HttpResult.getOk(plantingService.removeInviteFriend(uid, aid));
    }

    /**
     * 设置签到提醒
     */
    @GetMapping("setSignReminder")
    private HttpResult<Object> setSignReminder(@RequestParam String uid, @RequestParam(defaultValue = "1") int status) {
        logger.info("setSignReminder uid={} status={}", uid, status);
        plantingService.setSignReminder(uid, status);
        return HttpResult.getOk();
    }

    /**
     * 签到
     */
    @GetMapping("sign")
    private HttpResult<ListVO<PlantingSignVO>> sign(@RequestParam String uid) {
        logger.info("sign. uid={}", uid);
        return HttpResult.getOk(plantingService.sign(uid));
    }
}
