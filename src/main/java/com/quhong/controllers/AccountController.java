package com.quhong.controllers;

import com.quhong.constant.LoginHttpCode;
import com.quhong.data.dto.*;
import com.quhong.data.vo.LoginMethodBindVO;
import com.quhong.datas.HttpResult;
import com.quhong.enums.HttpCode;
import com.quhong.exception.CommonException;
import com.quhong.handler.HttpEnvData;
import com.quhong.handler.WebController;
import com.quhong.service.AccountService;
import com.quhong.service.impl.SpecialGustLoginService;
import com.quhong.utils.RequestUtils;
import com.quhong.vo.ListVO;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import jakarta.annotation.Resource;
import jakarta.servlet.http.HttpServletRequest;

import java.util.List;

@RestController
@RequestMapping("${baseUrl}")
public class AccountController extends WebController {
    private static final Logger logger = LoggerFactory.getLogger(AccountController.class);

    @Resource
    private AccountService accountService;

    @Resource
    private SpecialGustLoginService specialGustLoginService;
    /**
     * 删除账号
     */
    @RequestMapping("delete_account")
    private String deleteAccount(HttpServletRequest request) {
        HttpEnvData req = RequestUtils.getSendData(request, HttpEnvData.class);
        logger.info("delete account uid={}", req.getUid());
        return createResult(req, HttpCode.SUCCESS, accountService.deleteAccount(req.getUid()));
    }

    /**
     * 创建小号
     */
    @RequestMapping("special/create_account")
    private String createSpecialAccount(HttpServletRequest request) {
        SpecialAccountDTO req = RequestUtils.getSendData(request, SpecialAccountDTO.class);
        if (!req.isValid()) {
            logger.error("create special account param error req={}", req);
            throw new CommonException(LoginHttpCode.PARAM_ERROR);
        }
        specialGustLoginService.createSpecialAccount(req.getUid(), req.getAccount(), req.getPassword());
        return createResult(req, LoginHttpCode.SPECIAL_GUST_SUCCESS, new Object());
    }

    /**
     * 修改小号密码
     */
    @RequestMapping("special/set_password")
    private String setSpecialPassword(HttpServletRequest request) {
        SpecialPwdDTO req = RequestUtils.getSendData(request, SpecialPwdDTO.class);
        if (!req.isValid()) {
            logger.error("set special password param error req={}", req);
            throw new CommonException(LoginHttpCode.PARAM_ERROR);
        }
        specialGustLoginService.setSpecialPassword(req.getUid(), req.getNew_password(), req.getOld_password());
        return createResult(req, LoginHttpCode.SPECIAL_GUST_SET_SUCCESS, new Object());
    }

    /**
     * 发送验证码
     */
    @RequestMapping("send_verify_code")
    private HttpResult<Object> sendVerifyCode(HttpServletRequest request, @RequestBody CheckOutPhoneDTO dto) {
        String remoteIp = RequestUtils.getIpAddress(request);
        logger.info("sendVerifyCode. openId={} countryCode={} phone={} source={} ip={}", dto.getOpenId(), dto.getpCountryCode(), dto.getpNumber(), dto.getSource(), remoteIp);
        dto.setIp(remoteIp);
        accountService.sendVerifyCode(dto);
        return HttpResult.getOk();
    }

    /**
     * 确认验证码
     */
    @RequestMapping("confirm_verify_code")
    private HttpResult<Object> confirmVerifyCode(@RequestBody CheckOutPhoneDTO dto) {
        logger.info("confirmVerifyCode. phone={} verityCode={}", dto.getpNumber(), dto.getpVerifyCode());
        accountService.confirmVerifyCode(dto.getpNumber(), dto.getpCountryCode(), dto.getpVerifyCode());
        return HttpResult.getOk();
    }

    /**
     * 账号绑定的登录方式列表
     */
    @RequestMapping("login_method/list")
    private HttpResult<ListVO<LoginMethodBindVO>> loginMethodList(@RequestBody HttpEnvData dto) {
        logger.info("sendVerifyCode. uid={}", dto.getUid());
        return HttpResult.getOk(accountService.getLoginMethodList(dto));
    }

    /**
     * 登录方式绑定
     */
    @RequestMapping("login_method/bind")
    private HttpResult<Object> loginMethodBind(@RequestBody AccountBindDTO dto) {
        logger.info("loginMethodUnbind. uid={} type={}", dto.getUid(), dto.getType());
        accountService.loginMethodBind(dto);
        return HttpResult.getOk();
    }

    /**
     * 登录方式解绑
     */
    @RequestMapping("login_method/unbind")
    private HttpResult<Object> loginMethodUnbind(@RequestBody AccountBindDTO dto) {
        logger.info("loginMethodUnbind. uid={} type={}", dto.getUid(), dto.getType());
        accountService.loginMethodUnbind(dto);
        return HttpResult.getOk();
    }
}
