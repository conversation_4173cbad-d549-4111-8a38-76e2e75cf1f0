package com.quhong.controllers;

import com.alibaba.fastjson.JSON;
import com.quhong.constant.DataCenterHttpCode;
import com.quhong.core.config.ServerConfig;
import com.quhong.data.dto.DataCenterPageDTO;
import com.quhong.enums.ApiResult;
import com.quhong.monitor.MonitorSender;
import com.quhong.mysql.data.MoneyDetail;
import com.quhong.mysql.data.UserMoney;
import com.quhong.service.MoneyService;
import com.quhong.service.UserMoneyService;
import jakarta.annotation.Resource;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.http.MediaType;
import org.springframework.util.CollectionUtils;
import org.springframework.util.ObjectUtils;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 数据中心/钻石操作
 */
@RestController
@RequestMapping(value = "${baseUrl}", produces = MediaType.APPLICATION_JSON_VALUE)
public class BeansController {
    private static final Logger logger = LoggerFactory.getLogger(BeansController.class);

    @Resource
    private MoneyService moneyService;
    @Resource
    private UserMoneyService userMoneyService;
    @Resource
    private MonitorSender monitorSender;

    private void timeOutCheck(long timeMillis, MoneyDetail detail) {
        long cost = System.currentTimeMillis() - timeMillis;
        if (cost > 3000L) {
            warn(JSON.toJSONString(detail), cost);
        }
    }

    private void warn(String detail, long cost) {
        try {
            if (ServerConfig.isProduct()) {
                monitorSender.info("waho_java_exception", "钻石处理超时：" + cost + "(ms)", detail);
            }
        } catch (Exception e) {
            logger.error("monitor error. {}", e.getMessage(), e);
        }
    }

    /**
     * 加钻
     */
    @PostMapping("charge_beans")
    private ApiResult<String> chargeBeans(@RequestBody MoneyDetail detail) {
        long timeMillis = System.currentTimeMillis();
        if (null == detail || ObjectUtils.isEmpty(detail.getId()) || ObjectUtils.isEmpty(detail.getUid())
                || null == detail.getAtype() || null == detail.getChanged() || 0 >= detail.getChanged()
                || ObjectUtils.isEmpty(detail.getTitle())) {
            logger.info("charge beans error moneyDetail={}", detail);
            return ApiResult.getError(DataCenterHttpCode.PARAMS_ERROR);
        }
        logger.info("chargeBeans uid={} id={} change={}", detail.getUid(), detail.getId(), detail.getChanged());
        ApiResult<String> apiResult = moneyService.chargeBeans(detail);
        timeOutCheck(timeMillis, detail);
        return apiResult;
    }

    /**
     * 减钻
     * 支持赊账，credit=true
     *
     * @return 成功返回0，1钻石余额不足，2重复扣费，1002服务器错误
     */
    @PostMapping("reduce_beans")
    private ApiResult<String> reduceBeans(@RequestBody MoneyDetail detail) {
        long timeMillis = System.currentTimeMillis();
        if (null == detail || ObjectUtils.isEmpty(detail.getId()) || ObjectUtils.isEmpty(detail.getUid())
                || null == detail.getAtype() || null == detail.getChanged() || (!detail.isCredit() && 0 <= detail.getChanged())
                || ObjectUtils.isEmpty(detail.getTitle())) {
            logger.info("reduce beans error moneyDetail={}", detail);
            return ApiResult.getError(DataCenterHttpCode.PARAMS_ERROR);
        }
        logger.info("reduceBeans uid={} id={} change={}", detail.getUid(), detail.getId(), detail.getChanged());
        ApiResult<String> apiResult = moneyService.chargeBeans(detail);
        timeOutCheck(timeMillis, detail);
        return apiResult;
    }

    /**
     * 查询用户余额
     */
    @PostMapping("user_money")
    private ApiResult<List<UserMoney>> userMoney(@RequestBody List<String> uidList) {
        logger.info("get user money uidList={}", uidList);
        ApiResult<List<UserMoney>> result = new ApiResult<>();
        if (CollectionUtils.isEmpty(uidList)) {
            return result.error(DataCenterHttpCode.PARAMS_ERROR);
        }
        return result.ok(userMoneyService.getUserMoney(uidList));
    }

    /**
     * 查询用户流水
     */
    @PostMapping("es_money_detail")
    private ApiResult<List<MoneyDetail>> esMoneyDetail(@RequestBody DataCenterPageDTO dto) {
        logger.info("get user money detail={}", dto);
        ApiResult<List<MoneyDetail>> result = new ApiResult<>();
        if (ObjectUtils.isEmpty(dto.getUid())) {
            return result.error(DataCenterHttpCode.PARAMS_ERROR);
        }
        return result.ok(userMoneyService.getMyVisitDetails(dto.getUid(), dto.getaType(), dto.getPage(), dto.getSize()));
    }

    /**
     * 加钻
     */
    @PostMapping("change_virtual_diamond")
    private ApiResult<String> changeVirtualDiamond(@RequestParam(value = "uid") String uid,
                                                   @RequestParam(value = "change") Integer change,
                                                   @RequestParam(value = "title") String title,
                                                   @RequestParam(value = "remark") String remark) {
        if (ObjectUtils.isEmpty(uid) || ObjectUtils.isEmpty(change) || ObjectUtils.isEmpty(title)) {
            logger.info("changeVirtualDiamond error uid={} change={} title={} remark={}", uid, change, title, remark);
            return ApiResult.getError(DataCenterHttpCode.PARAMS_ERROR);
        }
        logger.info("changeVirtualDiamond. uid={} change={} title={} remark={}", uid, change, title, remark);
        return moneyService.changeVirtualDiamond(uid, change, title, remark);
    }
}
