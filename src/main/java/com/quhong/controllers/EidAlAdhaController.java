package com.quhong.controllers;

import com.quhong.data.vo.DrawRecordVO;
import com.quhong.data.vo.EidAlAdhaVO;
import com.quhong.datas.HttpResult;
import com.quhong.enums.HttpCode;
import com.quhong.exception.CommonH5Exception;
import com.quhong.service.EidAlAdhaService;
import com.quhong.vo.PageVO;
import jakarta.annotation.Resource;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.util.StringUtils;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

/**
 * 宰牲节活动
 *
 * <AUTHOR>
 * @date 2025/1/20
 */
@RestController
@RequestMapping("${baseUrl}eidAlAdha/")
public class EidAlAdhaController {

    private static final Logger logger = LoggerFactory.getLogger(EidAlAdhaController.class);

    @Resource
    private EidAlAdhaService eidAlAdhaService;

    /**
     * 获取活动信息
     */
    @RequestMapping("info")
    private HttpResult<EidAlAdhaVO> getEidAlAdhaInfo(@RequestParam String activityId, @RequestParam String uid) {
        logger.info("getEidAlAdhaInfo. activityId={} uid={}", activityId, uid);
        return HttpResult.getOk(eidAlAdhaService.getInfo(activityId, uid));
    }

    /**
     * 出售羊兑换金币
     */
    @RequestMapping("sellSheep")
    private HttpResult<EidAlAdhaVO> sellSheep(@RequestParam String activityId, 
                                              @RequestParam String uid, 
                                              @RequestParam Integer marketType, 
                                              @RequestParam Integer sheepNum) {
        logger.info("sellSheep. activityId={} uid={} marketType={} sheepNum={}", activityId, uid, marketType, sheepNum);
        if (marketType == null || sheepNum == null || sheepNum <= 0) {
            throw new CommonH5Exception(HttpCode.PARAM_ERROR);
        }
        return HttpResult.getOk(eidAlAdhaService.sellSheep(activityId, uid, marketType, sheepNum));
    }

    /**
     * 兑换奖励
     */
    @RequestMapping("exchange")
    private HttpResult<EidAlAdhaVO> exchange(@RequestParam String activityId, 
                                             @RequestParam String uid, 
                                             @RequestParam String rewardKey) {
        logger.info("exchange. activityId={} uid={} rewardKey={}", activityId, uid, rewardKey);
        if (!StringUtils.hasLength(rewardKey)) {
            throw new CommonH5Exception(HttpCode.PARAM_ERROR);
        }
        return HttpResult.getOk(eidAlAdhaService.exchange(activityId, uid, rewardKey));
    }

    /**
     * 设置自动售卖
     */
    @RequestMapping("setAutoSell")
    private HttpResult<EidAlAdhaVO> setAutoSell(@RequestParam String activityId, 
                                                @RequestParam String uid, 
                                                @RequestParam Integer enabled) {
        logger.info("setAutoSell. activityId={} uid={} enabled={}", activityId, uid, enabled);
        if (enabled == null || (enabled != 0 && enabled != 1)) {
            throw new CommonH5Exception(HttpCode.PARAM_ERROR);
        }
        return HttpResult.getOk(eidAlAdhaService.setAutoSell(activityId, uid, enabled));
    }

    /**
     * 获取用户花费金币记录
     */
    @RequestMapping("userRecord")
    private HttpResult<PageVO<DrawRecordVO>> getUserRecord(@RequestParam String activityId,
                                                           @RequestParam String uid,
                                                           @RequestParam(defaultValue = "1") Integer page) {
        logger.info("getUserRecord. activityId={} uid={} page={}", activityId, uid, page);
        return HttpResult.getOk(eidAlAdhaService.getUserRecord(activityId, uid, page));
    }
} 