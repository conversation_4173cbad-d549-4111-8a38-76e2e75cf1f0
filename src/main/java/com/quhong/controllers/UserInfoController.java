package com.quhong.controllers;

import com.quhong.data.dto.*;
import com.quhong.data.vo.*;
import com.quhong.datas.HttpResult;
import com.quhong.enums.HttpCode;
import com.quhong.enums.SLangType;
import com.quhong.exception.CommonException;
import com.quhong.handler.AidDTO;
import com.quhong.handler.HttpEnvData;
import com.quhong.handler.WebController;
import com.quhong.service.UserInfoService;
import com.quhong.service.UserOptService;
import com.quhong.utils.RequestUtils;
import jakarta.annotation.Resource;
import jakarta.servlet.http.HttpServletRequest;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;


@RestController
@RequestMapping(value = "${baseUrl}")
public class UserInfoController extends WebController {

    private static final Logger logger = LoggerFactory.getLogger(UserInfoController.class);

    @Resource
    private UserInfoService userInfoService;
    @Resource
    private UserOptService userOptService;

    /**
     * 个人主页信息
     */
    @RequestMapping("actor/info")
    public HttpResult<UserInfoVO> getActorInfo(@RequestBody UserInfoDTO dto) {
        long millis = System.currentTimeMillis();
        if (!dto.isParamsValid()) {
            logger.error("aid is empty dto={}", dto);
            throw new CommonException(HttpCode.PARAM_ERROR);
        }
        UserInfoVO vo = userInfoService.getActorInfo(dto);
        logger.info("getActorInfo. uid={} aid={} timeMillis={}", dto.getUid(), dto.getAid(), System.currentTimeMillis() - millis);
        return HttpResult.getOk(vo);
    }

    /**
     * 房间资料卡
     */
    @RequestMapping("actor/card")
    public HttpResult<UserCardVO> getActorCard(@RequestBody UserCardDTO dto) {
        long millis = System.currentTimeMillis();
        UserCardVO vo = userInfoService.getActorCard(dto);
        logger.info("getActorCard. uid={} aid={} timeMillis={}", dto.getUid(), dto.getAid(), System.currentTimeMillis() - millis);
        return HttpResult.getOk(vo);
    }

    @RequestMapping("actor/me")
    public String getMeUserInfo(HttpServletRequest request) {
        long millis = System.currentTimeMillis();
        HttpEnvData httpEnvData = RequestUtils.getSendData(request, HttpEnvData.class);
        MeUserInfoVO vo = userInfoService.getMeUserInfo(httpEnvData);
        logger.info("getMeUserInfo uid={} timeMillis={}",
                httpEnvData.getUid(), System.currentTimeMillis() - millis);
        return createResult(httpEnvData, HttpCode.SUCCESS, vo);
    }

    @RequestMapping("actor/add_blacklist")
    public String addBlackList(HttpServletRequest request) {
        long millis = System.currentTimeMillis();
        UserInfoDTO userInfoDTO = RequestUtils.getSendData(request, UserInfoDTO.class);
        if (!userInfoDTO.isParamsValid()) {
            logger.error("aid is empty userInfoDTO={}", userInfoDTO);
            return createResult(HttpCode.PARAM_ERROR, new Object());
        }
        userOptService.addBlackList(userInfoDTO.getUid(), userInfoDTO.getAid(), null == userInfoDTO.getForbid() || 1 == userInfoDTO.getForbid());
        logger.info("addBlackList uid={} aid={} forbid={} timeMillis={}",
                userInfoDTO.getUid(), userInfoDTO.getAid(), userInfoDTO.getForbid(), System.currentTimeMillis() - millis);
        return createResult(userInfoDTO, HttpCode.SUCCESS, new Object());
    }

    @RequestMapping("actor/del_blacklist")
    public String delBlackList(HttpServletRequest request) {
        long millis = System.currentTimeMillis();
        UserInfoDTO userInfoDTO = RequestUtils.getSendData(request, UserInfoDTO.class);
        if (!userInfoDTO.isParamsValid()) {
            logger.error("aid is empty userInfoDTO={}", userInfoDTO);
            return createResult(HttpCode.PARAM_ERROR, new Object());
        }
        userOptService.delBlackList(userInfoDTO.getUid(), userInfoDTO.getAid(), userInfoDTO);
        logger.info("delBlackList uid={} aid={} timeMillis={}",
                userInfoDTO.getUid(), userInfoDTO.getAid(), System.currentTimeMillis() - millis);
        return createResult(userInfoDTO, HttpCode.SUCCESS, new Object());
    }

    @RequestMapping("actor/list_blacklist")
    public String listBlackList(HttpServletRequest request) {
        long millis = System.currentTimeMillis();
        UserListPageDTO userListPageDTO = RequestUtils.getSendData(request, UserListPageDTO.class);
        BlackListVO vo = userOptService.getBlackList(userListPageDTO.getUid(), userListPageDTO.getPage(), userListPageDTO.getOs());
        logger.info("listBlackList uid={} page={} timeMillis={}",
                userListPageDTO.getUid(), userListPageDTO.getPage(), System.currentTimeMillis() - millis);
        return createResult(userListPageDTO, HttpCode.SUCCESS, vo);
    }

    @RequestMapping("actor/search_blacklist")
    public String searchBlackList(HttpServletRequest request) {
        long millis = System.currentTimeMillis();
        UserListPageDTO userListPageDTO = RequestUtils.getSendData(request, UserListPageDTO.class);
        BlackListVO vo = userOptService.searchBlackList(userListPageDTO.getUid(), userListPageDTO.getKey());
        logger.info("searchBlackList uid={} key={} timeMillis={}",
                userListPageDTO.getUid(), userListPageDTO.getKey(), System.currentTimeMillis() - millis);
        return createResult(userListPageDTO, HttpCode.SUCCESS, vo);
    }

    @RequestMapping("actor/add_follow")
    public String addFollow(HttpServletRequest request) {
        long millis = System.currentTimeMillis();
        UserInfoDTO userInfoDTO = RequestUtils.getSendData(request, UserInfoDTO.class);
        if (!userInfoDTO.isParamsValid()) {
            logger.error("aid is empty userInfoDTO={}", userInfoDTO);
            return createResult(HttpCode.PARAM_ERROR, new Object());
        }
        AddFollowVO vo = userOptService.addFollow(userInfoDTO.getUid(), userInfoDTO.getAid(), userInfoDTO.getRoomId());
        logger.info("addFollow uid={} aid={} timeMillis={}",
                userInfoDTO.getUid(), userInfoDTO.getAid(), System.currentTimeMillis() - millis);
        return createResult(userInfoDTO, HttpCode.SUCCESS, vo);
    }

    @RequestMapping("actor/cancel_follow")
    public String cancelFollow(HttpServletRequest request) {
        long millis = System.currentTimeMillis();
        UserInfoDTO userInfoDTO = RequestUtils.getSendData(request, UserInfoDTO.class);
        if (!userInfoDTO.isParamsValid()) {
            logger.error("aid is empty userInfoDTO={}", userInfoDTO);
            return createResult(HttpCode.PARAM_ERROR, new Object());
        }
        AddFollowVO vo = userOptService.cancelFollow(userInfoDTO.getUid(), userInfoDTO.getAid());
        logger.info("cancelFollow uid={} aid={} timeMillis={}",
                userInfoDTO.getUid(), userInfoDTO.getAid(), System.currentTimeMillis() - millis);
        return createResult(userInfoDTO, HttpCode.SUCCESS, vo);
    }

    /**
     * 查询follow状态
     */
    @RequestMapping("actor/follow_status")
    public HttpResult<FollowStatusVO> followStatus(@RequestBody AidDTO dto) {
        return HttpResult.getOk(userInfoService.followStatus(dto.getUid(), dto.getAid()));
    }

    @RequestMapping("actor/list_homepage_visitors")
    public String listHomepageVisitors(HttpServletRequest request) {
        long millis = System.currentTimeMillis();
        UserListVisitorsDTO userInfoDTO = RequestUtils.getSendData(request, UserListVisitorsDTO.class);
        HomePageVisitorsDetailVO vo = userInfoService.listHomepageVisitors(userInfoDTO.getUid(), userInfoDTO.getAtype());
        logger.info("listHomepageVisitors uid={} atype={} timeMillis={}",
                userInfoDTO.getUid(), userInfoDTO.getAtype(), System.currentTimeMillis() - millis);
        return createResult(userInfoDTO, HttpCode.SUCCESS, vo);
    }

    /**
     * 清除访客new标签
     */
    @RequestMapping("actor/clear_new_visitor")
    public HttpResult<Object> clearNewVisitor(@RequestBody UserCardDTO dto) {
        logger.info("clearNewVisitor uid={} aid={}", dto.getUid(), dto.getAid());
        return HttpResult.getOk(userInfoService.clearNewVisitor(dto));
    }

    @RequestMapping("actor/v2/label_info")
    public String labelInfo(HttpServletRequest request) {
        long millis = System.currentTimeMillis();
        HttpEnvData httpEnvData = RequestUtils.getSendData(request, HttpEnvData.class);
        LabelInfoConfigVO vo = userOptService.labelInfoConfig(httpEnvData.getUid(), httpEnvData.getSlang());
        logger.info("labelInfo uid={} timeMillis={}",
                httpEnvData.getUid(), System.currentTimeMillis() - millis);
        return createResult(httpEnvData, HttpCode.SUCCESS, vo);
    }

    @RequestMapping("actor/visitor/label_info")
    public String visitorLabelInfo(HttpServletRequest request) {
        LabelInfoConfigVO vo = userOptService.labelInfoConfig("", SLangType.ENGLISH);
        return createResult(HttpCode.SUCCESS, vo);
    }

    @RequestMapping("actor/user_label")
    public String oldUserLabel(HttpServletRequest request) {
        HttpEnvData httpEnvData = RequestUtils.getSendData(request, HttpEnvData.class);
        List<LabelInfoConfigVO.LabelItemInfo> labelList = userOptService.oldUserLabel(httpEnvData);
        return createResult(httpEnvData, HttpCode.SUCCESS, labelList);
    }


    @RequestMapping("actor/update/v2")
    public String userUpdateV2(HttpServletRequest request) {
        long millis = System.currentTimeMillis();
        UpdateUserInfoDTO dto = RequestUtils.getSendData(request, UpdateUserInfoDTO.class);
        logger.info("userUpdateV2 dto={}", dto);
        UpdateUserInfoVO vo = userOptService.updateUserInfo(dto);
        logger.info("userUpdateV2 uid={} timeMillis={}", dto.getUid(), System.currentTimeMillis() - millis);
        return createResult(dto, HttpCode.SUCCESS, vo);
    }

    /**
     * 8.25及以后只有非Google，facebook注册调用，之前都调用
     *
     * @param request
     * @return
     */
    @RequestMapping("actor/improve")
    public String userImprove(HttpServletRequest request) {
        long millis = System.currentTimeMillis();
        UpdateUserInfoDTO dto = RequestUtils.getSendData(request, UpdateUserInfoDTO.class);
        logger.info("userImprove dto={}", dto);
        UpdateUserInfoVO vo = userOptService.improveUserInfo(dto);
        logger.info("userImprove uid={} timeMillis={}", dto.getUid(), System.currentTimeMillis() - millis);
        return createResult(dto, HttpCode.SUCCESS, vo);
    }

    /**
     * 更新用户兴趣标签
     */
    @RequestMapping("actor/label_update")
    public HttpResult<Object> labelUpdate(@RequestBody UpdateUserInfoDTO dto) {
        long millis = System.currentTimeMillis();
        logger.info("labelUpdate dto:{} ", dto);
        userOptService.labelUpdate(dto);
        logger.info("labelUpdate uid={} timeMillis={}", dto.getUid(), System.currentTimeMillis() - millis);
        return HttpResult.getOk();
    }

    /**
     * 个人主页用户资源展示
     */
    @RequestMapping("actor/resShow")
    public HttpResult<UserResShowVO> userResourceShow(@RequestBody UserResShowDTO dto) {
        long millis = System.currentTimeMillis();
        UserResShowVO vo = userInfoService.getUserResourceShow(dto);
        logger.info("getUserResourceShow. uid={} type={} page={} cost={}", dto.getUid(), dto.getType(), dto.getPage(), System.currentTimeMillis() - millis);
        return HttpResult.getOk(vo);
    }
}
