package com.quhong.controllers;

import com.quhong.data.dto.VoiceRoomDTO;
import com.quhong.data.vo.VoiceRoomRankingVO;
import com.quhong.datas.HttpResult;
import com.quhong.service.VoiceRankingService;
import jakarta.annotation.Resource;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * 房间模块/语聊房
 */
@RestController
@RequestMapping("/room_service/voice_room")
public class VoiceRoomController extends AbstractRoomController {

    private static final Logger logger = LoggerFactory.getLogger(VoiceRoomController.class);

    @Resource
    private VoiceRankingService voiceRankingService;

    /**
     * 财富榜、魅力榜、房间榜
     */
    @RequestMapping("ranking")
    private HttpResult<VoiceRoomRankingVO> ranking(@RequestBody VoiceRoomDTO.RankingDTO req) {
        logger.info("voice ranking. uid={} rankTime={} ranking={}", req.getUid(), req.getRankTime(), req.getRankType());
        return HttpResult.getOk(voiceRankingService.getRanking(req.getUid(), req.getRankTime(), req.getRankType()));
    }

    /**
     * 名人榜
     */
    @RequestMapping("celebrity")
    private HttpResult<VoiceRoomRankingVO> celebrity(@RequestBody VoiceRoomDTO.CelebrityDTO req) {
        return HttpResult.getOk(voiceRankingService.getVoiceCelebrityRanking(req.getUid(), req.getStartDate(), req.getEndDate(), req.getRankType()));
    }
}
