package com.quhong.controllers;

import com.alibaba.fastjson.JSON;
import com.quhong.data.dto.NoticeSettingDTO;
import com.quhong.data.vo.NoticeSettingVO;
import com.quhong.enums.HttpCode;
import com.quhong.enums.SundryHttpCode;
import com.quhong.handler.WebController;
import com.quhong.service.NoticeSettingService;
import com.quhong.utils.RequestUtils;
import jakarta.annotation.Resource;
import jakarta.servlet.http.HttpServletRequest;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * <AUTHOR>
 * @date 2022/8/17
 */
@RestController
@RequestMapping("${baseUrl}")
public class NoticeSettingController extends WebController {

    private static final Logger logger = LoggerFactory.getLogger(NoticeSettingController.class);

    @Resource
    private NoticeSettingService noticeSettingService;

    /**
     * 设置各种开关
     */
    @RequestMapping("app/setting/notice")
    public String settingNotice(HttpServletRequest request) {
        NoticeSettingDTO req = RequestUtils.getSendData(request, NoticeSettingDTO.class);
        logger.info("setting notice.uid={} req={}", req.getUid(), JSON.toJSONString(req));
        noticeSettingService.settingNotice(req);
        return createResult(req, HttpCode.SUCCESS, null);
    }

    /**
     * 获取各种开关状态
     */
    @RequestMapping("app/setting/get/notice")
    public String getNoticeSetting(HttpServletRequest request) {
        NoticeSettingDTO req = RequestUtils.getSendData(request, NoticeSettingDTO.class);
        logger.info("get notice setting.uid={} requestId={}", req.getUid(), req.getRequestId());
        NoticeSettingVO vo = noticeSettingService.getNoticeSetting(req);
        return createResult(req, SundryHttpCode.SUCCESS_RESULT, vo);
    }
}

