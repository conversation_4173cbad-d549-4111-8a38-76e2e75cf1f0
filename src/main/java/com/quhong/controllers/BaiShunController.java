package com.quhong.controllers;

import com.alibaba.fastjson.JSONObject;
import com.quhong.core.config.ServerConfig;
import com.quhong.core.distribution.DistributeLock;
import com.quhong.core.utils.DateHelper;
import com.quhong.data.BaiShunGameInfo;
import com.quhong.datas.HttpResult;
import com.quhong.dto.*;
import com.quhong.enums.GameHttpCode;
import com.quhong.enums.HttpCode;
import com.quhong.enums.LogType;
import com.quhong.exception.CommonException;
import com.quhong.exception.GameException;
import com.quhong.service.BaiShunService;
import com.quhong.utils.WalletUtils;
import com.quhong.vo.BaiShunResult;
import com.quhong.vo.BsChangeBalanceVO;
import com.quhong.vo.BsGameTaskVO;
import jakarta.annotation.Resource;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;


/**
 * 百顺小游戏
 *
 * <AUTHOR>
 * @date 2023/7/31
 */
@RestController
@RequestMapping("${baseUrl}bai_shun_game/")
public class BaiShunController {

    private static final Logger logger = LoggerFactory.getLogger(BaiShunController.class);
    private static final Logger msgLogger = LoggerFactory.getLogger(LogType.MESSAGE_LOG);

    @Resource
    private BaiShunService baiShunService;

    /**
     * 获取调⽤接⼝的⻓期令牌
     * 调用方：百顺游戏服务
     */
    @PostMapping("/get_sstoken")
    public Object getSSToken(@RequestBody BsGetSSTokenDTO reqParam) {
        msgLogger.info("get sstoken. reqParam={}", JSONObject.toJSONString(reqParam));
        try {
            return baiShunService.getSSToken(reqParam);
        } catch (Exception e) {
            logger.error("get sstoken error. {}", e.getMessage(), e);
            return BaiShunResult.getError(1021, "server error");
        }
    }

    /**
     * 刷新长期令牌
     * 调用方：百顺游戏服务
     */
    @PostMapping("/update_sstoken")
    public Object updateSSToken(@RequestBody BsUpdateSSTokenDTO reqParam) {
        msgLogger.info("update sstoken. reqParam={}", JSONObject.toJSONString(reqParam));
        try {
            return baiShunService.updateSSToken(reqParam);
        } catch (Exception e) {
            logger.error("update sstoken error. {}", e.getMessage(), e);
            return BaiShunResult.getError(1021, "server error");
        }
    }

    /**
     * 获取用户信息
     * 调用方：百顺游戏服务
     */
    @PostMapping("/get_user_info")
    public Object getUserInfo(@RequestBody BsGetUserInfoDTO reqParam) {
        msgLogger.info("get user info. reqParam={}", JSONObject.toJSONString(reqParam));
        try {
            return baiShunService.getUserInfo(reqParam);
        } catch (Exception e) {
            logger.error("get user info error. {}", e.getMessage(), e);
            return BaiShunResult.getError(1021, "server error");
        }
    }

    /**
     * 游戏下注和结算修改app的平台货币
     * 调用方：游戏服务
     */
    @PostMapping("/change_balance")
    public Object changeBalance(@RequestBody BsChangeBalanceDTO reqParam) {
        msgLogger.info("change user balance. reqParam={}", JSONObject.toJSONString(reqParam));
        try {
            long timeMillis = System.currentTimeMillis();
            BaiShunResult<BsChangeBalanceVO> result = baiShunService.changeBalance(reqParam);
            if (DateHelper.getNowSeconds() < 1728727200) {
                logger.info("change user balance. gameId={} cost={}", reqParam.getGame_id(), System.currentTimeMillis() - timeMillis);
            }
            return result;
        } catch (Exception e) {
            logger.error("change user balance error. reqParam={} {}", JSONObject.toJSONString(reqParam), e.getMessage(), e);
            return BaiShunResult.getError(1021, "server error");
        }
    }

    /**
     * 获取百顺⼩游戏信息
     * 调用方：客户端
     */
    @PostMapping("/one_game_info")
    public HttpResult<BaiShunGameInfo> getGameInfo(@RequestBody BsGameDTO reqDTO) {
        logger.info("get game info. uid={} gameId={}", reqDTO.getUid(), reqDTO.getGame_id());
        return HttpResult.getOk(baiShunService.getGameInfo(reqDTO));
    }

    /**
     * 获取百顺⼩游戏列表
     * 调用方：客户端
     */
    @PostMapping("/gamelist")
    public HttpResult<List<BaiShunGameInfo>> getGameList(@RequestBody BsGameDTO reqDTO) {
        logger.info("get game info list. uid={} gameListType={}", reqDTO.getUid(), reqDTO.getGame_list_type());
        return HttpResult.getOk(baiShunService.getGameList(reqDTO));
    }

    /**
     * 新增百顺⼩游戏点控任务
     * 调用方：客户端
     */
    @PostMapping("/add_task")
    public HttpResult<BsGameTaskVO> addTask(@RequestBody GameTaskDTO reqDTO) {
        if (ServerConfig.isProduct()) {
            throw new GameException(GameHttpCode.PARAM_ERROR);
        }
        logger.info("addTask. uid={} user_type={} release_cond={}", reqDTO.getUid(), reqDTO.getUser_type(), reqDTO.getRelease_cond());
        return HttpResult.getOk(baiShunService.addTask(reqDTO));
    }

    /**
     * 删除百顺⼩游戏点控任务
     * 调用方：客户端
     */
    @PostMapping("/del_task")
    public HttpResult<BsGameTaskVO> delTask(@RequestBody GameTaskDTO reqDTO) {
        if (ServerConfig.isProduct()) {
            throw new GameException(GameHttpCode.PARAM_ERROR);
        }
        logger.info("delTask. uid={} task_id={}", reqDTO.getUid(), reqDTO.getTask_id());
        return HttpResult.getOk(baiShunService.delTask(reqDTO));
    }

    /**
     * 获取百顺⼩游戏点控任务信息
     * 调用方：客户端
     */
    @PostMapping("/task_info")
    public HttpResult<BsGameTaskVO> getTaskInfo(@RequestBody GameTaskDTO reqDTO) {
        if (ServerConfig.isProduct()) {
            throw new GameException(GameHttpCode.PARAM_ERROR);
        }
        logger.info("getTaskInfo. uid={} task_id={}", reqDTO.getUid(), reqDTO.getTask_id());
        return HttpResult.getOk(baiShunService.getTaskInfo(reqDTO));
    }
}
