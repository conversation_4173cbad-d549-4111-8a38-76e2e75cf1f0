package com.quhong.controllers;

import com.quhong.data.vo.GuardianActivityVO;
import com.quhong.data.vo.GuardianDrawVO;
import com.quhong.data.vo.GuardianRecordVO;
import com.quhong.datas.HttpResult;
import com.quhong.enums.HttpCode;
import com.quhong.exception.CommonH5Exception;
import com.quhong.service.GuardianService;
import com.quhong.vo.PageVO;
import jakarta.annotation.Resource;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.util.StringUtils;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

/**
 * 守护摩天轮活动
 *
 * <AUTHOR>
 * @date 2024/12/10
 */
@RestController
@RequestMapping("${baseUrl}guardian/")
public class GuardianController {

    private final Logger logger = LoggerFactory.getLogger(getClass());

    @Resource
    private GuardianService guardianService;

    /**
     * 活动信息
     */
    @RequestMapping("info")
    private HttpResult<GuardianActivityVO> getInfo(@RequestParam String activityId, @RequestParam String uid) {
        logger.info("getInfo. activityId={} uid={}", activityId, uid);
        return HttpResult.getOk(guardianService.getInfo(activityId, uid));
    }

    /**
     * 抽奖
     */
    @RequestMapping("draw")
    private HttpResult<GuardianDrawVO> draw(@RequestParam String activityId, @RequestParam String uid, @RequestParam Integer num) {
        logger.info("draw. activityId={} uid={} num={}", activityId, uid, num);
        if (num == null || num <= 0) {
            throw new CommonH5Exception(HttpCode.PARAM_ERROR);
        }
        return HttpResult.getOk(guardianService.draw(activityId, uid, num));
    }

    /**
     * 兑换奖励
     */
    @RequestMapping("exchange")
    private HttpResult<GuardianActivityVO> exchange(@RequestParam String activityId, @RequestParam String uid, @RequestParam String rewardKey) {
        logger.info("exchange. activityId={} uid={} rewardKey={}", activityId, uid, rewardKey);
        if (!StringUtils.hasLength(rewardKey)) {
            throw new CommonH5Exception(HttpCode.PARAM_ERROR);
        }
        return HttpResult.getOk(guardianService.exchange(activityId, uid, rewardKey));
    }

    /**
     * 购买抽奖次数
     */
    @RequestMapping("buyDrawNum")
    private HttpResult<GuardianActivityVO> buyDrawNum(@RequestParam String activityId,
                                                      @RequestParam String uid,
                                                      @RequestParam Integer num,
                                                      @RequestParam(defaultValue = "2") Integer slang) {
        logger.info("buyDrawNum. activityId={} uid={} num={}", activityId, uid, num);
        if (num == null || num <= 0) {
            throw new CommonH5Exception(HttpCode.PARAM_ERROR);
        }
        return guardianService.buyDrawNum(activityId, uid, num, slang);
    }

    /**
     * 赠送信封
     */
    @RequestMapping("sendEnvelope")
    private HttpResult<Object> sendEnvelope(@RequestParam String activityId,
                                                        @RequestParam String uid,
                                                        @RequestParam String rid,
                                                        @RequestParam(defaultValue = "2") Integer slang) {
        logger.info("sendEnvelope. activityId={} uid={} rid={}", activityId, uid, rid);
        if (!StringUtils.hasLength(rid)) {
            throw new CommonH5Exception(HttpCode.PARAM_ERROR);
        }
        return guardianService.sendEnvelope(activityId, uid, rid, slang);
    }

    /**
     * 记录
     */
    @RequestMapping("record")
    private HttpResult<PageVO<GuardianRecordVO>> record(@RequestParam String activityId,
                                                        @RequestParam String uid,
                                                        @RequestParam int type,
                                                        @RequestParam int page) {
        logger.info("record. activityId={} uid={} type={} page={}", activityId, uid, type, page);
        return HttpResult.getOk(guardianService.record(activityId, uid, type, page));
    }
}
