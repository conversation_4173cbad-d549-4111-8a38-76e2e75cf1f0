package com.quhong.controllers;

import com.quhong.constant.UserHttpCode;
import com.quhong.data.dto.SetResourcesDTO;
import com.quhong.data.dto.StoreDTO;
import com.quhong.data.dto.StoreGoodsDTO;
import com.quhong.data.vo.GoodsListHomeVO;
import com.quhong.datas.HttpResult;
import com.quhong.enums.HttpCode;
import com.quhong.enums.UserInfoHttpCode;
import com.quhong.exception.CommonException;
import com.quhong.handler.WebController;
import com.quhong.service.FloatScreenService;
import com.quhong.service.JoinCartonService;
import com.quhong.service.MicFrameService;
import com.quhong.service.StoreService;
import com.quhong.utils.RequestUtils;
import com.quhong.utils.WalletUtils;
import jakarta.annotation.Resource;
import jakarta.servlet.http.HttpServletRequest;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * 商店
 */
@RestController
@RequestMapping(value = "${baseUrl}")
public class StoreController extends WebController {

    private static final Logger logger = LoggerFactory.getLogger(StoreController.class);

    @Resource
    private StoreService storeService;
    @Resource
    private MicFrameService micFrameService;
    @Resource
    private FloatScreenService floatScreenService;
    @Resource
    private JoinCartonService joinCartonService;

    /**
     * 获取指定商品的价格、状态、剩余天数等信息
     */
    @RequestMapping("store/check")
    public String storeCheck(HttpServletRequest request) {
        long millis = System.currentTimeMillis();
        StoreDTO req = RequestUtils.getSendData(request, StoreDTO.class);
        logger.info("store check. uid={} gid={} requestId={} timeMillis={}", req.getUid(), req.getGid(), req.getRequestId(), System.currentTimeMillis() - millis);
        return createResult(req, HttpCode.SUCCESS, null);
    }

    /**
     * 特权商店列表
     */
    @RequestMapping("store/list")
    public String getStoreList(HttpServletRequest request) {
        long millis = System.currentTimeMillis();
        StoreDTO req = RequestUtils.getSendData(request, StoreDTO.class);
        logger.info("get store list. uid={} requestId={} timeMillis={}", req.getUid(), req.getRequestId(), System.currentTimeMillis() - millis);
        return createResult(req, UserInfoHttpCode.PARAMETER_UPDATE_APP, null);
    }

    /**
     * 购买麦位框
     */
    @RequestMapping("new_store/mic_frame/buy")
    public String buyFrameMic(HttpServletRequest request) {
        long millis = System.currentTimeMillis();
        StoreGoodsDTO req = RequestUtils.getSendData(request, StoreGoodsDTO.class);
        micFrameService.buyGoods(req);
        logger.info("buy mic frame. uid={} mic_id={} timeMillis={}", req.getUid(), req.getMic_frame_id(), System.currentTimeMillis() - millis);
        return createResult(req, UserHttpCode.MIC_FRAME_BUY_SUCCESS, null);
    }

    /**
     * 购买浮萍
     */
    @RequestMapping("new_store/float_screen/buy")
    public String buyFloatScreen(HttpServletRequest request) {
        long millis = System.currentTimeMillis();
        StoreGoodsDTO req = RequestUtils.getSendData(request, StoreGoodsDTO.class);
        floatScreenService.buyGoods(req);
        logger.info("buy FloatScreen uid={} screen_id={} timeMillis={}", req.getUid(), req.getScreen_id(), System.currentTimeMillis() - millis);
        return createResult(req, UserHttpCode.FLOAT_SCREEN_BUY_SUCCESS, null);
    }


    /**
     * 购买坐骑
     */
    @RequestMapping("new_store/ride/buy")
    public String buyRide(HttpServletRequest request) {
        long millis = System.currentTimeMillis();
        StoreGoodsDTO req = RequestUtils.getSendData(request, StoreGoodsDTO.class);
        joinCartonService.buyGoods(req);
        logger.info("buy ride uid={} joinId={} timeMillis={}", req.getUid(), req.getJoin_id(), System.currentTimeMillis() - millis);
        return createResult(req, UserHttpCode.RIDE_BUY_SUCCESS, null);
    }

    /**
     * 新版商店首页列表
     */
    @RequestMapping("new_store/goods/list")
    public HttpResult<GoodsListHomeVO> goodsList(@RequestBody StoreDTO req) {
        return HttpResult.getOk(storeService.getGoodsListHomeVO(req));
    }

    /**
     * 新版商店购买
     */
    @RequestMapping("new_store/goods/buy")
    public String goodsBuy(HttpServletRequest request) {
        long millis = System.currentTimeMillis();
        StoreGoodsDTO req = RequestUtils.getSendData(request, StoreGoodsDTO.class);
        logger.info("goods buy req={}", req);
        if (WalletUtils.accessControl()) {
            throw new CommonException(HttpCode.SERVER_UPGRADING);
        }
        HttpCode code = storeService.goodsBuy(req);
        logger.info("goods buy success. timeMillis={}", System.currentTimeMillis() - millis);
        return createResult(req, code, null);
    }

    /**
     * 新版商店设置
     */
    @RequestMapping("new_store/goods/set")
    public String goodsSet(HttpServletRequest request) {
        long millis = System.currentTimeMillis();
        SetResourcesDTO req = RequestUtils.getSendData(request, SetResourcesDTO.class);
        logger.info("goods set req={}", req);
        HttpCode code = storeService.goodsSet(req);
        logger.info("goods set success timeMillis={}", System.currentTimeMillis() - millis);
        return createResult(req, code, null);
    }

    /**
     * 新版商店商品详情
     */
    @RequestMapping("new_store/goods/list/detail")
    public String getStoreGoodsList(HttpServletRequest request) {
        long millis = System.currentTimeMillis();
        StoreDTO req = RequestUtils.getSendData(request, StoreDTO.class);
        logger.info("goods detail req={}", req);
        GoodsListHomeVO vo = storeService.getStoreGoodsList(req);
        logger.info("goods detail success timeMillis={}", System.currentTimeMillis() - millis);
        return createResult(req, HttpCode.SUCCESS, vo);
    }

    /**
     * 新版商店我的装扮详情
     */
    @RequestMapping("new_store/goods/list/my_detail")
    public String getMyGoodsList(HttpServletRequest request) {
        long millis = System.currentTimeMillis();
        StoreDTO req = RequestUtils.getSendData(request, StoreDTO.class);
        logger.info("goods my_detail req={}", req);
        GoodsListHomeVO vo = storeService.getMyGoodsList(req);
        logger.info("goods my_detail success timeMillis={}", System.currentTimeMillis() - millis);
        return createResult(req, HttpCode.SUCCESS, vo);
    }

    /**
     * 新版商店购买
     */
    @RequestMapping("new_store/goods/unlock")
    public String goodsUnLockSend(HttpServletRequest request) {
        long millis = System.currentTimeMillis();
        StoreGoodsDTO req = RequestUtils.getSendData(request, StoreGoodsDTO.class);
        logger.info("goods unlock req={}", req);
        HttpCode code = storeService.goodsUnLockSend(req);
        logger.info("goods unlock success. timeMillis={}", System.currentTimeMillis() - millis);
        return createResult(req, code, null);
    }
}
