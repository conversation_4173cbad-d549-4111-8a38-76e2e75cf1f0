package com.quhong.enums;

/**
 * <AUTHOR>
 * @date 2022/9/19
 * 弃用，使用最新的com.quhong.enums.ResTypeEnum
 * @see ResTypeEnum
 */
@Deprecated
public enum RewardTypeEnum {

    // 金币
    COIN(-1, "coin"),
    // 钻石
    DIAMOND(0, "diamond"),
    // 勋章
    BADGE(1, "badge"),
    // 麦位框
    MIC(2, "mic"),
    // 坐骑
    RIDE(3, "ride"),
    // 背包礼物
    BAG_GIFT(4, "gift"),
    // 房间锁
    ROOM_LOCK(5, "lock"),
    // 气泡框
    BUDDLE(6, "buddle"),
    // 声波
    RIPPLE(7, "ripple"),
    // 浮屏
    FLOAT_SCREEN(8, "float_screen"),
    // 房间背景
    BACKGROUND(9, "background"),
    // vip
    VIP(102, "vip");

    private int code;
    private String name;

    RewardTypeEnum(int code, String name) {
        this.code = code;
        this.name = name;
    }

    public int getCode() {
        return code;
    }

    public void setCode(int code) {
        this.code = code;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public static RewardTypeEnum getEnumByName(String name) {
        for (RewardTypeEnum rewardTypeEnum : RewardTypeEnum.values()) {
            if (rewardTypeEnum.getName().equals(name)) {
                return rewardTypeEnum;
            }
        }
        return null;
    }

    public static RewardTypeEnum getEnumByCode(int code) {
        for (RewardTypeEnum rewardTypeEnum : RewardTypeEnum.values()) {
            if (rewardTypeEnum.getCode() == code) {
                return rewardTypeEnum;
            }
        }
        return null;
    }

    public static boolean showRewardDays(String name) {
        if (RewardTypeEnum.COIN.getName().equals(name)) {
            return false;
        }
        if (RewardTypeEnum.DIAMOND.getName().equals(name)) {
            return false;
        }
        if (RewardTypeEnum.BAG_GIFT.getName().equals(name)) {
            return false;
        }
        return true;
    }

    public ResTypeEnum toResTypeEnum() {
        if (0 == code) {
            return ResTypeEnum.DIAMONDS;
        } else if (1 == code) {
            return ResTypeEnum.BADGE;
        } else if (2 == code) {
            return ResTypeEnum.MIC;
        } else if (3 == code) {
            return ResTypeEnum.RIDE;
        } else if (4 == code) {
            return ResTypeEnum.BAG_GIFT;
        } else if (5 == code) {
            return ResTypeEnum.ROOM_LOCKER;
        } else if (6 == code) {
            return ResTypeEnum.BUBBLE;
        } else if (7 == code) {
            return ResTypeEnum.RIPPLE;
        } else if (8 == code) {
            return ResTypeEnum.FLOAT_SCREEN;
        } else if (9 == code) {
            return ResTypeEnum.MINE_BACKGROUND;
        } else if (102 == code) {
            return ResTypeEnum.VIP_LEVEL;
        } else {
            return ResTypeEnum.COIN;
        }
    }
}
