package com.quhong.enums;

public class RoomHttpCode extends HttpCode {
    public static final HttpCode BE_KICKED_OUT_OF_THE_ROOM = new HttpCode(0, "be_kicked_out_of_the_room");
    public static final HttpCode NEED_BECOME_MEMBER = new HttpCode(2002, "");
    public static final HttpCode KICK_OUT = new HttpCode(91, "'You are kicked out of the room by room owner and will not be able to enter the room in 24hrs", "تم طردك من الغرفة بواسطة مالك الغرفة ولا يمكنك دخول الغرفة مرّة أخرى لمدة 24 ساعة");
    public static final HttpCode ROOM_PARAM_ERROR = new HttpCode(91, "");
    public static final HttpCode PERMIT_ERROR = new HttpCode(2101, "error code:2101");
    public static final HttpCode ROOM_NOT_EXIST = new HttpCode(44, "room_not_exist");
    public static final HttpCode BLOCK_CREATE_ROOM = new HttpCode(72, "block_create_room");
    public static final HttpCode ROOM_BLOCK = new HttpCode(1041, "room_block");
    public static final HttpCode ROOM_FULL = new HttpCode(56, "room_full");
    public static final HttpCode REPORTED = new HttpCode(42, "reported");
    public static final HttpCode USER_INVALID = new HttpCode(48, "user_invalid");
    public static final HttpCode BLOCKED = new HttpCode(47, "blocked");
    public static final HttpCode KICKED = new HttpCode(46, "kicked");
    public static final HttpCode KICKED_HOURS = new HttpCode(46, "kicked_hours");
    public static final HttpCode KICKED_MINUTES = new HttpCode(46, "kicked_minutes");
    public static final HttpCode DIRTY_WORD_TITLE = new HttpCode(141, "dirty_word_title");
    public static final HttpCode PASSWORD_ERROR = new HttpCode(122, "password_error");
    public static final HttpCode PLEASE_UPGRADE_VERSION = new HttpCode(126, "please_upgrade_version");
    public static final HttpCode NEED_UPGRADE = new HttpCode(441, "need_upgrade");
    public static final HttpCode NOT_ROOM_ADMIN = new HttpCode(411, "not_room_owner_or_vice_owner");
    public static final HttpCode ADMIN_NUM_REACHED_LIMIT = new HttpCode(145, "admin_num_reached_limit");
    public static final HttpCode THEME_NOT_VALID = new HttpCode(10, "theme_not_valid");
    public static final HttpCode ROOM_INFO_HAS_DIRTY_WORD = new HttpCode(412, "room_info_has_dirty_word");
    public static final HttpCode PERMISSION_DENIED_DELETE_MEMBER = new HttpCode(413, "permission_denied_delete_member");
    public static final HttpCode OVER_THE_LIMITATION = new HttpCode(45, "over_the_limitation");
    public static final HttpCode ALREADY_ROOM_MEMBER = new HttpCode(30, "already_room_member");
    public static final HttpCode KEY_WORD_CANNOT_BE_NULL = new HttpCode(20, "key_word_cannot_be_null");
    public static final HttpCode NO_PERMISSION_KICK_USER = new HttpCode(60, "no_permission_kick_user");
    public static final HttpCode NOT_KICK_ADMIN_USER = new HttpCode(61, "not_kick_admin_user");
    public static final HttpCode MESSAGE_EXPIRED = new HttpCode(64, "message_expired");
    public static final HttpCode NO_RECORD_OF_KICK = new HttpCode(65, "no_record_of_kick");
    public static final HttpCode UNION_NOT_ENOUGH_DIAMOND = new HttpCode(50, "not_enough_diamond");
    public static final HttpCode NO_PERMISSION_TO_OPERATE = new HttpCode(414, "no_permission_to_operate");
    public static final HttpCode ACCOUNT_HAS_BEEN_FROZEN = new HttpCode(42, "account_has_been_frozen");
    public static final HttpCode SEND_PIC_TURNED_OFF = new HttpCode(43, "send_pic_turned_off");
    public static final HttpCode YOU_HAVE_BEEN_BANNED = new HttpCode(44, "you_have_been_banned");
    public static final HttpCode NOT_FORBID_USER_TALK = new HttpCode(47, "not_forbid_user_talk");
    public static final HttpCode ALREADY_FORBID_USER_TALK = new HttpCode(48, "already_forbid_user_talk");
    public static final HttpCode NO_PERMISSION_CANCEL_BAN_TEXT = new HttpCode(49, "no_permission_cancel_ban_text");
    public static final HttpCode NOT_CANCEL_FORBID_USER_TALK = new HttpCode(51, "not_cancel_forbid_user_talk");
    public static final HttpCode NO_PERMISSION_FORBID_ALL_MIC = new HttpCode(51, "no_permission_forbid_all_mic");

    public static final HttpCode VIP2_EXPIRED = new HttpCode(61, "vip2_expired");
    public static final HttpCode BACKGROUND_EXPIRED = new HttpCode(63, "background_expired");
    public static final HttpCode UPLOAD_BG_NO_MORE_THAN_10_PHOTOS = new HttpCode(64, "upload_bg_no_more_than_10_photos");
    public static final HttpCode NOT_HAVE_PERMISSION = new HttpCode(41, "not_have_permission");
    public static final HttpCode PLEASE_CHANGE_TIME = new HttpCode(42, "please_change_time");
    public static final HttpCode HAVE_ALREADY_BEEN_REWARDED = new HttpCode(44, "have_already_been_rewarded");
    public static final HttpCode NO_REWARDS_AVAILABLE = new HttpCode(45, "no_rewards_available");
    public static final HttpCode TIMED_OUT_AND_CANNOT_BE_CLAIMED = new HttpCode(46, "timed_out_and_cannot_be_claimed");
    public static final HttpCode EVENT_TIME_HAS_EXPIRED = new HttpCode(47, "event_time_has_expired");
    public static final HttpCode EVENT_HAS_STARTED = new HttpCode(48, "event_has_started");
    public static final HttpCode EVENT_HAS_BEEN_DELETED = new HttpCode(49, "event_has_been_deleted");
    public static final HttpCode NOT_HAVE_PERMISSION_DELETE_EVENT = new HttpCode(51, "not_have_permission_delete_event");
    public static final HttpCode HAVE_DIRTY_WORDS_OR_OFFENDING_IMAGES = new HttpCode(60, "have_dirty_words_or_offending_images");
    public static final HttpCode APPLICATION_REQUIRED = new HttpCode(666, "For better experience, please update waho in the store.", "من أجل الحصول على تجربة أفضل ، يرجى تحديث waho في المتجر");
    public static final HttpCode APPLICATION_CANCELED = new HttpCode(667, "Up mic application canceled.");
    public static final HttpCode APPLIED = new HttpCode(668, "You have applied for a mic.", "لقد تقدمت بطلب للحصول على ميكروفون");

    public static final HttpCode ILLEGAL_CONTENT = new HttpCode(102, "illegal_content");
    public static final HttpCode PLEASE_JOIN_THE_ROOM_MEMBERS_FIRST = new HttpCode(103, "please_join_the_room_members_first");
    public static final HttpCode PLEASE_FOLLOWING_ROOM_FIRST = new HttpCode(104, "please_following_room_first");
    public static final HttpCode NO_BROADCASTABLE_USER = new HttpCode(105, "no_broadcastable_users");
    public static final HttpCode INVALID_EMAIL_ADDRESS = new HttpCode(109, "invalid_email_address");

    public static final HttpCode FAMILY_ADMIN_LIMIT = new HttpCode(2111, "The maximum number of administrators has been reached.", "تم بلوغ الحد الأقصى لعدد المسؤولين.");
    public static final HttpCode FAMILY_FULL = new HttpCode(2112, "The Family is already full.", "العائلة مكتملة بالفعل.");
    public static final HttpCode FAMILY_DO_NOT_REAPPLY = new HttpCode(2113, "Your application has been rejected. Please do not reapply.", "تم رفض طلبك. يرجى عدم تقديم طلب جديد.");
    public static final HttpCode FAMILY_ONLY_JOIN_ONE = new HttpCode(2114, "Each user can only join one Family.", "يمكن لكل مستخدم أن ينضم إلى عائلة واحدة فقط.");
    public static final HttpCode FAMILY_LEADER_LEAVE = new HttpCode(2115, "Family leaders cannot leave the Family.", "لا يمكن لقادة العائلة مغادرة العائلة.");
    public static final HttpCode PWD_SIX_DIGIT_ONLY = new HttpCode(2116, "Only six-digit passcodes are supported.");
    public static final HttpCode LEAVE_ONCE_LIMIT = new HttpCode(2117, "You are only allowed to apply to leave the family once within 30 days. Please try again later.", "يُسمح لك بتقديم طلب للخروج من العائلة مرة واحدة فقط في غضون 30 يومًا. يرجى المحاولة مرة أخرى لاحقًا.");
    public static final HttpCode ACTOR_NOT_IN_ROOM = new HttpCode(2118, "Actor not in room.");
    public static final HttpCode TEAM_BATTLE_ACTIVATED = new HttpCode(2119, "Team Battle has been activated, quickly join the exciting Team Battle!", "تم تفعيل معركة الفريق، انضم بسرعة إلى معركة الفريق المثيرة!");
    public static final HttpCode CANNOT_CLOSE_TEAM_BATTLE = new HttpCode(2120, "Sorry, you do not have permission to close the team battle.", "عذرًا، ليس لديك إذن لإغلاق معركة الفريق.");

    public static final HttpCode LIVE_ROOM_LIMIT_VIP = new HttpCode(41, "live_room_limit_vip");
    public static final HttpCode VIP_USER_CANNOT_BE_KICK = new HttpCode(141, "vip_user_cannot_be_kick");
    public static final HttpCode VIP_CAN_SEND_PICTURE = new HttpCode(41, "vip_can_send_picture");
    public static final HttpCode THE_USER_CANNOT_BECOME_ANCHOR = new HttpCode(1201, "the_user_cannot_become_anchor");
    public static final HttpCode PLEASE_DO_NOT_REAPPLY = new HttpCode(1202, "please_do_not_reapply");
    public static final HttpCode CONTENT_IS_FROZEN = new HttpCode(1203, "content_is_frozen");
    public static final HttpCode YOU_ALREADY_REQUESTED = new HttpCode(1204, "you_already_requested");
    public static final HttpCode BALANCE_IS_INSUFFICIENT_TO_REJECT = new HttpCode(1006, "balance_is_insufficient_to_reject");
    public static final HttpCode REMOVE_FAMILY_MEMBER = new HttpCode(1007, "remove_family_member");
    public static final HttpCode CAN_NOT_USE_ROOM_LOCK = new HttpCode(41, "can_not_use_room_lock");
    public static final HttpCode CAN_NOT_UPLOAD_ROOM_BACKGROUND = new HttpCode(41, "can_not_upload_room_background");
    public static final HttpCode CAN_USE_ROOM_LOCK_VIP_LIMIT = new HttpCode(41, "can_use_room_lock_vip_limit");
    public static final HttpCode INCORRECT_HOST_CODE = new HttpCode(1007, "incorrect_host_code");
    public static final HttpCode HAS_JOINED_THE_AGENT = new HttpCode(1008, "has_joined_the_agent");
    public static final HttpCode HAS_JOINED_YOUR_AGENT = new HttpCode(1009, "has_joined_your_agent");
    public static final HttpCode HAVE_SENT_AN_INVITATION = new HttpCode(1010, "have_sent_an_invitation");
    public static final HttpCode THE_INVITATION_HAS_EXPIRED = new HttpCode(1011, "the_invitation_has_expired");
    public static final HttpCode HAVE_JOINED_ANOTHER_AGENT = new HttpCode(1012, "have_joined_another_agent");
    public static final HttpCode ROOM_BANNED = new HttpCode(1013, "the room banned");
    public static final HttpCode COVER_UNAVAILABLE = new HttpCode(1014, "cover_unavailable");
    public static final HttpCode BANNED_SEND_MSG = new HttpCode(1001, "banned_send_msg");
    public static final HttpCode NO_PERMISSION_TO_START_LIVE = new HttpCode(1015, "no_permission_to_start_live");

    public static final HttpCode DO_NOT_DISTURB = new HttpCode(2130, "The other party is busy, please try again later.", "الطرف الآخر مشغول، يرجى المحاولة لاحقًا.");
    public static final HttpCode INVITE_IN_PROGRESS = new HttpCode(2131, "PK invitation in progress...", "دعوة PK قيد الإرسال...");
    public static final HttpCode INVITE_PK_CANCELED = new HttpCode(2132, "Invitation canceled.", "تم إلغاء الدعوة");
    public static final HttpCode LIVE_PK_START_LIMIT = new HttpCode(2133, "At least three users are required to join to start team pk.", "يجب انضمام ثلاثة مستخدمين على الأقل لبدء تحدي الفريق.");
    public static final HttpCode LIVE_PK_INVITE_EXPIRE = new HttpCode(2133, "This invitation has expired.", "لقد انتهت صلاحية هذه الدعوة.");
    public static final HttpCode LIVE_PK_INVITE_UPGRADE = new HttpCode(2134, "Unable to invite this user to PK. Please invite others.", "لا يمكن دعوة هذا المستخدم إلى PK. يرجى دعوة الآخرين.");
    public static final HttpCode MYSTERY_FOLLOW_ROOM_LIMIT = new HttpCode(2135, "The mystery identity does not support following.", "الهوية الغامضة لا تدعم المتابعة.");
    public static final HttpCode MYSTERY_MEMBER_ROOM_LIMIT = new HttpCode(2135, "The mystery identity does not support joining the room.", "الهوية الغامضة لا تدعم الانضمام إلى الغرفة.");
    public static final HttpCode MYSTERY_ROOM_LIMIT = new HttpCode(2136, "You have activated the mystery man identity and cannot create a voice room. Do you need to turn off the mystery man identity?", "لقد قمتَ بتفعيل هوية الرجل الغامض، ولا يمكنك إنشاء غرفة صوتية. هل تحتاج إلى إيقاف هوية الرجل الغامض؟");
    public static final HttpCode MYSTERY_LIVE_ROOM_LIMIT = new HttpCode(2136, "You have activated the mystery man identity, and live streaming is not supported. Do you need to turn off the mystery man identity?", "لقد قمت بتفعيل هوية الرجل الغامض، ولا يدعم البث المباشر. هل تحتاج إلى إيقاف هوية الرجل الغامض؟");
    public static final HttpCode MYSTERY_GATHER_LIMIT = new HttpCode(2137, "Mystery person identity cannot send broadcasts.", "هوية الشخص الغامض لا يمكنها إرسال البث.");
    public static final HttpCode MYSTERY_VIDEO_LIMIT = new HttpCode(2138, "Mystery man identity cannot turn on the camera.", "هوية الرجل الغامض لا يمكنها تشغيل الكاميرا.");
    public static final HttpCode MUTE_SVIP_LIMIT = new HttpCode(2139, "Honored SVIP privilege users cannot be muted.", "لا يمكن إسكات مستخدمي SVIP المميزين.");
    public static final HttpCode KICK_SVIP_LIMIT = new HttpCode(2140, "Honored SVIP privilege users cannot be kicked from the room.", "لا يمكن طرد مستخدمي SVIP المميزين من الغرفة.");
    public static final HttpCode KICK_MYSTERY_LIMIT = new HttpCode(2141, "Distinguished SVIP mystery person, cannot be kicked from the room.", "الشخص الغامض SVIP المميز، لا يمكن طرده من الغرفة.");
    public static final HttpCode SVIP_CANNOT_REMOVE_FAMILY = new HttpCode(2142, "SVIP users cannot be removed from the family.", "لا يمكن إزالة مستخدمي SVIP من العائلة.");
    public static final HttpCode HAS_SUSPENDED_THIS_FEATURE = new HttpCode(2150, "has_suspended_this_feature");
    public static final HttpCode FEATURE_HAS_FROZEN = new HttpCode(2151, "feature_has_frozen");
    public static final HttpCode ONLY_OPEN_TO_NEW_USER = new HttpCode(2160, "only_open_to_new_user");
    public static final HttpCode ROOM_BLOCK_LIST_ADD_LIMIT = new HttpCode(2161, "You do not have permission to add to the blacklist.", "ليس لديك صلاحية إضافة إلى القائمة السوداء.");
    public static final HttpCode ROOM_BLOCK_LIST_REMOVE_LIMIT = new HttpCode(2161, "You do not have permission to remove.", "ليس لديك صلاحية الإزالة.");

    public static final HttpCode ACCOUNT_HAS_DISABLED = new HttpCode(62, "Your account has been disabled.", "تم تعطيل حسابك.");
    public static final HttpCode NOT_HAVE_PERMISSION_MODIFY_EVENT = new HttpCode(3001, "没有权限修改", "没有权限修改");


    public RoomHttpCode(int code, String... langMsg) {
        super(code, langMsg);
    }
}
