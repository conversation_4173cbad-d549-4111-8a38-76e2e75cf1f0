package com.quhong.handler;

import com.quhong.constant.DataResourcesConstant;
import com.quhong.core.utils.DateHelper;
import com.quhong.enums.ApiResult;
import com.quhong.mysql.dao.BackgroundUploadCardDao;
import com.quhong.mysql.data.BackgroundUploadCardData;
import com.quhong.mysql.data.ResourcesDetail;
import jakarta.annotation.Resource;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2024/2/5
 */
@Service
public class BackgroundUploadCardHandler extends BaseResourcesHandler {

    private final Logger logger = LoggerFactory.getLogger(getClass());

    @Resource
    private BackgroundUploadCardDao backgroundUploadCardDao;

    @Override
    public ApiResult<String> add(ResourcesDetail resourcesDetail) {
        String uid = resourcesDetail.getUid();
        int days = resourcesDetail.getDays();
        Integer seconds = resourcesDetail.getSeconds();
        long endTime = resourcesDetail.getEndTime();
        BackgroundUploadCardData data = backgroundUploadCardDao.selectOneByUid(resourcesDetail.getUid());
        if (null != data) {
            endTime = this.getResExtendTime(data.getEndTime(), days, seconds);
            resourcesDetail.setEndTime((int) endTime);
            data.setEndTime(endTime);
            backgroundUploadCardDao.update(data);
        } else {
            data = new BackgroundUploadCardData();
            data.setUid(uid);
            data.setStartTime((long) DateHelper.getNowSeconds());
            data.setEndTime(endTime);
            backgroundUploadCardDao.insert(data);
        }
        return ApiResult.getOk();
    }

    @Override
    public ApiResult<String> wear(ResourcesDetail resourcesDetail) {
        logger.info("room lock can not wear resourcesDetail:{}", resourcesDetail);
        return null;
    }

    @Override
    public ApiResult<String> addAndWear(ResourcesDetail resourcesDetail) {
        return null;
    }

    @Override
    public ApiResult<String> remove(ResourcesDetail resourcesDetail) {
        backgroundUploadCardDao.deleteByUid(resourcesDetail.getUid());
        return ApiResult.getOk();
    }

    @Override
    public ApiResult<String> expire() {
        int now = DateHelper.getNowSeconds();
        List<BackgroundUploadCardData> allExpireData = backgroundUploadCardDao.selectExpiredList(now);
        int size = CollectionUtils.isEmpty(allExpireData) ? 0 : allExpireData.size();
        logger.info("BackgroundUploadCardData allExpireData size={} ", size);
        ResourcesDetail resourcesDetail = new ResourcesDetail();
        resourcesDetail.setResType(DataResourcesConstant.TYPE_BACKGROUND_UPLOAD_CARD);
        for (BackgroundUploadCardData item : allExpireData) {
            resourcesDetail.setActionType(DataResourcesConstant.ACTION_ALL_EXPIRE);
            resourcesDetail.setUid(item.getUid());
            resourcesDetail.setResId("1");
            resourcesDetail.setmTime(now);
            resourcesDetail.setHandleTime(now);
            long nowEtime = item.getEndTime();
            resourcesDetail.setEndTime((int) nowEtime);
            resourcesDetail.setDesc(DataResourcesConstant.EXIPRE_DESC);
            backgroundUploadCardDao.deleteById(item.getId());
            this.writeToDb(resourcesDetail);
            logger.info("background upload card expire success resourcesDetail={}", resourcesDetail);
        }
        return ApiResult.getOk();
    }

    @Override
    public ApiResult<String> expireNotified() {
        return null;
    }
}
