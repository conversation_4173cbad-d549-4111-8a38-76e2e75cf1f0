package com.quhong.handler;


import com.alibaba.fastjson.JSONObject;
import com.quhong.constant.DataResourcesConstant;
import com.quhong.constant.DataResourcesHttpCode;
import com.quhong.constant.ResourceOfficialMsgConstant;
import com.quhong.core.utils.DateHelper;
import com.quhong.data.ActorData;
import com.quhong.data.dto.ResourcesDTO;
import com.quhong.enums.ApiResult;
import com.quhong.enums.HttpCode;
import com.quhong.enums.SLangType;
import com.quhong.mongo.dao.BadgeDao;
import com.quhong.mongo.dao.BadgeListDao;
import com.quhong.mongo.data.BadgeData;
import com.quhong.mongo.data.BadgeListData;
import com.quhong.mysql.data.ResourcesDetail;
import jakarta.annotation.Resource;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import java.util.List;
import java.util.Map;
import java.util.function.Function;
import java.util.stream.Collectors;

@Component
public class BadgeResourcesHandler extends BaseResourcesHandler {
    private static final Logger logger = LoggerFactory.getLogger(BadgeResourcesHandler.class);

    @Resource
    private BadgeListDao badgeListDao;

    @Resource
    private BadgeDao badgeDao;

    @Override
    public ApiResult<String> add(ResourcesDetail resourcesDetail) {
        int resId = Integer.parseInt(resourcesDetail.getResId());
        BadgeListData data = badgeListDao.findData(resId);
        if (null != data) {
            resourcesDetail.setName(data.getName());
            return update(resourcesDetail, data, false);
        } else {
            logger.info("not find resId={}", resId);
            return ApiResult.getError(DataResourcesHttpCode.NOT_FIND_RESOURCES);
        }
    }

    @Override
    public ApiResult<String> wear(ResourcesDetail resourcesDetail) {
        int resId = Integer.parseInt(resourcesDetail.getResId());
        BadgeListData data = badgeListDao.findData(resId);
        if (null != data) {
            return myWear(resourcesDetail);
        } else {
            logger.info("not find resId={}", resId);
            return ApiResult.getError(DataResourcesHttpCode.NOT_FIND_RESOURCES);
        }
    }

    @Override
    public ApiResult<String> remove(ResourcesDetail resourcesDetail) {
        int resId = Integer.parseInt(resourcesDetail.getResId());
        BadgeData badgeData = badgeDao.getBadgeData(resourcesDetail.getUid(), resId);
        if (null != badgeData) {
            String uid = resourcesDetail.getUid();
            int days = resourcesDetail.getDays();
            int newEndTime = getNewEndTime(days, (int) badgeData.getEnd_time());
            int now = DateHelper.getNowSeconds();
            if (days > 0 && newEndTime > now) {
                badgeDao.updateBadgeByTime(uid, resId, newEndTime, badgeData);
            } else {
                badgeDao.deleteBadge(badgeData);
            }
            return ApiResult.getOk();
        } else {
            return ApiResult.getError(DataResourcesHttpCode.NOT_OWN_RESOURCES);
        }

    }

    @Override
    public ApiResult<String> addAndWear(ResourcesDetail resourcesDetail) {
        int resId = Integer.parseInt(resourcesDetail.getResId());
        BadgeListData data = badgeListDao.findData(resId);
        if (null != data) {
            resourcesDetail.setName(data.getName());
            return update(resourcesDetail, data, true);
        } else {
            logger.info("not find resId={}", resId);
            return ApiResult.getError(DataResourcesHttpCode.NOT_FIND_RESOURCES);
        }
    }

    @Override
    public ApiResult<String> expire() {
        int now = DateHelper.getNowSeconds();
        List<BadgeData> allBadgeData = badgeDao.listByEndTime(DataResourcesConstant.BASE_EXIPRE_START_TIME, now);
        int size = CollectionUtils.isEmpty(allBadgeData) ? 0 : allBadgeData.size();
        logger.info("badge allBadgeData size={}", size);
        ResourcesDetail resourcesDetail = new ResourcesDetail();
        resourcesDetail.setResType(DataResourcesConstant.TYPE_BADGE);
        resourcesDetail.setActionType(DataResourcesConstant.ACTION_ALL_EXPIRE);
        for (BadgeData item : allBadgeData) {
            resourcesDetail.setUid(item.getUid());
            resourcesDetail.setResId(String.valueOf(item.getBadge_id()));
            resourcesDetail.setmTime(now);
            resourcesDetail.setHandleTime(now);
            resourcesDetail.setEndTime((int) item.getEnd_time());
            resourcesDetail.setDesc(DataResourcesConstant.EXIPRE_DESC);
            badgeDao.deleteBadge(item);
            this.writeToDb(resourcesDetail);
            logger.info("BadgeData exipre success resourcesDetail={}", resourcesDetail);
        }
        return ApiResult.getOk();
    }

    @Override
    public ApiResult<String> expireNotified() {
        return null;
    }

    private ApiResult<String> update(ResourcesDetail resourcesDetail, BadgeListData badgeSourceData, boolean putOn) {
        int resId = Integer.parseInt(resourcesDetail.getResId());
        String uid = resourcesDetail.getUid();
        int days = resourcesDetail.getDays();
        Integer seconds = resourcesDetail.getSeconds();
        BadgeData badgeData = badgeDao.getBadgeData(uid, resId);
        long endTime = resourcesDetail.getEndTime();
        if (null != badgeData) {
            endTime = this.getResExtendTime(badgeData.getEnd_time(), days, seconds);
            resourcesDetail.setEndTime((int) endTime);
        }
        badgeDao.updateBadgeByTime(uid, resId, endTime, badgeData);
        handleSameSeriesBadge(uid, badgeSourceData);
        if (putOn) {
            myWear(resourcesDetail);
        }
        return ApiResult.getOk();
    }

    private void handleSameSeriesBadge(String uid, BadgeListData badgeSourceData) {
        List<BadgeData> wearBadgeList = badgeDao.findBadgeList(uid);
        if (!CollectionUtils.isEmpty(wearBadgeList)) {
            // 同一系列的勋章需要下掉
            for (BadgeData badgeData : wearBadgeList) {
                BadgeListData data = badgeListDao.findData(badgeData.getBadge_id());
                logger.info("data={} P_badge_id={}", JSONObject.toJSONString(data), badgeSourceData.getP_badge_id());
                if (data.getP_badge_id() != 0 && data.getP_badge_id() == badgeSourceData.getP_badge_id()) {
                    badgeData.setStatus(0);
                    badgeDao.update(badgeData);
                }
            }
        }
    }

    private ApiResult<String> myWear(ResourcesDetail resourcesDetail) {
        int resId = Integer.parseInt(resourcesDetail.getResId());
        String uid = resourcesDetail.getUid();
        List<BadgeData> wearBadgeList = badgeDao.findBadgeList(uid);
        if (wearBadgeList.size() >= 3) {
            logger.info("BadgeData WEAR_POSITION_FULL resourcesDetail={}", resourcesDetail);
            return ApiResult.getError(HttpCode.WEAR_POSITION_FULL);
        }

        Map<Integer, BadgeData> wearBadgeMap = wearBadgeList.stream().collect
                (Collectors.toMap(BadgeData::getBadge_id, Function.identity()));

        // 已经佩戴的勋章不能再次佩戴
        if (wearBadgeMap.containsKey(resId)) {
            return ApiResult.getOk();
        }
        int pos = badgeDao.getAvailableWearPos(uid, wearBadgeList);
        badgeDao.updateByBid(uid, resId, pos);
        return ApiResult.getOk();
    }

    @Override
    public void sendOfficialMsg(ResourcesDetail resourcesDTO) {
        if (resourcesDTO.getOfficialMsg() > 0) {
            String uid = resourcesDTO.getUid();
            ActorData actorData = actorDao.getActorDataFromCache(uid);
            int badgeId = Integer.parseInt(resourcesDTO.getResId());
            BadgeListData badgeInfo = badgeListDao.findData(badgeId);
            if (badgeInfo != null) {
                int slang = actorData.getSlang();
                String actText = slang == SLangType.ARABIC ? ResourceOfficialMsgConstant.ACTION_AR : ResourceOfficialMsgConstant.ACTION_EN;
                String title = slang == SLangType.ARABIC ? ResourceOfficialMsgConstant.BADGE_AR : ResourceOfficialMsgConstant.BADGE_EN;
                String body = slang == SLangType.ARABIC ? ResourceOfficialMsgConstant.BADGE_DESC_AR : ResourceOfficialMsgConstant.BADGE_DESC_EN;
                commonOfficialMsg(uid, badgeInfo.getIcon(), ResourceOfficialMsgConstant.ACTION_TYPE_BADGE, actText, title, body);
            }
        }
    }
}
