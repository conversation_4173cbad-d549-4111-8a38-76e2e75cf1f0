package com.quhong.handler;

import com.google.common.collect.Interner;
import com.google.common.collect.Interners;
import com.quhong.analysis.EventDTO;
import com.quhong.analysis.EventReport;
import com.quhong.analysis.RoomRocketLogEvent;
import com.quhong.constant.AchievementConstant;
import com.quhong.core.concurrency.BaseTaskFactory;
import com.quhong.core.concurrency.tasks.Task;
import com.quhong.core.utils.DateHelper;
import com.quhong.data.SendGiftData;
import com.quhong.image.ImageUrlGenerator;
import com.quhong.mongo.dao.MongoRoomDao;
import com.quhong.mongo.dao.RocketRewardConfigDao;
import com.quhong.mongo.dao.SysConfigDao;
import com.quhong.mongo.data.MongoRoomData;
import com.quhong.mongo.data.RocketRewardConfigData;
import com.quhong.mq.CommonData;
import com.quhong.mq.MqSenderService;
import com.quhong.msg.obj.UserInfoObject;
import com.quhong.msg.room.RocketLaunchPlatformMsg;
import com.quhong.msg.room.RocketProgressChangeMsg;
import com.quhong.msg.room.RoomRocketLaunchMsg;
import com.quhong.mysql.dao.GiftDao;
import com.quhong.mysql.data.GiftData;
import com.quhong.redis.RoomRocketRedis;
import com.quhong.room.RoomWebSender;
import com.quhong.room.cache.RoomActorCache;
import com.quhong.room.data.RoomActorDetailData;
import com.quhong.utils.RoomUtils;
import jakarta.annotation.Resource;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import java.math.BigDecimal;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2022/12/17
 */
@Component
public class RoomRocketSendGiftHandler implements ActivityHandler {

    private static final Logger logger = LoggerFactory.getLogger(RoomRocketSendGiftHandler.class);

    private static final String ROCKET_LOCK_KEY = "rocket_";

    private static final Interner<String> stringPool = Interners.newWeakInterner();

    @Resource
    private RoomRocketRedis roomRocketRedis;
    @Resource
    private RoomWebSender roomWebSender;
    @Resource
    private RoomActorCache roomActorCache;
    @Resource
    private MongoRoomDao roomDao;
    @Resource
    private RocketRewardConfigDao rocketRewardConfigDao;
    @Autowired(required = false)
    private EventReport eventReport;
    @Resource
    private SysConfigDao sysConfigDao;
    @Resource
    private MqSenderService mqSenderService;
    @Resource
    private GiftDao giftDao;

    public RoomRocketSendGiftHandler() {
    }

    @Override
    public void process(SendGiftData data) {
        if (data == null || !RoomUtils.isVoiceRoom(data.getRoomId()) || StringUtils.isEmpty(data.getFrom_uid())) {
            return;
        }
        // if (!whiteTestDao.isMemberByType(data.getRoomId(), WhiteTestDao.WHITE_TYPE_ROOM_ID)) {
        //     return;
        // }
        int rocketSwitch = sysConfigDao.getIntValue(SysConfigDao.ROCKET_CONFIG, SysConfigDao.ROOM_ROCKET_SWITCH_KEY);
        if (rocketSwitch == 0) {
            return;
        }
        GiftData giftData = giftDao.getGiftFromCache(data.getGid());
        if (giftData.getGtype() == 2) {
            // 银币礼物不加火箭能量
            return;
        }
        int totalCostBeans = data.getNumber() * data.getPrice() * data.getAid_list().size();
        synchronized (stringPool.intern(ROCKET_LOCK_KEY + data.getRoomId())) {
            updateRoomRocketRedis(data.getRoomId(), data.getFrom_uid(), totalCostBeans);
        }
    }

    private void updateRoomRocketRedis(String roomId, String uid, int totalCostBeans) {
        int rocketLevel = roomRocketRedis.getRoomRocketLevel(roomId);
        int showRocketLevel = roomRocketRedis.getShowRocketLevel(rocketLevel);
        RocketRewardConfigData data = rocketRewardConfigDao.findDataFromCache(showRocketLevel);
        if (data == null) {
            logger.error("RocketRewardConfigData is null. rocketLevel={}", rocketLevel);
            return;
        }

        // 房间火箭充能
        int originalEnergy = roomRocketRedis.getRocketEnergy(roomId, rocketLevel);
        roomRocketRedis.addRocketEnergy(roomId, rocketLevel, totalCostBeans);
        // 更新火箭排行榜分数
        int rankingScore = roomRocketRedis.getRocketRankingScore(roomId, rocketLevel, uid);
        roomRocketRedis.setRocketRank(roomId, rocketLevel, uid, rankingScore + totalCostBeans, DateHelper.getNowSeconds());
        // 判断火箭是否升级
        int currentEnergy = roomRocketRedis.getRocketEnergy(roomId, rocketLevel);
        if (currentEnergy >= data.getRocketLaunchLimit()) {
            // 更新房间火箭等级
            int newRocketLevel = rocketLevel + 1;
            roomRocketRedis.setRoomRocketLevel(roomId, newRocketLevel);
            // 发送火箭能量集满的房间消息
            List<String> rocketRankingList = roomRocketRedis.getRocketRankingList(roomId, rocketLevel);
            String top1Uid = !CollectionUtils.isEmpty(rocketRankingList) ? rocketRankingList.get(0) : null;
            sendRoomRocketLaunchMsg(roomId, top1Uid, rocketLevel, showRocketLevel);
            // 发送全平台广播
            sendRocketLaunchPlatformMsg(roomId, showRocketLevel);
            // 设置火箭宝箱的过期时间
            setRewardBoxEndTime(roomId, rocketLevel);
            // 发送火箭能量进度改变消息
            sendRocketProgressChangeMsg(roomId, roomRocketRedis.getShowRocketLevel(newRocketLevel), 0);
            // 火箭触发数数埋点
            rocketLaunchEventReport(roomId, rocketLevel, showRocketLevel, data.getRocketLaunchLimit());
            // 用户成就：火箭专家
            mqSenderService.sendUserAchievementToMq(new CommonData(top1Uid, AchievementConstant.ROCKET_EXPERT, 1));
        } else {
            BigDecimal rate = calculateProgressRate(currentEnergy, data.getRocketLaunchLimit());
            if (!rate.equals(calculateProgressRate(originalEnergy, data.getRocketLaunchLimit()))) {
                // 发送火箭能量进度改变消息
                sendRocketProgressChangeMsg(roomId, showRocketLevel, rate.multiply(new BigDecimal(100)).intValue());
            }
        }
    }

    private BigDecimal calculateProgressRate(int energy, int launchLimit) {
        return new BigDecimal(energy).divide(new BigDecimal(launchLimit), 2, BigDecimal.ROUND_DOWN);
    }

    private void rocketLaunchEventReport(String roomId, int rocketLevel, int showRocketLevel, int rocketLaunchLimit) {
        RoomRocketLogEvent event = new RoomRocketLogEvent();
        event.setUid(RoomUtils.getRoomHostId(roomId));
        event.setRoom_id(roomId);
        event.setRoom_rocket_round((rocketLevel - 1) / 5 + 1);
        event.setRoom_rocket_level(showRocketLevel);
        event.setRoom_rocket_cost(rocketLaunchLimit);
        event.setDate(DateHelper.ARABIAN.formatDateInDay());
        event.setCtime(DateHelper.getNowSeconds());
        eventReport.track(new EventDTO(event));
    }

    private void setRewardBoxEndTime(String roomId, int rocketLevel) {
        int nowTime = DateHelper.getNowSeconds();
        Map<Integer, Integer> allRewardEndTime = roomRocketRedis.getAllRewardEndTime(roomId);
        int maxLeftTime = 0;
        if (!CollectionUtils.isEmpty(allRewardEndTime)) {
            for (Map.Entry<Integer, Integer> entry : allRewardEndTime.entrySet()) {
                if (entry.getValue() > nowTime) {
                    int leftTime = entry.getValue() - nowTime;
                    maxLeftTime = Math.max(leftTime, maxLeftTime);
                }
            }
        }
        int endTime = maxLeftTime != 0 ? nowTime + maxLeftTime + 60 : nowTime + 65;
        roomRocketRedis.setRocketRewardEndTime(roomId, rocketLevel, endTime);
    }

    private void sendRocketProgressChangeMsg(String roomId, int rocketLevel, int energyRate) {
        BaseTaskFactory.getFactory().addSlow(new Task() {
            @Override
            protected void execute() {
                RocketProgressChangeMsg msg = new RocketProgressChangeMsg();
                msg.setRocketLevel(rocketLevel);
                msg.setEnergyRate(energyRate);
                roomWebSender.sendRoomWebMsg(roomId, "", msg, false, 106);
            }
        });
    }

    private void sendRocketLaunchPlatformMsg(String roomId, int rocketLevel) {
        BaseTaskFactory.getFactory().addSlow(new Task() {
            @Override
            protected void execute() {
                MongoRoomData roomData = roomDao.getDataFromCache(roomId);
                if (roomData == null) {
                    logger.error("can not find room data. roomId={}", roomId);
                    return;
                }
                RocketLaunchPlatformMsg msg = new RocketLaunchPlatformMsg();
                msg.setRocketLevel(rocketLevel);
                msg.setRid(roomData.getOwnerRid());
                msg.setFromRoomId(roomId);
                msg.setRoomName(roomData.getName());
                msg.setRoomHead(ImageUrlGenerator.generateRoomUserUrl(roomData.getHead()));
                roomWebSender.sendAllRoomMsgPrivately(roomId, msg, true);
            }
        });
    }

    private void sendRoomRocketLaunchMsg(String roomId, String uid, int rocketLevel, int showRocketLevel) {
        BaseTaskFactory.getFactory().addSlow(new Task() {
            @Override
            protected void execute() {
                RoomRocketLaunchMsg msg = new RoomRocketLaunchMsg();
                msg.setRocketLevel(showRocketLevel);
                UserInfoObject userInfoObject = new UserInfoObject();
                RoomActorDetailData actorData = roomActorCache.getData(roomId, uid, false);
                userInfoObject.setUid(uid);
                userInfoObject.setName(actorData.getName());
                userInfoObject.setHead(actorData.getHead());
                msg.setTop1User(userInfoObject);
                msg.setBoxId(rocketLevel);
                roomWebSender.sendRoomWebMsg(roomId, "", msg, true, 106);
            }
        });
    }
}
