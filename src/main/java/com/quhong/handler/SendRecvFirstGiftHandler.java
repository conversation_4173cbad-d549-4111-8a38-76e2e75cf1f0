package com.quhong.handler;

import com.quhong.config.GiftYamlConfig;
import com.quhong.constant.GiftConstant;
import com.quhong.core.concurrency.BaseTaskFactory;
import com.quhong.core.concurrency.tasks.Task;
import com.quhong.core.utils.DateHelper;
import com.quhong.data.ActorData;
import com.quhong.data.GiftContext;
import com.quhong.data.dto.SendGiftDTO;
import com.quhong.image.ImageUrlGenerator;
import com.quhong.mongo.dao.ActorDao;
import com.quhong.mongo.dao.GiftNumDao;
import com.quhong.mongo.data.GiftNum;
import com.quhong.msg.push.GiftNamingMsg;
import com.quhong.mysql.dao.SendRecvFirstDao;
import com.quhong.mysql.dao.VipInfoDao;
import com.quhong.mysql.data.GiftData;
import com.quhong.mysql.data.SendRecvFirstData;
import com.quhong.room.RoomWebSender;
import com.quhong.room.redis.MicFrameRedis;
import com.quhong.service.BadgeService;
import jakarta.annotation.Resource;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * @date 2023/4/21
 */
@Component
public class SendRecvFirstGiftHandler implements IGiftHandler {

    private static final Logger logger = LoggerFactory.getLogger(SendRecvFirstGiftHandler.class);

    @Resource
    private SendRecvFirstDao sendRecvFirstDao;
    @Resource
    private GiftNumDao giftNumDao;
    @Resource
    private RoomWebSender roomWebSender;
    @Resource
    private ActorDao actorDao;
    @Resource
    private VipInfoDao vipInfoDao;
    @Resource
    private MicFrameRedis micFrameRedis;
    @Resource
    private GiftYamlConfig giftYamlConfig;
    @Resource
    private BadgeService badgeService;

    @Override
    public void process(SendGiftDTO req, GiftData giftData, GiftContext context) {
        int now = DateHelper.getNowSeconds();
        if (req.getSendType() == GiftConstant.ST_SEND_ONE) {
            int giftId = context.isRandomGift() ? context.getGiftInfo().getGiftRandomId() : req.getGiftId();
            // 礼物发送给单个人
            String aid = req.getAid();
            if (!context.isUseVirtualBeans()) {
                giftNumDao.incrGiftNum(aid, giftId, req.getNumber());
            }
            if (!context.isUseVirtualBeans()) {
                SendRecvFirstData data = new SendRecvFirstData(req.getUid(), aid, giftId, (long) req.getNumber(), now);
                updateOne(data);
            }
            BaseTaskFactory.getFactory().addSlow(new Task() {
                @Override
                protected void execute() {
                    GiftNum giftNum = giftNumDao.getGiftNum(aid, giftId);
                    int totalNum = giftNum == null ? 0 : giftNum.getGiftNum();
                    if (totalNum > 0) {
                        // 私信礼物推送冠名消息
                        if (!context.isRandomGift() && !context.isUseVirtualBeans()) {
                            notice(aid, giftId, req.getNumber(), context, req.getUid(), totalNum);
                        }

                        // 处理等级勋章
                        if (!giftYamlConfig.getGiftBadgeMap().containsKey(giftId)) {
                            return;
                        }
                        logger.info("gift badge giftNum record uid={} aid={} giftId={} sendNum={} total={}",
                                req.getUid(), giftNum.getAid(), giftId, req.getNumber(), giftNum.getGiftNum());
                        badgeService.doGiftBadge(giftNum.getAid(), giftId, giftNum.getGiftNum(),
                                giftNum.getGiftNum() - req.getNumber(), giftYamlConfig.getGiftBadgeMap(), giftYamlConfig.getGiftBadgeIdMap());
                    }
                }
            });
        } else {
            BaseTaskFactory.getFactory().addSlow(new Task() {
                @Override
                protected void execute() {
                    // 礼物发送给多个人
                    long timeMillis = System.currentTimeMillis();
                    for (String item : context.getAidSet()) {
                        if (!context.isUseVirtualBeans()) {
                            int giftId = context.isRandomGift() ? context.getRandomGiftMap().get(item).getGiftId() : req.getGiftId();
                            SendRecvFirstData data = new SendRecvFirstData(req.getUid(), item, giftId, (long) req.getNumber(), now);
                            updateOne(data);
                        }
                    }
                    logger.info("execute SendRecvFirstGiftHandler end. cost={}", System.currentTimeMillis() - timeMillis);
                }
            });
        }
    }

    private void updateOne(SendRecvFirstData data) {
        int row = sendRecvFirstDao.incGiftNum(data);
        if (row <= 0) {
            sendRecvFirstDao.insert(data);
        }
    }

    private void notice(String aid, int giftId, int num, GiftContext context, String uid, int totalNum) {
        try {
            long timeMillis = System.currentTimeMillis();
            SendRecvFirstData firstSendRecvFirstData = sendRecvFirstDao.getFirstDataByReceiveGid(aid, giftId);
            if (firstSendRecvFirstData != null) {
                SendRecvFirstData mySendRecvFirstData = sendRecvFirstDao.getDataByUid(aid, uid, giftId);
                String firstSendUid = firstSendRecvFirstData.getSendUid();
                long firstNum = firstSendRecvFirstData.getGiftNum();
                long remainNum = 0;
                if (mySendRecvFirstData != null) {
                    if (!firstSendUid.equals(uid)) {
                        remainNum = firstNum - mySendRecvFirstData.getGiftNum() + 1;
                    }
                } else {
                    remainNum = firstNum + 1;
                }

                ActorData sendActorData = actorDao.getActorDataFromCache(firstSendUid);
                GiftNamingMsg msg = new GiftNamingMsg();
                msg.setGift_num(String.valueOf(totalNum));
                msg.setSort_num((long) totalNum * context.getGiftInfo().getGiftPrice());
                msg.setGiftId(giftId);
                msg.setSendUserName(sendActorData.getName());
                msg.setSendUserHead(ImageUrlGenerator.generateRoomUserUrl(sendActorData.getHead()));
                msg.setSendNum(firstNum);
                msg.setRemainNum(remainNum);
                msg.setState(1);
                msg.setSendUserMicUrl(micFrameRedis.getMicSourceFromCache(firstSendUid));
                msg.setAid(aid);
                msg.setSendUserUid(firstSendUid);
                roomWebSender.sendPlayerWebMsg("", "", uid, msg, false);
                logger.info("notice success uid={} aid={} giftId={} num={} cost={} GiftNamingMsg={}", uid, aid, giftId, num, System.currentTimeMillis() - timeMillis, msg);
            }
        } catch (Exception e) {
            logger.error("notice error uid={} giftId={} num={}", uid, giftId, num, e);
        }
    }

}
