package com.quhong.handler;

import com.quhong.constant.DataResourcesConstant;
import com.quhong.constant.DataResourcesHttpCode;
import com.quhong.core.utils.DateHelper;
import com.quhong.enums.ApiResult;
import com.quhong.enums.BaseDataResourcesConstant;
import com.quhong.enums.HttpCode;
import com.quhong.exception.CommonException;
import com.quhong.mongo.dao.ResourceConfigDao;
import com.quhong.mongo.data.ResourceConfigData;
import com.quhong.mysql.dao.UserResourceDao;
import com.quhong.mysql.data.ResourcesDetail;
import com.quhong.mysql.data.UserResourceData;
import com.quhong.utils.CollectionUtil;
import jakarta.annotation.Resource;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import java.util.List;
import java.util.Set;

/**
 * <AUTHOR>
 * @date 2023/5/5
 */
@Component
public class HonorTitleResourcesHandler extends BaseResourcesHandler {

    private static final Logger logger = LoggerFactory.getLogger(HonorTitleResourcesHandler.class);

    @Resource
    private ResourceConfigDao resourceConfigDao;
    @Resource
    private UserResourceDao userResourceDao;

    /**
     * 用户活得资源
     */
    @Override
    public ApiResult<String> add(ResourcesDetail resourcesDetail) {
        int resId = Integer.parseInt(resourcesDetail.getResId());
        ResourceConfigData resourceConfigData = resourceConfigDao.getResourceDataFromDb(resId, resourcesDetail.getResType());
        if (resourceConfigData == null) {
            logger.info("can not find resource config data. resId={} resType={}", resId, resourcesDetail.getResType());
            return ApiResult.getError(DataResourcesHttpCode.NOT_FIND_RESOURCES);
        }
        int days = resourcesDetail.getDays();
        Integer seconds = resourcesDetail.getSeconds();
        UserResourceData data = userResourceDao.selectUserResource(resourcesDetail.getUid(), resId);
        if (data == null) {
            int endTime = DateHelper.getNowSeconds() + days * 86400;
            insertUserResourceData(resourcesDetail.getUid(), resId, resourcesDetail.getResType(), endTime, 0);
        } else {
            long newEndTime = this.getResExtendTime(data.getEndTime(), days, seconds);
            userResourceDao.updateEndTime(resourcesDetail.getUid(), resId, data.getStatus(), newEndTime);
        }
        return ApiResult.getOk();
    }

    /**
     * 用户设置资源
     */
    @Override
    public ApiResult<String> wear(ResourcesDetail resourcesDetail) {
        int resId = Integer.parseInt(resourcesDetail.getResId());
        ResourceConfigData data = resourceConfigDao.getResourceDataFromDb(resId, resourcesDetail.getResType());
        if (data == null) {
            logger.info("can not find resource config data. resId={} resType={}", resId, resourcesDetail.getResType());
            return ApiResult.getError(DataResourcesHttpCode.NOT_FIND_RESOURCES);
        }
        List<UserResourceData> wearingResList = userResourceDao.selectUserUsingResourceList(resourcesDetail.getUid(), BaseDataResourcesConstant.TYPE_HONOR_TITLE);
        Set<Integer> wearingResIdSet = CollectionUtil.listToPropertySet(wearingResList, UserResourceData::getId);
        if (wearingResIdSet.contains(resId)) {
            return ApiResult.getOk();
        }
        if (wearingResIdSet.size() >= 2) {
            return ApiResult.getError(DataResourcesHttpCode.ONLY_WEAR_UP_TO_TWO_HONOR_TITLES);
        }
        userResourceDao.updateStatus(resourcesDetail.getUid(), resId, 1);
        return ApiResult.getOk();
    }

    /**
     * 用户获取资源并设置
     */
    @Override
    public ApiResult<String> addAndWear(ResourcesDetail resourcesDetail) {
        int resId = Integer.parseInt(resourcesDetail.getResId());
        ResourceConfigData resourceConfigData = resourceConfigDao.getResourceDataFromDb(resId, resourcesDetail.getResType());
        if (resourceConfigData == null) {
            logger.info("can not find resource config data. resId={} resType={}", resId, resourcesDetail.getResType());
            return ApiResult.getError(DataResourcesHttpCode.NOT_FIND_RESOURCES);
        }
        int days = resourcesDetail.getDays();
        Integer seconds = resourcesDetail.getSeconds();
        UserResourceData data = userResourceDao.selectUserResource(resourcesDetail.getUid(), resId);
        List<UserResourceData> wearingResList = userResourceDao.selectUserUsingResourceList(resourcesDetail.getUid(), BaseDataResourcesConstant.TYPE_HONOR_TITLE);
        Set<Integer> wearingResIdSet = CollectionUtil.listToPropertySet(wearingResList, UserResourceData::getId);
        if (data == null) {
            int endTime = DateHelper.getNowSeconds() + days * 86400;
            int status = wearingResIdSet.size() < 2 ? 1 : 0;
            insertUserResourceData(resourcesDetail.getUid(), resId, resourcesDetail.getResType(), endTime, status);
        } else {
            long newEndTime = this.getResExtendTime(data.getEndTime(), days, seconds);
            int status = wearingResIdSet.size() < 2 ? 1 : data.getStatus();
            userResourceDao.updateEndTime(resourcesDetail.getUid(), resId, status, newEndTime);
        }
        return ApiResult.getOk();
    }

    /**
     * 用户取消设置资源
     */
    @Override
    public ApiResult<String> unWear(ResourcesDetail resourcesDetail) {
        int resId = Integer.parseInt(resourcesDetail.getResId());
        userResourceDao.updateStatus(resourcesDetail.getUid(), resId, 0);
        return ApiResult.getOk();
    }

    /**
     * 移除用户资源
     */
    @Override
    public ApiResult<String> remove(ResourcesDetail resourcesDetail) {
        int resId = Integer.parseInt(resourcesDetail.getResId());
        String uid = resourcesDetail.getUid();
        UserResourceData wearingResource = userResourceDao.selectUserResource(uid, resId);
        if (wearingResource == null) {
            logger.error("user resource data not exist. uid={} resId={}", uid, resId);
            return ApiResult.getError(DataResourcesHttpCode.NOT_OWN_RESOURCES);
        }
        userResourceDao.deleteUserResource(uid, resId);
        return ApiResult.getOk();
    }

    @Override
    public ApiResult<String> expire() {
        int now = DateHelper.getNowSeconds();
        List<UserResourceData> expiredResourceList = userResourceDao.getExpiredResourceList(DataResourcesConstant.TYPE_HONOR_TITLE, DataResourcesConstant.BASE_EXIPRE_START_TIME, now);
        int size = CollectionUtils.isEmpty(expiredResourceList) ? 0 : expiredResourceList.size();
        logger.info("expiredResourceList. size={}", size);
        int expireCount = 0;
        for (UserResourceData item : expiredResourceList) {
            ResourcesDetail resourcesDetail = new ResourcesDetail();
            resourcesDetail.setResType(DataResourcesConstant.TYPE_HONOR_TITLE);
            resourcesDetail.setActionType(DataResourcesConstant.ACTION_ALL_EXPIRE);
            resourcesDetail.setUid(item.getUid());
            resourcesDetail.setResId(String.valueOf(item.getResourceId()));
            resourcesDetail.setmTime(now);
            resourcesDetail.setHandleTime(now);
            resourcesDetail.setEndTime((int) item.getEndTime());
            resourcesDetail.setDesc(DataResourcesConstant.EXIPRE_DESC);
            userResourceDao.deleteUserResource(item.getUid(), item.getResourceId());
            this.writeToDb(resourcesDetail);
            expireCount++;
            logger.info("resourceData expire success. resourcesDetail={}", resourcesDetail.toString());
        }
        logger.info("expiredResourceList size={} expireCount={}", size, expireCount);
        return ApiResult.getOk();
    }

    @Override
    public ApiResult<String> expireNotified() {
        return null;
    }

    @Override
    public void sendOfficialMsg(ResourcesDetail resourcesDTO) {
    }

    private void insertUserResourceData(String uid, int resId, int resType, int endTime, int status) {
        UserResourceData data = new UserResourceData();
        data.setUid(uid);
        data.setResourceId(resId);
        data.setResourceType(resType);
        data.setStatus(status);
        data.setEndTime(endTime);
        data.setCtime(DateHelper.getNowSeconds());
        userResourceDao.insert(data);
    }
}
