package com.quhong.handler;


import com.quhong.constant.DataResourcesConstant;
import com.quhong.constant.DataResourcesHttpCode;
import com.quhong.constant.ResourceOfficialMsgConstant;
import com.quhong.core.concurrency.BaseTaskFactory;
import com.quhong.core.concurrency.tasks.Task;
import com.quhong.core.utils.DateHelper;
import com.quhong.data.ActorData;
import com.quhong.data.dto.ResourcesDTO;
import com.quhong.enums.ApiResult;
import com.quhong.enums.SLangType;
import com.quhong.mongo.dao.BubbleDao;
import com.quhong.mongo.dao.BuddleSourceDao;
import com.quhong.mongo.data.BubbleData;
import com.quhong.mongo.data.BuddleSourceData;
import com.quhong.msg.push.BubbleChangeMsg;
import com.quhong.mysql.data.ResourcesDetail;
import jakarta.annotation.Resource;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import java.util.List;

@Component
public class BuddleResourcesHandler extends BaseResourcesHandler {
    private static final Logger logger = LoggerFactory.getLogger(BuddleResourcesHandler.class);

    @Resource
    private BubbleDao bubbleDao;
    @Resource
    private BuddleSourceDao buddleSourceDao;

    @Override
    public ApiResult<String> add(ResourcesDetail resourcesDetail) {
        int resId = Integer.parseInt(resourcesDetail.getResId());
        if (resId == DataResourcesConstant.DEFAULT_BUDDLE_ID) {
            logger.info("not add resId={}", resId);
            return ApiResult.getError(DataResourcesHttpCode.OTHER_HANDLE_ERROR);
        }
        BuddleSourceData data = buddleSourceDao.findData(resId);
        if (null != data) {
            resourcesDetail.setName(data.getName());
            int days = resourcesDetail.getDays();
            Integer seconds = resourcesDetail.getSeconds();
            BubbleData bubbleData = bubbleDao.findData(resourcesDetail.getUid(), resId);
            long endTime = resourcesDetail.getEndTime();
            int emptyWearType = resourcesDetail.getEmptyWearType();
            boolean isEmptyWear = emptyWearType > 0;
            if (null != bubbleData) {
                endTime = this.getResExtendTime(bubbleData.getEnd_time(), days, seconds);
                resourcesDetail.setEndTime((int) endTime);
            }
            boolean putOn = bubbleDao.updateBubbleNew(resourcesDetail.getUid(), resId, endTime, bubbleData, false, isEmptyWear);
            if (putOn){
                pushBubbleChangeMsg(resourcesDetail.getUid(),resId);
            }
            return ApiResult.getOk();
        } else {
            logger.info("not find resId={}", resId);
            return ApiResult.getError(DataResourcesHttpCode.NOT_FIND_RESOURCES);
        }
    }

    @Override
    public ApiResult<String> wear(ResourcesDetail resourcesDetail) {
        int resId = Integer.parseInt(resourcesDetail.getResId());
        BubbleData oldWearData = bubbleDao.findDataByStatus(resourcesDetail.getUid(), 1);
        BubbleData bubbleData = bubbleDao.findData(resourcesDetail.getUid(), resId);
        int preBubbleId = -100;
        if (null != bubbleData) {
            if (null != oldWearData) {
                if (oldWearData.getBuddle_id() == bubbleData.getBuddle_id()) {
                    return ApiResult.getOk();
                }
                bubbleDao.updateBubbleStatus(oldWearData, 0);
            }
            bubbleDao.updateBubbleStatus(bubbleData, 1);
            pushBubbleChangeMsg(resourcesDetail.getUid(),resId);
            return ApiResult.getOk();
        } else {
            if (resId == DataResourcesConstant.DEFAULT_BUDDLE_ID) {
                if (null != oldWearData) {
                    bubbleDao.updateBubbleStatus(oldWearData, 0);
                }
                bubbleDao.insertData(resourcesDetail.getUid(), resId, Integer.MAX_VALUE, true);
                pushBubbleChangeMsg(resourcesDetail.getUid(),resId);
                return ApiResult.getOk();
            } else {
                logger.info("uid={} can not wear because user not get resId={}", resourcesDetail.getUid(), resId);
                return ApiResult.getError(DataResourcesHttpCode.NOT_OWN_RESOURCES);
            }
        }
    }

    @Override
    public ApiResult<String> remove(ResourcesDetail resourcesDetail) {
        int resId = Integer.parseInt(resourcesDetail.getResId());
        if (resId == DataResourcesConstant.DEFAULT_BUDDLE_ID) {
            logger.info("not remove resId={}", resId);
            return ApiResult.getError(DataResourcesHttpCode.OTHER_HANDLE_ERROR);
        }
        BubbleData bubbleData = bubbleDao.findData(resourcesDetail.getUid(), resId);
        if (null != bubbleData) {
            String uid = resourcesDetail.getUid();
            int days = resourcesDetail.getDays();
            int newEndTime = getNewEndTime(days, (int) bubbleData.getEnd_time());
            int now = DateHelper.getNowSeconds();
            if (days > 0 && newEndTime > now) {
                bubbleDao.updateBubbleNew(uid, resId, newEndTime, bubbleData, false, false);
            }else {
                bubbleDao.removeBubble(bubbleData);
            }
            return ApiResult.getOk();
        }
        return ApiResult.getError(DataResourcesHttpCode.NOT_OWN_RESOURCES);
    }

    @Override
    public ApiResult<String> addAndWear(ResourcesDetail resourcesDetail) {
        int resId = Integer.parseInt(resourcesDetail.getResId());
        if (resId == DataResourcesConstant.DEFAULT_BUDDLE_ID) {
            logger.info("not addAndWear resId={}", resId);
            return ApiResult.getError(DataResourcesHttpCode.OTHER_HANDLE_ERROR);
        }
        BuddleSourceData data = buddleSourceDao.findData(resId);
        if (null != buddleSourceDao.findData(resId)) {
            resourcesDetail.setName(data.getName());
            int days = resourcesDetail.getDays();
            Integer seconds = resourcesDetail.getSeconds();
            BubbleData bubbleData = bubbleDao.findData(resourcesDetail.getUid(), resId);
            long endTime = resourcesDetail.getEndTime();
            if (null != bubbleData) {
                endTime = this.getResExtendTime(bubbleData.getEnd_time(), days, seconds);
                resourcesDetail.setEndTime((int) endTime);
            }
            bubbleDao.updateBubbleNew(resourcesDetail.getUid(), resId, endTime, bubbleData, true, false);
            pushBubbleChangeMsg(resourcesDetail.getUid(),resId);
            return ApiResult.getOk();
        } else {
            logger.info("not find resId={}", resId);
            return ApiResult.getError(DataResourcesHttpCode.NOT_FIND_RESOURCES);
        }
    }

    @Override
    public ApiResult<String> expire() {
        int now = DateHelper.getNowSeconds();
        List<BubbleData> allExipreData = bubbleDao.listByEndTime(DataResourcesConstant.BASE_EXIPRE_START_TIME, now);
        int size = CollectionUtils.isEmpty(allExipreData) ? 0 : allExipreData.size();
        logger.info("bubble allExipreData size={}", size);
        ResourcesDetail resourcesDetail = new ResourcesDetail();
        resourcesDetail.setResType(DataResourcesConstant.TYPE_BUDDLE);
        for (BubbleData item : allExipreData) {
            resourcesDetail.setActionType(DataResourcesConstant.ACTION_ALL_EXPIRE);
            resourcesDetail.setUid(item.getUid());
            resourcesDetail.setResId(String.valueOf(item.getBuddle_id()));
            resourcesDetail.setmTime(now);
            resourcesDetail.setHandleTime(now);
            long nowEtime = item.getEnd_time();
            resourcesDetail.setEndTime((int) nowEtime);
            resourcesDetail.setDesc(DataResourcesConstant.EXIPRE_DESC);
            if (DataResourcesConstant.VIP_BUDDLE_LIST.contains(item.getBuddle_id())) {
                long eTime = this.getVipEndTime(item.getUid(), DataResourcesConstant.MIN_VIP_GET_BUDDLE, now);
                if (eTime >= now + 30) {
                    bubbleDao.updateBubbleNew(resourcesDetail.getUid(), item.getBuddle_id(), eTime, item, false, false);
                    resourcesDetail.setActionType(DataResourcesConstant.ACTION_EXPIRE_GET);
                    resourcesDetail.setEndTime((int) eTime);
                    logger.info("action_exipre_get eTime={} now={} nowEtime={} resourcesDetail={}", eTime, now, nowEtime, resourcesDetail);
                } else {
                    bubbleDao.removeBubble(item);
                }
            } else {
                bubbleDao.removeBubble(item);
            }
            this.writeToDb(resourcesDetail);
            logger.info("buddle exipre success resourcesDetail={}", resourcesDetail);
        }
        return ApiResult.getOk();
    }

    @Override
    public ApiResult<String> expireNotified() {
        return null;
    }

    @Override
    public void sendOfficialMsg(ResourcesDetail resourcesDTO) {
        if (resourcesDTO.getOfficialMsg() > 0) {
            String uid = resourcesDTO.getUid();
            int gainType = resourcesDTO.getGainType();
            ActorData actorData = actorDao.getActorDataFromCache(uid);
            if (gainType == 0) {
                int bubbleId = Integer.parseInt(resourcesDTO.getResId());
                BuddleSourceData buddleSourceData = buddleSourceDao.findData(bubbleId);
                if (buddleSourceData != null) {
                    int slang = actorData.getSlang();
                    String actText = slang == SLangType.ARABIC ? ResourceOfficialMsgConstant.ACTION_AR : ResourceOfficialMsgConstant.ACTION_EN;
                    String title = slang == SLangType.ARABIC ? ResourceOfficialMsgConstant.BUBBLE_AR : ResourceOfficialMsgConstant.BUBBLE_EN;
                    String body = slang == SLangType.ARABIC ? ResourceOfficialMsgConstant.BUBBLE_DESC_AR : ResourceOfficialMsgConstant.BUBBLE_DESC_EN;
                    commonOfficialMsg(uid, buddleSourceData.getBuddle_icon(), ResourceOfficialMsgConstant.ACTION_TYPE_BUBBLE, actText, title, body);
                }
            }
        }
    }

    private void pushBubbleChangeMsg(String uid, int bid) {
        BaseTaskFactory.getFactory().add(new Task() {
            @Override
            protected void execute() {
                BubbleChangeMsg msg = new BubbleChangeMsg();
                msg.setBid(bid);
                roomWebSender.sendPlayerWebMsg("", uid, uid, msg, false);
            }
        });
    }
}
