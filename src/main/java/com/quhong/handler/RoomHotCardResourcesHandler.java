package com.quhong.handler;

import com.quhong.constant.DataResourcesConstant;
import com.quhong.constant.DataResourcesHttpCode;
import com.quhong.core.utils.DateHelper;
import com.quhong.enums.ApiResult;
import com.quhong.enums.BaseDataResourcesConstant;
import com.quhong.enums.HttpCode;
import com.quhong.mysql.dao.RoomHotCardDao;
import com.quhong.mysql.data.ResourcesDetail;
import com.quhong.mysql.data.RoomHotCardData;
import com.quhong.redis.RoomHotCardRedis;
import jakarta.annotation.Resource;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import java.util.List;
import java.util.Set;
import java.util.concurrent.TimeUnit;

@Component
public class RoomHotCardResourcesHandler extends BaseResourcesHandler {

    private final Logger logger = LoggerFactory.getLogger(getClass());
    private static final Set<Integer> SUPPORT_HOT_TIME = Set.of(5, 10, 20, 30);

    @Resource
    private RoomHotCardDao roomHotCardDao;
    @Resource
    private RoomHotCardRedis roomHotCardRedis;

    @Override
    public ApiResult<String> add(ResourcesDetail resourcesDetail) {
        String uid = resourcesDetail.getUid();
        int hotTime = Integer.parseInt(resourcesDetail.getResId());
        int num = resourcesDetail.getNum();
        int days = resourcesDetail.getDays();
        if (!SUPPORT_HOT_TIME.contains(hotTime)) {
            logger.error("not support hot time. uid={} hotTime={}", uid, hotTime);
            return ApiResult.getError(HttpCode.PARAM_ERROR);
        }
        if (num < 1 || num > 100) {
            logger.error("num must between 1 and 100. uid={} num={}", uid, num);
            return ApiResult.getError(HttpCode.PARAM_ERROR);
        }
        if (days < 1 || days > 30) {
            logger.error("days must between 1 and 30. uid={} days={}", uid, days);
            return ApiResult.getError(HttpCode.PARAM_ERROR);
        }
        int nowTime = DateHelper.getNowSeconds();
        RoomHotCardData data = new RoomHotCardData();
        data.setUid(uid);
        data.setHotTime(hotTime);
        data.setNum(num);
        data.setEndTime(nowTime + (int)TimeUnit.DAYS.toSeconds(days));
        data.setCtime(nowTime);
        roomHotCardDao.insert(data);
        return ApiResult.getOk();
    }

    @Override
    public ApiResult<String> wear(ResourcesDetail resourcesDetail) {
        String uid = resourcesDetail.getUid();
        int cardId = Integer.parseInt(resourcesDetail.getResId());
        String roomId = resourcesDetail.getRoomId();
        if (!StringUtils.hasLength(roomId)) {
            logger.error("roomId is empty. uid={} cardId={}", uid, cardId);
            return ApiResult.getError(HttpCode.PARAM_ERROR);
        }
        int nowTime = DateHelper.getNowSeconds();
        RoomHotCardData cardData = roomHotCardDao.selectById(cardId);
        if (cardData == null || !cardData.getUid().equals(uid) || cardData.getNum() < 1) {
            logger.error("not find card data. uid={} cardId={}", uid, cardId);
            return ApiResult.getError(DataResourcesHttpCode.NOT_FIND_RESOURCES);
        }
        if (cardData.getStatus() != 0 || nowTime > cardData.getEndTime()) {
            logger.error("card is not available. uid={} cardId={}", uid, cardId);
            return ApiResult.getError(DataResourcesHttpCode.NOT_FIND_RESOURCES);
        }
        int endTime = roomHotCardRedis.getRoomHotCardEndTime(roomId);
        if (endTime != 0 && endTime > nowTime) {
            logger.error("room is hot. uid={} cardId={} roomId={} endTime={}", uid, cardId, roomId, endTime);
            return ApiResult.getError(DataResourcesHttpCode.ROOM_IS_HOT);
        }
        if (cardData.getNum() == 1) {
            cardData.setStatus(1);
            cardData.setRoomId(roomId);
            cardData.setMtime(nowTime);
            roomHotCardDao.updateById(cardData);
        } else {
            roomHotCardDao.updateNum(cardId, cardData.getNum() - 1);
            RoomHotCardData newData = new RoomHotCardData();
            BeanUtils.copyProperties(cardData, newData, "id");
            newData.setNum(1);
            newData.setStatus(1);
            newData.setRoomId(roomId);
            newData.setMtime(nowTime);
            roomHotCardDao.insert(newData);
        }
        return ApiResult.getOk();
    }

    @Override
    public ApiResult<String> addAndWear(ResourcesDetail resourcesDetail) {
        return ApiResult.getError(HttpCode.PARAM_ERROR);
    }

    @Override
    public ApiResult<String> remove(ResourcesDetail resourcesDetail) {
        int cardId = Integer.parseInt(resourcesDetail.getResId());
        String uid = resourcesDetail.getUid();
        RoomHotCardData cardData = roomHotCardDao.selectById(cardId);
        if (cardData == null || !cardData.getUid().equals(uid) || cardData.getStatus() != 0) {
            logger.error("not find card data. uid={} cardId={}", uid, cardId);
            return ApiResult.getError(DataResourcesHttpCode.NOT_FIND_RESOURCES);
        }
        cardData.setStatus(3);
        cardData.setMtime(DateHelper.getNowSeconds());
        roomHotCardDao.updateById(cardData);
        return ApiResult.getOk();
    }

    @Override
    public ApiResult<String> expire() {
        int now = DateHelper.getNowSeconds();
        List<RoomHotCardData> expiredList = roomHotCardDao.getExpiredNotupdateList();
        int size = CollectionUtils.isEmpty(expiredList) ? 0 : expiredList.size();
        logger.info("expiredList. size={}", size);
        ResourcesDetail resourcesDetail = new ResourcesDetail();
        resourcesDetail.setResType(BaseDataResourcesConstant.TYPE_ROOM_HOT_CARD);
        resourcesDetail.setActionType(DataResourcesConstant.ACTION_ALL_EXPIRE);
        int expireCount = 0;
        for (RoomHotCardData item : expiredList) {
            resourcesDetail.setUid(item.getUid());
            resourcesDetail.setResId(String.valueOf(item.getId()));
            resourcesDetail.setmTime(now);
            resourcesDetail.setHandleTime(now);
            resourcesDetail.setEndTime(item.getEndTime());
            resourcesDetail.setDesc(DataResourcesConstant.EXIPRE_DESC);
            this.writeToDb(resourcesDetail);
            expireCount++;
            logger.info("resourceData expire success. resourcesDetail={}", resourcesDetail.toString());
        }
        logger.info("expiredList size={} expireCount={}", size, expireCount);
        return ApiResult.getOk();
    }

    @Override
    public ApiResult<String> expireNotified() {
        return null;
    }

}
