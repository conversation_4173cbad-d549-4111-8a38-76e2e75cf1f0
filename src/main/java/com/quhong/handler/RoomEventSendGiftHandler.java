package com.quhong.handler;

import com.alibaba.fastjson.JSONObject;
import com.google.common.collect.Interner;
import com.google.common.collect.Interners;
import com.quhong.core.config.ServerConfig;
import com.quhong.core.utils.DateHelper;
import com.quhong.data.SendGiftData;
import com.quhong.mysql.dao.GiftDao;
import com.quhong.mysql.dao.RoomEventDao;
import com.quhong.mysql.data.GiftData;
import com.quhong.mysql.data.RoomEventData;
import com.quhong.redis.RoomEventRedis;
import jakarta.annotation.Resource;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;
import org.springframework.util.StringUtils;

/**
 * <AUTHOR>
 * @date 2022/12/12
 */
 @Component
public class RoomEventSendGiftHandler implements ActivityHandler {

    private static final Logger logger = LoggerFactory.getLogger(RoomEventSendGiftHandler.class);

    private static final String EVENT_LOCK_KEY = "event_send_gift_";
    private static final Interner<String> stringPool = Interners.newWeakInterner();

    @Resource
    private RoomEventDao roomEventDao;
    @Resource
    private GiftDao giftDao;
    @Resource
    private RoomEventRedis roomEventRedis;

    @Override
    public void process(SendGiftData data) {
        if (data == null || StringUtils.isEmpty(data.getRoomId()) || StringUtils.isEmpty(data.getFrom_uid()) || data.isUseVirtualBeans()) {
            return;
        }
        if (ServerConfig.isNotProduct()) {
            logger.info("SendGiftData={}", JSONObject.toJSONString(data));
        }
        GiftData giftData = giftDao.getGiftFromCache(data.getGid());
        if (giftData.getGtype() == 2) {
            // 银币礼物不计算活动贡献
            return;
        }
        int nowTime = DateHelper.getNowSeconds();
        int totalPrice = data.getPrice() * data.getNumber() * data.getAid_list().size();
        synchronized (stringPool.intern(EVENT_LOCK_KEY + data.getRoomId())) {
            try {
                RoomEventData ongoingRoomEvent = roomEventDao.getOngoingRoomEvent(data.getRoomId());
                if (ongoingRoomEvent == null || ongoingRoomEvent.getEndTime() < nowTime || ongoingRoomEvent.getStartTime() > nowTime) {
                    return;
                }
                roomEventRedis.incRankingScore(data.getFrom_uid(), ongoingRoomEvent.getId(), RoomEventRedis.DIAMOND_RANKING, totalPrice);
                for (String aid : data.getAid_list()) {
                    roomEventRedis.incRankingScore(aid, ongoingRoomEvent.getId(), RoomEventRedis.CHARM_RANKING, data.getEarn_charm().intValue());
                }
            } catch (Exception e) {
                logger.error("update room event send gift record data error. roomId={} uid={} number={} price={} {}", data.getRoomId(), data.getFrom_uid(), data.getNumber(), data.getPrice(), e.getMessage(), e);
            }
        }
    }
}
