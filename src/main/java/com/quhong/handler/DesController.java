package com.quhong.handler;

import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.annotation.JSONField;
import com.quhong.enums.HttpCode;
import com.quhong.utils.DESEncoderUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

/**
 * 返回DES加密后的数据
 *
 * <AUTHOR>
 * @date 2022/9/30
 */
public class DesController extends H5Controller{

    private static final Logger logger = LoggerFactory.getLogger(DesController.class);

    @Override
    protected String createResult(HttpCode httpCode, Object data) {
        HttpEnvData envData = new HttpEnvData();
        return createResult(httpCode, data, envData);
    }

    protected String createResult(HttpCode httpCode, Object data, HttpEnvData envData) {
        String result = super.createResult(httpCode, data);
        if (envData.getDebug() == 1) {
            return result;
        } else {
            String encryptParam = "";
            try {
                encryptParam = DESEncoderUtils.encryptParam(result);
            } catch (Exception e) {
                logger.error("param des encoder error. {}", e.getMessage(), e);
            }
            TotalData totalData = new TotalData();
            totalData.setTotalData(encryptParam);
            return JSONObject.toJSONString(totalData);
        }
    }

    public static class TotalData {

        @JSONField(name = "total_data")
        private String totalData;

        public String getTotalData() {
            return totalData;
        }

        public void setTotalData(String totalData) {
            this.totalData = totalData;
        }
    }
}
