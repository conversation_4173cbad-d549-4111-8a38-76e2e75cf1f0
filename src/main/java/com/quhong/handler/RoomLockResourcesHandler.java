package com.quhong.handler;

import com.quhong.constant.DataResourcesConstant;
import com.quhong.constant.DataResourcesHttpCode;
import com.quhong.core.utils.DateHelper;
import com.quhong.enums.ApiResult;
import com.quhong.mongo.dao.MongoRoomDao;
import com.quhong.mongo.dao.RoomLockDao;
import com.quhong.mongo.data.MongoRoomData;
import com.quhong.mongo.data.RoomLockData;
import com.quhong.mysql.dao.WhiteTestDao;
import com.quhong.mysql.data.ResourcesDetail;
import com.quhong.redis.RoomPwdRedis;
import com.quhong.utils.RoomUtils;
import jakarta.annotation.Resource;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import java.util.List;


@Component
public class RoomLockResourcesHandler extends BaseResourcesHandler {
    private static final Logger logger = LoggerFactory.getLogger(RoomLockResourcesHandler.class);

    @Resource
    private RoomLockDao roomLockDao;
    @Resource
    private MongoRoomDao mongoRoomDao;
    @Resource
    private RoomPwdRedis roomPwdRedis;
    @Resource
    private WhiteTestDao whiteTestDao;

    @Override
    public ApiResult<String> add(ResourcesDetail resourcesDetail) {
        int days = resourcesDetail.getDays();
        Integer seconds = resourcesDetail.getSeconds();
        RoomLockData roomLockData = roomLockDao.findData(resourcesDetail.getUid());
        long endTime = resourcesDetail.getEndTime();
        if (null != roomLockData) {
            endTime = this.getResExtendTime(roomLockData.getEnd_time(), days, seconds);
            resourcesDetail.setEndTime((int) endTime);
            roomLockDao.updateRoomLockByTime(resourcesDetail.getUid(), endTime, roomLockData);
        } else {
            roomLockDao.insertDB(resourcesDetail.getUid(), (int) endTime);
        }
        return ApiResult.getOk();

    }

    @Override
    public ApiResult<String> wear(ResourcesDetail resourcesDetail) {
        logger.info("room lock can not wear resourcesDetail:{}", resourcesDetail);
        return ApiResult.getError(DataResourcesHttpCode.PARAMS_ERROR);
    }

    @Override
    public ApiResult<String> remove(ResourcesDetail resourcesDetail) {
        logger.info("room lock can not remove resourcesDetail:{}", resourcesDetail);
        return ApiResult.getError(DataResourcesHttpCode.PARAMS_ERROR);
    }

    @Override
    public ApiResult<String> addAndWear(ResourcesDetail resourcesDetail) {
        return add(resourcesDetail);
    }

    @Override
    public ApiResult<String> expire() {
        int now = DateHelper.getNowSeconds();
        List<RoomLockData> allExpireData = roomLockDao.listByEndTime(DataResourcesConstant.BASE_EXIPRE_START_TIME, now);
        int size = CollectionUtils.isEmpty(allExpireData) ? 0 : allExpireData.size();
        logger.info("RoomLockData allExpireData size={} ", size);
        ResourcesDetail resourcesDetail = new ResourcesDetail();
        resourcesDetail.setResType(DataResourcesConstant.TYPE_ROOM_LOCK);
        for (RoomLockData item : allExpireData) {
            resourcesDetail.setActionType(DataResourcesConstant.ACTION_ALL_EXPIRE);
            resourcesDetail.setUid(item.getUid());
            resourcesDetail.setResId(String.valueOf(DataResourcesConstant.DEFAULT_ROOM_LOCK_ID));
            resourcesDetail.setmTime(now);
            resourcesDetail.setHandleTime(now);
            long nowEtime = item.getEnd_time();
            resourcesDetail.setEndTime((int) nowEtime);
            resourcesDetail.setDesc(DataResourcesConstant.EXIPRE_DESC);
            roomLockDao.removeRoomLock(item);
            reSetRoomPwd(item.getUid());
            this.writeToDb(resourcesDetail);
            logger.info("room lock expire success resourcesDetail={}", resourcesDetail);
        }
        return ApiResult.getOk();
    }

    @Override
    public ApiResult<String> expireNotified() {
        return null;
    }


    private void reSetRoomPwd(String uid) {
        String roomId = RoomUtils.formatRoomId(uid);
        if (whiteTestDao.isMemberByType(roomId, WhiteTestDao.WHITE_TYPE_ROOM_ID)) {
            return;
        }
        MongoRoomData mongoRoomData = mongoRoomDao.findData(roomId);
        if (mongoRoomData != null) {
            mongoRoomDao.updateField(roomId, "pwd", "");
            roomPwdRedis.remove(roomId);
            logger.info("room pwd set empty success uid={}", uid);
        } else {
            logger.info("room lock delete fail not find mongo room uid={}", uid);
        }
    }
}
