package com.quhong.handler;

import com.quhong.config.CaffeineCacheConfig;
import com.quhong.core.utils.DateHelper;
import com.quhong.data.RechargeInfo;
import com.quhong.mongo.dao.SysConfigDao;
import com.quhong.mysql.dao.VersionRechargeRankDao;
import com.quhong.mysql.data.VersionRechargeRankData;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.stereotype.Component;

import jakarta.annotation.Resource;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2022/11/16
 */
@Component
public class RechargeRankHandler implements RechargeHandler{

    private static final Logger logger = LoggerFactory.getLogger(RechargeRankHandler.class);

    private static final int BIG_BOSS_START_TIME = 20210315;

    @Resource
    private SysConfigDao sysConfigDao;
    @Resource
    private VersionRechargeRankDao rechargeRankDao;


    @Override
    public void process(RechargeInfo rechargeInfo) {
        // int rechargeDate = Integer.parseInt(DateHelper.ARABIAN.formatDateInDay2());
        // if (rechargeInfo.getRechargeType() == 1 && rechargeDate >= BIG_BOSS_START_TIME) {
        //     List<String> hideUidList = getHideUidList();
        //     int isAdmin = hideUidList.contains(rechargeInfo.getUid()) ? 1 :0;
        //     rechargeRankDao.insert(new VersionRechargeRankData(rechargeInfo.getUid(), rechargeInfo.getRechargeDiamond(), rechargeInfo.getRechargeTime(), rechargeDate, isAdmin));
        // }
    }

    @Cacheable(value = "getHideUidList", cacheManager = CaffeineCacheConfig.EXPIRE_1M_AFTER_WRITE)
    public List<String> getHideUidList() {
        return sysConfigDao.getList(SysConfigDao.HIDE_UID_LIST, SysConfigDao.HIDE_UID_LIST);
    }
}
