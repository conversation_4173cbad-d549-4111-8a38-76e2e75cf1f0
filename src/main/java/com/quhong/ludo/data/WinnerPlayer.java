package com.quhong.ludo.data;

public class WinnerPlayer {
    private String uid;
    private String head;
    private String name;
    private String rewardValue;
    private int auto; // 1玩家托管 0 正常

    public WinnerPlayer() {
    }

    public WinnerPlayer(GamePlayer gamePlayer, String rewardValue, int auto) {
        this.uid = gamePlayer.getUid();
        this.head = gamePlayer.getHead();
        this.name = gamePlayer.getName();
        this.rewardValue = rewardValue;
        this.auto = auto;
    }

    public String getUid() {
        return uid;
    }

    public void setUid(String uid) {
        this.uid = uid;
    }

    public String getHead() {
        return head;
    }

    public void setHead(String head) {
        this.head = head;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getRewardValue() {
        return rewardValue;
    }

    public void setRewardValue(String rewardValue) {
        this.rewardValue = rewardValue;
    }

    public int getAuto() {
        return auto;
    }

    public void setAuto(int auto) {
        this.auto = auto;
    }

    @Override
    public String toString() {
        return "WinnerPlayer{" +
                "uid='" + uid + '\'' +
                ", head='" + head + '\'' +
                ", name='" + name + '\'' +
                ", rewardValue='" + rewardValue + '\'' +
                ", auto=" + auto +
                '}';
    }
}
