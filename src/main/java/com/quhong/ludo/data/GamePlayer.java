package com.quhong.ludo.data;

import com.quhong.core.utils.DateHelper;

import java.util.ArrayList;
import java.util.List;

public class GamePlayer {
    private String uid;
    private String name;
    private String rid;
    private String head;
    private int status; // 玩家状态 0 正常1 托管 2 退出 3 所有棋子已到达终点
    private int hostingStartTime; // 托管开始时间
    private int hostingTime; // 托管时长
    private int side; // 1, 蓝 2 红 3 绿 4 黄
    private int diceType; // -1全部棋子在基地，需要骰子数为6才能执行
    private int startNodeCount; // 每局游戏的第一颗棋子出基地，前4次投掷骰子中至少必出一个6点。
    private int fastDiceType; // 快速模式，如果投掷2个骰子点数相同，走完相同的2个点数后，可再投掷一次
    private int fastDiceCount; // 快速模式可摇骰子次数

    private List<Chessman> chessmanList;

    public GamePlayer() {
        chessmanList = new ArrayList<>();
    }

    public String getUid() {
        return uid;
    }

    public void setUid(String uid) {
        this.uid = uid;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getRid() {
        return rid;
    }

    public void setRid(String rid) {
        this.rid = rid;
    }

    public String getHead() {
        return head;
    }

    public void setHead(String head) {
        this.head = head;
    }

    public int getSide() {
        return side;
    }

    public void setSide(int side) {
        this.side = side;
    }

    public int getStatus() {
        return status;
    }

    public void setStatus(int status) {
        this.status = status;
    }

    public int getDiceType() {
        return diceType;
    }

    public void setDiceType(int diceType) {
        this.diceType = diceType;
    }

    public int getHostingStartTime() {
        return hostingStartTime;
    }

    public void setHostingStartTime(int hostingStartTime) {
        this.hostingStartTime = hostingStartTime;
    }

    public int getHostingTime() {
        return hostingTime;
    }

    public void setHostingTime(int hostingTime) {
        this.hostingTime = hostingTime;
    }

    public void addHostingTime() {
        this.hostingTime = hostingTime + DateHelper.getNowSeconds() - hostingStartTime;
    }

    public List<Chessman> getChessmanList() {
        return chessmanList;
    }

    public void setChessmanList(List<Chessman> chessmanList) {
        this.chessmanList = chessmanList;
    }

    public int getStartNodeCount() {
        return startNodeCount;
    }

    public void setStartNodeCount(int startNodeCount) {
        this.startNodeCount = startNodeCount;
    }

    public int getFastDiceType() {
        return fastDiceType;
    }

    public void setFastDiceType(int fastDiceType) {
        this.fastDiceType = fastDiceType;
    }

    public int getFastDiceCount() {
        return fastDiceCount;
    }

    public void setFastDiceCount(int fastDiceCount) {
        this.fastDiceCount = fastDiceCount;
    }

    public void initChessman(int side) {
        this.side = side;
        for (int i = 1; i <= 4; i++) {
            Chessman chessman = new Chessman();
            chessman.setChessmanId(side + "-" + i);
            chessman.setSide(side);
            chessman.setUid(uid);
            chessman.setNodeId(-(side * 10 + i));
            chessman.setStartNodeId(-(side * 10 + i));
            chessmanList.add(chessman);
        }
    }

}
