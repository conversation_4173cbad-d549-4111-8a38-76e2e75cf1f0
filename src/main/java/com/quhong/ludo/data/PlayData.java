package com.quhong.ludo.data;

import java.util.ArrayList;
import java.util.List;

public class PlayData {
    private String opUid; // 操作者uid
    private int opType; // 1 掷骰子， 2 棋子走动， 3 棋子返回， 4 棋子到达终点， 5 连续投掷3次均为6点， 6 棋子出基地
    private List<Integer> diceValueList; // 掷骰子时的播放骰子数
    private String chessmanId; //棋子id
    private int fromNodeId; //开始格子
    private int toNodeId; //结束格子

    public PlayData() {
        this.diceValueList = new ArrayList<>();
    }

    public String getOpUid() {
        return opUid;
    }

    public void setOpUid(String opUid) {
        this.opUid = opUid;
    }

    public int getOpType() {
        return opType;
    }

    public void setOpType(int opType) {
        this.opType = opType;
    }

    public List<Integer> getDiceValueList() {
        return diceValueList;
    }

    public void setDiceValueList(List<Integer> diceValueList) {
        this.diceValueList = diceValueList;
    }

    public String getChessmanId() {
        return chessmanId;
    }

    public void setChessmanId(String chessmanId) {
        this.chessmanId = chessmanId;
    }

    public int getFromNodeId() {
        return fromNodeId;
    }

    public void setFromNodeId(int fromNodeId) {
        this.fromNodeId = fromNodeId;
    }

    public int getToNodeId() {
        return toNodeId;
    }

    public void setToNodeId(int toNodeId) {
        this.toNodeId = toNodeId;
    }

    @Override
    public String toString() {
        return "PlayData{" +
                "opUid='" + opUid + '\'' +
                ", opType=" + opType +
                ", diceValueList=" + diceValueList +
                ", chessmanId='" + chessmanId + '\'' +
                ", fromNodeId=" + fromNodeId +
                ", toNodeId=" + toNodeId +
                '}';
    }
}
