package com.quhong.task;

import com.quhong.core.concurrency.BaseTaskFactory;
import com.quhong.core.concurrency.queues.MonitorTaskQueue;

public class MsgTaskQueue extends MonitorTaskQueue {
    public MsgTaskQueue() {
        super(TaskFactory.getFactory().getMsgPool());
    }

    public MsgTaskQueue(long runOnceLimit) {
        super(BaseTaskFactory.getFactory().getMainPool(), runOnceLimit);
    }
}
