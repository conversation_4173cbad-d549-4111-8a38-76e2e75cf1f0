package com.quhong.task;

import com.quhong.analysis.EventDTO;
import com.quhong.analysis.EventReport;
import com.quhong.analysis.RoomEventLogEvent;
import com.quhong.core.date.DateSupport;
import com.quhong.mongo.dao.EventRankingRecordDao;
import com.quhong.mongo.data.EventRankingRecordData;
import com.quhong.mysql.dao.RoomEventDao;
import com.quhong.mysql.data.RoomEventData;
import com.quhong.redis.RoomEventRedis;
import com.quhong.service.EventRobotService;
import com.quhong.utils.K8sUtils;
import com.quhong.utils.RoomUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import jakarta.annotation.Resource;

import java.time.LocalDate;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2022/12/13
 */
@Component
public class RoomEventTask {

    private static final Logger logger = LoggerFactory.getLogger(RoomEventTask.class);

    @Resource
    private EventRobotService eventRobotService;
    @Resource
    private RoomEventDao roomEventDao;
    @Resource
    private RoomEventRedis roomEventRedis;
    @Resource
    private EventRankingRecordDao eventRankingRecordDao;
    @Resource
    private K8sUtils k8sUtils;
    @Resource
    private EventReport eventReport;

    /**
     * 每59秒钟执行一次自动添加机器人订阅检查
     */
//    @Scheduled(fixedDelay = 59 * 1000, initialDelay = 5000)
    public void autoSubCheck() {
        if (k8sUtils.isMasterFromCache()) {
            long timeMillis = System.currentTimeMillis();
            logger.info("auto sub check.");
            eventRobotService.autoSubEventCheck();
            logger.info("auto sub check. cost={}", System.currentTimeMillis() - timeMillis);
        }
    }

    /**
     * 扫描前一天已结束的活动，上报埋点数据
     * 每天北京时间：5:05 执行一次
     */
    @Scheduled(cron = "30 5 21 * * ?")
    public void autoDoEventReport() {
        if (!k8sUtils.isMasterFromCache()) {
            return;
        }
        long timeMillis = System.currentTimeMillis();
        LocalDate yesterday = DateSupport.ARABIAN.getYesterday();
        int[] startAndEndTime = DateSupport.ARABIAN.getStartAndEndTime(yesterday);
        int startTime = startAndEndTime[0];
        int endTime = startAndEndTime[1];
        logger.info("auto do event report . startTime={}, endTime={}", startTime, endTime);
        List<RoomEventData> list = roomEventDao.getHasEndedEventList(startTime, endTime);
        if (CollectionUtils.isEmpty(list)) {
            logger.info("not find have ended event. startTime={}, endTime={}", startTime, endTime);
            return;
        }
        list.forEach(k -> {
            Map<String, Long> diamondRankingMap = roomEventRedis.getRankingMap(k.getId(), RoomEventRedis.DIAMOND_RANKING, 0, -1);
            eventRankingRecordDao.save(new EventRankingRecordData(k.getId(), RoomEventRedis.DIAMOND_RANKING, diamondRankingMap));
            Map<String, Long> charmRankingMap = roomEventRedis.getRankingMap(k.getId(), RoomEventRedis.CHARM_RANKING, 0, -1);
            eventRankingRecordDao.save(new EventRankingRecordData(k.getId(), RoomEventRedis.CHARM_RANKING, charmRankingMap));
            long totalBeans = diamondRankingMap.values().stream().mapToLong(Long::longValue).sum();
            int sendGiftNum = diamondRankingMap.size();
            int receiveGiftNum = charmRankingMap.size();
            int enterRoomNum = roomEventRedis.getEnterRoomUserNum(k.getId());
            doEventReport(k, totalBeans, sendGiftNum, receiveGiftNum, enterRoomNum);
        });
        logger.info("auto do event report end. list.size={} cost={}", list.size(), System.currentTimeMillis() - timeMillis);
    }

    /**
     * 房间活动埋点
     */
    public void doEventReport(RoomEventData data, long costBeans, int sendGiftNum, int receiveGiftNum, int enterRoomNum) {
        RoomEventLogEvent event = new RoomEventLogEvent();
        event.setUid(data.getCreator());
        event.setRoom_id(data.getRoomId());
        event.setRoom_event_id(data.getId());
        event.setRoom_event_name(data.getName());
        event.setRoom_event_desc(data.getDescription());
        event.setRoom_event_type(getStrEventType(data.getType()));
        event.setRoom_event_start_time(data.getStartTime());
        event.setRoom_event_end_time(data.getEndTime());
        event.setRoom_event_duration(data.getDuration());
        event.setRoom_event_cost_beans(costBeans);
        event.setRoom_event_send_gift_sum(sendGiftNum);
        event.setRoom_event_receive_gift_sum(receiveGiftNum);
        event.setRoom_event_enter_room_sum(enterRoomNum);
        event.setRoom_event_sub_num(data.getSubNum());
        event.setRoom_event_creator(data.getCreator());
        event.setRoom_event_creator_identity(data.getCreator().equals(RoomUtils.getRoomHostId(data.getRoomId())) ? 1 : 2);
        event.setIs_delete(0);
        event.setCtime(data.getCtime());
        eventReport.track(new EventDTO(event));
    }

    private String getStrEventType(int type) {
        switch (type) {
            case 1 -> {
                return "Party";
            }
            case 2 -> {
                return "Chat";
            }
            case 3 -> {
                return "Game";
            }
            case 4 -> {
                return "Singing";
            }
            case 5 -> {
                return "Competition";
            }
            case 6 -> {
                return "Poetry";
            }
            default -> {
                return "Other";
            }
        }
    }

}
