package com.quhong.task;

import com.quhong.config.AsyncConfig;
import com.quhong.service.StarGiftService;
import com.quhong.utils.K8sUtils;
import jakarta.annotation.Resource;
import org.springframework.scheduling.annotation.Async;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * @date 2025/1/8
 */
@Component
public class StarGiftTask {

    @Resource
    private K8sUtils k8sUtils;
    @Resource
    private StarGiftService starGiftService;


    /**
     * 名人礼物榜单奖励
     * 每周日00:05:00(GTM+3)执行一次
     */
    @Async(AsyncConfig.ASYNC_TASK)
    @Scheduled(cron = "0 5 21 ? * SAT")
    public void starGiftWeeklyRankingReward() {
        if (k8sUtils.isMasterFromCache()) {
            starGiftService.sendWeeklyRankingReward();
        }
    }

    /**
     * 更新名人礼物展示给所有人的结束时间
     * 每天23:59:00(GTM+3)执行一次
     */
    @Async(AsyncConfig.ASYNC_TASK)
    @Scheduled(cron = "0 59 20 ? * *")
    public void updateStarGiftShowAllEndTime() {
        if (k8sUtils.isMasterFromCache()) {
            starGiftService.updateStarGiftShowAllEndTime();
        }
    }
}
