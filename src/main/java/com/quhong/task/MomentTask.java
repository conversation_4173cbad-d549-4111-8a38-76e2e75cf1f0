package com.quhong.task;

import com.quhong.config.AsyncConfig;
import com.quhong.mongo.dao.MomentNoticeDao;
import com.quhong.service.MomentRobotService;
import com.quhong.service.MomentService;
import com.quhong.service.RecommendService;
import com.quhong.utils.K8sUtils;
import jakarta.annotation.Resource;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.scheduling.annotation.Async;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

@Component
public class MomentTask {
    private static final Logger logger = LoggerFactory.getLogger(MomentTask.class);

    @Resource
    private K8sUtils k8sUtils;
    @Resource
    private MomentNoticeDao momentNoticeDao;
    @Resource
    private RecommendService recommendService;
    @Resource
    private MomentRobotService momentRobotService;
    @Resource
    private MomentService momentService;

    /**
     * 每天02:10定期清理3个月前的朋友圈通知（北京时间10:10）
     */
    @Async(AsyncConfig.ASYNC_TASK)
    @Scheduled(cron = "0 10 02 * * ?")
    public void cleanupNotice() {
        if (k8sUtils.isMaster()) {
            logger.info("start cleanup Notice.");
            long cleanupCount = momentNoticeDao.cleanupNotice();
            logger.info("finish cleanup Notice. cleanupCount={}", cleanupCount);
        }
    }

    /**
     * 计算最近三天内发送的朋友圈热度，并推送到推荐池中
     */
//    @Async(AsyncConfig.ASYNC_TASK)
//    @Scheduled(cron = "0 0/10 * * * ?")
    public void buildRecommendMomentList() {
        if (k8sUtils.isMaster()) {
            long currentTimeMillis = System.currentTimeMillis();
            logger.info("start build recommend moment.");
            recommendService.buildRecommendMomentList();
            logger.info("finish build recommend moment. timeMillis={}", System.currentTimeMillis() - currentTimeMillis);
        }
    }

    /**
     * 每59秒钟执行一次
     */
    @Async(AsyncConfig.ASYNC_TASK)
    @Scheduled(fixedDelay = 59 * 1000, initialDelay = 5000)
    public void autoLikeCheck() {
        momentRobotService.autoLikeCheck();
        momentService.expiredTopMoment();
    }
}
