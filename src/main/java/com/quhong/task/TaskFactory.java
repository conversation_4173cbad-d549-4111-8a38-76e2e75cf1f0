package com.quhong.task;

import com.quhong.core.concurrency.BaseTaskFactory;
import com.quhong.core.concurrency.pools.TaskPool;
import com.quhong.core.concurrency.tasks.ITask;
import org.springframework.stereotype.Component;

@Component
public class TaskFactory extends BaseTaskFactory {
    public static TaskFactory getFactory() {
        return (TaskFactory) factory;
    }

    public TaskFactory() {
        super(16, 32);
    }


    private TaskPool subPool;

    @Override
    public void init() {
        super.init();
        this.subPool = createTaskPool("sub", 4, 32);
    }

    public void addSubTask(ITask task) {
        this.subPool.add(task);
    }
}
