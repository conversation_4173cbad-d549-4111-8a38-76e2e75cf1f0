package com.quhong.task;

import com.quhong.config.AsyncConfig;
import com.quhong.constant.RoomListConstant;
import com.quhong.service.HomeRankListService;
import com.quhong.service.PartyListService;
import com.quhong.utils.K8sUtils;
import org.springframework.scheduling.annotation.Async;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

import jakarta.annotation.Resource;

@Component
public class RoomListTask {

    @Resource
    private K8sUtils k8sUtils;
    @Resource
    private PartyListService partyListService;
    @Resource
    private HomeRankListService homeRankListService;

    /**
     * 每20秒钟生成一次popular首页列表
     */
    @Async(AsyncConfig.ASYNC_TASK)
    @Scheduled(cron = "0/20 * * * * ?")
    public void makePartyList() {
        if (k8sUtils.isMasterFromCache()) {
            partyListService.makePartyList();
        }
    }

    /**
     * 用新mysql数据源最快更新频率 从第5分5秒开始，每10分钟执行 5 5/10 * * * ?
     * 老的数据 从第30分10秒开始，每1小时更新 10 30 0/1 * * ?
     * 每5分钟更新 5 0/5 * * * ?
     */
    @Async(AsyncConfig.ASYNC_TASK)
    @Scheduled(cron = "10 30 0/1 * * ?")
    public void makeHomeRoomRankList() {
        if (k8sUtils.isMasterFromCache()) {
            homeRankListService.makeHomeRankList(RoomListConstant.ROOM_RANK_TYPE);
        }
    }

    /**
     * 用新mysql数据源最快更新频率 从第5分5秒开始，每10分钟执行 5 5/10 * * * ?
     * 老的数据 从第31分10秒开始，每1小时更新 10 30 0/1 * * ?
     */
    @Async(AsyncConfig.ASYNC_TASK)
    @Scheduled(cron = "10 31 0/1 * * ?")
    public void makeHomeSendRankList() {
        if (k8sUtils.isMasterFromCache()) {
            homeRankListService.makeHomeRankList(RoomListConstant.USER_RANK_SEND_TYPE);
        }
    }

    /**
     * 用新mysql数据源最快更新频率 从第5分5秒开始，每10分钟执行 5 5/10 * * * ?
     * 老的数据 从第32分10秒开始，每1小时更新 10 30 0/1 * * ?
     */
    @Async(AsyncConfig.ASYNC_TASK)
    @Scheduled(cron = "10 32 0/1 * * ?")
    public void makeHomeReceiveRankList() {
        if (k8sUtils.isMasterFromCache()) {
            homeRankListService.makeHomeRankList(RoomListConstant.USER_RANK_RECEIVE_TYPE);
        }
    }

    /**
     * 从redis生成新的榜单mysql数据源
     * 从第0分5秒开始，每5分钟执行 5 0/5 * * * ?
     */
    @Async(AsyncConfig.ASYNC_TASK)
    @Scheduled(cron = "5 0/5 * * * ?")
    public void makeHomeRankListRes() {
        if (k8sUtils.isMasterFromCache()) {
            homeRankListService.writeToDb();
        }
    }

    /**
     * 每分钟执行一次，刷新推荐用户列表
     */
    @Async(AsyncConfig.ASYNC_TASK)
    @Scheduled(cron = "35 0/1 * * * ?")
    public void refreshRecommendList() {
        if (k8sUtils.isMasterFromCache()) {
            partyListService.refreshRecommendList();
        }
    }
}
