package com.quhong;

import com.quhong.core.config.ServerConfig;
import org.apache.dubbo.config.spring.context.annotation.EnableDubbo;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.boot.autoconfigure.data.redis.RedisAutoConfiguration;
import org.springframework.boot.autoconfigure.jdbc.DataSourceAutoConfiguration;
import org.springframework.boot.autoconfigure.mongo.MongoAutoConfiguration;
import org.springframework.boot.web.servlet.ServletComponentScan;
import org.springframework.cloud.openfeign.EnableFeignClients;
import org.springframework.context.annotation.ImportResource;
import org.springframework.scheduling.annotation.EnableAsync;
import org.springframework.scheduling.annotation.EnableScheduling;

@EnableDubbo
@EnableFeignClients
@EnableScheduling
@EnableAsync
@ServletComponentScan
@ImportResource("classpath*:spring-context.xml")
@SpringBootApplication(exclude = {RedisAutoConfiguration.class, MongoAutoConfiguration.class,
        DataSourceAutoConfiguration.class})
public class RoomServiceApplication {
    private static final Logger logger = LoggerFactory.getLogger(RoomServiceApplication.class);

    public static void main(String[] args) {
        SpringApplication.run(RoomServiceApplication.class, args);
        logger.info("============= room service {} start ===============================", ServerConfig.getServerID());
    }
}
