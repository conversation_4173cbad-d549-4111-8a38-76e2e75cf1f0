package com.quhong.mongo.data;

import org.bson.types.ObjectId;
import org.springframework.data.annotation.Id;
import org.springframework.data.mongodb.core.mapping.Document;

import java.util.List;

/**
 * vip奖励
 */
@Document(collection = "vip_award_data")
public class VipAwardData {

    @Id
    private ObjectId _id;
    private int vipLevel;      // vip等级
    private int invisible;     // 是否可以隐身访问
    private List<SourceInfo> bubbleList;     // 赠送气泡id
    private List<SourceInfo> micList;     // 赠送麦位框id
    private List<SourceInfo> joinList;     // 赠送坐骑id
    private List<SourceInfo> screenList;     // 赠送浮萍id
    private List<SourceInfo> enterEffectList;     // 赠送入场通知id

    private List<Integer> bubbleRemoveList;     // 删除气泡id
    private List<Integer> micRemoveList;     // 删除麦位框id
    private List<Integer> joinRemoveList;     // 删除坐骑id
    private List<Integer> screenRemoveList;     // 删除浮萍id
    private List<Integer> enterEffectRemoveList;     // 删除入场通知id

    public static class SourceInfo {
        private Integer sourceId;
        private Integer autoWear;

        public Integer getSourceId() {
            return sourceId;
        }

        public void setSourceId(Integer sourceId) {
            this.sourceId = sourceId;
        }

        public Integer getAutoWear() {
            return autoWear;
        }

        public void setAutoWear(Integer autoWear) {
            this.autoWear = autoWear;
        }
    }

    public ObjectId get_id() {
        return _id;
    }

    public void set_id(ObjectId _id) {
        this._id = _id;
    }

    public int getVipLevel() {
        return vipLevel;
    }

    public void setVipLevel(int vipLevel) {
        this.vipLevel = vipLevel;
    }

    public int getInvisible() {
        return invisible;
    }

    public void setInvisible(int invisible) {
        this.invisible = invisible;
    }

    public List<SourceInfo> getBubbleList() {
        return bubbleList;
    }

    public void setBubbleList(List<SourceInfo> bubbleList) {
        this.bubbleList = bubbleList;
    }

    public List<SourceInfo> getMicList() {
        return micList;
    }

    public void setMicList(List<SourceInfo> micList) {
        this.micList = micList;
    }

    public List<SourceInfo> getJoinList() {
        return joinList;
    }

    public void setJoinList(List<SourceInfo> joinList) {
        this.joinList = joinList;
    }

    public List<SourceInfo> getScreenList() {
        return screenList;
    }

    public void setScreenList(List<SourceInfo> screenList) {
        this.screenList = screenList;
    }

    public List<Integer> getBubbleRemoveList() {
        return bubbleRemoveList;
    }

    public void setBubbleRemoveList(List<Integer> bubbleRemoveList) {
        this.bubbleRemoveList = bubbleRemoveList;
    }

    public List<Integer> getMicRemoveList() {
        return micRemoveList;
    }

    public void setMicRemoveList(List<Integer> micRemoveList) {
        this.micRemoveList = micRemoveList;
    }

    public List<Integer> getJoinRemoveList() {
        return joinRemoveList;
    }

    public void setJoinRemoveList(List<Integer> joinRemoveList) {
        this.joinRemoveList = joinRemoveList;
    }

    public List<Integer> getScreenRemoveList() {
        return screenRemoveList;
    }

    public void setScreenRemoveList(List<Integer> screenRemoveList) {
        this.screenRemoveList = screenRemoveList;
    }

    public List<SourceInfo> getEnterEffectList() {
        return enterEffectList;
    }

    public void setEnterEffectList(List<SourceInfo> enterEffectList) {
        this.enterEffectList = enterEffectList;
    }

    public List<Integer> getEnterEffectRemoveList() {
        return enterEffectRemoveList;
    }

    public void setEnterEffectRemoveList(List<Integer> enterEffectRemoveList) {
        this.enterEffectRemoveList = enterEffectRemoveList;
    }
}
