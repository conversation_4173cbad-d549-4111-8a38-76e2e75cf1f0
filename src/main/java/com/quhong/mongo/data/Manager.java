package com.quhong.mongo.data;

import org.bson.types.ObjectId;
import org.springframework.data.mongodb.core.mapping.Document;

import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2020/6/11
 */
@Document(collection = "manager")
public class Manager {

    // 账号 必填
    private ObjectId _id;
    private String account = "";
    // 密码 必填
    private String pwd = "";
    private String icon = "";
    private String name = "";
    private Integer role = 0;
    private String ip = "";
    // 公会管理角色 1运营负责人 2超管
    private Integer family_manage_role = 0;
    // 所属上级uid
    private String superior_uid = "";
    private Integer ctime = 0;
    private Integer show_chat = 0;
    // 公会管理员账户有app内部id
    private String service_uid; // app内部id(客服id)
    private int roleId; // 角色id
    private int game_control;
    private int live_audit; // 直播审核权限
    private List<String> functionList; // 拥有权限的功能
    private List<String> pageList; // 拥有可见权限的页面

    public ObjectId get_id() {
        return _id;
    }

    public void set_id(ObjectId _id) {
        this._id = _id;
    }

    public String getAccount() {
        return account;
    }

    public void setAccount(String account) {
        this.account = account;
    }

    public String getPwd() {
        return pwd;
    }

    public void setPwd(String pwd) {
        this.pwd = pwd;
    }

    public String getIcon() {
        return icon;
    }

    public void setIcon(String icon) {
        this.icon = icon;
    }

    public Integer getRole() {
        return role;
    }

    public void setRole(Integer role) {
        this.role = role;
    }

    public String getIp() {
        return ip;
    }

    public void setIp(String ip) {
        this.ip = ip;
    }

    public Integer getCtime() {
        return ctime;
    }

    public void setCtime(Integer ctime) {
        this.ctime = ctime;
    }

    public Integer getFamily_manage_role() {
        return family_manage_role;
    }

    public void setFamily_manage_role(Integer family_manage_role) {
        this.family_manage_role = family_manage_role;
    }

    public String getSuperior_uid() {
        return superior_uid;
    }

    public void setSuperior_uid(String superior_uid) {
        this.superior_uid = superior_uid;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public Integer getShow_chat() {
        return show_chat;
    }

    public void setShow_chat(Integer show_chat) {
        this.show_chat = show_chat;
    }

    public String getService_uid() {
        return service_uid;
    }

    public void setService_uid(String service_uid) {
        this.service_uid = service_uid;
    }

    public int getRoleId() {
        return roleId;
    }

    public void setRoleId(int roleId) {
        this.roleId = roleId;
    }

    public int getGame_control() {
        return game_control;
    }

    public void setGame_control(int game_control) {
        this.game_control = game_control;
    }

    public int getLive_audit() {
        return live_audit;
    }

    public void setLive_audit(int live_audit) {
        this.live_audit = live_audit;
    }

    public List<String> getFunctionList() {
        return functionList;
    }

    public void setFunctionList(List<String> functionList) {
        this.functionList = functionList;
    }

    public List<String> getPageList() {
        return pageList;
    }

    public void setPageList(List<String> pageList) {
        this.pageList = pageList;
    }
}
