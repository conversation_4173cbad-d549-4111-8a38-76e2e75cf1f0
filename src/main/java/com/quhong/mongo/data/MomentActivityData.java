package com.quhong.mongo.data;

import org.bson.types.ObjectId;
import org.springframework.data.annotation.Id;
import org.springframework.data.mongodb.core.mapping.Document;

import java.util.List;

/**
 * 朋友圈
 */
@Document(collection = "moment")
public class MomentActivityData {

    @Id
    private ObjectId _id;
    private String uid; // 用户id
    private String text; // 内容
    private List<Image> imgs;  // 图片信息
    private int show = 1;  // 浏览权限  1 公开，2 朋友可见，3 仅自己可见
    private int comments;  // 评论数
    private int reports;  // 举报数
    private int repost;  // 转发数量
    private List<String> likes;  // 点赞列表，存aid
    private Quote quote; // 引用对象，链接、转发及分享等
    private String location;  // 地理位置
    private int c_time;  // 发布时间


    public static class Image {
        private String origin;
        private String thumbnail;
        private Integer safe;
        private String width;
        private String height;

        public String getOrigin() {
            return origin;
        }

        public void setOrigin(String origin) {
            this.origin = origin;
        }

        public String getThumbnail() {
            return thumbnail;
        }

        public void setThumbnail(String thumbnail) {
            this.thumbnail = thumbnail;
        }

        public Integer getSafe() {
            return safe;
        }

        public void setSafe(Integer safe) {
            this.safe = safe;
        }

        public String getWidth() {
            return width;
        }

        public void setWidth(String width) {
            this.width = width;
        }

        public String getHeight() {
            return height;
        }

        public void setHeight(String height) {
            this.height = height;
        }
    }

    public static class Quote {
        private int type; // 1转发动态 2分享链接 3YouTube链接 4官方链接(需要拼接uid&token) 5分享房间
        private String content; // 引用内容，原博内容或网页title
        private String icon; // 引用的图标，原博主头像、原博第一个图片、网站图标等
        private String action; // type=1为mid、type=2为普通link、type=3为YouTube链接
        private String videoId; // 视频id，YouTube才有值

        public String getContent() {
            return content;
        }

        public void setContent(String content) {
            this.content = content;
        }

        public String getIcon() {
            return icon;
        }

        public void setIcon(String icon) {
            this.icon = icon;
        }

        public String getAction() {
            return action;
        }

        public void setAction(String action) {
            this.action = action;
        }

        public int getType() {
            return type;
        }

        public void setType(int type) {
            this.type = type;
        }

        public String getVideoId() {
            return videoId;
        }

        public void setVideoId(String videoId) {
            this.videoId = videoId;
        }
    }

    public ObjectId get_id() {
        return _id;
    }

    public void set_id(ObjectId _id) {
        this._id = _id;
    }

    public String getUid() {
        return uid;
    }

    public void setUid(String uid) {
        this.uid = uid;
    }

    public String getText() {
        return text;
    }

    public void setText(String text) {
        this.text = text;
    }

    public List<Image> getImgs() {
        return imgs;
    }

    public void setImgs(List<Image> imgs) {
        this.imgs = imgs;
    }

    public int getShow() {
        return show;
    }

    public void setShow(int show) {
        this.show = show;
    }

    public int getComments() {
        return comments;
    }

    public void setComments(int comments) {
        this.comments = comments;
    }

    public int getReports() {
        return reports;
    }

    public void setReports(int reports) {
        this.reports = reports;
    }

    public int getRepost() {
        return repost;
    }

    public void setRepost(int repost) {
        this.repost = repost;
    }

    public List<String> getLikes() {
        return likes;
    }

    public void setLikes(List<String> likes) {
        this.likes = likes;
    }

    public Quote getQuote() {
        return quote;
    }

    public void setQuote(Quote quote) {
        this.quote = quote;
    }

    public String getLocation() {
        return location;
    }

    public void setLocation(String location) {
        this.location = location;
    }

    public int getC_time() {
        return c_time;
    }

    public void setC_time(int c_time) {
        this.c_time = c_time;
    }
}
