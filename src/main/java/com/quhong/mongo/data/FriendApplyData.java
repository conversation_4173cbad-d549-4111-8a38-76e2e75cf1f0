package com.quhong.mongo.data;

import com.quhong.mongo.dao.FriendApplyDao;
import org.bson.types.ObjectId;
import org.springframework.data.annotation.Id;
import org.springframework.data.mongodb.core.mapping.Document;

@Document(collection = FriendApplyDao.TABLE_NAME)
public class FriendApplyData {
    @Id
    private ObjectId _id;
    private String uid; // 申请人
    private String aid; //
    private int opt_type; //0 没处理, 1 已处理
    private int is_new; // 0 否, 1 新的申请,
    private int ctime;
    private String msg;

    public FriendApplyData() {

    }

    public ObjectId get_id() {
        return _id;
    }

    public void set_id(ObjectId _id) {
        this._id = _id;
    }

    public String getUid() {
        return uid;
    }

    public void setUid(String uid) {
        this.uid = uid;
    }

    public String getAid() {
        return aid;
    }

    public void setAid(String aid) {
        this.aid = aid;
    }

    public int getOpt_type() {
        return opt_type;
    }

    public void setOpt_type(int opt_type) {
        this.opt_type = opt_type;
    }

    public int getIs_new() {
        return is_new;
    }

    public void setIs_new(int is_new) {
        this.is_new = is_new;
    }

    public int getCtime() {
        return ctime;
    }

    public void setCtime(int ctime) {
        this.ctime = ctime;
    }

    public String getMsg() {
        return msg;
    }

    public void setMsg(String msg) {
        this.msg = msg;
    }
}
