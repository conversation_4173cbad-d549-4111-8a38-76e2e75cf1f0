package com.quhong.mongo.data;

import com.quhong.mongo.dao.ChangeRoomCountryLogDao;
import org.bson.types.ObjectId;
import org.springframework.data.annotation.Id;
import org.springframework.data.mongodb.core.mapping.Document;

/**
 * <AUTHOR>
 * @date 2024/10/31
 */
@Document(collection = ChangeRoomCountryLogDao.TABLE_NAME)
public class ChangeRoomCountryLog {

    @Id
    private ObjectId _id;
    private String uid;
    private String beforeCountry;
    private String afterCountry;
    private String optUid;
    private Integer mtime;
    private Integer ctime;

    public ChangeRoomCountryLog() {
    }

    public ChangeRoomCountryLog(String uid, String beforeCountry, String afterCountry, String optUid, Integer mtime) {
        this.uid = uid;
        this.beforeCountry = beforeCountry;
        this.afterCountry = afterCountry;
        this.optUid = optUid;
        this.mtime = mtime;
        this.ctime = mtime;
    }

    public ObjectId get_id() {
        return _id;
    }

    public void set_id(ObjectId _id) {
        this._id = _id;
    }

    public String getUid() {
        return uid;
    }

    public void setUid(String uid) {
        this.uid = uid;
    }

    public String getBeforeCountry() {
        return beforeCountry;
    }

    public void setBeforeCountry(String beforeCountry) {
        this.beforeCountry = beforeCountry;
    }

    public String getAfterCountry() {
        return afterCountry;
    }

    public void setAfterCountry(String afterCountry) {
        this.afterCountry = afterCountry;
    }

    public String getOptUid() {
        return optUid;
    }

    public void setOptUid(String optUid) {
        this.optUid = optUid;
    }

    public Integer getMtime() {
        return mtime;
    }

    public void setMtime(Integer mtime) {
        this.mtime = mtime;
    }

    public Integer getCtime() {
        return ctime;
    }

    public void setCtime(Integer ctime) {
        this.ctime = ctime;
    }
}
