package com.quhong.mongo.dao;

import com.quhong.config.CaffeineCacheConfig;
import com.quhong.mongo.config.MongoBean;
import com.quhong.mongo.data.AchievementConfigData;
import jakarta.annotation.Resource;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.data.mongodb.core.query.Criteria;
import org.springframework.data.mongodb.core.query.Query;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2024/2/21
 */
@Component
public class AchievementConfigDao {

    private final Logger logger = LoggerFactory.getLogger(getClass());

    public static final String TABLE_NAME = "achievement_config";

    @Resource(name = MongoBean.MOVIES)
    private MongoTemplate mongoTemplate;

    @Cacheable(value = "cache", cacheManager = CaffeineCacheConfig.EXPIRE_10M_AFTER_WRITE,
            key = "#root.method.name+T(org.springframework.util.StringUtils).arrayToDelimitedString(args,'-')")
    public List<AchievementConfigData> selectAllList() {
        return mongoTemplate.findAll(AchievementConfigData.class);
    }

    @Cacheable(value = "cache", cacheManager = CaffeineCacheConfig.EXPIRE_10M_AFTER_WRITE,
            key = "#root.method.name+T(org.springframework.util.StringUtils).arrayToDelimitedString(args,'-')")
    public AchievementConfigData getDataFromCache(String key) {
        try {
            Criteria criteria = Criteria.where("key").is(key);
            return mongoTemplate.findOne(new Query(criteria), AchievementConfigData.class);
        } catch (Exception e) {
            logger.error("getDataFromCache error. key={} {}", key, e.getMessage(), e);
            return null;
        }
    }
}
