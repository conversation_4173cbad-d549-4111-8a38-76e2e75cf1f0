package com.quhong.mongo.dao;

import com.alibaba.fastjson.JSONObject;
import com.mongodb.client.FindIterable;
import com.mongodb.client.MongoCollection;
import com.quhong.mongo.config.MongoBean;
import com.quhong.mongo.data.SignTableData;
import org.bson.BsonDocument;
import org.bson.BsonString;
import org.bson.Document;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.stereotype.Component;

import jakarta.annotation.Resource;
import java.util.Map;
import java.util.stream.Collectors;

@Component
public class SignTableDao {
    private static final Logger logger = LoggerFactory.getLogger(SignTableDao.class);
    public static final String TABLE_NAME = "sign_table";

    @Resource(name = MongoBean.MOVIES)
    private MongoTemplate mongoTemplate;

    public SignTableData getByUid(String uid) {
        try {
            MongoCollection<Document> collection = mongoTemplate.getDb().getCollection(TABLE_NAME);
            BsonDocument filter = new BsonDocument("_id", new BsonString(uid));
            FindIterable<Document> documents = collection.find(filter);
            Document document = documents.first();
            if (null == document) {
                return null;
            }
            Map<String, Object> map = document.entrySet().stream()
                    .collect(Collectors.toMap(Map.Entry::getKey, Map.Entry::getValue, (k1, k2) -> k2));
            return new JSONObject(map).toJavaObject(SignTableData.class);
        } catch (Exception e) {
            logger.error("get sign table error uid={} {}", uid, e.getMessage(), e);
            return null;
        }
    }
}
