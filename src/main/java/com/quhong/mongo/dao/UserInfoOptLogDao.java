package com.quhong.mongo.dao;

import com.quhong.mongo.config.MongoBean;
import com.quhong.mongo.data.UserInfoOptLogData;
import com.quhong.mongo.data.UserInfoOptLogData;
import jakarta.annotation.Resource;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.data.domain.Sort;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.data.mongodb.core.aggregation.Aggregation;
import org.springframework.data.mongodb.core.query.Criteria;
import org.springframework.data.mongodb.core.query.Query;
import org.springframework.stereotype.Component;
import org.springframework.util.StringUtils;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2024/12/20
 */
@Component
public class UserInfoOptLogDao {

    private static final Logger logger = LoggerFactory.getLogger(UserInfoOptLogDao.class);

    public static final String TABLE_NAME = "user_info_opt_log";

    @Resource(name = MongoBean.MOVIES)
    private MongoTemplate mongoTemplate;

    public void save(UserInfoOptLogData data) {
        try {
            mongoTemplate.save(data);
        } catch (Exception e) {
            logger.error("saveUserInfoOptLogData error. {}", e.getMessage(), e);
        }
    }

    public int selectCount(String aid, String optUid, int type, String optField) {
        try {
            return (int) mongoTemplate.count(new Query(buildCriteria(aid, optUid, type, optField)), UserInfoOptLogData.class);
        } catch (Exception e) {
            logger.error("selectCount error. aid={} optUid={} type={} optField={} {}", aid, optUid, type, optField, e.getMessage(), e);
            return 0;
        }
    }

    public List<UserInfoOptLogData> selectList(String uid, String optUid, int type, String optField, int start, int pageSize) {
        try {
            Aggregation aggregation = Aggregation.newAggregation(
                    Aggregation.match(buildCriteria(uid, optUid, type, optField)),
                    // 排序
                    Aggregation.sort(Sort.by(Sort.Direction.DESC, "ctime")),
                    // 分页
                    Aggregation.skip(start),
                    Aggregation.limit(pageSize)
            );
            return mongoTemplate.aggregate(aggregation, TABLE_NAME, UserInfoOptLogData.class).getMappedResults();
        } catch (Exception e) {
            logger.error("selectPage error. uid={} optUid={} type={} optField={} {}", uid, optUid, type, optField, e.getMessage(), e);
            return null;
        }
    }

    private Criteria buildCriteria(String aid, String optUid, int type, String optField) {
        Criteria criteria = new Criteria();
        if (StringUtils.hasLength(aid)) {
            criteria.and("aid").is(aid);
        }
        if (StringUtils.hasLength(optUid)) {
            criteria.and("optUid").is(optUid);
        }
        if (type != -1) {
            criteria.and("type").is(type);
        }
        if (StringUtils.hasLength(optField)) {
            criteria.and("optField").is(optField);
        }
        return criteria;
    }
}
