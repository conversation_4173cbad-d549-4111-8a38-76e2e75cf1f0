package com.quhong.tickers;

import com.quhong.config.AsyncConfig;
import com.quhong.core.config.ServerConfig;
import com.quhong.core.utils.DateHelper;
import com.quhong.data.ActorData;
import com.quhong.mongo.dao.ActorDao;
import com.quhong.mysql.dao.FamilyDao;
import com.quhong.mysql.dao.FamilyMemberDao;
import com.quhong.mysql.dao.InviteRewardRecordDao;
import com.quhong.mysql.dao.InvitedUserDao;
import com.quhong.mysql.data.FamilyData;
import com.quhong.mysql.data.FamilyMemberData;
import com.quhong.mysql.data.InvitedUserData;
import com.quhong.service.InviteUserService;
import com.quhong.utils.AsyncUtils;
import com.quhong.utils.K8sUtils;
import jakarta.annotation.PostConstruct;
import jakarta.annotation.Resource;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.scheduling.annotation.Async;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import java.util.Map;
import java.util.concurrent.TimeUnit;

@Component
public class InviteRewardTicker {

    private final static Logger logger = LoggerFactory.getLogger(InviteRewardTicker.class);

    @Resource
    private InviteUserService inviteUserService;
    @Resource
    private FamilyMemberDao familyMemberDao;
    @Resource
    private FamilyDao familyDao;
    @Resource
    private InvitedUserDao invitedUserDao;
    @Resource
    private ActorDao actorDao;
    @Resource
    private InviteRewardRecordDao inviteRewardRecordDao;
    @Resource
    private K8sUtils k8sUtils;

    /**
     * utc+3周日最后一秒
     */
    @Async(AsyncConfig.ASYNC_TASK)
    @Scheduled(cron = "59 59 20 ? * SUN")
    public void sendReward() {
        if (!k8sUtils.isMaster()) {
            return;
        }
        long millis = System.currentTimeMillis();
        logger.info("start send invite reward.");
        inviteUserService.sendReward();
        logger.info("finish send invite reward. cost={}", System.currentTimeMillis() - millis);
    }

    // @Async(AsyncConfig.ASYNC_TASK)
    // @Scheduled(cron = "0 0 0/1 * * ?")
    public void sendRewardTest() {
        if (ServerConfig.isProduct()) {
            return;
        }
        if (!k8sUtils.isMaster()) {
            return;
        }
        long millis = System.currentTimeMillis();
        logger.info("start send invite reward.");
        inviteUserService.sendReward();
        logger.info("finish send invite reward. cost={}", System.currentTimeMillis() - millis);
    }

    public void initInviteUser() {
        AsyncUtils.execute(() -> {
            long millis = System.currentTimeMillis();
            logger.info("start initInviteUser.");
            Map<String, Integer> memberMap = familyMemberDao.selectAllMember();
            for (Map.Entry<String, Integer> entry : memberMap.entrySet()) {
                if (invitedUserDao.selectByUid(entry.getKey()) != null) {
                    continue;
                }
                FamilyMemberData memberData = familyMemberDao.selectByFamilyIdAndUid(entry.getValue(), entry.getKey());
                if (memberData == null) {
                    continue;
                }
                FamilyData familyData = familyDao.selectById(entry.getValue());
                if (familyData == null || familyData.getStatus() != 1) {
                    continue;
                }
                if (memberData.getUid().equals(familyData.getOwnerUid())) {
                    continue;
                }
                ActorData actorData = actorDao.getActorDataFromCache(memberData.getUid());
                if (actorData == null) {
                    continue;
                }
                InvitedUserData invitedUserData = new InvitedUserData();
                invitedUserData.setUid(memberData.getUid());
                invitedUserData.setAid(familyData.getOwnerUid());
                invitedUserData.setStatus(1);
                invitedUserData.setIp(actorData.getIp());
                invitedUserData.setTnId(actorData.getTn_id());
                invitedUserData.setOldUser(1);
                invitedUserData.setCtime(DateHelper.getNowSeconds());
                invitedUserDao.insert(invitedUserData);
            }
            logger.info("finish initInviteUser. cost={}", System.currentTimeMillis() - millis);
        });
    }

    // @PostConstruct
    public void initInviteRanking() {
        AsyncUtils.execute(() -> {
            long millis = System.currentTimeMillis();
            logger.info("start initInviteRanking.");
            int endTime = inviteUserService.getWeekStartTime();
            int startTime = endTime - (int) TimeUnit.DAYS.toSeconds(7);
            Map<String, Integer> rewardMap = inviteRewardRecordDao.getRewardMap(0, startTime, endTime);
            Map<String, Integer> rechargeRewardMap = inviteRewardRecordDao.getRewardMap(2, startTime, endTime);
            if (!CollectionUtils.isEmpty(rechargeRewardMap)) {
                rechargeRewardMap.forEach((k, v) -> rewardMap.put(k, rewardMap.getOrDefault(k, 0) + v));
            }
            inviteRewardRecordDao.updateRanking(rewardMap);
            logger.info("finish initInviteRanking. cost={}", System.currentTimeMillis() - millis);
        });
    }
}
