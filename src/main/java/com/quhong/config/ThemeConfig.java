package com.quhong.config;

import com.quhong.core.config.ServerConfig;
import com.quhong.data.ThemeData;
import com.quhong.data.VersionBeanData;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.PropertySource;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Component
@PropertySource(value = "classpath:room_config.yml", encoding = "UTF-8", factory = YamlPropertySourceFactory.class)
@ConfigurationProperties(prefix = "theme")
public class ThemeConfig {

    private List<ThemeData> list = new ArrayList<>();
    private List<ThemeData> testList = new ArrayList<>();
    private Map<Integer, VersionBeanData> vMap;

    public List<ThemeData> getList() {
        return ServerConfig.isProduct() ? list : testList;
    }

    public void setList(List<ThemeData> list) {
        this.list = list;
    }

    public List<ThemeData> getTestList() {
        return testList;
    }

    public void setTestList(List<ThemeData> testList) {
        this.testList = testList;
    }

    public Map<Integer, VersionBeanData> getVMap() {
        return vMap;
    }

    public void setVMap(Map<Integer, VersionBeanData> vMap) {
        if (CollectionUtils.isEmpty(vMap)) {
            vMap = new HashMap<>();
        }
        this.vMap = vMap;
    }

    public Map<Integer, VersionBeanData> getvMap() {
        return vMap;
    }

    public void setvMap(Map<Integer, VersionBeanData> vMap) {
        this.vMap = vMap;
    }
}
