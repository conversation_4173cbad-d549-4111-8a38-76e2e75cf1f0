package com.quhong.config;

import com.quhong.filter.AbstractFilterConfig;
import org.springframework.context.annotation.Configuration;

import java.util.Arrays;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 拦截内容
 */
@Configuration
public class ActivityInterceptorConfig extends AbstractFilterConfig {
    @Override
    protected Map<String, Long> getRequestWarnDurationMap() {
        Map<String, Long> map = new HashMap<>();
        map.put("/activity/uploadBaseEncode", 16000L);
        map.put("/activity/uploadFileOSS", 16000L);
        map.put("/activity/painterPicture", 6000L);
        return map;
    }

    @Override
    public List<String> getAnonymousPath() {
        return Arrays.asList();
    }

    @Override
    protected List<String> getExcludePaths() {
        return Arrays.asList("/shareRoom/**",
                "/shareLiveRoom/**",
                "/shareUser/**",
                "/shareActivity/**",
                "/share/**",
                "/poster",
                baseUrl + "feedback/**",
                baseUrl + "userReport",
                baseUrl + "uploadFileOSS");
    }
}
