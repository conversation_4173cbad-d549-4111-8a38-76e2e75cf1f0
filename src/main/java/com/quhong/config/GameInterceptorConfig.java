package com.quhong.config;

import com.quhong.filter.AbstractFilterConfig;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Configuration;

import java.util.Arrays;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 拦截内容
 */
@Configuration
public class GameInterceptorConfig extends AbstractFilterConfig {

    @Value("${baseUrl}")
    private String baseUrl;

    @Override
    protected List<String> getExcludePaths() {
        return Arrays.asList(baseUrl + "sud_game/**", baseUrl + "yaXun/**"
                , baseUrl + "bai_shun_game/get_sstoken"
                , baseUrl + "bai_shun_game/update_sstoken"
                , baseUrl + "bai_shun_game/get_user_info"
                , baseUrl + "bai_shun_game/change_balance"
                , baseUrl + "quhong/get_token"
                , baseUrl + "quhong/get_player_info"
                , baseUrl + "quhong/change_balance"
                , baseUrl + "quhong/get_robot");
    }

    protected Map<String, Long> getRequestWarnDurationMap() {
        Map<String, Long> map = new HashMap<>();
        map.put("/game/sud/start_game", 5000L);
        map.put("/game/box/check", 5000L);
        return map;
    }
}
