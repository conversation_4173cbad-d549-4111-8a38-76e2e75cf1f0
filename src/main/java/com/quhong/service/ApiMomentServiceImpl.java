package com.quhong.service;

import com.quhong.analysis.EventReport;
import com.quhong.analysis.MomentGiftEvent;
import com.quhong.api.ApiMomentService;
import com.quhong.config.AsyncConfig;
import com.quhong.constant.MomentConstant;
import com.quhong.core.utils.DateHelper;
import com.quhong.data.dto.GiftRewardDTO;
import com.quhong.exception.CommonException;
import com.quhong.mongo.dao.MomentDao;
import com.quhong.mongo.data.MomentData;
import com.quhong.mongo.data.MomentNotice;
import com.quhong.mysql.data.MomentRewardData;
import jakarta.annotation.Resource;
import org.apache.dubbo.config.annotation.DubboService;

import java.util.concurrent.CompletableFuture;
import java.util.concurrent.Executor;

@DubboService(timeout = 3000)
public class ApiMomentServiceImpl implements ApiMomentService {

    @Resource
    private MomentRewardService momentRewardService;
    @Resource
    private MomentNoticeService momentNoticeService;
    @Resource
    private MomentService momentService;
    @Resource
    private EventReport eventReport;
    @Resource
    private MomentDao momentDao;
    @Resource(name = AsyncConfig.ASYNC_TASK)
    private Executor executor;

    @Override
    public CompletableFuture<Void> giftReward(GiftRewardDTO req) throws CommonException {
        return CompletableFuture.runAsync(() -> {
            MomentData moment = momentService.getMoment(req.getMid());
            momentDao.incrMomentGifted(req.getMid(), req.getRewardPrice());
            momentRewardService.saveMomentReward(new MomentRewardData(req.getMid(), req.getUid(), req.getGiftId(), req.getGiftIcon(), req.getRewardNum()));
            // 通知博主
            MomentNotice momentNotice = new MomentNotice(moment.getUid(), req.getUid(),
                    MomentConstant.NOTICE_MOMENT_REWARD, req.getMid(), null, moment.getText(), null);
            momentService.fillMomentNotice(moment, momentNotice);
            momentNotice.setGiftReward(new MomentNotice.GiftReward(req.getRewardNum(), req.getGiftIcon()));
            momentNoticeService.addNotice(moment.getUid(), momentNotice, moment.getUid());
            // 埋点
            MomentGiftEvent event = new MomentGiftEvent();
            event.setUid(req.getUid());
            event.setMoment_id(req.getMid());
            event.setFrom_uid(req.getUid());
            event.setTo_uid(moment.getUid());
            event.setGift_id(req.getGiftId());
            event.setGift_number(req.getRewardNum());
            event.setGift_price(req.getGiftPrice());
            event.setGift_price_type(req.getPriceType());
            event.setEarn_beans(req.getGiftPrice() * 3 / 10);
            event.setCtime(DateHelper.getNowSeconds());
            eventReport.track(new com.quhong.analysis.EventDTO(event));
        }, executor);
    }
}
