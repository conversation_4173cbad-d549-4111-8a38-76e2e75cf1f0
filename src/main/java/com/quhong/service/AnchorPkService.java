package com.quhong.service;

import com.google.common.collect.Interner;
import com.google.common.collect.Interners;
import com.quhong.analysis.EventDTO;
import com.quhong.analysis.PkActivityRecordEvent;
import com.quhong.constant.MoneyActionType;
import com.quhong.core.config.ServerConfig;
import com.quhong.core.date.DateSupport;
import com.quhong.core.utils.DateHelper;
import com.quhong.data.ActorData;
import com.quhong.data.SendGiftData;
import com.quhong.data.vo.AnchorPkVO;
import com.quhong.data.vo.OtherMyRankVO;
import com.quhong.data.vo.OtherRankingListVO;
import com.quhong.enums.HttpCode;
import com.quhong.exception.CommonH5Exception;
import com.quhong.image.ImageUrlGenerator;
import com.quhong.mongo.data.OtherRankingActivityData;
import com.quhong.mongo.data.ResourceKeyConfigData;
import com.quhong.mysql.dao.FamilyMemberDao;
import com.quhong.redis.AnchorPkRedis;
import com.quhong.utils.AsyncUtils;
import jakarta.annotation.PostConstruct;
import jakarta.annotation.Resource;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import java.time.LocalDate;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.concurrent.TimeUnit;

/**
 * 主播pk赛
 *
 * <AUTHOR>
 * @date 2025/2/14
 */
@Service
public class AnchorPkService extends OtherActivityService implements DailyTaskHandler {

    private static final Logger logger = LoggerFactory.getLogger(AnchorPkService.class);

    private static final String ACTIVITY_ID = ServerConfig.isProduct() ? "67b84b9b3f0e000036001298" : "67b82d8e3f0e000036001295";

    private static final String RECEIVE_GIFT_BEANS = "receive_gift_beans_%s";
    private static final int PK_LIMIT = 10;
    private static final double POOL_BEAN_RATE = 0.1;
    private static final String PK_HALL = "pk_hall_%s";
    private static final String PK_TOTAL_RANKING = "pk_total_ranking";
    private static final String GUARDIAN_RANKING = "guardian_ranking_%s_%s";
    private static final String PK_START_TIME = "pk_start_time_%s";
    private static final int PAGE_SIZE = 8;
    private static final String RANKING_REWARD_KEY = "anchorPkTop%s";
    private static final String GUARDIAN_REWARD_TITLE = "Host PK-daily pk top1 reward";
    private static final String RANKING_REWARD_TITLE = "Host PK-total rank reward";

    private final Interner<String> stringPool = Interners.newWeakInterner();

    @Resource
    private AnchorPkRedis anchorPkRedis;
    @Resource
    private FamilyMemberDao familyMemberDao;

    public AnchorPkVO getInfo(String activityId, String uid) {
        ActorData actorData = actorDao.getActorDataFromCache(uid);
        if (actorData == null) {
            throw new CommonH5Exception(HttpCode.PARAM_ERROR);
        }
        OtherRankingActivityData activityData = getActivityData(activityId);
        if (activityData == null) {
            throw new CommonH5Exception(HttpCode.PARAM_ERROR);
        }
        AnchorPkVO vo = new AnchorPkVO();
        vo.setStartTime(activityData.getStartTime());
        vo.setEndTime(activityData.getEndTime());
        Map<String, Long> rankingMap = anchorPkRedis.getRankingMap(activityId, PK_TOTAL_RANKING, 10);
        vo.setRankingList(getRankingList(rankingMap));
        vo.setMyRank(getMyRanking(activityId, actorData));
        vo.setDailyDataList(getDailyDataList(activityId, uid, activityData.getStartTime(), activityData.getEndTime()));
        vo.setAnchor(familyMemberDao.selectByUidFromCache(uid) != null);
        vo.setHotRoomId(getPopularRoomId());
        return vo;
    }

    public AnchorPkVO getPkHall(String activityId, String strDate, int page) {
        int start = (page - 1) * PAGE_SIZE;
        int end = page * PAGE_SIZE - 1;
        Map<String, Long> rankingMap = anchorPkRedis.getRankingMap(activityId, PK_HALL.formatted(strDate), start, end);
        List<AnchorPkVO.PkInfo> list = new ArrayList<>();
        if (!CollectionUtils.isEmpty(rankingMap)) {
            for (Map.Entry<String, Long> entry : rankingMap.entrySet()) {
                String[] aids = entry.getKey().split("_");
                String redAid = aids[0];
                String blueAid = aids[1];
                AnchorPkVO.PkUserInfo redUserInfo = getPkUserInfo(activityId, strDate, redAid);
                AnchorPkVO.PkUserInfo blueUserInfo = getPkUserInfo(activityId, strDate, blueAid);
                int poolBeans = (int) ((redUserInfo.getBeans() + blueUserInfo.getBeans()) * POOL_BEAN_RATE);
                int winnerPool = (int) ((redUserInfo.getBeans() + blueUserInfo.getBeans()) * 0.09);
                int loserPool;
                if (redUserInfo.getBeans() >= blueUserInfo.getBeans()) {
                    redUserInfo.setWinner(true);
                    loserPool = (int) (blueUserInfo.getBeans() * 0.01);
                } else {
                    blueUserInfo.setWinner(true);
                    loserPool = (int) (redUserInfo.getBeans() * 0.01);
                }
                list.add(new AnchorPkVO.PkInfo(redUserInfo, blueUserInfo, poolBeans, winnerPool, loserPool));
            }
        }
        return new AnchorPkVO(list, list.size() >= PAGE_SIZE ? String.valueOf(page + 1) : "");
    }

    public void sendGiftHandle(SendGiftData giftData, String activityId) {
        Map<String, Integer> member = familyMemberDao.selectAllMember();
        for (String aid : giftData.getAid_list()) {
            if (!member.containsKey(aid)) {
                // 非主播不参与活动
                continue;
            }
            int receiveBeans = giftData.getNumber() * giftData.getPrice();
            synchronized (stringPool.intern(aid)) {
                String today = DateHelper.ARABIAN.formatDateInDay();
                long afterValue = anchorPkRedis.incUserData(activityId, aid, RECEIVE_GIFT_BEANS.formatted(today), receiveBeans);
                anchorPkRedis.incrRankingScore(activityId, GUARDIAN_RANKING.formatted(today, aid), giftData.getFrom_uid(), receiveBeans);
                String userPkPool = anchorPkRedis.getUserPkPool(activityId, aid);
                if (StringUtils.hasLength(userPkPool)) {
                    anchorPkRedis.incrRankingScore(activityId, PK_HALL.formatted(today), userPkPool, (int) (receiveBeans * POOL_BEAN_RATE));
                } else {
                    if (afterValue >= PK_LIMIT && afterValue - receiveBeans < PK_LIMIT) {
                        // 达到pk门槛匹配对手
                        synchronized (stringPool.intern("pk_match")) {
                            int pkAnchorSize = anchorPkRedis.addPkAnchor(activityId, aid);
                            if (pkAnchorSize % 2 == 0) {
                                // 匹配成功
                                List<String> pkAnchorList = anchorPkRedis.getPkAnchorList(activityId, pkAnchorSize - 2, pkAnchorSize - 1);

                                String pkRankingKey = String.join("_", pkAnchorList);
                                // 计算pk池子钻石数
                                int pkPoolBeans = (int) afterValue;
                                for (String pkAnchor : pkAnchorList) {
                                    if (!pkAnchor.equals(aid)) {
                                        pkPoolBeans += (int) (anchorPkRedis.getUserData(activityId, pkAnchor, RECEIVE_GIFT_BEANS.formatted(today)) * POOL_BEAN_RATE);
                                    }
                                    // 记录主播所属pk池子
                                    anchorPkRedis.addUserPkPool(activityId, pkAnchor, pkRankingKey);
                                    anchorPkRedis.setUserData(activityId, pkAnchor, PK_START_TIME.formatted(today), DateHelper.getNowSeconds());
                                }
                                anchorPkRedis.incrRankingScore(activityId, PK_HALL.formatted(today), pkRankingKey, pkPoolBeans);
                            }
                        }
                    }
                }
            }
        }
    }

    @Override
    public void dailyTaskRun(String dateStr) {
        String yesterday = DateHelper.ARABIAN.getYesterdayStr(new Date());
        sendDailyReward(yesterday);
    }

    // @PostConstruct
    public void testDailyReward() {
        // String yesterday = DateHelper.ARABIAN.getYesterdayStr(new Date());
        AsyncUtils.execute(() -> {
            sendDailyReward(DateHelper.ARABIAN.formatDateInDay());
        });
    }

    public void sendDailyReward(String yesterday) {
        OtherRankingActivityData activityData = otherActivityService.getActivityData(ACTIVITY_ID);
        int nowSeconds = DateHelper.getNowSeconds();
        if (activityData == null || nowSeconds <= activityData.getStartTime() || nowSeconds >= activityData.getEndTime() + (int) TimeUnit.HOURS.toSeconds(1)) {
            return;
        }

        Map<String, Long> rankingMap = anchorPkRedis.getRankingMap(ACTIVITY_ID, PK_HALL.formatted(yesterday), 0);
        if (CollectionUtils.isEmpty(rankingMap)) {
            logger.error("dailyTaskRun error, rankingMap is empty. activityId={} strDate={}", ACTIVITY_ID, yesterday);
            return;
        }
        for (Map.Entry<String, Long> entry : rankingMap.entrySet()) {
            String[] aids = entry.getKey().split("_");
            String anchor1 = aids[0];
            String anchor2 = aids[1];
            long receiveBeans1 = anchorPkRedis.getUserData(ACTIVITY_ID, anchor1, RECEIVE_GIFT_BEANS.formatted(yesterday));
            long receiveBeans2 = anchorPkRedis.getUserData(ACTIVITY_ID, anchor2, RECEIVE_GIFT_BEANS.formatted(yesterday));
            long poolBeans = (long) ((receiveBeans1 + receiveBeans2) * POOL_BEAN_RATE);
            String winner;
            String loser;
            long loserReceiveBeans;
            int result;
            if (receiveBeans1 < receiveBeans2) {
                winner = anchor2;
                loser = anchor1;
                loserReceiveBeans = receiveBeans1;
                result = 2;
            } else {
                winner = anchor1;
                loser = anchor2;
                loserReceiveBeans = receiveBeans2;
                result = 1;
            }
            handlePkResult(ACTIVITY_ID, yesterday, winner, loser, poolBeans, loserReceiveBeans);
            // 榜1大哥奖励
            String guardian1 = getGuardianTop1Uid(ACTIVITY_ID, yesterday, anchor1);
            String guardian2 = getGuardianTop1Uid(ACTIVITY_ID, yesterday, anchor2);
            resourceKeyHandlerService.sendResourceData(guardian1, "anchorGuardianTop1", GUARDIAN_REWARD_TITLE, GUARDIAN_REWARD_TITLE);
            resourceKeyHandlerService.sendResourceData(guardian2, "anchorGuardianTop1", GUARDIAN_REWARD_TITLE, GUARDIAN_REWARD_TITLE);
            // pk结果埋点
            PkActivityRecordEvent event = new PkActivityRecordEvent();
            event.setUid(anchor1);
            event.setActivity_pk_pid(yesterday + "_" + entry.getKey());
            event.setFrom_uid(anchor1);
            event.setFrom_uid_score((int) receiveBeans1);
            event.setFrom_no1_uid(guardian1);
            event.setTo_uid(anchor2);
            event.setTo_uid_score((int) receiveBeans2);
            event.setTo_no1_uid(guardian2);
            event.setWin_uid(winner);
            event.setConfront_start_at((int) anchorPkRedis.getUserData(ACTIVITY_ID, anchor1, PK_START_TIME.formatted(yesterday)));
            event.setConfront_end_at(DateSupport.ARABIAN.getStartAndEndTime(DateSupport.parse(yesterday))[1]);
            event.setResult(result);
            event.setCtime(DateHelper.getNowSeconds());
            eventReport.track(new EventDTO(event));
        }
    }

    /**
     * 发送榜单奖励
     */
    public void sendRankingReward(String activityId) {
        try {
            Map<String, Long> rankingMap = anchorPkRedis.getRankingMap(activityId, PK_TOTAL_RANKING, 10);
            int rank = 1;
            for (Map.Entry<String, Long> entry : rankingMap.entrySet()) {
                String rewardKey = rank <= 3 ? RANKING_REWARD_KEY.formatted(rank) : rank <= 6 ? RANKING_REWARD_KEY.formatted("4-6") : RANKING_REWARD_KEY.formatted("7-10");
                String aid = entry.getKey();
                logger.info("sendRankingReward. aid={} score={} rank={} rewardKey={}", aid, entry.getValue(), rank, rewardKey);
                ResourceKeyConfigData resourceKeyConfigData = resourceKeyHandlerService.getConfigData(rewardKey);
                if (resourceKeyConfigData == null || CollectionUtils.isEmpty(resourceKeyConfigData.getResourceMetaList())) {
                    logger.error("sendResourceData not find; resourceKey={}", rewardKey);
                    return;
                }
                for (ResourceKeyConfigData.ResourceMeta resourceMeta : resourceKeyConfigData.getResourceMetaList()) {
                    resourceKeyHandlerService.sendResource(aid, resourceMeta, MoneyActionType.ACTIVITY_COMPETITION.actionType, RANKING_REWARD_TITLE, RANKING_REWARD_TITLE, RANKING_REWARD_TITLE, 1, activityId);
                }
                rank++;
            }
        } catch (Exception e) {
            logger.error("sendRankingReward error. activityId={} {}", activityId, e.getMessage(), e);
        }
    }

    private List<AnchorPkVO.DailyData> getDailyDataList(String activityId, String uid, int startTime, int endTime) {
        List<AnchorPkVO.DailyData> list = new ArrayList<>();
        LocalDate startDate = DateSupport.ARABIAN.getLocalDate(startTime * 1000L);
        LocalDate endDate = DateSupport.ARABIAN.getLocalDate(endTime * 1000L);
        LocalDate today = DateSupport.ARABIAN.getToday();
        while (!startDate.isAfter(endDate)) {
            String strDate = DateSupport.format(startDate);
            String showDate = DateSupport.ARABIAN.dm(startDate);
            AnchorPkVO.DailyData dailyData = new AnchorPkVO.DailyData();
            dailyData.setStrDate(strDate);
            dailyData.setShowDate(showDate);
            if (startDate.isAfter(today)) {
                // 未开始
                dailyData.setStatus(0);
            } else {
                // 正在进行或已结束
                boolean isToday = startDate.isEqual(today);
                dailyData.setStatus(isToday ? 1 : 2);
                dailyData.setMyPk(getMyPkInfo(activityId, uid, strDate, isToday));
                AnchorPkVO pkHall = getPkHall(activityId, strDate, 1);
                dailyData.setList(pkHall.getList());
                dailyData.setNextUrl(pkHall.getNextUrl());
            }
            list.add(dailyData);
            startDate = startDate.plusDays(1);
        }
        return list;
    }

    private AnchorPkVO.PkInfo getMyPkInfo(String activityId, String uid, String strDate, boolean isToday) {
        long receiveBeans = anchorPkRedis.getUserData(activityId, uid, RECEIVE_GIFT_BEANS.formatted(strDate));
        AnchorPkVO.PkInfo pkInfo = new AnchorPkVO.PkInfo();
        if (receiveBeans < PK_LIMIT) {
            // 未达到门槛
            pkInfo.setResult(1);
            return pkInfo;
        }
        pkInfo.setRedUser(getPkUserInfo(activityId, strDate, uid));
        String userPkPool = anchorPkRedis.getUserPkPool(activityId, uid, strDate);
        if (!StringUtils.hasLength(userPkPool)) {
            // 匹配中或流拍
            pkInfo.setResult(isToday ? 2 : 5);
            pkInfo.setPoolBeans((long) (receiveBeans * POOL_BEAN_RATE));
        } else {
            // 匹配成功
            String[] aids = userPkPool.split("_");
            String blueUid = aids[0].equals(uid) ? aids[1] : aids[0];
            pkInfo.setBlueUser(getPkUserInfo(activityId, strDate, blueUid));
            if (!isToday) {
                if (aids[0].equals(uid)) {
                    pkInfo.setResult(pkInfo.getRedUser().getBeans() >= pkInfo.getBlueUser().getBeans() ? 3 : 4);
                } else {
                    pkInfo.setResult(pkInfo.getRedUser().getBeans() > pkInfo.getBlueUser().getBeans() ? 3 : 4);
                }
            }
            pkInfo.setPoolBeans((long) ((pkInfo.getRedUser().getBeans() + pkInfo.getBlueUser().getBeans()) * POOL_BEAN_RATE));
            int loserPool;
            if (pkInfo.getRedUser().getBeans() >= pkInfo.getBlueUser().getBeans()) {
                loserPool = (int) (pkInfo.getBlueUser().getBeans() * 0.01);
                pkInfo.getRedUser().setWinner(true);
                if (pkInfo.getRedUser().getBeans() == pkInfo.getBlueUser().getBeans() && aids[1].equals(uid)) {
                    pkInfo.getBlueUser().setWinner(true);
                }
            } else {
                pkInfo.getBlueUser().setWinner(true);
                loserPool = (int) (pkInfo.getRedUser().getBeans() * 0.01);
            }
            int winnerPool = (int) ((pkInfo.getRedUser().getBeans() + pkInfo.getBlueUser().getBeans()) * 0.09);
            pkInfo.setWinnerPool(winnerPool);
            pkInfo.setLoserPool(loserPool);
        }
        return pkInfo;
    }

    private AnchorPkVO.PkUserInfo getPkUserInfo(String activityId, String strDate, String uid) {
        ActorData actorData = actorDao.getActorDataFromCache(uid);
        AnchorPkVO.PkUserInfo pkUserInfo = new AnchorPkVO.PkUserInfo();
        pkUserInfo.setName(actorData.getName());
        pkUserInfo.setHead(ImageUrlGenerator.generateRoomUserUrl(actorData.getHead()));
        pkUserInfo.setBeans(anchorPkRedis.getUserData(activityId, uid, RECEIVE_GIFT_BEANS.formatted(strDate)));
        String guardianId = getGuardianTop1Uid(activityId, strDate, uid);
        if (StringUtils.hasLength(guardianId)) {
            ActorData guardianData = actorDao.getActorDataFromCache(guardianId);
            if (guardianData != null) {
                pkUserInfo.setGuardianName(guardianData.getName());
                pkUserInfo.setGuardianHead(ImageUrlGenerator.generateRoomUserUrl(guardianData.getHead()));
            }
        }
        return pkUserInfo;
    }


    private List<OtherRankingListVO> getRankingList(Map<String, Long> rankingMap) {
        List<OtherRankingListVO> list = new ArrayList<>();
        int rank = 1;
        for (Map.Entry<String, Long> entry : rankingMap.entrySet()) {
            OtherRankingListVO vo = new OtherRankingListVO();
            String aid = entry.getKey();
            ActorData rankActor = actorDao.getActorDataFromCache(aid);
            vo.setHead(ImageUrlGenerator.generateRoomUserUrl(rankActor.getHead()));
            vo.setName(rankActor.getName());
            vo.setScore(entry.getValue().intValue());
            vo.setRank(rank);
            list.add(vo);
            rank += 1;
        }
        return list;
    }

    private OtherMyRankVO getMyRanking(String activityId, ActorData actorData) {
        long score = anchorPkRedis.getRankingScore(activityId, PK_TOTAL_RANKING, actorData.getUid());
        int rank = anchorPkRedis.getRankingRank(activityId, PK_TOTAL_RANKING, actorData.getUid());
        OtherMyRankVO vo = new OtherMyRankVO();
        vo.setScore((int)score);
        vo.setRank(rank == 0 || rank > 99 ? -1 : rank);
        vo.setName(actorData.getName());
        vo.setHead(ImageUrlGenerator.generateRoomUserUrl(actorData.getHead()));
        return vo;
    }

    private void handlePkResult(String activityId, String strDate, String winner, String loser, long poolBeans, long loserReceiveBeans) {
        logger.info("handlePkResult. strDate={} winner={} loser={} poolBeans={} loserReceiveBeans={}", strDate, winner, loser, poolBeans, loserReceiveBeans);
        // 输家
        int loserDiamonds = (int) (loserReceiveBeans * 0.1 * 0.1);
        chargeDiamonds(loser, loserDiamonds, "Host PK-pk lose reward", "Host PK-pk lose reward");
        // 赢家
        int winnerDiamonds = (int) (poolBeans * 0.9);
        chargeDiamonds(winner, winnerDiamonds, "Host PK-pk win reward", "Host PK-pk win reward");
        anchorPkRedis.incrRankingScore(activityId, PK_TOTAL_RANKING, winner, winnerDiamonds);
        anchorPkRedis.incrRankingScore(activityId, PK_TOTAL_RANKING, loser, loserDiamonds);
    }

    private String getGuardianTop1Uid(String activityId, String strDate, String anchorUid) {
        Map<String, Long> guardianRankingMap = anchorPkRedis.getRankingMap(activityId, GUARDIAN_RANKING.formatted(strDate, anchorUid), 1);
        if (CollectionUtils.isEmpty(guardianRankingMap)) {
            return "";
        }
        return guardianRankingMap.keySet().iterator().next();
    }
}
