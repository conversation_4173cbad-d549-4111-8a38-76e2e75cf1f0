package com.quhong.service;

import com.quhong.core.date.DateSupport;
import com.quhong.core.date.DayTimeSupport;
import com.quhong.core.distribution.DistributeLock;
import com.quhong.core.utils.DateHelper;
import com.quhong.enums.SLangType;
import com.quhong.mongo.dao.ActorDao;
import com.quhong.mongo.dao.RechargeUniqueIdConfigDao;
import com.quhong.mongo.data.OfficialData;
import com.quhong.mongo.data.RechargeUniqueIdConfigData;
import com.quhong.mysql.dao.RechargeDailyInfoDao;
import com.quhong.mysql.dao.RechargeUniqueIdDao;
import com.quhong.mysql.data.RechargeUniqueIdData;
import jakarta.annotation.Resource;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.time.LocalDate;
import java.time.ZoneId;
import java.time.temporal.TemporalAdjusters;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2025/4/18
 */
@Service
public class RechargeUniqueIdService {

    public final ZoneId zoneId = ZoneId.of("+3");

    private static final String TITLE_EN = "System Notification";
    private static final String TITLE_AR = "إشعار النظام";
    private static final String BODY_EN = "Based on your recharge data, you can claim a higher-level special ID.";
    private static final String BODY_AR = "بناءً على بيانات الشحن الخاصة بك، يمكنك استلام ايدى مميز من مستوى أعلى.";

    @Resource
    private ActorDao actorDao;
    @Resource
    private OfficialMsgService officialMsgService;
    @Resource
    private RechargeDailyInfoDao rechargeDailyInfoDao;
    @Resource
    private RechargeUniqueIdConfigDao rechargeUniqueIdConfigDao;
    @Resource
    private RechargeUniqueIdDao rechargeUniqueIdDao;

    public void sendUniqueId(String uid) {
        List<RechargeUniqueIdConfigData> configList = rechargeUniqueIdConfigDao.selectAllFromCache();
        if (CollectionUtils.isEmpty(configList)) {
            return;
        }
        LocalDate today = DateSupport.ARABIAN.getToday();
        String strMonth = DateSupport.formatYYYY_MM(today);
        Integer[] timeArr = DayTimeSupport.ARABIAN.getMonthStartAndEndTime();
        int nowTime = DateHelper.getNowSeconds();
        try (DistributeLock lock = new DistributeLock(RechargeUniqueIdDao.LOCK_KEY + uid)) {
            lock.lock();
            long rechargeBeans = rechargeDailyInfoDao.getAllRechargeDiamonds(uid, timeArr[0], timeArr[1]);
            if (rechargeBeans < configList.get(0).getRechargeBeans()) {
                return;
            }
            RechargeUniqueIdData data = rechargeUniqueIdDao.selectHighestOne(uid, strMonth);
            int oldLevel = data != null ? data.getLevel() : 0;
            int newLevel = 0;
            for (RechargeUniqueIdConfigData configData : configList) {
                if (rechargeBeans >= configData.getRechargeBeans()) {
                    newLevel = Math.max(newLevel, configData.getLevel());
                }
            }
            if (newLevel > oldLevel) {
                RechargeUniqueIdData newData = new RechargeUniqueIdData();
                newData.setUid(uid);
                newData.setMonth(strMonth);
                newData.setLevel(newLevel);
                newData.setStatus(0);
                newData.setExpireTime(getNextMonthStartAndEndTime(today)[1]);
                newData.setMtime(nowTime);
                newData.setCtime(nowTime);
                rechargeUniqueIdDao.insert(newData);
                sendNotice(uid);
            }
        }
    }

    private void sendNotice(String uid) {
        int slang = actorDao.getActorDataFromCache(uid).getSlang();
        OfficialData officialData = new OfficialData();
        officialData.setTo_uid(uid);
        officialData.setTitle(SLangType.ENGLISH == slang ? TITLE_EN : TITLE_AR);
        officialData.setBody((SLangType.ENGLISH == slang ? BODY_EN : BODY_AR));
        officialData.setValid(1);
        officialData.setAtype(0);
        officialData.setNews_type(0);
        officialData.setUrl(RechargeUniqueIdDao.URL);
        officialData.setCtime(DateHelper.getNowSeconds());
        officialData.setAct(SLangType.ENGLISH == slang ? "View" : "عرض");
        officialMsgService.officialMsgPush(officialData);
    }

    private Integer[] getNextMonthStartAndEndTime(LocalDate today) {
        LocalDate date = today.plusMonths(1).withDayOfMonth(1);
        LocalDate startOfMonth = date.with(TemporalAdjusters.firstDayOfMonth());
        LocalDate endOfMonth = date.with(TemporalAdjusters.lastDayOfMonth());
        int startTime = (int) startOfMonth.atTime(0, 0, 0).atZone(zoneId).toEpochSecond();
        int endTime = (int) endOfMonth.atTime(23, 59, 59).atZone(zoneId).toEpochSecond();
        return new Integer[]{startTime, endTime};
    }
}
