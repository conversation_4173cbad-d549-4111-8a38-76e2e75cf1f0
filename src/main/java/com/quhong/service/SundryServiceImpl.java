package com.quhong.service;

import com.alibaba.excel.EasyExcel;
import com.alibaba.excel.ExcelWriter;
import com.alibaba.excel.write.metadata.WriteSheet;
import com.quhong.api.SundryService;
import com.quhong.country.CountryQuery;
import com.quhong.data.CountryData;
import com.quhong.data.vo.*;
import com.quhong.enums.HttpCode;
import com.quhong.enums.SLangType;
import com.quhong.exception.CommonException;
import com.quhong.utils.EmailUtils;
import com.quhong.utils.OSSUploadUtils;
import jakarta.annotation.Resource;
import org.apache.dubbo.config.annotation.DubboService;
import org.apache.http.entity.ContentType;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.BeanUtils;
import org.springframework.mock.web.MockMultipartFile;
import org.springframework.web.multipart.MultipartFile;

import java.io.ByteArrayInputStream;
import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2022/8/17
 */
@DubboService(timeout = 20000)
public class SundryServiceImpl implements SundryService {

    private final Logger logger = LoggerFactory.getLogger(getClass());

    private static final String REPORT_FILE_PATH = "reports/";

    @Resource
    private CountryQuery countryQuery;

    @Override
    public void exportAnchorReport(List<AnchorInfoVO> anchorInfoList, String fileName, Map<String, String> receiveAccountMap, int slang) throws CommonException {
        // 生成Excel报表并上传oss
        String reportDownloadUrl;
        String subject;
        if (slang == SLangType.ENGLISH) {
            List<AnchorInfoEnVO> enList = new ArrayList<>();
            anchorInfoList.forEach(k -> {
                AnchorInfoEnVO vo = new AnchorInfoEnVO();
                BeanUtils.copyProperties(k, vo);
                enList.add(vo);
            });
            reportDownloadUrl = exportAndUpload(enList, AnchorInfoEnVO.class, fileName);
            subject = "Family Data";
        } else {
            List<AnchorInfoArVO> arList = new ArrayList<>();
            anchorInfoList.forEach(k -> {
                AnchorInfoArVO vo = new AnchorInfoArVO();
                BeanUtils.copyProperties(k, vo);
                arList.add(vo);
            });
            reportDownloadUrl = exportAndUpload(arList, AnchorInfoArVO.class, fileName);
            subject = "بيانات العائلة";
        }
        logger.info("reportDownloadUrl={}", reportDownloadUrl);
        // 发送邮件
        EmailUtils.sendMail(subject, reportDownloadUrl, receiveAccountMap);
    }

    @Override
    public void exportHostsReport(List<HostDataVO> dataList, String fileName, Map<String, String> receiveAccountMap, int slang) throws CommonException {
        // 生成Excel报表并上传oss
        String reportDownloadUrl;
        String subject;
        if (slang == SLangType.ENGLISH) {
            List<HostDataEnVO> enList = new ArrayList<>();
            dataList.forEach(k -> {
                HostDataEnVO vo = new HostDataEnVO();
                BeanUtils.copyProperties(k, vo);
                enList.add(vo);
            });
            reportDownloadUrl = exportAndUpload(enList, HostDataEnVO.class, fileName);
            subject = "Hosts Data";
        } else {
            List<HostDataEnVO> arList = new ArrayList<>();
            dataList.forEach(k -> {
                HostDataEnVO vo = new HostDataEnVO();
                BeanUtils.copyProperties(k, vo);
                arList.add(vo);
            });
            reportDownloadUrl = exportAndUpload(arList, HostDataEnVO.class, fileName);
            subject = "Hosts Data";
        }
        logger.info("reportDownloadUrl={}", reportDownloadUrl);
        // 发送邮件
        EmailUtils.sendMail(subject, reportDownloadUrl, receiveAccountMap);
    }

    @Override
    public void exportAgentsReport(List<AgentDataVO> dataList, String fileName, Map<String, String> receiveAccountMap, int slang) throws CommonException {
        // 生成Excel报表并上传oss
        String reportDownloadUrl;
        String subject;
        if (slang == SLangType.ENGLISH) {
            List<AgentDataEnVO> enList = new ArrayList<>();
            dataList.forEach(k -> {
                AgentDataEnVO vo = new AgentDataEnVO();
                BeanUtils.copyProperties(k, vo);
                enList.add(vo);
            });
            reportDownloadUrl = exportAndUpload(enList, AgentDataEnVO.class, fileName);
            subject = "Agent Data";
        } else {
            List<AgentDataEnVO> arList = new ArrayList<>();
            dataList.forEach(k -> {
                AgentDataEnVO vo = new AgentDataEnVO();
                BeanUtils.copyProperties(k, vo);
                arList.add(vo);
            });
            reportDownloadUrl = exportAndUpload(arList, AgentDataEnVO.class, fileName);
            subject = "Agent Data";
        }
        logger.info("reportDownloadUrl={}", reportDownloadUrl);
        // 发送邮件
        EmailUtils.sendMail(subject, reportDownloadUrl, receiveAccountMap);
    }

    @Override
    public void exportReport(List<?> infoList, Class<?> head, String fileName, Map<String, String> receiveAccountMap) throws CommonException {
        // 生成Excel报表并上传oss
        String reportDownloadUrl = exportAndUpload(infoList, head, fileName);
        String subject = "Report Data";

        logger.info("reportDownloadUrl={}", reportDownloadUrl);
        // 发送邮件
        EmailUtils.sendMail(subject, reportDownloadUrl, receiveAccountMap);
    }

    private String exportAndUpload(List<?> data, Class<?> head, String fileName) {
        try {
            ByteArrayOutputStream bos = new ByteArrayOutputStream();
            ExcelWriter excelWriter = EasyExcel.write(bos, head).build();
            WriteSheet writeSheet = EasyExcel.writerSheet().build();
            excelWriter.write(data, writeSheet);
            excelWriter.finish();
            return uploadOss(bos, fileName);
        } catch (IOException e) {
            logger.error("exportAndUpload error :{}", e.getMessage(), e);
            throw new CommonException(HttpCode.SERVER_ERROR);
        }
    }

    private String uploadOss(ByteArrayOutputStream bos, String fileName) throws IOException {
        byte[] bytes = bos.toByteArray();
        MultipartFile file = new MockMultipartFile(
                fileName,
                fileName + ".xlsx",
                ContentType.APPLICATION_OCTET_STREAM.toString(),
                new ByteArrayInputStream(bytes)
        );
        return OSSUploadUtils.upload(file, REPORT_FILE_PATH);
    }

    @Override
    public String ipToCountryCode(String ip) {
        CountryData countryData = countryQuery.getCountryData(ip);
        return null == countryData ? null : countryData.getCode();
    }
}
