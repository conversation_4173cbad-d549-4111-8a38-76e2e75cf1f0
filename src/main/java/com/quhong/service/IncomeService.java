package com.quhong.service;


import com.alibaba.fastjson.JSON;
import com.google.common.collect.Interner;
import com.google.common.collect.Interners;
import com.quhong.config.CaffeineCacheConfig;
import com.quhong.constant.BankTransfer;
import com.quhong.core.config.ServerConfig;
import com.quhong.core.date.DateSupport;
import com.quhong.core.redis.BasePlayerRedis;
import com.quhong.core.utils.DateHelper;
import com.quhong.data.ActorData;
import com.quhong.data.dto.*;
import com.quhong.data.vo.*;
import com.quhong.enums.ClientOS;
import com.quhong.enums.HttpCode;
import com.quhong.enums.SLangType;
import com.quhong.enums.WithdrawReviewStatus;
import com.quhong.exception.CommonException;
import com.quhong.exception.CommonH5Exception;
import com.quhong.feign.DataCenterService;
import com.quhong.handler.HttpEnvData;
import com.quhong.httpResult.PayHttpCode;
import com.quhong.image.ImageUrlGenerator;
import com.quhong.mongo.dao.*;
import com.quhong.mongo.data.*;
import com.quhong.mysql.dao.*;
import com.quhong.mysql.data.*;
import com.quhong.redis.FreezeRechargeRedis;
import com.quhong.redis.SellDiamondRedis;
import com.quhong.redis.WhitelistRedis;
import com.quhong.utils.*;
import com.quhong.vo.PageVO;
import jakarta.annotation.Resource;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import org.springframework.util.ObjectUtils;
import org.springframework.util.StringUtils;
import org.springframework.web.util.UriComponentsBuilder;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.LocalDate;
import java.time.temporal.ChronoUnit;
import java.time.temporal.TemporalAdjusters;
import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.function.Function;
import java.util.stream.Collectors;

@Service
public class IncomeService {

    private final Logger logger = LoggerFactory.getLogger(getClass());

    // key=usd
    private static final Map<Integer, Integer> USD_TO_DIAMONDS_MAP = new LinkedHashMap<>() {{
        put(10, 10 * AnchorWalletService.getUsdToDiamonds());
        put(50, 50 * AnchorWalletService.getUsdToDiamonds());
        put(100, 100 * AnchorWalletService.getUsdToDiamonds());
    }};

    private static final List<WithdrawMethodVO.Bank> BANK_LIST = new ArrayList<>(); // Bank Transfer可选择的银行

    static {
        for (Map.Entry<String, String> entry : BankTransfer.MAP.entrySet()) {
            BANK_LIST.add(new WithdrawMethodVO.Bank(entry.getKey(), entry.getValue()));
        }
    }

    private static final String WITHDRAW_URL_DEBUG = "https://api.opswaho.com/withdraw/";
    private static final String WITHDRAW_URL_PRO = "https://statics.waho.live/withdraw/";

    private final Interner<String> stringPool = Interners.newWeakInterner();

    private static final int PAGE_SIZE = 20;
    private static final int MIN_OPT_USD = 20; // 转移魅力值/售卖钻石增加最低限额20美金

    private @Resource ActorDao actorDao;
    private @Resource FamilyDao familyDao;
    private @Resource FamilyMemberDao familyMemberDao;
    private @Resource AnchorUsdLogDao anchorUsdLogDao;
    private @Resource AnchorWalletService anchorWalletService;
    private @Resource WithdrawReviewDao withdrawReviewDao;
    private @Resource WithdrawMethodConfigDao withdrawMethodConfigDao;
    private @Resource MineWithdrawMethodDao mineWithdrawMethodDao;
    private @Resource BasePlayerRedis basePlayerRedis;
    private @Resource FreezeRechargeRedis freezeRechargeRedis;
    private @Resource BannerListService bannerListService;
    private @Resource WhitelistRedis whitelistRedis;
    private @Resource AuditAccountDao auditAccountDao;
    private @Resource RoomMicLogDao roomMicLogDao;
    private @Resource InvitedUserDao invitedUserDao;
    private @Resource WelcomePackLogDao welcomePackLogDao;
    private @Resource FreezeCharmDao freezeCharmDao;
    private @Resource DataCenterService dataCenterService;
    private @Resource CharmStatDao charmStatDao;
    private @Resource IncomeService incomeService;
    private @Resource WithdrawalOrderDao withdrawalOrderDao;
    private @Resource SellDiamondRedis sellDiamondRedis;
    private @Resource BasicSalaryAnchorDao basicSalaryAnchorDao;
    private @Resource MicTimeDao micTimeDao;
    private @Resource TaskCharmLogDao taskCharmLogDao;
    private @Resource RechargeOrderDao rechargeOrderDao;
    private @Resource FreezeCharmStatDao freezeCharmStatDao;

    private IncomeVO walletToVO(AnchorWalletData walletData) {
        return new IncomeVO(anchorWalletService.getShowCharmBalance(walletData.getUid(), walletData).intValue());
    }

    private List<IncomeVO.UsdToDiamondsVO> getUsdToDiamondsList() {
        List<IncomeVO.UsdToDiamondsVO> usdToDiamondsList = new ArrayList<>(); // 美金转钻石，支持的钻石列表
        for (Map.Entry<Integer, Integer> entry : USD_TO_DIAMONDS_MAP.entrySet()) {
            usdToDiamondsList.add(new IncomeVO.UsdToDiamondsVO(entry.getKey(), (int) (entry.getKey() * AnchorWalletService.USD_TO_CHARM), WalletUtils.diamondsForDisplay(entry.getValue())));
        }
        return usdToDiamondsList;
    }

    public IncomeVO incomeInfo(HttpEnvData dto) {
        IncomeVO vo = new IncomeVO();
        ActorData actorData = actorDao.getActorData(dto.getUid());
        FamilyMemberData familyMemberData = familyMemberDao.selectByUidFromCache(dto.getUid());
        AnchorWalletData wallet = anchorWalletService.getWallet(dto.getUid());
        BigDecimal freezeCharm = freezeCharmDao.getTotalFreezeCharm(dto.getUid());
        BigDecimal charmBalance = null == wallet ? BigDecimal.ZERO : wallet.getCharmBalance();
        if (freezeCharm.compareTo(charmBalance) > 0) {
            freezeCharm = charmBalance;
        }
        vo.setPendingCharm(freezeCharm.intValue());
        vo.setTotalConfirmedCharm((long) freezeCharmStatDao.getFreezeCharmSum(dto.getUid(), 0, DateSupport.getIntDate(DateSupport.ARABIAN.getToday())));
        vo.setCharmBalance(Math.max(charmBalance.subtract(freezeCharm).intValue(), 0));
        vo.setUsdToDiamondsList(getUsdToDiamondsList());
        vo.setIncomeYesterday(incomeService.getAnchorIncomeYesterday(dto.getUid()));
        vo.setFace(ObjectUtils.isEmpty(actorData.getFaceId()) && !whitelistRedis.inWhitelist(WhitelistRedis.FACE_AUTH, dto.getUid()) ? 0 : 1);
        vo.setBannerList(bannerListService.getBannerList(dto, BannerListService.INCOME_BANNER));
        vo.setWithdrawConfirm(withdrawalOrderDao.getCountByStatus(dto.getUid(), "", Collections.singletonList(1)) > 0 ? 1 : 0);
        if (null != familyMemberData) {
            FamilyData familyData = familyDao.selectByIdFromCache(familyMemberData.getFamilyId());
            ActorData familyOwner = actorDao.getActorDataFromCache(familyData.getOwnerUid());
            vo.setFamilyOwnRid(familyOwner.getRid() + "");
            vo.setFamilyOwnName(familyOwner.getName());
            vo.setFamilyOwnHead(ImageUrlGenerator.generateRoomUserUrl(familyOwner.getHead()));
            vo.setForcedControlMember(isForcedControlMember(dto.getUid(), familyData) ? 1 : 0);
        }
        String token = basePlayerRedis.getToken(dto.getUid());
        boolean isAnchor = familyMemberData != null;
        boolean hasWithdrawRecord = withdrawReviewDao.selectCount(dto.getUid()) > 0;
        fillWithdrawUrl(dto, token, isAnchor, hasWithdrawRecord, vo);
        BasicSalaryAnchorData basicSalaryAnchor = basicSalaryAnchorDao.selectByUid(dto.getUid());
        if (basicSalaryAnchor != null) {
            // 底薪主播
            vo.setBasicSalaryAnchor(basicSalaryAnchorDao.selectByUid(dto.getUid()) != null ? 1 : 0);
            LocalDate today = DateSupport.ARABIAN.getToday();
            LocalDate startDate = today.with(TemporalAdjusters.firstDayOfMonth());
            LocalDate endDate = today.with(TemporalAdjusters.lastDayOfMonth());
            LocalDate joinDate = DateSupport.ARABIAN.getLocalDate(basicSalaryAnchor.getCtime() * 1000L);
            if (ChronoUnit.DAYS.between(joinDate, endDate) + 1 >= 20) {
                int workTimeLimit = ServerConfig.isProduct() ? (int) TimeUnit.HOURS.toSeconds(2) : (int) TimeUnit.MINUTES.toSeconds(2);
                int todayMicTime = micTimeDao.getTodayUpMicTime(dto.getUid(), 1);
                vo.setTodayLiveStrTime(getStrTime(todayMicTime));
                vo.setTodayTaskProgress(Math.min(todayMicTime * 100 / workTimeLimit, 100));
                int intStartDate = Math.max(DateSupport.getIntDate(startDate), DateSupport.getIntDate(joinDate));
                int intEndDate = DateSupport.getIntDate(endDate);
                vo.setLiveHours(micTimeDao.getUserUpMicTimeNew(dto.getUid(), 1, intStartDate, intEndDate) / (int) TimeUnit.HOURS.toSeconds(1));
                vo.setLiveDays(micTimeDao.getWorkDayNew(dto.getUid(), 1, intStartDate, intEndDate, workTimeLimit));
                vo.setBasicSalaryTaskStatus(1);
            }
        }
        return vo;
    }

    @Cacheable(value = "cache", cacheManager = CaffeineCacheConfig.EXPIRE_10M_AFTER_WRITE,
            key = "#root.method.name+T(org.springframework.util.StringUtils).arrayToDelimitedString(args,'-')")
    public int getAnchorIncomeYesterday(String uid) {
        LocalDate yesterday = DateSupport.ARABIAN.getYesterday();
        int intYesterday = Integer.parseInt(DateSupport.yyyyMMdd(yesterday));
        return (int) charmStatDao.getStatCharm(Collections.singleton(uid), null, Arrays.asList(1, 2, 4, 6, 7, 8), intYesterday, intYesterday);
    }

    public boolean isForcedControlMember(String uid, FamilyData familyData) {
        if (familyData == null) {
            FamilyMemberData memberData = familyMemberDao.selectByUidFromCache(uid);
            if (memberData != null) {
                familyData = familyDao.selectById(memberData.getFamilyId());
            }
        }
        if (familyData != null) {
            return familyData.getForcedControl() == 1 && !uid.equals(familyData.getOwnerUid());
        }
        return false;
    }

    private boolean blockWithdraw(String uid, boolean isAnchor, boolean hasWithdrawRecord) {
        if (whitelistRedis.inWhitelist(WhitelistRedis.WITHDRAW, uid) || hasWithdrawRecord) {
            return false;
        }
        // 公会长、主播身份默认显示提现入口
        if (isAnchor) {
            return false;
        }
        // 通过邀请链接注册的用户显示提现入口
        if (invitedUserDao.selectByUid(uid) != null) {
            return false;
        }
        // 收到欢迎礼包的用户显示提现入口
        if (welcomePackLogDao.selectCount(uid) > 0) {
            return false;
        }
        // 最近30天上麦满10小时才能提现
        int nowSeconds = DateHelper.getNowSeconds();
        int upMicTimeLimit = ServerConfig.isProduct() ? (int) TimeUnit.HOURS.toSeconds(10) : (int) TimeUnit.MINUTES.toSeconds(10);
        return roomMicLogDao.actorAddUpMicTime((int) (nowSeconds - TimeUnit.DAYS.toSeconds(30)), nowSeconds, uid) <= upMicTimeLimit;
    }

    private void fillWithdrawUrl(HttpEnvData dto, String token, boolean isAnchor, boolean hasWithdrawRecord, IncomeVO vo) {
        if (ClientOS.IOS == dto.getOs() && auditAccountDao.selectOneByUid(dto.getUid()) != null) {
            // ios审核人员不显示提现入口
            return;
        }
        if (vo.getForcedControlMember() == 1) {
            // 强控公会成员不显示入口
            return;
        }
        if (blockWithdraw(dto.getUid(), isAnchor, hasWithdrawRecord)) {
            logger.info("The microphone duration is not reached. uid={}", dto.getUid());
            return;
        }
        vo.setWithdrawUrl(buildUrl(ServerConfig.isProduct() ? WITHDRAW_URL_PRO : WITHDRAW_URL_DEBUG, dto.getUid(), token, dto.getSlang()));
    }

    public String buildUrl(String url, String uid, String token, int slang) {
        UriComponentsBuilder urlBuilder = UriComponentsBuilder.fromHttpUrl(url);
        urlBuilder.queryParam("uid", uid);
        urlBuilder.queryParam("token", token);
        urlBuilder.queryParam("slang", slang);
        return urlBuilder.build(false).encode().toUriString();
    }

    public IncomeVO transferUsd(IncomeDTO dto) {
        if (dto.getAmount() < MIN_OPT_USD) {
            throw new CommonException(PayHttpCode.AMOUNT_MUST_BE_GTE);
        }
        ActorData actorData = actorDao.getActorDataFromCache(dto.getUid());
        if (null == actorData) {
            throw new CommonException(HttpCode.PARAM_ERROR);
        }
        if (ObjectUtils.isEmpty(actorData.getFaceId()) && AppVersionUtils.versionCheck(115, dto)) {
            // todo 转账人脸验证
            throw new CommonH5Exception(HttpCode.AUTH_ERROR);
        }
        if (freezeRechargeRedis.isFreezeRecharge(actorData.getUid(), actorData.getTn_id())) {
            logger.info("account has been frozen. uid={}", actorData.getUid());
            throw new CommonException(PayHttpCode.ACCOUNT_HAS_BEEN_FROZEN);
        }
        AnchorWalletData wallet = anchorWalletService.getWallet(dto.getUid());
        if (null == wallet) {
            throw new CommonException(HttpCode.INSUFFICIENT_BALANCE);
        }
        FamilyMemberData familyMemberData = familyMemberDao.selectByUid(dto.getUid());
        if (null == familyMemberData) {
            throw new CommonException(HttpCode.AUTH_ERROR);
        }
        FamilyData familyData = familyDao.selectByIdFromCache(familyMemberData.getFamilyId());
        if (dto.getUid().equals(familyData.getOwnerUid())) {
            throw new CommonException(PayHttpCode.TRANSFER_SELF);
        }
        return walletToVO(anchorWalletService.transferUsd(dto.getUid(), familyData.getOwnerUid(), dto.getAmount(), familyMemberData.getFamilyId()));
    }

    public IncomeVO usdToDiamonds(String uid, int amount) {
        ActorData actorData = actorDao.getActorDataFromCache(uid);
        if (null == actorData) {
            throw new CommonException(HttpCode.PARAM_ERROR);
        }
        if (isForcedControlMember(uid, null)) {
            logger.error("isForcedControlMember. uid={}", uid);
            throw new CommonH5Exception(HttpCode.AUTH_ERROR);
        }
        if (freezeRechargeRedis.isFreezeRecharge(actorData.getUid(), actorData.getTn_id())) {
            logger.info("account has been frozen. uid={}", actorData.getUid());
            throw new CommonException(PayHttpCode.ACCOUNT_HAS_BEEN_FROZEN);
        }
        AnchorWalletData wallet = anchorWalletService.getWallet(uid);
        if (null == wallet) {
            throw new CommonException(HttpCode.INSUFFICIENT_BALANCE);
        }
        if (!USD_TO_DIAMONDS_MAP.containsKey(amount)) {
            throw new CommonException(HttpCode.PARAM_ERROR);
        }
        FamilyMemberData familyMemberData = familyMemberDao.selectByUid(uid);
        int familyId = familyMemberData == null ? 0 : familyMemberData.getFamilyId();
        return walletToVO(anchorWalletService.usdToDiamonds(uid, amount, familyId));
    }

    public CharmDetailListVO charmDetailFromEs(IncomeDTO.PageDTO dto) {
        CharmDetailListVO vo = new CharmDetailListVO();
        List<MoneyDetailStatData> list = new ArrayList<>();
        long now = System.currentTimeMillis();
        Date s = DateHelper.ARABIAN.parseDate(dto.getStartDate());
        Date e = DateHelper.ARABIAN.parseDate(dto.getEndDate());
        long limit = TimeUnit.DAYS.toMillis(100);
        if (Math.abs(now - s.getTime()) > limit || Math.abs(now - e.getTime()) > limit) {
            vo.setHasWithdrawRecord(withdrawReviewDao.selectCount(dto.getUid()) > 0 ? 1 : 0);
            vo.setList(list);
            vo.setNextUrl("");
            return vo;
        }
        DataCenterCharmDetailDTO detailDTO = new DataCenterCharmDetailDTO();
        detailDTO.setUid(dto.getUid());
        detailDTO.setStartDate(dto.getStartDate());
        detailDTO.setEndDate(dto.getEndDate());
        detailDTO.setLogTypeList(getIntegers(dto.getLogType()));
        detailDTO.setChangeType(dto.getChangeType());
        detailDTO.setPage(dto.getPage());
        List<AnchorCharmLogData> pageList = dataCenterService.esCharmDetail(detailDTO).getData();
        for (AnchorCharmLogData data : pageList) {
            MoneyDetailStatData moneyDetailStatData = new MoneyDetailStatData();
            moneyDetailStatData.setTitle(data.getTitle());
            moneyDetailStatData.setTitleAr(data.getTitleAr());
            moneyDetailStatData.setDesc(data.getDesc());
            moneyDetailStatData.setDescAr(data.getDescAr());
            moneyDetailStatData.setMtime(data.getCtime());
            moneyDetailStatData.setChanged(data.getChange().longValue());
            moneyDetailStatData.setBalance(data.getBalance().longValue());
            list.add(moneyDetailStatData);
        }
        vo.setHasWithdrawRecord(withdrawReviewDao.selectCount(dto.getUid()) > 0 ? 1 : 0);
        vo.setList(list);
        vo.setNextUrl(pageList.size() < PAGE_SIZE ? "" : String.valueOf(dto.getPage() + 1));
        return vo;
    }

    /**
     * 1接收房间礼物 2接收私信礼物 3美金提现失败返还 4接收公会成员转账 5运营系统操作魅力值
     * -1转换为美金 -2每月清零 -3退出公会清零 -4转账给公会长 -5转换钻石 -6拒绝成员退出公会扣除费用 -7美金提现 -8主播给用户或币商充值 -9退款扣除
     *
     * @param logType 0all, 1Live & party, 2Chat, 3Agency commission, 4Reward, 5Exchange, 6Official Withdraw, 7Other 8Transaction 9Agency withdrawal
     */
    private List<Integer> getIntegers(int logType) {
        List<Integer> logTypeList = Collections.emptyList();
        if (1 == logType) {
            logTypeList = List.of(1);
        } else if (2 == logType) {
            logTypeList = List.of(2);
        } else if (3 == logType) {
            logTypeList = List.of(6, 7);
        } else if (4 == logType) {
            logTypeList = List.of(8, 11, 12, 9, 10, 15, 16, 17);
        } else if (5 == logType) {
            logTypeList = List.of(-5);
        } else if (6 == logType) {
            logTypeList = List.of(-7, 3);
        } else if (7 == logType) {
            logTypeList = List.of(-9, 5);
        } else if (8 == logType) {
            logTypeList = List.of(4, -8, -4);
        } else if (9 == logType) {
            logTypeList = List.of(13, 14, -13);
        }
        return logTypeList;
    }

    public PageVO<MoneyDetailStatData> usdDetail(String uid, int page) {
        int start = page > 1 ? (page - 1) * PAGE_SIZE : 0;
        List<AnchorUsdLogData> pageList = anchorUsdLogDao.selectPage(uid, start, PAGE_SIZE);
        PageVO<MoneyDetailStatData> vo = new PageVO<>(new ArrayList<>(PAGE_SIZE));
        for (AnchorUsdLogData data : pageList) {
            MoneyDetailStatData moneyDetailStatData = new MoneyDetailStatData();
            moneyDetailStatData.setTitle(data.getTitle());
            moneyDetailStatData.setDesc(data.getDesc());
            moneyDetailStatData.setMtime(data.getCtime());
            moneyDetailStatData.setChanged(data.getChange().longValue());
            vo.getList().add(moneyDetailStatData);
        }
        vo.setNextUrl(pageList.size() < PAGE_SIZE ? "" : String.valueOf(page + 1));
        return vo;
    }

    public IncomeVO withdrawUsd(IncomeDTO dto) {
        ActorData actorData = actorDao.getActorData(dto.getUid());
        if (actorData == null) {
            logger.error("can not find actor data. uid={}", dto.getUid());
            throw new CommonH5Exception(HttpCode.PARAM_ERROR);
        }
        if (isForcedControlMember(dto.getUid(), null)) {
            logger.error("isForcedControlMember. uid={}", dto.getUid());
            throw new CommonH5Exception(HttpCode.AUTH_ERROR);
        }
        if (ObjectUtils.isEmpty(actorData.getFaceId()) && AppVersionUtils.versionCheck(115, dto)) {
            // todo 提现人脸验证
            throw new CommonH5Exception(HttpCode.AUTH_ERROR);
        }
        MineWithdrawMethodData mineWithdrawMethod = mineWithdrawMethodDao.findByIdAndUid(dto.getMineMethodId(), dto.getUid());
        if (mineWithdrawMethod == null) {
            logger.info("can not find mine withdraw method. mineMethodId={}", dto.getMineMethodId());
            throw new CommonH5Exception(HttpCode.PARAM_ERROR);
        }
        WithdrawMethodConfigData methodConfig = withdrawMethodConfigDao.findById(mineWithdrawMethod.getMethodId());
        if (methodConfig == null) {
            logger.info("can not find withdraw method config. methodId={}", mineWithdrawMethod.getMethodId());
            throw new CommonH5Exception(HttpCode.PARAM_ERROR);
        }
        int amount = (int) (dto.getWithdrawAmount());
        if (amount < methodConfig.getMinimumAmount()) {
            logger.info("The minimum amount is ${}. amount={}", methodConfig.getMinimumAmount(), amount);
            throw new CommonH5Exception(new HttpCode(1, String.format("The minimum amount is $ %d.", methodConfig.getMinimumAmount())));
        }
        if (methodConfig.getMaximumAmount() > 0 && amount > methodConfig.getMaximumAmount()) {
            logger.info("The maximum amount is ${}. amount={}", methodConfig.getMaximumAmount(), amount);
            throw new CommonH5Exception(new HttpCode(1, String.format("The maximum amount is $ %d.", methodConfig.getMaximumAmount()), String.format("الحد الأقصى للمبلغ هو $ %d.", methodConfig.getMaximumAmount())));
        }
        FamilyMemberData familyMemberData = familyMemberDao.selectByUid(dto.getUid());
        boolean isAnchor = familyMemberData != null;
        boolean hasWithdrawRecord = withdrawReviewDao.selectCount(dto.getUid()) > 0;
        if (blockWithdraw(dto.getUid(), isAnchor, hasWithdrawRecord)) {
            throw new CommonH5Exception(HttpCode.AUTH_ERROR);
        }
        // 计算手续费
        BigDecimal handlingFee = calculateHandlingFee(amount, methodConfig);
        if (handlingFee.compareTo(BigDecimal.ZERO) == 0) {
            logger.error("handling fee is 0");
            throw new CommonH5Exception(HttpCode.PARAM_ERROR);
        }
        AnchorWalletData wallet = anchorWalletService.getWallet(dto.getUid());
        if (null == wallet) {
            logger.info("Insufficient Balance.");
            throw new CommonH5Exception(HttpCode.INSUFFICIENT_BALANCE);
        }
        long costCharm = amount * AnchorWalletService.USD_TO_CHARM;
        BigDecimal freezeCharm = freezeCharmDao.getTotalFreezeCharm(dto.getUid());
        if (costCharm > wallet.getCharmBalance().subtract(freezeCharm).intValue()) {
            logger.info("Amount exceeds your balance. amount={} handlingFee={} balance={}", amount, handlingFee, wallet.getCharmBalance());
            throw new CommonH5Exception(new HttpCode(1, "Amount exceeds your balance."));
        }
        WithdrawReviewData reviewData = new WithdrawReviewData();
        reviewData.setUid(dto.getUid());
        reviewData.setFamilyId(null == familyMemberData ? 0 : familyMemberData.getFamilyId());
        reviewData.setCountry(ActorUtils.getCountryCode(actorData.getCountry()));
        reviewData.setAmount(amount);
        reviewData.setHandlingFee(handlingFee);
        reviewData.setStatus(WithdrawReviewStatus.NONE);
        reviewData.setMethodId(mineWithdrawMethod.getMethodId());
        reviewData.setMineMethodId(mineWithdrawMethod.get_id().toString());
        reviewData.setReason("");
        reviewData.setRemarkUrl("");
        reviewData.setCtime(DateHelper.getNowSeconds());
        reviewData.setAutoPayment(methodConfig.getAutoPayment());
        reviewData.setCountryCode(methodConfig.getCountryCode());
        reviewData.setCurrency(methodConfig.getCurrency());
        reviewData.setUsdExchange(methodConfig.getUsdExchange());
        reviewData.setConfig(JSON.toJSONString(mineWithdrawMethod.getContent()));
        reviewData.setReviewTime(0);
        reviewData.setRequestTime(0);
        reviewData.setCallbackTime(0);
        return walletToVO(anchorWalletService.usdWithdrawal(reviewData));
    }

    public PageVO<WithdrawDetailVO> withdrawDetail(String uid, int page) {
        PageVO<WithdrawDetailVO> vo = new PageVO<>();
        List<WithdrawDetailVO> list = new ArrayList<>();
        page = page == 0 ? 1 : page;
        List<WithdrawReviewData> reviewDataList = withdrawReviewDao.selectListByUid(uid, page, PAGE_SIZE);
        if (!CollectionUtils.isEmpty(reviewDataList)) {
            List<WithdrawMethodConfigData> allWithdrawMethod = withdrawMethodConfigDao.findAllFromCache();
            Map<String, WithdrawMethodConfigData> withdrawMethodMap = allWithdrawMethod.stream().collect(Collectors.toMap(k -> k.get_id().toString(), Function.identity()));
            for (WithdrawReviewData data : reviewDataList) {
                WithdrawDetailVO detailVO = new WithdrawDetailVO();
                WithdrawMethodConfigData methodConfigData = withdrawMethodMap.get(data.getMethodId());
                detailVO.setOrderId(data.getId() + "");
                detailVO.setTitle(String.format("Withdraw to %s", methodConfigData != null ? methodConfigData.getName() : ""));
                detailVO.setIcon(methodConfigData != null ? methodConfigData.getIcon() : "");
                detailVO.setAmount(data.getAmount());
                detailVO.setHandlingFee(data.getHandlingFee());
                detailVO.setReceivedAmount(calcReceiveAmount(data.getAmount(), data.getHandlingFee(), data.getCurrency(), data.getUsdExchange(), data.getMethodId()));
                detailVO.setRemarkUrl(data.getRemarkUrl());
                detailVO.setStatus(getWithDrawStatus(data.getStatus()));
                detailVO.setCtime(data.getCtime());
                list.add(detailVO);
            }
        }
        vo.setList(list);
        vo.setNextUrl(page, PAGE_SIZE);
        return vo;
    }

    public WithdrawMethodVO withdrawMethod(PageDTO dto) {
        String uid = dto.getUid();
        int slang = dto.getSlang();
        ActorData actorData = actorDao.getActorData(uid);
        if (actorData == null) {
            logger.error("can not find actor data. uid={}", uid);
            throw new CommonH5Exception(HttpCode.PARAM_ERROR);
        }
        WithdrawMethodVO vo = new WithdrawMethodVO();
        String countryCode = ActorUtils.getCountryCode(actorData.getCountry());
        List<WithdrawMethodConfigData> allMethod = withdrawMethodConfigDao.findByCountry(StringUtils.hasLength(countryCode) ? countryCode.toUpperCase() : "");
        List<WithdrawMethodConfigVO> list = new ArrayList<>();
        if (!CollectionUtils.isEmpty(allMethod)) {
            List<MineWithdrawMethodData> mineMethodList = mineWithdrawMethodDao.findListByUid(uid);
            Map<String, MineWithdrawMethodData> mineMethodMap = CollectionUtil.listToKeyMap(mineMethodList, MineWithdrawMethodData::getMethodId);
            for (WithdrawMethodConfigData methodConfigData : allMethod) {
                if ("eg".equals(countryCode) && "USDT（TRC-20）".equals(methodConfigData.getName())) {
                    // 埃及的usdt提现下架掉
                    continue;
                }
                WithdrawMethodConfigVO methodConfigVO = new WithdrawMethodConfigVO();
                methodConfigVO.setMethodId(methodConfigData.get_id().toString());
                methodConfigVO.setName(methodConfigData.getName());
                methodConfigVO.setNameAr(methodConfigData.getNameAr());
                methodConfigVO.setIcon(methodConfigData.getIcon());
                methodConfigVO.setTags(slang == SLangType.ENGLISH ? methodConfigData.getTags() : methodConfigData.getArTags());
                methodConfigVO.setFields(delFields(methodConfigData.getFields(), slang));
                methodConfigVO.setOperatorList(getOperatorList(methodConfigData.getOperatorList()));
                if (mineMethodMap.containsKey(methodConfigVO.getMethodId())) {
                    MineWithdrawMethodData mineMethodData = mineMethodMap.get(methodConfigVO.getMethodId());
                    methodConfigVO.setStatus(getMethodStatus(methodConfigData.getFields(), mineMethodData.getContent()));
                    methodConfigVO.setVerify(mineMethodData.getVerify());
                    methodConfigVO.setContent(mineMethodData.getContent());
                    methodConfigVO.setMineMethodId(mineMethodData.get_id().toString());
                    if (mineMethodData.getContent().containsKey("operator")) {
                        String operatorKey = mineMethodData.getContent().get("operator");
                        Map<String, WithdrawOperatorData> withdrawOperatorMap = CollectionUtil.listToKeyMap(methodConfigData.getOperatorList(), WithdrawOperatorData::getKey);
                        WithdrawOperatorData withdrawOperatorData = withdrawOperatorMap.get(operatorKey);
                        if (withdrawOperatorData != null) {
                            methodConfigVO.setIsFixedAmount(withdrawOperatorData.getIsFixedAmount());
                            methodConfigVO.setFixedAmountList(withdrawOperatorData.getFixedAmountList());
                        }
                    }
                } else {
                    Map<String, String> content;
                    if (!CollectionUtils.isEmpty(methodConfigData.getFields())) {
                        content = new HashMap<>(methodConfigData.getFields().size());
                        methodConfigData.getFields().forEach(k -> content.put(k.getKey(), ""));
                    } else {
                        content = Collections.emptyMap();
                    }
                    methodConfigVO.setStatus(0);
                    methodConfigVO.setContent(content);
                    methodConfigVO.setMineMethodId("");
                }
                list.add(methodConfigVO);
            }
        }
        vo.setUsdBalance((int) (anchorWalletService.getShowCharmBalance(uid, null).intValue() / AnchorWalletService.USD_TO_CHARM));
        vo.setWithdrawMethodList(list);
        vo.setBankTransferChooseBanks(BANK_LIST);
        return vo;
    }

    private int getMethodStatus(List<WithdrawMethodField> fields, Map<String, String> content) {
        if (CollectionUtils.isEmpty(fields)) {
            return 0;
        }
        for (WithdrawMethodField field : fields) {
            if (!StringUtils.hasLength(content.get(field.getKey()))) {
                return 0;
            }
        }
        return 1;
    }

    private List<WithdrawMethodConfigVO.Operator> getOperatorList(List<WithdrawOperatorData> operatorList) {
        if (CollectionUtils.isEmpty(operatorList)) {
            return Collections.emptyList();
        }
        List<WithdrawMethodConfigVO.Operator> list = new ArrayList<>();
        for (WithdrawOperatorData data : operatorList) {
            list.add(new WithdrawMethodConfigVO.Operator(data.getKey(), data.getName()));
        }
        return list;
    }

    private List<WithdrawMethodField> delFields(List<WithdrawMethodField> fields, int slang) {
        if (CollectionUtils.isEmpty(fields)) {
            return Collections.emptyList();
        }
        fields.forEach(k -> {
            if (slang != SLangType.ENGLISH) {
                k.setName(k.getNameAr());
                k.setDesc(k.getDescAr());
            }
            k.setNameAr(null);
            k.setDescAr(null);
        });
        return fields;
    }

    public void saveWithdrawMethod(WithdrawMethodDTO dto) {
        WithdrawMethodConfigData methodConfigData = withdrawMethodConfigDao.findById(dto.getMethodId());
        if (methodConfigData == null) {
            logger.info("methodConfigData is null. methodId={}", dto.getMethodId());
            throw new CommonH5Exception(HttpCode.PARAM_ERROR);
        }
        checkContent(dto.getContent(), methodConfigData);
        // 使用usdt提现时，判断是地址是否是之前的收款地址，防止用户填错
        if ("654dc782171a0000fe001739".equals(dto.getMethodId())) {
            String walletAddress = dto.getContent().get("wallet_address");
            if (!ObjectUtils.isEmpty(walletAddress)) {
                if (rechargeOrderDao.selectUsdtAddressCount(walletAddress) > 0) {
                    logger.error("actor use payIn usdtAddress. uid={} address={}", dto.getUid(), walletAddress);
                    throw new CommonH5Exception(1, "Please check whether the address you entered is your own wallet address!");
                }
            }
        }
        synchronized (stringPool.intern("saveWithdrawMethod_" + dto.getUid())) {
            MineWithdrawMethodData mineData;
            if (StringUtils.hasLength(dto.getMineMethodId())) {
                mineData = mineWithdrawMethodDao.findByIdAndUid(dto.getMineMethodId(), dto.getUid());
                if (mineData == null) {
                    logger.error("can not find mine withdraw method data. uid={} mineMethodId={}", dto.getUid(), dto.getMineMethodId());
                    throw new CommonH5Exception(HttpCode.PARAM_ERROR);
                }
            } else {
                mineData = mineWithdrawMethodDao.findByUidAndMethodId(dto.getUid(), dto.getMethodId());
                if (mineData == null) {
                    mineData = new MineWithdrawMethodData();
                    mineData.setMethodId(dto.getMethodId());
                }
                mineData.setUid(dto.getUid());
            }
            // 发生修改时，重置验证状态
            if (null == mineData.getContent() || mineData.getContent().hashCode() != dto.getContent().hashCode()) {
                mineData.setVerify(0);
            }
            mineData.setContent(dto.getContent());
            mineWithdrawMethodDao.save(mineData);
        }
    }

    private void checkContent(Map<String, String> content, WithdrawMethodConfigData methodConfigData) {
        if (CollectionUtils.isEmpty(methodConfigData.getFields())) {
            logger.error("methodConfigData is error. methodId={}", methodConfigData.get_id());
            return;
        }
        for (WithdrawMethodField field : methodConfigData.getFields()) {
            if (!StringUtils.hasLength(content.get(field.getKey()))) {
                logger.info("field value is empty. key={}", field.getKey());
                throw new CommonH5Exception(HttpCode.PARAM_ERROR);
            }
            if (StringUtils.hasLength(field.getRegular())) {
                if (!content.get(field.getKey()).matches(field.getRegular())) {
                    logger.info("field value is invalid, key={} value={} regular={}", field.getKey(), content.get(field.getKey()), field.getRegular());
                    throw new CommonH5Exception(new HttpCode(1, "Please check the input"));
                }
            }
        }
    }

    public WithdrawHandlingFeeVO withdrawHandlingFee(IncomeDTO dto) {
        MineWithdrawMethodData mineWithdrawMethod = mineWithdrawMethodDao.findByIdAndUid(dto.getMineMethodId(), dto.getUid());
        if (mineWithdrawMethod == null) {
            logger.info("can not find mine withdraw method. mineMethodId={}", dto.getMineMethodId());
            throw new CommonH5Exception(HttpCode.PARAM_ERROR);
        }
        WithdrawMethodConfigData methodConfig = withdrawMethodConfigDao.findById(mineWithdrawMethod.getMethodId());
        if (methodConfig == null) {
            logger.info("can not find withdraw method config. methodId={}", mineWithdrawMethod.getMethodId());
            throw new CommonH5Exception(HttpCode.PARAM_ERROR);
        }
        WithdrawHandlingFeeVO vo = new WithdrawHandlingFeeVO();
        int amount = (int) dto.getWithdrawAmount();
        if (amount < methodConfig.getMinimumAmount()) {
            logger.info("The minimum amount is ${}. amount={}", methodConfig.getMinimumAmount(), amount);
            throw new CommonH5Exception(new HttpCode(1, String.format("The minimum amount is $ %d.", methodConfig.getMinimumAmount())));
        }
        // 计算手续费
        BigDecimal handlingFee = calculateHandlingFee(amount, methodConfig);
        vo.setWithdrawAmount(amount);
        vo.setHandlingFee(handlingFee);
        if (methodConfig.getHandlingFeeType() == 0) {
            vo.setRate("$" + methodConfig.getHandlingFeeRate());
        } else {
            vo.setRate(methodConfig.getHandlingFeeRate() + "%");
        }
        vo.setReceivedAmount(calcReceiveAmount(amount, handlingFee, methodConfig.getCurrency(), methodConfig.getUsdExchange(), mineWithdrawMethod.getMethodId()));
        return vo;
    }

    private String calcReceiveAmount(int amount, BigDecimal handlingFee, String currency, String usdExchange, String methodId) {
        if (!WithdrawMethodConfigDao.AUTO_PAYMENT_LOCAL_MAP.containsKey(methodId)) {
            // 非本地币种体现的直接显示USD
            return "USD " + new BigDecimal(amount).subtract(handlingFee).setScale(2, RoundingMode.HALF_UP);
        }
        if (!ObjectUtils.isEmpty(currency) && !ObjectUtils.isEmpty(usdExchange)) {
            return currency + " " + new BigDecimal(amount).subtract(handlingFee).multiply(new BigDecimal(usdExchange)).setScale(2, RoundingMode.HALF_UP);
        } else {
            return "USD " + new BigDecimal(amount).subtract(handlingFee).setScale(2, RoundingMode.HALF_UP);
        }
    }

    private BigDecimal calculateHandlingFee(int amount, WithdrawMethodConfigData methodConfig) {
        if (methodConfig.getHandlingFeeType() == 0) {
            // 按每笔固定收取手续费
            return new BigDecimal(methodConfig.getHandlingFeeRate() + "");
        } else {
            // 按百分比收取手续费
            return new BigDecimal(methodConfig.getHandlingFeeRate() * amount / 100f + "").setScale(2, RoundingMode.HALF_UP);
        }
    }

    /**
     * @return 状态：0提现中 1提现成功 2提现失败
     */
    private Integer getWithDrawStatus(int status) {
        if (status == WithdrawReviewStatus.NONE || status == WithdrawReviewStatus.PENDING_PAYMENT
                || status == WithdrawReviewStatus.AUTO_PAYMENT_WAITING || status == WithdrawReviewStatus.AUTO_PAYMENT_PENDING) {
            return 0;
        } else if (status == WithdrawReviewStatus.PAYMENT_SUCCESS || status == WithdrawReviewStatus.AUTO_PAYMENT_SUCCESS) {
            return 1;
        } else {
            return 2;
        }
    }

    public IncomeVO usdSellDiamonds(UsdSellDiamondDTO dto) {
        String uid = dto.getUid();
        String aid = dto.getAid();
        int amount = dto.getAmount();
        if (amount < MIN_OPT_USD * AnchorWalletService.USD_TO_CHARM) {
            throw new CommonException(PayHttpCode.CHARM_AMOUNT_MUST_BE_GTE);
        }
        ActorData actorData = actorDao.getActorDataFromCache(aid);
        if (null == actorData) {
            throw new CommonException(HttpCode.PARAM_ERROR);
        }
        if (isForcedControlMember(dto.getUid(), null)) {
            logger.error("isForcedControlMember. uid={}", dto.getUid());
            throw new CommonH5Exception(HttpCode.AUTH_ERROR);
        }
        if (freezeRechargeRedis.isFreezeRecharge(actorData.getUid(), actorData.getTn_id())) {
            logger.info("usdSellDiamonds account has been frozen. uid={}", actorData.getUid());
            throw new CommonException(PayHttpCode.ACCOUNT_HAS_BEEN_FROZEN);
        }
        AnchorWalletData wallet = anchorWalletService.getWallet(uid);
        if (null == wallet) {
            throw new CommonException(HttpCode.INSUFFICIENT_BALANCE);
        }
        FamilyMemberData familyMemberData = familyMemberDao.selectByUid(uid);
        int familyId = familyMemberData == null ? 0 : familyMemberData.getFamilyId();
        AnchorWalletData walletData = anchorWalletService.newUsdSellDiamonds(uid, aid, amount, familyId);
        sellDiamondRedis.addSellDiamondRecord(uid, aid);
        return walletToVO(walletData);
    }

    public BasicSalaryTaskVO basicSalaryTask(IncomeDTO dto) {
        BasicSalaryAnchorData data = basicSalaryAnchorDao.selectByUid(dto.getUid());
        if (data == null || data.getDelete() != 0) {
            throw new CommonH5Exception(HttpCode.PARAM_ERROR);
        }
        String liveRoomId = RoomUtils.formatLiveRoomId(dto.getUid());
        String month = StringUtils.hasLength(dto.getMonth()) ? dto.getMonth() : DateSupport.ARABIAN.getStrToday().substring(0, 7);
        BasicSalaryTaskVO vo = new BasicSalaryTaskVO();
        LocalDate today = DateSupport.ARABIAN.getToday();
        LocalDate joinDate = DateSupport.ARABIAN.getLocalDate(data.getCtime() * 1000L);
        LocalDate startDate = DateSupport.parse(month + "-01");
        LocalDate endDate = startDate.with(TemporalAdjusters.lastDayOfMonth());
        List<MicTimeData> micTimeDataList = micTimeDao.getDataList(liveRoomId, dto.getUid(), DateSupport.getIntDate(startDate), DateSupport.getIntDate(endDate));
        Map<String, MicTimeData> micTimeDataMap = CollectionUtil.listToKeyMap(micTimeDataList, MicTimeData::getDate);
        List<BasicSalaryTaskVO.DailyInfo> dailyInfoList = new ArrayList<>();
        int totalMicTime = 0;
        int liveDays = 0;
        int todayStartTime = (int) (DateHelper.ARABIAN.getTodayStartTime() / 1000);
        int todayMicTime = 0;
        int workTimeLimit = ServerConfig.isProduct() ? (int) TimeUnit.HOURS.toSeconds(2) : (int) TimeUnit.MINUTES.toSeconds(2);
        if (ChronoUnit.DAYS.between(joinDate, endDate) + 1 >= 20) {
            startDate = startDate.isBefore(joinDate) ? joinDate : startDate;
            endDate = today.isBefore(endDate) ? today : endDate;
            while (!startDate.isAfter(endDate)) {
                BasicSalaryTaskVO.DailyInfo dailyInfo = new BasicSalaryTaskVO.DailyInfo();
                String strDate = DateSupport.format(startDate);
                dailyInfo.setStrDate(strDate);
                int micTime;
                if (strDate.equals(DateSupport.ARABIAN.getStrToday()) || startDate.equals(DateSupport.ARABIAN.getYesterday())) {
                    int startTime = (int) DateSupport.ARABIAN.getStartTime(startDate);
                    int endTime = (int) DateSupport.ARABIAN.getEndTime(startDate);
                    micTime = roomMicLogDao.getUpMicTotalTime(liveRoomId, dto.getUid(), startTime, endTime);
                } else {
                    MicTimeData micTimeData = micTimeDataMap.get(strDate);
                    micTime = micTimeData != null ? micTimeData.getMicTime() : 0;
                }
                dailyInfo.setTaskProgress(Math.min(micTime * 100 / workTimeLimit, 100));
                dailyInfo.setLiveTime(micTime);
                dailyInfo.setStatus(micTime >= workTimeLimit ? 1 : 0);
                dailyInfo.setLiveStrTime(getStrTime(micTime));
                dailyInfoList.add(dailyInfo);
                totalMicTime += micTime;
                liveDays += dailyInfo.getStatus();
                startDate = startDate.plusDays(1);
            }
            todayMicTime = roomMicLogDao.getUpMicTotalTime(liveRoomId, dto.getUid(), todayStartTime, DateHelper.getNowSeconds());
        }
        vo.setLiveDays(liveDays);
        vo.setLiveHours(totalMicTime / (int) TimeUnit.HOURS.toSeconds(1));
        vo.setTodayLiveStrTime(getStrTime(todayMicTime));
        vo.setTodayTaskProgress(Math.min(todayMicTime * 100 / workTimeLimit, 100));
        vo.setDailyInfoList(dailyInfoList);
        vo.setJoinTime(data.getCtime());
        return vo;
    }

    public BasicSalaryTaskVO basicSalaryTaskNew(IncomeDTO dto) {
        BasicSalaryAnchorData data = basicSalaryAnchorDao.selectByUid(dto.getUid());
        if (data == null || data.getDelete() != 0) {
            throw new CommonH5Exception(HttpCode.PARAM_ERROR);
        }
        String liveRoomId = RoomUtils.formatLiveRoomId(dto.getUid());
        String month = StringUtils.hasLength(dto.getMonth()) ? dto.getMonth() : DateSupport.ARABIAN.getStrToday().substring(0, 7);
        BasicSalaryTaskVO vo = new BasicSalaryTaskVO();
        LocalDate today = DateSupport.ARABIAN.getToday();
        LocalDate joinDate = DateSupport.ARABIAN.getLocalDate(data.getCtime() * 1000L);
        LocalDate startDate = DateSupport.parse(month + "-01");
        LocalDate endDate = startDate.with(TemporalAdjusters.lastDayOfMonth());
        List<MicTimeData> micTimeDataList = micTimeDao.getDataList(liveRoomId, dto.getUid(), DateSupport.getIntDate(startDate), DateSupport.getIntDate(endDate));
        Map<String, MicTimeData> micTimeDataMap = CollectionUtil.listToKeyMap(micTimeDataList, MicTimeData::getDate);
        List<BasicSalaryTaskVO.DailyInfo> dailyInfoList = new ArrayList<>();
        int totalMicTime = 0;
        int liveDays = 0;
        int todayMicTime = 0;
        int workTimeLimit = ServerConfig.isProduct() ? (int) TimeUnit.HOURS.toSeconds(2) : (int) TimeUnit.MINUTES.toSeconds(2);
        if (ChronoUnit.DAYS.between(joinDate, endDate) + 1 >= 20) {
            startDate = startDate.isBefore(joinDate) ? joinDate : startDate;
            endDate = today.isBefore(endDate) ? today : endDate;
            todayMicTime = micTimeDao.getTodayUpMicTime(dto.getUid(), 1);
            while (!startDate.isAfter(endDate)) {
                BasicSalaryTaskVO.DailyInfo dailyInfo = new BasicSalaryTaskVO.DailyInfo();
                String strDate = DateSupport.format(startDate);
                dailyInfo.setStrDate(strDate);
                int micTime;
                if (strDate.equals(DateSupport.ARABIAN.getStrToday())) {
                    micTime = todayMicTime;
                } else if (startDate.equals(DateSupport.ARABIAN.getYesterday())) {
                    micTime = micTimeDao.getYesterdayUpMicTime(dto.getUid(), 1);
                } else {
                    MicTimeData micTimeData = micTimeDataMap.get(strDate);
                    micTime = micTimeData != null ? micTimeData.getMicTime() : 0;
                }
                dailyInfo.setTaskProgress(Math.min(micTime * 100 / workTimeLimit, 100));
                dailyInfo.setLiveTime(micTime);
                dailyInfo.setStatus(micTime >= workTimeLimit ? 1 : 0);
                dailyInfo.setLiveStrTime(getStrTime(micTime));
                dailyInfoList.add(dailyInfo);
                totalMicTime += micTime;
                liveDays += dailyInfo.getStatus();
                startDate = startDate.plusDays(1);
            }
        }
        vo.setLiveDays(liveDays);
        vo.setLiveHours(totalMicTime / (int) TimeUnit.HOURS.toSeconds(1));
        vo.setTodayLiveStrTime(getStrTime(todayMicTime));
        vo.setTodayTaskProgress(Math.min(todayMicTime * 100 / workTimeLimit, 100));
        vo.setDailyInfoList(dailyInfoList);
        vo.setJoinTime(data.getCtime());
        return vo;
    }

    private String getStrTime(int micTime) {
        long hours = TimeUnit.SECONDS.toHours(micTime);
        long minutes = TimeUnit.SECONDS.toMinutes(micTime) % 60;
        logger.info("getStrTime. micTime={} strTime={}", micTime, String.format("%dh%dm", hours, minutes));
        return String.format("%dh %dm", hours, minutes);
    }

    public CharmDetailListVO basicSalaryTaskCharmDetail(IncomeDTO.PageDTO dto) {
        CharmDetailListVO vo = new CharmDetailListVO();
        List<MoneyDetailStatData> list = new ArrayList<>();
        List<TaskCharmLogData> pageList = taskCharmLogDao.selectPage(dto.getUid(), dto.getPage(), PAGE_SIZE);
        for (TaskCharmLogData data : pageList) {
            MoneyDetailStatData moneyDetailStatData = new MoneyDetailStatData();
            moneyDetailStatData.setTitle(data.getTitle());
            moneyDetailStatData.setTitleAr(data.getTitleAr());
            moneyDetailStatData.setDesc(data.getDesc());
            moneyDetailStatData.setDescAr(data.getDescAr());
            moneyDetailStatData.setMtime(data.getCtime());
            moneyDetailStatData.setChanged(data.getChange().longValue());
            moneyDetailStatData.setBalance(data.getBalance().longValue());
            list.add(moneyDetailStatData);
        }
        vo.setList(list);
        vo.setNextUrl(pageList.size() < PAGE_SIZE ? "" : String.valueOf(dto.getPage() + 1));
        return vo;
    }

    public FreezeCharmVO freezeCharmDetail(IncomeDTO.PageDTO dto) {
        int intStartDate = DateSupport.getIntDate(DateSupport.parse(dto.getStartDate()));
        int intEndDate = DateSupport.getIntDate(DateSupport.parse(dto.getEndDate()));
        List<FreezeCharmStatData> dataList = freezeCharmStatDao.selectList(dto.getUid(), intStartDate, intEndDate);
        long totalCharm = 0;
        List<FreezeCharmVO.Data> list = new ArrayList<>();
        if (!CollectionUtils.isEmpty(dataList)) {
            for (FreezeCharmStatData data : dataList) {
                if (data.getCharm() == 0) {
                    continue;
                }
                totalCharm += (long) data.getCharm();
                list.add(new FreezeCharmVO.Data(DateSupport.format(DateSupport.ARABIAN.parse_yyyyMMdd(data.getUnFreezeDate() + "")), (long) data.getCharm()));
            }
        }
        return new FreezeCharmVO(totalCharm, list);
    }
}
