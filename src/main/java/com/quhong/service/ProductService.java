package com.quhong.service;


import com.alibaba.fastjson.JSONObject;
import com.quhong.config.CaffeineCacheConfig;
import com.quhong.core.config.ServerConfig;
import com.quhong.core.utils.DateHelper;
import com.quhong.data.ActorData;
import com.quhong.data.dto.DataCenterPageDTO;
import com.quhong.data.dto.PageDTO;
import com.quhong.data.vo.ProductListVO;
import com.quhong.data.vo.RechargeAgentVO;
import com.quhong.data.vo.RechargeOrderVO;
import com.quhong.enums.ApiResult;
import com.quhong.enums.CustomerService;
import com.quhong.enums.HttpCode;
import com.quhong.enums.SLangType;
import com.quhong.exception.CommonException;
import com.quhong.feign.DataCenterService;
import com.quhong.handler.HttpEnvData;
import com.quhong.httpResult.PayHttpCode;
import com.quhong.image.ImageUrlGenerator;
import com.quhong.mongo.dao.ActorDao;
import com.quhong.mongo.dao.RechargeLimitUserDao;
import com.quhong.mongo.data.RechargeLimitUserData;
import com.quhong.mysql.dao.*;
import com.quhong.mysql.data.*;
import com.quhong.redis.FreezeRechargeRedis;
import com.quhong.redis.RechargeAgentRedis;
import com.quhong.redis.WhitelistRedis;
import com.quhong.room.redis.MicFrameRedis;
import com.quhong.userMonitor.UserMonitorRedis;
import com.quhong.utils.AppVersionUtils;
import com.quhong.utils.MatchUtils;
import com.quhong.vo.PageVO;
import jakarta.annotation.Resource;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import org.springframework.util.ObjectUtils;

import java.math.RoundingMode;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

@Service
public class ProductService extends AbstractPayService {

    private static final Logger logger = LoggerFactory.getLogger(ProductService.class);

    private static final String RECHARGE_TITLE = "recharge offer";
    private static final String BANNER_IMAGE_EN = "https://cloudcdn.waho.live/resource/op_sys_1697437314_banner_en.png";
    private static final String BANNER_IMAGE_AR = "https://cloudcdn.waho.live/resource/op_sys_1697437314_banner_ar.png";
    private static final String BANNER_URL = ServerConfig.isProduct() ? "https://statics.waho.live/recharge_rewards/" : "https://api.opswaho.com/recharge_rewards/";
    private static final int PAGE_SIZE = 20;
    private static final String MORE_URL = ServerConfig.isProduct() ? "https://wahopay.com/user/index.html" : "https://api.opswaho.com/pay/waho";
    private static final String MORE_ICON_EN = "https://cloudcdn.waho.live/resource/op_sys_1729048673_recharge_discounts.png";
    private static final String MORE_ICON_AR = "https://cloudcdn.waho.live/resource/op_sys_1729048673_recharge_discounts_ar.png";
    private static final String RECHARGE_AGENT_URL = ServerConfig.isProduct() ? "https://statics.waho.live/recharge-agent/" : "https://api.opswaho.com/recharge-agent/";
    private static final String NEW_RECHARGE_AGENT_URL = ServerConfig.isProduct() ? "https://statics.waho.live/agent_v2/" : "https://api.opswaho.com/agent_v2/";
    private static final String RECHARGE_AGENT_ICON_EN = "https://cloudcdn.waho.live/resource/op_sys_1729048673_recharge_agent.png";
    private static final String RECHARGE_AGENT_ICON_AR = "https://cloudcdn.waho.live/resource/op_sys_1729048673_recharge_agent_ar.png";
    private static final List<String> RECHARGE_WHITELIST = List.of("66bf7e7022c8fb4f3fde252d", "6503739ec9d5187dab8ecc46");
    // 每周5000，1316195
    private static final List<String> RECHARGE_WEEKLY_WHITELIST = List.of("67043eaf8a556d0eaba69433");

    @Resource
    private ActorDao actorDao;
    @Resource
    private SlaveHeartRecordDao slaveHeartRecordDao;
    @Resource
    private GoodsDao goodsDao;
    @Resource
    private DataCenterService dataCenterService;
    @Resource
    private RechargeDailyInfoDao rechargeDailyInfoDao;
    @Resource
    private AuditAccountDao auditAccountDao;
    @Resource
    private RechargeOrderDao rechargeOrderDao;
    @Resource
    private WhitelistRedis whitelistRedis;
    @Resource
    private FreezeRechargeRedis freezeRechargeRedis;
    @Resource
    private RechargeAgentRedis rechargeAgentRedis;
    @Resource
    private CoinSellerDao coinSellerDao;
    @Resource
    private CoinSellerUserRecordDao coinSellerUserRecordDao;
    @Resource
    private MicFrameRedis micFrameRedis;
    @Resource
    private VipInfoDao vipInfoDao;
    @Resource
    private WhiteTestDao whiteTestDao;
    @Resource
    private BannerListService bannerListService;
    @Resource
    private CoinsService coinsService;
    @Resource
    private UserMonitorRedis userMonitorRedis;
    @Resource
    private SlaveVirtualDiamondRecordDao slaveVirtualDiamondRecordDao;
    @Resource
    private RiskControlService riskControlService;
    @Resource
    private RechargeLimitUserDao rechargeLimitUserDao;

    public ProductListVO getProductList(String uid, int slang, HttpEnvData envData) {
        ActorData actor = actorDao.getActorData(uid);
        if (null == actor) {
            throw new CommonException(HttpCode.PARAM_ERROR);
        }

        boolean freezeRecharge = freezeRechargeRedis.isFreezeRecharge(actor.getUid(), actor.getTn_id());
        ProductListVO productListVo = new ProductListVO();
        // 设置金币数量、钻石数量
        ProductListVO.Balance userBalance = new ProductListVO.Balance();
        userBalance.setDiamonds(Math.max(0, actorDao.getBalance(uid)));
        userBalance.setVirtualDiamonds(actor.getVirtualDiamonds());
        userBalance.setCoins(actor.getHeartGot());
        productListVo.setBalance(userBalance);
        // 设置商品列表
        List<ProductListVO.DiamondProduct> diamondProductList = new ArrayList<>();
        if (!freezeRecharge) {
            // 设置 充值优惠
            ProductListVO.RechargeOffer rechargeOffer = new ProductListVO.RechargeOffer();
            rechargeOffer.setTitle(RECHARGE_TITLE);
            // todo 暂时不显示充值页banner
            rechargeOffer.setShowOffer(!ServerConfig.isProduct());
            rechargeOffer.setBanner(slang == 1 ? BANNER_IMAGE_EN : BANNER_IMAGE_AR);
            rechargeOffer.setUrl(BANNER_URL);
            productListVo.setRechargeOffer(rechargeOffer);
            // 审核账号不展示第三方充值入口以及充值代理入口
            if (auditAccountDao.selectOneByUid(uid) == null) {
                boolean canSeeRecharge = riskControlService.canSeeRecharge(uid);
                if (canSeeRecharge || whitelistRedis.inWhitelist(WhitelistRedis.THIRD_RECHARGE_ENTRANCE, uid)) {
                    productListVo.setMoreUrl(getMoreUrl(actor.getRid(), slang));
                    productListVo.setMoreIcon(slang == SLangType.ENGLISH ? MORE_ICON_EN : MORE_ICON_AR);
                }
                if (canSeeRecharge || whitelistRedis.inWhitelist(WhitelistRedis.RECHARGE_AGENT_LIST, uid)) {
                    productListVo.setShowRechargeAgent(1);
                    productListVo.setDlUrl(AppVersionUtils.versionCheck(139, envData) ? NEW_RECHARGE_AGENT_URL : RECHARGE_AGENT_URL);
                    productListVo.setDlIcon(slang == SLangType.ENGLISH ? RECHARGE_AGENT_ICON_EN : RECHARGE_AGENT_ICON_AR);
                }
            }
            List<GoodsData> goodsDataList = goodsDao.getProductList();
            float discount = getDiscount();
            for (GoodsData goodsData : goodsDataList) {
                ProductListVO.DiamondProduct diamondProduct = new ProductListVO.DiamondProduct();
                diamondProduct.setDiamonds(goodsData.getDiamonds() + getBounds(goodsData.getDiamonds(), discount));
                diamondProduct.setOrigin("$" + goodsData.getUsdPrice());
                diamondProduct.setPid(goodsData.getProductId());
                diamondProductList.add(diamondProduct);
            }
        }
        productListVo.setList(diamondProductList);
        productListVo.setCoinProductList(coinsService.getCoinsProducts());
        productListVo.setWhatsapp(CustomerService.CUSTOMER_SERVICE.uid);
        productListVo.setBannerList(bannerListService.getBannerList(envData, BannerListService.WALLET_BANNER));
        return productListVo;
    }

    private String getMoreUrl(int rid, int slang) {
        return MORE_URL + "?rid=" + rid + "&slang=" + slang;
    }

    public PageVO<MoneyDetailStatData> apiMoneyDetail(String uid, int aType, int page) {
        if (page < 1) {
            page = 1;
        }
        PageVO<MoneyDetailStatData> vo = new PageVO<>(new ArrayList<>(PAGE_SIZE));
        DataCenterPageDTO req = new DataCenterPageDTO();
        req.setUid(uid);
        req.setPage(page);
        req.setSize(PAGE_SIZE);
        req.setaType(aType);
        ApiResult<List<MoneyDetail>> result = dataCenterService.esMoneyDetail(req);
        if (result.isError()) {
            logger.info("uid={} page={} code={} msg={}", uid, page, result.getCode().getCode(), result.getCode().getMsg());
            throw new CommonException(HttpCode.PARAM_ERROR);
        } else {
            List<MoneyDetail> moneyDetailList = result.getData();
            vo.setNextUrl(moneyDetailList.size() < PAGE_SIZE ? "" : String.valueOf(page + 1));
            for (MoneyDetail item : moneyDetailList) {
                MoneyDetailStatData moneyDetailStatData = new MoneyDetailStatData();
                moneyDetailStatData.setTitle(item.getAtype() == 777 ? item.getTitle() + "_1" : item.getTitle());
                moneyDetailStatData.setDesc(item.getDesc());
                moneyDetailStatData.setMtime(item.getMtime());
                moneyDetailStatData.setChanged(item.getChanged());
                moneyDetailStatData.setBalance(Math.max(0, item.getBalance()));
                vo.getList().add(moneyDetailStatData);
            }
            return vo;
        }
    }

    public PageVO<MoneyDetailStatData> coinDetail(String uid, int page) {
        if (page < 1) {
            page = 1;
        }
        PageVO<MoneyDetailStatData> vo = new PageVO<>(new ArrayList<>(PAGE_SIZE));
        List<HeartRecordData> dataList = slaveHeartRecordDao.getDetails(uid, page, PAGE_SIZE);
        for (HeartRecordData item : dataList) {
            MoneyDetailStatData data = new MoneyDetailStatData();
            data.setChanged(item.getChanged());
            data.setTitle(item.getTitle());
            data.setDesc(item.getRemark());
            data.setMtime(item.getCtime());
            vo.getList().add(data);
        }
        vo.setNextUrl(dataList.size() < PAGE_SIZE ? "" : String.valueOf(page + 1));
        return vo;
    }

    public PageVO<MoneyDetailStatData> virtualDiamondDetail(String uid, int page) {
        if (page < 1) {
            page = 1;
        }
        PageVO<MoneyDetailStatData> vo = new PageVO<>(new ArrayList<>(PAGE_SIZE));
        List<VirtualDiamondRecordData> dataList = slaveVirtualDiamondRecordDao.getDetails(uid, page, PAGE_SIZE);
        for (VirtualDiamondRecordData item : dataList) {
            MoneyDetailStatData data = new MoneyDetailStatData();
            data.setChanged(item.getChanged());
            data.setTitle(item.getTitle());
            data.setDesc(item.getRemark());
            data.setMtime(item.getCtime());
            vo.getList().add(data);
        }
        vo.setNextUrl(dataList.size() < PAGE_SIZE ? "" : String.valueOf(page + 1));
        return vo;
    }

    @Override
    void onChargeFailure(String bizId, String productId, String uid) {

    }

    public PageVO<RechargeOrderVO> rechargeOrder(PageDTO dto) {
        String uid = dto.getUid();
        int page = dto.getPage();
        ActorData actorData = actorDao.getActorDataFromCache(uid);
        if (freezeRechargeRedis.isFreezeRecharge(uid, actorData.getTn_id())) {
            return new PageVO<>();
        }
        List<RechargeOrderData> orderDataList = rechargeOrderDao.selectOrderList(uid, page, PAGE_SIZE);
        PageVO<RechargeOrderVO> pageVO = new PageVO<>(new ArrayList<>(orderDataList.size()));
        for (RechargeOrderData orderData : orderDataList) {
            RechargeOrderVO vo = new RechargeOrderVO();
            vo.setOrderStatus(orderData.getOrderStatus());
            vo.setOrderAmount(ObjectUtils.isEmpty(orderData.getTradingPrice()) ? "USD " + orderData.getUsdPrice() : orderData.getTradingPrice());
            vo.setOrderNumber(orderData.getOrderId());
            vo.setProductName(orderData.getDiamonds() + " diamonds");
            vo.setProductId(orderData.getProductName());
            vo.setPayMethod(orderData.getPayMethod());
            vo.setPayMethodName(orderData.getPayChannel().replaceAll("\\d", ""));
            vo.setCtime(orderData.getCtime());
            // epay订单id特殊处理，替换为付款地址
            if (!ObjectUtils.isEmpty(orderData.getExtraJson())) {
                JSONObject jsonObject = JSONObject.parseObject(orderData.getExtraJson());
                String trc20Address = jsonObject.getString("trc20Address");
                String productName = jsonObject.getString("productName");
                if (!ObjectUtils.isEmpty(trc20Address)) {
                    vo.setOrderNumber("trc20:" + trc20Address);
                }
                if (!ObjectUtils.isEmpty(productName)) {
                    vo.setProductName(productName);
                }
            }
            pageVO.getList().add(vo);
        }
        pageVO.setNextUrl(page, PAGE_SIZE);
        return pageVO;
    }

    public Object preRecharge(String uid) {
        RechargeLimitUserData limitUserData = rechargeLimitUserDao.findByUid(uid);
        if (limitUserData != null) {
            int startTime = 0;
            switch (limitUserData.getLimitType()) {
                case 0 -> startTime = (int) (DateHelper.ARABIAN.getTodayStartTime() / 1000);
                case 1 -> startTime = DateHelper.ARABIAN.getWeekStartTime();
                case 2 -> startTime = (int) (DateHelper.ARABIAN.getFirstMonthDay(new Date()).getTime() / 1000);
                default -> {
                }
            }
            if (rechargeDailyInfoDao.selectUserRechargeAmount(uid, startTime, DateHelper.getNowSeconds()) >= limitUserData.getAmountLimit() || rechargeOrderDao.selectRefundCount(uid) > 0) {
                throw new CommonException(PayHttpCode.CANNOT_RECHARGE);
            }
        } else {
            // 每个自然周非审核版本谷歌、苹果累积充值100美金限制充值
            int weekStartTime = DateHelper.ARABIAN.getWeekStartTime();
            if (rechargeDailyInfoDao.selectUserRechargeAmount(uid, weekStartTime, DateHelper.getNowSeconds()) >= 100 || rechargeOrderDao.selectRefundCount(uid) > 0) {
                throw new CommonException(PayHttpCode.CANNOT_RECHARGE);
            }
        }
        return null;
    }

    public void makeRechargeAgentList() {
        List<SoldDiamondsData> soldDiamondsDataList = coinSellerUserRecordDao.getSoldDiamondsSum((int) (DateHelper.getNowSeconds() - TimeUnit.DAYS.toSeconds(60)));
        if (!CollectionUtils.isEmpty(soldDiamondsDataList)) {
            Map<String, Long> soldDiamondsMap = soldDiamondsDataList.stream().collect(Collectors.toMap(SoldDiamondsData::getUid, SoldDiamondsData::getDiamondSum));
            soldDiamondsMap.put("64f2a3ee2bb89646c57e07b2", 100000000L);
            rechargeAgentRedis.addRechargeAgents(soldDiamondsMap);
        }
    }

    @Cacheable(value = "cache", cacheManager = CaffeineCacheConfig.EXPIRE_1M_AFTER_WRITE,
            key = "#root.method.name+T(org.springframework.util.StringUtils).arrayToDelimitedString(args,'-')")
    public PageVO<RechargeAgentVO> rechargeAgentList(int page) {
        List<RechargeAgentVO> list = new ArrayList<>();
        page = Math.max(page, 1);
        int pageSize = 30;
        int start = (page - 1) * pageSize;
        int end = page * pageSize - 1;
        Map<String, Long> rechargeAgentsMap = rechargeAgentRedis.getRechargeAgentsMap(start, end);
        for (Map.Entry<String, Long> entry : rechargeAgentsMap.entrySet()) {
            String aid = entry.getKey();
            ActorData actorData = actorDao.getActorData(aid);
            if (actorData == null) {
                logger.error("can not find actor. uid={}", aid);
                continue;
            }
            CoinSellerData coinSellerData = coinSellerDao.selectByUid(aid);
            if (coinSellerData == null || coinSellerData.getStatus() == 2) {
                continue;
            }
            if (whiteTestDao.isMemberByType(aid, WhiteTestDao.WHITE_TYPE_RID)) {
                continue;
            }
            if (userMonitorRedis.isMember(aid)) {
                continue;
            }
            RechargeAgentVO vo = new RechargeAgentVO();
            vo.setUid(aid);
            vo.setName(actorData.getName());
            vo.setHead(ImageUrlGenerator.generateRoomUserUrl(actorData.getHead()));
            vo.setMicFrame(micFrameRedis.getMicSourceFromCache(aid));
            vo.setVipLevel(vipInfoDao.getIntVipLevelFromCache(aid));
            vo.setStrBalance(coinSellerData.getBalance() > 22000000 ? "22M+" : MatchUtils.formatDevotes(coinSellerData.getBalance(), 2, RoundingMode.FLOOR));
            list.add(vo);
        }
        return new PageVO<>(list, rechargeAgentsMap.size() >= pageSize ? (page + 1) + "" : "");
    }
}
