package com.quhong.service.impl;

import com.quhong.data.FireBaseLoginVerifyData;
import com.quhong.data.RegisterOrLoginContext;
import com.quhong.service.AbstractLoginService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;

@Service
public class FireBaseMailLoginService extends AbstractLoginService {
    private static final Logger logger = LoggerFactory.getLogger(FireBaseMailLoginService.class);

    @Override
    public RegisterOrLoginContext beforeLogin(RegisterOrLoginContext context) {
        FireBaseLoginVerifyData data = thirdApiLoginService.getFireBase(context);
        context.setThirdUid(data.getUser_id());
        context.setRealEmail(data.getEmail());
        return context;
    }

    @Override
    public RegisterOrLoginContext afterLogin(RegisterOrLoginContext context) {
        return null;
    }
}
