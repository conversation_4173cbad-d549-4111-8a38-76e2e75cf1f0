package com.quhong.service;

import com.alibaba.fastjson.JSONObject;
import com.quhong.analysis.CreateGameLogEvent;
import com.quhong.analysis.EventDTO;
import com.quhong.analysis.EventReport;
import com.quhong.analysis.GameRecordLogEvent;
import com.quhong.core.concurrency.BaseTaskFactory;
import com.quhong.core.concurrency.queues.SlowTaskQueue;
import com.quhong.core.concurrency.tasks.Task;
import com.quhong.core.timers.LoopTask;
import com.quhong.core.timers.TimerService;
import com.quhong.core.utils.DateHelper;
import com.quhong.dailyTask.DailyTaskService;
import com.quhong.data.ActorData;
import com.quhong.data.DailyTaskMqData;
import com.quhong.data.RoomRoleData;
import com.quhong.data.TurntableGameInfo;
import com.quhong.data.dto.MoneyDetailReq;
import com.quhong.dto.TurntableDTO;
import com.quhong.enums.*;
import com.quhong.exception.GameException;
import com.quhong.feign.DataCenterService;
import com.quhong.image.ImageUrlGenerator;
import com.quhong.mongo.dao.ActorDao;
import com.quhong.mongo.dao.RoomMemberDao;
import com.quhong.mq.MqSenderService;
import com.quhong.msg.obj.LuckyWheelGameObject;
import com.quhong.msg.obj.LuckyWheelPlayer;
import com.quhong.msg.obj.UNameObject;
import com.quhong.msg.room.LuckyWheelRoomPushMsg;
import com.quhong.mysql.config.WahoLogMySQLBean;
import com.quhong.mysql.dao.TurntablePlayerRecordDao;
import com.quhong.mysql.dao.TurntableRecordDao;
import com.quhong.mysql.data.TurntablePlayerRecordData;
import com.quhong.mysql.data.TurntableRecordData;
import com.quhong.redis.FamilyDevoteHelperRedis;
import com.quhong.redis.TurntableGameRedis;
import com.quhong.redis.WhitelistRedis;
import com.quhong.room.RoomWebSender;
import com.quhong.room.cache.RoomActorCache;
import com.quhong.room.data.RoomActorDetailData;
import com.quhong.utils.K8sUtils;
import com.quhong.utils.WalletUtils;
import com.quhong.vo.CheckTurntableGameVO;
import com.quhong.vo.CreateTurntableVO;
import com.quhong.vo.StartTurntableVO;
import com.quhong.vo.TurntableGameVO;
import jakarta.annotation.PostConstruct;
import jakarta.annotation.Resource;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import java.util.*;

/**
 * 转盘游戏
 *
 * <AUTHOR>
 * @date 2022/10/13
 */
@Service
public class TurntableService extends SlowTaskQueue {

    private static final Logger logger = LoggerFactory.getLogger(TurntableService.class);

    private static final String JOIN_GAME_TITLE = "join turntable game";

    private static final String TAKE_A_PERCENTAGE_TITLE = "take a percentage in turntable game";  // 房主抽成
    private static final String WIN_TURNTABLE_GAME_TITLE = "win turntable game";  // 赢家奖励
    private static final String RETURN_TITLE = "return in turntable game";  // 退还入参费
    private static final int A_TYPE = 40;

    private static final int MAX_WAIT_TIME = 5 * 60; // 最大等待时间 5 分钟
    private static final int LOOP_TIME = 800; // 每500ms扫描一下是否有结束的游戏

    @Resource
    private ActorDao actorDao;
    @Resource
    private TurntableGameRedis turntableGameRedis;
    @Resource
    private DataCenterService dataCenterService;
    @Resource
    private RoomActorCache roomActorCache;
    @Resource
    private RoomWebSender roomWebSender;
    @Resource
    private DailyTaskService dailyTaskService;
    @Resource
    private EventReport eventReport;
    @Resource
    private RoomMemberDao roomMemberDao;
    @Resource
    private TurntableRecordDao turntableRecordDao;
    @Resource
    private TurntablePlayerRecordDao turntablePlayerRecordDao;
    @Resource
    private MqSenderService mqSenderService;
    @Resource
    private FamilyDevoteHelperRedis familyDevoteHelperRedis;
    @Resource
    private MysteryService mysteryService;
    @Resource
    private K8sUtils k8sUtils;
    @Resource
    private WhitelistRedis whitelistRedis;

    @PostConstruct
    public void postInit() {
        TimerService.getService().addDelay(new LoopTask(this, LOOP_TIME) {
            @Override
            protected void execute() {
                if (k8sUtils.isMasterFromCache()) {
                    onTick1();
                    onTick2();
                }
            }
        });
    }

    private void onTick1() {
        Set<String> gameIds = turntableGameRedis.getRunningEndGameIds(DateHelper.getNowSeconds());
        if (CollectionUtils.isEmpty(gameIds)) {
            return;
        }
        logger.info("turntable game settle accounts. gameIds.size={} gameIds={}", gameIds.size(), Arrays.toString(gameIds.toArray()));
        for (String gameId : gameIds) {
            turntableGameRedis.removeGameTimerRunning(gameId);
            BaseTaskFactory.getFactory().addSlow(new Task() {
                @Override
                protected void execute() {
                    // 游戏结算
                    gameSettleAccounts(gameId);
                }
            });
        }
    }

    private void onTick2() {
        Set<String> gameIds = turntableGameRedis.getWaitingEndGameIds(DateHelper.getNowSeconds());
        if (CollectionUtils.isEmpty(gameIds)) {
            return;
        }
        logger.info("dismiss turntable game. gameIds.size={} gameIds={}", gameIds.size(), Arrays.toString(gameIds.toArray()));
        for (String gameId : gameIds) {
            turntableGameRedis.removeGameTimerWaiting(gameId);
            BaseTaskFactory.getFactory().addSlow(new Task() {
                @Override
                protected void execute() {
                    // 结束等待超时游戏
                    dismissTurntableGame(gameId, true);
                }
            });
        }
    }

    /**
     * 创建转盘游戏
     */
    public CreateTurntableVO createGame(TurntableDTO reqDTO) {
        String uid = reqDTO.getUid();
        String roomId = reqDTO.getRoom_id();
        ActorData actorData = actorDao.getActorDataFromCache(uid);
        if (actorData == null) {
            logger.error("The actor not found. uid={}", uid);
            throw new GameException(GameHttpCode.THE_ACTOR_NOT_FOUND);
        }
        RoomRoleData roleData = roomMemberDao.getRoleData(roomId, uid);
        if (!roleData.isAdmin()) {
            logger.info("Only room owner and admin can create the game. uid={}, roomId={}", uid, roomId);
            throw new GameException(GameHttpCode.OWNER_AND_ADMIN_CAN_CREATE);
        }
        String gameId = turntableGameRedis.getGameIdFromRedis(roomId);
        TurntableGameInfo gameInfo;
        if (!StringUtils.isEmpty(gameId)) {
            // 已经存在转盘游戏了
            gameInfo = turntableGameRedis.getGameInfo(gameId);
            logger.info("The game has already existed. gameId={}", gameId);
            throw new GameException(GameHttpCode.GAME_HAS_ALREADY_EXISTED, gameInfo);
        }
        if (reqDTO.getJoin_self() != 0 && reqDTO.getJoin_beans() != 0) {
            // 扣除参加转盘游戏的费用
            deductCost(uid, roomId, reqDTO.getJoin_beans());
        }
        gameInfo = buildTurntableGameInfo(uid, roomId, reqDTO.getJoin_beans(), reqDTO.getMax_players(), reqDTO.getJoin_self());
        turntableGameRedis.setGameInfo(gameInfo);
        turntableGameRedis.setGameIdInRedis(roomId, gameInfo.get_id());
        int endTime = DateHelper.getNowSeconds() + MAX_WAIT_TIME;
        turntableGameRedis.setGameTimerWaiting(gameInfo.get_id(), endTime);
        // 发送创建游戏im
        sendLuckyWheelRoomPushMsg(uid, roomId, LuckyWheelState.CREATE, gameInfo);
        // 用户创建游戏时上报数数
        doReportEvent(gameInfo, reqDTO.getJoin_self());
        return new CreateTurntableVO(gameInfo);
    }

    /**
     * 加入转盘游戏
     */
    public TurntableGameVO joinGame(TurntableDTO reqDTO) {
        String uid = reqDTO.getUid();
        String gameId = reqDTO.getGame_id();
        ActorData actorData = actorDao.getActorDataFromCache(uid);
        if (actorData == null) {
            logger.error("The actor not found. uid={}", uid);
            throw new GameException(GameHttpCode.THE_ACTOR_NOT_FOUND);
        }
        TurntableGameInfo gameInfo = turntableGameRedis.getGameInfo(gameId);
        if (gameInfo == null) {
            logger.error("The game is not found. uid={} gameId={}", uid, gameId);
            throw new GameException(GameHttpCode.THE_GAME_IS_NOT_FOUND);
        }
        if (whitelistRedis.isInvisibilityEnterRoom(gameInfo.getRoomId(), reqDTO.getUid())) {
            // 隐身进房无法操作
            throw new GameException(HttpCode.INVISIBLE_ACCOUNT_CANNOT_BE_OPERATED);
        }
        // 校验游戏是否正在进行
        if (gameInfo.getStatus() == TurntableConstant.GAME_RUNNING) {
            logger.info("The game is running. uid={} gameId={}", uid, gameId);
            throw new GameException(GameHttpCode.THE_GAME_IS_RUNNING);
        }
        // 校验游戏是否已经结束
        if (gameInfo.getStatus() == TurntableConstant.GAME_END) {
            logger.info("The game has been cancelled. uid={} gameId={}", uid, gameId);
            throw new GameException(GameHttpCode.GAME_HAS_BEEN_CANCELLED);
        }
        List<TurntableGameInfo.Actor> players = gameInfo.getPlayers();
        if (CollectionUtils.isEmpty(players)) {
            players = new ArrayList<>();
        }
        // 校验人数是否大于最大玩家数
        if (players.size() >= gameInfo.getMaxPlayers()) {
            logger.info("The number of players reach max. uid={} roomId={} gameId={}", uid, gameInfo.getRoomId(), gameId);
            throw new GameException(GameHttpCode.PLAYERS_NUM_REACH_MAX);
        }
        // 校验是否已经加入游戏
        if (players.stream().anyMatch(a -> a.get_id().equals(uid))) {
            logger.info("You have joined the game. uid={} roomId={} gameId={}", uid, gameInfo.getRoomId(), gameId);
            throw new GameException(GameHttpCode.YOU_HAVE_JOINED_THE_GAME);
        }
        players.add(getActorInfo(uid));
        gameInfo.setPlayers(players);
        gameInfo.setMtime(DateHelper.getNowSeconds());
        // 扣入场费用
        if (gameInfo.getJoinBeans() != 0) {
            deductCost(uid, gameInfo.getRoomId(), gameInfo.getJoinBeans());
        }
        gameInfo.setTotal_beans(gameInfo.getPlayers().size() * gameInfo.getJoinBeans());
        turntableGameRedis.setGameInfo(gameInfo);
        String creator = gameInfo.getCreator();
        if (players.size() >= gameInfo.getMaxPlayers()) {
            // 人数已满，游戏自动开始
            startGame(creator, gameInfo);
        } else {
            // 发送创建游戏im
            sendLuckyWheelRoomPushMsg(uid, gameInfo.getRoomId(), LuckyWheelState.JOIN, gameInfo);
        }
        TurntableGameVO vo = new TurntableGameVO();
        vo.setGame(gameInfo);
        return vo;
    }

    /**
     * 开始转盘游戏
     */
    public StartTurntableVO startGame(TurntableDTO reqDTO) {
        String uid = reqDTO.getUid();
        String gameId = reqDTO.getGame_id();
        TurntableGameInfo gameInfo = turntableGameRedis.getGameInfo(gameId);
        if (gameInfo == null) {
            logger.error("The game is not found. uid={} gameId={}", uid, gameId);
            throw new GameException(GameHttpCode.THE_GAME_IS_NOT_FOUND);
        }
        String roomId = gameInfo.getRoomId();
        // 校验游戏是否正在进行
        if (gameInfo.getStatus() == TurntableConstant.GAME_RUNNING) {
            logger.info("The game is running. uid={} roomId={} gameId={}", uid, roomId, gameId);
            throw new GameException(GameHttpCode.THE_GAME_IS_RUNNING);
        }
        // 校验游戏是否已结束
        if (gameInfo.getStatus() == TurntableConstant.GAME_END) {
            logger.info("The game has been cancelled. uid={} gameId={}", uid, gameId);
            throw new GameException(GameHttpCode.GAME_HAS_BEEN_CANCELLED);
        }
        // 游戏创建者才能主动开始游戏
        if (!gameInfo.getCreator().equals(uid)) {
            logger.info("you can't start the game. uid={} gameId={}", uid, gameId);
            throw new GameException(GameHttpCode.CAN_NOT_START_THE_GAME);
        }
        // 校验人数是否满足最小开始人数
        if (CollectionUtils.isEmpty(gameInfo.getPlayers()) || gameInfo.getPlayers().size() < gameInfo.getMinPlayers()) {
            logger.info("Please wait other players joining first. uid={} roomId={} gameId={}", uid, roomId, gameId);
            throw new GameException(GameHttpCode.WAIT_OTHER_PLAYERS_JOINING);
        }
        StartTurntableVO vo = new StartTurntableVO();
        vo.setGame(startGame(uid, gameInfo));
        vo.setWhellLeftnums(10);
        return vo;
    }

    /**
     * 转盘游戏等待中点击关闭
     */
    public void closeAtWaiting(TurntableDTO reqDTO) {
        ActorData actorData = actorDao.getActorDataFromCache(reqDTO.getUid());
        if (actorData == null) {
            logger.error("The actor not found. uid={}", reqDTO.getUid());
            throw new GameException(GameHttpCode.THE_ACTOR_NOT_FOUND);
        }
        TurntableGameInfo gameInfo = turntableGameRedis.getGameInfo(reqDTO.getGame_id());
        if (gameInfo == null) {
            return;
        }
        // 防重复调用
        String gameId = turntableGameRedis.getGameIdFromRedis(gameInfo.getRoomId());
        if (StringUtils.isEmpty(gameId) || !gameId.equals(reqDTO.getGame_id())) {
            return;
        }
        // 不能关闭非等待状态的转盘
        if (gameInfo.getStatus() != TurntableConstant.GAME_WAITING) {
            logger.info("The game is running. uid={} gameId={}", reqDTO.getUid(), reqDTO.getGame_id());
            throw new GameException(GameHttpCode.THE_GAME_IS_RUNNING);
        }
        // 同级身份可关闭
        if (!reqDTO.getUid().equals(gameInfo.getCreator())) {
            RoomRoleData reqRole = roomMemberDao.getRoleData(gameInfo.getRoomId(), reqDTO.getUid());
            RoomRoleData creatorRole = roomMemberDao.getRoleData(gameInfo.getRoomId(), gameInfo.getCreator());
            if (reqRole.getRole() <= creatorRole.getRole()) {
                logger.info("close game. reqUid={} creator={} gameId={}",
                        reqDTO.getUid(), gameInfo.getCreator(), reqDTO.getGame_id());
            } else {
                logger.info("Yor are not the creator. uid={} gameId={}", reqDTO.getUid(), reqDTO.getGame_id());
                throw new GameException(GameHttpCode.YOU_ARE_NOT_THE_CREATOR);
            }
        }
        turntableGameRedis.removeGameId(gameInfo.getRoomId());
        turntableGameRedis.removeGameTimerWaiting(gameInfo.get_id());
        BaseTaskFactory.getFactory().addSlow(new Task() {
            @Override
            protected void execute() {
                // 结束等待游戏
                dismissTurntableGame(gameId, false);
            }
        });
        sendLuckyWheelRoomPushMsg(reqDTO.getUid(), gameInfo.getRoomId(), LuckyWheelState.CLOSE, gameInfo);
    }

    /**
     * 游戏过程中刷新游戏的状态
     */
    public TurntableGameVO refreshGame(TurntableDTO reqDTO) {
        TurntableGameInfo gameInfo = getGameInfo("", reqDTO.getGame_id());
        if (gameInfo == null) {
            logger.error("The game is not found. gameId={}", reqDTO.getGame_id());
            throw new GameException(GameHttpCode.THE_GAME_IS_NOT_FOUND);
        }
        TurntableGameVO vo = new TurntableGameVO();
        vo.setGame(gameInfo);
        return vo;
    }

    /**
     * 进入房间时，请求当前房间是否存在转盘游戏
     */
    public StartTurntableVO checkGameForEnterRoom(TurntableDTO reqDTO) {
        StartTurntableVO vo = new StartTurntableVO();
        vo.setWhellLeftnums(10);
        TurntableGameInfo gameInfo = getGameInfo(reqDTO.getRoom_id(), "");
        if (gameInfo != null) {
            vo.setGame(gameInfo);
            return vo;
        }
        return vo;
    }

    private TurntableGameInfo startGame(String uid, TurntableGameInfo gameInfo) {
        int nowTime = DateHelper.getNowSeconds();
        buildStartGameInfo(nowTime, gameInfo);
        turntableGameRedis.setGameInfo(gameInfo);
        int endTime = gameInfo.getLosers().size() * 9 + nowTime;
        turntableGameRedis.setGameTimerRunning(gameInfo.get_id(), endTime);
        turntableGameRedis.removeGameTimerWaiting(gameInfo.get_id());
        // 发送im
        sendLuckyWheelRoomPushMsg(uid, gameInfo.getRoomId(), LuckyWheelState.START, gameInfo);
        for (TurntableGameInfo.Actor player : gameInfo.getPlayers()) {
            // 玩转盘游戏的每日任务
            dailyTaskService.sendToMq(new DailyTaskMqData(player.get_id(), 10, DateHelper.ARABIAN.formatDateInDay2(), 1));
        }
        return gameInfo;
    }

    private void doReportEvent(TurntableGameInfo gameInfo, int joinSelf) {
        CreateGameLogEvent event = new CreateGameLogEvent();
        event.setUid(gameInfo.getCreator());
        event.setIs_robot(0);
        event.setRoom_id(gameInfo.getRoomId());
        event.setGame_id(gameInfo.get_id());
        event.setGame_type(3);
        event.setCost_type(gameInfo.getJoinBeans() == 0 ? 0 : 2);
        event.setCost_number(joinSelf != 0 ? gameInfo.getJoinBeans() : 0);
        event.setCtime(DateHelper.getNowSeconds());
        eventReport.track(new EventDTO(event));
    }

    private void buildStartGameInfo(int nowTime, TurntableGameInfo gameInfo) {
        List<TurntableGameInfo.Actor> players = gameInfo.getPlayers();
        gameInfo.setLastStartAt(nowTime);
        gameInfo.setStatus(TurntableConstant.GAME_RUNNING);
        List<TurntableGameInfo.Actor> copyPlayers = new ArrayList<>(players);
        Collections.shuffle(copyPlayers);
        TurntableGameInfo.Actor winner = new TurntableGameInfo.Actor();
        BeanUtils.copyProperties(copyPlayers.get(0), winner);
        gameInfo.setWinner(winner);
        gameInfo.setLosers(getPlayerList(copyPlayers.subList(1, copyPlayers.size())));
        gameInfo.setTotal_beans(gameInfo.getPlayers().size() * gameInfo.getJoinBeans());
        gameInfo.setWin_beans(getWinBeans(gameInfo.getWinner().get_id(), gameInfo.getTotal_beans()));
        gameInfo.setLoop(1);
    }

    private List<TurntableGameInfo.Actor> getPlayerList(List<TurntableGameInfo.Actor> list) {
        List<TurntableGameInfo.Actor> players = new ArrayList<>();
        for (TurntableGameInfo.Actor actor : list) {
            TurntableGameInfo.Actor player = new TurntableGameInfo.Actor();
            BeanUtils.copyProperties(actor, player);
            players.add(player);
        }
        return players;
    }

    private void sendLuckyWheelRoomPushMsg(String uid, String roomId, int status, TurntableGameInfo gameInfo) {
        sendLuckyWheelRoomPushMsg(uid, roomId, status, gameInfo, false);
    }

    /**
     * 发送创建游戏im
     */
    private void sendLuckyWheelRoomPushMsg(String uid, String roomId, int status, TurntableGameInfo gameInfo, boolean isAutoClose) {
        LuckyWheelRoomPushMsg msg = new LuckyWheelRoomPushMsg();
        LuckyWheelGameObject gameObject = buildLuckyWheelGameObject(gameInfo);
        msg.setGame(gameObject);
        msg.setCurrent_time(System.currentTimeMillis());
        msg.setGame_status(status);
        msg.setCreatorUname(generateUnameObject(uid, roomId));
        msg.setAutoClose(isAutoClose ? 1 : 0);
        roomWebSender.sendRoomWebMsg(roomId, "", msg, true);
    }

    private LuckyWheelGameObject buildLuckyWheelGameObject(TurntableGameInfo gameInfo) {
        LuckyWheelGameObject gameObject = new LuckyWheelGameObject();
        gameObject.set_id(gameInfo.get_id());
        gameObject.setPlayers(toLuckyWheelPlayerList(gameInfo.getPlayers()));
        gameObject.setLosers(toLuckyWheelPlayerList(gameInfo.getLosers()));
        gameObject.setStatus(gameInfo.getStatus());
        gameObject.setJoin_beans(WalletUtils.diamondsForDisplay(gameInfo.getJoinBeans()));
        gameObject.setMax_players(gameInfo.getMaxPlayers());
        gameObject.setMin_players(gameInfo.getMinPlayers());
        gameObject.setTotal_beans(WalletUtils.diamondsForDisplay(gameInfo.getTotal_beans()));
        if (gameInfo.getWinner() != null) {
            LuckyWheelPlayer winner = new LuckyWheelPlayer();
            winner.fillFrom((JSONObject) JSONObject.toJSON(gameInfo.getWinner()));
            gameObject.setWinner(winner);
        } else {
            gameObject.setWinner(null);
        }
        gameObject.setWin_beans(WalletUtils.diamondsForDisplay(gameInfo.getWin_beans()));
        gameObject.setLoop(gameInfo.getLoop());
        gameObject.setCreator(gameInfo.getCreator());
        return gameObject;
    }

    private List<LuckyWheelPlayer> toLuckyWheelPlayerList(List<TurntableGameInfo.Actor> players) {
        List<LuckyWheelPlayer> list = new ArrayList<>();
        if (CollectionUtils.isEmpty(players)) {
            return list;
        }
        for (TurntableGameInfo.Actor actor : players) {
            LuckyWheelPlayer wheelPlayer = new LuckyWheelPlayer();
            wheelPlayer.fillFrom((JSONObject) JSONObject.toJSON(actor));
            list.add(wheelPlayer);
        }
        return list;
    }

    /**
     * 构建UNameObject
     */
    private UNameObject generateUnameObject(String uid, String roomId) {
        RoomActorDetailData detailData = roomActorCache.getData(roomId, uid, false);
        if (detailData == null) {
            logger.info("can not find room actor data. roomId={} uid={}", roomId, uid);
            return null;
        }
        return detailData.toUNameObject();
    }

    /**
     * 扣除入场费用
     */
    private void deductCost(String uid, String roomId, int joinBeans) {
        MoneyDetailReq moneyDetailReq = new MoneyDetailReq();
        moneyDetailReq.setRandomId();
        moneyDetailReq.setRoomId(roomId);
        moneyDetailReq.setUid(uid);
        moneyDetailReq.setAtype(A_TYPE);
        moneyDetailReq.setChanged(-joinBeans);
        moneyDetailReq.setTitle(JOIN_GAME_TITLE);
        moneyDetailReq.setDesc("");
        ApiResult<String> result = dataCenterService.reduceBeans(moneyDetailReq);
        if (result.isError()) {
            if (1 == result.getCode().getCode()) {
                logger.info("diamonds are not enough. uid={} joinBeans={}", uid, joinBeans);
                throw new GameException(GameHttpCode.DIAMOND_NOT_ENOUGH);
            }
            logger.error("reduce beans error, msg={}", result.getCode().getMsg());
            throw new GameException(GameHttpCode.SERVER_ERROR);
        }
    }

    /**
     * 异步下发钻石
     */
    private void asyncChargeDiamonds(String uid, String roomId, int changed, String title) {
        MoneyDetailReq moneyDetailReq = new MoneyDetailReq();
        moneyDetailReq.setRandomId();
        moneyDetailReq.setRoomId(roomId);
        moneyDetailReq.setUid(uid);
        moneyDetailReq.setAtype(A_TYPE);
        moneyDetailReq.setChanged(changed);
        moneyDetailReq.setTitle(title);
        moneyDetailReq.setDesc("");
        mqSenderService.asyncChargeDiamonds(moneyDetailReq);
    }

    /**
     * 构建转盘游戏信息
     */
    private TurntableGameInfo buildTurntableGameInfo(String uid, String roomId, int joinBeans, int maxPlayers, int joinSelf) {
        TurntableGameInfo data = new TurntableGameInfo();
        data.set_id(UUID.randomUUID().toString().replace("-", ""));
        data.setCreator(uid);
        data.setRoomId(roomId);
        data.setJoinBeans(joinBeans);
        data.setMinPlayers(2);
        data.setMaxPlayers(maxPlayers);
        if (joinSelf != 0) {
            data.setPlayers(Collections.singletonList(getActorInfo(uid)));
            data.setTotal_beans(data.getPlayers().size() * data.getJoinBeans());
        }
        int nowSeconds = DateHelper.getNowSeconds();
        data.setStatus(TurntableConstant.GAME_WAITING);
        data.setCtime(nowSeconds);
        data.setMtime(nowSeconds);
        return data;
    }

    /**
     * 获取游戏信息
     */
    private TurntableGameInfo getGameInfo(String roomId, String gameId) {
        if (StringUtils.isEmpty(gameId)) {
            gameId = turntableGameRedis.getGameIdFromRedis(roomId);
        }
        TurntableGameInfo gameInfo = turntableGameRedis.getGameInfo(gameId);
        if (gameInfo != null) {
            int lastStart = gameInfo.getLastStartAt();
            List<TurntableGameInfo.Actor> losers = gameInfo.getLosers();
            int loop;
            if (lastStart > 1 && !CollectionUtils.isEmpty(losers)) {
                // 已经开始游戏了
                loop = ((DateHelper.getNowSeconds() - lastStart) / 9) + 1;
                if (loop >= losers.size()) {
                    loop = losers.size();
                }
                if (loop <= 0) {
                    loop = 1;
                }
                gameInfo.setLoop(loop);
            }
        }
        return gameInfo;
    }

    private Integer getWinBeans(String winner, int totalBeans) {
        if (StringUtils.isEmpty(winner) || totalBeans == 0) {
            return 0;
        }
        // 房主获得总金额的5%, 系统扣除手续费10%
        return (int) (totalBeans - totalBeans * 0.05 - totalBeans * 0.1);
    }

    /**
     * 获取用户信息
     */
    private TurntableGameInfo.Actor getActorInfo(String uid) {
        if (StringUtils.isEmpty(uid)) {
            return null;
        }
        ActorData actorData = actorDao.getMysteryActorFromCache(uid);
        if (actorData == null) {
            return null;
        }
        TurntableGameInfo.Actor actor = new TurntableGameInfo.Actor();
        actor.set_id(actorData.getUid());
        actor.setName(actorData.getName());
        actor.setHead(ImageUrlGenerator.generateUrl(actorData.getHead(), 200, 200));
        actor.setGender(actorData.getFb_gender());
        actor.setFb_gender(actorData.getFb_gender());
        actor.setRid(actorData.getRid());
        actor.setMystery(actorData.getMystery());
        return actor;
    }

    /**
     * 游戏正常结束，结算处理
     */
    private void gameSettleAccounts(String gameId) {
        TurntableGameInfo gameInfo = turntableGameRedis.getGameInfo(gameId);
        if (checkNotProcessed(gameId, gameInfo)) {
            return;
        }
        int totalBeans = gameInfo.getJoinBeans() * gameInfo.getPlayers().size();
        // 房主抽成
        int hostGetBeans = (int) (totalBeans * 0.05);
        if (hostGetBeans >= 1) {
            asyncChargeDiamonds(gameInfo.getCreator(), gameInfo.getRoomId(), hostGetBeans, TAKE_A_PERCENTAGE_TITLE);
        }
        // 赢家获得的钻石
        TurntableGameInfo.Actor gameWinner = gameInfo.getWinner();
        String winner = gameWinner != null ? gameWinner.get_id() : "";
        int winRewardBeans = getWinBeans(winner, totalBeans);
        if (winRewardBeans >= 1) {
            asyncChargeDiamonds(winner, gameInfo.getRoomId(), winRewardBeans, WIN_TURNTABLE_GAME_TITLE);
        }
        // 保存转盘游戏记录
        insertTurntableRecord(gameInfo, winRewardBeans);
    }

    /**
     * 保存转盘游戏记录
     */
    @Transactional(value = WahoLogMySQLBean.WAHO_LOG_TRANSACTION, rollbackFor = {Exception.class, RuntimeException.class})
    public void insertTurntableRecord(TurntableGameInfo gameInfo, int winRewardBeans) {
        TurntableRecordData recordData = new TurntableRecordData();
        recordData.setGameId(gameInfo.get_id());
        recordData.setRoomId(gameInfo.getRoomId());
        recordData.setJoinBeans(gameInfo.getJoinBeans());
        recordData.setMinPlayers(gameInfo.getMinPlayers());
        recordData.setMaxPlayers(gameInfo.getMaxPlayers());
        recordData.setPlayersNum(!CollectionUtils.isEmpty(gameInfo.getPlayers()) ? gameInfo.getPlayers().size() : 0);
        recordData.setTotalBeans(gameInfo.getTotal_beans());
        recordData.setWinBeans(winRewardBeans);
        recordData.setWinner(gameInfo.getWinner() != null ? gameInfo.getWinner().get_id() : "");
        recordData.setCreator(gameInfo.getCreator());
        recordData.setCtime(gameInfo.getLastStartAt());
        turntableRecordDao.insert(recordData);
        // 转盘游戏结束上报数数
        GameRecordLogEvent logEvent = new GameRecordLogEvent();
        logEvent.setRoom_id(gameInfo.getRoomId());
        logEvent.setGame_id(gameInfo.get_id());
        logEvent.setGame_type(EventGameTypeConstant.TURNTABLE);
        logEvent.setCost_diamonds(gameInfo.getJoinBeans());
        logEvent.setRobot(0);
        logEvent.setCtime(DateHelper.getNowSeconds());
        if (!CollectionUtils.isEmpty(gameInfo.getPlayers())) {
            List<TurntablePlayerRecordData> playerRecordDataList = new ArrayList<>();
            int joinBeans = gameInfo.getJoinBeans();
            for (TurntableGameInfo.Actor actor : gameInfo.getPlayers()) {
                TurntablePlayerRecordData playerRecordData = new TurntablePlayerRecordData();
                playerRecordData.setRoomId(gameInfo.getRoomId());
                playerRecordData.setGameId(gameInfo.get_id());
                playerRecordData.setUid(actor.get_id());
                if (gameInfo.getWinner() != null && Objects.equals(actor.get_id(), gameInfo.getWinner().get_id())) {
                    playerRecordData.setChanged(winRewardBeans - joinBeans);
                    playerRecordData.setIsWinner(1);
                    logEvent.setGet_diamonds(winRewardBeans);
                } else {
                    playerRecordData.setChanged(-joinBeans);
                    playerRecordData.setIsWinner(0);
                    logEvent.setGet_diamonds(0);
                }
                playerRecordData.setCtime(gameInfo.getLastStartAt());
                playerRecordDataList.add(playerRecordData);

                logEvent.setUid(actor.get_id());
                logEvent.setIs_creator(Objects.equals(actor.get_id(), gameInfo.getCreator()) ? 1 : 0);
                eventReport.track(new EventDTO(logEvent));
                familyDevoteHelperRedis.playLuckyWheelDevote(actor.get_id());
            }
            turntablePlayerRecordDao.batchInsert(playerRecordDataList);
        }
    }

    /**
     * 游戏等待超时，结算游戏
     */
    private void dismissTurntableGame(String gameId, boolean isAutoClose) {
        TurntableGameInfo gameInfo = turntableGameRedis.getGameInfo(gameId);
        if (checkNotProcessed(gameId, gameInfo)) {
            return;
        }
        boolean needBeans = true;
        if (gameInfo.getJoinBeans() < 1) {
            logger.info("game join beans is less than 1. gameId={} joinBean={}", gameId, gameInfo.getJoinBeans());
            needBeans = false;
        }
        if (!needBeans && !isAutoClose) {
            return;
        }
        if (needBeans) {
            List<TurntableGameInfo.Actor> players = gameInfo.getPlayers();
            if (!CollectionUtils.isEmpty(players)) {
                for (TurntableGameInfo.Actor actor : players) {
                    ActorData player = actorDao.getActorDataFromCache(actor.get_id());
                    if (player == null) {
                        logger.error("can not find actor, uid={}", actor.get_id());
                        continue;
                    }
                    asyncChargeDiamonds(player.getUid(), gameInfo.getRoomId(), gameInfo.getJoinBeans(), RETURN_TITLE);
                }
            }
        }
        if (isAutoClose) {
            // 系统自动结束游戏,创建者和玩家端弹出提示弹窗
            sendLuckyWheelRoomPushMsg(gameInfo.getCreator(), gameInfo.getRoomId(), LuckyWheelState.CLOSE, gameInfo, isAutoClose);
        }
    }

    /**
     * 是否需要后续处理 ture不需要 false需要
     */
    private boolean checkNotProcessed(String gameId, TurntableGameInfo gameInfo) {
        if (gameInfo == null) {
            logger.error("can not find turntable game info by gameId. gameId={}", gameId);
            return true;
        }
        if (gameInfo.getStatus() == TurntableConstant.GAME_END) {
            logger.info("The turntable game is over. gameId={} status={}", gameId, gameInfo.getStatus());
            return true;
        }
        gameInfo.setStatus(TurntableConstant.GAME_END);
        turntableGameRedis.setGameInfo(gameInfo);
        turntableGameRedis.removeGameId(gameInfo.getRoomId());
        if (gameInfo.getJoinBeans() < 1) {
            logger.info("game join beans is less than 1. gameId={} joinBean={}", gameId, gameInfo.getJoinBeans());
            return true;
        }
        return false;
    }

    /**
     * 用户进房间调用检测房间内游戏
     */
    public CheckTurntableGameVO checkAll(TurntableDTO reqDTO) {
        ActorData actorData = actorDao.getActorDataFromCache(reqDTO.getUid());
        if (actorData == null) {
            logger.error("The actor not found. uid={}", reqDTO.getUid());
            throw new GameException(GameHttpCode.THE_ACTOR_NOT_FOUND);
        }
        CheckTurntableGameVO vo = new CheckTurntableGameVO();
        vo.setLeftNums(10);
        TurntableGameInfo gameInfo = getGameInfo(reqDTO.getRoom_id(), "");
        if (gameInfo != null) {
            vo.setTurntableGame(new CheckTurntableGameVO.TurntableGameInfoVO(gameInfo));
            vo.setType(2);
            return vo;
        }
        return vo;
    }
}
