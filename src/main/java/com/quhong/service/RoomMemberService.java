package com.quhong.service;

import com.alibaba.fastjson.JSONObject;
import com.mongodb.client.result.UpdateResult;
import com.quhong.config.CaffeineCacheConfig;
import com.quhong.constant.SvipConstant;
import com.quhong.core.concurrency.BaseTaskFactory;
import com.quhong.core.concurrency.tasks.Task;
import com.quhong.core.utils.DateHelper;
import com.quhong.data.ActorData;
import com.quhong.data.dto.MoneyDetailReq;
import com.quhong.data.dto.RoomAdminDTO;
import com.quhong.data.dto.RoomBlockUserDTO;
import com.quhong.data.dto.SearchMemberDTO;
import com.quhong.data.vo.*;
import com.quhong.enums.ApiResult;
import com.quhong.enums.HttpCode;
import com.quhong.enums.RoomHttpCode;
import com.quhong.exception.CommonException;
import com.quhong.feign.DataCenterService;
import com.quhong.mongo.dao.*;
import com.quhong.mongo.data.FollowRoomData;
import com.quhong.mongo.data.MongoRoomData;
import com.quhong.mongo.data.RoomConfigData;
import com.quhong.mongo.data.RoomMemberData;
import com.quhong.mq.MqSenderService;
import com.quhong.msg.obj.UserInfoObject;
import com.quhong.msg.push.CancelAdminPushMsg;
import com.quhong.msg.push.RoomViceHostPushMsg;
import com.quhong.msg.room.LuckyNumRoomChangeMsg;
import com.quhong.msg.room.RoomMemberPushMsg;
import com.quhong.redis.*;
import com.quhong.room.RoomWebSender;
import com.quhong.room.cache.RoomActorCache;
import com.quhong.room.data.RoomActorDetailData;
import com.quhong.utils.CollectionUtil;
import com.quhong.utils.RoomUtils;
import com.quhong.utils.WalletUtils;
import com.quhong.vo.PageVO;
import jakarta.annotation.Resource;
import org.apache.commons.lang.math.NumberUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.cache.annotation.CacheEvict;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import java.util.*;
import java.util.stream.Collectors;

/**
 * 房间会员Service
 *
 * <AUTHOR>
 * @date 2022/7/21
 */
@Service
public class RoomMemberService {

    private static final Logger logger = LoggerFactory.getLogger(RoomMemberService.class);

    private static final String PY_SEND_MSG_TO_WS_CHANNEL = "send_msg_to_ws";
    private static final String PY_BLOCK_USER_CHANNEL = "block_user";

    public static final int LIVE_ROOM_MAX_ADMIN_NUM = 10;
    public static final int ROOM_MAX_ADMIN_NUM = 20;

    private static final int ADMIN = 1;
    private static final int MEMBER = 2;
    private static final int FOLLOWER = 3;

    private static final int DEF_MEMBER_LIMIT = 8000;

    /**
     * 成为会员扣费title
     */
    public static final String ROOM_MEMBER_FEE_TITLE = "Room Member Fee";
    /**
     * 成为会员扣费desc
     */
    public static final String ROOM_MEMBER_FEE_DESC = "become member of %s";
    /**
     * 会员收入title
     */
    public static final String ROOM_MEMBER_INCOME_TITLE = "Room Member income";
    /**
     * 会员收入desc
     */
    public static final String ROOM_MEMBER_INCOME_DESC = "%s become member";

    @Resource
    private MongoRoomDao roomDao;
    @Resource
    private ActorDao actorDao;
    @Resource
    private RoomWebSender roomWebSender;
    @Resource(name = DataRedisBean.MAIN_CLUSTER)
    private StringRedisTemplate clusterRedis;
    @Resource
    private MysteryRedis mysteryRedis;
    @Resource
    private RoomMemberDao memberDao;
    @Resource
    private RoomAdminRedis roomAdminRedis;
    @Resource
    private RedisTaskService redisTaskService;
    @Resource
    private RoomDevoteOneDao roomDevoteOneDao;
    @Resource
    private RoomActorCache roomActorCache;
    @Resource
    private DataCenterService dataCenterService;
    @Resource
    private FollowRoomDao followRoomDao;
    @Resource
    private RoomConfigDao roomConfigDao;
    @Resource
    private RoomMemberRedis roomMemberRedis;
    @Resource
    private RoomMemberService roomMemberService;
    @Resource
    private MqSenderService mqSenderService;
    @Autowired
    private SvipLevelService svipLevelService;
    @Resource
    private WhitelistRedis whitelistRedis;

    public void delRoomMember(RoomAdminDTO req) {
        String uid = req.getUid();
        String roomId = req.getRoomId();
        String aid = dealStrAid(req.getAid());
        if (StringUtils.isEmpty(aid) || StringUtils.isEmpty(req.getRoomId())) {
            logger.error("delRoomMember param error. uid={} roomId={}", uid, roomId);
            throw new CommonException(RoomHttpCode.PARAM_ERROR);
        }
        if (!RoomUtils.isHomeowner(uid, roomId)) {
            logger.info("Only room owner or vice owner can delete room member");
            throw new CommonException(RoomHttpCode.PERMISSION_DENIED_DELETE_MEMBER);
        }
        RoomMemberData data = memberDao.findData(roomId, uid);
        int utype = data != null ? data.getUtype() : 0;
        UpdateResult updateResult = memberDao.removeRoomMember(roomId, aid);
        int rowCount = (int) updateResult.getModifiedCount();
        if (rowCount > 0) {
            roomDao.updateMemnum(roomId, -rowCount);
        }
        if (utype > 1) {
            // 管理员被取消了
            roomAdminRedis.removeRoomAdmin(roomId, aid);
        }
        roomMemberRedis.removeMember(roomId, aid);
        // 1是管理员，2是非管理员 admin是android本地字段 role 1管理员  2非会员  3会员
        CancelAdminPushMsg msg = new CancelAdminPushMsg();
        msg.setRoomId(roomId);
        msg.setAid(aid);
        msg.setRole(2);
        msg.setNow_utype(0);
        msg.setAdmin(2);
        roomWebSender.sendPlayerWebMsg(roomId, uid, aid, msg, true);
    }

    private int getMaxMembers(String uid) {
        return SvipConstant.ROOM_MEMBER_LIMIT.getOrDefault(svipLevelService.getSvipLevel(uid), DEF_MEMBER_LIMIT);
    }

    /**
     * 成为房间会员
     */
    public BecomeMemberVO becomeMember(RoomAdminDTO req) {
        BecomeMemberVO vo = new BecomeMemberVO();
        String uid = req.getUid();
        String roomId = req.getRoomId();
        MongoRoomData roomData = roomDao.getDataFromCache(roomId);
        if (roomData == null) {
            logger.error("room not exist. uid={} roomId={}", uid, roomId);
            throw new CommonException(RoomHttpCode.ROOM_NOT_EXIST);
        }
        if (whitelistRedis.isInvisibilityEnterRoom(req.getRoomId(), req.getUid())) {
            // 隐身进房无法操作
            throw new CommonException(HttpCode.INVISIBLE_ACCOUNT_CANNOT_BE_OPERATED);
        }
        String hostId = RoomUtils.getRoomHostId(roomId);
        ActorData userInfo = actorDao.getActorDataFromCache(uid);
        ActorData hostInfo = actorDao.getActorDataFromCache(hostId);
        if (userInfo == null) {
            logger.error("user not exist. uid={} roomId={}", uid, roomId);
            throw new CommonException(RoomHttpCode.ROOM_NOT_EXIST);
        }
        if (memberDao.getMemberCount(roomId) >= getMaxMembers(hostId)) {
            logger.info("Room members room are full, can't join. uid={} roomId={}", uid, roomId);
            throw new CommonException(RoomHttpCode.OVER_THE_LIMITATION);
        }
        RoomMemberData memberData = memberDao.findDataValidAndInvalid(roomId, uid);
        boolean isExisted = false;
        if (memberData != null) {
            isExisted = true;
            if (memberData.getValid() == 1) {
                logger.info("you are already member here.uid={} roomId={}", uid, roomId);
                throw new CommonException(RoomHttpCode.ALREADY_ROOM_MEMBER);
            }
        }
        if (null != mysteryRedis.getMysteryId(uid)) {
            throw new CommonException(RoomHttpCode.MYSTERY_MEMBER_ROOM_LIMIT);
        }
        int fee = roomData.getFee();
        if (fee != 0) {
            // 扣除成为会员的费用
            reduceBeans(uid, -fee, String.format(ROOM_MEMBER_FEE_DESC, hostInfo.getRid()));
        }
        // 扣费成功，设置房间会员
        saveMemberData(uid, roomId, userInfo, hostInfo, memberData);
        roomDao.updateMemnum(roomId, 1);
        roomMemberRedis.addMemberToRedis(roomId, uid);
        if (!isExisted) {
            sendToImTask(uid, roomId);
        }
        vo.setBeans(WalletUtils.diamondsForDisplay(actorDao.getBalance(uid)));
        vo.setGold(0);

        return vo;
    }

    private void saveMemberData(String uid, String roomId, ActorData userInfo, ActorData hostInfo, RoomMemberData memberData) {
        if (memberData != null) {
            memberData.setName(userInfo.getName());
            memberData.setRid(userInfo.getRid());
            memberData.setRname(hostInfo.getName());
            memberData.setRrid(hostInfo.getRid());
        } else {
            memberData = new RoomMemberData();
        }
        memberData.setAid(uid);
        memberData.setRoomId(roomId);
        memberData.setValid(1);
        memberData.setUtype(1);
        memberData.setMtime(DateHelper.getNowSeconds());
        memberData.setDevote(roomDevoteOneDao.findRoomDevote(uid, roomId));
        memberDao.save(memberData);
    }

    private MoneyDetailReq buildMoneyDetailReq(String uid, int changed, String title, String desc) {
        MoneyDetailReq moneyDetailReq = new MoneyDetailReq();
        moneyDetailReq.setRandomId();
        moneyDetailReq.setUid(uid);
        moneyDetailReq.setAtype(103);
        moneyDetailReq.setChanged(changed);
        moneyDetailReq.setTitle(title);
        moneyDetailReq.setDesc(desc);
        return moneyDetailReq;
    }

    /**
     * 成为会员减少钻石
     */
    private void reduceBeans(String uid, int changed, String desc) {
        ApiResult<String> result = dataCenterService.reduceBeans(buildMoneyDetailReq(uid, changed, RoomMemberService.ROOM_MEMBER_FEE_TITLE, desc));
        if (result.isError()) {
            if (1 == result.getCode().getCode()) {
                throw new CommonException(RoomHttpCode.UNION_NOT_ENOUGH_DIAMOND);
            }
            logger.error("reduce beans error, msg={}", result.getCode().getMsg());
            throw new CommonException(HttpCode.SERVER_ERROR);
        }
    }

    /**
     * 房主获取部分会员费
     */
    private void increaseBeans(String uid, int changed, String desc) {
        mqSenderService.asyncChargeDiamonds(buildMoneyDetailReq(uid, changed, RoomMemberService.ROOM_MEMBER_INCOME_TITLE, desc));
    }

    private void sendToImTask(String uid, String roomId) {
        BaseTaskFactory.getFactory().addSlow(new Task() {
            @Override
            protected void execute() {
                RoomActorDetailData actorData = roomActorCache.getData(roomId, uid, false);
                if (actorData == null) {
                    return;
                }
                RoomMemberPushMsg msg = new RoomMemberPushMsg();
                msg.setName(actorData.toUNameObject());
                roomWebSender.sendRoomWebMsg(roomId, uid, msg, true);
            }
        });
    }

    private int getJoinRoomNum() {
        int joinRoomNum = 0;
        try {
            Object strJoinRoomNum = clusterRedis.opsForHash().get("hash:common_config_key", "join_room_num");
            joinRoomNum = StringUtils.isEmpty(strJoinRoomNum) ? 8000 : Integer.parseInt(String.valueOf(strJoinRoomNum));
        } catch (Exception e) {
            logger.error("get join room num error. msg={}", e.getMessage(), e);
        }
        return joinRoomNum;
    }

    /**
     * 搜索指定房间的会员
     */
    public RoomMemberVO searchMember(SearchMemberDTO req) {
        RoomMemberVO vo = new RoomMemberVO();
        if (req.getPage() <= 0) {
            vo.setNextUrl("");
            vo.setList(Collections.emptyList());
            return vo;
        }
        int page = req.getPage() > 0 ? req.getPage() : 1;
        List<RoomMemberVO.RoomMember> list = new ArrayList<>();
        int pageSize = 30;
        int start = (page - 1) * pageSize;
        String roomId = req.getRoomId();
        List<RoomMemberData> dataList = new ArrayList<>();
        if (req.getKey() != null && !StringUtils.isEmpty(req.getKey().trim())) {
            // 搜索关键字不为空
            String key = req.getKey().trim();
            ActorData actorData = actorDao.getActorByRidOrAlphaRid(key);
            if (null != actorData) {
                RoomMemberData memberData = memberDao.findData(roomId, actorData.getUid());
                if (memberData != null) {
                    dataList.add(memberData);
                }
            } else {
                // 用户名模糊查询
                dataList = roomMemberService.selectRoomMemberPage(roomId, key, start, pageSize);
            }
        } else {
            // 搜索关键字为空且是第一页, 把房主加进去
            if (page == 1) {
                RoomMemberData ownerData = new RoomMemberData();
                ownerData.setAid(RoomUtils.getRoomHostId(roomId));
                dataList.add(ownerData);
                vo.setTotalMembers(memberDao.getMemberCount(roomId));
                vo.setMaxMembers(getMaxMembers(ownerData.getAid()));
            }
            List<RoomMemberData> memberList = roomMemberService.selectRoomMemberPage(roomId, start, pageSize);
            if (!CollectionUtils.isEmpty(memberList)) {
                dataList.addAll(memberList);
            }
        }
        if (CollectionUtils.isEmpty(dataList)) {
            vo.setNextUrl("");
            vo.setList(Collections.emptyList());
            return vo;
        }
        for (RoomMemberData data : dataList) {
            RoomMemberVO.RoomMember roomMember = getRoomMemberVO(data, roomId);
            if (roomMember == null) {
                continue;
            }
            list.add(roomMember);
        }
        vo.setList(list);
        vo.setNextUrl(dataList.size() < pageSize ? "" : String.valueOf(page + 1));
        return vo;
    }

    @Cacheable(value = "selectRoomMemberPage2", key = "T(org.springframework.util.StringUtils).arrayToDelimitedString(args,'-')",
            cacheManager = CaffeineCacheConfig.EXPIRE_15S_AFTER_WRITE)
    public List<RoomMemberData> selectRoomMemberPage(String roomId, String key, int start, int pageSize) {
        return memberDao.selectRoomMemberPage(roomId, key, start, pageSize);
    }

    /**
     * 针对会员数较多的列表进行缓存
     * cacheable中的condition和unless
     * condition是对入参进行判断，符合条件的缓存，不符合的不缓存。
     * unless是对出参进行判断，符合条件的不缓存，不符合的缓存。
     */
    @Cacheable(value = "selectRoomMemberPage", key = "T(org.springframework.util.StringUtils).arrayToDelimitedString(args,'-')",
            unless = "#result.size()!=#p2",
            cacheManager = CaffeineCacheConfig.EXPIRE_10M_AFTER_WRITE)
    public List<RoomMemberData> selectRoomMemberPage(String roomId, int start, int pageSize) {
        List<RoomMemberData> result = memberDao.selectRoomMemberPage(roomId, start, pageSize);
        logger.info("selectRoomMemberPage roomId={} start={} pageSize={} resultSize={}", roomId, start, pageSize, result.size());
        return result;
    }

    @CacheEvict(value = "selectRoomMemberPage", key = "T(org.springframework.util.StringUtils).arrayToDelimitedString(args,'-')", cacheManager = CaffeineCacheConfig.EXPIRE_10M_AFTER_WRITE)
    public void clearMemberListCache(String roomId, int start, int pageSize) {

    }

    public MemberVO searchMemberOld(SearchMemberDTO req) {
        String key = req.getKey() != null ? req.getKey().trim() : "";
        if (StringUtils.isEmpty(key)) {
            logger.info("Please enter key word.");
            throw new CommonException(RoomHttpCode.KEY_WORD_CANNOT_BE_NULL);
        }
        int pageSize = 30;
        int page = req.getPage() > 0 ? req.getPage() : 1;
        int start = (page - 1) * pageSize;
        MemberVO vo = new MemberVO();
        List<String> uidList = new ArrayList<>();
        List<RoomMemberData> dataList = new ArrayList<>();
        if (NumberUtils.isDigits(key)) {
            // rid精确查询
            int rid = -1;
            try {
                rid = Integer.parseInt(key);
            } catch (Exception e) {
                logger.error("String to Integer failed. key={} {}", key, e.getMessage(), e);
            }
            ActorData actor = actorDao.getActorByRid(rid);
            if (req.getMtype() == null) {
                RoomMemberData memberData;
                if (actor != null) {
                    memberData = memberDao.findData(req.getRoomId(), actor.getUid());
                } else {
                    memberData = memberDao.findData(req.getRoomId(), rid);
                }
                if (memberData != null) {
                    dataList = new ArrayList<>();
                    dataList.add(memberData);
                }
            } else if (req.getMtype() == ADMIN && actor != null) {
                dataList = memberDao.findRoomMemberPage(req.getRoomId(), actor.getUid(), start, pageSize);
            } else if (req.getMtype() == MEMBER) {
                dataList = memberDao.findRoomMemberPage(req.getRoomId(), start, pageSize);
            } else if (req.getMtype() == FOLLOWER && actor != null) {
                List<FollowRoomData> followRoomList = followRoomDao.selectPage(actor.getUid(), req.getRoomId(), start, pageSize);
                if (!CollectionUtils.isEmpty(followRoomList)) {
                    uidList = followRoomList.stream().map(FollowRoomData::getUid).collect(Collectors.toList());
                }
            }
        } else {
            // 用户名模糊查询
            dataList = memberDao.selectRoomMemberPage(req.getRoomId(), key, start, pageSize);
        }
        Map<String, Integer> utypeMap = new HashMap<>();
        if (!CollectionUtils.isEmpty(dataList)) {
            uidList = dataList.stream().map(RoomMemberData::getAid).collect(Collectors.toList());
            utypeMap = dataList.stream().collect(Collectors.toMap(RoomMemberData::getAid, RoomMemberData::getUtype));
        }
        if (CollectionUtils.isEmpty(uidList)) {
            vo.setCode(0);
            vo.setNextUrl("");
            vo.setList(Collections.emptyList());
            return vo;
        }
        List<MemberVO.RoomMember> list = new ArrayList<>();
        for (String uid : uidList) {
            MemberVO.RoomMember roomMember = getMemberVO(uid, req.getRoomId(), utypeMap);
            if (roomMember == null) {
                continue;
            }
            list.add(roomMember);
        }
        vo.setList(list);
        vo.setNextUrl(uidList.size() < pageSize ? "" : String.valueOf(page + 1));
        return vo;
    }

    private MemberVO.RoomMember getMemberVO(String uid, String roomId, Map<String, Integer> utypeMap) {
        MemberVO.RoomMember roomMember = new MemberVO.RoomMember();
        RoomActorDetailData actorData = roomActorCache.getData(roomId, uid, false, false);
        if (actorData == null) {
            return null;
        }
        roomMember.setName(actorData.getName());
        roomMember.setHead(actorData.getHead());
        roomMember.setDesc(actorData.getDesc());
        roomMember.setGender(actorData.getGender());
        roomMember.setAge(actorData.getAge());
        roomMember.setCountry(actorData.getCountry());
        roomMember.setRid(actorData.getRid());
        roomMember.setOs(actorData.getOs());
        roomMember.setValid(actorData.getValid());
        roomMember.setAid(uid);
        int utype = utypeMap.get(uid) != null ? utypeMap.get(uid) : 1;
        roomMember.setUtype(utype);
        roomMember.setViplevel(actorData.getVipLevel());
        roomMember.setUlvl(actorData.getActiveLevel());
        roomMember.setViceHost(utype == 4 ? 1 : 0);
        return roomMember;
    }

    /**
     * 构建RoomMemberVO
     */
    private RoomMemberVO.RoomMember getRoomMemberVO(RoomMemberData data, String roomId) {
        String aid = data.getAid();
        RoomMemberVO.RoomMember roomMember = new RoomMemberVO.RoomMember();
        RoomActorDetailData actorData = roomActorCache.getData(roomId, aid, false, false);
        if (actorData == null) {
            return null;
        }
        BeanUtils.copyProperties(actorData, roomMember);
        roomMember.setViplevel(actorData.getVipLevel());
        roomMember.setSvipLevel(actorData.getSvipLevel());
        roomMember.setActiveLevel(actorData.getActiveLevel());
        roomMember.setWealthLevel(actorData.getWealthLevel());
        roomMember.setCharmLevel(actorData.getCharmLevel());
        roomMember.setUtype(data.getUtype());
        roomMember.setRole(memberDao.getRoleContainMember(roomId, aid));
        roomMember.setViceHost((data.getUtype() == 4) ? 1 : 0);
        roomMember.setMicFrame(actorData.getMicFrame());
        return roomMember;
    }

    /**
     * 设置房间管理员
     */
    public void setRoomAdmin(RoomAdminDTO req) {
        String uid = req.getUid();
        String roomId = req.getRoomId();
        int isVice = req.getIs_vice();
        String aid = dealStrAid(req.getAid());
        if (!StringUtils.hasLength(req.getAid()) || Objects.equals(req.getUid(), req.getAid())
                || !RoomUtils.isHomeowner(req.getUid(), req.getRoomId())) {
            logger.info("set live room admin param error. roomId={} uid={} aid={}", req.getRoomId(), req.getUid(), req.getAid());
            throw new CommonException(HttpCode.PARAM_ERROR);
        }
        List<RoomMemberData> adminList = memberDao.findAdminList(roomId);
        List<String> adminUidList = adminList.stream().map(RoomMemberData::getAid).collect(Collectors.toList());
        int adminCount = adminList.size();
        if (adminCount + 1 > ROOM_MAX_ADMIN_NUM) {
            boolean alreadyAdmin = false;
            if (isVice != 0) {
                if (adminUidList.contains(aid)) {
                    alreadyAdmin = true;
                }
            }
            if (!alreadyAdmin) {
                logger.info("Only support 20 room admin in total. uid={}", uid);
                throw new CommonException(RoomHttpCode.ADMIN_NUM_REACHED_LIMIT, ROOM_MAX_ADMIN_NUM);
            }
        }
        memberDao.updateUtype(roomId, aid, RoomMemberDao.UTYPE_MANAGER);
        roomAdminRedis.addRoomAdmin(roomId, aid);
        sendToImSetAdminTask(uid, aid, roomId, isVice);
        clearMemberListCache(roomId, 1, 30);
    }

    private String dealStrAid(String aid) {
        if (aid == null) {
            return "";
        }
        // ios 传入的参数aid ["6253fcd345d9f384048e52a8"]
        return aid.replace("[", "").replace("]", "").replace("\"", "");
    }

    private void sendToImSetAdminTask(String uid, String aid, String roomId, int isVice) {
        ActorData actorData = actorDao.getActorDataFromCache(uid);
        if (actorData == null) {
            return;
        }
        RoomViceHostPushMsg msg = new RoomViceHostPushMsg();
        msg.setFrom_head(actorData.getHead());
        msg.setFrom_name(actorData.getName());
        msg.setViceHost(isVice);
        msg.setMsg_en(isVice == 0 ? "Set you as the room admin and hope you can bring better service to the room." : "Set you as the vice owner and hope you can bring better service to the room.");
        msg.setMsg_ar(isVice == 0 ? ".تم تعيينك كمشرف الغرفة ونأمل أن تتمكن من تقديم خدمة أفضل للغرفة" : "تم تعيينك كنائب المالك ونأمل أن تتمكن من تقديم خدمة أفضل للغرفة");
        roomWebSender.sendPlayerWebMsg(roomId, uid, aid, msg, true);
        logger.info("set admin push msg.msg={}", JSONObject.toJSONString(msg));

        // sendLuckyNumAsync(uid, aid, roomId, isVice);

    }


    private void sendLuckyNumAsync(String uid, String aid, String roomId, int isVice) {
        RoomConfigData roomConfig = roomConfigDao.findData(roomId);
        if (roomConfig == null) {
            roomConfig = roomConfigDao.initRoomConfigData(roomId);
        }

        ActorData actorData = actorDao.getActorDataFromCache(aid);
        if (actorData == null) {
            return;
        }

        Map<String, Object> config = roomConfig.getRoom_config();
        int luckyNumAdmin = (int) config.getOrDefault("luckyNumAdmin", 0);
        int luckyNumCost = (int) config.getOrDefault("luckyNumCost", 0);
        int luckyNumRange = (int) config.getOrDefault("luckyNumRange", 9);
        int luckyNumSwitch = (int) config.getOrDefault("luckyNumSwitch", 0);
        int luckyNumData = (int) config.getOrDefault("luckyNumData", 0);

        LuckyNumRoomChangeMsg changeMsg = new LuckyNumRoomChangeMsg();
        changeMsg.setNum(luckyNumData);
        changeMsg.setLuckyNumCost(luckyNumCost);
        changeMsg.setLuckyNumSwitch(luckyNumSwitch);
        changeMsg.setLuckyNumAdmin(luckyNumAdmin);
        changeMsg.setLuckyNumRange(luckyNumRange);
        UserInfoObject userInfoObject = new UserInfoObject();
        userInfoObject.setUid(aid);
        userInfoObject.setName(actorData.getName());
        userInfoObject.setHead(actorData.getHead());
        userInfoObject.setViceHost(isVice);
        changeMsg.setOpt_user(userInfoObject);
        roomWebSender.sendPlayerWebMsg(roomId, uid, aid, changeMsg, true);
    }

    /**
     * 取消房间管理员权限
     */
    public void cancelAdmin(RoomAdminDTO req) {
        String uid = req.getUid();
        String roomId = req.getRoomId();
        String aid = dealStrAid(req.getAid());
        boolean optVice = req.getIs_vice() != 0;
        if (StringUtils.isEmpty(uid) || roomId == null || roomId.length() < 2 || StringUtils.isEmpty(aid)) {
            logger.error("cancel admin params error. uid={} roomId={} aid={}", uid, roomId, aid);
            throw new CommonException(HttpCode.PARAM_ERROR);
        }
        if (!RoomUtils.isHomeowner(uid, roomId)) {
            logger.info("Only room owner or vice owner can cancel room admin. uid={} roomId={}", uid, roomId);
            throw new CommonException(new HttpCode(412, "Only room owner or vice owner can cancel room admin"));
        }
        UpdateResult updateResult;
        if (!optVice) {
            // 取消管理员
            updateResult = memberDao.cancelAdmin(roomId, aid);
            roomAdminRedis.removeRoomAdmin(roomId, aid);
            if (updateResult.getModifiedCount() > 0) {
                CancelAdminPushMsg msg = new CancelAdminPushMsg();
                msg.setRoomId(roomId);
                msg.setAid(aid);
                msg.setNow_utype(optVice ? 2 : 1);
                msg.setAdmin(optVice ? 1 : 2);
                msg.setRole(optVice ? 1 : 3);
                roomWebSender.sendPlayerWebMsg(roomId, uid, aid, msg, true);
                logger.info("cancel admin push msg.msg={}", JSONObject.toJSONString(msg));
            }
        }
    }

    /**
     * 列出我是会员的房间
     */
    public MemberRoomVO getMemberRoomList(SearchMemberDTO req) {
        String uid = req.getUid();
        int page = req.getPage();
        int size = 10;
        int start = (page - 1) * size;
        MemberRoomVO vo = new MemberRoomVO();
        List<RoomMemberData> memberDataList = memberDao.selectMemberPage(uid, start, size);
        if (CollectionUtils.isEmpty(memberDataList)) {
            vo.setCode(0);
            vo.setList(Collections.emptyList());
            vo.setNextUrl("");
            return vo;
        }
        List<MemberRoomVO.MemberRoom> list = new ArrayList<>();
        vo.setNextUrl(memberDataList.size() < size ? "" : String.valueOf(page + 1));
        for (RoomMemberData data : memberDataList) {
            String roomId = data.getRoomId();
            String ownerId = RoomUtils.getRoomHostId(roomId);
            MemberRoomVO.MemberRoom memberRoom = buildMemberRoom(ownerId, roomId);
            if (memberRoom == null) {
                continue;
            }
            list.add(memberRoom);
        }
        vo.setCode(0);
        vo.setList(list);
        return vo;
    }

    /**
     * 搜索我是会员的房间
     */
    public MemberRoomVO searchMemberRoomList(SearchMemberDTO req) {
        String uid = req.getUid();
        String key = req.getKey() != null ? req.getKey().trim() : "";
        if (StringUtils.isEmpty(key)) {
            logger.info("key word is empty.");
            throw new CommonException(RoomHttpCode.KEY_WORD_CANNOT_BE_NULL);
        }
        MemberRoomVO vo = new MemberRoomVO();
        int pageSize = 20;
        int start = (req.getPage() - 1) * pageSize;
        List<RoomMemberData> memberRoomList = memberDao.getMemberRoomPage(uid, key, start, pageSize);
        if (CollectionUtils.isEmpty(memberRoomList)) {
            vo.setCode(0);
            vo.setList(Collections.emptyList());
            vo.setNextUrl("");
            return vo;
        }
        List<MemberRoomVO.MemberRoom> list = new ArrayList<>();
        vo.setNextUrl(memberRoomList.size() < pageSize ? "" : String.valueOf(req.getPage() + 1));
        for (RoomMemberData data : memberRoomList) {
            String roomId = data.getRoomId();
            String ownerId = RoomUtils.getRoomHostId(roomId);
            MemberRoomVO.MemberRoom memberRoom = buildMemberRoom(ownerId, roomId);
            if (memberRoom == null) {
                continue;
            }
            list.add(memberRoom);
        }
        vo.setCode(0);
        vo.setList(list);
        return vo;
    }

    private MemberRoomVO.MemberRoom buildMemberRoom(String ownerId, String roomId) {
        RoomActorDetailData actorData = roomActorCache.getData(ownerId, roomId, false, false);
        if (actorData == null) {
            return null;
        }
        MongoRoomData roomData = roomDao.findData(roomId);
        if (roomData == null) {
            return null;
        }
        MemberRoomVO.MemberRoom memberRoom = new MemberRoomVO.MemberRoom();
        memberRoom.setName(actorData.getName());
        memberRoom.setHead(actorData.getHead());
        memberRoom.setDesc(actorData.getDesc());
        memberRoom.setGender(actorData.getGender());
        memberRoom.setAge(actorData.getAge());
        memberRoom.setOs(String.valueOf(actorData.getOs()));
        memberRoom.setAid(ownerId);
        memberRoom.setCountry(actorData.getCountry());
        memberRoom.setViplevel(actorData.getVipLevel());
        memberRoom.setRoomId(roomId);
        if (StringUtils.isEmpty(roomData.getCountry())) {
            memberRoom.setCountry(roomData.getCountry());
        }
        return memberRoom;
    }

    /**
     * 取消成为房间会员
     */
    public void deleteMemberRoom(RoomAdminDTO req) {
        String uid = req.getUid();
        String roomId = req.getRoomId();
        String aid = dealStrAid(req.getAid());
        if (whitelistRedis.isInvisibilityEnterRoom(req.getRoomId(), req.getUid())) {
            // 隐身进房无法操作
            throw new CommonException(RoomHttpCode.INVISIBLE_ACCOUNT_CANNOT_BE_OPERATED);
        }
        if (!StringUtils.isEmpty(aid)) {
            String aRoomId = RoomUtils.formatRoomId(aid);
            UpdateResult updateResult = memberDao.removeRoomMember(aRoomId, uid);
            int modifiedCount1 = (int) updateResult.getModifiedCount();
            if (modifiedCount1 > 0) {
                roomDao.updateMemnum(aRoomId, -modifiedCount1);
            }
            roomAdminRedis.removeRoomAdmin(aRoomId, uid);
            roomMemberRedis.removeMember(aRoomId, uid);
        }
        if (!StringUtils.isEmpty(roomId)) {
            UpdateResult updateResult = memberDao.removeRoomMember(roomId, uid);
            int modifiedCount2 = (int) updateResult.getModifiedCount();
            if (modifiedCount2 > 0) {
                roomDao.updateMemnum(roomId, -modifiedCount2);
            }
            boolean removed = roomAdminRedis.removeRoomAdmin(roomId, uid);
            roomMemberRedis.removeMember(roomId, uid);
            if (removed) {
                // 1是管理员，2是非管理员 admin是android本地字段 role 1管理员  2非会员  3会员
                CancelAdminPushMsg msg = new CancelAdminPushMsg();
                msg.setRoomId(roomId);
                msg.setAid(aid);
                msg.setRole(2);
                msg.setNow_utype(0);
                msg.setAdmin(2);
                roomWebSender.sendPlayerWebMsg(roomId, uid, uid, msg, true);
            }
        }
    }

    /**
     * 房间内拉黑用户
     */
    public void blockUser(RoomBlockUserDTO req) {
        String uid = req.getUid();
        String aid = req.getAid();
        String roomId = RoomUtils.formatRoomId(uid);
        ActorData actorData = actorDao.getActorDataFromCache(aid);
        if (actorData.getLastLogin() != null) {
            JSONObject fcmData = new JSONObject();
            fcmData.put("msg", "You are blocked by room owner");
            fcmData.put("roomId", roomId);
            JSONObject sendData = new JSONObject();
            sendData.put("topic", "ws_kick_room");
            sendData.put("uid", aid);
            sendData.put("data", fcmData);
            redisTaskService.broadcastMessage(PY_SEND_MSG_TO_WS_CHANNEL, sendData);
        }
        RoomAdminDTO dto = new RoomAdminDTO();
        dto.setUid(uid);
        dto.setRoomId(roomId);
        dto.setAid(aid);
        delRoomMember(dto);
        JSONObject msgBody = new JSONObject();
        msgBody.put("uid", uid);
        msgBody.put("aid", aid);
        redisTaskService.broadcastMessage(PY_BLOCK_USER_CHANNEL, msgBody);
    }

    public void setLiveRoomAdmin(RoomAdminDTO dto) {
        if (!StringUtils.hasLength(dto.getAid()) || Objects.equals(dto.getUid(), dto.getAid())
                || !RoomUtils.isHomeowner(dto.getUid(), dto.getRoomId())) {
            logger.info("set live room admin param error. roomId={} uid={} aid={}", dto.getRoomId(), dto.getUid(), dto.getAid());
            throw new CommonException(HttpCode.PARAM_ERROR);
        }
        ActorData actorData = actorDao.getActorDataFromCache(dto.getUid());
        if (actorData == null) {
            throw new CommonException(HttpCode.PARAM_ERROR);
        }
        List<RoomMemberData> adminList = memberDao.findAdminList(dto.getRoomId());
        if (!CollectionUtils.isEmpty(adminList)) {
            List<String> adminUidList = CollectionUtil.getPropertyList(adminList, RoomMemberData::getAid, null);
            if (adminUidList.contains(dto.getAid())) {
                return;
            }
            if (adminList.size() + 1 > LIVE_ROOM_MAX_ADMIN_NUM) {
                logger.info("Only support 10 room admin in total. uid={}", dto.getUid());
                throw new CommonException(RoomHttpCode.ADMIN_NUM_REACHED_LIMIT, LIVE_ROOM_MAX_ADMIN_NUM);
            }
        }
        RoomMemberData memberData = new RoomMemberData();
        memberData.setRoomId(dto.getRoomId());
        memberData.setAid(dto.getAid());
        memberData.setValid(1);
        memberData.setUtype(RoomMemberDao.UTYPE_MANAGER);
        memberData.setMtime(DateHelper.getNowSeconds());
        memberDao.save(memberData);
        roomAdminRedis.addRoomAdmin(dto.getRoomId(), dto.getAid());
        sendRoomViceHostPushMsg(actorData, dto);
    }

    public void cancelLiveRoomAdmin(RoomAdminDTO dto) {
        if (!StringUtils.hasLength(dto.getAid()) || Objects.equals(dto.getUid(), dto.getAid())
                || !RoomUtils.isHomeowner(dto.getUid(), dto.getRoomId())) {
            logger.error("cancel live room admin param error. roomId={} uid={} aid={}", dto.getRoomId(), dto.getUid(), dto.getAid());
            throw new CommonException(HttpCode.PARAM_ERROR);
        }
        // 取消管理员
        UpdateResult updateResult = memberDao.cancelLiveRoomAdmin(dto.getRoomId(), dto.getAid());
        roomAdminRedis.removeRoomAdmin(dto.getRoomId(), dto.getAid());
        if (updateResult.getModifiedCount() > 0) {
            CancelAdminPushMsg msg = new CancelAdminPushMsg();
            msg.setRoomId(dto.getRoomId());
            msg.setAid(dto.getAid());
            msg.setNow_utype(2);
            msg.setAdmin(0);
            msg.setRole(0);
            roomWebSender.sendPlayerWebMsg(dto.getRoomId(), dto.getUid(), dto.getAid(), msg, true);
        }
    }

    private void sendRoomViceHostPushMsg(ActorData actorData, RoomAdminDTO dto) {
        RoomViceHostPushMsg msg = new RoomViceHostPushMsg();
        msg.setFrom_head(actorData.getHead());
        msg.setFrom_name(actorData.getName());
        msg.setViceHost(0);
        msg.setMsg_en("The host has set you as an administrator.");
        msg.setMsg_ar("قام المضيف بتعيينك كمسؤول.");
        roomWebSender.sendPlayerWebMsg(dto.getRoomId(), dto.getUid(), dto.getAid(), msg, true);
    }

    public PageVO<RoomAdminVO> getAdminList(RoomAdminDTO dto) {
        PageVO<RoomAdminVO> result = new PageVO<>();
        int page = dto.getPage() > 0 ? dto.getPage() : 1;
        int pageSize = 10;
        int start = (page - 1) * pageSize;
        List<RoomMemberData> dataList = memberDao.selectRoomAdminPage(dto.getRoomId(), start, pageSize);
        List<RoomAdminVO> list = new ArrayList<>();
        if (!CollectionUtils.isEmpty(dataList)) {
            for (RoomMemberData data : dataList) {
                RoomActorDetailData detailData = roomActorCache.getData(dto.getRoomId(), data.getAid(), false, false);
                RoomAdminVO vo = new RoomAdminVO();
                vo.setAid(detailData.getAid());
                vo.setName(detailData.getName());
                vo.setHead(detailData.getHead());
                vo.setGender(detailData.getGender());
                vo.setVipLevel(detailData.getVipLevel());
                vo.setAge(detailData.getAge());
                vo.setSvipLevel(detailData.getSvipLevel());
                vo.setCharmLevel(detailData.getCharmLevel());
                vo.setWealthLevel(detailData.getWealthLevel());
                list.add(vo);
            }
        }
        result.setList(list);
        result.setNextUrl(page, pageSize);
        return result;
    }
}
