package com.quhong.service;

import com.quhong.analysis.BackendReviewRecordEvent;
import com.quhong.analysis.EventDTO;
import com.quhong.analysis.EventReport;
import com.quhong.analysis.RoomEventActionLogEvent;
import com.quhong.constant.DetectOriginConstant;
import com.quhong.constant.MoneyActionType;
import com.quhong.constant.RoomEventConstant;
import com.quhong.core.concurrency.BaseTaskFactory;
import com.quhong.core.concurrency.tasks.Task;
import com.quhong.core.config.ServerConfig;
import com.quhong.core.distribution.DistributeLock;
import com.quhong.core.utils.DateHelper;
import com.quhong.data.ActorData;
import com.quhong.data.RoomRoleData;
import com.quhong.data.dto.MoneyDetailReq;
import com.quhong.data.dto.RoomEventDTO;
import com.quhong.data.vo.*;
import com.quhong.dto.ImageDTO;
import com.quhong.dto.TextDTO;
import com.quhong.enums.*;
import com.quhong.exception.CommonException;
import com.quhong.feign.DataCenterService;
import com.quhong.feign.IDetectService;
import com.quhong.image.ImageUrlGenerator;
import com.quhong.mongo.dao.ActorDao;
import com.quhong.mongo.dao.EventRankingRecordDao;
import com.quhong.mongo.dao.MongoRoomDao;
import com.quhong.mongo.dao.RoomMemberDao;
import com.quhong.mongo.data.MongoRoomData;
import com.quhong.mq.MqSenderService;
import com.quhong.msg.obj.HighlightTextObject;
import com.quhong.msg.room.RoomEventNumChangeMsg;
import com.quhong.msg.room.RoomNotificationMsg;
import com.quhong.mysql.config.WahoLogMySQLBean;
import com.quhong.mysql.dao.RoomEventDao;
import com.quhong.mysql.dao.RoomEventSubDao;
import com.quhong.mysql.dao.WhiteTestDao;
import com.quhong.mysql.data.RoomEventData;
import com.quhong.mysql.data.RoomEventSubData;
import com.quhong.redis.DataRedisBean;
import com.quhong.redis.MysteryRedis;
import com.quhong.redis.RoomEventRedis;
import com.quhong.room.RoomWebSender;
import com.quhong.room.cache.RoomActorCache;
import com.quhong.room.data.RoomActorDetailData;
import com.quhong.room.redis.RoomPlayerRedis;
import com.quhong.utils.CollectionUtil;
import com.quhong.utils.RoomUtils;
import com.quhong.vo.PageVO;
import jakarta.annotation.Resource;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.*;
import java.util.concurrent.TimeUnit;

/**
 * <AUTHOR>
 * @date 2022/12/6
 */
@Service
public class RoomEventService {

    private static final Logger logger = LoggerFactory.getLogger(RoomEventService.class);

    private static final String SUB_EVENT_LOCK_KEY = "sub_event_";

    private static final String SUB_EVENT_MSG = "%s has subscribed the room activities.";
    private static final String SUB_EVENT_MSG_AR = "\u202b%s اشتركت في أنشطة الغرفة.";

    private static final List<EventCreateInfoVO.EventTypeVO> EVENT_TYPE_LIST = List.of(
            new EventCreateInfoVO.EventTypeVO(1, "Party", "الحفلة", "https://cloudcdn.waho.live/resource/op_sys_1748311765_party.png"),
            new EventCreateInfoVO.EventTypeVO(2, "Chat", "الدردشة", "https://cloudcdn.waho.live/resource/op_sys_1748311765_chat.png"),
            new EventCreateInfoVO.EventTypeVO(3, "Game", "اللعب", "https://cloudcdn.waho.live/resource/op_sys_1748311765_game.png"),
            new EventCreateInfoVO.EventTypeVO(4, "Singing", "الغناء", "https://cloudcdn.waho.live/resource/op_sys_1748311765_singing.png"),
            new EventCreateInfoVO.EventTypeVO(5, "Competition", "المسابقة", "https://cloudcdn.waho.live/resource/op_sys_1748311765_competition.png"),
            new EventCreateInfoVO.EventTypeVO(6, "Poetry", "الشعر", "https://cloudcdn.waho.live/resource/op_sys_1748311765_poetry.png"),
            new EventCreateInfoVO.EventTypeVO(7, "Other", "الآخر", "https://cloudcdn.waho.live/resource/op_sys_1748311765_other.png")
    );

    @Resource
    private RoomEventDao roomEventDao;
    @Resource
    private RoomEventSubDao roomEventSubDao;
    @Resource
    private DataCenterService dataCenterService;
    @Resource
    private MongoRoomDao roomDao;
    @Resource
    private RoomActorCache roomActorCache;
    @Resource
    private ActorDao actorDao;
    @Resource
    private RoomPlayerRedis roomPlayerRedis;
    @Resource
    private EventRobotService eventRobotService;
    @Resource
    private RoomWebSender roomWebSender;
    @Resource
    private EventReport eventReport;
    @Resource
    private IDetectService detectService;
    @Resource(name = DataRedisBean.MAIN_CLUSTER)
    private StringRedisTemplate mainRedisTemplate;
    @Resource
    private WhiteTestDao whiteTestDao;
    @Resource
    private MysteryRedis mysteryRedis;
    @Resource
    private RoomEventService roomEventService;
    @Resource
    private RoomMemberDao memberDao;
    @Resource
    private MqSenderService mqSenderService;
    @Resource
    private RoomEventRedis roomEventRedis;
    @Autowired
    private EventRankingRecordDao eventRankingRecordDao;

    /**
     * 获取创建房间活动信息
     */
    public EventCreateInfoVO getCreateInfo(RoomEventDTO req) {
        EventCreateInfoVO vo = new EventCreateInfoVO();
        vo.setCostBeans(RoomEventDao.COST_BEAN);
        vo.setEventTypeMap(EVENT_TYPE_LIST);
        List<RoomEventData> roomEventList = roomEventDao.getRoomNotEndEvent(req.getRoomId(), DateHelper.getNowSeconds());
        List<EventCreateInfoVO.EventTimeVO> eventTimeVOList = new ArrayList<>();
        if (!CollectionUtils.isEmpty(roomEventList)) {
            for (RoomEventData eventData : roomEventList) {
                eventTimeVOList.add(new EventCreateInfoVO.EventTimeVO(eventData.getId(), eventData.getStartTime(), eventData.getEndTime()));
            }
        }
        vo.setAlreadyEventTime(eventTimeVOList);
        return vo;
    }

    /**
     * 保存房间event活动
     */
    public void saveRoomEvent(RoomEventDTO req) {
        if (req.getEventId() == null || req.getEventId() == 0) {
            // 创建活动
            // 只有房主和管理员可以创建
            if (!isRoomAdmin(req.getRoomId(), req.getUid())) {
                logger.info("room owner and admin can create. roomId={} uid={}", req.getRoomId(), req.getUid());
                throw new CommonException(new HttpCode(1, "只有房主和管理员可以创建"));
            }
            int nowTime = DateHelper.getNowSeconds();
            // 活动开始时间不能早于当前时间
            if (req.getStartTime() < nowTime) {
                logger.info("The time you entered has expired, please re-enter. roomId={} uid={} startTime={} nowTime={}", req.getRoomId(), req.getUid(), req.getStartTime(), nowTime);
                throw new CommonException(RoomHttpCode.EVENT_TIME_HAS_EXPIRED);
            }
            int endTime = req.getStartTime() + req.getDuration() * 60 * 60;
            try (DistributeLock lock = new DistributeLock(RoomEventDao.ROOM_EVENT_LOCK_KEY + req.getRoomId())) {
                lock.lock();
                List<RoomEventData> roomEventList = roomEventDao.getRoomNotEndEvent(req.getRoomId(), nowTime);
                // 活动时间不能和其他活动重叠
                if (!CollectionUtils.isEmpty(roomEventList)) {
                    if (checkEventTime(req.getStartTime(), endTime, roomEventList)) {
                        logger.info("There is another event created in this period, please change time. roomId={} uid={}", req.getRoomId(), req.getUid());
                        throw new CommonException(RoomHttpCode.PLEASE_CHANGE_TIME);
                    }
                }
                // 扣除创建费用
                deductCost(req.getUid(), req.getRoomId());
                // 保存活动
                RoomEventData data = insertRoomEvent(req);
                // 添加订阅机器人
                eventRobotService.addAutoSubEvent(data.getId(), data.getStartTime());
                // 发送房间活动数量改变消息
                int roomEventNum = roomEventDao.getNotEndRoomEventNum(req.getRoomId(), nowTime);
                sendRoomEventNumChangeMsg(req.getRoomId(), roomEventNum);
            }
        } else {
            // 修改活动
            try (DistributeLock lock = new DistributeLock(RoomEventDao.ROOM_EVENT_LOCK_KEY + req.getEventId())) {
                lock.lock();
                RoomEventData data = roomEventDao.selectById(req.getEventId());
                // 只有创建者可修改
                if (!req.getUid().equals(data.getCreator())) {
                    logger.info("You do not have permission to modify this event. uid={} eventId={}", req.getUid(), req.getEventId());
                    throw new CommonException(RoomHttpCode.NOT_HAVE_PERMISSION_MODIFY_EVENT);
                }
                // 审核通过的活动不允许修改
                if (data.getStatus() == 1) {
                    logger.info("The event has been approved and cannot be modified. uid={} eventId={}", req.getUid(), req.getEventId());
                    throw new CommonException(RoomHttpCode.PARAM_ERROR);
                }
                // 只能修改未开始的活动
                int nowTime = DateHelper.getNowSeconds();
                if (data.getStartTime() <= nowTime) {
                    logger.info("The event has started and cannot be modified. uid={} eventId={}", req.getUid(), req.getEventId());
                    throw new CommonException(RoomHttpCode.EVENT_HAS_STARTED);
                }
                updateRoomEvent(req, data);
            }
        }
    }

    /**
     * 全部房间的活动列表
     */
    public PageVO<RoomEventVO> getRoomEventList(RoomEventDTO req) {
        int nowTime = DateHelper.getNowSeconds();
        boolean isHomepage = Objects.equals(req.getIsHomepage(), 1);
        int page = isHomepage ? 1 : (req.getPage() != null ? req.getPage() : 1);
        int pageSize = 10;
        Set<String> testList = whiteTestDao.getAllIdByTypeCache(WhiteTestDao.WHITE_TYPE_ROOM_ID);
        List<RoomEventData> roomEventList = roomEventDao.getRoomEventList(nowTime, testList);
        List<Integer> allMineSubList = roomEventSubDao.getAllMineSubList(req.getUid(), nowTime);
        if (!CollectionUtils.isEmpty(roomEventList)) {
            List<Integer> finalAllMineSubList = !CollectionUtils.isEmpty(allMineSubList) ? allMineSubList : new ArrayList<>();
            // - 未订阅或者未创建活动的用户：正在进行的活动>即将开始的活动。按开始时间顺序排列。
            // - 已订阅或者自己创建了活动：正在进行的活动（先排列我订阅的活动或者我创建的活动>展示其他正在进行的活动,按开始时间顺序排列）>即将开始的活动，按开始时间顺序排列。
            Comparator<RoomEventData> startEvent = Comparator.comparing(o -> o.getStartTime() < nowTime ? (Objects.equals(req.getUid(), o.getCreator()) || finalAllMineSubList.contains(o.getId()) ? 0 : 1) : 2);
            Comparator<RoomEventData> startTimeAsc = Comparator.comparing(RoomEventData::getStartTime);
            roomEventList.sort(startEvent.thenComparing(startTimeAsc));
            // 分页
            int start = (page - 1) * pageSize;
            int end = Math.min(page * pageSize, roomEventList.size());
            roomEventList = roomEventList.subList(start, end);
        }
        return buildPageVO(nowTime, page, pageSize, null, roomEventList, false, allMineSubList, req.getSlang());
    }

    /**
     * 单个房间的活动列表
     */
    public PageVO<RoomEventVO> getEventRoomList(RoomEventDTO req) {
        if (!StringUtils.hasLength(req.getRoomId())) {
            logger.error("param error. roomId={} ", req.getRoomId());
            throw new CommonException(HttpCode.PARAM_ERROR);
        }
        int nowTime = DateHelper.getNowSeconds();
        int page = req.getPage() != null && req.getPage() > 0 ? req.getPage() : 1;
        int pageSize = 10;
        Integer status = isRoomAdmin(req.getRoomId(), req.getUid()) ? null : 1;
        List<RoomEventData> roomEventList = roomEventDao.getNotEndRoomEvent(req.getRoomId(), nowTime, status, page, pageSize);
        List<Integer> allMineSubList = roomEventSubDao.getAllMineSubList(req.getUid(), nowTime);
        return buildPageVO(nowTime, page, pageSize, null, roomEventList, false, allMineSubList, req.getSlang());
    }

    /**
     * 判断是否为房间管理员（包括房主）
     */
    private boolean isRoomAdmin(String roomId, String uid) {
        RoomRoleData roomRoleData = memberDao.getRoleData(roomId, uid);
        return roomRoleData.getRole() == RoomRoleType.MANAGER || roomRoleData.isHost();
    }

    public PageVO<EventActorVO> getSubList(RoomEventDTO req) {
        PageVO<EventActorVO> vo = new PageVO<>();
        int page = req.getPage() == null || req.getPage() < 1 ? 1 : req.getPage();
        RoomEventData data = roomEventDao.selectById(req.getEventId());
        if (data == null) {
            logger.info("Event has been deleted. eventId={}", req.getEventId());
            throw new CommonException(RoomHttpCode.EVENT_HAS_BEEN_DELETED);
        }
        int pageSize = 10;
        List<RoomEventSubData> subDataList = roomEventSubDao.selectList(req.getEventId(), page, pageSize);
        List<EventActorVO> list = new ArrayList<>();
        if (CollectionUtils.isEmpty(subDataList)) {
            vo.setList(list);
            vo.setNextUrl("");
            return vo;
        }
        for (RoomEventSubData subData : subDataList) {
            list.add(getUserInfo(subData.getUid(), data.getRoomId()));
        }
        vo.setList(list);
        vo.setNextUrl(subDataList.size() >= pageSize ? String.valueOf(page + 1) : "");
        return vo;
    }

    /**
     * 房间活动历史列表
     */
    public PageVO<RoomEventVO> getPastRoomEventList(RoomEventDTO req) {
        int page = req.getPage() == null || req.getPage() < 1 ? 1 : req.getPage();
        int pageSize = 10;
        int nowTime = DateHelper.getNowSeconds();
        List<RoomEventData> pastEventList;
        boolean isAdmin = isRoomAdmin(req.getRoomId(), req.getUid());
        if (isAdmin && req.getTabType() != 0) {
            pastEventList = roomEventDao.getRoomEventRecord(req.getRoomId(), req.getTabType(), nowTime, page, pageSize);
        } else {
            pastEventList = roomEventDao.getPastRoomEvent(req.getRoomId(), nowTime, page, pageSize);
        }
        List<Integer> allMineSubList = roomEventSubDao.getAllMineSubList(req.getUid(), nowTime);
        return buildPageVO(nowTime, page, pageSize, null, pastEventList, true, allMineSubList, req.getSlang());
    }

    /**
     * 用户订阅房间活动
     */
    public void subEvent(RoomEventDTO req) {
        ActorData actorData = actorDao.getActorDataFromCache(req.getUid());
        if (actorData == null) {
            logger.error("can not find actor. uid={}", req.getUid());
            throw new CommonException(RoomHttpCode.PARAM_ERROR);
        }
        try (DistributeLock lock = new DistributeLock(SUB_EVENT_LOCK_KEY + req.getEventId())) {
            lock.lock();
            roomEventService.subEvent(req.getUid(), req.getEventId(), req.getSubOpt(), req.isRobot());
        }
    }

    @Transactional(value = WahoLogMySQLBean.WAHO_LOG_TRANSACTION)
    public void subEvent(String uid, int eventId, int subOpt, boolean isRobot) {
        RoomEventData data = roomEventDao.selectById(eventId);
        if (data == null) {
            logger.error("Event has been deleted. uid={} eventId={}", uid, eventId);
            throw new CommonException(RoomHttpCode.EVENT_HAS_BEEN_DELETED);
        }
        RoomEventSubData subData = roomEventSubDao.selectOne(eventId, uid);
        if (subOpt == RoomEventConstant.SUB_OPT) {
            if (subData != null) {
                return;
            }
            subData = new RoomEventSubData(data.getId(), uid, isRobot ? 1 : 0, data.getStartTime(), data.getEndTime(), DateHelper.getNowSeconds());
            roomEventSubDao.insert(subData);
            data.setSubNum(data.getSubNum() + 1);
            sendSubEventMsg(uid, data.getRoomId(), eventId);
        } else {
            if (subData == null) {
                return;
            }
            roomEventSubDao.delete(subData.getEventId(), uid);
            data.setSubNum(data.getSubNum() - 1);
        }
        roomEventDao.updateSubNum(data);
    }

    public void sendSubEventMsg(String uid, String roomId, int eventId) {
        BaseTaskFactory.getFactory().addSlow(new Task() {
            @Override
            protected void execute() {
                if (null != mysteryRedis.getMysteryId(uid)) {
                    return;
                }
                ActorData actorData = actorDao.getActorDataFromCache(uid);
                if (null == actorData) {
                    return;
                }
                if (!roomPlayerRedis.isActorInRoom(roomId, uid)) {
                    return;
                }
                if (ServerConfig.isProduct() && Boolean.FALSE.equals(mainRedisTemplate.opsForValue().setIfAbsent("str:subEventNotice:" + uid + eventId, uid, 2, TimeUnit.HOURS))) {
                    return;
                }
                RoomNotificationMsg msg = new RoomNotificationMsg();
                msg.setUid(uid);
                msg.setUser_name(actorData.getName());
                msg.setUser_head(ImageUrlGenerator.generateRoomUserUrl(actorData));
                msg.setText(String.format(SUB_EVENT_MSG, actorData.getName()));
                msg.setText_ar(String.format(SUB_EVENT_MSG_AR, actorData.getName()));
                List<HighlightTextObject> list = new ArrayList<>();
                HighlightTextObject object = new HighlightTextObject();
                object.setText(actorData.getName());
                object.setHighlightColor("#FFE200");
                list.add(object);
                msg.setHighlight_text(list);
                msg.setHighlight_text_ar(list);
                msg.setHide_head(1);
                msg.setFromRoomId(roomId);
                roomWebSender.sendRoomWebMsg(roomId, uid, msg, false);
            }
        });
    }

    /**
     * Mine房间活动列表
     */
    public PageVO<RoomEventVO> getMineList(RoomEventDTO req) {
        int page = req.getPage() == null || req.getPage() < 1 ? 1 : req.getPage();
        int pageSize = 10;
        int nowTime = DateHelper.getNowSeconds();
        String uid = req.getUid();
        List<RoomEventData> roomEventList;
        int isSubscribed = 0;
        if (req.getListType() == RoomEventConstant.MINE_SUB_LIST) {
            List<Integer> mineSubList = roomEventSubDao.getMineSubList(uid, nowTime, page, pageSize);
            if (!CollectionUtils.isEmpty(mineSubList)) {
                roomEventList = roomEventDao.selectList(mineSubList);
            } else {
                roomEventList = new ArrayList<>();
            }
            isSubscribed = 1;
        } else {
            int status = req.getListType() == RoomEventConstant.MINE_IN_REVIEW_LIST ? 0 : req.getListType() == RoomEventConstant.MINE_APPROVED_LIST ? 1 : 2;
            roomEventList = roomEventDao.getMyCreatedList(uid, status, page, pageSize);
        }
        List<Integer> allMineSubList = roomEventSubDao.getAllMineSubList(req.getUid(), nowTime);
        return buildPageVO(nowTime, page, pageSize, isSubscribed, roomEventList, false, allMineSubList, req.getSlang());
    }

    /**
     * 删除房间Event活动
     */
    public void removeRoomEvent(RoomEventDTO req) {
        try (DistributeLock lock = new DistributeLock(RoomEventDao.ROOM_EVENT_LOCK_KEY + req.getEventId())) {
            lock.lock();
            RoomEventData data = roomEventDao.selectById(req.getEventId());
            if (data == null) {
                logger.error("Event has been deleted. uid={} eventId={}", req.getUid(), req.getEventId());
                throw new CommonException(RoomHttpCode.EVENT_HAS_BEEN_DELETED);
            }
            if (!RoomUtils.isHomeowner(req.getUid(), req.getRoomId()) && !req.getUid().equals(data.getCreator())) {
                logger.info("You do not have permission to delete this event. uid={} eventId={}", req.getUid(), req.getEventId());
                throw new CommonException(RoomHttpCode.NOT_HAVE_PERMISSION_DELETE_EVENT);
            }
            int nowTime = DateHelper.getNowSeconds();
            if (data.getStartTime() <= nowTime) {
                logger.info("The event has started and cannot be deleted. uid={} eventId={}", req.getUid(), req.getEventId());
                throw new CommonException(RoomHttpCode.EVENT_HAS_STARTED);
            }
            roomEventDao.delete(data.getId());
            // 返还创建费用
            MoneyActionType actionType = MoneyActionType.REFUND_CREATE_ROOM_EVENT_FEE;
            MoneyDetailReq moneyDetailReq = new MoneyDetailReq();
            moneyDetailReq.setId("refund_create_room_event_fee_" + data.getId());
            moneyDetailReq.setRoomId(data.getRoomId());
            moneyDetailReq.setUid(data.getCreator());
            moneyDetailReq.setAtype(actionType.actionType);
            moneyDetailReq.setChanged(RoomEventDao.COST_BEAN);
            moneyDetailReq.setTitle(actionType.title);
            moneyDetailReq.setDesc(actionType.desc);
            mqSenderService.asyncChargeDiamonds(moneyDetailReq);
            roomEventSubDao.batchDeleteByEventId(req.getEventId());
            if (data.getEndTime() > nowTime) {
                // 发送房间活动数量改变消息
                int roomEventNum = roomEventDao.getNotEndRoomEventNum(data.getRoomId(), nowTime);
                sendRoomEventNumChangeMsg(data.getRoomId(), roomEventNum);
            }
        }
    }

    /**
     * 获取房间活动详情
     */
    public RoomEventDetailVO getEventDetail(RoomEventDTO req) {
        RoomEventData data = roomEventDao.selectById(req.getEventId());
        if (data == null) {
            logger.info("Event has been deleted. uid={} eventId={}", req.getUid(), req.getEventId());
            throw new CommonException(RoomHttpCode.EVENT_HAS_BEEN_DELETED);
        }
        MongoRoomData roomData = roomDao.getDataFromCache(data.getRoomId());
        if (roomData == null) {
            logger.info("Room not exist. roomId={}", req.getRoomId());
            throw new CommonException(RoomHttpCode.ROOM_NOT_EXIST);
        }
        int nowTime = DateHelper.getNowSeconds();
        RoomEventDetailVO vo = new RoomEventDetailVO();
        vo.setId(data.getId());
        vo.setRid(roomData.getOwnerRid());
        vo.setRidData(actorDao.getActorDataFromCache(RoomUtils.getRoomHostId(data.getRoomId())).getRidData());
        vo.setAid(data.getCreator());
        vo.setRoomId(data.getRoomId());
        vo.setRoomName(roomData.getName());
        vo.setRoomHead(ImageUrlGenerator.generateRoomUserUrl(roomData.getHead()));
        vo.setRoomUserNum(roomPlayerRedis.getRoomActorsCount(data.getRoomId()));
        vo.setName(data.getName());
        vo.setDescription(data.getDescription());
        vo.setType(data.getType());
        vo.setStartTime(data.getStartTime());
        vo.setEndTime(data.getEndTime());
        vo.setStatus(getRoomEventStatus(data.getStartTime(), data.getEndTime(), nowTime));
        vo.setEventStatus(data.getStatus());
        vo.setIsSubscribed(roomEventSubDao.selectOne(data.getId(), req.getUid()) != null ? 1 : 0);
        vo.setSubNum(data.getSubNum());
        vo.setEventCoverUrl(data.getEventCoverUrl());
        vo.setCreator(getUserInfo(data.getCreator(), data.getRoomId()));
        vo.setReviewReason(data.getReviewReason());
        EventCreateInfoVO.EventTypeVO eventType = CollectionUtil.listToKeyMap(EVENT_TYPE_LIST, EventCreateInfoVO.EventTypeVO::getId).get(data.getType());
        if (eventType != null) {
            vo.setTypeIcon(eventType.getIcon());
            vo.setTypeName(req.getSlang() == SLangType.ARABIC ? eventType.getNameAr() : eventType.getName());
        }
        Map<String, Long> rankingMap;
        if (data.getEndTime() < nowTime - TimeUnit.DAYS.toSeconds(1)) {
            rankingMap = eventRankingRecordDao.getRankingMapFromCache(data.getId(), RoomEventRedis.DIAMOND_RANKING);
        } else {
            rankingMap = roomEventRedis.getRankingMap(data.getId(), RoomEventRedis.DIAMOND_RANKING, 0, -1);
        }
        vo.setSendGiftUserNum(rankingMap.size());
        return vo;
    }

    /**
     * 异步审核
     *
     * @param eventData 活动信息
     */
    private void asyncCheck(RoomEventData eventData) {
        BaseTaskFactory.getFactory().addSlow(new Task() {
            @Override
            protected void execute() {
                if (detectService.detectText(new TextDTO(eventData.getName() + eventData.getDescription(), DetectOriginConstant.ROOM_EVENT_TEXT, eventData.getCreator())).getData().getIsSafe() == 0) {
                    eventData.setReviewerUid("system");
                    eventData.setStatus(2);
                    eventData.setReviewReason("活动标题或描述违规");
                    roomEventDao.updateStatus(eventData);
                    return;
                }
                if (detectService.detectImage(new ImageDTO(eventData.getEventCoverUrl(), DetectOriginConstant.ROOM_EVENT_BACKGROUND, eventData.getCreator())).getData().getIsSafe() == 0) {
                    eventData.setReviewerUid("system");
                    eventData.setStatus(2);
                    eventData.setReviewReason("Banner内容违规");
                    roomEventDao.updateStatus(eventData);
                }
            }
        });
    }

    private String getPosterThumbnails(String posterUrl) {
        if (!StringUtils.hasLength(posterUrl)) {
            return "";
        }
        return posterUrl + "?x-oss-process=image/resize,h_320,m_lfit";
    }

    /**
     * 活动送礼物的排行榜
     */
    public RoomEventRankVO getRankList(RoomEventDTO req) {
        RoomEventData data = roomEventDao.selectById(req.getEventId());
        if (data == null) {
            logger.info("Event has been deleted. uid={} eventId={}", req.getUid(), req.getEventId());
            throw new CommonException(RoomHttpCode.EVENT_HAS_BEEN_DELETED);
        }
        RoomEventRankVO vo = new RoomEventRankVO();
        Map<String, Long> rankingMap;
        if (data.getEndTime() < DateHelper.getNowSeconds() - TimeUnit.DAYS.toSeconds(1)) {
            rankingMap = eventRankingRecordDao.getRankingMapFromCache(data.getId(), RoomEventRedis.CHARM_RANKING);
        } else {
            rankingMap = roomEventRedis.getRankingMap(data.getId(), RoomEventRedis.CHARM_RANKING, 0, -1);
        }
        long totalCharm = rankingMap.values().stream().mapToLong(Long::longValue).sum();
        vo.setSenders(roomEventRedis.getRankingSize(data.getId(), RoomEventRedis.DIAMOND_RANKING));
        vo.setContribution(getStrNum(totalCharm));
        vo.setRankingType(1);
        int page = req.getPage() == null || req.getPage() < 1 ? 1 : req.getPage();
        int pageSize = 10;
        int start = (page - 1) * pageSize;
        int end = page * pageSize;
        List<EventActorVO> list = new ArrayList<>();
        int index = 0;
        for (Map.Entry<String, Long> entry : rankingMap.entrySet()) {
            if (index >= start && index < end) {
                String aid = entry.getKey();
                RoomActorDetailData detailData = roomActorCache.getData(data.getRoomId(), aid, false);
                EventActorVO userInfo = new EventActorVO();
                BeanUtils.copyProperties(detailData, userInfo);
                userInfo.setAid(aid);
                userInfo.setMicFrame(detailData.getMicFrame());
                userInfo.setActiveLevel(detailData.getActiveLevel());
                userInfo.setWealthLevel(detailData.getWealthLevel());
                userInfo.setCharmLevel(detailData.getCharmLevel());
                userInfo.setTotalBeans(entry.getValue() + "");
                list.add(userInfo);
                if (list.size() >= pageSize) {
                    break;
                }
            }
            index++;
        }
        vo.setNextUrl(list.size() == pageSize ? String.valueOf(page + 1) : "");
        vo.setRankList(list);
        return vo;
    }

    /**
     * 获取房间内最近一次要举办的活动信息
     */
    public RoomEventVO getRoomEventVO(String roomId, String uid, int slang) {
        RoomEventVO vo = new RoomEventVO();
        int nowTime = DateHelper.getNowSeconds();
        RoomEventData data = roomEventDao.getOneNotEndRoomEvent(roomId, DateHelper.getNowSeconds());
        if (data != null) {
            vo.setEventId(data.getId());
            vo.setName(data.getName());
            vo.setType(data.getType());
            EventCreateInfoVO.EventTypeVO eventType = CollectionUtil.listToKeyMap(EVENT_TYPE_LIST, EventCreateInfoVO.EventTypeVO::getId).get(data.getType());
            if (eventType != null) {
                vo.setTypeIcon(eventType.getIcon());
                vo.setTypeName(slang == SLangType.ARABIC ? eventType.getNameAr() : eventType.getName());
            }
            vo.setStartTime(data.getStartTime());
            vo.setEndTime(data.getEndTime());
            vo.setStatus(getRoomEventStatus(data.getStartTime(), data.getEndTime(), nowTime));
            RoomEventSubData subData = roomEventSubDao.selectOne(data.getId(), uid);
            vo.setIsSubscribed(subData != null ? 1 : 0);
            vo.setEventCoverUrl(ImageUrlGenerator.generateUrl(data.getEventCoverUrl(), 350, 200));
        }
        return vo;
    }

    private String getStrNum(long num) {
        long oneThousand = 1000L;
        if (num >= oneThousand) {
            return new BigDecimal(num).divide(new BigDecimal(oneThousand), 1, RoundingMode.HALF_UP) + "K";
        } else {
            return num + "";
        }
    }

    /**
     * 发送房间活动数量改变消息
     */
    private void sendRoomEventNumChangeMsg(String roomId, int roomEventNum) {
        BaseTaskFactory.getFactory().addSlow(new Task() {
            @Override
            protected void execute() {
                RoomEventNumChangeMsg msg = new RoomEventNumChangeMsg();
                msg.setRoomEventNum(roomEventNum);
                roomWebSender.sendRoomWebMsg(roomId, "", msg, false);
            }
        });
    }

    /**
     * 校验要保存的活动时间是否与已存在的活动时间冲突
     * 是否冲突：true是 false否
     */
    private boolean checkEventTime(int startTime, int endTime, List<RoomEventData> roomEventList) {
        for (RoomEventData roomEventData : roomEventList) {
            if (startTime > roomEventData.getStartTime() && startTime < roomEventData.getEndTime()) {
                return true;
            }
            if (endTime > roomEventData.getStartTime() && endTime < roomEventData.getEndTime()) {
                return true;
            }
            if (startTime >= roomEventData.getStartTime() && endTime <= roomEventData.getEndTime()) {
                return true;
            }
            if (startTime <= roomEventData.getStartTime() && endTime >= roomEventData.getEndTime()) {
                return true;
            }
        }
        return false;
    }

    /**
     * 保存房间活动到数据库
     */
    private RoomEventData insertRoomEvent(RoomEventDTO req) {
        RoomEventData data = new RoomEventData();
        data.setRoomId(req.getRoomId());
        data.setName(req.getName());
        data.setDescription(req.getDescription());
        data.setType(req.getType());
        data.setDuration(req.getDuration());
        data.setStartTime(req.getStartTime());
        data.setEndTime(req.getStartTime() + req.getDuration() * 60 * 60);
        data.setEventCoverUrl(dealUploadImgUrl(req.getEventCoverUrl()));
        data.setSubNum(0);
        data.setStatus(0);
        data.setCostBeans(RoomEventDao.COST_BEAN);
        data.setCreator(req.getUid());
        data.setCtime(DateHelper.getNowSeconds());
        data.setMtime(DateHelper.getNowSeconds());
        roomEventDao.insert(data);
        // 异步系统审核
//        asyncCheck(data);
        // 埋点
        doEventReport(data);
        return data;
    }

    private RoomEventData updateRoomEvent(RoomEventDTO req, RoomEventData data) {
        data.setName(req.getName());
        data.setDescription(req.getDescription());
        data.setType(req.getType());
        data.setDuration(req.getDuration());
        data.setStartTime(req.getStartTime());
        data.setEndTime(req.getStartTime() + req.getDuration() * 60 * 60);
        data.setEventCoverUrl(dealUploadImgUrl(req.getEventCoverUrl()));
        data.setStatus(0);
        data.setCtime(DateHelper.getNowSeconds());
        roomEventDao.update(data);
        // 异步系统审核
//        asyncCheck(data);
        // 埋点
        doEventReport(data);
        return data;
    }

    private void reportEventActionLogEvent(String roomId, String uid, int eventId, int action, int startTime) {
        RoomEventActionLogEvent event = new RoomEventActionLogEvent();
        event.setUid(uid);
        event.setRoom_id(roomId);
        event.setRoom_event_id(eventId);
        event.setRoom_event_action(action);
        event.setRoom_event_start_time(startTime);
        event.setCtime(DateHelper.getNowSeconds());
        eventReport.track(new EventDTO(event));
    }

    /**
     * 处理上传的图片url
     */
    private String dealUploadImgUrl(String imgUrl) {
        if (!StringUtils.hasLength(imgUrl)) {
            return "";
        }
        if (imgUrl.startsWith("http")) {
            return imgUrl;
        }
        return ImageUrlGenerator.createCdnUrl(imgUrl);
    }

    /**
     * 扣除创建房间活动的费用
     */
    private void deductCost(String uid, String roomId) {
        MoneyDetailReq moneyDetailReq = new MoneyDetailReq();
        moneyDetailReq.setRandomId();
        moneyDetailReq.setRoomId(roomId);
        moneyDetailReq.setUid(uid);
        moneyDetailReq.setAtype(MoneyActionType.CREATE_ROOM_EVENT.actionType);
        moneyDetailReq.setChanged(-RoomEventDao.COST_BEAN);
        moneyDetailReq.setTitle(MoneyActionType.CREATE_ROOM_EVENT.title);
        moneyDetailReq.setDesc(MoneyActionType.CREATE_ROOM_EVENT.desc);
        ApiResult<String> result = dataCenterService.reduceBeans(moneyDetailReq);
        if (result.isError()) {
            if (1 == result.getCode().getCode()) {
                throw new CommonException(RoomHttpCode.UNION_NOT_ENOUGH_DIAMOND);
            }
            logger.error("reduce beans error, msg={}", result.getCode().getMsg());
            throw new CommonException(RoomHttpCode.SERVER_ERROR);
        }
    }

    private RoomEventVO buildRoomEventVO(int nowTime, RoomEventData data, MongoRoomData roomData, Integer isSubscribed, boolean isPastList, List<Integer> allMineSubList, Integer slang) {
        RoomEventVO vo = new RoomEventVO();
        vo.setEventId(data.getId());
        vo.setDesc(data.getDescription());
        vo.setAid(data.getCreator());
        vo.setRid(roomData.getOwnerRid());
        vo.setRidData(actorDao.getActorDataFromCache(RoomUtils.getRoomHostId(data.getRoomId())).getRidData());
        vo.setRoomId(data.getRoomId());
        vo.setRoomName(roomData.getName());
        vo.setRoomHead(ImageUrlGenerator.generateRoomUserUrl(roomData.getHead()));
        vo.setRoomUserNum(roomPlayerRedis.getRoomActorsCount(data.getRoomId()));
        vo.setName(data.getName());
        vo.setStartTime(data.getStartTime());
        vo.setEndTime(data.getEndTime());
        vo.setStatus(getRoomEventStatus(data.getStartTime(), data.getEndTime(), nowTime));
        vo.setEventStatus(data.getStatus());
        vo.setIsSubscribed(Objects.requireNonNullElseGet(isSubscribed, () -> allMineSubList.contains(data.getId()) ? 1 : 0));
        vo.setSubNum(data.getSubNum());
        vo.setCreator(getUserInfo(data.getCreator(), data.getRoomId()));
        vo.setReviewReason(data.getReviewReason());
        EventCreateInfoVO.EventTypeVO eventType = CollectionUtil.listToKeyMap(EVENT_TYPE_LIST, EventCreateInfoVO.EventTypeVO::getId).get(data.getType());
        if (eventType != null) {
            vo.setTypeIcon(eventType.getIcon());
            vo.setTypeName(slang == SLangType.ARABIC ? eventType.getNameAr() : eventType.getName());
        }
        vo.setEventCoverUrl(ImageUrlGenerator.generateUrl(data.getEventCoverUrl(), 350, 200));
        // 历史记录需要展示活动期间发礼物人数和礼物消耗总钻石
        if (isPastList) {
            Map<String, Long> rankingMap = eventRankingRecordDao.getRankingMapFromCache(data.getId(), RoomEventRedis.DIAMOND_RANKING);
            vo.setSendGiftUserNum(rankingMap.size());
            vo.setTotalCost(getStrNum(rankingMap.values().stream().mapToLong(Long::longValue).sum()));
        }
        return vo;
    }

    /**
     * 获取房间活动状态 0未开始 1正在进行 2已结束
     */
    private int getRoomEventStatus(int startTime, int endTime, int nowTime) {
        if (startTime > nowTime) {
            return RoomEventConstant.EVENT_NOT_STARTED;
        } else if (endTime < nowTime) {
            return RoomEventConstant.EVENT_ENDED;
        } else {
            return RoomEventConstant.EVENT_RUNNING;
        }
    }

    private EventActorVO getUserInfo(String uid, String roomId) {
        RoomActorDetailData detailData = roomActorCache.getData(roomId, uid, false);
        EventActorVO userInfo = new EventActorVO();
        BeanUtils.copyProperties(detailData, userInfo);
        userInfo.setMicFrame(detailData.getMicFrame());
        userInfo.setActiveLevel(detailData.getActiveLevel());
        userInfo.setWealthLevel(detailData.getWealthLevel());
        userInfo.setCharmLevel(detailData.getCharmLevel());
        userInfo.setAid(uid);
        return userInfo;
    }

    private PageVO<RoomEventVO> buildPageVO(int nowTime, int page, int pageSize, Integer isSubscribed, List<RoomEventData> roomEventList, boolean isPastList, List<Integer> allMineSubList, Integer slang) {
        if (CollectionUtils.isEmpty(allMineSubList)) {
            allMineSubList = new ArrayList<>();
        }
        PageVO<RoomEventVO> pageVO = new PageVO<>();
        List<RoomEventVO> voList = new ArrayList<>();
        if (CollectionUtils.isEmpty(roomEventList)) {
            pageVO.setList(voList);
            pageVO.setNextUrl("");
            return pageVO;
        }
        for (RoomEventData data : roomEventList) {
            MongoRoomData roomData = roomDao.getDataFromCache(data.getRoomId());
            if (roomData == null) {
                continue;
            }
            voList.add(buildRoomEventVO(nowTime, data, roomData, isSubscribed, isPastList, allMineSubList, slang));
        }
        pageVO.setList(voList);
        pageVO.setNextUrl(roomEventList.size() == pageSize ? String.valueOf(page + 1) : "");
        return pageVO;
    }

    public void doEventReport(RoomEventData data) {
        BackendReviewRecordEvent event = new BackendReviewRecordEvent();
        event.setUid(data.getCreator());
        event.setCreate_ctime(data.getCtime());
        event.setModify_ctime(data.getMtime());
        event.setScene("room_event");
        event.setScene_desc(data.getId() + "");
        event.setSub_scene(data.getRoomId());
        event.setReview_status(data.getStatus());
        eventReport.track(new EventDTO(event));
    }
}
