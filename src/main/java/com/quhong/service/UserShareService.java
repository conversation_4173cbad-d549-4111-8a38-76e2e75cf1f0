package com.quhong.service;

import com.google.common.collect.Interner;
import com.google.common.collect.Interners;
import com.quhong.analysis.EventDTO;
import com.quhong.analysis.EventReport;
import com.quhong.analysis.FamilyLogEvent;
import com.quhong.constant.UserHttpCode;
import com.quhong.core.config.ServerConfig;
import com.quhong.core.utils.DateHelper;
import com.quhong.data.ActorData;
import com.quhong.data.dto.InviteAgentShareDTO;
import com.quhong.data.dto.UserShareDTO;
import com.quhong.data.vo.InviteAgentShareVO;
import com.quhong.data.vo.UserShareVO;
import com.quhong.enums.HttpCode;
import com.quhong.enums.SLangType;
import com.quhong.enums.ShareModeEnum;
import com.quhong.exception.CommonException;
import com.quhong.exception.CommonH5Exception;
import com.quhong.image.ImageUrlGenerator;
import com.quhong.mongo.dao.ActorDao;
import com.quhong.mongo.dao.NoticeNewDao;
import com.quhong.mongo.dao.OfficialDao;
import com.quhong.mongo.data.NoticeNewData;
import com.quhong.mongo.data.OfficialData;
import com.quhong.msg.chat.OfficialPushMsg;
import com.quhong.mysql.config.WahoMySQLBean;
import com.quhong.mysql.dao.FamilyDao;
import com.quhong.mysql.dao.FamilyMemberDao;
import com.quhong.mysql.data.FamilyData;
import com.quhong.mysql.data.FamilyMemberData;
import com.quhong.redis.GenFamilyRidRedis;
import com.quhong.redis.InviteAgentRedis;
import com.quhong.room.RoomWebSender;
import com.quhong.utils.RoomUtils;
import jakarta.annotation.Resource;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.ObjectUtils;

import java.util.Objects;
import java.util.concurrent.ThreadLocalRandom;

/**
 * <AUTHOR>
 * @date 2022/11/30
 */
@Service
public class UserShareService {

    private static final Logger logger = LoggerFactory.getLogger(UserShareService.class);

    private static final String DEBUG_SHARE_ROOM_LINK = "https://share.opswaho.com/shareRoom/";
    private static final String PRO_SHARE_ROOM_LINK = "https://share.waho.live/shareRoom/";

    private static final String DEBUG_SHARE_LIVE_ROOM_LINK = "https://share.opswaho.com/shareLiveRoom/";
    private static final String PRO_SHARE_LIVE_ROOM_LINK = "https://share.waho.live/shareLiveRoom/";

    private static final String DEBUG_SHARE_USER_LINK = "https://share.opswaho.com/shareUser/";
    private static final String PRO_SHARE_USER_LINK = "https://share.waho.live/shareUser/";

    // h5活动网页地址 https://api.opswaho.com/invite-agent/?inviterId=123
    private static final String DEBUG_SHARE_INVITE_AGENT_LINK = "https://api.opswaho.com/invite_agent/";
    private static final String PRO_SHARE_INVITE_AGENT_LINK = "https://statics.waho.live/invite_agent/";
    public static final String DEFAULT_HEAD = "https://cloudcdn.waho.live/resource/op_sys_1693394734_default_family_icon.png";

    private static final String SHARE_ROOM_EVENT_LINK = ServerConfig.isNotProduct() ? "https://share.opswaho.com/share/shareRoomEvent/" : "https://share.waho.live/share/shareRoomEvent/";


    public static final String CODE_TITLE_EN = "Agent Verification Code"; // 注册代理验证码通知
    public static final String CODE_TITLE_AR = "رمز التحقق للوكيل";
    public static final String CODE_MSG_EN = "Someone invited you to be an agent Verification code:%d." +
            "This code is valid for 3 minutes. Please do not disclose it to others";
    public static final String CODE_MSG_AR = "شخص ما قام بدعوتك لتصبح وكيلاً. رمز التحقق: %d. هذا الرمز صالح لمدة 3 دقائق. يرجى عدم الكشف عنه للآخرين.";

    public static final String REV_TITLE_EN = "Agent anchors notification!";
    public static final String REV_TITLE_AR = "إشعار وكلاء المضيفين";
    public static final String REV_MSG_EN = "Congratulations! You have successfully become an agent. You can view the performance and commission of your anchors and invited agents on the agent page in ME page.";
    public static final String REV_MSG_AR = "تهانينا! لقد أصبت بنجاح وكيلاً. يمكنك فحص أداء وبونص مضيفيك والوكلاء المدعوين على صفحة الوكيل في صفحة ME.";

    public static final String SEND_TITLE_EN = "Agent invitation successful!";
    public static final String SEND_TITLE_AR = "دعوة وكيل ناجحة";
    public static final String SEND_MSG_EN = "You have successfully invited %s to become an agent!";
    public static final String SEND_MSG_AR = "لقد قمت بدعوة  %s بنجاح ليصبح وكيلاً!";

    @Resource
    private ActorDao actorDao;
    @Resource
    private InviteAgentRedis inviteAgentRedis;
    @Resource
    private FamilyDao familyDao;
    @Resource
    private GenFamilyRidRedis genFamilyRidRedis;
    @Resource
    protected OfficialDao officialDao;
    @Resource
    protected NoticeNewDao noticeNewDao;
    @Resource
    protected RoomWebSender roomWebSender;
    @Resource
    protected FamilyMemberDao familyMemberDao;
    @Resource
    protected EventReport eventReport;

    private Interner<String> stringPool = Interners.newWeakInterner();

    /**
     * 生成分享链接
     */
    public UserShareVO share(UserShareDTO dto) {
        UserShareVO vo = new UserShareVO();
        switch (Objects.requireNonNull(ShareModeEnum.getModeEnum(dto.getMode()))) {
            case HOME_PAGE -> shareHomePageUrl(dto, vo);
            case ROOM -> shareRoomUrl(dto, vo);
            case INVITE_AGENT -> shareInviteAgentUrl(dto, vo);
            case LIVE_ROOM -> shareLiveRoomUrl(dto, vo);
            case ROOM_EVENT -> shareRoomEvent(dto, vo);
            default -> {
                logger.error("mode param error. mode={}", dto.getMode());
                throw new CommonException(HttpCode.PARAM_ERROR);
            }
        }
        return vo;
    }

    private void shareRoomUrl(UserShareDTO dto, UserShareVO vo) {
        String baseUrl = ServerConfig.isNotProduct() ? DEBUG_SHARE_ROOM_LINK : PRO_SHARE_ROOM_LINK;
        try {
            ActorData actorData = actorDao.getActorData(RoomUtils.getRoomHostId(dto.getMid()));
            vo.setUrl(baseUrl + (ObjectUtils.isEmpty(actorData.getAlphaRid()) ? actorData.getRid() : actorData.getAlphaRid()));
            vo.setDesc(dto.getSlang() == SLangType.ARABIC ? "أنا أستمتع بوقتي في هذه الغرفة. انقر على الرابط لتجدني في الغرفة." : "I'm having a great time in this room. Click the link to find me in the room.");
        } catch (Exception ignored) {
        }
    }

    private void shareLiveRoomUrl(UserShareDTO dto, UserShareVO vo) {
        String baseUrl = ServerConfig.isNotProduct() ? DEBUG_SHARE_LIVE_ROOM_LINK : PRO_SHARE_LIVE_ROOM_LINK;
        try {
            ActorData actorData = actorDao.getActorData(RoomUtils.getRoomHostId(dto.getMid()));
            vo.setUrl(baseUrl + (ObjectUtils.isEmpty(actorData.getAlphaRid()) ? actorData.getRid() : actorData.getAlphaRid()));
            vo.setDesc(dto.getSlang() == SLangType.ARABIC ? "تعال إلى #Waho، شاهد بث %s مباشرًا، وتعرف على أصدقاء جدد!".formatted(actorData.getName()) : "Come to #Waho, watch %s live, and make new friends!".formatted(actorData.getName()));
        } catch (Exception ignored) {
        }
    }

    private void shareHomePageUrl(UserShareDTO dto, UserShareVO vo) {
        String baseUrl = ServerConfig.isNotProduct() ? DEBUG_SHARE_USER_LINK : PRO_SHARE_USER_LINK;
        ActorData actorData = actorDao.getActorData(dto.getMid());
        if (null == actorData) {
            return;
        }
        vo.setUrl(baseUrl + (ObjectUtils.isEmpty(actorData.getAlphaRid()) ? actorData.getRid() : actorData.getAlphaRid()));
        vo.setDesc(dto.getSlang() == SLangType.ARABIC ? "لقد قابلت بعض الأشخاص المثيرين للاهتمام على تطبيق واهو، والدردشة معهم كانت ممتعة حقًا. آمل أن تنضم إلينا!" : "I've met some interesting people on the Waho app, and chatting with them has been really enjoyable. I hope you can join us!");
    }

    public void shareInviteAgentUrl(UserShareDTO dto, UserShareVO vo) {
        String baseUrl = ServerConfig.isNotProduct() ? DEBUG_SHARE_INVITE_AGENT_LINK : PRO_SHARE_INVITE_AGENT_LINK;
        ActorData actorData = actorDao.getActorData(dto.getUid());
        if (null == actorData) {
            return;
        }
        vo.setUrl(baseUrl + "?inviterId=" + actorData.getRid() + "&inviterName=" + actorData.getName());
        vo.setDesc("");
    }

    public InviteAgentShareVO queryUserInfo(InviteAgentShareDTO dto) {
        String rid = dto.getMyRid();
        if (ObjectUtils.isEmpty(rid)) {
            logger.error("not find rid:{}", rid);
            throw new CommonH5Exception(HttpCode.PARAM_ERROR);
        }
        ActorData actorData = actorDao.getActorByRidOrAlphaRid(rid);
        if (ObjectUtils.isEmpty(actorData)) {
            logger.error("not find rid:{}", rid);
            throw new CommonH5Exception(UserHttpCode.INCORRECT_USER_ID);
        }
        InviteAgentShareVO vo = new InviteAgentShareVO();
//        vo.setQueryUid(actorData.getUid());
        vo.setQueryName(actorData.getName());
        vo.setQueryHead(ImageUrlGenerator.generateNormalUrl(actorData.getHead()));
        return vo;
    }


    public void generateCode(InviteAgentShareDTO dto) {
        String rid = dto.getMyRid();
        if (ObjectUtils.isEmpty(rid)) {
            logger.error("not find rid:{}", rid);
            throw new CommonH5Exception(HttpCode.PARAM_ERROR);
        }
        ActorData actorData = actorDao.getActorByRidOrAlphaRid(rid);
        if (ObjectUtils.isEmpty(actorData)) {
            logger.error("not find rid:{}", rid);
            throw new CommonH5Exception(UserHttpCode.INCORRECT_USER_ID);
        }
        String myUid = actorData.getUid();
        int count = inviteAgentRedis.getDayCount(myUid);
        if (count >= 5) {
            logger.error("count gte 5 rid:{} count:{}", rid, count);
            throw new CommonH5Exception(UserHttpCode.AGENT_LIMIT_SEND);
        }
        int code = inviteAgentRedis.getCode(myUid);
        if (code == -1) {
            code = ThreadLocalRandom.current().nextInt(100000, 1000000);//100000-999999
            inviteAgentRedis.setCode(myUid, code);
        }
        int slang = actorData.getSlang();
        String bodyMsg = slang == SLangType.ENGLISH ? CODE_MSG_EN : CODE_MSG_AR;
        String titleMsg = slang == SLangType.ENGLISH ? CODE_TITLE_EN : CODE_TITLE_AR;
        String body = String.format(bodyMsg, code);
        commonOfficialMsg(myUid, titleMsg, body);
        inviteAgentRedis.incrDayCount(myUid);
        logger.info("generateCode success rid:{} myUid:{} code:{}", rid, myUid, code);
    }


    @Transactional(value = WahoMySQLBean.WAHO_TRANSACTION, rollbackFor = {Exception.class, RuntimeException.class})
    public void inviteSubmit(InviteAgentShareDTO dto) {
        String inviteRid = dto.getInviteId();
        String MyRid = dto.getMyRid();
        Integer queryCode = dto.getQueryCode();
        if (ObjectUtils.isEmpty(inviteRid) || ObjectUtils.isEmpty(MyRid) || ObjectUtils.isEmpty(queryCode)) {
            logger.error("inviteRid:{} or MyRid:{} queryCode:{} is empty", inviteRid, MyRid, queryCode);
            throw new CommonH5Exception(HttpCode.PARAM_ERROR);
        }
        ActorData myActorData = actorDao.getActorByRidOrAlphaRid(MyRid);
        ActorData inviteActorData = actorDao.getActorByRidOrAlphaRid(inviteRid);
        if (ObjectUtils.isEmpty(myActorData) || ObjectUtils.isEmpty(inviteActorData)) {
            logger.error("not find actor inviteRid:{} or MyRid:{}", inviteRid, MyRid);
            throw new CommonH5Exception(UserHttpCode.INCORRECT_USER_ID);
        }
        String myUid = myActorData.getUid();
        String inviteUid = inviteActorData.getUid();

        if (myUid.equals(inviteUid)) {
            logger.error("不能邀请自己成为的子代理 myUid:{} MyRid:{} inviteUid:{}", myUid, MyRid, inviteUid);
            throw new CommonH5Exception(UserHttpCode.AGENT_NOT_OWNER);
        }
        int code = inviteAgentRedis.getCode(myUid);
        if (code != queryCode) {
            logger.error("验证码错误 myUid:{} code:{} reqCode:{}", myUid, code, queryCode);
            throw new CommonH5Exception(UserHttpCode.AGENT_CODE_ERROR);
        }

        FamilyData inviteFamily = familyDao.selectByOwnerUid(inviteUid);
        if (inviteFamily == null) {
            logger.error("邀请者不是代理身份 inviteUid:{}", inviteUid);
            throw new CommonH5Exception(UserHttpCode.AGENT_NOT_POWER);
        }
        if (familyDao.isBannedCreateFamily(myActorData.getUid())) {
            throw new CommonH5Exception(UserHttpCode.UNABLE_TO_BECOME_AGENT);
        }
        synchronized (stringPool.intern("agent:invite:" + myUid)) {

            FamilyData myFamily = familyDao.selectByOwnerUid(myUid);
            if (myFamily != null) {
                if (myFamily.getPid() != 0) {
                    if (Objects.equals(myFamily.getPid(), inviteFamily.getId())) {
                        logger.error("你已成为该账号的子代理 pid:{} myUid:{}",
                                myFamily.getPid(), myUid);
                        throw new CommonH5Exception(UserHttpCode.AGENT_IS_ALREADY);
                    } else {
                        logger.error("你已成为其他账号的子代理，需先退出 pid:{} myUid:{} invitePid:{}",
                                myFamily.getPid(), myUid, inviteFamily.getId());
                        throw new CommonH5Exception(UserHttpCode.AGENT_IS_ALREADY_OTHERS);
                    }
                }
                FamilyMemberData memberData = familyMemberDao.selectByUid(myUid);
                if (memberData != null) {
                    logger.error("已经是主播，无法成为代理 myUid:{}", myUid);
                    throw new CommonH5Exception(UserHttpCode.MEMBER_IS_ALREADY);
                }
                myFamily.setPid(inviteFamily.getId());
                familyDao.updateById(myFamily);
            } else {
                FamilyMemberData memberData = familyMemberDao.selectByUid(myUid);
                if (memberData != null) {
                    logger.error("已经是主播，无法成为代理 myUid:{}", myUid);
                    throw new CommonH5Exception(UserHttpCode.MEMBER_IS_ALREADY);
                }
                myFamily = new FamilyData();
                int rid = genFamilyRidRedis.getGenFamilyRid();
                if (rid == 0) {
                    logger.error("gen rid error pid:{} myUid:{} invitePid:{}",
                            myFamily.getPid(), myUid, inviteFamily.getId());
                    throw new CommonH5Exception(UserHttpCode.INCORRECT_USER_ID);
                }
                myFamily.setRid(rid);
                myFamily.setPid(inviteFamily.getId());
                String fName = "Family" + rid;
                myFamily.setName(fName);
                myFamily.setHead(DEFAULT_HEAD);
                myFamily.setOwnerUid(myUid);
                myFamily.setCountry(inviteFamily.getCountry());
                myFamily.setRegion(inviteFamily.getRegion());
                myFamily.setAgentContact(inviteFamily.getAgentContact());
                myFamily.setManager(inviteFamily.getManager());
                myFamily.setSuperManager(inviteFamily.getSuperManager());
                myFamily.setServiceUid(inviteFamily.getServiceUid());
                myFamily.setStatus(1);
                myFamily.setCascadeLevel(inviteFamily.getCascadeLevel() + 1);
                myFamily.setBecomeAgentTime(DateHelper.getNowSeconds());
                myFamily.setCtime(DateHelper.getNowSeconds());
                familyDao.insert(myFamily);
                EventDTO eventDTO = new EventDTO(new FamilyLogEvent(myFamily.getId(), myFamily.getRid(), myFamily.getOwnerUid(), myFamily.getCtime()));
                eventDTO.setEventId("family:" + myFamily.getId());
                eventReport.trackUpdate(eventDTO);
                FamilyMemberData data = new FamilyMemberData();
                data.setUid(myUid);
                data.setFamilyId(myFamily.getId());
                data.setRole(1);
                data.setCtime(DateHelper.getNowSeconds());
                familyMemberDao.insert(data);
            }
            logger.info("邀请子代理成功 myUid:{} myFamilyId:{} myFamilyRid:{} inviteUid:{} inviteFamilyId:{} inviteFamilyRid:{}",
                    myUid, myFamily.getId(), myFamily.getRid(), inviteUid, inviteFamily.getId(), inviteFamily.getRid());
        }

        int mySlang = myActorData.getSlang();
        String revMsg = mySlang == SLangType.ENGLISH ? REV_MSG_EN : REV_MSG_AR;
        String revTitle = mySlang == SLangType.ENGLISH ? REV_TITLE_EN : REV_TITLE_AR;
//        String body1 = String.format(revMsg, inviteRid);
        commonOfficialMsg(myUid, revTitle, revMsg);

        int inviteSlang = inviteActorData.getSlang();
        String sendMsg = inviteSlang == SLangType.ENGLISH ? SEND_MSG_EN : SEND_MSG_AR;
        String sendTitle = inviteSlang == SLangType.ENGLISH ? SEND_TITLE_EN : SEND_TITLE_AR;
        String body2 = String.format(sendMsg, MyRid);
        commonOfficialMsg(inviteUid, sendTitle, body2);
    }


    public void commonOfficialMsg(String uid, String title, String body) {
        OfficialData officialData = new OfficialData();
        officialData.setSubTitle("");
        officialData.setValid(1);
        officialData.setTo_uid(uid);
        officialData.setCtime(DateHelper.getNowSeconds());
        officialData.setNews_type(0);
        officialData.setAtype(0);
        officialData.setAct("");
        officialData.setTitle(title);
        officialData.setBody(body);
        officialDao.save(officialData);
        if (officialData.get_id() != null) {
            noticeNewDao.save(new NoticeNewData(uid, officialData.get_id().toString()));
            OfficialPushMsg msg = new OfficialPushMsg();
            msg.setTitle(officialData.getTitle());
            msg.setBody(officialData.getBody());
            roomWebSender.sendPlayerWebMsg("", uid, uid, msg, false);
        }
    }

    private void shareRoomEvent(UserShareDTO dto, UserShareVO vo) {
        vo.setUrl(SHARE_ROOM_EVENT_LINK + dto.getMid());
        vo.setDesc("Share Room Event");
    }
}
