package com.quhong.service;

import com.quhong.data.DiscountInfo;
import com.quhong.data.dto.StoreDTO;
import com.quhong.data.dto.StoreGoodsDTO;
import com.quhong.data.vo.GoodsListHomeVO;

/**
 * <AUTHOR>
 * @date 2022/9/16
 */

public interface IStoreGoodsService {

    /**
     * @param resId
     * @return
     */
    GoodsListHomeVO.GoodsItemVO getGoodsItem(StoreDTO dto, int resId);

    GoodsListHomeVO getStoreGoodsList(StoreDTO req);

    GoodsListHomeVO getMyGoodsList(StoreDTO dto);

    String buyGoods(StoreGoodsDTO dto);

    String unLockGoods(StoreGoodsDTO dto);

    int getSvipDiscountCost(int svipLevel, int cost);

    DiscountInfo getDiscountInfo(int svipLevel, int price);
}
