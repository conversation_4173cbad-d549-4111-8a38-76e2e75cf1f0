package com.quhong.service;

import com.google.common.collect.Interner;
import com.google.common.collect.Interners;
import com.quhong.core.utils.DateHelper;
import com.quhong.data.SendGiftData;
import com.quhong.data.vo.OtherRankConfigVO;
import com.quhong.data.vo.WindVO;
import com.quhong.mongo.data.OtherRankingActivityData;
import com.quhong.mongo.data.PackConfigData;
import com.quhong.redis.ActivityCommonRedis;
import com.quhong.utils.RoomUtils;
import jakarta.annotation.Resource;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;
import org.springframework.util.ObjectUtils;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;

import static com.quhong.constant.MoneyActionType.REACHING_REWARD_V2;


/**
 * 埃及惠风节活动
 */
@Service
public class SafariService extends OtherActivityService{

    private static final Logger logger = LoggerFactory.getLogger(SafariService.class);
    private static final String ACTIVITY_TITLE_EN = "Safari";
    private static final String ACTIVITY_TITLE_AR = "سفاري";
    private static final String ACTIVITY_DESC = "Safari reward";
    private static final Interner<String> stringPool = Interners.newWeakInterner();
    private static final List<Integer> REWARD_LEVEL_LIST = Arrays.asList(10000, 100000, 300000, 600000, 1000000);
    private static final List<String> REWARD_LEVEL_KEY_LIST = Arrays.asList("safari202407-10k", "safari202407-100k", "safari202407-300k", "safari202407-600k", "safari202407-1000k");

    @Resource
    private ActivityCommonRedis activityCommonRedis;
    @Resource
    private PackService packService;

    private String getDailyRankKey(String activityId, String queryDate){
        return String.format("dailyRank:%s:%s", activityId, queryDate);
    }

    private String getDailyDate(String activityId){
        return String.format("dailyDate:%s", activityId);
    }


    public OtherRankConfigVO safariConfig(String activityId, String uid, String queryDate) {
        OtherRankingActivityData activity = otherActivityService.getOtherRankingActivity(activityId);
        OtherRankConfigVO vo = new OtherRankConfigVO();
        vo.setStartTime(activity.getStartTime());
        vo.setEndTime(activity.getEndTime());
        vo.setCurrentDate(DateHelper.ARABIAN.formatDateInDay());
        // vo.setCurrentDate(activityCommonRedis.getCommonStrValue(getDailyDate(activityId)));

        String roomId = RoomUtils.formatRoomId(uid);
        String dailyRankKey = getDailyRankKey(activityId, queryDate);
        vo.setScore(activityCommonRedis.getCommonZSetRankingScore(dailyRankKey, roomId));
        return vo;
    }

    public void handleGiftMqMsg(SendGiftData giftData, String activityId) {
        String roomId = giftData.getRoomId();
        if(ObjectUtils.isEmpty(roomId)){
            return;
        }

        synchronized (stringPool.intern(ACTIVITY_TITLE_EN)) {
            // 日榜增加数值
            String currentDay = DateHelper.ARABIAN.formatDateInDay();
            String dailyRankKey = getDailyRankKey(activityId, currentDay);
            int currentNum = activityCommonRedis.getCommonZSetRankingScore(dailyRankKey, roomId);
            int value = giftData.getNumber() * giftData.getPrice() * giftData.getAid_list().size();

            while (value > 0){
                List<Integer> tempLevelNumList = new ArrayList<>(REWARD_LEVEL_LIST);
                int currentLevelIndex = 0;
                if(tempLevelNumList.contains(currentNum)){
                    currentLevelIndex = tempLevelNumList.indexOf(currentNum);
                }else {
                    tempLevelNumList.add(currentNum);
                    tempLevelNumList.sort(Integer::compare);
                    currentLevelIndex = tempLevelNumList.indexOf(currentNum) - 1;
                }

                int upLevelIndex = currentLevelIndex + 1;
                if(upLevelIndex >= REWARD_LEVEL_LIST.size()){
                    activityCommonRedis.incrCommonZSetRankingScore(dailyRankKey, roomId, value);
                    value = 0;
                }else {
                    int upLevelNum = REWARD_LEVEL_LIST.get(upLevelIndex);     // 下一级的数量
                    int needUpNum = upLevelNum - currentNum;                     // 需要升级到下一级的数量
                    if(value >= needUpNum){                                      // 如果【增加的数量】大于等于【需要升级到下一级的数量】则升级, 否则不升级直接增加数量
                        currentNum = currentNum + needUpNum;
                        value  = value - needUpNum;
                        activityCommonRedis.incrCommonZSetRankingScore(dailyRankKey, roomId, needUpNum);
                        String resKey = REWARD_LEVEL_KEY_LIST.get(upLevelIndex);
                        String uid = RoomUtils.getRoomHostId(roomId);
                        packService.sendPackage(uid, resKey, REACHING_REWARD_V2.actionType,
                                String.format(REACHING_REWARD_V2.title, ACTIVITY_TITLE_EN),
                                REACHING_REWARD_V2.desc, 1, "Safari-Stage rewards", 1);
                    }else {
                        activityCommonRedis.incrCommonZSetRankingScore(dailyRankKey, roomId, value);
                        value = 0;
                    }
                }
            }
        }
    }

}
