package com.quhong.service;

import com.quhong.data.dto.MoneyDetailReq;
import com.quhong.data.vo.ProductListVO;
import com.quhong.enums.ApiResult;
import com.quhong.enums.HttpCode;
import com.quhong.exception.CommonException;
import com.quhong.feign.DataCenterService;
import com.quhong.httpResult.PayHttpCode;
import com.quhong.mongo.dao.ActorDao;
import com.quhong.mysql.dao.HeartRecordDao;
import com.quhong.utils.WalletUtils;
import jakarta.annotation.Resource;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;


@Service
public class CoinsService {
    private static final Logger logger = LoggerFactory.getLogger(CoinsService.class);

    private static final String RECORD_TITLE = "Buy Coins";
    private static final int COINS_MONEY_TYPE = 180;

    private static final List<ProductListVO.CoinProduct> COINS_PRODUCTS = new ArrayList<>();

    static {
        COINS_PRODUCTS.add(new ProductListVO.CoinProduct(1, 110, 1100));
        COINS_PRODUCTS.add(new ProductListVO.CoinProduct(2, 1100, 11000));
        COINS_PRODUCTS.add(new ProductListVO.CoinProduct(3, 5500, 55000));
        COINS_PRODUCTS.add(new ProductListVO.CoinProduct(4, 11000, 110000));
        COINS_PRODUCTS.add(new ProductListVO.CoinProduct(5, 55000, 550000));
        COINS_PRODUCTS.add(new ProductListVO.CoinProduct(6, 1100000, 11000000));
    }

    @Resource
    private HeartRecordDao heartRecordDao;
    @Resource
    private ActorDao actorDao;
    @Resource
    private DataCenterService dataCenterService;

    public List<ProductListVO.CoinProduct> getCoinsProducts() {
        List<ProductListVO.CoinProduct> coinProductList = new ArrayList<>();
        COINS_PRODUCTS.forEach(k -> {
            ProductListVO.CoinProduct showCoinProduct = new ProductListVO.CoinProduct();
            showCoinProduct.setPid(k.getPid());
            showCoinProduct.setCoins(k.getCoins());
            showCoinProduct.setDiamonds(k.getDiamonds());
            coinProductList.add(showCoinProduct);
        });
        return coinProductList;
    }

    private void deductDiamonds(String uid, int changed) {
        MoneyDetailReq moneyDetailReq = new MoneyDetailReq();
        moneyDetailReq.setRandomId();
        moneyDetailReq.setUid(uid);
        moneyDetailReq.setAtype(COINS_MONEY_TYPE);
        moneyDetailReq.setChanged(-changed);
        moneyDetailReq.setTitle(RECORD_TITLE);
        moneyDetailReq.setDesc(RECORD_TITLE);
        ApiResult<String> result = dataCenterService.reduceBeans(moneyDetailReq);
        if (result.isError()) {
            if (1 == result.getCode().getCode()) {
                throw new CommonException(PayHttpCode.NOT_ENOUGH_DIAMOND);
            }
            logger.error("reduce beans error, uid={} msg={}", uid, result.getCode().getMsg());
            throw new CommonException(HttpCode.SERVER_ERROR);
        }
    }

    public ProductListVO.Balance buyCoins(String uid, int pid) {
        ProductListVO.CoinProduct coinProductData = COINS_PRODUCTS.get(pid - 1);
        if (coinProductData == null) {
            logger.error("params error.uid={}, pid={}", uid, pid);
            throw new CommonException(HttpCode.PARAM_ERROR);
        }
        int coins = WalletUtils.diamondsToRaw(coinProductData.getCoins());
        int diamonds = WalletUtils.diamondsToRaw(coinProductData.getDiamonds());
        deductDiamonds(uid, diamonds);
        boolean heartFlag = heartRecordDao.changeHeart(uid, coins, RECORD_TITLE, RECORD_TITLE);
        if (!heartFlag) {
            throw new CommonException(HttpCode.SERVER_ERROR);
        }
        ProductListVO.Balance balanceVo = new ProductListVO.Balance();
        balanceVo.setDiamonds(WalletUtils.diamondsForDisplay(actorDao.getBalance(uid)));
        balanceVo.setCoins(WalletUtils.diamondsForDisplay(actorDao.getActorData(uid).getHeartGot()));
        balanceVo.setVirtualDiamonds(WalletUtils.diamondsForDisplay(actorDao.getActorData(uid).getVirtualDiamonds()));
        return balanceVo;
    }
}
