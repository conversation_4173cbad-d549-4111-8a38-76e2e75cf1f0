package com.quhong.service;

import com.alibaba.fastjson.JSON;
import com.quhong.api.UserService;
import com.quhong.constant.AdminActionCode;
import com.quhong.core.utils.DateHelper;
import com.quhong.data.ActorData;
import com.quhong.data.dto.AdminDTO;
import com.quhong.data.dto.ResourcesDTO;
import com.quhong.data.dto.WelcomePackDTO;
import com.quhong.data.vo.WelcomePackReceiverVO;
import com.quhong.data.vo.WelcomePackRecordVO;
import com.quhong.data.vo.WelcomePackVO;
import com.quhong.enums.*;
import com.quhong.exception.CommonH5Exception;
import com.quhong.exceptions.AdminCommonException;
import com.quhong.feign.DataCenterService;
import com.quhong.image.ImageUrlGenerator;
import com.quhong.mongo.dao.*;
import com.quhong.mongo.data.*;
import com.quhong.mq.MqSenderService;
import com.quhong.msg.obj.PackInfoObject;
import com.quhong.msg.room.WelcomePackMsg;
import com.quhong.mysql.dao.CountriesDao;
import com.quhong.mysql.dao.FamilyDao;
import com.quhong.mysql.dao.HeartRecordDao;
import com.quhong.mysql.dao.WelcomePackLogDao;
import com.quhong.mysql.data.FamilyData;
import com.quhong.mysql.data.WelcomePackLogData;
import com.quhong.room.RoomWebSender;
import com.quhong.utils.ActorUtils;
import com.quhong.utils.WalletUtils;
import com.quhong.vo.PageVO;
import jakarta.annotation.Resource;
import org.bson.types.ObjectId;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.data.mongodb.core.query.Criteria;
import org.springframework.data.mongodb.core.query.Query;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2023/9/4
 */
@Service
public class WelcomePackService {

    public final Logger logger = LoggerFactory.getLogger(getClass());

    private static final String PERMANENT = "Permanent";
    private static final String PERMANENT_AR = "دائم";

    @Resource
    private ActorDao actorDao;
    @Resource
    private HeartRecordDao heartRecordDao;
    @Resource
    private UserService userService;
    @Resource
    private UserLevelDao userLevelDao;
    @Resource
    private RoomWebSender roomWebSender;
    @Resource
    private MqSenderService mqSenderService;
    @Resource
    private OfficialMsgService officialMsgService;
    @Resource
    private WelcomePackLogDao welcomePackLogDao;
    @Resource
    private WelcomePackConfigDao welcomePackConfigDao;
    @Resource
    private AdminHandleLogDao adminHandleLogDao;
    @Resource
    private AdminService adminService;
    @Resource
    private CountriesDao countriesDao;
    @Resource
    private AdminUserDao adminUserDao;
    @Resource
    private DataCenterService dataCenterService;

    public PageVO<WelcomePackVO> selectList(String adminUid) {
        PageVO<WelcomePackVO> pageVO = new PageVO<>();
        List<WelcomePackConfigData> packConfigList = welcomePackConfigDao.getAllList();
        AdminUserData adminUserData = adminUserDao.findData(adminUid);
        List<WelcomePackVO> list = new ArrayList<>();
        if (!CollectionUtils.isEmpty(packConfigList)) {
            packConfigList = packConfigList.stream().filter(p -> p.getAdminRoleLevel().contains(adminUserData.getLevel())).collect(Collectors.toList());
            for (WelcomePackConfigData data : packConfigList) {
                WelcomePackVO vo = new WelcomePackVO();
                vo.setPackId(data.get_id().toString());
                vo.setPackName(data.getName());
                list.add(vo);
            }
        }
        pageVO.setList(list);
        return pageVO;
    }

    public PageVO<WelcomePackReceiverVO> check(WelcomePackDTO dto) {
        AdminUserData adminUserData = adminUserDao.findData(dto.getUid());
        if (adminUserData == null) {
            throw new AdminCommonException(HttpCode.PARAM_ERROR);
        }
        String familyManagerAccount = adminService.getFamilyManagerAccount(adminUserData.getOperationUid());
        PageVO<WelcomePackReceiverVO> pageVO = new PageVO<>();
        List<WelcomePackReceiverVO> list = new ArrayList<>();
        Set<String> noPerOptRidSet = new HashSet<>();
        if (StringUtils.hasLength(dto.getRidsText())) {
            String[] rids = dto.getRidsText().trim().split(",");
            for (String strRid : rids) {
                WelcomePackReceiverVO vo = new WelcomePackReceiverVO();
                vo.setRid(strRid.trim());
                ActorData actorData = actorDao.getActorByRidOrAlphaRid(strRid);
                if (actorData == null) {
                    vo.setIsValidRid(0);
                    list.add(vo);
                    continue;
                }
                if (!adminService.canOptUser(familyManagerAccount, actorData)) {
                    noPerOptRidSet.add(actorData.getRid() + "");
                    continue;
                }
                vo.setIsValidRid(1);
                vo.setUid(actorData.getUid());
                vo.setName(actorData.getName());
                vo.setGender(actorData.getFb_gender());
                vo.setHead(ImageUrlGenerator.generateRoomUserUrl(actorData.getHead()));
                vo.setCountry(countriesDao.getCountryNameByCode(actorData.getCountry()));
                vo.setWealthLevel(userLevelDao.getUserLevel(actorData.getUid(), UserLevelConstant.WEALTH_LEVEL));
                vo.setRegisterTime(new ObjectId(actorData.getUid()).getTimestamp());
                list.add(vo);
            }
        }
        pageVO.setList(list);
        if (!noPerOptRidSet.isEmpty()) {
            pageVO.setNextUrl("Users(%s) don't belong to you. You can't deliver welcome package to him/her.".formatted(String.join(",", noPerOptRidSet)));
        }
        return pageVO;
    }

    /**
     * 发送欢迎礼包
     */
    public synchronized void sendWelcomePack(Set<String> aidSet, String packId, String adminUid) {
        if (CollectionUtils.isEmpty(aidSet)) {
            throw new AdminCommonException(new HttpCode(1, "请输入要递送礼包的用户rid"));
        }
        WelcomePackConfigData config = welcomePackConfigDao.findById(packId);
        if (null == config) {
            throw new AdminCommonException(new HttpCode(1, "未找到礼包"));
        }
        AdminUserData adminUserData = adminUserDao.findData(adminUid);
        if (!config.getAdminRoleLevel().contains(adminUserData.getLevel())) {
            throw new AdminCommonException(new HttpCode(1, "没有权限"));
        }
        if (CollectionUtils.isEmpty(config.getPackList())) {
            logger.info("send welcome pack. pack is empty. packId={}", packId);
            return;
        }
        String familyManagerAccount = adminService.getFamilyManagerAccount(adminUserData.getOperationUid());
        Set<String> invalidRidSet = new HashSet<>();
        Set<String> noPerOptRidSet = new HashSet<>();
        for (String aid : aidSet) {
            if (!StringUtils.hasLength(aid)) {
                continue;
            }
            ActorData actorData = actorDao.getActorDataFromCache(aid);
            if (actorData == null) {
                logger.info("can not find actor data. aid={}", aid);
                continue;
            }
            if (config.getRegisterDayLimit() > 0 && ActorUtils.getRegDays(aid) > config.getRegisterDayLimit()) {
                invalidRidSet.add(actorData.getRid() + "");
                continue;
            }
            if (!adminService.canOptUser(familyManagerAccount, actorData)) {
                noPerOptRidSet.add(aid);
                continue;
            }
            int oldWealthLevel = 0;
            for (PackData packData : config.getPackList()) {
                if (packData.getResType() == ResTypeEnum.COIN.getType()) {
                    heartRecordDao.changeHeart(aid, packData.getNum(), "Welcome Package", "Welcome Package");
                } else if (packData.getResType() == ResTypeEnum.WEALTH_LEVEL.getType()) {
                    oldWealthLevel = userLevelDao.getUserLevel(aid, UserLevelConstant.WEALTH_LEVEL);
                    // 财富等级大于礼包财富等级时不覆盖财富等级
                    if (oldWealthLevel < packData.getNum()) {
                        userService.updateUserLevel(aid, UserLevelConstant.WEALTH_LEVEL, packData.getNum());
                    }
                } else if (packData.getResType() == ResTypeEnum.VIRTUAL_DIAMOND.getType()) {
                    dataCenterService.changeVirtualDiamond(aid, packData.getNum(), "Welcome Package", "Welcome Package");
                } else {
                    mqSenderService.asyncHandleResources(getResourcesDTO(aid, packData, 2));
                }
            }
            WelcomePackLogData welcomePackLogData = new WelcomePackLogData(aid, packId, config.getName(), adminUserData.getAccount());
            // 发送记录
            welcomePackLogDao.insert(welcomePackLogData);
            // 官方通知及im消息
            sendOfficialData(welcomePackLogData, config, oldWealthLevel);
            adminHandleLogDao.insert(new AdminHandleLogData(aid, adminUid, AdminActionCode.SEND_WELCOME_PACK, String.format(AdminActionCode.SEND_WELCOME_PACK_DESC, welcomePackLogData.getPackName())));
        }
        if (!noPerOptRidSet.isEmpty()) {
            String msg = "Users(%s) don't belong to you. You can't deliver welcome package to him/her.".formatted(String.join(",", noPerOptRidSet));
            throw new CommonH5Exception(new HttpCode(1, msg));
        }
        if (!invalidRidSet.isEmpty()) {
            String msg = "%d IDs have exceeded the registration day limit, and the gift package delivery failed. id：%s。".formatted(invalidRidSet.size(), String.join("/", invalidRidSet));
            throw new CommonH5Exception(new HttpCode(1, msg));
        }
    }

    private static ResourcesDTO getResourcesDTO(String aid, PackData packData, int actionType) {
        ResourcesDTO dto = new ResourcesDTO();
        dto.setUid(aid);
        dto.setResId(String.valueOf(packData.getResId()));
        dto.setResType(packData.getResType());
        if(packData.getLockType() > 0){
            dto.setLockType(actionType == BaseDataResourcesConstant.ACTION_DELETE ? BaseDataResourcesConstant.REMOVE_LOCK_TYPE : packData.getLockType());
        }else {
            dto.setLockType(0);
        }
        if (BaseDataResourcesConstant.TYPE_BAG_GIFT == packData.getResType()) {
            dto.setNum(packData.getNum());
            dto.setDays(1);
        } else if (BaseDataResourcesConstant.TYPE_VIP_LEVEL == packData.getResType()) {
            dto.setResLevel(packData.getResId());
            dto.setDays(packData.getNum());
            dto.setLockType(packData.getLockType());
        } else {
            dto.setDays(packData.getNum());
        }
        dto.setGetWay(BaseDataResourcesConstant.TYPE_WELCOME_PACK_GET);
        dto.setActionType(actionType);
        dto.setmTime(DateHelper.getNowSeconds());
        // 自然天过期
        dto.setGainType(0);
        return dto;
    }

    /**
     * 撤回欢迎礼包
     */
    public synchronized void revertWelcomePack(String rid, String adminUid) {
        ActorData actorData = adminService.paramCheck(rid);
        WelcomePackLogData packLogData = welcomePackLogDao.getReceivedRecentlyOne(actorData.getUid());
        if (null == packLogData) {
            throw new AdminCommonException(new HttpCode(1, "未找到礼包发送记录"));
        }
        if (DateHelper.getNowSeconds() - packLogData.getCtime() > TimeUnit.DAYS.toSeconds(1)) {
            logger.info("revert welcome pack. pack post more then one day. id={}", packLogData.getId());
            throw new AdminCommonException(new HttpCode(1, "礼包下发已超过一天，无法撤回"));
        }
        WelcomePackConfigData welcomePackConfig = welcomePackConfigDao.findById(packLogData.getPackId());
        if (null == welcomePackConfig) {
            throw new AdminCommonException(new HttpCode(1, "未找到礼包"));
        }
        AdminUserData adminUserData = adminUserDao.findData(adminUid);
        if (!welcomePackConfig.getAdminRoleLevel().contains(adminUserData.getLevel())) {
            throw new AdminCommonException(new HttpCode(1, "权限不足"));
        }
        for (PackData packData : welcomePackConfig.getPackList()) {
            // 撤回资源
            if (packData.getResType() == ResTypeEnum.COIN.getType()) {
                heartRecordDao.changeHeart(packLogData.getUid(), -packData.getNum(), "Welcome Package", "Take back rewards");
            } else if (packData.getResType() == ResTypeEnum.WEALTH_LEVEL.getType()) {
                logger.info("revert welcome pack. not support wealth level revert. id={} uid={}", packLogData.getId(), packLogData.getUid());
            } else if (packData.getResType() == ResTypeEnum.VIRTUAL_DIAMOND.getType()) {
                dataCenterService.changeVirtualDiamond(packLogData.getUid(), -packData.getNum(), "Welcome Package", "Take back rewards");
            } else {
                mqSenderService.asyncHandleResources(getResourcesDTO(packLogData.getUid(), packData, 4));
            }
        }
        packLogData.setStatus(2);
        packLogData.setRevertName(adminUserData.getAccount());
        welcomePackLogDao.updateById(packLogData);
        // 删除官方通知
        officialMsgService.deleteMsg(packLogData.getUid(), packLogData.getCtime());
        adminHandleLogDao.insert(new AdminHandleLogData(packLogData.getUid(), adminUid, AdminActionCode.REVERT_WELCOME_PACK, String.format(AdminActionCode.REVERT_WELCOME_PACK_DESC, packLogData.getPackName())));
    }

    public PageVO<WelcomePackRecordVO> getPackageList(AdminDTO dto) {
        PageVO<WelcomePackRecordVO> pageVO = new PageVO<>();
        ActorData actorData = adminService.paramCheck(dto.getRid());
        List<WelcomePackLogData> welcomePackLogList = welcomePackLogDao.selectListByUid(actorData.getUid());
        if (CollectionUtils.isEmpty(welcomePackLogList)) {
            return pageVO;
        }
        List<WelcomePackRecordVO> list = new ArrayList<>();
        for (WelcomePackLogData logData : welcomePackLogList) {
            WelcomePackRecordVO vo = new WelcomePackRecordVO();
            vo.setPackName(logData.getPackName());
            vo.setSendName(logData.getSendName());
            vo.setCtime(DateHelper.ARABIAN.formatDateTime(new Date(logData.getCtime() * 1000L)));
            list.add(vo);
        }
        pageVO.setList(list);
        adminHandleLogDao.insert(new AdminHandleLogData(actorData.getUid(), dto.getUid(), AdminActionCode.VIEW_WELCOME_PACK_RECORD, String.format(AdminActionCode.VIEW_WELCOME_PACK_RECORD_DESC, dto.getRid())));
        return pageVO;
    }

    private void sendOfficialData(WelcomePackLogData logData, WelcomePackConfigData welcomePackConfig, int oldWealthLevel) {
        ActorData actorData = actorDao.getActorDataFromCache(logData.getUid());
        int slang = actorData != null ? actorData.getSlang() : SLangType.ARABIC;
        WelcomePackMsg msg = sendWelcomePackMsg(logData.getUid(), slang, welcomePackConfig, oldWealthLevel);
        OfficialData officialData = new OfficialData();
        officialData.setTo_uid(logData.getUid());
        officialData.setTitle(slang == SLangType.ENGLISH ? welcomePackConfig.getTitle() : welcomePackConfig.getTitleAr());
        officialData.setBody(slang == SLangType.ENGLISH ? welcomePackConfig.getDesc() : welcomePackConfig.getDescAr());
        officialData.setValid(1);
        officialData.setAtype(0);
        officialData.setNews_type(6);
        List<PackInfoObject> packList = msg.getPackList();
        List<OfficialData.AwardInfo> awardList = new ArrayList<>();
        for (PackInfoObject packInfoObject : packList) {
            awardList.add(new OfficialData.AwardInfo(packInfoObject.getName(), packInfoObject.getIcon(), packInfoObject.getTag()));
        }
        officialData.setAward_list(awardList);
        // 和记录时间相同方便撤回时删除
        officialData.setCtime(logData.getCtime());
        officialMsgService.officialMsgPush(officialData);
    }

    private WelcomePackMsg sendWelcomePackMsg(String uid, int slang, WelcomePackConfigData configData, int oldWealthLevel) {
        WelcomePackMsg msg = new WelcomePackMsg();
        msg.setTitle(SLangType.ENGLISH == slang ? configData.getTitle() : configData.getTitleAr());
        msg.setDesc(SLangType.ENGLISH == slang ? configData.getDesc() : configData.getDescAr());
        for (PackData packData : configData.getPackList()) {
            if (packData.getResType() == ResTypeEnum.WEALTH_LEVEL.getType()) {
                // 财富等级大于礼包财富等级时不显示奖励
                if (oldWealthLevel >= packData.getNum()) {
                    continue;
                }
            }
            msg.getPackList().add(getPackInfoObject(packData, slang));
        }
        roomWebSender.sendPlayerWebMsg(null, null, uid, msg, true);
        return msg;
    }

    private PackInfoObject getPackInfoObject(PackData packData, int slang) {
        PackInfoObject packInfoObject = new PackInfoObject();
        packInfoObject.setIcon(packData.getIcon());
        ResTypeEnum typeEnum = ResTypeEnum.getByType(packData.getResType());
        if (null == typeEnum) {
            logger.error("cannot find res. packData={}", JSON.toJSONString(packData));
            return packInfoObject;
        }
        packInfoObject.setName(SLangType.ENGLISH == slang ? typeEnum.getNameEn() : typeEnum.getNameAr());
        if (-1 == packData.getNum()) {
            packInfoObject.setTag(SLangType.ENGLISH == slang ? PERMANENT : PERMANENT_AR);
        } else {
            if (typeEnum == ResTypeEnum.COIN || typeEnum == ResTypeEnum.DIAMONDS || typeEnum == ResTypeEnum.VIRTUAL_DIAMOND) {
                Integer num = WalletUtils.diamondsForDisplay(packData.getNum());
                packInfoObject.setTag(SLangType.ENGLISH == slang ? typeEnum.getTagEn().formatted(num) : typeEnum.getTagAr().formatted(num));
            } else {
                packInfoObject.setTag(SLangType.ENGLISH == slang ? typeEnum.getTagEn().formatted(packData.getNum()) : typeEnum.getTagAr().formatted(packData.getNum()));
            }
        }
        return packInfoObject;
    }
}
