package com.quhong.service;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.google.common.collect.Interner;
import com.google.common.collect.Interners;
import com.quhong.analysis.ActivityExchangeEvent;
import com.quhong.analysis.EventDTO;
import com.quhong.analysis.EventReport;
import com.quhong.constant.ActivityHttpCode;
import com.quhong.core.config.ServerConfig;
import com.quhong.core.utils.DateHelper;
import com.quhong.data.CommonMqTopicData;
import com.quhong.data.vo.GiftCollectVO;
import com.quhong.data.vo.OtherRankingListVO;
import com.quhong.enums.BaseDataResourcesConstant;
import com.quhong.exception.CommonH5Exception;
import com.quhong.mongo.dao.ResourceKeyConfigDao;
import com.quhong.mongo.data.OtherRankingActivityData;
import com.quhong.mongo.data.ResourceKeyConfigData;
import com.quhong.mysql.dao.WhiteTestDao;
import jakarta.annotation.Resource;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

@Service
public class GiftCollectService extends OtherActivityService {


    private static final Logger logger = LoggerFactory.getLogger(GiftCollectService.class);
    private static final String ACTIVITY_TITLE_EN = "Gift Nabob";
    private static final String ACTIVITY_TITLE_AR = "جامع الهدايا";
    private static final String ACTIVITY_DESC = "Gift Nabob Reward";
    private static final String ACTIVITY_ID = "671a09908b7200001f000c66";
    private static final String ACTIVITY_URL = ServerConfig.isProduct() ? String.format("https://statics.waho.live/gift_collector/?activityId=%s", ACTIVITY_ID) : String.format("https://api.opswaho.com/gift_collector/?activityId=%s", ACTIVITY_ID);
    private static final String ACTIVITY_PRIZE_ICON = "https://cdn3.qmovies.tv/common/op_1724754132_fjrk.png";
    private static final Integer RECORD_PAGE_SIZE = 10;
    private static final Integer UPGRADE_GIFT_NUMBER = 5;
    private static final Interner<String> stringPool = Interners.newWeakInterner();
    private static final List<String> SECTION_CONFIG_LIST = Arrays.asList("new", "master", "super");
    private static final String SECTION_COMBINE_KEY = "upgrade";
    private static final Map<String, List<Integer>> RESOURCE_KEY_GIFT_MAP = new HashMap<>();
    private static final Map<String, String> SECTION_BADGE_MAP = new HashMap<>();
    private static final Map<String, Integer> SECTION_TYPE_MAP = new HashMap<>();
    private static final Map<Integer, Integer> SECTION_GIFT_UPGRADE_MAP = new HashMap<>();
    private static final List<String> RESOURCE_CONFIG_LIST = Arrays.asList("giftCollectPrimary1", "giftCollectPrimary2", "giftCollectPrimary3",
            "giftCollectMiddle1", "giftCollectMiddle2", "giftCollectMiddle3", "giftCollectSenior1", "giftCollectSenior2", "giftCollectSenior3");
    private static final List<GiftCollectVO.SectionConfig> GIFT_TASK_CONFIG_LIST = new ArrayList<>();
    static {
        GIFT_TASK_CONFIG_LIST.add(new GiftCollectVO.SectionConfig(SECTION_CONFIG_LIST.get(0), 383, 0, "https://cloudcdn.waho.live/gift/op_1729735068_01_icon.png", "https://cloudcdn.waho.live/resource/op_sys_1729848502_1.mp4", RESOURCE_CONFIG_LIST.get(0)));
        GIFT_TASK_CONFIG_LIST.add(new GiftCollectVO.SectionConfig(SECTION_CONFIG_LIST.get(0), 384, 0, "https://cloudcdn.waho.live/gift/op_1729735189_02_icon.png", "https://cloudcdn.waho.live/resource/op_sys_1729848502_2.mp4", RESOURCE_CONFIG_LIST.get(1)));
        GIFT_TASK_CONFIG_LIST.add(new GiftCollectVO.SectionConfig(SECTION_CONFIG_LIST.get(0), 385, 0, "https://cloudcdn.waho.live/gift/op_1729735283_03_icon.png", "https://cloudcdn.waho.live/resource/op_sys_1729848502_3.mp4", RESOURCE_CONFIG_LIST.get(2)));
        GIFT_TASK_CONFIG_LIST.add(new GiftCollectVO.SectionConfig(SECTION_CONFIG_LIST.get(1), 386, 0, "https://cloudcdn.waho.live/gift/op_1729735413_04_icon.png", "https://cloudcdn.waho.live/resource/op_sys_1729848518_4.mp4", RESOURCE_CONFIG_LIST.get(3)));
        GIFT_TASK_CONFIG_LIST.add(new GiftCollectVO.SectionConfig(SECTION_CONFIG_LIST.get(1), 387, 0, "https://cloudcdn.waho.live/gift/op_1729736260_05_icon.png", "https://cloudcdn.waho.live/resource/op_sys_1729848526_5.mp4", RESOURCE_CONFIG_LIST.get(4)));
        GIFT_TASK_CONFIG_LIST.add(new GiftCollectVO.SectionConfig(SECTION_CONFIG_LIST.get(1), 388, 0, "https://cloudcdn.waho.live/gift/op_1729736607_06_icon.png", "https://cloudcdn.waho.live/resource/op_sys_1729848534_6.mp4", RESOURCE_CONFIG_LIST.get(5)));
        GIFT_TASK_CONFIG_LIST.add(new GiftCollectVO.SectionConfig(SECTION_CONFIG_LIST.get(2), 389, 0, "https://cloudcdn.waho.live/gift/op_1729736976_07_icon.png", "https://cloudcdn.waho.live/resource/op_sys_1729848552_7.mp4", RESOURCE_CONFIG_LIST.get(6)));
        GIFT_TASK_CONFIG_LIST.add(new GiftCollectVO.SectionConfig(SECTION_CONFIG_LIST.get(2), 390, 0, "https://cloudcdn.waho.live/gift/op_1729737048_08_icon.png", "https://cloudcdn.waho.live/resource/op_sys_1729848558_8.mp4", RESOURCE_CONFIG_LIST.get(7)));
        GIFT_TASK_CONFIG_LIST.add(new GiftCollectVO.SectionConfig(SECTION_CONFIG_LIST.get(2), 391, 0, "https://cloudcdn.waho.live/gift/op_1729737188_09_icon.png", "https://cloudcdn.waho.live/resource/op_sys_1729848565_9.mp4", RESOURCE_CONFIG_LIST.get(8)));


        RESOURCE_KEY_GIFT_MAP.put(RESOURCE_CONFIG_LIST.get(0), Arrays.asList(383));
        RESOURCE_KEY_GIFT_MAP.put(RESOURCE_CONFIG_LIST.get(1), Arrays.asList(383, 384));
        RESOURCE_KEY_GIFT_MAP.put(RESOURCE_CONFIG_LIST.get(2), Arrays.asList(383, 384, 385));
        RESOURCE_KEY_GIFT_MAP.put(RESOURCE_CONFIG_LIST.get(3), Arrays.asList(386));
        RESOURCE_KEY_GIFT_MAP.put(RESOURCE_CONFIG_LIST.get(4), Arrays.asList(386, 387));
        RESOURCE_KEY_GIFT_MAP.put(RESOURCE_CONFIG_LIST.get(5), Arrays.asList(386, 387, 388));
        RESOURCE_KEY_GIFT_MAP.put(RESOURCE_CONFIG_LIST.get(6), Arrays.asList(389));
        RESOURCE_KEY_GIFT_MAP.put(RESOURCE_CONFIG_LIST.get(7), Arrays.asList(389, 390));
        RESOURCE_KEY_GIFT_MAP.put(RESOURCE_CONFIG_LIST.get(8), Arrays.asList(389, 390, 391));

        SECTION_BADGE_MAP.put(SECTION_CONFIG_LIST.get(0), "giftCollectPrimaryBadge");
        SECTION_BADGE_MAP.put(SECTION_CONFIG_LIST.get(1), "giftCollectMiddleBadge");
        SECTION_BADGE_MAP.put(SECTION_CONFIG_LIST.get(2), "giftCollectSeniorBadge");

        SECTION_TYPE_MAP.put(SECTION_CONFIG_LIST.get(0), 1);
        SECTION_TYPE_MAP.put(SECTION_CONFIG_LIST.get(1), 2);
        SECTION_TYPE_MAP.put(SECTION_CONFIG_LIST.get(2), 3);

        SECTION_GIFT_UPGRADE_MAP.put(383, 4);
        SECTION_GIFT_UPGRADE_MAP.put(384, 5);
        SECTION_GIFT_UPGRADE_MAP.put(386, 4);
        SECTION_GIFT_UPGRADE_MAP.put(387, 5);
        SECTION_GIFT_UPGRADE_MAP.put(389, 4);
        SECTION_GIFT_UPGRADE_MAP.put(390, 5);

    }

    @Resource
    protected ResourceKeyHandlerService resourceKeyHandlerService;
    @Resource
    private ResourceKeyConfigDao resourceKeyConfigDao;
    @Resource
    private WhiteTestDao whiteTestDao;
    @Resource
    private EventReport eventReport;

    // 抽奖相关的每日key
    private String getSectionCombineGiftKey(String section, String giftId){
        return String.format("%s:%s", section, giftId);
    }

    //场次-勋章状态
    private String getSectionBadgeKey(String section){
        return String.format("badge:%s", section);
    }

    private String getHashActivityId(String activityId, String uid){
        return String.format("giftCollect:%s:%s", activityId, uid);
    }

    private String getPlayGameRankKey(String activityId){
        return String.format("playGameRank:%s", activityId);
    }

    private String getHistoryRecordListKey(String activityId, String uid){
        return String.format("historyRecord:%s:%s", activityId, uid);
    }


    private Map<String, ResourceKeyConfigData> getResourceDataMap(){
        List<ResourceKeyConfigData> resourceKeyConfigDataList = resourceKeyConfigDao.findListByKeys(new HashSet<>(RESOURCE_CONFIG_LIST));
        return resourceKeyConfigDataList.stream().collect(Collectors.toMap(ResourceKeyConfigData::getKey, Function.identity()));
    }

    private GiftCollectVO.UpgradeConfig getUpgradeConfig(List<GiftCollectVO.SectionConfig> sectionConfigList, int fromGiftId, int toGiftId){
        Map<Integer, GiftCollectVO.SectionConfig> sectionConfigMap = sectionConfigList.stream().collect(Collectors.toMap(GiftCollectVO.SectionConfig::getGiftId, Function.identity()));
        GiftCollectVO.UpgradeConfig upgradeConfig = new GiftCollectVO.UpgradeConfig();
        GiftCollectVO.SectionConfig fromSectionConfig = sectionConfigMap.get(fromGiftId);

        upgradeConfig.setFromGiftId(fromSectionConfig.getGiftId());
        upgradeConfig.setFromGiftIcon(fromSectionConfig.getGiftIcon());
        upgradeConfig.setFromGiftPreview(fromSectionConfig.getGiftPreview());
        upgradeConfig.setFromGiftNum(fromSectionConfig.getGiftNum());
        upgradeConfig.setUpgradeNum(UPGRADE_GIFT_NUMBER);

        GiftCollectVO.SectionConfig toSectionConfig = sectionConfigMap.get(toGiftId);
        upgradeConfig.setToGiftId(toSectionConfig.getGiftId());
        upgradeConfig.setToGiftIcon(toSectionConfig.getGiftIcon());
        upgradeConfig.setToGiftPreview(toSectionConfig.getGiftPreview());
        upgradeConfig.setToGiftNum(toSectionConfig.getGiftNum());
        return upgradeConfig;
    }

    private List<GiftCollectVO.SectionConfig> getSectionConfig(String section){
        List<GiftCollectVO.SectionConfig> sectionConfigList = null;
        if (section.equals(SECTION_CONFIG_LIST.get(0))){
            sectionConfigList = GIFT_TASK_CONFIG_LIST.subList(0, 3);
        } else if (section.equals(SECTION_CONFIG_LIST.get(1))) {
            sectionConfigList = GIFT_TASK_CONFIG_LIST.subList(3, 6);
        }else {
            sectionConfigList = GIFT_TASK_CONFIG_LIST.subList(6, 9);
        }
        return sectionConfigList;
    }

    /**
     * 礼物收藏配置
     */
    public GiftCollectVO giftCollectConfig(String activityId, String uid) {

        OtherRankingActivityData activity = otherActivityService.getOtherRankingActivity(activityId);
        GiftCollectVO vo = new GiftCollectVO();
        vo.setStartTime(activity.getStartTime());
        vo.setEndTime(activity.getEndTime());
        String  hashActivityId = getHashActivityId(activityId, uid);
        Map<String, Integer> userDataMap = activityCommonRedis.getCommonHashAll(hashActivityId);
        Map<String, ResourceKeyConfigData> resourceDataMap = this.getResourceDataMap();
        // 礼物配置
        List<GiftCollectVO.CollectConfig> collectConfigList = new ArrayList<>();
        for (String section : SECTION_CONFIG_LIST) {
            GiftCollectVO.CollectConfig collectConfig = new GiftCollectVO.CollectConfig();
            collectConfig.setSection(section);
            collectConfig.setCombineTimes(userDataMap.getOrDefault(getSectionCombineGiftKey(section, SECTION_COMBINE_KEY), 0));

            List<GiftCollectVO.SectionConfig> sectionConfigVOList = new ArrayList<>();
            List<GiftCollectVO.CombineConfig> combineRewardList = new ArrayList<>();
            List<GiftCollectVO.UpgradeConfig> upgradeConfigList = new ArrayList<>();
            List<GiftCollectVO.SectionConfig> sectionConfigList = this.getSectionConfig(section);

            // 设置合成奖励配置
            for (GiftCollectVO.SectionConfig sectionConfig: sectionConfigList) {
                sectionConfig.setGiftNum(userDataMap.getOrDefault(getSectionCombineGiftKey(section, String.valueOf(sectionConfig.getGiftId())), 0));
                sectionConfigVOList.add(sectionConfig);

                GiftCollectVO.CombineConfig combineConfig = new GiftCollectVO.CombineConfig();
                BeanUtils.copyProperties(resourceDataMap.get(sectionConfig.getResourceKey()), combineConfig);
                List<Integer> giftIdList = RESOURCE_KEY_GIFT_MAP.get(sectionConfig.getResourceKey());
                combineConfig.setGiftIconList(sectionConfigList.stream().filter(item -> giftIdList.contains(item.getGiftId())).
                        map(GiftCollectVO.SectionConfig::getGiftIcon).collect(Collectors.toList()));
                combineRewardList.add(combineConfig);
            }
            collectConfig.setSectionConfigList(sectionConfigVOList);
            collectConfig.setCombineRewardList(combineRewardList);

            // 设置升级礼物配置
            upgradeConfigList.add(this.getUpgradeConfig(sectionConfigVOList, sectionConfigList.get(0).getGiftId(),  sectionConfigList.get(1).getGiftId()));
            upgradeConfigList.add(this.getUpgradeConfig(sectionConfigVOList, sectionConfigList.get(1).getGiftId(),  sectionConfigList.get(2).getGiftId()));
            collectConfig.setUpgradeConfigList(upgradeConfigList);

            collectConfigList.add(collectConfig);
        }
        vo.setCollectConfigList(collectConfigList);

        // 玩游戏榜单
        String playGameRankKey = getPlayGameRankKey(activityId);
        List<OtherRankingListVO> rankingList = new ArrayList<>();
        OtherRankingListVO myRank = new OtherRankingListVO();
        makeOtherRankingData(rankingList, myRank, playGameRankKey, uid, 10, true);
        vo.setPlayGameRankingList(rankingList);
        vo.setMyPlayGameVO(myRank);
        return vo;
    }

    public void giftCollectCombine(String activityId, String uid, String section, String resourceKey) {
        checkActivityTime(activityId);
        List<Integer> giftList = RESOURCE_KEY_GIFT_MAP.get(resourceKey);
        if(CollectionUtils.isEmpty(giftList)){
            throw new CommonH5Exception(ActivityHttpCode.PARAM_ERROR);
        }

        if(!SECTION_CONFIG_LIST.contains(section)){
            throw new CommonH5Exception(ActivityHttpCode.PARAM_ERROR);
        }

        synchronized (stringPool.intern(uid)) {
            String  hashActivityId = getHashActivityId(activityId, uid);
            Map<String, Integer> userDataMap = activityCommonRedis.getCommonHashAll(hashActivityId);
            for (Integer giftId: giftList) {
                int giftNum = userDataMap.getOrDefault(getSectionCombineGiftKey(section, String.valueOf(giftId)), 0);
                if (giftNum <= 0){
                    throw new CommonH5Exception(ActivityHttpCode.INSUFFICIENT_NUMBER);
                }
            }
            for (Integer giftId: giftList) {
                activityCommonRedis.incCommonHashNum(hashActivityId, getSectionCombineGiftKey(section, String.valueOf(giftId)), -1);
            }
            resourceKeyHandlerService.sendResourceData(uid, resourceKey, ACTIVITY_TITLE_EN, ACTIVITY_TITLE_AR, ACTIVITY_DESC, ACTIVITY_URL, ACTIVITY_PRIZE_ICON);
            Map<String, ResourceKeyConfigData> resourceDataMap = this.getResourceDataMap();
            GiftCollectVO.CombineConfig combineConfig = new GiftCollectVO.CombineConfig();
            BeanUtils.copyProperties(resourceDataMap.get(resourceKey), combineConfig);
            combineConfig.setSection(section);
            combineConfig.setCtime(DateHelper.getNowSeconds());
            combineConfig.setGiftIconList(GIFT_TASK_CONFIG_LIST.stream().filter(item -> giftList.contains(item.getGiftId())).
                    map(GiftCollectVO.SectionConfig::getGiftIcon).collect(Collectors.toList()));
            activityCommonRedis.addCommonListData(getHistoryRecordListKey(activityId, uid), JSONObject.toJSONString(combineConfig));
            this.doCombineReportEvent(uid, section, resourceKey);
        }
    }

    private void doCombineReportEvent(String uid, String section, String resourceKey) {
        ActivityExchangeEvent event = new ActivityExchangeEvent();
        event.setUid(uid);
        event.setCtime(DateHelper.getNowSeconds());
        event.setActivity_name(ACTIVITY_TITLE_EN);
        event.setActive_id(ACTIVITY_ID);
        event.setSence_type(SECTION_TYPE_MAP.getOrDefault(section, 0));
        event.setExchange_type(Integer.valueOf(resourceKey.substring(resourceKey.length() - 1)));
        event.setExchange_times(1);
        eventReport.track(new EventDTO(event));
    }

    public void giftCollectUpgrade(String activityId, String uid, String section, int fromGiftId, int toGiftId) {
        checkActivityTime(activityId);
        if(!SECTION_CONFIG_LIST.contains(section)){
            throw new CommonH5Exception(ActivityHttpCode.PARAM_ERROR);
        }

        synchronized (stringPool.intern(uid)) {
            String  hashActivityId = getHashActivityId(activityId, uid);
            Map<String, Integer> userDataMap = activityCommonRedis.getCommonHashAll(hashActivityId);
            int fromGiftNum = userDataMap.getOrDefault(getSectionCombineGiftKey(section, String.valueOf(fromGiftId)), 0);
            if(fromGiftNum < UPGRADE_GIFT_NUMBER){
                throw new CommonH5Exception(ActivityHttpCode.INSUFFICIENT_NUMBER);
            }
            activityCommonRedis.incCommonHashNum(hashActivityId, getSectionCombineGiftKey(section, String.valueOf(fromGiftId)), -UPGRADE_GIFT_NUMBER);
            activityCommonRedis.incCommonHashNum(hashActivityId, getSectionCombineGiftKey(section, String.valueOf(toGiftId)), 1);

            int sectionBadge = userDataMap.getOrDefault(getSectionBadgeKey(section), 0);
            int afterCombine = activityCommonRedis.incCommonHashNum(hashActivityId, getSectionCombineGiftKey(section, SECTION_COMBINE_KEY), 1);
            if(afterCombine >= 2 && sectionBadge <= 0){
                String badgeResourceKey = SECTION_BADGE_MAP.get(section);
                resourceKeyHandlerService.sendResourceData(uid, badgeResourceKey, "Gift Collector-forge reward", ACTIVITY_TITLE_AR, ACTIVITY_DESC, ACTIVITY_URL, ACTIVITY_PRIZE_ICON);
                activityCommonRedis.incCommonHashNum(hashActivityId, getSectionBadgeKey(section), 1);
            }

            int exchangeType = SECTION_GIFT_UPGRADE_MAP.getOrDefault(fromGiftId, 0);
            if(exchangeType == 0){
                logger.error("giftCollectUpgrade error uid:{}, section:{}, fromGiftId:{}", uid, section, fromGiftId);
            }
            this.doUpgradeReportEvent(uid, section, exchangeType);
        }
    }

    private void doUpgradeReportEvent(String uid, String section, int exchangeType) {
        ActivityExchangeEvent event = new ActivityExchangeEvent();
        event.setUid(uid);
        event.setCtime(DateHelper.getNowSeconds());
        event.setActivity_name(ACTIVITY_TITLE_EN);
        event.setActive_id(ACTIVITY_ID);
        event.setSence_type(SECTION_TYPE_MAP.getOrDefault(section, 0));
        event.setExchange_type(exchangeType);
        event.setExchange_times(1);
        eventReport.track(new EventDTO(event));
    }

    public GiftCollectVO  giftCollectRecord(String activityId, String uid, int page) {
        int start = (page - 1) * RECORD_PAGE_SIZE;
        int end = page * RECORD_PAGE_SIZE;
        List<String> combineRecordList = activityCommonRedis.getCommonListPageRecord(getHistoryRecordListKey(activityId, uid), start, end);
        GiftCollectVO vo = new  GiftCollectVO();
        List<GiftCollectVO.CombineConfig> drawRecordList = new ArrayList<>();
        for (String item : combineRecordList) {
            GiftCollectVO.CombineConfig drawRecord = JSON.parseObject(item, GiftCollectVO.CombineConfig.class);
            drawRecordList.add(drawRecord);
        }
        vo.setCombineConfigList(drawRecordList);
        if(drawRecordList.size() < RECORD_PAGE_SIZE){
            vo.setNextUrl(-1);
        }else {
            vo.setNextUrl(page + 1);
        }
        return vo;
    }

    public void distributionTotalRanking(String activityId) {
        try{
            String playGameRankKey = getPlayGameRankKey(activityId);
            Map<String, Integer> rankingMap = activityCommonRedis.getCommonRankingMap(playGameRankKey, 10);
            int rank = 1;
            for (Map.Entry<String, Integer> entry : rankingMap.entrySet()) {
                String aid = entry.getKey();
                String resourceKey = String.format("giftCollectTop%s", rank);
                ResourceKeyConfigData configData = resourceKeyHandlerService.getConfigData(resourceKey);
                if (configData == null) {
                    logger.error("send ranking reward. aid={} rank={} resourceKey={}", aid, rank, resourceKey);
                    continue;
                }
                for (ResourceKeyConfigData.ResourceMeta resourceMeta : configData.getResourceMetaList()) {
                    if (resourceMeta.getResourceType() == -2 && entry.getValue() < 5000000) {
                        continue;
                    }
                    resourceKeyHandlerService.sendOneResourceData(aid, resourceMeta, 905, ACTIVITY_TITLE_EN, ACTIVITY_TITLE_AR, ACTIVITY_DESC, ACTIVITY_DESC, ACTIVITY_URL, ACTIVITY_PRIZE_ICON, 1, activityId);
                }
                rank += 1;
            }
        }catch (Exception e){
            logger.error("distributionTotalRanking error: {}", e.getMessage(), e);
        }
    }

    // 统计玩钻石数
    public void handleMqMsg(CommonMqTopicData data) {
        String fromUid = data.getUid();
        if(!activityIsRunning(ACTIVITY_ID)){
            return;
        }
        synchronized (stringPool.intern(fromUid)) {
            int value = data.getValue();
            activityCommonRedis.incrCommonZSetRankingScore(getPlayGameRankKey(ACTIVITY_ID), fromUid, value);
        }
    }

    // 统计嘉年华收集礼物id
    public void handleCommonMqGiftMsg(CommonMqTopicData data) {
        try {
            String fromUid = data.getUid();
            if(!inActivityTime(ACTIVITY_ID)){
                return;
            }
            CommonMqTopicData.StarBeatGameInfo starBeatGameInfo = JSONObject.parseObject(data.getJsonData(), CommonMqTopicData.StarBeatGameInfo.class);
            int resourceType = starBeatGameInfo.getResourceType();
            if(resourceType != BaseDataResourcesConstant.TYPE_BAG_GIFT){
                return;
            }

            Map<Integer, GiftCollectVO.SectionConfig> sectionConfigMap = GIFT_TASK_CONFIG_LIST.stream().collect(Collectors.toMap(GiftCollectVO.SectionConfig::getGiftId, Function.identity()));
            int giftId = starBeatGameInfo.getResourceId();
            GiftCollectVO.SectionConfig sectionConfig = sectionConfigMap.get(giftId);
            if(sectionConfig == null){
                return;
            }
            synchronized (stringPool.intern(fromUid)) {
                String hashActivityId = getHashActivityId(ACTIVITY_ID, fromUid);
                activityCommonRedis.incCommonHashNum(hashActivityId, getSectionCombineGiftKey(sectionConfig.getSection(), String.valueOf(sectionConfig.getGiftId())), starBeatGameInfo.getResourceNum());
            }
        }catch (Exception e){
            logger.error("giftCollect MqGiftMsg: {}", JSONObject.toJSONString(data));
        }


    }

}
