package com.quhong.service;

import com.alibaba.fastjson.JSON;
import com.quhong.constant.DataResourcesConstant;
import com.quhong.constant.DataResourcesHttpCode;
import com.quhong.core.config.ServerConfig;
import com.quhong.core.utils.DateHelper;
import com.quhong.data.dto.ResourcesDTO;
import com.quhong.enums.ApiResult;
import com.quhong.enums.HttpCode;
import com.quhong.handler.*;
import com.quhong.mongo.dao.ActorDao;
import com.quhong.monitor.MonitorSender;
import com.quhong.mysql.data.ResourcesDetail;
import com.quhong.redis.GoodsListHomeRedis;
import com.quhong.room.RoomWebSender;
import jakarta.annotation.Resource;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;


@Service
public class ResourcesService {

    private final static Logger logger = LoggerFactory.getLogger(ResourcesService.class);

    @Autowired
    private ActorDao actorDao;
    @Resource
    private MonitorSender monitorSender;
    @Resource
    private RiskService riskService;
    @Resource
    private BadgeResourcesHandler badgeResourcesHandler;
    @Resource
    private BuddleResourcesHandler buddleResourcesHandler;
    @Resource
    private MicResourcesHandler micResourcesHandler;
    @Resource
    private RideResourcesHandler rideResourcesHandler;
    @Resource
    private RippleResourcesHandler rippleResourcesHandler;
    @Resource
    private FloatScreenResourcesHandler floatScreenResourcesHandler;
    @Resource
    private GiftBagResourcesHandler giftBagResourcesHandler;
    @Resource
    private MineBackgroundResourcesHandler mineBackgroundResourcesHandler;
    @Resource
    private BeautifulRidResourcesHandler beautifulRidResourcesHandler;
    @Resource
    private EntryEffectResourcesHandler entryEffectResourcesHandler;
    @Resource
    private HonorTitleResourcesHandler honorTitleResourcesHandler;
    @Resource
    private VipResourcesHandler vipResourcesHandler;
    @Resource
    private BackgroundUploadCardHandler backgroundUploadCardHandler;
    @Resource
    private RoomLockResourcesHandler roomLockResourcesHandler;
    @Resource
    private GoodsListHomeRedis goodsListHomeRedis;
    @Autowired
    private RoomWebSender roomWebSender;
    @Resource
    private TicketResourcesHandler ticketResourcesHandler;
    @Resource
    private RoomHotCardResourcesHandler roomHotCardResourcesHandler;
//    @Resource
//    private DataResourcesApi dataResourcesApi;
//    @Resource
//    private DataResourcesService k8sApi;
//    @Resource
//    private MqSenderService mqSenderService;


    private void myTest() {
        if (ServerConfig.isNotProduct()) {
            String uid = "5be6a3f402bd89003b083f0e";
            ResourcesDTO req = new ResourcesDTO();
            req.setUid(uid);
            req.setResType(DataResourcesConstant.TYPE_MIC);
            req.setResId("416");
            req.setDays(2);
            req.setActionType(DataResourcesConstant.ACTION_GET_WEAR);
            req.setmTime(DateHelper.getNowSeconds());
//            req.setDesc("from old api");
//            dataResourcesApi.handleRes(req);
//            req.setDesc("from k8s api");
//            ApiResult<String> ret = k8sApi.handleRes(req);
//            logger.info("ret code={}", ret.getCode().getCode());
//            req.setDesc("from mq");
//            mqSenderService.asyncHandleResources(req);
        }
    }

    public ApiResult<String> dealRes(ResourcesDTO resourcesDto) {
        return dealRes(resourcesDto, false);
    }

    /**
     * 资源处理
     *
     * @return 失败返回异常信息
     */
    public ApiResult<String> dealRes(ResourcesDTO resourcesDto, boolean isCheckUser) {
        try {
            if (isCheckUser && actorDao.getActorDataFromCache(resourcesDto.getUid()) == null) {
                logger.error("dealRes error uid is invalid resourcesDto={}", resourcesDto);
                return ApiResult.getError(DataResourcesHttpCode.USER_NOT_EXIST);
            }
            return resourcesBusiness(resourcesDto);
        } catch (Exception e) {
            logger.error("dealRes error resourcesDto={} msg={}", resourcesDto, e.getMessage(), e);
            riskService.warn("资源处理异常：" + e.getMessage(), true);
            return ApiResult.getError(HttpCode.SERVER_ERROR);
        }
    }

    private BaseResourcesHandler getHandler(int resType) {
        BaseResourcesHandler handler;
        switch (resType) {
            case DataResourcesConstant.TYPE_BADGE:
                handler = badgeResourcesHandler;
                break;
            case DataResourcesConstant.TYPE_MIC:
                handler = micResourcesHandler;
                break;
            case DataResourcesConstant.TYPE_RIDE:
                handler = rideResourcesHandler;
                break;
            case DataResourcesConstant.TYPE_BUDDLE:
                handler = buddleResourcesHandler;
                break;
            case DataResourcesConstant.TYPE_RIPPLE:
                handler = rippleResourcesHandler;
                break;
            case DataResourcesConstant.TYPE_FLOAT_SCREEN:
                handler = floatScreenResourcesHandler;
                break;
            case DataResourcesConstant.TYPE_BAG_GIFT:
                handler = giftBagResourcesHandler;
                break;
            case DataResourcesConstant.TYPE_MINE_BACKGROUND:
                handler = mineBackgroundResourcesHandler;
                break;
            case DataResourcesConstant.TYPE_BEAUTIFUL_RID:
                handler = beautifulRidResourcesHandler;
                break;
            case DataResourcesConstant.TYPE_ENTRY_EFFECT:
                handler = entryEffectResourcesHandler;
                break;
            case DataResourcesConstant.TYPE_HONOR_TITLE:
                handler = honorTitleResourcesHandler;
                break;
            case DataResourcesConstant.TYPE_VIP_LEVEL:
                handler = vipResourcesHandler;
                break;
            case DataResourcesConstant.TYPE_BACKGROUND_UPLOAD_CARD:
                handler = backgroundUploadCardHandler;
                break;
            case DataResourcesConstant.TYPE_ROOM_LOCK:
                handler = roomLockResourcesHandler;
                break;
            case DataResourcesConstant.TYPE_TICKET:
                handler = ticketResourcesHandler;
                break;
            case DataResourcesConstant.TYPE_ROOM_HOT_CARD:
                handler = roomHotCardResourcesHandler;
                break;
            default:
                return null;
        }
        return handler;
    }

    /**
     * 业务逻辑处理
     */
    public ApiResult<String> resourcesBusiness(ResourcesDTO resourcesDto) {
        int resType = resourcesDto.getResType();
        int actionType = resourcesDto.getActionType();
        ResourcesDetail resourcesDetail = new ResourcesDetail();
        BeanUtils.copyProperties(resourcesDto, resourcesDetail);
        ApiResult<String> result;
        BaseResourcesHandler handler = getHandler(resType);
        if (null == handler) {
            logger.error("unsupported uid={} resId={} resType={}", resourcesDto.getUid(), resourcesDto.getResId(), resType);
            return ApiResult.getError(DataResourcesHttpCode.PARAMS_ERROR);
        }
        fillTime(resourcesDetail);
        switch (actionType) {
            case DataResourcesConstant.ACTION_GET:
                result = handler.synAdd(resourcesDetail);
//                handler.sendNewResAddNotice(resourcesDto.getUid(),resType);
//                handler.sendOfficialMsg(resourcesDto);
                break;
            case DataResourcesConstant.ACTION_GET_WEAR:
                result = handler.synAddAndWear(resourcesDetail);
//                handler.sendNewResAddNotice(resourcesDto.getUid(),resType);
//                handler.sendOfficialMsg(resourcesDto);
                break;
            case DataResourcesConstant.ACTION_WEAR:
                result = handler.synWear(resourcesDetail);
                break;
            case DataResourcesConstant.ACTION_CANCLE_WEAR:
                result = handler.synUnWear(resourcesDetail);
                break;
            case DataResourcesConstant.ACTION_DELETE:
                result = handler.synRemove(resourcesDetail);
                break;
            case DataResourcesConstant.ACTION_ALL_EXPIRE:
                result = handler.expire();
                break;
            case DataResourcesConstant.ACTION_ALL_EXPIRE_NOTE:
                result = handler.expireNotified();
                break;
            case DataResourcesConstant.ACTION_TEST:
                myTest();
                result = ApiResult.getOk();
                break;
            default:
                logger.error("unsupported uid={} resId={} actionType={}", resourcesDto.getUid(), resourcesDto.getResId(), actionType);
                return ApiResult.getError(DataResourcesHttpCode.PARAMS_ERROR);
        }
        if (result == null) {
            logger.error("handle fail result is null resourcesDetail={} ", resourcesDetail);
            riskService.warn("调用资源处理接口，返回为 null dto=" + JSON.toJSONString(resourcesDto), false);
            return ApiResult.getError(HttpCode.SERVER_ERROR);
        }

        boolean isOk = result.isOk();
        if (isOk) {
            handler.addDetailToDb(resourcesDetail);
            logger.info("handle success resourcesDetail={}", resourcesDetail);
        } else {
            String eMsg = result.getCode().getMsg();
            logger.error("handle fail resourcesDetail={} msg={}", resourcesDetail, eMsg);
            if (result.getCode().getCode() != DataResourcesHttpCode.NOT_OWN_RESOURCES.getCode()) {
                riskService.warn("调用资源处理接口，返回失败：" + JSON.toJSONString(resourcesDto) + " return:" + eMsg,
                        false);
            }
        }
        return result;
    }

    public void checkUseByEnterRoom(String uid) {
//        BaseTaskFactory.getFactory().addSlow(new Task() {
//            @Override
//            protected void execute() {
//                micResourcesHandler.checkUseExpire(uid);
//                rideResourcesHandler.checkUseExpire(uid);
//            }
//        });

    }

    private void fillTime(ResourcesDetail resourcesDetail) {
        int now = DateHelper.getNowSeconds();
        Integer seconds = resourcesDetail.getSeconds();
        Integer actType = resourcesDetail.getActionType();
        Integer gainType = resourcesDetail.getGainType();
        resourcesDetail.setHandleTime(now);
        if (actType == DataResourcesConstant.ACTION_GET || actType == DataResourcesConstant.ACTION_GET_WEAR) {
            if (gainType == DataResourcesConstant.GAIN_TYPE_DAY) {
                Integer days = resourcesDetail.getDays();
                int endTime = days == DataResourcesConstant.FOREVER_DAY ?
                        Integer.MAX_VALUE : now + days * 86400;
                if (ServerConfig.isNotProduct() && null != seconds) {
                    endTime = now + seconds;
                }
                resourcesDetail.setEndTime(endTime);
            }
        }
    }


}
