package com.quhong.service;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.quhong.analysis.*;
import com.quhong.api.MsgService;
import com.quhong.config.CaffeineCacheConfig;
import com.quhong.config.RoomConfig;
import com.quhong.constant.DetectOriginConstant;
import com.quhong.controllers.RoomMsgController;
import com.quhong.core.concurrency.BaseTaskFactory;
import com.quhong.core.concurrency.tasks.Task;
import com.quhong.core.config.ServerConfig;
import com.quhong.core.redis.BasePlayerRedis;
import com.quhong.core.utils.DateHelper;
import com.quhong.dailyTask.CommonTaskService;
import com.quhong.dailyTask.DailyTaskService;
import com.quhong.data.*;
import com.quhong.data.dto.*;
import com.quhong.data.vo.*;
import com.quhong.dto.ImageDTO;
import com.quhong.dto.TextDTO;
import com.quhong.enums.*;
import com.quhong.exception.CommonException;
import com.quhong.feign.DataCenterService;
import com.quhong.feign.IDetectService;
import com.quhong.feign.IFriendService;
import com.quhong.handler.HttpEnvData;
import com.quhong.image.ImageUrlGenerator;
import com.quhong.mongo.dao.*;
import com.quhong.mongo.data.*;
import com.quhong.monitor.MonitorSender;
import com.quhong.msg.obj.HighlightTextObject;
import com.quhong.msg.obj.UNameObject;
import com.quhong.msg.obj.UserInfoObject;
import com.quhong.msg.room.*;
import com.quhong.mysql.dao.*;
import com.quhong.mysql.data.*;
import com.quhong.redis.*;
import com.quhong.room.*;
import com.quhong.room.api.zego.ZegoApi;
import com.quhong.room.cache.RoomActorCache;
import com.quhong.room.data.EnterCartonData;
import com.quhong.room.data.RoomActorDetailData;
import com.quhong.room.mic.RoomMicService;
import com.quhong.room.redis.*;
import com.quhong.sdk.zego.ZegoService;
import com.quhong.utils.*;
import com.quhong.vo.RoomMicListVo;
import jakarta.annotation.Resource;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import org.springframework.util.DigestUtils;
import org.springframework.util.ObjectUtils;
import org.springframework.web.util.UriComponentsBuilder;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.text.DecimalFormat;
import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;
import java.util.stream.IntStream;


@Service
public class RoomNewService {
    private static final Logger logger = LoggerFactory.getLogger(RoomNewService.class);

    // 用户列表大小
    public static final int ACTOR_PAGE_SIZE = 30;
    private static final int MAX_UPLOAD_NUM = 10;
    private static final List<String> EMOJI_TAB_LIST = List.of(
            "https://cloudcdn.waho.live/emoji/op_1736847389_0.png",
            "https://cloudcdn.waho.live/emoji/op_1736847471_1.png",
            "https://cloudcdn.waho.live/emoji/op_1736847483_2.png",
            "https://cloudcdn.waho.live/emoji/op_1736847500_3.png"
    );

    // 幸运数字V2配置
    private static final List<Integer> LUCKY_NUM_COST_LIST_NEW = Arrays.asList(0, 66, 110);
    private static final List<Integer> LUCKY_NUM_RANGE_LIST = Arrays.asList(9, 99, 999);
    private static final List<Integer> LUCKY_NUM_SWITCH = Arrays.asList(0, 1);

    // 关注房间消息文案
    private static final String FOLLOW_ROOM_MSG = "%s followed the room, invite to chat together.";
    private static final String FOLLOW_ROOM_MSG_AR = "\u202b%s تابعو الغرفة، ادعوا للدردشة معًا.";

    // fcm文案
    public static final String FCM_FRIEND_EN_BODY = "Your friend %s, has entered the room.";
    public static final String FCM_FRIEND_AR_BODY = " صديقك %s دخل الغرفة";
    public static final String FCM_FOLLOW_EN_BODY = "The room you follow is now live. Open the room >>";
    public static final String FCM_FOLLOW_AR_BODY = "الغرفة التي تتابعها الآن في بث مباشر، افتح الغرفة >>";

    @Resource
    private MongoRoomDao roomDao;
    @Resource
    private ActorDao actorDao;
    @Resource
    private RoomWebSender roomWebSender;
    @Resource
    private UserLevelDao userLevelDao;
    @Resource
    private CommonConfig commonConfig;
    @Resource(name = DataRedisBean.MAIN_CLUSTER)
    private StringRedisTemplate clusterRedis;
    @Resource
    private LowModelsRedis lowModelsRedis;
    @Resource
    private VipInfoDao vipInfoDao;
    @Resource
    private SysConfigDao sysConfigDao;
    @Resource
    private RecentlyRoomRedis recentlyRoomRedis;
    @Resource
    private RoomMemberDao roomMemberDao;
    @Resource
    private FollowDao followDao;
    @Resource
    private CommonDao commonDao;
    @Resource
    private MongoThemeDao mongoThemeDao;
    @Resource
    private ZegoService zegoService;
    @Resource
    private MicFrameRedis micFrameRedis;
    @Resource
    private RoomMicRedis roomMicRedis;
    @Resource
    private RoomPlayerRedis roomPlayerRedis;
    @Resource
    private BlockRedis blockRedis;
    @Resource
    private RoomKickRedis roomKickRedis;
    @Resource
    private RoomBlacklistDao roomBlacklistDao;
    @Resource
    private BasePlayerRedis basePlayerRedis;
    @Resource
    private MonitorSender monitorSender;
    @Resource
    private RoomConfig roomConfig;
    @Resource
    private DailyTaskService dailyTaskService;
    @Resource
    private GiftBagDao giftBagDao;
    @Resource
    private RoomMicService roomMicService;
    @Resource
    private RoomActorListService roomActorListService;
    @Resource
    private RoomActorCache roomActorCache;
    @Resource
    private RoomRedis roomRedis;
    @Resource
    private EnterRoomContent enterRoomContent;
    @Resource
    private RoomMemberDao memberDao;
    @Resource
    private RoomTags roomTags;
    @Resource
    private ActorConfigDao actorConfigDao;
    @Resource
    private ZegoApi zegoApi;
    @Resource
    private RoomBannerDao roomBannerDao;
    @Resource
    private RoomAdminRedis roomAdminRedis;
    @Resource
    private TurntableGameRedis turntableGameRedis;
    @Resource
    private WhitelistRedis whitelistRedis;
    @Resource
    private EmojiResourceDao emojiResourceDao;
    @Resource
    private IDetectService detectService;
    @Resource
    private UploadBackgroundDao uploadBackgroundDao;
    @Resource
    private MongoThemeDao themeDao;
    @Resource
    private MineBackgroundDao mineBackgroundDao;
    @Resource
    private FollowRoomDao followRoomDao;
    @Resource
    private RoomConfigDao roomConfigDao;
    @Resource
    private LuckyNumDao luckyNumDao;
    @Resource
    private LuckyNumRedis luckyNumRedis;
    @Resource
    private DataCenterService dataCenterService;
    @Autowired(required = false)
    private EventReport eventReport;
    @Resource
    private RoomRocketService roomRocketService;
    @Resource
    private MicApplyRedis micApplyRedis;
    @Resource
    private RoomSettingsService roomSettingsService;
    @Resource
    private RoomNewService roomNewService;
    @Resource
    private RiskControlService riskControlService;
    @Resource
    private LoginLimitService loginLimitService;
    @Resource
    private GameEntryConfigDao gameEntryConfigDao;
    @Resource
    private FamilyMemberDao familyMemberDao;
    @Resource
    private BannerListService bannerListService;
    @Resource
    private MsgService msgService;
    @Resource
    private IFriendService friendService;
    @Resource
    private BackgroundUploadCardDao backgroundUploadCardDao;
    @Resource
    private LiveDataRedis liveDataRedis;
    @Resource
    private LiveUserDataRedis liveUserDataRedis;
    @Resource
    private NewbieService newbieService;
    @Resource
    private AdvancedGameRedis advancedGameRedis;
    @Resource
    private MysteryRedis mysteryRedis;
    @Resource
    private SvipLevelService svipLevelService;
    @Resource
    private MysteryService mysteryService;
    @Resource
    private CommonTaskService commonTaskService;
    @Resource
    private UserMonitorDao userMonitorDao;
    @Resource
    private FreezeInfoRedis freezeInfoRedis;
    @Resource
    private RoomLastMsgRedis roomLastMsgRedis;
    @Resource
    private VoiceRoomRankingRedis voiceRoomRankingRedis;
    @Resource
    private DirtyPictureRedis dirtyPictureRedis;
    @Resource
    private NewRookieRoomRedis newRookieRoomRedis;
    @Resource
    private RoomEventService roomEventService;
    @Resource
    private RoomEventDao roomEventDao;
    @Resource
    private RoomEventRedis roomEventRedis;

    @Cacheable(value = "cache", cacheManager = CaffeineCacheConfig.EXPIRE_15S_AFTER_WRITE,
            key = "#root.method.name+#p0.uid")
    public PreCreateRoomVO preCreate(HttpEnvData req) {
        PreCreateRoomVO vo = new PreCreateRoomVO();
        MongoRoomData roomData = roomDao.findData(RoomUtils.formatRoomId(req.getUid()));
        if (null != roomData) {
            vo.setRoomId(roomData.getRid());
            vo.setRoomName(roomData.getName());
            vo.setOnline(roomPlayerRedis.getRoomActorsCount(roomData.getRid()));
            vo.setRoomHead(ImageUrlGenerator.generateRoomUserUrl(roomData.getHead()));
            vo.setAnnounce(roomData.getAnnounce());
        }
        return vo;
    }

    public void createRoomCheck(String uid) {
        if (vipInfoDao.getIntVipLevel(uid) < 2) {
            throw new CommonException(RoomHttpCode.LIVE_ROOM_LIMIT_VIP, "VIP2");
        }
    }

    public void checkImage(String uid, String roomHead) {
        String roomHeadUrl = roomHead.startsWith("http") ? CDNUtils.getHttpCdnUrl(roomHead) :
                ImageUrlGenerator.createCdnUrl(roomHead);
        if (detectService.detectImage(new ImageDTO(roomHeadUrl, DetectOriginConstant.ROOM_HEAD, uid)).getData().getIsSafe() == 0) {
            throw new CommonException(RoomHttpCode.COVER_UNAVAILABLE);
        }
    }

    /**
     * 房主创建房间
     */
    public RoomVO create(RoomDTO req) {
        String roomId = RoomUtils.formatRoomId(req.getUid());
        req.setRoomId(roomId);
        ActorData actorData = mysteryService.getMysteryActor(req.getUid());
        if (null == actorData) {
            logger.error("cannot find actor, uid={}", req.getUid());
            throw new CommonException(HttpCode.PARAM_ERROR);
        }
        // 封禁检测
        if (userMonitorDao.isChatRoomBanned(req.getRoomId())) {
            // if (!AppVersionUtils.versionCheck(135, req)) {
            //     throw new CommonException(RoomHttpCode.ROOM_BANNED);
            // }
            CommonException e = new CommonException(RoomHttpCode.ROOM_BANNED);
            RoomBannedVO vo = getRoomBannedVO(req.getUid(), UserMonitorState.BAN_CHAT_ROOM, actorData.getRid(), req.getSlang(), actorData.getRidData());
            e.setData(vo);
            throw e;
        }
        if (req.getRoomMode() == RoomConstant.LIVE_ROOM_MODE) {
            createRoomCheck(req.getUid());
        }
        MongoRoomData mongoRoomData = roomDao.findData(roomId);
        // 脏词检测
        if (!ObjectUtils.isEmpty(req.getRoomName())) {
            if (mongoRoomData != null && !mongoRoomData.getName().equals(req.getRoomName()) && freezeInfoRedis.isFrozenUpdateInfo(FreezeInfoRedis.ROOM_NAME, req.getRoomId())) {
                throw new CommonException(RoomHttpCode.FEATURE_HAS_FROZEN);
            }
            checkDirtyWord(req.getUid(), req.getRoomName().toLowerCase());
        }
        if (!ObjectUtils.isEmpty(req.getRoomHead())) {
            String roomHeadUrl = req.getRoomHead().startsWith("http") ? CDNUtils.getHttpCdnUrl(req.getRoomHead()) :
                    ImageUrlGenerator.createCdnUrl(req.getRoomHead());
            req.setRoomHead(roomHeadUrl);
            if (mongoRoomData != null && !mongoRoomData.getHead().equals(req.getRoomHead())) {
                if (freezeInfoRedis.isFrozenUpdateInfo(FreezeInfoRedis.ROOM_HEAD, req.getRoomId())) {
                    throw new CommonException(RoomHttpCode.HAS_SUSPENDED_THIS_FEATURE);
                }
                if (dirtyPictureRedis.isDirtyPicture(roomHeadUrl)) {
                    throw new CommonException(HttpCode.IS_DIRTY_PICTURE);
                }
            }
            checkImage(req.getUid(), roomHeadUrl);
        }
        if (null == mongoRoomData) {
            createRoom(actorData, roomId, req);
        } else {
            updateRoom(mongoRoomData, req, actorData);
        }
        // 调用加入房间接口
        return joinRoom(req);
    }

    private void checkDirtyWord(String uid, String roomName) {
        if (detectService.detectText(new TextDTO(roomName, DetectOriginConstant.ROOM_NAME_RELATED, uid)).getData().getIsSafe() == 0) {
            throw new CommonException(RoomHttpCode.DIRTY_WORD_TITLE);
        }
    }

    /**
     * 房主第一次创建房间
     */
    public void createRoom(ActorData actorData, String roomId, RoomDTO req) {
        logger.info("first time create room uid={}", req.getUid());
        int nowSeconds = DateHelper.getNowSeconds();
        MongoRoomData mongoRoomData = new MongoRoomData();
        mongoRoomData.setName(req.getRoomName());
        if (ObjectUtils.isEmpty(req.getRoomName())) {
            mongoRoomData.setName(actorData.getName());
        }
        mongoRoomData.setRid(roomId);
        mongoRoomData.setOwnerRid(actorData.getRid());
        mongoRoomData.setPrivi(1);
        mongoRoomData.setFee(0);
        mongoRoomData.setMicSize(8);
        mongoRoomData.setTextLimit(-1);
        mongoRoomData.setHead(ObjectUtils.isEmpty(req.getRoomHead()) ? actorData.getHead() : req.getRoomHead());
        mongoRoomData.setStream_id(genStreamId(roomId, null));
        mongoRoomData.setCountry(ObjectUtils.isEmpty(actorData.getCountry()) ? "AE_United Arab Emirates" : actorData.getCountry());
        mongoRoomData.setMtime(nowSeconds);
        mongoRoomData.setCtime(nowSeconds);
        mongoRoomData.setTag(null == req.getTagId() || !roomTags.hasTag(req.getTagId()) ? 1 : req.getTagId());
        mongoRoomData.setRoomMode(req.getRoomMode() == 0 ? 1 : req.getRoomMode());
        roomDao.save(mongoRoomData);
    }

    /**
     * 更新房间
     */
    public void updateRoom(MongoRoomData mongoRoomData, RoomDTO req, ActorData actorData) {
        String roomId = mongoRoomData.getRid();
        req.setRoomChange(0 != mongoRoomData.getRoomMode() && req.getRoomMode() != mongoRoomData.getRoomMode());
        if (req.getRoomMode() == RoomConstant.LIVE_ROOM_MODE) {
            initLiveRoom(mongoRoomData, req.getUid());
        } else {
            if (req.getRoomMode() == RoomConstant.VOICE_ROOM_MODE) {
                checkUpMicPrivilege(mongoRoomData, req.getUid());
            }
        }
        if (null != req.getTagId()) {
            mongoRoomData.setTag(roomTags.hasTag(req.getTagId()) ? req.getTagId() : 1);
        }
        // 更新房间信息
        if (!ObjectUtils.isEmpty(req.getRoomName())) {
            if (!req.getRoomName().equals(mongoRoomData.getName())) {
                req.setChangeRoomName(true);
            }
            mongoRoomData.setName(req.getRoomName());
        }
        mongoRoomData.setOwnerRid(actorData.getRid());
        mongoRoomData.setRoomMode(req.getRoomMode() == 0 ? 1 : req.getRoomMode());
        mongoRoomData.setStream_id(genStreamId(roomId, mongoRoomData.getPwd()));
        if (!ObjectUtils.isEmpty(req.getRoomHead())) {
            mongoRoomData.setHead(req.getRoomHead());
        }
        roomDao.save(mongoRoomData);
    }

    /**
     * 直播房重置音乐房、话题房、视频房状态
     */
    public void initLiveRoom(MongoRoomData roomData, String uid) {
        roomData.setMic_theme(RoomConstant.MIC_THEME_DEFAULT_ID);
        if (roomData.getOrigPrivi() != 3) {
            // live切换为申请上麦，并发送消息
            roomSettingsService.sendRoomOptMsg(roomData.getRid(), uid, 4);
        }
    }

    public void checkUpMicPrivilege(MongoRoomData roomData, String uid) {
        int upMicPrivilege = roomData.getOrigPrivi();
        // 如果之前不是申请上麦，则发送一次上麦方式变更消息
        if (upMicPrivilege != 3) {
            roomSettingsService.sendRoomOptMsg(roomData.getRid(), uid, upMicPrivilege == 1 ? 2 : upMicPrivilege == 2 ? 3 : 2);
        }
    }

    /**
     * 加入房间，返回房间信息、麦位信息
     * <p>
     * 房主进入房间 -> 开启直播房 OR 开启普通房
     * 其他用户进入房间 -> 进入直播房 OR 进入普通房
     */
    public RoomVO joinRoom(RoomDTO req) {
        if (needToUpgrade(req)) {
            logger.info("need to upgrade app uid={} versionCode={}", req.getUid(), req.getVersioncode());
            if (req.getOs() == ClientOS.ANDROID) {
                throw new CommonException(RoomHttpCode.NEED_UPGRADE);
            } else {
                throw new CommonException(new HttpCode(442, RoomHttpCode.NEED_UPGRADE.getMsg(req.getSlang())));
            }
        }
        RoomActorDetailData myData = roomActorCache.getData(req.getRoomId(), req.getUid(), false);
        String ownerUid;
        try {
            ownerUid = RoomUtils.getRoomHostId(req.getRoomId());
        } catch (Exception e) {
            logger.error("roomId invalid, roomId={} uid={}", req.getRoomId(), req.getUid());
            throw new CommonException(HttpCode.PARAM_ERROR);
        }
        RoomActorDetailData ownerData = roomActorCache.getData(req.getRoomId(), ownerUid, true, false);
        if (null == myData || null == ownerData) {
            logger.error("cannot find actor, uid={} ownerUid={}", req.getUid(), ownerUid);
            throw new CommonException(HttpCode.PARAM_ERROR);
        }
        MongoRoomData roomData = roomDao.findData(req.getRoomId());
        if (null == roomData) {
            logger.error("cannot find room roomId={} uid={}", req.getRoomId(), req.getUid());
            throw new CommonException(RoomHttpCode.ROOM_NOT_EXIST);
        }
        // 进入房间权限校验
        checkJoinRoomPrivilege(myData, ownerData, req);
        return getJoinRoomResp(req, roomData, myData, ownerData);
    }

    public RoomBannedVO getRoomBannedVO(String uid, int code, int rid, int slang, RidData ridData) {
        RoomBannedVO vo = new RoomBannedVO();
        String reason = slang == SLangType.ENGLISH ? "Your account has been blocked" : "حسابك محظور";
        int blockType = 3;
        UserMonitorData userMonitorData = userMonitorDao.findData(uid, Collections.singletonList(code));
        if (null != userMonitorData) {
            try {
                blockType = Integer.parseInt(userMonitorData.getBlock_term());
            } catch (NumberFormatException ignored) {
            }
            if (userMonitorData.getReason_type() == 1) {
                reason = slang == SLangType.ENGLISH ? "Publish pornographic material and indecent contents."
                        : "نشر مواد إباحية ومحتويات غير لائقة.";
            } else if (userMonitorData.getReason_type() == 2) {
                reason = slang == SLangType.ENGLISH ? "Obtaining or reselling diamonds illegally"
                        : "سرقة الألماس وإعادة بيعها بشكل غير قانوني";
            } else if (userMonitorData.getReason_type() == 3) {
                reason = slang == SLangType.ENGLISH ? "Insult the reputation of the platform and damage the official image"
                        : "اهانة سمعة المنصة وتدمير صورة رسمية البرنامج";
            } else if (userMonitorData.getReason_type() == 4) {
                reason = slang == SLangType.ENGLISH ? "Abuse others and damage the atmosphere of community"
                        : "الشتم للآخرين والإضرار ببيئة البرنامج";
            } else {
                reason = ObjectUtils.isEmpty(userMonitorData.getReason()) ? "" : userMonitorData.getReason();
            }
        }
        vo.setRid(rid);
        vo.setRidData(ridData);
        vo.setBlock_type(blockType);
        vo.setReason(reason);
        if (3 == blockType) {
            vo.setRestored("2099-12-31 00:00:00");
        } else {
            vo.setRestored(DateHelper.ARABIAN.formatDateTime(new Date(userMonitorData.getRelease_at() * 1000L)));
        }
        return vo;
    }

    private boolean needToUpgrade(RoomDTO req) {
        loginLimitService.limitCheck(req);
        return RoomUtils.isLiveRoom(req.getRoomId());
    }

    /**
     * 填充进房间返回数据
     */
    private RoomVO getJoinRoomResp(RoomDTO req, MongoRoomData roomData, RoomActorDetailData myData, RoomActorDetailData ownerData) {
        RoomVO resp = new RoomVO();
        // 基础信息填充
        fillRoomResp(ownerData, myData, roomData, resp, req);
        // if (resp.getRoomConfig().getMicSize() != 8 && !AppVersionUtils.versionCheck(106, req)) {
        //     throw new CommonException(HttpCode.UPDATE_APP);
        // }
        resp.getRoomVisitor().setRid(myData.getRid());
        resp.getRoomVisitor().setVip(myData.getVipLevel());
        resp.getRoomVisitor().setMyName(myData.getName());
        if (myData.getAid().equals(ownerData.getAid())) {
            if (roomData.getOwnerRid() != ownerData.getRid()) {
                // 同步房主最新的rid
                roomDao.updateField(req.getRoomId(), "ownerRid", ownerData.getRid());
            }
            ownerJoinRoom(roomData, ownerData, myData.getVipLevel(), req, resp);
        } else {
            otherJoinRoom(ownerData, req, resp);
        }
        if (!ObjectUtils.isEmpty(myData.getMysteryId())) {
            resp.getRoomVisitor().setRole(RoomRoleType.AUDIENCE);
            resp.getRoomVisitor().setAdmin(2);
        }
        resp.getRoomVisitor().setApplyCount(micApplyRedis.getMicApplyCount(req.getRoomId()));
        if (resp.getRoomVisitor().getAdmin() == 1) {
            resp.getRoomVisitor().setFirstHead(micApplyRedis.getFirstMicApplyHead(req.getRoomId()));
        }
        // 设置房间主题
        resp.getRoomConfig().setRoomTheme(roomNewService.getRoomTheme(roomData.getTheme(), ownerData.getSvipLevel()));
        // 新房间背景上架时间
        resp.getRoomConfig().setNewBgShelfTime(themeDao.getNewBgShelfTime());
        // 房间火箭信息
        resp.getRoomConfig().setRocketInfo(roomRocketService.getRocketAndBoxInfo(req.getRoomId(), req.getUid(), req.getOs(), req.getVersioncode()));
        // 房间活动信息
        resp.getRoomConfig().setRoomEvent(roomEventService.getRoomEventVO(req.getRoomId(), req.getUid(), req.getSlang()));
        // 普通房间设置相关属性
        if (RoomConstant.LIVE_ROOM_MODE != resp.getRoomConfig().getRoomMode()) {
            resp.getRoomVisitor().setTalkForbidTime(micFrameRedis.getForbidTime(req.getRoomId(), req.getUid()));
            resp.getRoomVisitor().setMicForbidTime(roomMicRedis.getMicForbidTime(req.getRoomId(), req.getUid()));
        }
        if (pwdCheck(roomData, ownerData, myData, resp)) {
            // 进入房间后置处理
            afterJoinRoom(req, roomData, myData, resp);
            // 最后才填充麦位信息
            fillMicData(req, resp);
        }
        // 填充进入房间的用户自己设置的配置
        fillActorRoomConfig(myData.getAid(), resp, req);
        return resp;
    }

    /**
     * 填充进入房间的用户自己设置的配置
     */
    private void fillActorRoomConfig(String uid, RoomVO resp, RoomDTO req) {
        ActorConfigData configData = actorConfigDao.findData(uid);
        if (null == configData) {
            configData = actorConfigDao.initActorConfigData(req.getUid());
        }
        Map<String, Object> config = configData.getRoom_config();
        if (config == null) {
            config = new HashMap<>(ActorConfigDao.DEF_ROOM_CONFIG);
            configData.setRoom_config(config);
        }
        boolean modify = false;
        if (!config.containsKey(ActorConfigDao.HIDE_ALL)) {
            config.putIfAbsent(ActorConfigDao.HIDE_ALL, 0);
            config.putIfAbsent(ActorConfigDao.HIDE_GIFT_RIDE_VOICE, 0);
            config.putIfAbsent(ActorConfigDao.HIDE_MIC, 0);
            config.putIfAbsent(ActorConfigDao.HIDE_RIPPLE, 0);
            config.putIfAbsent(ActorConfigDao.HIDE_GIF_AUTO, 0);
            modify = true;
        }
        JSONObject objectConfig = new JSONObject();
        objectConfig.putAll(config);

        if (lowModelsRedis.isLowModels(req.getUid())) {
            boolean modifyLow = false;
            for (String key : config.keySet()) {
                if (ActorConfigDao.HIDE_GIFT.equals(key)) {
                    if (objectConfig.getIntValue(key) != 1) {
                        config.put(key, 1);
                        objectConfig.put(key, 1);
                        modifyLow = true;
                    }
                } else if (ActorConfigDao.HIDE_RIDE.equals(key)) {
                    if (objectConfig.getIntValue(key) != 1) {
                        config.put(key, 1);
                        objectConfig.put(key, 1);
                        modifyLow = true;
                    }
                } else if (ActorConfigDao.HIDE_ALL.equals(key)) {
                    if (objectConfig.getIntValue(key) != 1) {
                        config.put(key, 1);
                        objectConfig.put(key, 1);
                        modifyLow = true;
                    }
                }
            }
            if (modifyLow) {
                modify = true;
                logger.info("low models auto close effect uid={}", req.getUid());
            }
            resp.getRoomConfig().setLowModels(1);
        }

        if (objectConfig.getIntValue(ActorConfigDao.HIDE_ALL) == 1) {
            if (objectConfig.getIntValue(ActorConfigDao.HIDE_GIFT_RIDE_VOICE) == 0
                    || objectConfig.getIntValue(ActorConfigDao.HIDE_MIC) == 0
                    || objectConfig.getIntValue(ActorConfigDao.HIDE_RIPPLE) == 0
                    || objectConfig.getIntValue(ActorConfigDao.HIDE_RIDE) == 0
                    || objectConfig.getIntValue(ActorConfigDao.HIDE_GIFT) == 0
            ) {
                config.put(ActorConfigDao.HIDE_GIFT_RIDE_VOICE, 1);
                config.put(ActorConfigDao.HIDE_MIC, 1);
                config.put(ActorConfigDao.HIDE_RIPPLE, 1);
                config.put(ActorConfigDao.HIDE_RIDE, 1);
                config.put(ActorConfigDao.HIDE_GIFT, 1);
                modify = true;
            }
        }

        if (modify) {
            logger.info("reset effect uid={} config={}", req.getUid(), config);
            actorConfigDao.save(configData);
        }
        resp.getRoomConfig().setActorRoomConfig(config);
//        logger.info("after room config uid={} config={} lowModels={} objectConfig={}",
//                req.getUid(), config, resp.getRoomConfig().getLowModels(), objectConfig);
    }


    /**
     * 判断房间是否有密码，有密码时不返回流信息，app弹窗输入密码后再返回(pwd_check)
     */
    private boolean pwdCheck(MongoRoomData roomData, RoomActorDetailData ownerData, RoomActorDetailData myData, RoomVO resp) {
        // 房间密码处理
        if (!ObjectUtils.isEmpty(roomData.getPwd()) && !ownerData.getAid().equals(myData.getAid()) && !whitelistRedis.isInvisibilityEnterRoom(roomData.getRid(), myData.getAid())) {
            // 删除旧的进房记录，py回重新写入带密码的值
            roomRedis.deleteRoomJoin(myData.getAid());
            return false;
        } else {
            // 这里增加进房记录
            roomRedis.addRoomJoin(myData.getAid(), roomData.getRid());
            // 第三方数据
            String streamId = genStreamId(roomData.getRid(), roomData.getPwd());
            resp.getRoomConfig().setStreamId(streamId);
            resp.getRoomConfig().setZegoToken(zegoService.getZegoToken(myData.getAid(), streamId));
            return true;
        }
    }

    /**
     * 获取房间主题配置
     */
    @Cacheable(value = "cache", cacheManager = CaffeineCacheConfig.EXPIRE_1M_AFTER_WRITE,
            key = "#root.method.name+T(org.springframework.util.StringUtils).arrayToDelimitedString(args,'-')")
    public RoomThemeVO getRoomTheme(int theme, int svipLevel) {
        RoomThemeVO themeVO = new RoomThemeVO();
        getRoomTheme(theme, themeVO);
        if (svipLevel < SvipLevelService.GIF_BACKGROUND_LEVEL && !ObjectUtils.isEmpty(themeVO.getBgUrl())) {
            themeVO.setBgUrl(themeVO.getBgUrl() + (themeVO.getBgUrl().contains("x-oss-process") ? "/format,jpg" : "?x-oss-process=image/format,jpg"));
        }
        return themeVO;
    }

    public void getRoomTheme(int theme, RoomThemeVO themeVO) {
        if (theme >= 1000) {
            UploadBackgroundData bgData = uploadBackgroundDao.selectOne(theme);
            if (null != bgData) {
                themeVO.setTid(theme);
                themeVO.setBgUrl(bgData.getBgUrl());
                themeVO.setmIcon(bgData.getmIcon());
                themeVO.setcIcon(bgData.getcIcon());
                themeVO.setPreview(bgData.getPreview());
            }
        } else {
            MongoThemeData roomTheme = mongoThemeDao.findThemeData(theme);
            if (null != roomTheme) {
                themeVO.setTid(theme);
                String thumbnailUrl = ImageUrlGenerator.generateRoomTopicUrl(roomTheme.getBgurl());
                themeVO.setBgUrl(thumbnailUrl);
                themeVO.setmIcon(roomTheme.getMicon());
                themeVO.setcIcon(roomTheme.getCicon());
                themeVO.setPreview(thumbnailUrl);
                themeVO.setSvipLevel(roomTheme.getSvipLevel());
            }
        }
    }

    /**
     * 进入房间后置处理
     */
    private void afterJoinRoom(RoomDTO dto, MongoRoomData roomData, RoomActorDetailData myData, RoomVO resp) {
        BaseTaskFactory.getFactory().add(new Task() {
            @Override
            protected void execute() {
                roomWebSender.sendForceEnterRoom(dto.getRoomId(), dto.getUid());
                // 非房主加入房间记录
                recentlyRoomRedis.addRecentlyActor(dto.getRoomId(), dto.getUid());
                // 记录进入房间的时间
                if (0 < dto.getRequestTime() && dto.getRequestTime() < System.currentTimeMillis() + 1000000L) {
                    recentlyRoomRedis.addEnterRoomClientTime(dto.getUid(), dto.getRequestTime());
                }
                if (dto.isChangeRoomName() && null != resp) {
                    // 房间名字变更
                    sendRoomInfoChangeMsg(resp.getRoomConfig().getRoomTheme().getBgUrl(), roomData);
                }
                micApplyRedis.removeMicApply(dto.getRoomId(), dto.getUid());
                if (RoomUtils.isHomeowner(myData.getAid(), roomData.getRid())) {
                    roomRedis.setLastCreateRoom(myData.getAid(), 1);
                }
                if (whitelistRedis.isInvisibilityEnterRoom(dto.getRoomId(), dto.getUid())) {
                    // 隐身进房
                    return;
                }
                // 发送fcm通知
                pushFcmNotice(dto, roomData, myData);
                // 语音房增加新用户进房消息
                if (ActorUtils.isNewRegisterActor(myData.getAid(), 14)) {
                    sendNewbieJoinRoomMsg(myData, dto.getRoomId());
                }
                if (dto.getSvipOnlineScene() == 1 && myData.getSvipLevel() >= 11) {
                    svipLevelService.onlineBroadcastMsg(dto.getUid());
                }
                int nowTime = DateHelper.getNowSeconds();
                RoomEventData ongoingRoomEvent = roomEventDao.getOngoingRoomEvent(dto.getRoomId());
                if (ongoingRoomEvent != null && nowTime >= ongoingRoomEvent.getStartTime() && nowTime <= ongoingRoomEvent.getEndTime()) {
                    roomEventRedis.addEnterRoomUser(ongoingRoomEvent.getId(), dto.getUid());
                }
            }
        });
    }

    private void sendNewbieJoinRoomMsg(RoomActorDetailData myData, String roomId) {
        Set<String> inRoomUserSet = roomPlayerRedis.getRoomActors(roomId);
        Set<String> allStaffUser = newbieService.getNewbieHostSet(inRoomUserSet);
        if (!CollectionUtils.isEmpty(inRoomUserSet) && !CollectionUtils.isEmpty(allStaffUser)) {
            Set<String> inRoomStaffSet = inRoomUserSet.stream().filter(allStaffUser::contains).collect(Collectors.toSet());
            if (!CollectionUtils.isEmpty(inRoomStaffSet)) {
                NewbieJoinRoomMessage msg = newbieService.getJoinRoomMsg(myData);
                if (ServerConfig.isNotProduct()) {
                    logger.info("sendNewbieJoinRoomMsg inRoomStaffSet={}", JSON.toJSONString(inRoomStaffSet));
                }
                inRoomStaffSet.forEach(staff -> roomWebSender.sendPlayerWebMsg(roomId, "", staff, msg, false));
            }
        }
    }

    private void pushFcmNotice(RoomDTO dto, MongoRoomData roomData, RoomActorDetailData myData) {
        if (myData == null || myData.getRobot() == 1) {
            return;
        }
        if (dto.getRoomId().contains(dto.getUid())) {
            String key = "str:createRoomFcm:" + DateHelper.ARABIAN.formatDateInDay() + ":" + dto.getRoomId();
            if (Boolean.TRUE.equals(clusterRedis.opsForValue().setIfAbsent(key, String.valueOf(DateHelper.getNowSeconds()), 1, TimeUnit.DAYS))) {
                Set<String> friends = friendService.getFriendSet(dto.getUid()).getData();
                Set<String> follows = followRoomDao.getRoomAllFansSet(roomData.getRid());
                follows.removeIf(friends::contains);
                // 好友：创建者头像+昵称，邀请您进房间
                Map<String, String> paramMap = new HashMap<>();
                paramMap.put("aid", roomData.getRid());
                paramMap.put("actionType", MsgType.FCM_JUMP_ROOM);
                if (!ObjectUtils.isEmpty(friends)) {
                    msgService.sendFcm(new SendFcmDTO(friends, paramMap, myData.getName(), myData.getName(),
                            FCM_FRIEND_EN_BODY.formatted(myData.getName()), FCM_FRIEND_AR_BODY.formatted(myData.getName()), myData.getHead()));
                }
                if (!ObjectUtils.isEmpty(follows)) {
                    // 房间关注者：房间头像+房间名字，欢迎您来我的房间，愿我们一起玩得开心。
                    msgService.sendFcm(new SendFcmDTO(follows, paramMap, roomData.getName(), roomData.getName(),
                            FCM_FOLLOW_EN_BODY, FCM_FOLLOW_AR_BODY, ImageUrlGenerator.generateRoomUserUrl(roomData.getHead())));
                }
            }
        }
    }

    private void sendRoomInfoChangeMsg(String themeUrl, MongoRoomData roomData) {
        BaseTaskFactory.getFactory().addSlow(new Task() {
            @Override
            protected void execute() {
                // 房间名字变更
                RoomInfoChangeMsg msg = new RoomInfoChangeMsg();
                msg.setRid(roomData.getRid());
                msg.setThemeUrl(themeUrl);
                msg.setRoomName(roomData.getName());
                msg.setRoomHead(ImageUrlGenerator.generateRoomUserUrl(roomData.getHead()));
                msg.setSvipLevel(0);
                roomWebSender.sendRoomWebMsg(roomData.getRid(), "", msg, true);
            }
        });
    }

    private void sendOnboardingMsg(RoomDTO dto) {

    }

    /**
     * 判断是否为房间管理员 （包括管理员/房主/副房主 ）
     */
    private boolean isAdmin(String roomId, String uid) {
        RoomRoleData roomRoleData = memberDao.getRoleData(roomId, uid);
        return roomRoleData.getRole() == RoomRoleType.MANAGER || roomRoleData.isHost();
    }

    /**
     * 生成第三方流
     *
     * @param roomId 房间id
     * @param pwd    房间密码
     */
    public String genStreamId(String roomId, String pwd) {
        if (ObjectUtils.isEmpty(pwd)) {
            pwd = "you_star";
        }
        try {
            return DigestUtils.md5DigestAsHex((roomId + pwd).getBytes());
        } catch (Exception e) {
            logger.error("get streamId error roomId={} pwd={}", roomId, pwd);
            return null;
        }
    }

    /**
     * 填充返回数据
     */
    private void fillRoomResp(RoomActorDetailData ownerData, RoomActorDetailData myData, MongoRoomData roomData, RoomVO resp, RoomDTO req) {
        // 房主信息
        resp.copyFromActorData(ownerData);
        resp.getRoomOwner().setIsBeautifulRid(AppVersionUtils.versionCheck(107, req) ? ownerData.getBeautifulRid() : 0);
        resp.getRoomOwner().setUserLevel(ownerData.getActiveLevel());
        resp.getRoomOwner().setBadgeList(ownerData.getBadgeList());
        resp.getRoomOwner().setIdentify(ownerData.getIdentify());
        resp.getRoomOwner().setCountry(ownerData.getCountry());
        resp.getRoomOwner().setName(ownerData.getName());
        resp.getRoomOwner().setVip(ownerData.getVipLevel());
        resp.getRoomOwner().setSvipLevel(ownerData.getSvipLevel());
        resp.getRoomOwner().setHead(ownerData.getHead());
        resp.getRoomOwner().setMicFrame(ownerData.getMicFrame());
        resp.getRoomOwner().setBd(ownerData.getBd());
        resp.getRoomOwner().setBdAdminLevel(ownerData.getBdAdminLevel());
        // 用户信息
        resp.getRoomVisitor().setRidInfo(myData.getRidData());
        resp.getRoomVisitor().setUid(myData.getAid());
        resp.getRoomVisitor().setMyName(myData.getName());
        resp.getRoomVisitor().setMyHead(myData.getHead());
        resp.getRoomVisitor().setIdentify(myData.getIdentify());
        resp.getRoomVisitor().setBubbleId(myData.getBubbleId());
        resp.getRoomVisitor().setIsFollowRoom(commonDao.isFollowRoom(req.getRoomId(), myData.getAid()) ? 1 : 0);
        resp.getRoomVisitor().setUserLevel(myData.getActiveLevel());
        resp.getRoomVisitor().setWealthLevel(myData.getWealthLevel());
        resp.getRoomVisitor().setCharmLevel(myData.getCharmLevel());
        resp.getRoomVisitor().setBadgeList(myData.getBadgeList());
        resp.getRoomVisitor().setStreamId(zegoApi.generateActorStreamId(roomData.getOwnerRid(), myData.getRid(), myData.getAid()));
        resp.getRoomVisitor().setFamilyId(myData.getFamilyId());
        // svip
        SvipLevelData svipLevelData = svipLevelService.getSvipLevelData(myData.getAid());
        if (null != svipLevelData) {
            resp.getRoomVisitor().setSvipLevel(svipLevelData.getLevel());
            if (svipLevelData.getConfig().getMystery() == 1) {
                resp.getRoomVisitor().setMystery(1);
                resp.getRoomVisitor().setMysteryVoice(svipLevelData.getConfig().getMysteryVoice());
            }
        }
        // 平台迎新白名单
        if (whitelistRedis.inWhitelist(WhitelistRedis.NEWBIE_HOST_LIST, myData.getAid()) || (myData.getGender() == 2 && myData.getFamilyId() > 0)) {
            resp.getRoomVisitor().setNewbieHost(1);
            resp.getRoomConfig().setWelcomeWordList(roomConfig.getRandomWelconeList(req.getSlang()));
        }
        resp.getRoomVisitor().setOfficialUser(RoomKickService.POWER_USER.contains(myData.getAid()) ? 1 : 0);
        // 处理进场动画
        fillJoinCartoon(myData, resp, req.getRoomId());
        // 房间配置信息
        resp.copyFromMongoRoomData(roomData);
        if (whitelistRedis.isInvisibilityEnterRoom(req.getRoomId(), req.getUid())) {
            // 隐身白名单用户进入密码房时无需弹出密码输入框，可隐身进入
            resp.getRoomConfig().setPwd(0);
        }
        resp.getRoomConfig().setTag(roomData.getTag());
        resp.getRoomConfig().setOnline(roomPlayerRedis.getRoomActorsCount(roomData.getRid()));
        resp.getRoomConfig().setTagName(roomTags.getTagNameById(roomData.getTag(), req.getSlang()));
        resp.getRoomConfig().setRoomHead(ImageUrlGenerator.generateRoomUserUrl(roomData.getHead(), ownerData.getSvipLevel()));
        resp.getRoomConfig().setMemberFeeList(RoomSettingsService.MEMBER_FEE_LIST);
        // 房间贡献度
        long devotes = roomNewService.getRoomTotalDevote(roomData.getRid());
        resp.getRoomConfig().setDevotes(devotes);
        resp.getRoomConfig().setDevotesStr(formatDevotes(devotes));
        // 房间会员数
        resp.getRoomConfig().setMemberCount(roomMemberDao.getMemberCount(req.getRoomId()));
        // 房间固定配置
        resp.getRoomConfig().setFollowTime(roomConfig.getFollowTime());
        resp.getRoomConfig().setDirtyVersion(getDirtyVersion());
        resp.getRoomConfig().setSendGiftExpire(roomConfig.getSendGiftExpireTime());
        resp.getRoomConfig().setVipRoomConfig(roomConfig.getVipRoomConfig());
        resp.getRoomConfig().setFollowWord(roomConfig.getRandomFollowStr(req.getSlang()));
        resp.getRoomConfig().setVideoVip(commonConfig.getConfigValue(CommonConfig.VIDEO_VIP_LEVEL, 3));
        resp.getRoomConfig().setHttpHeartSwitch(roomConfig.getHttpHeartSwitch());
        resp.getRoomConfig().setEmojiSwitch(roomConfig.getEmojiSwitch());
        resp.getRoomConfig().setGuideList(roomConfig.getRandomGuideList(req.getSlang()));
        // 新用户特殊属性
        if (ActorUtils.isNewRegisterActor(req.getUid(), 7)) {
            resp.getRoomVisitor().setIsRookie(1);
            resp.getRoomVisitor().setGiftBag(giftBagDao.hadReceivedGift(req.getUid()) ? 1 : 0);
        }
        // 房间重复消息费用
        resp.getRoomConfig().setRepeatMsgCost(RoomMsgController.REPEAT_MSG_COST);
        // 房间最近消息
        //resp.getRoomConfig().setLastRoomTextMsgList(roomLastMsgRedis.getLastRoomMsgList(req.getRoomId(), 3));
        resp.getRoomConfig().setRankInfo(voiceRoomRankingRedis.getVoiceRoomRanking().get(req.getRoomId()));
    }

    @Cacheable(value = "getRoomTotalDevote", key = "#p0", cacheManager = CaffeineCacheConfig.EXPIRE_1M_AFTER_WRITE)
    public long getRoomTotalDevote(String roomId) {
        return commonDao.getRoomTotalDevote(roomId);
    }

    /**
     * 处理进场对象
     */
    private void fillJoinCartoon(RoomActorDetailData myData, RoomVO resp, String roomId) {
        EnterCartonData cartonData = enterRoomContent.fillJoinCartoon(myData.getRideOption(), myData.getAid(), 1 == myData.getMystery(), roomId);
        resp.getRoomVisitor().setSmallIcon(cartonData.getSmallIcon());
        resp.getRoomVisitor().setJoinCartoonId(cartonData.getJoinCartoonId());
        resp.getRoomVisitor().setJoinCartoonType(cartonData.getJoinCartoonType());
        resp.getRoomVisitor().setSourceType(cartonData.getSourceType());
        resp.getRoomVisitor().setSourceUrl(cartonData.getSourceUrl());
        resp.getRoomVisitor().setSourceMd5(cartonData.getSourceMd5());
        resp.getRoomVisitor().setCartoonTimes(cartonData.getCartoonTimes());
        // 处理进房通知
        resp.getRoomVisitor().setEntryEffectUrl(cartonData.getEntryEffectUrl());
        resp.getRoomVisitor().setEntryEffectUrlAr(cartonData.getEntryEffectUrlAr());
    }

    /**
     * 填充麦位信息
     */
    private void fillMicData(RoomDTO req, RoomVO resp) {
        // 处理actor信息
        PageUtils.PageData<RoomActorDetailData> pageData = roomActorListService.findDetailList(req, 1, ACTOR_PAGE_SIZE, true);
        resp.getRoomActor().setNextPage(pageData.nextPage);
        resp.getRoomActor().setPageSize(pageData.pageSize);
        resp.getRoomActor().setTotalActors(pageData.totalSize);
        resp.getRoomActor().setRoomActorList(pageData.list);
        resp.setRoomMic(getRoomMicList(req.getRoomId()));
    }

    /**
     * 获取脏词版本
     */
    private int getDirtyVersion() {
        try {
            Object dirtyVersion = clusterRedis.opsForHash().get("hash:common_config_key", "dirty_version");
            return null == dirtyVersion ? 0 : Integer.parseInt(String.valueOf(dirtyVersion));
        } catch (Exception e) {
            logger.error("get dirty version msg={}", e.getMessage());
            return 0;
        }
    }

    /**
     * 判断是否调用了图灵盾接口，未调用接口的用户返回不为空
     */
    private boolean isCheckTnId(String uid) {
        try {
            Double score = clusterRedis.opsForZSet().score("zset:is:tn:check", uid);
            return score == null;
        } catch (Exception e) {
            logger.error("isCheckTnId error msg={}", e.getMessage(), e);
            return true;
        }
    }

    /**
     * 格式化房间贡献数值
     */
    private String formatDevotes(long devotes) {
        if (devotes >= 1000000) {
            return new BigDecimal(devotes / 1000000f + "").setScale(1, RoundingMode.HALF_UP) + "M";
        } else if (devotes >= 1000) {
            return new BigDecimal(devotes / 1000f + "").setScale(1, RoundingMode.HALF_UP) + "K";
        } else {
            return devotes + "";
        }
    }

    /**
     * 房主进入房间
     */
    private void ownerJoinRoom(MongoRoomData roomData, RoomActorDetailData ownerData, int vipLevel, RoomDTO req, RoomVO resp) {
        // 校验背景图片是否过期
        checkThemeExpired(roomData, ownerData, req.getUid(), vipLevel);
        resp.getRoomVisitor().setAdmin(1);
        resp.getRoomOwner().setName(resp.getRoomVisitor().getMyName());
        resp.getRoomOwner().setHead(resp.getRoomVisitor().getMyHead());
        resp.getRoomOwner().setCanUseRoomLock(roomSettingsService.canUseRoomLock(req.getUid()) ? 1 : 0);
        // 每日任务房主进入自己的房间
        dailyTaskService.sendToMq(new DailyTaskMqData(req.getUid(), 3, DateHelper.ARABIAN.formatDateInDay2(), 1));
        if (req.isRoomChange()) {
            roomChange(roomData, req, resp);
        }
    }

    /**
     * 校验背景图片是否过期
     */
    private void checkThemeExpired(MongoRoomData roomData, RoomActorDetailData ownerData, String uid, int vipLevel) {
        if (roomData.getTheme() >= 1000) {
            // 使用自定义上传的主题，如果vip等级小于5且上传卡过期，设置成默认主题
            if (!checkUploadBackground(uid)) {
                logger.info("room owner vip4 expired, set default theme. roomId={} theme={}", roomData.getRid(), roomData.getTheme());
                setDefaultTheme(uid, roomData);
            }
        } else {
            // 非自定义上传的主题
            MongoThemeData themeData = themeDao.findThemeData(roomData.getTheme());
            if (themeData == null) {
                setDefaultTheme(uid, roomData);
                return;
            }
            String hostId = RoomUtils.getRoomHostId(roomData.getRid());
            if (themeData.getType() == 1) {
                // 使用vip主题，如果vip等级小于2且用户等级小于20，设置成默认主题
                int userLevel = userLevelDao.getUserLevel(hostId);
                if (vipLevel < 2 && userLevel < 20) {
                    logger.info("room owner vip2 expired, set default theme. roomId={} theme={}", roomData.getRid(), roomData.getTheme());
                    setDefaultTheme(uid, roomData);
                }
            } else if (themeData.getType() == 11) {
                // 主播专属背景，不是主播身份后设置成默认主题
                if (familyMemberDao.selectByUid(hostId) == null) {
                    logger.info("room is not family member room. roomId={}", roomData.getRid());
                    setDefaultTheme(uid, roomData);
                }
            } else if (themeData.getType() == 12) {
                // SVIP权益失效后背景自动消失，如果正在使用，系统随机切换使用一张免费的背景。
                if (svipLevelService.getSvipLevel(ownerData.getAid()) < themeData.getSvipLevel()) {
                    logger.info("room owner svip expired, set default theme. roomId={} theme={}", roomData.getRid(), roomData.getTheme());
                    setDefaultTheme(uid, roomData);
                }
            } else if (themeData.getType() != 0) {
                // 购买和下发的主题过期后，设置成默认主题
                MineBackgroundData data = mineBackgroundDao.selectOne(roomData.getRid(), roomData.getTheme());
                if (data == null || DateHelper.getNowSeconds() >= data.getEndTime()) {
                    logger.info("Purchased background image expired, set default theme. roomId={} theme={}", roomData.getRid(), roomData.getTheme());
                    setDefaultTheme(uid, roomData);
                }
            }
        }
    }

    /**
     * 设置默认主题
     */
    private void setDefaultTheme(String uid, MongoRoomData roomData) {
        int defaultThemeId = 1;
        roomDao.updateField(roomData.getRid(), "theme", defaultThemeId);
        roomData.setTheme(defaultThemeId);
    }

    private void roomChange(MongoRoomData roomData, RoomDTO req, RoomVO resp) {
        roomSettingsService.sendRoomOptMsg(roomData.getRid(), req.getUid(), RoomConstant.LIVE_ROOM_MODE == roomData.getRoomMode() ? 8 : 7, false);
        // 防止切换房间造成缓存不一致，删除缓存重新发送麦位变更消息
        roomMicRedis.deleteRoomMic(roomData.getRid());
        try {
            roomMicService.switchRoomMode(roomData, req.getUid(), req.getRoomMode());
        } catch (Exception ignored) {
        }
    }

    /**
     * 其他用户进入房间
     */
    private void otherJoinRoom(RoomActorDetailData ownerData, RoomDTO req, RoomVO resp) {
        // 设置房间角色
        RoomMemberData managerData = roomMemberDao.findData(req.getRoomId(), req.getUid());
        // 0 房主 1 管理员 2 观众 3 会员
        int roomRoleType = roomMemberDao.getRoleContainMember(managerData, req.getRoomId(), req.getUid());
        resp.getRoomVisitor().setRole(roomRoleType);
        resp.getRoomVisitor().setAdmin(roomRoleType == RoomRoleType.HOST || roomRoleType == RoomRoleType.MANAGER ? 1 : 2);
        resp.getRoomVisitor().setIsMember(roomRoleType != RoomRoleType.AUDIENCE ? 1 : 0);
        resp.getRoomVisitor().setIsFollowHost(followDao.isFollowed(req.getUid(), ownerData.getAid()) ? 1 : 0);
    }

    /**
     * 进入房间权限判断
     */
    private void checkJoinRoomPrivilege(RoomActorDetailData myData, RoomActorDetailData ownerData, RoomDTO req) {
        if (myData.getValid() != 1) {
            throw new CommonException(RoomHttpCode.USER_INVALID);
        }
        if (ownerData.getValid() != 1 || userMonitorDao.isChatRoomBanned(req.getRoomId())) {
            throw new CommonException(RoomHttpCode.ROOM_BLOCK);
        }
        if (roomPlayerRedis.getRoomActorCount(req.getRoomId()) > 1500 && ServerConfig.isProduct()) {
            throw new CommonException(RoomHttpCode.ROOM_FULL);
        }
        if (myData.getAid().equals(ownerData.getAid())) {
            // 房主被禁，不能创建和进入房间
            String blockTime = blockRedis.checkBlock(actorDao.getActorDataFromCache(ownerData.getAid()).getTn_id(), BlockTnConstant.BLOCK_CREATE_ROOM);
            if (!ObjectUtils.isEmpty(blockTime)) {
                throw new CommonException(RoomHttpCode.BLOCK_CREATE_ROOM, blockTime);
            }
            if (!ObjectUtils.isEmpty(mysteryService.getMysteryId(ownerData.getAid()))) {
                throw new CommonException(RoomHttpCode.MYSTERY_ROOM_LIMIT);
            }
        } else {

            if (roomBlacklistDao.isBlock(req.getRoomId(), myData.getAid())) {
                throw new CommonException(RoomHttpCode.BLOCKED);
            }
            if (newRookieRoomRedis.isNewRookieRoom(req.getRoomId()) && !newRookieRoomRedis.canSeeRookieRoom(myData.getAid())) {
                // 非新用户不可进入迎新房
                throw new CommonException(RoomHttpCode.ONLY_OPEN_TO_NEW_USER);
            }
        }
        int kickTime = roomKickRedis.getKickTime(req.getRoomId(), myData.getAid());
        if (kickTime > 0) {
            int leftTime = (kickTime + (int) TimeUnit.DAYS.toSeconds(1)) - DateHelper.getNowSeconds();
            if (leftTime > 0) {
                if (leftTime >= TimeUnit.HOURS.toSeconds(1)) {
                    throw new CommonException(RoomHttpCode.KICKED_HOURS, leftTime / TimeUnit.HOURS.toSeconds(1));
                } else {
                    throw new CommonException(RoomHttpCode.KICKED_MINUTES, leftTime / TimeUnit.MINUTES.toSeconds(1));
                }
            }
        }
        if (!isCheckTnId(myData.getAid())) {
            logger.error("actor is not check tnId uid={}", myData.getAid());
            monitorSender.info("waho_java_exception", "用户未校验图灵盾接口", JSON.toJSONString(req));
            basePlayerRedis.removeToken(myData.getAid());
            throw new CommonException(RoomHttpCode.SESSION_INVALID);
        }
        if (req.invalidPkgName()) {
            logger.error("app pkg name invalid uid={} pkgName={}", myData.getAid(), req.getApp_package_name());
            basePlayerRedis.removeToken(myData.getAid());
            throw new CommonException(RoomHttpCode.SESSION_INVALID);
        }
    }

    /**
     * 分页获取房间内的用户
     */
    public RoomActorVO getRoomActors(PageDTO req) {
        RoomActorVO vo = new RoomActorVO();
        PageUtils.PageData<RoomActorDetailData> pageData = roomActorListService.findDetailList(req, req.getPage(), ACTOR_PAGE_SIZE, true);
        vo.setNextPage(pageData.nextPage);
        vo.setPageSize(pageData.pageSize);
        vo.setTotalActors(pageData.totalSize);
        vo.setRoomActorList(pageData.list);
        return vo;
    }

    /**
     * 校验进房密码，成功后返回麦位列表和流信息
     */
    public PwdCheckVo pwdCheck(PwdCheckDTO req) {
        PwdCheckVo vo = new PwdCheckVo();
        String pwd = parseUserPwd(req.getPwd());
        if (ObjectUtils.isEmpty(pwd)) {
            logger.info("pwd check fail roomId={} uid={}", req.getRoomId(), req.getUid());
            throw new CommonException(HttpCode.PARAM_ERROR);
        }
        ActorData actorData = actorDao.getActorDataFromCache(req.getUid());
        if (null == actorData) {
            logger.error("cannot find actor, uid={}", req.getUid());
            throw new CommonException(HttpCode.PARAM_ERROR);
        }
        MongoRoomData roomData = roomDao.findData(req.getRoomId());
        if (null == roomData) {
            logger.error("cannot find room roomId={} uid={}", req.getRoomId(), req.getUid());
            throw new CommonException(RoomHttpCode.ROOM_NOT_EXIST);
        }
        doPwdCheck(roomData.getPwd(), pwd);
        // 第三方数据
        vo.setStreamId(genStreamId(roomData.getRid(), roomData.getPwd()));
        vo.setZegoToken(zegoService.getZegoToken(req.getUid(), vo.getStreamId()));
        // 麦位列表
        vo.setRoomMic(getRoomMicList(req.getRoomId()));
        roomRedis.addRoomJoin(req.getUid(), roomData.getRid() + "_" + DigestUtils.md5DigestAsHex(pwd.getBytes()));
        PageDTO pageDTO = new PageDTO();
        pageDTO.setPage(1);
        pageDTO.setUid(req.getUid());
        pageDTO.setRoomId(req.getRoomId());
        vo.setRoomActor(getRoomActors(pageDTO));
        vo.setRoomMode(roomData.getRoomMode() == 0 ? 1 : roomData.getRoomMode());
        afterJoinRoom(req, roomData, roomActorCache.getData(req.getRoomId(), req.getUid(), false), null);
        return vo;
    }

    /**
     * 房间密码校验
     *
     * @param roomPwd 房主设置的密码
     * @param userPwd 用户输入的密码
     */
    private void doPwdCheck(String roomPwd, String userPwd) {
        if (null == roomPwd || !roomPwd.equals(userPwd)) {
            throw new CommonException(RoomHttpCode.PASSWORD_ERROR);
        }
    }

    private String parseUserPwd(String userPwd) {
        String userStrPwd = "";
        try {
            // 针对阿语数字进行特殊处理
            userStrPwd = Integer.parseInt(userPwd) + "";
        } catch (NumberFormatException e) {
            logger.info("parse userPwd error. userPwd={}", userPwd);
        }
        return userStrPwd;
    }

    /**
     * 高频接口：获取麦位列表
     */
    public RoomMicListVo getRoomMicList(String roomId) {
        return roomMicService.getRoomMicListFromRedis(roomId);
    }

    /**
     * 获取即构或声网RTC授权
     */
    public String getToken(RoomDTO req) {
        String inRoomId = roomPlayerRedis.getActorRoomStatus(req.getUid());
        if (null == inRoomId || !inRoomId.equals(req.getRoomId())) {
            throw new CommonException(HttpCode.NOT_IN_ROOM);
        }
        MongoRoomData roomData = roomDao.findData(req.getRoomId());
        String streamId = genStreamId(req.getRoomId(), roomData.getPwd());
        return zegoService.getZegoToken(req.getUid(), streamId);
    }

    /**
     * 进入房间记录
     */
    public void enterRoom(RoomDTO req) {
        logger.info("enter room roomId={} uid={} reqTime={} requestId={}",
                req.getRoomId(), req.getUid(), req.getRequestTime(), req.getRequestId());
    }

    /**
     * 退出房间记录
     */
    public void quitRoom(QuitRoomDTO dto) {
        // 8.42起客户端不发送2003离开房间的mars消息
        long enterRoomTime = recentlyRoomRedis.getEnterRoomClientTime(dto.getUid());
        if (0 != dto.getRequestTime() && dto.getRequestTime() < enterRoomTime) {
            logger.info("quit request invalid, roomId={} uid={} enterRoomTime={} reqTime={}", dto.getRoomId(), dto.getUid(), enterRoomTime, dto.getRequestTime());
        } else {
            // 用户进出直播间事件
            if (RoomUtils.isLiveRoom(dto.getRoomId())) {
                int nowTime = DateHelper.getNowSeconds();
                LiveRoomEnterExitRecordEvent recordEvent = new LiveRoomEnterExitRecordEvent();
                recordEvent.setUid(dto.getUid());
                recordEvent.setRoom_Id(dto.getRoomId());
                String liveId = liveDataRedis.getLiveId(dto.getRoomId());
                recordEvent.setLive_id(liveId);
                recordEvent.setEnter_time((int) (enterRoomTime / 1000L));
                recordEvent.setEnter_room_source(dto.getEnterLiveSource());
                recordEvent.setExit_time(nowTime);
                recordEvent.setExit_room_reason(dto.getReason());
                int stayDurationTime = nowTime - (int) (enterRoomTime / 1000L);
                recordEvent.setStay_duration(stayDurationTime);
                recordEvent.setIs_robot(dto.getRobot());
                recordEvent.setSend_gift_price(liveUserDataRedis.getLiveSendGiftTotalPrice(liveId, dto.getUid()));
                recordEvent.setIs_click_follow(liveUserDataRedis.hasClickFollow(liveId, dto.getUid()) ? 1 : 0);
                recordEvent.setSend_msg_counts(liveUserDataRedis.getLiveSendMsgCount(liveId, dto.getUid()));
                eventReport.track(new EventDTO(recordEvent));
                if (DateHelper.getNowSeconds() < 1741986000) {
                    // 斋月活动2025年3月12日结束，3月15日停止发送
                    commonTaskService.sendCommonTaskMq(new CommonMqTopicData(dto.getUid(), dto.getRoomId(), "", liveId, CommonMqTaskConstant.WATCH_LIVE_TIME, stayDurationTime / 60));
                }
            }
            roomWebSender.sendLeaveRoom(dto.getRoomId(), dto.getUid());
        }
    }

    /**
     * 获取房间密码
     */
    public RoomPwdVO getRoomPwd(RoomDTO req) {
        String uid = req.getUid();
        String roomId = req.getRoomId();
        if (ObjectUtils.isEmpty(uid) || ObjectUtils.isEmpty(roomId)) {
            logger.error("get room pwd param error. uid={} roomId={}", uid, roomId);
            throw new CommonException(HttpCode.PARAM_ERROR);
        }
        if (!Objects.equals(uid, RoomUtils.getRoomHostId(roomId))) {
            logger.error("not room owner cannot get room pwd. uid={} roomId={}", uid, roomId);
            throw new CommonException(HttpCode.PARAM_ERROR);
        }
        MongoRoomData roomData = roomDao.findData(roomId);
        if (roomData == null) {
            logger.error("room data is null. uid={} roomId={}", uid, roomId);
            throw new CommonException(HttpCode.PARAM_ERROR);
        }
        RoomPwdVO vo = new RoomPwdVO();
        vo.setRoom_id(roomId);
        vo.setPwd(ObjectUtils.isEmpty(roomData.getPwd()) ? "" : roomData.getPwd());
        return vo;
    }

    public MongoRoomData getDataFromCache(String roomId) {
        MongoRoomData roomData = roomDao.getDataFromCache(roomId);
        if (null == roomData) {
            throw new CommonException(HttpCode.PARAM_ERROR);
        }
        return roomData;
    }

    public void actorValidCheck(String uid) {
        ActorData actorData = actorDao.getActorDataFromCache(uid);
        if (null == actorData) {
            throw new CommonException(HttpCode.PARAM_ERROR);
        }
        if (actorData.getValid() == 0) {
            throw new CommonException(RoomHttpCode.USER_INVALID);
        }
    }

    public Object addFollow(RoomNewDTO req) {
        actorValidCheck(req.getUid());
        if (whitelistRedis.isInvisibilityEnterRoom(req.getRoomId(), req.getUid())) {
            // 隐身进房无法操作
            throw new CommonException(RoomHttpCode.INVISIBLE_ACCOUNT_CANNOT_BE_OPERATED);
        }
        if (null != mysteryRedis.getMysteryId(req.getUid())) {
            throw new CommonException(RoomHttpCode.MYSTERY_FOLLOW_ROOM_LIMIT);
        }
        MongoRoomData roomData = getDataFromCache(req.getRoomId());
        followRoomDao.upsert(roomData.getRid(), req.getUid());
        sendFollowRoomMsg(req.getUid(), req.getRoomId());
        return null;
    }

    public void sendFollowRoomMsg(String uid, String roomId) {
        if (ServerConfig.isProduct() && Boolean.FALSE.equals(clusterRedis.opsForValue().setIfAbsent("str:addRoomFollow:" + roomId + uid, uid, 2, TimeUnit.HOURS))) {
            return;
        }
        BaseTaskFactory.getFactory().addSlow(new Task() {
            @Override
            protected void execute() {
                ActorData actorData = actorDao.getActorDataFromCache(uid);
                if (null == actorData) {
                    return;
                }
                RoomNotificationMsg msg = new RoomNotificationMsg();
                msg.setUid(uid);
                msg.setUser_name(actorData.getName());
                msg.setUser_head(ImageUrlGenerator.generateRoomUserUrl(actorData));
                msg.setText(String.format(FOLLOW_ROOM_MSG, actorData.getName()));
                msg.setText_ar(String.format(FOLLOW_ROOM_MSG_AR, actorData.getName()));
                List<HighlightTextObject> list = new ArrayList<>();
                HighlightTextObject object = new HighlightTextObject();
                object.setText(actorData.getName());
                object.setHighlightColor("#FFE200");
                list.add(object);
                msg.setHighlight_text(list);
                msg.setHighlight_text_ar(list);
                msg.setHide_head(1);
                msg.setFromRoomId(roomId);
                List<RoomMemberData> adminList = memberDao.findAdminList(roomId);
                for (RoomMemberData roomMemberData : adminList) {
                    roomWebSender.sendPlayerWebMsg(roomId, uid, roomMemberData.getAid(), msg, false);
                }
                roomWebSender.sendPlayerWebMsg(roomId, uid, RoomUtils.getRoomHostId(roomId), msg, false);
            }
        });
    }

    public Object delFollow(RoomNewDTO req) {
        if (whitelistRedis.isInvisibilityEnterRoom(req.getRoomId(), req.getUid())) {
            // 隐身进房无法操作
            throw new CommonException(RoomHttpCode.INVISIBLE_ACCOUNT_CANNOT_BE_OPERATED);
        }
        followRoomDao.delete(req.getRoomId(), req.getUid());
        return null;
    }

    public UploadRoomThemeVO uploadTheme(RoomNewDTO req) {
        if (ObjectUtils.isEmpty(req.getRoom_them()) || ObjectUtils.isEmpty(req.getUid()) ||
                ObjectUtils.isEmpty(req.getRoomId()) || req.getRoomId().length() < 2) {
            throw new CommonException(HttpCode.PARAM_ERROR);
        }
        MongoRoomData roomData = getDataFromCache(req.getRoomId());
        RoomRoleData roleData = memberDao.getRoleDataFromCache(req.getRoomId(), req.getUid());
        if (RoomRoleType.HOST != roleData.getRole()) {
            logger.info("upload theme not room owner or vice owner. roomId={} uid={}", req.getRoomId(), req.getUid());
            throw new CommonException(RoomHttpCode.NOT_HAVE_PERMISSION);
        }
        if (!checkUploadBackground(req.getUid())) {
            logger.info("Only VIP5 and above can upload room theme. roomId={} uid={}", req.getRoomId(), req.getUid());
            throw new CommonException(RoomHttpCode.CAN_NOT_UPLOAD_ROOM_BACKGROUND);
        }
        if (uploadBackgroundDao.selectCount(RoomUtils.getRoomHostId(req.getRoomId())) >= MAX_UPLOAD_NUM) {
            logger.info("Upload no more than 10 photos. uid={}", req.getUid());
            throw new CommonException(RoomHttpCode.UPLOAD_BG_NO_MORE_THAN_10_PHOTOS);
        }
        String roomTheme;
        if (req.getRoom_them().startsWith("http")) {
            roomTheme = CDNUtils.getHttpCdnUrl(req.getRoom_them());
        } else {
            roomTheme = CDNUtils.getCdnUrl(req.getRoom_them());
        }
        if (freezeInfoRedis.isFrozenUpdateInfo(FreezeInfoRedis.ROOM_BACKGROUND, req.getRoomId())) {
            throw new CommonException(RoomHttpCode.HAS_SUSPENDED_THIS_FEATURE);
        }
        if (dirtyPictureRedis.isDirtyPicture(roomTheme)) {
            throw new CommonException(HttpCode.IS_DIRTY_PICTURE);
        }
        if (detectService.detectImage(new ImageDTO(roomTheme + "?x-oss-process=image/format,jpg", DetectOriginConstant.ROOM_BG, req.getUid())).getData().getIsSafe() == 0) {
            throw new CommonException(RoomHttpCode.COVER_UNAVAILABLE);
        }
        UploadBackgroundData bgData = new UploadBackgroundData(RoomUtils.getRoomHostId(req.getRoomId()), roomTheme, roomTheme);
        uploadBackgroundDao.insert(bgData);
        roomDao.updateRoomTheme(req.getRoomId(), bgData.getId(), 0);
        // 房间背景变更
        int svipLevel = svipLevelService.getSvipLevel(req.getUid());
        if (svipLevel < SvipLevelService.GIF_BACKGROUND_LEVEL) {
            roomTheme += "?x-oss-process=image/format,jpg";
        }
        sendRoomInfoChangeMsg(roomTheme, roomData);
        return new UploadRoomThemeVO(bgData.getId(), bgData.getBgUrl(), bgData.getmIcon(), bgData.getcIcon(), bgData.getPreview());
    }

    /**
     * 上传背景需要vip5以上，或者购买了背景上传卡
     */
    private Boolean checkUploadBackground(String uid) {
        if (vipInfoDao.getIntVipLevel(uid) >= 5 || svipLevelService.getSvipLevel(uid) >= 5) {
            return true;
        }
        BackgroundUploadCardData data = backgroundUploadCardDao.selectOneByUid(uid);
        return data != null && data.getEndTime() > DateHelper.getNowSeconds();
    }

    /**
     * 传主题时提示用户要vip4以上
     */
    public void checkUploadTheme(RoomNewDTO req) {
        String uid = req.getUid();
        String roomId = req.getRoomId();
        if (ObjectUtils.isEmpty(uid) || ObjectUtils.isEmpty(roomId) || roomId.length() < 2) {
            logger.info("check upload theme param error. uid={} roomId={}", uid, roomId);
            throw new CommonException(RoomHttpCode.PARAM_ERROR);
        }
        if (!isOwnerOrViceOwner(uid, roomId)) {
            logger.info("only room owner or vice owner can operate this. uid={} roomId={}", uid, roomId);
            throw new CommonException(RoomHttpCode.NOT_HAVE_PERMISSION);
        }
        if (!checkUploadBackground(uid)) {
            logger.info("Only Tycoon4 or above can upload room theme. uid={} roomId={}", uid, roomId);
            throw new CommonException(RoomHttpCode.CAN_NOT_UPLOAD_ROOM_BACKGROUND);
        }
        if (uploadBackgroundDao.selectCount(RoomUtils.getRoomHostId(req.getRoomId())) >= MAX_UPLOAD_NUM) {
            logger.info("Upload no more than 10 photos. uid={}", req.getUid());
            throw new CommonException(RoomHttpCode.UPLOAD_BG_NO_MORE_THAN_10_PHOTOS);
        }
    }

    /**
     * 房间内banner图标按钮
     */
    public RoomBannerVO getRoomBanner(RoomDTO req) {
        ActorData actorData = actorDao.getActorDataFromCache(req.getUid());
        if (actorData == null) {
            logger.error("can not find actor data. uid={}", req.getUid());
            throw new CommonException(HttpCode.PARAM_ERROR);
        }
        RoomBannerVO vo = new RoomBannerVO();
        JSONObject switchConfig = commonConfig.getSwitchConfig();
        vo.setList(bannerListService.getRoomBanner(req, 0, req.getToken(), actorData));
        vo.setSidebarBannerList(bannerListService.getRoomBanner(req, 1, req.getToken(), actorData));
        vo.setFunctionList(bannerListService.getRoomBanner(req, 2, req.getToken(), actorData));
        vo.setHotGameList(bannerListService.getRoomBanner(req, 3, req.getToken(), actorData));
        if (advancedGameRedis.hasAdvancedGamePer(req.getUid())) {
            vo.setAdvancedGameList(bannerListService.getRoomBanner(req, 4, req.getToken(), actorData));
        }
        vo.setGuess_icon("");
        vo.setDice2_option(1);
        // ludo 游戏开关
        vo.setLudo(switchConfig.getIntValue("ludo"));
        // 幸运数字开关
        vo.setLuckyNumFunction(switchConfig.getIntValue("lucky_num_function"));
        // 房间投票开关
        vo.setRoom_vote(switchConfig.getIntValue("room_vote"));
        // 幸运转盘开关
        vo.setLucky_wheel_switch(switchConfig.getIntValue("lucky_wheel_switch"));
        // 红包开关
        vo.setLucky_bag_switch(switchConfig.getIntValue("lucky_box_switch"));
        vo.setLucky_box_switch(ServerConfig.isNotProduct() ? 1 : 0);
        fillLuckyNumConfig(vo, req.getUid(), req.getRoomId());
        // UMO游戏开关
        vo.setUmo_switch(switchConfig.getIntValue("umo_switch"));
        // 消消乐游戏开关
        vo.setMonster_crush_switch(switchConfig.getIntValue("monster_crush_switch"));
        // 多米诺游戏开关
        vo.setDomino_switch(switchConfig.getIntValue("domino_switch"));
        // 团战pk开关
        vo.setTeamPk(1);
        vo.setTurntable(turntableGameRedis.getGameInfoByRoomId(req.getRoomId()) != null ? 1 : 0);
        vo.setGame_status(getGameStatus(req.getRoomId()));
        // vo.setFinger_guess(fingerGuessRedis.getAllGuessNum() > 0 ? 1 : 0);
        vo.setRocket_switch(AppVersionUtils.versionCheck(106, req) ? sysConfigDao.getIntValue(SysConfigDao.ROCKET_CONFIG, SysConfigDao.ROOM_ROCKET_SWITCH_KEY) : 0);
        vo.setLucky_box(checkLuckyBox(req.getRoomId()));  // 进房间是否检查红包
        // vo.setRoomEventNum(roomEventDao.getNotEndRoomEventNum(req.getRoomId(), curTime));
        return vo;
    }

    public VideoCallsBannerVO getVideoCallsBanner(RoomDTO req) {
        VideoCallsBannerVO vo = new VideoCallsBannerVO();
        if (!riskControlService.canSeeBcGame(req.getUid())) {
            vo.setRoomInsideBanners(Collections.emptyList());
            vo.setRoomOutsideBannerList(Collections.emptyList());
            return vo;
        }
        List<RoomBannerData> bannerList = roomBannerDao.findShow1v1RoomBanners();
        List<VideoCallsBannerVO.RoomInsideBanner> roomInsideBannerList = new ArrayList<>();
        if (!CollectionUtils.isEmpty(bannerList)) {
            for (RoomBannerData data : bannerList) {
                String url = data.getUrl() != null ? data.getUrl() : "";
                VideoCallsBannerVO.RoomInsideBanner roomInsideBanner = new VideoCallsBannerVO.RoomInsideBanner();
                roomInsideBanner.setWebType(data.getWeb_type());
                roomInsideBanner.setType(data.getType());
                roomInsideBanner.setEvent(data.getEvent());
                roomInsideBanner.setGame_type(data.getGame_type());
                roomInsideBanner.setWidth(data.getWidth());
                roomInsideBanner.setHeight(data.getHeight());
                roomInsideBanner.setIcon(req.getSlang() == 1 ? data.getIcon() : data.getIcon_ar());
                roomInsideBanner.setRoomIcon(req.getSlang() == 1 ? data.getRoom_icon() : data.getRoom_icon_ar());
                roomInsideBanner.setUrl(getBannerUrl(url, req.getUid(), req.getSlang(), req.getOs(), req.getVersioncode(), req.getToken()));
                roomInsideBannerList.add(roomInsideBanner);
            }
        }
        vo.setRoomInsideBanners(roomInsideBannerList);
        List<VideoCallsBannerVO.RoomOutsideBanner> roomOutsideBannerList = new ArrayList<>();
        List<GameEntryConfigData> allGameEntry = gameEntryConfigDao.findAll();
        if (!CollectionUtils.isEmpty(allGameEntry)) {
            for (GameEntryConfigData gameEntry : allGameEntry) {
                roomOutsideBannerList.add(new VideoCallsBannerVO.RoomOutsideBanner(req.getSlang() == SLangType.ENGLISH ? gameEntry.getBanner() : gameEntry.getBannerAr(), gameEntry.getGameUrl()));
            }
        }
        vo.setRoomOutsideBannerList(roomOutsideBannerList);
        return vo;
    }


    /**
     * 检查房间是否有红包
     */
    private int checkLuckyBox(String roomId) {
        try {
            Long luckyBoxNum = clusterRedis.opsForHash().size("hash:luckyBoxInRoom_" + roomId);
            if (luckyBoxNum != 0) {
                return 1;
            }
            Long giftBoxNum = clusterRedis.opsForHash().size("hash:giftBoxInfo_" + roomId);
            if (giftBoxNum != 0) {
                return 1;
            }
        } catch (Exception e) {
            logger.error("checkLuckyBox error. roomId={} {}", roomId, e.getMessage(), e);
        }
        return 0;
    }

    private void fillLuckyNumConfig(RoomBannerVO vo, String uid, String roomId) {
        RoomConfigData roomConfig = roomConfigDao.findData(roomId);

        Map<String, Object> config = roomConfig.getRoom_config();
        int luckyNumAdmin = (int) config.getOrDefault("luckyNumAdmin", 0);

        RoomBannerVO.LuckyNumConfig luckyNumConfig = new RoomBannerVO.LuckyNumConfig();

        luckyNumConfig.setLuckyNumCost(WalletUtils.diamondsForDisplay((int) config.getOrDefault("luckyNumCost", 0)));
        luckyNumConfig.setLuckyNumRange((int) config.getOrDefault("luckyNumRange", 9));
        luckyNumConfig.setLuckyNumSwitch((int) config.getOrDefault("luckyNumSwitch", 0));
        luckyNumConfig.setLuckyNumData((int) config.getOrDefault("luckyNumData", 0));
        luckyNumConfig.setLuckyNumAdmin(luckyNumAdmin);

        int hostOrAdmin = hostOrAdminByChoice(uid, roomId);
        if ((hostOrAdmin == 1) || (luckyNumAdmin == 1 && hostOrAdminByChoice(uid, roomId) > 0)) {
            luckyNumConfig.setEnableSetting(1);
        } else {
            luckyNumConfig.setEnableSetting(0);
        }
        vo.setLuckyNumV2Config(luckyNumConfig);
    }


    /**
     * 0: 普通用户 1: 【房主】、 2: 管理员
     */
    private int hostOrAdminByChoice(String uid, String roomId) {
        int hostOrAdmin;
        hostOrAdmin = RoomUtils.isHomeowner(uid, roomId) ? 1 : 0;
        if (hostOrAdmin == 0) {
            hostOrAdmin = roomAdminRedis.isRoomAdmin(roomId, uid) ? 2 : 0;
        }
        return hostOrAdmin;
    }


    private String TruncateUrlPage(String url) {
        String strAllParam = null;
        url = url.trim();
        String[] arrSplit = url.split("[?]");
        if (url.length() > 1) {
            if (arrSplit.length > 1) {
                for (int i = 1; i < arrSplit.length; i++) {
                    strAllParam = arrSplit[i];
                }
            }
        }
        return strAllParam;
    }

    private Map<String, String> urlSplit(String url) {
        Map<String, String> mapRequest = new HashMap<>();
        String strUrlParam = TruncateUrlPage(url);
        if (strUrlParam == null) {
            return mapRequest;
        }
        String[] arrSplit = strUrlParam.split("[&]");
        for (String strSplit : arrSplit) {
            String[] arrSplitEqual = strSplit.split("[=]");
            if (arrSplitEqual.length > 1) {
                mapRequest.put(arrSplitEqual[0], arrSplitEqual[1]);
            } else {
                if (!"".equals(arrSplitEqual[0])) {
                    mapRequest.put(arrSplitEqual[0], "");
                }
            }
        }
        return mapRequest;
    }

    public String getGameId(String roomId) {
        // 值为gameId-gameType
        String gameInfo = clusterRedis.opsForValue().get("str:room_sud_game_" + roomId);
        if (ObjectUtils.isEmpty(gameInfo)) {
            return null;
        }
        String[] split = gameInfo.split("-");
        if (split.length == 2) {
            return split[0];
        }
        return null;
    }

    private int getGameStatus(String roomId) {
        // 值为gameId-gameType
        String gameInfo = clusterRedis.opsForValue().get("str:room_sud_game_" + roomId);
        if (ObjectUtils.isEmpty(gameInfo)) {
            return 0;
        }
        String[] split = gameInfo.split("-");
        if (split.length == 2) {
            return Integer.parseInt(split[1]);
        }
        return 0;
    }

    private int strToInt(String strValue) {
        if (ObjectUtils.isEmpty(strValue)) {
            return 0;
        }
        try {
            return Integer.parseInt(strValue);
        } catch (Exception e) {
            logger.error("string to integer error. value={}", strValue);
        }
        return 0;
    }

    private String getBannerUrl(String url, String uid, int slang, int os, int versioncode, String
            token) {
        if (ObjectUtils.isEmpty(url)) {
            return "";
        }
        UriComponentsBuilder urlBuilder;
        try {
            urlBuilder = UriComponentsBuilder.fromHttpUrl(url.trim());
        } catch (Exception e) {
            logger.error("getBannerUrl error. url={} {}", url, e.getMessage(), e);
            return "";
        }
        urlBuilder.queryParam("uid", uid);
        urlBuilder.queryParam("nlang", slang);
        urlBuilder.queryParam("os", os);
        urlBuilder.queryParam("versioncode", versioncode);
        urlBuilder.queryParam("token", token);
        return urlBuilder.build(false).encode().toUriString();
    }

    /**
     * 表情资源列表
     */
    @Cacheable(value = "getCheckpointConfigList", key = "#p0", cacheManager = CaffeineCacheConfig.EXPIRE_1M_AFTER_WRITE)
    public EmojiListVO getEmojiList(int slang) {
        EmojiListVO vo = new EmojiListVO();
        List<EmojiResourceData> emojiList = emojiResourceDao.findEmojiList();
        if (CollectionUtils.isEmpty(emojiList)) {
            vo.setList(Collections.emptyList());
            return vo;
        }
        List<EmojiListVO.EmojiVO> list = new ArrayList<>();
        for (EmojiResourceData emoji : emojiList) {
            if (emoji.getType() > 0) {
                continue;
            }
            EmojiListVO.EmojiVO emojiVO = new EmojiListVO.EmojiVO();
            BeanUtils.copyProperties(emoji, emojiVO);
            if (SLangType.ARABIC == slang) {
                emojiVO.setName(emoji.getNameAr());
            }
            list.add(emojiVO);
        }
        vo.setList(list);
        return vo;
    }

    public EmojiTabListVO getMsgEmojiList(String uid, int slang) {
        int svipLevel = svipLevelService.getSvipLevel(uid);
        return roomNewService.getMsgEmojiList(svipLevel, slang);
    }

    @Cacheable(value = "cache", cacheManager = CaffeineCacheConfig.EXPIRE_10M_AFTER_WRITE,
            key = "#root.method.name+T(org.springframework.util.StringUtils).arrayToDelimitedString(args,'-')")
    public EmojiTabListVO getMsgEmojiList(int svipLevel, int slang) {
        EmojiTabListVO vo = new EmojiTabListVO();
        List<EmojiResourceData> emojiList = emojiResourceDao.findEmojiList();
        if (CollectionUtils.isEmpty(emojiList)) {
            vo.setList(Collections.emptyList());
            return vo;
        }
        Map<Integer, List<EmojiResourceData>> typeMap = CollectionUtil.listToKeyListMap(emojiList, EmojiResourceData::getType);
        for (int i = 0; i < EMOJI_TAB_LIST.size(); i++) {
            EmojiTabListVO.EmojiTabVO tabVO = new EmojiTabListVO.EmojiTabVO();
            // type=0为默认分类标签
            tabVO.setSvip(i > 0 ? 1 : 0);
            tabVO.setCategoryIconUrl(EMOJI_TAB_LIST.get(i));
            vo.getList().add(tabVO);
            List<EmojiResourceData> list = typeMap.get(i);
            if (CollectionUtils.isEmpty(list)) {
                continue;
            }
            for (EmojiResourceData emoji : list) {
                if (svipLevel < emoji.getSvipLevel()) {
                    continue;
                }
                EmojiTabListVO.EmojiMsgPanel panelVO = new EmojiTabListVO.EmojiMsgPanel();
                BeanUtils.copyProperties(emoji, panelVO);
                panelVO.setIconFile(emoji.getIcon_file());
                panelVO.setName(slang == SLangType.ARABIC ? emoji.getNameAr() : emoji.getName());
                tabVO.getEmojiPanel().add(panelVO);
            }
        }
        return vo;
    }

    /**
     * 房间内发送图片
     */
    public RoomSendPicVO sendPicture(RoomSendPicDTO req) {
        if (whitelistRedis.isInvisibilityEnterRoom(req.getRoomId(), req.getUid())) {
            // 隐身进房无法操作
            throw new CommonException(RoomHttpCode.INVISIBLE_ACCOUNT_CANNOT_BE_OPERATED);
        }
        // 是否被禁止发公屏消息
        if (blockRedis.getBlockUserPublicMsgStatus(req.getUid()) != 0) {
            logger.info("actor BlockUserPublicMsg uid={} roomId={}", req.getUid(), req.getRoomId());
            throw new CommonException(RoomHttpCode.ACCOUNT_HAS_DISABLED);
        }
        RoomSendPicVO vo = new RoomSendPicVO();
        String uid = req.getUid();
        String roomId = req.getRoom_id();
        String url = ImageUrlGenerator.createCdnUrl(req.getImg());
        ImageUrlData data = new ImageUrlData();
        data.setUrl(url);
        data.setWidth(200);
        data.setHeight(300);
        data.setFit("lfit");
        data.setGifToImg(false);
        String thumbnail = ImageUrlGenerator.generateUrl(data);
        data.setGifToImg(true);
        String thumbnailStaticUrl = ImageUrlGenerator.generateUrl(data);
        asyncSendPic(url, uid, roomId, thumbnail, thumbnailStaticUrl, req.getWidth(), req.getHeight());
        vo.setUrl(url);
        vo.setThumbnailUrl(thumbnail);
        vo.setThumbnailStaticUrl(thumbnailStaticUrl);
//        logger.info("uid={} vo={}", uid, vo);
        return vo;
    }

    private void asyncSendPic(String url, String uid, String roomId, String thumbnail, String thumbnail2, int width,
                              int height) {
        BaseTaskFactory.getFactory().addSlow(new Task() {
            @Override
            protected void execute() {
                try {
                    boolean isSafe = detectService.detectImage(new ImageDTO(url, DetectOriginConstant.SCREEN_PICTURE, uid)).getData().getIsSafe() == 1;
                    if (isSafe) {
                        sendPictureInRoom(url, uid, roomId, thumbnail, thumbnail2, width, height);
                    }
                } catch (Exception e) {
                    sendPictureInRoom(url, uid, roomId, thumbnail, thumbnail2, width, height);
                }
            }
        });
    }

    /**
     * 在房间内发送图片
     */
    private void sendPictureInRoom(String url, String uid, String roomId, String thumbnail, String thumbnail2,
                                   int width, int height) {
        if (ObjectUtils.isEmpty(uid) || ObjectUtils.isEmpty(roomId)) {
            return;
        }
        SendPicPushMsg msg = new SendPicPushMsg();
        UNameObject uNameObject = generateUNameObject(uid, roomId);
        msg.setName(uNameObject);
        msg.setUrl(url);
        msg.setHeight(height);
        msg.setWidth(width);
        msg.setThumbnailUrl(thumbnail);
        msg.setThumbnailStaticUrl(thumbnail2);
        msg.setImageType(0);
        String suffix = url.substring(url.lastIndexOf(".") + 1);
        if ("gif".equals(suffix) || "webp".equals(suffix)) {
            msg.setImageType(1);
        }
        msg.setPkTeam(1);
        roomWebSender.sendRoomWebMsg(roomId, uid, msg, false);

        // 房间消息事件埋点
        RoomMsgNewEvent event = new RoomMsgNewEvent();
        int userCount = roomPlayerRedis.getRoomActorsCount(roomId);
        ActorData userData = actorDao.getActorDataFromCache(uid);
        event.setUid(uid);
        event.setRoom_id(roomId);
        event.setMsg_type(3);
        event.setSend_user_count(userCount);
        event.setRoom_user_count(userCount);
        event.setMsg_id(String.valueOf(msg.getMsgId()));
        event.setFrom_os(userData.getIntOs());
        event.setFrom_uid(uid);
        event.setCtime(DateHelper.getNowSeconds());
        eventReport.track(new EventDTO(event));
    }

    /**
     * 构建UNameObject
     */
    private UNameObject generateUNameObject(String uid, String roomId) {
        RoomActorDetailData detailData = roomActorCache.getData(roomId, uid, false);
        if (detailData == null) {
            logger.error("can not find room actor data. roomId={} uid={}", roomId, uid);
            return null;
        }
        return detailData.toUNameObject();
    }

    /**
     * 房间内禁言
     */
    public void banMsg(BanMsgDTO req) {
        if (req.getComp() != 0) {
            // show room 直接返回
            return;
        }
        String uid = req.getUid();
        String aid = req.getAid();
        String roomId = req.getRoomId();
        if (ObjectUtils.isEmpty(uid) || ObjectUtils.isEmpty(aid) || ObjectUtils.isEmpty(roomId)) {
            logger.info("cancel ban msg param error. uid={} aid={} roomId={}", uid, aid, roomId);
            throw new CommonException(RoomHttpCode.PARAM_ERROR);
        }
        if (whitelistRedis.isInvisibilityEnterRoom(req.getRoomId(), req.getUid())) {
            // 隐身进房无法操作
            throw new CommonException(RoomHttpCode.INVISIBLE_ACCOUNT_CANNOT_BE_OPERATED);
        }
        boolean official = RoomKickService.POWER_USER.contains(uid);
        if (!official && svipLevelService.getSvipLevel(aid) >= 5) {
            throw new CommonException(RoomHttpCode.MUTE_SVIP_LIMIT);
        }
        int uType = memberDao.findMemberUtype(roomId, uid);
        int aType = memberDao.findMemberUtype(roomId, aid);
        if (!official && !RoomUtils.isHomeowner(uid, roomId) && aType >= uType) {
            logger.info("You can not forbid this user talk. uid={} aid={} roomId={}", uid, aid, roomId);
            throw new CommonException(RoomHttpCode.NOT_FORBID_USER_TALK);
        }
        if (micFrameRedis.getForbidTime(roomId, aid) > 0) {
            logger.info("Already forbid this user to talk. uid={} aid={} roomId={}", uid, aid, roomId);
            throw new CommonException(RoomHttpCode.ALREADY_FORBID_USER_TALK);
        }
        micFrameRedis.setForbidTime(roomId, aid);
        sendOptBanTxtPushMsg(uid, aid, roomId, true, official);
    }

    /**
     * 房间内取消禁言
     */
    public void cancelBanMsg(BanMsgDTO req) {
        if (req.getComp() != 0) {
            // show room 直接返回
            return;
        }
        String uid = req.getUid();
        String aid = req.getAid();
        String roomId = req.getRoomId();
        if (ObjectUtils.isEmpty(uid) || ObjectUtils.isEmpty(aid) || ObjectUtils.isEmpty(roomId)) {
            logger.error("cancel ban msg param error. uid={} aid={} roomId={}", uid, aid, roomId);
            throw new CommonException(RoomHttpCode.PARAM_ERROR);
        }
        if (whitelistRedis.isInvisibilityEnterRoom(req.getRoomId(), req.getUid())) {
            // 隐身进房无法操作
            throw new CommonException(RoomHttpCode.INVISIBLE_ACCOUNT_CANNOT_BE_OPERATED);
        }
        int uType = memberDao.findMemberUtype(roomId, uid);
        int aType = memberDao.findMemberUtype(roomId, aid);
        boolean isOwner = RoomUtils.isHomeowner(uid, roomId);
        if (!isOwner) {
            if (uType == 0) {
                logger.info("Only Owner or Admin  can cancel ban on text. uid={} aid={} roomId={}", uid, aid, roomId);
                throw new CommonException(RoomHttpCode.NO_PERMISSION_CANCEL_BAN_TEXT);
            }
            if (aType >= uType) {
                logger.info("You can not cancel forbid this user talk. uid={} aid={} roomId={}", uid, aid, roomId);
                throw new CommonException(RoomHttpCode.NOT_CANCEL_FORBID_USER_TALK);
            }
        }
        micFrameRedis.removeForbidTime(roomId, aid);
        sendOptBanTxtPushMsg(uid, aid, roomId, false, false);
    }

    /**
     * 发送禁言操作消息
     */
    private void sendOptBanTxtPushMsg(String uid, String aid, String roomId, boolean ban, boolean official) {
        BaseTaskFactory.getFactory().addSlow(new Task() {
            @Override
            protected void execute() {
                OptBanTxtPushMsg msg = new OptBanTxtPushMsg();
                UserInfoObject userInfo = buildUserInfoObject(uid, roomId);
                JSONObject jsonObject = new JSONObject();
                jsonObject.put("from_name", userInfo.getName());
                jsonObject.put("from_img", userInfo.getHead());
                jsonObject.put("opt_user", userInfo);
                msg.fillFrom(jsonObject, ban);
                msg.setOfficial(official ? 1 : 0);
                roomWebSender.sendPlayerWebMsg(roomId, uid, aid, msg, true);
            }
        });
    }

    /**
     * 发送麦位操作消息
     */
    private void sendMuteMicPushMsg(String uid, String aid, String roomId, int opt) {
        BaseTaskFactory.getFactory().addSlow(new Task() {
            @Override
            protected void execute() {
                MuteMicPushMsg msg = new MuteMicPushMsg();
                UserInfoObject userInfo = buildUserInfoObject(uid, roomId);
                JSONObject jsonObject = new JSONObject();
                jsonObject.put("from_name", userInfo.getName());
                jsonObject.put("from_img", userInfo.getHead());
                jsonObject.put("opt_user", userInfo);
                jsonObject.put("opt", opt);
                jsonObject.put("to_uid", aid);
                msg.fillFrom(jsonObject);
                logger.info("MuteMicPushMsg={}", JSONObject.toJSONString(msg));
                roomWebSender.sendPlayerWebMsg(roomId, uid, aid, msg, true);
            }
        });
    }

    /**
     * 构建UserInfoObject
     */
    private UserInfoObject buildUserInfoObject(String uid, String roomId) {
        UserInfoObject userInfo = new UserInfoObject();
        RoomActorDetailData detailData = roomActorCache.getData(roomId, uid, false);
        userInfo.setUid(uid);
        if (detailData != null) {
            userInfo.setName(detailData.getName());
            userInfo.setHead(detailData.getHead());
        }
        return userInfo;
    }

    /**
     * 所有麦位静音时房主调用
     */
    public void allMicDown(BanMsgDTO req) {
        if (!isOwnerOrViceOwner(req.getUid(), req.getRoomId())) {
            logger.info("only room owner or vice owner can forbid all mic. uid={} roomId={}", req.getUid(), req.getRoomId());
            throw new CommonException(RoomHttpCode.NO_PERMISSION_FORBID_ALL_MIC);
        }
        if (req.getOpt() == 1) {
            roomMicRedis.setAllMicDown(req.getRoomId());
        } else {
            roomMicRedis.deleteAllMicDown(req.getRoomId());
        }
    }

    /**
     * 判断是否是房主或副房主
     */
    private Boolean isOwnerOrViceOwner(String uid, String roomId) {
        if (RoomUtils.isHomeowner(uid, roomId)) {
            return true;
        }
        return roomAdminRedis.isRoomAdmin(roomId, uid);
    }

    /**
     * 幸运数字启动
     */
    public DiceGameVO startLuckyNumber(RoomDTO req) {
        DiceGameVO vo = new DiceGameVO();
        String uid = req.getUid();
        String roomId = req.getRoomId();
        if (ObjectUtils.isEmpty(uid) || ObjectUtils.isEmpty(roomId)) {
            logger.error("start lucky number param error. uid={} roomId={}", req.getUid(), req.getRoomId());
            throw new CommonException(RoomHttpCode.PARAM_ERROR);
        }
        if (whitelistRedis.isInvisibilityEnterRoom(req.getRoomId(), req.getUid())) {
            // 隐身进房无法操作
            throw new CommonException(RoomHttpCode.INVISIBLE_ACCOUNT_CANNOT_BE_OPERATED);
        }
        Random random = new Random();
        int randomInt = random.nextInt(100);
        EnterRoomCheckData data = roomRedis.getEnterRoomValue(uid);
        if (RoomUtils.isHomeowner(uid, roomId) || (data != null && roomId.equals(data.getRoomId()))) {
            sendLuckyNumberPushMsg(uid, roomId, randomInt, false, String.valueOf(randomInt));
        } else {
            logger.info("is_really_enter_room false not broadcast uid={} roomId={}", uid, roomId);
        }
        vo.setRand_int(randomInt);
        vo.setBeans(actorDao.getIntBalance(uid));
        return vo;
    }

    /**
     * 骰子游戏
     */
    public DiceGameVO startDiceGame(RoomDTO req) {
        DiceGameVO vo = new DiceGameVO();
        String uid = req.getUid();
        String roomId = req.getRoomId();
        if (ObjectUtils.isEmpty(uid) || ObjectUtils.isEmpty(roomId)) {
            logger.error("start dice game param error. uid={} roomId={}", req.getUid(), req.getRoomId());
            throw new CommonException(RoomHttpCode.PARAM_ERROR);
        }
        if (whitelistRedis.isInvisibilityEnterRoom(req.getRoomId(), req.getUid())) {
            // 隐身进房无法操作
            throw new CommonException(RoomHttpCode.INVISIBLE_ACCOUNT_CANNOT_BE_OPERATED);
        }
        Random random = new Random();
        int randomInt = random.nextInt(6) + 1;
        EnterRoomCheckData data = roomRedis.getEnterRoomValue(uid);
        if (RoomUtils.isHomeowner(uid, roomId) || (data != null && roomId.equals(data.getRoomId()))) {
            sendDicePushMsg(uid, roomId, randomInt);
        } else {
            logger.info("is_really_enter_room false not broadcast uid={} roomId={}", uid, roomId);
        }
        vo.setRand_int(randomInt);
        vo.setBeans(actorDao.getIntBalance(uid));
        return vo;
    }

    private void sendDicePushMsg(String uid, String roomId, int num) {
        BaseTaskFactory.getFactory().addSlow(new Task() {
            @Override
            protected void execute() {
                DicePushMsg msg = new DicePushMsg();
                msg.setUname(generateUNameObject(uid, roomId));
                msg.setNum(num);
                roomWebSender.sendRoomWebMsg(roomId, uid, msg, true);
            }
        });
    }

    private void sendLuckyNumberPushMsg(String uid, String roomId, int num, boolean match, String formatLuckyNum) {
        BaseTaskFactory.getFactory().addSlow(new Task() {
            @Override
            protected void execute() {
                LuckyNumberPushMsg msg = new LuckyNumberPushMsg();
                msg.setUname(generateUNameObject(uid, roomId));
                msg.setNum(num);
                msg.setRet(match ? 1 : 0);
                msg.setLuckyNum(formatLuckyNum);
                roomWebSender.sendRoomWebMsg(roomId, uid, msg, true);
            }
        });
    }

    /**
     * 幸运数字V2版本
     */

    public LuckyNumConfigVO luckyNumConfig(RoomDTO req) {
        String uid = req.getUid();
        String roomId = req.getRoomId();
        if (ObjectUtils.isEmpty(uid) || ObjectUtils.isEmpty(roomId)) {
            logger.error("luckyNumSetting param error. uid={} roomId={}", req.getUid(), req.getRoomId());
            throw new CommonException(RoomHttpCode.PARAM_ERROR);
        }

        MongoRoomData mongoRoomData = roomDao.findData(roomId);
        if (mongoRoomData == null) {
            logger.error("mongoRoomData data not find. uid={} roomId={}", req.getUid(), req.getRoomId());
            throw new CommonException(RoomHttpCode.PARAM_ERROR);
        }

        LuckyNumConfigVO vo = new LuckyNumConfigVO();
        vo.setCostNumList(LUCKY_NUM_COST_LIST_NEW);
        vo.setRangeNumList(LUCKY_NUM_RANGE_LIST);

        RoomConfigData roomConfig = roomConfigDao.findData(roomId);

        if (null == roomConfig) {
            roomConfig = roomConfigDao.initRoomConfigData(roomId);
        }

        Map<String, Object> config = roomConfig.getRoom_config();
        vo.setLuckyNumCost(WalletUtils.diamondsForDisplay((int) config.getOrDefault("luckyNumCost", 0)));
        vo.setLuckyNumRange((int) config.getOrDefault("luckyNumRange", 9));
        vo.setLuckyNumSwitch((int) config.getOrDefault("luckyNumSwitch", 0));
        vo.setLuckyNumData((int) config.getOrDefault("luckyNumData", 0));
        vo.setLuckyNumAdmin((int) config.getOrDefault("luckyNumAdmin", 0));

        return vo;
    }

    private void userSetLuckyNumberEvent(String uid, String roomId, int luckyNumCost, int luckyNumRange,
                                         int luckyNumSwitch, int luckyNumData, int luckyNumAdmin) {
        LuckyNumberEvent event = new LuckyNumberEvent();
        event.setUid(uid);
        event.setRoom_id(roomId);
        event.setLucky_number_action(3);
        event.setLucky_number_price(luckyNumCost);
        event.setLucky_number_range(luckyNumRange);
        event.setIs_allow_admin(luckyNumAdmin);
        event.setCtime(DateHelper.getNowSeconds());
        if (luckyNumSwitch == 1) {
            event.setLucky_number_set(luckyNumData);
        }

        eventReport.track(new EventDTO(event));
    }


    public void luckyNumSetting(LuckyNumSettingDTO req) {
        String uid = req.getUid();
        String roomId = req.getRoomId();
        int luckyNumCostReq = WalletUtils.diamondsToRaw(req.getLuckyNumCost());
        int luckyNumRangeReq = req.getLuckyNumRange();
        int luckyNumSwitchReq = req.getLuckyNumSwitch();
        int luckyNumDataReq = req.getLuckyNumData();
        int luckyNumAdminReq = req.getLuckyNumAdmin();
        if (ObjectUtils.isEmpty(uid) || ObjectUtils.isEmpty(roomId)) {
            logger.error("luckyNumSetting param error. uid={} roomId={}", req.getUid(), req.getRoomId());
            throw new CommonException(RoomHttpCode.PARAM_ERROR);
        }
        if (whitelistRedis.isInvisibilityEnterRoom(req.getRoomId(), req.getUid())) {
            // 隐身进房无法操作
            throw new CommonException(RoomHttpCode.INVISIBLE_ACCOUNT_CANNOT_BE_OPERATED);
        }
        if (!LUCKY_NUM_COST_LIST_NEW.contains(WalletUtils.diamondsForDisplay(luckyNumCostReq))) {
            logger.error("costNum param error. uid={} roomId={} costNum={}", req.getUid(), req.getRoomId(), luckyNumCostReq);
            throw new CommonException(RoomHttpCode.PARAM_ERROR);
        }

        if (!LUCKY_NUM_RANGE_LIST.contains(luckyNumRangeReq)) {
            logger.error("rangeNum param error. uid={} roomId={} rangeNum={}", req.getUid(), req.getRoomId(), luckyNumRangeReq);
            throw new CommonException(RoomHttpCode.PARAM_ERROR);
        }

        if (!LUCKY_NUM_SWITCH.contains(luckyNumSwitchReq) || !LUCKY_NUM_SWITCH.contains(luckyNumAdminReq)) {
            logger.error("luckyNumSwitchReq or luckyNumAdminReq param error. uid={} roomId={} luckyNumSwitchReq={}, " +
                    "luckyNumAdminReq={}", req.getUid(), req.getRoomId(), luckyNumSwitchReq, luckyNumAdminReq);
            throw new CommonException(RoomHttpCode.PARAM_ERROR);
        }

        if (luckyNumSwitchReq == 1 && (luckyNumDataReq < 0 || luckyNumDataReq > luckyNumRangeReq)) {
            logger.error("luckyNumData param error. uid={} roomId={} luckyNumData={}, rangeNum={}", req.getUid(), req.getRoomId(), luckyNumDataReq, luckyNumRangeReq);
            throw new CommonException(RoomHttpCode.PARAM_ERROR);
        }


        ActorData actorData = actorDao.getActorDataFromCache(uid);
        if (actorData == null) {
            logger.error("actor data not find. uid={} roomId={}", req.getUid(), req.getRoomId());
            throw new CommonException(RoomHttpCode.PARAM_ERROR);
        }

        RoomConfigData roomConfig = roomConfigDao.findData(roomId);
        int hostOrAdmin = hostOrAdminByChoice(uid, roomId);
        if (hostOrAdmin <= 0) {
            logger.info("hostOrAdmin1 limit uid={} roomId={}  hostOrAdmin={}", req.getUid(), req.getRoomId(), hostOrAdmin);
            throw new CommonException(RoomHttpCode.AUTH_ERROR);
        }

        Map<String, Object> config = roomConfig.getRoom_config();
        int luckyNumAdmin = (int) config.getOrDefault("luckyNumAdmin", 0);
        if (hostOrAdmin == 2 && luckyNumAdmin == 0) {
            logger.info("hostOrAdmin2 limit uid={} roomId={}  hostOrAdmin={}", req.getUid(), req.getRoomId(), hostOrAdmin);
            throw new CommonException(RoomHttpCode.AUTH_ERROR);
        }

        boolean pushStatus = false;
        int luckyNumCost = (int) config.getOrDefault("luckyNumCost", 0);
        int luckyNumRange = (int) config.getOrDefault("luckyNumRange", 9);
        int luckyNumSwitch = (int) config.getOrDefault("luckyNumSwitch", 0);
        int luckyNumData = (int) config.getOrDefault("luckyNumData", 0);

        if (luckyNumCost != luckyNumCostReq || luckyNumRangeReq != luckyNumRange ||
                luckyNumSwitch != luckyNumSwitchReq || luckyNumData != luckyNumDataReq ||
                luckyNumAdmin != luckyNumAdminReq) {
            pushStatus = true;
        }


        luckyNumDataReq = luckyNumSwitchReq == 1 ? luckyNumDataReq : 0;
        Map<String, Object> updateMap = new HashMap<>();
        updateMap.put("luckyNumCost", luckyNumCostReq);
        updateMap.put("luckyNumRange", luckyNumRangeReq);
        updateMap.put("luckyNumSwitch", luckyNumSwitchReq);
        updateMap.put("luckyNumData", luckyNumDataReq);
        updateMap.put("luckyNumAdmin", luckyNumAdminReq);
        roomConfigDao.updateRoomConfigByMap(roomId, updateMap);

        luckyNumRedis.deleteLuckyNumSize(roomId);
        userSetLuckyNumberEvent(uid, roomId, luckyNumCostReq, luckyNumRangeReq, luckyNumSwitchReq, luckyNumDataReq, luckyNumAdminReq);

        if (pushStatus) {
            // 推送设置消息
            LuckyNumRoomChangeMsg changeMsg = new LuckyNumRoomChangeMsg();
            changeMsg.setNum(luckyNumDataReq);
            changeMsg.setLuckyNumCost(WalletUtils.diamondsForDisplay(luckyNumCostReq));
            changeMsg.setLuckyNumSwitch(luckyNumSwitchReq);
            changeMsg.setLuckyNumAdmin(luckyNumAdminReq);
            changeMsg.setLuckyNumRange(luckyNumRangeReq);
            UserInfoObject userInfoObject = new UserInfoObject();
            userInfoObject.setUid(uid);
            userInfoObject.setName(actorData.getName());
            userInfoObject.setHead(actorData.getHead());
            userInfoObject.setViceHost(hostOrAdmin);
            changeMsg.setOpt_user(userInfoObject);
            roomWebSender.sendRoomWebMsg(roomId, uid, changeMsg, true);
        }

    }

    private void deductLuckyNumDiamonds(String uid, int changed) {
        MoneyDetailReq moneyDetailReq = new MoneyDetailReq();
        moneyDetailReq.setRandomId();
        moneyDetailReq.setUid(uid);
        moneyDetailReq.setAtype(210);
        moneyDetailReq.setChanged(-changed);
        moneyDetailReq.setTitle("lucky number");
        moneyDetailReq.setDesc("send lucky number");
        ApiResult<String> result = dataCenterService.reduceBeans(moneyDetailReq);

        if (result.isError()) {
            if (1 == result.getCode().getCode()) {
                throw new CommonException(RoomHttpCode.UNION_NOT_ENOUGH_DIAMOND);
            }
            logger.error("reduce beans error, uid={} msg={}", uid, result.getCode().getMsg());
            throw new CommonException(HttpCode.SERVER_ERROR);
        }
    }

    private void initLuckyNumSize(String roomId, int luckyNumRange, int luckyNumData) {
        List<Integer> initList = IntStream.rangeClosed(0, luckyNumRange).boxed().collect(Collectors.toList());
        initList.remove(Integer.valueOf(luckyNumData));
        Collections.shuffle(initList);
        List<Integer> pushList;
        if (luckyNumRange == 99) {
            pushList = initList.subList(0, 49);
        } else {
            pushList = initList.subList(0, 79);
        }
        pushList.add(luckyNumData);
        Collections.shuffle(pushList);

        List<String> indexStrList = pushList.stream().map(String::valueOf).collect(Collectors.toList());
        luckyNumRedis.initLuckyNumSize(roomId, indexStrList);
    }

    private int getLuckyNumByRule(String roomId, int luckyNumRange, int luckyNumSwitch, int luckyNumData) {

        int randomInt;
        Random random = new Random();
        if (luckyNumRange <= LUCKY_NUM_RANGE_LIST.get(0) || luckyNumSwitch == 0) {
            randomInt = random.nextInt(luckyNumRange + 1);
        } else {
            if (luckyNumRedis.getLuckyNumSize(roomId) <= 5) {
                initLuckyNumSize(roomId, luckyNumRange, luckyNumData);
            }

            String luckyNum = luckyNumRedis.drawLuckyNumKey(roomId);
            randomInt = Integer.parseInt(luckyNum);
        }

        return randomInt;
    }

    private String getFormatLuckyNum(int num, int luckyNumRange) {
        String STR_FORMAT = "0";
        if (luckyNumRange == LUCKY_NUM_RANGE_LIST.get(1)) {
            STR_FORMAT = "00";
        } else if (luckyNumRange == LUCKY_NUM_RANGE_LIST.get(2)) {
            STR_FORMAT = "000";
        }
        DecimalFormat df = new DecimalFormat(STR_FORMAT);
        return df.format(num);
    }

    private void saveLuckyNumberRecord(String uid, String roomId, int num) {
        BaseTaskFactory.getFactory().addSlow(new Task() {
            @Override
            protected void execute() {
                LuckyNumData luckyNumObj = new LuckyNumData();
                luckyNumObj.setLuckyNum(num);
                luckyNumObj.setRoomId(roomId);
                luckyNumObj.setUid(uid);
                luckyNumObj.setCtime(DateHelper.getNowSeconds());
                luckyNumDao.insert(luckyNumObj);
            }
        });
    }


    public LuckyNumVO sendLuckyNum(RoomDTO req) {
        LuckyNumVO vo = new LuckyNumVO();
        String uid = req.getUid();
        String roomId = req.getRoomId();
        if (ObjectUtils.isEmpty(uid) || ObjectUtils.isEmpty(roomId)) {
            logger.error("sendLuckyNum param error. uid={} roomId={}", req.getUid(), req.getRoomId());
            throw new CommonException(RoomHttpCode.PARAM_ERROR);
        }
        if (whitelistRedis.isInvisibilityEnterRoom(req.getRoomId(), req.getUid())) {
            // 隐身进房无法操作
            throw new CommonException(RoomHttpCode.INVISIBLE_ACCOUNT_CANNOT_BE_OPERATED);
        }
        RoomConfigData roomConfig = roomConfigDao.findData(roomId);
        Map<String, Object> config = roomConfig.getRoom_config();
        int luckyNumCost = (int) config.getOrDefault("luckyNumCost", 0);
        if (luckyNumCost > 0) {
            ActorData actorData = actorDao.getActorData(uid);
            if (actorData.getVirtualDiamonds() >= luckyNumCost) {
                // 如果账号有虚拟钻石，在虚拟钻石可使用的功能或场景下优先使用虚拟钻石
                ApiResult<String> result = dataCenterService.changeVirtualDiamond(req.getUid(), -luckyNumCost, "lucky number", "send lucky number");
                if (!result.isOk() && result.getCode().getCode() == 1) {
                    logger.info("reduceVirtualBeans insufficient balance. uid={} change={}", req.getUid(), -luckyNumCost);
                    throw new CommonException(RoomHttpCode.UNION_NOT_ENOUGH_DIAMOND);
                }
            } else {
                deductLuckyNumDiamonds(uid, luckyNumCost);
            }
        }

        int luckyNumRange = (int) config.getOrDefault("luckyNumRange", 9);
        int luckyNumSwitch = (int) config.getOrDefault("luckyNumSwitch", 0);
        int luckyNumData = (int) config.getOrDefault("luckyNumData", 0);
        int randomInt = getLuckyNumByRule(roomId, luckyNumRange, luckyNumSwitch, luckyNumData);
        String formatLuckyNum = getFormatLuckyNum(randomInt, luckyNumRange);

        boolean match = luckyNumSwitch == 1 && randomInt == luckyNumData;

        EnterRoomCheckData data = roomRedis.getEnterRoomValue(uid);
        if (RoomUtils.isHomeowner(uid, roomId) || (data != null && roomId.equals(data.getRoomId()))) {

            sendLuckyNumberPushMsg(uid, roomId, randomInt, match, formatLuckyNum);
        } else {
            logger.info("is_really_enter_room false not broadcast uid={} roomId={}", uid, roomId);
        }

        if (match) {
            saveLuckyNumberRecord(uid, roomId, randomInt);
        }
        vo.setLuckyNum(formatLuckyNum);
        vo.setBeans(actorDao.getIntBalance(uid));
        vo.setMatch(match);
        return vo;
    }


    public LuckyNumRecordVO luckyNumRecord(String roomId, int page) {
        LuckyNumRecordVO recordVO = new LuckyNumRecordVO();
        int page_size = 20;
        if (page < 1) {
            page = 1;
        }


        int currentTime = DateHelper.getNowSeconds();
        int startTime = currentTime - 3 * 24 * 3600;
        List<LuckyNumData> dataList = luckyNumDao.getRecords(roomId, page, page_size, startTime, currentTime);

        List<LuckyNumRecordVO.LuckyNumRecord> recordList = new ArrayList<>();
        for (LuckyNumData data : dataList) {
            LuckyNumRecordVO.LuckyNumRecord luckyNumRecord = new LuckyNumRecordVO.LuckyNumRecord();
            luckyNumRecord.setLuckyNum(data.getLuckyNum());
            luckyNumRecord.setUid(data.getUid());
            ActorData actorData = actorDao.getActorDataFromCache(data.getUid());
            luckyNumRecord.setName(actorData.getName());
            luckyNumRecord.setHead(ImageUrlGenerator.generateRoomUserUrl(actorData));
            luckyNumRecord.setCtime(data.getCtime());
            recordList.add(luckyNumRecord);
        }

        recordVO.setList(recordList);
        if (dataList.size() < page_size) {
            recordVO.setNextUrl("");
        } else {
            recordVO.setNextUrl(String.valueOf(page + 1));
        }
        return recordVO;
    }
}
