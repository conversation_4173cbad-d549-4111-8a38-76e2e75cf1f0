package com.quhong.service;

import com.alibaba.fastjson.JSON;
import com.quhong.api.MsgService;
import com.quhong.config.AsyncConfig;
import com.quhong.core.config.ServerConfig;
import com.quhong.data.dto.MsgSendDTO;
import com.quhong.data.dto.SendFcmDTO;
import com.quhong.data.vo.SendMsgVO;
import com.quhong.enums.HttpCode;
import com.quhong.enums.MsgHttpCode;
import com.quhong.enums.SLangType;
import com.quhong.exception.CommonException;
import com.quhong.exception.CommonH5Exception;
import com.quhong.fcm.FCMPusher;
import com.quhong.managers.SendChatMsgProcessor;
import com.quhong.mongo.dao.ActorDao;
import com.quhong.mongo.data.MongoActorData;
import com.quhong.redis.PlayerStatusRedis;
import jakarta.annotation.Resource;
import org.apache.dubbo.config.annotation.DubboService;
import org.apache.dubbo.rpc.RpcContext;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.util.ObjectUtils;

import java.util.HashSet;
import java.util.List;
import java.util.Set;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.Executor;

@DubboService(timeout = 3000)
public class MsgServiceImpl implements MsgService {

    private static final Logger logger = LoggerFactory.getLogger(MsgServiceImpl.class);

    @Resource
    private SendChatMsgProcessor sendChatMsgProcessor;
    @Resource
    private FCMPusher fcmPusher;
    @Resource
    private ActorDao actorDao;
    @Resource
    private PlayerStatusRedis playerStatusRedis;
    @Resource(name = AsyncConfig.ASYNC_TASK)
    private Executor executor;

    public SendMsgVO sendMsg(MsgSendDTO reqData) {
        logger.info("dubbo send msg... data={}", JSON.toJSONString(reqData));
        return sendChatMsgProcessor.doSendMsg(reqData);
    }

    @Override
    public String test(String param) throws CommonException {
        logger.info("test param={} remoteHost={}", param, RpcContext.getServiceContext().getRemoteHost());
        if ("error".equals(param)) {
            throw new CommonException(HttpCode.AUTH_ERROR);
        } else if ("h5error".equals(param)) {
            throw new CommonH5Exception(HttpCode.UPDATE_APP);
        } else if ("oerror".equals(param)) {
            throw new CommonException(MsgHttpCode.VIP_LEVEL_IS_NOT_HIGH_ENOUGH_TO_SEND);
        } else if (param.startsWith("timeout")) {
            try {
                Thread.sleep(Long.parseLong(param.replace("timeout", "")));
                return param + "-" + System.currentTimeMillis();
            } catch (InterruptedException e) {
                throw new CommonException(HttpCode.SERVER_ERROR);
            }
        }
        return "hello, " + param +
                ", response from provider: " + RpcContext.getServerContext().getLocalAddress() +
                ", client: " + RpcContext.getServerContext().getRemoteHost() +
                ", local: " + RpcContext.getServerContext().getUrl().getParameter("application") +
                ", remote: " + RpcContext.getServerContext().getRemoteApplicationName() +
                ", isProviderSide: " + RpcContext.getServerContext().isProviderSide() +
                ", protocol: " + RpcContext.getServerContext().getProtocol() +
                ", time: " + System.currentTimeMillis();
    }

    @Override
    public CompletableFuture<Void> sendFcm(SendFcmDTO dto) {
        return CompletableFuture.runAsync(() -> {
            if (null != dto.getToUidSet()) {
                batchSend(dto);
            } else {
                fcmPusher.pushSingle(dto.getToUid(), dto.getParamMap(), dto.getTitle(), dto.getBody(), dto.getImg());
            }
        }, executor);
    }

    public void batchSend(SendFcmDTO dto) {
        long startTimeMillis = System.currentTimeMillis();
        Set<String> enBatch = new HashSet<>();
        Set<String> arBatch = new HashSet<>();
        List<MongoActorData> actors = actorDao.getActors(dto.getToUidSet());
        for (MongoActorData actor : actors) {
            if (null != actor.getPush()
                    && actor.getPush().containsKey("token")
                    && (int) actor.getPush().getOrDefault("private_message", 1) == 1) {
                if (ServerConfig.isProduct() && playerStatusRedis.getPlayerStatus(actor.get_id().toString()) > 0) {
                    continue;
                }
                String token = (String) actor.getPush().get("token");
                if (ObjectUtils.isEmpty(token)) {
                    continue;
                }
                if (SLangType.ENGLISH == actor.getSlang()) {
                    enBatch.add(token);
                } else {
                    arBatch.add(token);
                }
                if (enBatch.size() == FCMPusher.FCM_BATCH_SEND_MAX) {
                    fcmPusher.pushBatch(enBatch, dto.getParamMap(), dto.getTitle(), dto.getBody(), dto.getImg());
                    enBatch = new HashSet<>();
                }
                if (arBatch.size() == FCMPusher.FCM_BATCH_SEND_MAX) {
                    fcmPusher.pushBatch(arBatch, dto.getParamMap(), dto.getTitleAr(), dto.getBodyAr(), dto.getImg());
                    arBatch = new HashSet<>();
                }
            }
        }
        // 处理最后一批剩余的元素
        if (!enBatch.isEmpty()) {
            fcmPusher.pushBatch(enBatch, dto.getParamMap(), dto.getTitle(), dto.getBody(), dto.getImg());
        }
        if (!arBatch.isEmpty()) {
            fcmPusher.pushBatch(arBatch, dto.getParamMap(), dto.getTitleAr(), dto.getBodyAr(), dto.getImg());
        }
        logger.info("finish batchSend. total={} cost={}", actors.size(), System.currentTimeMillis() - startTimeMillis);
    }
}
