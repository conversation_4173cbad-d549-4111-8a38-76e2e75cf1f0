package com.quhong.service;

import com.quhong.config.CaffeineCacheConfig;
import com.quhong.constant.MatchConstant;
import com.quhong.constant.RoomListConstant;
import com.quhong.core.concurrency.BaseTaskFactory;
import com.quhong.core.concurrency.tasks.Task;
import com.quhong.core.utils.DateHelper;
import com.quhong.data.ActorData;
import com.quhong.data.MakeUserRoomModeData;
import com.quhong.data.UserRoomRankData;
import com.quhong.data.vo.RankListVO;
import com.quhong.data.vo.RoomRankItemVO;
import com.quhong.data.vo.UserInfoItemVO;
import com.quhong.data.vo.UserRoomRankTotalVO;
import com.quhong.enums.UserLevelConstant;
import com.quhong.image.ImageUrlGenerator;
import com.quhong.mongo.dao.*;
import com.quhong.mongo.data.MongoRoomData;
import com.quhong.msg.obj.HighlightTextObject;
import com.quhong.msg.obj.UserRoomRankInfoObject;
import com.quhong.msg.room.RoomNotificationMsg;
import com.quhong.msg.room.UserRoomRankMsg;
import com.quhong.mysql.dao.SlaveUidAidDevoteLogDao;
import com.quhong.mysql.dao.UidAidDevoteLogDao;
import com.quhong.mysql.dao.VipInfoDao;
import com.quhong.mysql.dao.WhiteTestDao;
import com.quhong.mysql.data.CountData;
import com.quhong.mysql.data.UidAidDevoteData;
import com.quhong.redis.*;
import com.quhong.room.RoomWebSender;
import com.quhong.room.redis.MicFrameRedis;
import com.quhong.room.redis.RoomRedis;
import com.quhong.utils.MatchUtils;
import com.quhong.utils.RoomUtils;
import jakarta.annotation.PostConstruct;
import jakarta.annotation.Resource;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.cache.annotation.CacheEvict;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import org.springframework.util.ObjectUtils;
import org.springframework.util.StringUtils;

import java.math.BigDecimal;
import java.util.*;
import java.util.stream.Collectors;

@Service
public class HomeRankListService {
    private static final Logger logger = LoggerFactory.getLogger(HomeRankListService.class);
    private static final List<Integer> ALL_TYPE_LIST = new ArrayList<>();
    private static final int LIMIT_SIZE = 100;
    private static final int BASE_SIZE = 50;
    private static final String TEXT_EN = "Congratulations to #username# for topping the room contribution list today. View >>";
    private static final String TEXT_AR = "تهانينا لـ #username# لتتصدر قائمة مساهمات الغرف اليوم. عرض >>";
    private static final String GAME_TYPE_DEVOTE_CHANGE = "room_hot_devote_change";

    private static final String TEXT_NEW_EN = "Congratulations! \u202b#username#\u202c has achieved Top #rank# in today's room contribution leaderboard.";
    private static final String TEXT_NEW_EN_F = "Congratulations! \u202b%s\u202c has achieved Top %s in today's room contribution leaderboard.";
    private static final String TEXT_NEW_AR = "تهانينا! حقق #username# المرتبة #rank# في قائمة تصنيف إسهام الغرف اليوم.";

    private static final String TEXT_NEW_WEEK_EN = "Congratulations! \u202b#username#\u202c has achieved Top #rank# in the 7-day room contribution leaderboard.";
    private static final String TEXT_NEW_WEEK_EN_F = "Congratulations! \u202b%s\u202c has achieved Top %s in today's room contribution leaderboard.";
    private static final String TEXT_NEW_WEEK_AR = "تهانينا! حقق #username# المرتبة #rank# في قائمة تصنيف إسهام الغرف لمدة 7 أيام.";


    private static final int maxFixNum = 9999;
    private static final int BASE_RANK_NUM = 10;

    @Resource
    private MongoRoomDao mongoRoomDao;
    @Resource
    private VipInfoDao vipInfoDao;
    @Resource
    private ActorDao actorDao;
    @Resource
    private HomeRankListRedis homeRankListRedis;
    @Resource
    private SysConfigDao configDao;
    @Resource
    private HomeRankListService homeRankListService;
    @Resource
    private UserLevelDao userLevelDao;
    @Resource
    private BadgeDao badgeDao;
    @Resource
    private CommonConfig commonConfig;
    @Resource
    private UidAidDevoteLogRedis uidAidDevoteLogRedis;
    @Resource
    private UidAidDevoteLogDao uidAidDevoteLogDao;
    @Resource
    private SlaveUidAidDevoteLogDao slaveUidAidDevoteLogDao;
    @Resource
    private RoomHotDevoteRedis roomHotDevoteRedis;
    @Resource
    private RoomWebSender roomWebSender;
    @Resource
    private RoomListService roomListService;
    @Resource
    private RoomRedis roomRedis;
    @Resource
    private MicFrameRedis micFrameRedis;
    @Resource
    private WhiteTestDao whiteTestDao;
    @Resource
    private WhitelistRedis whitelistRedis;
    @Resource
    private MysteryService mysteryService;
    @Resource
    private MysteryRedis mysteryRedis;

    @PostConstruct
    public void postInit() {
        ALL_TYPE_LIST.add(RoomListConstant.RANK_HOUR_MODE);
        ALL_TYPE_LIST.add(RoomListConstant.RANK_DAY_MODE);
        ALL_TYPE_LIST.add(RoomListConstant.RANK_WEEK_MODE);
        ALL_TYPE_LIST.add(RoomListConstant.RANK_MONTH_MODE);
    }

    private int getStartTime(int mode) {
        int now = DateHelper.getNowSeconds();
        int d;
        switch (mode) {
            case RoomListConstant.RANK_DAY_MODE:
                d = now - 24 * 3600;
                break;
            case RoomListConstant.RANK_WEEK_MODE:
                d = now - 7 * 24 * 3600;
                break;
            case RoomListConstant.RANK_MONTH_MODE:
                d = now - 30 * 24 * 3600;
                break;
            default:
                d = now - 3600;
                break;
        }
        return d;
    }

    @Cacheable(value = "getHideUidList", cacheManager = CaffeineCacheConfig.EXPIRE_10M_AFTER_WRITE)
    public List<String> getHideUidList() {
        return configDao.getList(SysConfigDao.HIDE_UID_LIST, SysConfigDao.HIDE_UID_LIST);
    }

    public void makeHomeRankList(int rankType) {
        long startTime = System.currentTimeMillis();
        for (Integer i : ALL_TYPE_LIST) {
            if (rankType == RoomListConstant.ROOM_RANK_TYPE) {
                makeRoomRankList(i);
            } else if (rankType == RoomListConstant.USER_RANK_SEND_TYPE) {
                makeUserRankList(RoomListConstant.USER_RANK_SEND_TYPE, i);
            } else if (rankType == RoomListConstant.USER_RANK_RECEIVE_TYPE) {
                makeUserRankList(RoomListConstant.USER_RANK_RECEIVE_TYPE, i);
            }
        }
        logger.info("all makeHomeRankList cost={} rankType={}", System.currentTimeMillis() - startTime, rankType);
    }

    public List<RoomRankItemVO> makeRoomRankList(int mode) {
        List<RoomRankItemVO> toList = new ArrayList<>();
        try {
            long startTime = System.currentTimeMillis();
            int start = getStartTime(mode);
            List<CountData> allList = slaveUidAidDevoteLogDao.getRoomRank(start);
            List<String> hideList = homeRankListService.getHideUidList();
            for (CountData item : allList) {
                String roomId = item.getMyKey();
                long count = item.getCount();
                String rUid = RoomUtils.getRoomHostId(roomId);
                if (hideList.contains(rUid)) {
                    continue;
                }
                MongoRoomData roomData = mongoRoomDao.getDataFromCache(roomId);
                if (null == roomData) {
                    continue;
                }
                if (whiteTestDao.isMemberByType(roomId, WhiteTestDao.WHITE_TYPE_ROOM_ID)) {
                    continue;
                }
                if (whitelistRedis.inWhitelist(WhitelistRedis.OFFICIAL_ACCOUNT, RoomUtils.getRoomHostId(roomId))) {
                    continue;
                }
                RoomRankItemVO vo = new RoomRankItemVO();
                ActorData actorData = actorDao.getActorDataFromCache(rUid);
                vo.setRidData(actorData.getRidData());
                vo.setSvipLevel(actorData.getSvipLevel());
                vo.setRoom_id(roomData.getRid());
                vo.setName(roomData.getName());
                vo.setHead(ImageUrlGenerator.generateNormalUrl(roomData.getHead()));
                vo.setTotal(MatchUtils.formatDevotes(count));
                toList.add(vo);
                if (toList.size() >= BASE_SIZE) {
                    break;
                }
            }
            homeRankListRedis.saveRoomRankList(toList, mode);
            logger.info("makeRoomRankList cost={} mode={} listSize={}", System.currentTimeMillis() - startTime, mode, allList.size());
        } catch (Exception e) {
            logger.error("makeRoomRankList error mode={} toListSize={} e={}", mode, toList.size(), e.getMessage(), e);
        }
        return toList;
    }

    public List<UserInfoItemVO> makeUserRankList(int type, int mode) {
        List<UserInfoItemVO> toList = new ArrayList<>();
        try {
            long startTime = System.currentTimeMillis();
            int start = getStartTime(mode);
            List<CountData> allList = type == RoomListConstant.USER_RANK_SEND_TYPE ?
                    slaveUidAidDevoteLogDao.getSendRank(start) :
                    slaveUidAidDevoteLogDao.getReceiveRank(start);
            List<String> hideList = homeRankListService.getHideUidList();
            Set<String> whaleSet = whitelistRedis.getWhitelistSetFromCache(WhitelistRedis.WHALE_PROTECT_LIST);
            for (CountData item : allList) {
                String aid = item.getMyKey();
                long count = item.getCount();
                if (hideList.contains(aid)) {
                    continue;
                }
                ActorData aidData = actorDao.getActorDataFromCache(aid);
                if (null == aidData || aidData.getValid() == 0) {
                    continue;
                }
                if (whiteTestDao.isMemberByType(aid, WhiteTestDao.WHITE_TYPE_RID)) {
                    continue;
                }
                if (whitelistRedis.inWhitelist(WhitelistRedis.OFFICIAL_ACCOUNT, aid)) {
                    continue;
                }
                if (whaleSet.contains(aid)) {
                    logger.info("skip whale actor. uid={}", aid);
                    continue;
                }
                UserInfoItemVO userInfoItemVO = new UserInfoItemVO();
                userInfoItemVO.setRidData(aidData.getRidData());
                userInfoItemVO.setAid(aid);
                userInfoItemVO.setName(aidData.getName());
                userInfoItemVO.setAge(aidData.getAge());
                userInfoItemVO.setGender(aidData.getFb_gender() == 2 ? 2 : 1);
                userInfoItemVO.setActiveLevel(userLevelDao.getUserLevel(aid, UserLevelConstant.ACTIVE_LEVEL));
                userInfoItemVO.setWealthLevel(userLevelDao.getUserLevel(aid, UserLevelConstant.WEALTH_LEVEL));
                userInfoItemVO.setCharmLevel(userLevelDao.getUserLevel(aid, UserLevelConstant.CHARM_LEVEL));
                int vipLevel = vipInfoDao.getIntVipLevel(aid);
                userInfoItemVO.setHead(ImageUrlGenerator.generateRoomUserUrl(aidData));
                userInfoItemVO.setMicFrame(micFrameRedis.getMicSourceFromCache(aid));
                userInfoItemVO.setViplevel(vipLevel);
                userInfoItemVO.setBadge(badgeDao.getBadgeList(aid));
                userInfoItemVO.setTotal(MatchUtils.formatDevotes(count));
                // py老代码
                userInfoItemVO.setCountry(aidData.getCountry());
                userInfoItemVO.setDesc("");
                userInfoItemVO.setRid(aidData.getRid());
                userInfoItemVO.setOs(aidData.getIntOs());
                userInfoItemVO.setValid(aidData.getValid());
                userInfoItemVO.setIdentify(vipLevel > 0 ? 1 : 0);
                userInfoItemVO.setSvipLevel(aidData.getSvipLevel());
                toList.add(userInfoItemVO);
                if (toList.size() >= BASE_SIZE) {
                    break;
                }
            }
            if (type == RoomListConstant.USER_RANK_SEND_TYPE) {
                homeRankListRedis.saveSendRankList(toList, mode);
            } else {
                homeRankListRedis.saveReceiveRankList(toList, mode);
            }
            logger.info("makeUserRankList cost={} type={} mode={} listSize={}", System.currentTimeMillis() - startTime,
                    type, mode, allList.size());
        } catch (Exception e) {
            logger.error("makeUserRankList error  type={} mode={} toListSize={} e={}"
                    , type, mode, toList.size(), e.getMessage(), e);
        }
        return toList;
    }

    @Deprecated(since = "1.3.8起弃用")
    @Cacheable(value = "getRankListVO", key = "#root.method.name+T(org.springframework.util.StringUtils).arrayToDelimitedString(args,'-')",
            cacheManager = CaffeineCacheConfig.EXPIRE_1M_AFTER_WRITE)
    public RankListVO getRankListVO(int type, int mode) {
        RankListVO rankListVO = new RankListVO();
        List<?> myList;
        if (type == RoomListConstant.USER_RANK_SEND_TYPE) {
            myList = homeRankListRedis.getSendRankList(mode);
            if (CollectionUtils.isEmpty(myList)) {
                myList = homeRankListService.makeUserRankList(RoomListConstant.USER_RANK_SEND_TYPE, mode);
            }
            stealthActor(myList);
        } else if (type == RoomListConstant.USER_RANK_RECEIVE_TYPE) {
            myList = homeRankListRedis.getReceiveRankList(mode);
            if (CollectionUtils.isEmpty(myList)) {
                myList = homeRankListService.makeUserRankList(RoomListConstant.USER_RANK_RECEIVE_TYPE, mode);
            }
            stealthActor(myList);
        } else if (type == RoomListConstant.ROOM_RANK_TYPE) {
            myList = homeRankListRedis.getRoomRankList(mode);
            if (CollectionUtils.isEmpty(myList)) {
                myList = homeRankListService.makeRoomRankList(mode);
            }
        } else {
            myList = Collections.emptyList();
        }
        rankListVO.setList(myList);
        rankListVO.setJump(0);
        return rankListVO;
    }

    private void stealthActor(List<?> myList) {
        for (Object object : myList) {
            if (object instanceof UserInfoItemVO vo) {
                ActorData actorData = mysteryService.getStealthActor(vo.getAid());
                if (!ObjectUtils.isEmpty(actorData.getMysteryId())) {
                    vo.setMystery(1);
                    vo.setRidData(actorData.getRidData());
                    vo.setRid(0);
                    vo.setHead(actorData.getHead());
                    vo.setName(actorData.getName());
                    vo.setMicFrame("");
                    vo.setViplevel(0);
                    vo.setSvipLevel(0);
                    vo.setGender(0);
                    vo.setAge(0);
                    vo.setWealthLevel(0);
                    vo.setCharmLevel(0);
                    vo.setActiveLevel(0);
                    vo.setBadge(Collections.emptyList());
                }
            }
        }
    }

    public void writeToDb() {
        long startTime = System.currentTimeMillis();
        int nowStep = MatchUtils.getStepFromDayStart(MatchConstant.STEP_LENGTH);
        int countTime = DateHelper.getNowSeconds() - 10; // 第5秒跑，这里减10秒,取上个区间的时间
        int countStep = MatchUtils.getPreStepByStep(nowStep, MatchConstant.STEP_LENGTH);
        List<UidAidDevoteData> dataList = new ArrayList<>();
        Set<String> rmFieldList = new HashSet<>();
        Set<String> changeRoomList = new HashSet<>();
        Map<String, Long> resultMap = uidAidDevoteLogRedis.getAllData();
        int reCount = 0;
        for (Map.Entry<String, Long> entry : resultMap.entrySet()) {
            try {
                String field = entry.getKey();
                String[] allField = field.split("_");
                if (allField.length != 5) {
                    logger.error("writeToDb allField length error ={}", Arrays.toString(allField));
                    continue;
                }
                String roomId = allField[0];
                String uid = allField[1];
                String aid = allField[2];
                int step = Integer.parseInt(allField[3]);
                int action = Integer.parseInt(allField[4]);
                long beans = entry.getValue();
                if (nowStep == step) {
                    // 不收集当前时间区间内的值
                    continue;
                } else if (countStep != step) {
                    logger.info("step warning countStep:{} != step:{} field:{} beans:{}",
                            countStep, step, field, beans);
                }
                dataList.add(new UidAidDevoteData(roomId, uid, aid, beans, countTime, action));
                rmFieldList.add(field);
                changeRoomList.add(roomId);
                if (dataList.size() >= 1000) {
                    uidAidDevoteLogDao.insertList(dataList, countTime);
                    uidAidDevoteLogRedis.removeAllField(rmFieldList);
                    dataList.clear();
                    rmFieldList.clear();
                    reCount++;
                }

            } catch (Exception e) {
                logger.error("writeToDb error msg:{}", e.getMessage(), e);
            }
        }
        if (!CollectionUtils.isEmpty(dataList)) {
            uidAidDevoteLogDao.insertList(dataList, countTime);
            uidAidDevoteLogRedis.removeAllField(rmFieldList);
        }

        BaseTaskFactory.getFactory().addSlow(new Task() {
            @Override
            protected void execute() {
                long startTime = System.currentTimeMillis();
                Set<String> roomSet = roomRedis.getRoomSet(); //当前活跃房间
                MakeUserRoomModeData weekData = makeUserRankByMode(RoomHotDevoteRedis.ROOM_USER_RANK_WEEK, roomSet, changeRoomList);
                MakeUserRoomModeData dayData = makeUserRankByMode(RoomHotDevoteRedis.ROOM_USER_RANK_DAY, roomSet, changeRoomList);
                Map<String, List<UserRoomRankData>> dataMap = getRankListMap(weekData, dayData);
                asyncNoticeChange(RoomHotDevoteRedis.ROOM_USER_RANK_WEEK, null, null, weekData.getNoticeThreeMap(), dataMap);
                asyncNoticeChange(RoomHotDevoteRedis.ROOM_USER_RANK_DAY, null, dayData.getTopThreeMap(), dayData.getNoticeThreeMap(), null);
                logger.info("房间内用户榜单计算完成 cost={} ", System.currentTimeMillis() - startTime);
            }
        });

        logger.info("writeToDb cost={} countStep={} countTime={} resultMap={} dataList={} " +
                        "rmFieldList={} changeRoomList={} reCount={}",
                System.currentTimeMillis() - startTime, countStep, countTime,
                resultMap.size(), dataList.size(), rmFieldList.size(), changeRoomList.size(), reCount);

    }

    private Map<String, List<UserRoomRankData>> getRankListMap(MakeUserRoomModeData weekData, MakeUserRoomModeData dayData) {
        Map<String, List<UserRoomRankData>> dataMap = new HashMap<>();
        Map<String, List<UserRoomRankData>> nowDataMap = getTotalRank(weekData.getRankListMap(), dayData.getRankListMap());
        Map<String, List<UserRoomRankData>> oldDataMap = getTotalRank(weekData.getOldRankListMap(), dayData.getOldRankListMap());
        for (Map.Entry<String, List<UserRoomRankData>> entry : nowDataMap.entrySet()) {
            String roomId = entry.getKey();
            List<UserRoomRankData> nowList = entry.getValue();
            List<UserRoomRankData> oldList = oldDataMap.get(roomId);
            if (!isSameRankList(oldList, nowList)) {
                dataMap.put(roomId, nowList);
            }
        }
        return dataMap;
    }

    private Map<String, List<UserRoomRankData>> getTotalRank(Map<String, List<UserRoomRankData>> weekMap,
                                                             Map<String, List<UserRoomRankData>> dayMap) {
        Map<String, List<UserRoomRankData>> dataMap = new HashMap<>();
        for (Map.Entry<String, List<UserRoomRankData>> entry : weekMap.entrySet()) {
            String roomId = entry.getKey();
            List<UserRoomRankData> weekList = entry.getValue();
            List<String> weekUidList = weekList.stream().map(UserRoomRankData::getUid).collect(Collectors.toList());
            List<UserRoomRankData> dayList = dayMap.get(roomId);
            List<UserRoomRankData> dataList = new ArrayList<>(weekList);
            if (!CollectionUtils.isEmpty(dayList)) {
                for (UserRoomRankData item : dayList) {
                    if (!weekUidList.contains(item.getUid())) {
                        dataList.add(item);
                    }
                }
            }
            dataMap.put(roomId, dataList);
        }
        return dataMap;
    }


    private boolean isSameRankList(List<UserRoomRankData> oldList, List<UserRoomRankData> nowList) {
        boolean isSame = false;
        if (!CollectionUtils.isEmpty(oldList) && !CollectionUtils.isEmpty(nowList)) {
            if (oldList.size() == nowList.size()) {
                for (int index = 0; index < oldList.size(); index++) {
                    UserRoomRankData oldItem = oldList.get(index);
                    UserRoomRankData nowItem = nowList.get(index);
                    if (!oldItem.equals(nowItem)) {
                        return false;
                    }
                }
                isSame = true;
            }
        } else if (CollectionUtils.isEmpty(oldList) && CollectionUtils.isEmpty(nowList)) {
            isSame = true;
        }
        return isSame;
    }

    public UserRoomRankTotalVO getUserRoomRankList(String roomId) {
        UserRoomRankTotalVO vo = new UserRoomRankTotalVO();
        vo.setRoomId(roomId);
        vo.setRankList(homeRankListService.getUserRoomRank(roomId));
        return vo;
    }

    @CacheEvict(value = "getUserRoomRank", key = "#p0", cacheManager = CaffeineCacheConfig.EXPIRE_1M_AFTER_WRITE)
    public void clearUserRoomRank(String roomId) {
    }

    @Cacheable(value = "getUserRoomRank", key = "#p0", cacheManager = CaffeineCacheConfig.EXPIRE_1M_AFTER_WRITE)
    public List<UserRoomRankData> getUserRoomRank(String roomId) {
        List<UserRoomRankData> weekList = getUserRoomRankByMode(roomId, RoomHotDevoteRedis.ROOM_USER_RANK_WEEK, BASE_RANK_NUM);
        List<UserRoomRankData> dayList = getUserRoomRankByMode(roomId, RoomHotDevoteRedis.ROOM_USER_RANK_DAY, BASE_RANK_NUM);
        List<String> weekUidList = weekList.stream().map(UserRoomRankData::getUid).collect(Collectors.toList());
        List<UserRoomRankData> dataList = new ArrayList<>(weekList);
        for (UserRoomRankData item : dayList) {
            if (!weekUidList.contains(item.getUid())) {
                dataList.add(item);
            }
        }
        return dataList;
    }


    private List<UserRoomRankData> getUserRoomRankByMode(String roomId, int rankType, int num) {
        List<UserRoomRankData> rankList = new ArrayList<>();
        Map<String, Long> linkedRankMap = roomHotDevoteRedis.getUserInRoomMap(roomId, rankType, num);
        int dTime = rankType == RoomHotDevoteRedis.ROOM_USER_RANK_DAY ? 24 * 3600 : 7 * 24 * 3600;
        int startBefore = DateHelper.getNowSeconds() - dTime;
        if (CollectionUtils.isEmpty(linkedRankMap)) {
            List<String> inactiveRoomList = roomHotDevoteRedis.getInactiveRoomList(rankType); //不活跃的房间
            if (inactiveRoomList.contains(roomId)) {
                return rankList;
            } else {
                List<CountData> dataList = uidAidDevoteLogDao.getUserRankByRoom(roomId, startBefore, RoomHotDevoteRedis.ROOM_USER_RANK_NUM);
                if (!CollectionUtils.isEmpty(dataList)) {
                    Map<String, Double> sourcesMap = new TreeMap<>();
                    List<String> nowList = new ArrayList<>();
                    for (int index = 0; index < dataList.size(); index++) {
                        CountData item = dataList.get(index);
                        if (index < 3) {
                            nowList.add(item.getMyKey());
                        }
                        if (index < num) {
                            UserRoomRankData userRoomRankData = new UserRoomRankData();
                            userRoomRankData.setUid(item.getMyKey());
                            userRoomRankData.setRank(index + 1);
                            userRoomRankData.setRankType(rankType);
                            rankList.add(userRoomRankData);
                        }
                        double rankScore = new BigDecimal(item.getCount() + "." + (maxFixNum - index)).doubleValue();
                        sourcesMap.put(item.getMyKey(), rankScore);
                    }
                    roomHotDevoteRedis.addAllUserInRoomScore(roomId, sourcesMap, rankType);
                    sourcesMap.clear();
                    roomListService.clearRedisUserInfoItemList(roomId, rankType);
                    roomListService.clearTotalRoomDevoteByMode(roomId, rankType);
//                    if (rankType == RoomHotDevoteRedis.ROOM_USER_RANK_DAY) {
//                        asyncNoticePush(roomId, nowList);
//                    }
                } else {
                    inactiveRoomList.add(roomId);
                    roomHotDevoteRedis.saveInactiveRoomList(inactiveRoomList, rankType);
                }
            }
        } else {
            int pos = 1;
            for (Map.Entry<String, Long> entry : linkedRankMap.entrySet()) {
                UserRoomRankData userRoomRankData = new UserRoomRankData();
                userRoomRankData.setUid(entry.getKey());
                userRoomRankData.setRank(pos);
                userRoomRankData.setRankType(rankType);
                rankList.add(userRoomRankData);
                pos++;
                if (pos > num) {
                    break;
                }
            }
        }
        return rankList;
    }


//    private void asyncNoticePush(String roomId, List<String> nowList) {
//        BaseTaskFactory.getFactory().addSlow(new Task() {
//            @Override
//            protected void execute() {
//                List<String> aidHeadList = new ArrayList<>();
//                for (String aid : nowList) {
//                    String aidHead = actorDao.getActorDataFromCache(aid).getHead();
//                    aidHeadList.add(vipInfoDao.generateVipUrl(aid, aidHead, ImageUrlGenerator.MODE_50));
//                }
//                roomHotDevoteRedis.saveRoomUserRankList(roomId, aidHeadList);
//                RoomHotDevoteChangePushMsg msg = new RoomHotDevoteChangePushMsg();
//                msg.setDevoteUserList(aidHeadList);
//                msg.setrType(1);
//                roomWebSender.sendRoomWebMsg(roomId, null, msg, false);
//                logger.info("接口日榜前三名变化通知成功 roomId={} aidList={} aidHeadList={}", roomId, nowList, aidHeadList);
//            }
//        });
//    }

    private MakeUserRoomModeData makeUserRankByMode(int rankType, Set<String> roomSet, Set<String> changeRoomList) {
        MakeUserRoomModeData makeUserRoomModeData = new MakeUserRoomModeData();
        try {
            long startTime = System.currentTimeMillis();
            int dTime = rankType == RoomHotDevoteRedis.ROOM_USER_RANK_DAY ? 24 * 3600 : 7 * 24 * 3600;
            int startBeforeD = DateHelper.getNowSeconds() - dTime;
            Map<String, String> topOneMap = new HashMap<>();
            Map<String, List<String>> topThreeMap = new HashMap<>();
            Map<String, List<CountData>> noticeThreeMap = new HashMap<>();
            Map<String, Double> sourcesMap = new TreeMap<>();
            Set<String> countRoomList = new HashSet<>();//需要统计的房间
            List<String> oldInactiveRoomList = roomHotDevoteRedis.getInactiveRoomList(rankType);
            List<String> inactiveRoomList = new ArrayList<>();// 不活跃房间，最近24/7*24小时没有礼物发送
            Map<String, List<UserRoomRankData>> rankListMap = new HashMap<>();
            Map<String, List<UserRoomRankData>> oldRankListMap = new HashMap<>();
            for (String roomId : roomSet) {
                List<String> oldList = new ArrayList<>();
                List<String> nowList = new ArrayList<>();
                long oldScore = 0;
                long nowScore = 0;
                String oldTopOne = "";
                String nowTopOne = "";


                Map<String, Long> oldLinkedRankMap = roomHotDevoteRedis.getUserInRoomMap(roomId, rankType, BASE_RANK_NUM);
                if (CollectionUtils.isEmpty(oldLinkedRankMap) && oldInactiveRoomList.contains(roomId) && !changeRoomList.contains(roomId)) {
//                    if (rankType == RoomHotDevoteRedis.ROOM_USER_RANK_DAY) {
//                        //增加过期时间
//                        List<String> allHeadList = roomHotDevoteRedis.getRoomUserRankList(roomId);
//                        if (allHeadList != null) {
//                            //房间内top3用户头像列表,延长10分钟过期
//                            roomHotDevoteRedis.addExpireRoomUserRankList(roomId);
//                        }
//                    }
                    inactiveRoomList.add(roomId);
                    continue;
                }
                if (!CollectionUtils.isEmpty(oldLinkedRankMap)) {
                    int pos = 0;
                    List<UserRoomRankData> oldUserRoomRankDataList = new ArrayList<>();
                    for (Map.Entry<String, Long> entry : oldLinkedRankMap.entrySet()) {
                        if (pos == 0) {
                            oldTopOne = entry.getKey();
                            oldScore = entry.getValue();
                        }
                        if (pos < 3) {
                            oldList.add(entry.getKey());
                        }
                        UserRoomRankData userRoomRankData = new UserRoomRankData();
                        userRoomRankData.setUid(entry.getKey());
                        userRoomRankData.setRank(pos + 1);
                        userRoomRankData.setRankType(rankType);
                        oldUserRoomRankDataList.add(userRoomRankData);
                        pos++;
                    }
                    oldRankListMap.put(roomId, oldUserRoomRankDataList);
                }

                List<CountData> dataList = uidAidDevoteLogDao.getUserRankByRoom(roomId, startBeforeD, RoomHotDevoteRedis.ROOM_USER_RANK_NUM);
                List<UserRoomRankData> userRoomRankDataList = new ArrayList<>();
                if (!CollectionUtils.isEmpty(dataList)) {
                    for (int index = 0; index < dataList.size(); index++) {
                        CountData item = dataList.get(index);
                        if (index == 0) {
                            nowTopOne = item.getMyKey();
                            nowScore = item.getCount();
                        }
                        if (index < 3) {
                            nowList.add(item.getMyKey());
                        }
                        if (index < BASE_RANK_NUM) {
                            UserRoomRankData userRoomRankData = new UserRoomRankData();
                            userRoomRankData.setUid(item.getMyKey());
                            userRoomRankData.setRank(index + 1);
                            userRoomRankData.setRankType(rankType);
                            userRoomRankDataList.add(userRoomRankData);
                        }
                        double rankScore = new BigDecimal(item.getCount() + "." + (maxFixNum - index)).doubleValue();
                        sourcesMap.put(item.getMyKey(), rankScore);
                    }
                    roomHotDevoteRedis.addAllUserInRoomScore(roomId, sourcesMap, rankType);
                    sourcesMap.clear();
                    rankListMap.put(roomId, userRoomRankDataList);
                    roomListService.clearRedisUserInfoItemList(roomId, rankType);
                    roomListService.clearTotalRoomDevoteByMode(roomId, rankType);
                    if (!StringUtils.isEmpty(nowTopOne) && !nowTopOne.equals(oldTopOne)) {
                        topOneMap.put(roomId, nowTopOne);
                    }
                } else {
                    inactiveRoomList.add(roomId);
                    // 没有用户上榜也要下发空数组
                    rankListMap.put(roomId, userRoomRankDataList);
                }

                if (isListChangeByPos(nowList, oldList, 3)) {
//                    logger.info("isListChangeByPos--> rankType={} roomId={} nowList={} oldList={}"
//                            , rankType, roomId, nowList, oldList);
                    topThreeMap.put(roomId, nowList); // 这里空数组也要下发下去
                    makeNoticeThreeMap(roomId, nowList, oldList, noticeThreeMap);
                } else {
//                    if (rankType == RoomHotDevoteRedis.ROOM_USER_RANK_DAY) {
//                        //延长10分钟过期,更新用户头像
//                        List<String> aidHeadList = new ArrayList<>();
//                        for (String aid : nowList) {
//                            String aidHead = actorDao.getActorDataFromCache(aid).getHead();
//                            aidHeadList.add(vipInfoDao.generateVipUrl(aid, aidHead, ImageUrlGenerator.MODE_50));
//                        }
//                        roomHotDevoteRedis.saveRoomUserRankList(roomId, aidHeadList);
//                    }
                }
                countRoomList.add(roomId);
            }
            roomHotDevoteRedis.saveInactiveRoomList(inactiveRoomList, rankType);
            int roomSetSize = roomSet.size();
            int countRoomListSize = countRoomList.size();
            int changeRoomListSize = changeRoomList.size();
            int inactiveRoomListSize = inactiveRoomList.size();
            makeUserRoomModeData.setTopThreeMap(topThreeMap);
            makeUserRoomModeData.setNoticeThreeMap(noticeThreeMap);
            makeUserRoomModeData.setRankListMap(rankListMap);
            makeUserRoomModeData.setOldRankListMap(oldRankListMap);
            makeUserRoomModeData.setTopOneMap(topOneMap);
            logger.info("after writeRankToRedis success cost={} rankType={} roomSet={} countRoomList={} changeRoomList={} inactiveRoomList={}",
                    System.currentTimeMillis() - startTime, rankType, roomSetSize, countRoomListSize, changeRoomListSize, inactiveRoomListSize);
        } catch (Exception e) {
            logger.error("rankType={} changeRoomList size={} makeUserRankByRoom", rankType, changeRoomList.size(), e);
        }
        return makeUserRoomModeData;
    }

    private void asyncNoticeChange(int rankType, Map<String, String> topOneMap, Map<String, List<String>> topThreeMap,
                                   Map<String, List<CountData>> noticeThreeMap, Map<String, List<UserRoomRankData>> dataMap) {
        BaseTaskFactory.getFactory().addSlow(new Task() {
            @Override
            protected void execute() {
                long startTime = System.currentTimeMillis();
                noticeChange(rankType, topOneMap, topThreeMap, noticeThreeMap, dataMap);
                logger.info("房间内用户排行榜通知完成 success cost={} rankType={} topThreeMap={} noticeThreeMap={} dataMap={}",
                        System.currentTimeMillis() - startTime,
                        rankType, topThreeMap == null ? null : topThreeMap.size(),
                        noticeThreeMap.size(), dataMap == null ? null : dataMap.size());
            }
        });
    }


    private void clearUserRankByRoomWeek(Set<String> countRoomList) {
        try {
            long startTime = System.currentTimeMillis();
            for (String roomId : countRoomList) {
                roomListService.clearRedisUserInfoItemList(roomId, RoomHotDevoteRedis.ROOM_USER_RANK_WEEK);
                roomListService.clearUserInfoItemList(roomId, RoomHotDevoteRedis.ROOM_USER_RANK_WEEK);
                roomListService.clearTotalRoomDevoteByMode(roomId, RoomHotDevoteRedis.ROOM_USER_RANK_WEEK);
            }
            logger.info("after clearUserRankByRoomWeek week success cost={} countRoomList={} ",
                    System.currentTimeMillis() - startTime, countRoomList.size());
        } catch (Exception e) {
            logger.error("clearUserRankByRoomWeek size={} makeUserRankByRoom", countRoomList.size(), e);
        }
    }

    private void noticeChange(int rankType, Map<String, String> topOneMap, Map<String, List<String>> topThreeMap,
                              Map<String, List<CountData>> noticeThreeMap, Map<String, List<UserRoomRankData>> dataMap) {

//        if (rankType == RoomHotDevoteRedis.ROOM_USER_RANK_DAY && !CollectionUtils.isEmpty(topThreeMap)) {
//            for (Map.Entry<String, List<String>> entry : topThreeMap.entrySet()) {
//                String roomId = entry.getKey();
//                List<String> aidList = entry.getValue();
//                List<String> aidHeadList = new ArrayList<>();
//                for (String aid : aidList) {
//                    String aidHead = actorDao.getActorDataFromCache(aid).getHead();
//                    aidHeadList.add(vipInfoDao.generateVipUrl(aid, aidHead, ImageUrlGenerator.MODE_50));
//                }
//                roomHotDevoteRedis.saveRoomUserRankList(roomId, aidHeadList);
//                RoomHotDevoteChangePushMsg msg = new RoomHotDevoteChangePushMsg();
//                msg.setDevoteUserList(aidHeadList);
//                msg.setrType(1);
//                roomWebSender.sendRoomWebMsg(roomId, null, msg, false);
////                logger.info("日榜前三名变化通知成功 roomId={} aidList={} aidHeadList={}", roomId, aidList, aidHeadList);
//            }
//        }

        if (!CollectionUtils.isEmpty(dataMap)) {
            for (Map.Entry<String, List<UserRoomRankData>> entry : dataMap.entrySet()) {
                String roomId = entry.getKey();
                List<UserRoomRankData> rankDataList = entry.getValue();
                List<UserRoomRankInfoObject> rankList = new ArrayList<>();
                rankDataList.forEach(item -> {
                    UserRoomRankInfoObject object = new UserRoomRankInfoObject();
                    object.setUid(item.getUid());
                    object.setRank(item.getRank());
                    object.setRankType(item.getRankType());
                    rankList.add(object);
                });
                UserRoomRankMsg msg = new UserRoomRankMsg();
                msg.setRankList(rankList);
                homeRankListService.clearUserRoomRank(roomId);
                roomWebSender.sendRoomWebMsg(roomId, null, msg, false);
//                logger.info("总排行榜数据推送成功 roomId={} rankList={}", roomId, rankList);
            }
        }

        int noticeSwitch = commonConfig.getSwitchConfigValue(CommonConfig.ROOM_TOP_ONE_USER_NOTICE_KEY, 0);
        if (!CollectionUtils.isEmpty(noticeThreeMap) && noticeSwitch == 1) {
            for (Map.Entry<String, List<CountData>> entry : noticeThreeMap.entrySet()) {
                String roomId = entry.getKey();
                List<CountData> countDataList = entry.getValue();
                for (CountData item : countDataList) {
                    String aid = item.getMyKey();
                    long rank = item.getCount();
                    ActorData actorData = mysteryService.getMysteryActor(aid);
                    if (!ObjectUtils.isEmpty(actorData.getMysteryId()) || !ObjectUtils.isEmpty(mysteryRedis.getStealthFromCache(aid))) {
                        return;
                    }
                    String aidName = actorData.getName();
                    RoomNotificationMsg msg = new RoomNotificationMsg();
                    msg.setUid(aid);
                    msg.setUser_name(aidName);
                    msg.setHide_head(1);
                    if (rankType == RoomHotDevoteRedis.ROOM_USER_RANK_DAY) {
//                        msg.setText(TEXT_NEW_EN.replace("#username#", aidName).replace("#rank#", rank + ""));
                        msg.setText(String.format(TEXT_NEW_EN_F, aidName, rank));
                        msg.setText_ar(TEXT_NEW_AR.replace("#username#", aidName).replace("#rank#", rank + ""));
                    } else {
//                        msg.setText(TEXT_NEW_WEEK_EN.replace("#username#", aidName).replace("#rank#", rank + ""));
                        msg.setText(String.format(TEXT_NEW_WEEK_EN_F, aidName, rank));
                        msg.setText_ar(TEXT_NEW_WEEK_AR.replace("#username#", aidName).replace("#rank#", rank + ""));
                    }
                    List<HighlightTextObject> list = new ArrayList<>();
                    HighlightTextObject object = new HighlightTextObject();
                    object.setText(aidName);
                    object.setHighlightColor("#FFE200");//
                    list.add(object);
                    msg.setHighlight_text(list);
                    msg.setHighlight_text_ar(list);
                    msg.setWeb_type(0);
                    msg.setGame_type(GAME_TYPE_DEVOTE_CHANGE);
                    msg.setFromRoomId(roomId);
                    msg.setMystery(actorData.getMystery());
                    roomWebSender.sendRoomWebMsg(roomId, null, msg, false);
//                    logger.info("公屏消息前三名改变推送公屏消息成功 rankType={} roomId={} aid={} aidName={} rank={}", rankType, roomId, aid, aidName, rank);
                }
            }
        }

    }

    private boolean isListChangeByPos(List<String> nowList, List<String> oldList, int maxPos) {
        boolean isSame = true;
        if (oldList.size() == 0) {
            isSame = false;
        } else if (nowList.size() == oldList.size()) {
            for (int index = 0; index < nowList.size(); index++) {
                if (index == maxPos) {
                    break;
                }
                if (!nowList.get(index).equals(oldList.get(index))) {
                    isSame = false;
                    break;
                }
            }
        } else {
            isSame = false;
        }
        return !isSame;
    }

    private void makeNoticeThreeMap(String roomId, List<String> nowList, List<String> oldList, Map<String, List<CountData>> noticeThreeMap) {
        if (!CollectionUtils.isEmpty(nowList)) {
            List<CountData> countDataList = new ArrayList<>();
            for (int index = 0; index < nowList.size(); index++) {
                String uid = nowList.get(index);
                int oldIndex = oldList.indexOf(uid);
                if (index < oldIndex || oldIndex == -1) {
                    CountData countData = new CountData();
                    countData.setMyKey(uid);
                    countData.setCount(index + 1);
                    countDataList.add(countData);
                }
            }
            if (!CollectionUtils.isEmpty(countDataList)) {
                noticeThreeMap.put(roomId, countDataList);
            }
        }
    }

}
