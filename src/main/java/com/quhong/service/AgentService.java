package com.quhong.service;

import com.alibaba.fastjson.JSONObject;
import com.quhong.api.SundryService;
import com.quhong.core.concurrency.BaseTaskFactory;
import com.quhong.core.concurrency.tasks.Task;
import com.quhong.core.config.ServerConfig;
import com.quhong.core.date.DateSupport;
import com.quhong.core.utils.DateHelper;
import com.quhong.data.ActorData;
import com.quhong.data.CharmIncomeData;
import com.quhong.data.dto.AgentDTO;
import com.quhong.data.dto.DataCenterCharmDTO;
import com.quhong.data.dto.SendChatMsgDTO;
import com.quhong.data.vo.*;
import com.quhong.enums.*;
import com.quhong.exception.CommonException;
import com.quhong.exception.CommonH5Exception;
import com.quhong.feign.DataCenterService;
import com.quhong.feign.IMsgService;
import com.quhong.image.ImageUrlGenerator;
import com.quhong.mongo.dao.ActorDao;
import com.quhong.mongo.dao.CharmStatDao;
import com.quhong.mongo.dao.FamilyDailyIncomeDao;
import com.quhong.mongo.dao.GameStatDao;
import com.quhong.mongo.data.OfficialData;
import com.quhong.mysql.dao.*;
import com.quhong.mysql.data.*;
import com.quhong.redis.BlockAgentUserRedis;
import com.quhong.redis.HostCodeRedis;
import com.quhong.redis.PlayerStatusRedis;
import com.quhong.utils.CollectionUtil;
import com.quhong.utils.PageUtils;
import com.quhong.utils.RoomUtils;
import com.quhong.vo.PageVO;
import jakarta.annotation.Resource;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;
import org.springframework.web.util.UriComponentsBuilder;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2024/3/27
 */
@Service
public class AgentService {

    private final Logger logger = LoggerFactory.getLogger(getClass());

    private static final int PAGE_SIZE = 10;

    private static final String HELP_PAGE_URL_DEBUG = "https://api.opswaho.com/agent_rule/";
    private static final String HELP_PAGE_URL_PRO = "https://statics.waho.live/agent_rule/";
    private static final String INVITED_H5_URL_DEBUG = "https://api.opswaho.com/my_agent/?isInvite=1";
    private static final String INVITED_H5_URL_PRO = "https://statics.waho.live/my_agent/?isInvite=1";
    private static final String INVITE_AGENT_PAGE_URL_DEBUG = "https://api.opswaho.com/invite_agent/?isShare=1";
    private static final String INVITE_AGENT_PAGE_URL_PRO = "https://statics.waho.live/invite_agent/?isShare=1";
    private static final String INCOME_URL_V3_PRO = "https://statics.waho.live/income_v3/";
    private static final String INCOME_URL_V3_DEBUG = "https://api.opswaho.com/income_v3/";

    private static final String INVITE_MSG_BODY_EN = "You received the agent invitation";
    private static final String INVITE_MSG_BODY_AR = "لقد تلقيت دعوة وكيل";

    private static final String JOIN_AGENT_TITLE = "New anchor notification!";
    private static final String JOIN_AGENT_TITLE_AR = "إشعار مضيف جديد!";

    private static final String JOIN_AGENT_DESC = "Congratulations! (%s) successfully joined your agent.";
    private static final String JOIN_AGENT_DESC_AR = "تهانينا! انضم (%s) بنجاح لوكيلك.";

    @Resource
    private ActorDao actorDao;
    @Resource
    private FamilyDao familyDao;
    @Resource
    private FamilyMemberDao familyMemberDao;
    @Resource
    private AnchorWalletService anchorWalletService;
    @Resource
    private BannerListService bannerListService;
    @Resource
    private PlayerStatusRedis playerStatusRedis;
    @Resource
    private RoomMicLogDao roomMicLogDao;
    @Resource
    private HostCodeRedis hostCodeRedis;
    @Resource
    private InviteAnchorRecordDao inviteAnchorRecordDao;
    @Resource
    private IMsgService iMsgService;
    @Resource
    private FamilyService familyService;
    @Resource
    private QuitFamilyRequestDao quitFamilyRequestDao;
    @Resource
    private GameStatDao gameStatDao;
    @Resource
    private FamilyDailyIncomeDao familyDailyIncomeDao;
    @Resource
    private OfficialMsgService officialMsgService;
    @Resource
    private JoinFamilyRecordDao joinFamilyRecordDao;
    @Resource
    private CharmStatDao charmStatDao;
    @Resource
    private DailySendGiftRecordDao dailySendGiftRecordDao;
    @Resource
    private BlockAgentUserRedis blockAgentUserRedis;
    @Resource
    private DataCenterService dataCenterService;
    @Resource
    private FamilyRequestDao familyRequestDao;
    @Resource
    private SundryService sundryService;
    @Autowired
    private MicTimeDao micTimeDao;

    public AgentManageInfoVO manageInfo(AgentDTO dto) {
        FamilyData familyData = familyDao.selectByOwnerUid(dto.getUid());
        if (familyData == null) {
            throw new CommonException(HttpCode.PARAM_ERROR);
        }
        AgentManageInfoVO vo = new AgentManageInfoVO();
        AnchorWalletData wallet = anchorWalletService.getWallet(dto.getUid());
        List<FamilyMemberData> memberList = familyMemberDao.selectMemberList(familyData.getId());
        List<FamilyData> subAgentList = familyDao.getSubAgentList(familyData.getId());
        vo.setFamilyId(familyData.getId());
        vo.setFamilyRid(familyData.getRid());
        vo.setDiamondBalance(actorDao.getBalance(dto.getUid()));
        vo.setCharmBalance(wallet != null ? wallet.getCharmBalance().longValue() : 0);
        vo.setAnchorNum(memberList.size());
        vo.setAgentNum(!CollectionUtils.isEmpty(subAgentList) ? subAgentList.size() : 0);
        vo.setInviteAgentPageUrl(ServerConfig.isProduct() ? INVITE_AGENT_PAGE_URL_PRO : INVITE_AGENT_PAGE_URL_DEBUG);
        vo.setIncomePageUrl(ServerConfig.isProduct() ? INCOME_URL_V3_PRO : INCOME_URL_V3_DEBUG);
        vo.setBannerList(bannerListService.getBannerList(dto, BannerListService.AGENT_BANNER));
        vo.setFamilyRequestCount(familyRequestDao.getUnprocessedCount(familyData.getId()));
        return vo;
    }

    public AgentDetailInfoVO detailInfo(AgentDTO dto) {
        ActorData actorData = actorDao.getActorData(dto.getUid());
        if (actorData == null) {
            logger.error("can not find actor data. uid={}", dto.getUid());
            throw new CommonException(HttpCode.PARAM_ERROR);
        }
        FamilyData familyData = familyDao.selectByOwnerUid(dto.getUid());
        if (familyData == null) {
            throw new CommonException(HttpCode.PARAM_ERROR);
        }
        AgentDetailInfoVO vo = new AgentDetailInfoVO();
        fillAnchorData(vo, dto.getUid(), familyData.getId());
        fillAgentData(vo, dto.getUid(), familyData.getId());
        vo.setTotalIncome(vo.getAgentTotalIncome() + vo.getAnchorTotalIncome());
        vo.setMyCommission(vo.getMyAgentCommission() + vo.getMyAnchorCommission());
        vo.setFamilyId(familyData.getId());
        vo.setHelpPageUrl(ServerConfig.isProduct() ? HELP_PAGE_URL_PRO : HELP_PAGE_URL_DEBUG);
        fillCommissionRateInfo(vo, familyData.getRewardLevel());
        return vo;
    }

    private void fillCommissionRateInfo(AgentDetailInfoVO vo, String rewardLevel) {
        if (StringUtils.hasLength(rewardLevel)) {
            // 手动设置的佣金分成等级
            String nextRewardLevel = anchorWalletService.getNextRewardLevel(rewardLevel);
            vo.setCurrentProp(anchorWalletService.findAgentRate(rewardLevel).floatValue());
            vo.setCurrentLevel(rewardLevel);
            vo.setNextProp(anchorWalletService.findAgentRate(nextRewardLevel).floatValue());
            vo.setNextLevel(nextRewardLevel);
        } else {
            // 根据旗下代理和主播收益计算佣金分成等级
            long totalIncome = vo.getAgentTotalIncome() + vo.getAnchorTotalIncome();
            int currentIndex = getCurrentIndex(totalIncome);
            vo.setCurrentProp(AnchorWalletService.AGENT_RATE[currentIndex].floatValue());
            vo.setCurrentLevel(AnchorWalletService.AGENT_LEVEL[currentIndex]);
            vo.setNextProp(AnchorWalletService.AGENT_RATE[Math.min(currentIndex + 1, AnchorWalletService.AGENT_RATE.length - 1)].floatValue());
            vo.setNextLevel(AnchorWalletService.AGENT_LEVEL[Math.min(currentIndex + 1, AnchorWalletService.AGENT_LEVEL.length - 1)]);
            long curLevelTarget = AnchorWalletService.AGENT_INCOME[currentIndex];
            long nextLevelTarget = AnchorWalletService.AGENT_INCOME[Math.min(currentIndex + 1, AnchorWalletService.AGENT_INCOME.length - 1)];
            if (curLevelTarget != nextLevelTarget) {
                vo.setNextLevelNeedBeans(nextLevelTarget - totalIncome);
                vo.setProgressRate(BigDecimal.valueOf(totalIncome - curLevelTarget).divide(BigDecimal.valueOf(nextLevelTarget - curLevelTarget), 2, RoundingMode.FLOOR).floatValue());
            }
        }
    }

    public int getCurrentIndex(long totalIncome) {
        for (int i = AnchorWalletService.AGENT_INCOME.length - 1; i >= 0; i--) {
            if (totalIncome >= AnchorWalletService.AGENT_INCOME[i]) {
                return i;
            }
        }
        return 0;
    }

    private void fillAgentData(AgentDetailInfoVO vo, String uid, int familyId) {
        List<FamilyData> subAgentList = familyDao.getSubAgentList(familyId, null);
        long agentTotalIncome = 0;
        int incomeAgentNum = 0;
        if (!CollectionUtils.isEmpty(subAgentList)) {
            for (FamilyData subAgent : subAgentList) {
                long agentIncome = anchorWalletService.getAllAgentIncome(subAgent.getId());
                agentTotalIncome = agentTotalIncome + agentIncome;
                if (agentIncome > 0) {
                    incomeAgentNum++;
                }
            }
        }
        vo.setAgentTotalIncome(agentTotalIncome);
        int endTime = DateHelper.getNowSeconds();
        int startTime = (int) DateSupport.ARABIAN.getTimeSeconds(DateSupport.ARABIAN.getDayOffset(-29));
        vo.setMyAgentCommission((int) charmStatDao.getUserTotalCharm(uid, null, 7, startTime, endTime));
        vo.setIncomeAgentNum(incomeAgentNum);
    }

    private void fillAnchorData(AgentDetailInfoVO vo, String uid, int familyId) {
        List<FamilyMemberData> memberList = familyMemberDao.selectMemberList(familyId);
        int nowSeconds = DateHelper.getNowSeconds();
        int startTime = (int) DateSupport.ARABIAN.getTimeSeconds(DateSupport.ARABIAN.getDayOffset(-29));
        Set<String> memberUidSet = CollectionUtil.listToPropertySet(memberList, FamilyMemberData::getUid);
        int endDate = Integer.parseInt(DateSupport.yyyyMMdd(DateSupport.ARABIAN.getToday()));
        int startDate = Integer.parseInt(DateSupport.yyyyMMdd(DateSupport.ARABIAN.getDayOffset(-29)));
        vo.setAnchorTotalIncome(familyDailyIncomeDao.getFamilyIncome(Collections.singleton(familyId), startDate, endDate));
        vo.setMyAnchorCommission((int) charmStatDao.getUserTotalCharm(uid, null, 6, startTime, nowSeconds));
        vo.setIncomeAnchorNum(charmStatDao.getHasIncomeAnchor(familyId, startTime, nowSeconds));
        int activeAnchorNum = 0;
        int anchorLiveSumTime = 0;
        Map<String, Integer> upMicTimeMap = roomMicLogDao.actorAddUpMicTimeMap(startTime, nowSeconds, memberUidSet);
        for (Map.Entry<String, Integer> entry : upMicTimeMap.entrySet()) {
            anchorLiveSumTime += entry.getValue();
            if (entry.getValue() > 0) {
                activeAnchorNum++;
            }
        }
        vo.setActiveAnchorNum(activeAnchorNum);
        vo.setAnchorLiveSumTime(anchorLiveSumTime);
    }

    public PageVO<MembersPageVO> memberList(AgentDTO.Members dto) {
        FamilyData familyData = familyDao.selectByOwnerUid(dto.getUid());
        if (familyData == null) {
            return new PageVO<>();
        }
        if (StringUtils.hasLength(dto.getSearchKey())) {
            return memberSearch(familyData.getId(), dto.getType(), dto.getSearchKey());
        }
        List<MembersPageVO> list;
        int size;
        int page = Math.max(dto.getPage(), 1);
        if (dto.getType() == 0) {
            List<FamilyMemberData> memberList = familyMemberDao.selectMemberPageList(familyData.getId(), null, page, PAGE_SIZE, 3);
            list = getFamilyMemberList(memberList);
            size = memberList.size();
        } else {
            List<FamilyData> agentFamilyList = familyDao.selectSubAgentPage(familyData.getId(), page, PAGE_SIZE, false);
            list = getAgentMemberList(agentFamilyList);
            size = agentFamilyList.size();
        }
        return new PageVO<>(list, size >= PAGE_SIZE ? (page + 1) + "" : "");
    }

    public PageVO<MembersPageVO> memberSearch(int familyId, int type, String searchKey) {
        String aid = null;
        if (StringUtils.hasLength(searchKey)) {
            ActorData searchActor = actorDao.getActorByRidOrAlphaRid(searchKey);
            if (searchActor == null) {
                return new PageVO<>();
            }
            aid = searchActor.getUid();
        }
        List<MembersPageVO> list;
        if (type == 0) {
            FamilyMemberData familyMemberData = familyMemberDao.selectByFamilyIdAndUid(familyId, aid);
            if (familyMemberData == null) {
                return new PageVO<>();
            }
            list = getFamilyMemberList(Collections.singletonList(familyMemberData));
        } else {
            FamilyData familyData = familyDao.selectByOwnerUid(aid);
            if (familyData == null || familyData.getPid() != familyId) {
                return new PageVO<>();
            }
            list = getAgentMemberList(Collections.singletonList(familyData));
        }
        return new PageVO<>(list, "");
    }

    private List<MembersPageVO> getAgentMemberList(List<FamilyData> agentMemberList) {
        List<MembersPageVO> list = new ArrayList<>();
        for (FamilyData agentMember : agentMemberList) {
            ActorData actorData = actorDao.getActorDataFromCache(agentMember.getOwnerUid());
            if (actorData == null) {
                continue;
            }
            MembersPageVO vo = new MembersPageVO();
            vo.setAid(actorData.getUid());
            vo.setRid(actorData.getRid());
            vo.setName(actorData.getName());
            vo.setHead(actorData.getHead());
            vo.setBindTime(agentMember.getCtime());
            vo.setAnchorNum(familyMemberDao.selectMemberCount(agentMember.getId()));
            vo.setOnlineStatus(playerStatusRedis.getPlayerStatus(actorData.getUid()));
            long totalCharm = anchorWalletService.getAllAgentIncome(agentMember.getId());
            vo.setLevel(anchorWalletService.getAgentLevel(totalCharm));
            list.add(vo);
        }
        return list;
    }

    private List<MembersPageVO> getFamilyMemberList(List<FamilyMemberData> memberList) {
        List<MembersPageVO> list = new ArrayList<>();
        for (FamilyMemberData memberData : memberList) {
            ActorData actorData = actorDao.getActorDataFromCache(memberData.getUid());
            if (actorData == null) {
                continue;
            }
            MembersPageVO vo = new MembersPageVO();
            vo.setAid(actorData.getUid());
            vo.setRid(actorData.getRid());
            vo.setName(actorData.getName());
            vo.setHead(actorData.getHead());
            vo.setOnlineStatus(playerStatusRedis.getPlayerStatus(actorData.getUid()));
            list.add(vo);
        }
        return list;
    }

    public MineAgentPageVO myAgentPage(AgentDTO dto) {
        ActorData actorData = actorDao.getActorDataFromCache(dto.getUid());
        if (actorData == null) {
            logger.error("can not find actor data. uid={}", dto.getUid());
            throw new CommonH5Exception(HttpCode.PARAM_ERROR);
        }
        MineAgentPageVO vo = new MineAgentPageVO();
        vo.setRid(actorData.getRid());
        vo.setMyName(actorData.getName());
        vo.setHostCode(hostCodeRedis.getHostCodeByUid(dto.getUid()));
        FamilyMemberData familyMemberData = familyMemberDao.selectByUid(dto.getUid());
        if (familyMemberData != null) {
            FamilyData familyData = familyDao.selectById(familyMemberData.getFamilyId());
            if (familyMemberData.getRole() == 1) {
                // 是代理
                FamilyData agentData = familyDao.selectById(familyData.getPid());
                if (agentData != null) {
                    vo.setMyAgentId(agentData.getId());
                    fillMyAgentInfo(vo, agentData.getOwnerUid());
                    vo.setJoinTime(agentData.getCtime());
                    QuitFamilyRequestData quitRequestData = quitFamilyRequestDao.selectRecentOne(agentData.getId(), dto.getUid());
                    vo.setQuitRequestTime(quitRequestData != null && DateHelper.getNowSeconds() - quitRequestData.getCtime() <= TimeUnit.DAYS.toSeconds(30) ? quitRequestData.getCtime() : 0);
                }
            } else {
                // 是主播
                vo.setMyAgentId(familyData.getId());
                fillMyAgentInfo(vo, familyData.getOwnerUid());
                vo.setJoinTime(familyMemberData.getCtime());
                QuitFamilyRequestData quitRequestData = quitFamilyRequestDao.selectRecentOne(familyData.getId(), dto.getUid());
                vo.setQuitRequestTime(quitRequestData != null && DateHelper.getNowSeconds() - quitRequestData.getCtime() <= TimeUnit.DAYS.toSeconds(30) ? quitRequestData.getCtime() : 0);
            }
        }
        return vo;
    }

    private void fillMyAgentInfo(MineAgentPageVO vo, String myAgentUid) {
        ActorData familyOwner = actorDao.getActorDataFromCache(myAgentUid);
        vo.setMyAgentRid(familyOwner.getRid() + "");
        vo.setMyAgentName(familyOwner.getName());
        vo.setMyAgentHead(familyOwner.getHead());
    }

    public AgentSearchVO search(AgentDTO.Search dto) {
        AgentSearchVO vo = new AgentSearchVO();
        int intRid;
        try {
            intRid = Integer.parseInt(dto.getRid());
        } catch (NumberFormatException e) {
            logger.error("string to int error. rid={}", dto.getRid());
            return vo;
        }
        ActorData actorData = actorDao.getActorByRid(intRid);
        if (actorData == null) {
            return vo;
        }
        vo.setAid(actorData.getUid());
        vo.setRid(actorData.getRid());
        vo.setRidData(actorData.getRidData());
        vo.setName(actorData.getName());
        vo.setHead(ImageUrlGenerator.generateRoomUserUrl(actorData));
        FamilyData agentData = familyDao.selectByOwnerUid(actorData.getUid());
        vo.setIsAgent(agentData != null ? 1 : 0);
        vo.setAgentFamilyRid(agentData != null ? agentData.getRid() : 0);
        return vo;
    }

    public void inviteAnchor(AgentDTO.InviteAnchor dto, String ipAddress) {
        FamilyData agentData = familyDao.selectByOwnerUid(dto.getUid());
        if (agentData == null) {
            throw new CommonH5Exception(HttpCode.PARAM_ERROR);
        }
        // 校验主播码格式
        if (!hostCodeRedis.checkHostCode(dto.getHostCode())) {
            logger.info("incorrect host code. hostCode={}", dto.getHostCode());
            throw new CommonH5Exception(RoomHttpCode.INCORRECT_HOST_CODE);
        }
        // 校验主播码是否匹配
        String hostCode = hostCodeRedis.getHostCodeByUid(dto.getAid());
        if (!dto.getHostCode().equals(hostCode)) {
            logger.info("incorrect host code. hostCode={} hostCodeFromRedis={}", dto.getHostCode(), hostCode);
            throw new CommonH5Exception(RoomHttpCode.INCORRECT_HOST_CODE);
        }
        // 邀请者是否已是主播
        FamilyMemberData memberData = familyMemberDao.selectByUid(dto.getAid());
        if (memberData != null) {
            logger.info("The other party has joined the agent. aid={} familyId={}", dto.getAid(), memberData.getFamilyId());
            if (memberData.getFamilyId().equals(agentData.getId())) {
                throw new CommonH5Exception(RoomHttpCode.HAS_JOINED_YOUR_AGENT);
            } else {
                throw new CommonH5Exception(RoomHttpCode.HAS_JOINED_THE_AGENT);
            }
        }
        int nowTime = DateHelper.getNowSeconds();
        // 代理对单个主播24小时内最多发送1次邀请
        InviteAnchorRecordData inviteRecord = inviteAnchorRecordDao.selectOneByUidAndAid(dto.getUid(), dto.getAid());
        int timeLimit = ServerConfig.isProduct() ? (int) TimeUnit.DAYS.toSeconds(1) : (int) TimeUnit.MINUTES.toSeconds(5);
        if (inviteRecord != null && nowTime - inviteRecord.getCtime() <= timeLimit) {
            logger.info("You have sent an invitation, which cannot be resent within 24 hours. uid={} aid={} lastInviteTime={}", dto.getUid(), dto.getAid(), inviteRecord.getCtime());
            throw new CommonH5Exception(RoomHttpCode.HAVE_SENT_AN_INVITATION);
        }
        InviteAnchorRecordData newInviteRecord = new InviteAnchorRecordData();
        newInviteRecord.setUid(dto.getUid());
        newInviteRecord.setAid(dto.getAid());
        newInviteRecord.setStatus(0);
        newInviteRecord.setCtime(nowTime);
        inviteAnchorRecordDao.insert(newInviteRecord);
        sendInviteMsg(dto, newInviteRecord.getId());
        blockAgentUserRedis.addRequestIP(dto.getUid(), ipAddress);
    }

    private void sendInviteMsg(AgentDTO.InviteAnchor dto, int inviteId) {
        SendChatMsgDTO msgDto = new SendChatMsgDTO();
        msgDto.setUid(dto.getUid());
        msgDto.setAid(dto.getAid());
        msgDto.setMsgType(MsgType.AGENT_INVITATION);
        msgDto.setOs(dto.getOs());
        msgDto.setSlang(dto.getSlang());
        msgDto.setMsgBody(dto.getSlang() == SLangType.ENGLISH ? INVITE_MSG_BODY_EN : INVITE_MSG_BODY_AR);
        msgDto.setVersioncode(dto.getVersioncode());
        msgDto.setNew_versioncode(dto.getNew_versioncode());
        JSONObject jsonObject = new JSONObject();
        jsonObject.put("url", getInvitedH5Url(inviteId));
        jsonObject.put("endTime", DateHelper.getNowSeconds() + TimeUnit.DAYS.toSeconds(1));
        msgDto.setMsgInfo(jsonObject);
        iMsgService.sendMsg(msgDto);
    }

    private String getInvitedH5Url(int inviteId) {
        String url = ServerConfig.isProduct() ? INVITED_H5_URL_PRO : INVITED_H5_URL_DEBUG;
        UriComponentsBuilder urlBuilder = UriComponentsBuilder.fromHttpUrl(url);
        urlBuilder.queryParam("inviteId", inviteId);
        return urlBuilder.build(false).encode().toUriString();
    }

    public PageVO<InviteAnchorRecordVO> inviteRecord(AgentDTO.InviteRecord dto) {
        if (dto.getRecentData() == 1) {
            List<InviteAnchorRecordData> inviteRecordList = inviteAnchorRecordDao.getRecentInviteRecord(dto.getUid(), (int) (DateHelper.getNowSeconds() - TimeUnit.DAYS.toSeconds(7)));
            List<InviteAnchorRecordVO> list = getInviteAnchorRecordVOList(inviteRecordList);
            Comparator<InviteAnchorRecordVO> comparing = Comparator.comparing(o -> o.getStatus() != 2 ? o.getStatus() * 10 : o.getStatus());
            Comparator<InviteAnchorRecordVO> ctimeDesc = Comparator.comparingInt(InviteAnchorRecordVO::getCtime).reversed();
            list.sort(comparing.thenComparing(ctimeDesc));
            return new PageVO<>(list, "");
        } else {
            int page = Math.max(dto.getPage(), 1);
            List<InviteAnchorRecordData> inviteRecordList = inviteAnchorRecordDao.selectPage(dto.getUid(), page, PAGE_SIZE);
            List<InviteAnchorRecordVO> list = getInviteAnchorRecordVOList(inviteRecordList);
            return new PageVO<>(list, inviteRecordList.size() >= PAGE_SIZE ? (page + 1) + "" : "");
        }
    }

    private List<InviteAnchorRecordVO> getInviteAnchorRecordVOList(List<InviteAnchorRecordData> inviteRecordList) {
        if (CollectionUtils.isEmpty(inviteRecordList)) {
            return Collections.emptyList();
        }
        List<InviteAnchorRecordVO> list = new ArrayList<>();
        for (InviteAnchorRecordData recordData : inviteRecordList) {
            ActorData actorData = actorDao.getActorDataFromCache(recordData.getAid());
            if (actorData == null) {
                logger.error("can not find actor data. uid={}", recordData.getUid());
                continue;
            }
            InviteAnchorRecordVO vo = new InviteAnchorRecordVO();
            vo.setUid(actorData.getUid());
            vo.setRid(actorData.getRid());
            vo.setRidData(actorData.getRidData());
            vo.setName(actorData.getName());
            vo.setHead(ImageUrlGenerator.generateRoomUserUrl(actorData));
            vo.setInviteTime(recordData.getCtime());
            vo.setStatus(getInviteRecordStatus(recordData.getStatus(), recordData.getCtime()));
            vo.setOnlineStatus(playerStatusRedis.getPlayerStatus(actorData.getUid()));
            vo.setCtime(recordData.getCtime());
            list.add(vo);
        }
        return list;
    }


    /**
     * 邀请发送超过24小时，用户没有接受邀请视为已拒绝
     */
    private Integer getInviteRecordStatus(Integer status, Integer ctime) {
        if (status != 0) {
            return status;
        }
        if (ServerConfig.isNotProduct()) {
            return DateHelper.getNowSeconds() - ctime > TimeUnit.MINUTES.toSeconds(5) ? 2 : 0;
        }
        return DateHelper.getNowSeconds() - ctime > TimeUnit.DAYS.toSeconds(1) ? 2 : 0;
    }

    public InvitedPageVO invitedPage(AgentDTO.InvitedPage dto) {
        InviteAnchorRecordData recordData = inviteAnchorRecordDao.selectById(dto.getInviteId());
        if (recordData == null) {
            logger.error("can not find invite record data. inviteId={}", dto.getInviteId());
            throw new CommonH5Exception(HttpCode.PARAM_ERROR);
        }
        ActorData agentUserInfo = actorDao.getActorDataFromCache(recordData.getUid());
        InvitedPageVO vo = new InvitedPageVO();
        vo.setInviteId(dto.getInviteId());
        vo.setAgentName(agentUserInfo != null ? agentUserInfo.getName() : "");
        vo.setAgentHead(agentUserInfo != null ? ImageUrlGenerator.generateRoomUserUrl(agentUserInfo.getHead()) : "");
        vo.setAgentRid(agentUserInfo != null ? agentUserInfo.getRid() + "" : null);
        return vo;
    }

    public void agreeAnchorInvite(AgentDTO.InvitedPage dto, String ipAddress) {
        ActorData actorData = actorDao.getActorDataFromCache(dto.getUid());
        if (actorData == null) {
            throw new CommonH5Exception(HttpCode.PARAM_ERROR);
        }
        InviteAnchorRecordData recordData = inviteAnchorRecordDao.selectById(dto.getInviteId());
        if (recordData == null || !recordData.getAid().equals(dto.getUid())) {
            logger.error("can not find invite record data. inviteId={}", dto.getInviteId());
            throw new CommonH5Exception(HttpCode.PARAM_ERROR);
        }
        int nowTime = DateHelper.getNowSeconds();
        int timeLimit = ServerConfig.isProduct() ? (int) TimeUnit.DAYS.toSeconds(1) : (int) TimeUnit.MINUTES.toSeconds(5);
        if (nowTime - recordData.getCtime() > timeLimit) {
            logger.error("The invitation has expired. inviteId={} nowTime={} ctime={}", dto.getInviteId(), nowTime, recordData.getCtime());
            throw new CommonH5Exception(RoomHttpCode.THE_INVITATION_HAS_EXPIRED);
        }
        FamilyMemberData memberData = familyMemberDao.selectByUid(dto.getUid());
        if (memberData != null) {
            logger.error("have joined another agent. uid={} familyId={}", dto.getUid(), memberData.getFamilyId());
            throw new CommonH5Exception(RoomHttpCode.HAVE_JOINED_ANOTHER_AGENT);
        }

        FamilyData agentData = familyDao.selectByOwnerUid(recordData.getUid());
        try {
            familyService.joinFamily(dto.getUid(), dto.getSlang(), agentData);
        } catch (CommonException e) {
            throw new CommonH5Exception(e.getHttpCode());
        }
        recordData.setStatus(1);
        inviteAnchorRecordDao.update(recordData);
        sendJoinSuccessMsg(actorData, actorDao.getActorDataFromCache(agentData.getOwnerUid()));
        // 绑定关系建立时，如果判断主播和该主播的代理IP地址相同，则该主播不产生魅力值分成，包括代理和上级代理；
        familyService.checkIpAddress(recordData.getUid(), recordData.getAid(), ipAddress);
    }

    public MemberBenefitsVO memberBenefitList(AgentDTO.Members dto) {
        Integer[] timeArr = getTimeArrByStrDate(dto.getStrDate(), dto.getStartDate(), dto.getEndDate());
        MemberBenefitsVO vo = new MemberBenefitsVO();
        if (dto.getType() == 0) {
            // 主播佣金
            vo.setMyCommission((int) charmStatDao.getUserTotalCharm(dto.getUid(), null, 6, timeArr[0], timeArr[1]));
        } else {
            // 代理佣金
            vo.setMyCommission((int) charmStatDao.getUserTotalCharm(dto.getUid(), null, 7, timeArr[0], timeArr[1]));
        }
        FamilyData familyData = familyDao.selectByOwnerUid(dto.getUid());
        if (familyData == null) {
            return vo;
        }
        if (StringUtils.hasLength(dto.getSearchKey())) {
            return memberBenefitSearch(vo, familyData.getId(), dto.getUid(), dto.getType(), dto.getSearchKey(), timeArr[0], timeArr[1]);
        }
        List<MemberBenefitsVO.MemberInfo> list;
        int size;
        int page = Math.max(dto.getPage(), 1);
        if (dto.getType() == 0) {
            List<FamilyMemberData> memberList = familyMemberDao.selectMemberPageList(familyData.getId(), null, page, PAGE_SIZE, 3);
            size = memberList.size();
            // if (size < PAGE_SIZE) {
            //     // 最后一页把退出公会的主播也加上
            //     Set<String> uidSet = anchorCharmLogDao.getMyIncomeFromUidSet(dto.getUid(), 6, timeArr[0], timeArr[1]);
            //     if (!CollectionUtils.isEmpty(uidSet)) {
            //         if (CollectionUtils.isEmpty(memberList)) {
            //             memberList = new ArrayList<>();
            //         }
            //         List<String> allMember = familyMemberDao.selectMemberUidList(familyData.getId());
            //         for (String aid : uidSet) {
            //             if (CollectionUtils.isEmpty(allMember) || !allMember.contains(aid)) {
            //                 FamilyMemberData memberData = new FamilyMemberData();
            //                 memberData.setFamilyId(familyData.getId());
            //                 memberData.setUid(aid);
            //                 memberData.setCtime(0);
            //                 memberList.add(memberData);
            //             }
            //         }
            //     }
            // }
            list = getFamilyMemberInfoList(memberList, dto.getUid(), timeArr[0], timeArr[1]);
        } else {
            List<FamilyData> agentFamilyList = familyDao.selectSubAgentPage(familyData.getId(), page, PAGE_SIZE, true);
            size = agentFamilyList.size();
            list = getAgentMemberInfoList(agentFamilyList, dto.getUid(), timeArr[0], timeArr[1]);
        }
        vo.setList(list);
        vo.setNextUrl(size >= PAGE_SIZE ? (page + 1) + "" : "");
        return vo;
    }

    public MemberBenefitsVO memberBenefitSearch(MemberBenefitsVO vo, int familyId, String uid, int type, String searchKey, int startTime, int endTime) {
        String aid = null;
        if (StringUtils.hasLength(searchKey)) {
            ActorData searchActor = actorDao.getActorByRidOrAlphaRid(searchKey);
            if (searchActor == null) {
                return vo;
            }
            aid = searchActor.getUid();
        }
        List<MemberBenefitsVO.MemberInfo> list;
        if (type == 0) {
            FamilyMemberData familyMemberData = familyMemberDao.selectByFamilyIdAndUid(familyId, aid);
            if (familyMemberData == null) {
                return vo;
            }
            list = getFamilyMemberInfoList(Collections.singletonList(familyMemberData), uid, startTime, endTime);
        } else {
            FamilyData familyData = familyDao.selectByOwnerUid(aid);
            if (familyData == null || familyData.getPid() != familyId) {
                return vo;
            }
            list = getAgentMemberInfoList(Collections.singletonList(familyData), uid, startTime, endTime);
        }
        vo.setList(list);
        return vo;
    }

    private List<MemberBenefitsVO.MemberInfo> getAgentMemberInfoList(List<FamilyData> agentMemberList, String uid, int startTime, int endTime) {
        List<MemberBenefitsVO.MemberInfo> list = new ArrayList<>();
        for (FamilyData agentData : agentMemberList) {
            String aid = agentData.getOwnerUid();
            ActorData actorData = actorDao.getActorDataFromCache(aid);
            if (actorData == null) {
                continue;
            }
            int startDate = charmStatDao.getIntDateByTimestamp(startTime);
            int endDate = charmStatDao.getIntDateByTimestamp(endTime);
            long benefit = familyDailyIncomeDao.getFamilyIncome(familyDao.findAllAgent(agentData.getId()), startDate, endDate);
            if (agentData.getStatus() == 2 && benefit <= 0) {
                continue;
            }
            MemberBenefitsVO.MemberInfo memberInfo = new MemberBenefitsVO.MemberInfo();
            memberInfo.setAid(aid);
            memberInfo.setRid(actorData.getRid());
            memberInfo.setName(actorData.getName());
            memberInfo.setHead(ImageUrlGenerator.generateRoomUserUrl(actorData));
            memberInfo.setAge(actorData.getAge());
            memberInfo.setCountry(actorData.getCountry());
            memberInfo.setGender(actorData.getFb_gender());
            memberInfo.setOnlineStatus(playerStatusRedis.getPlayerStatus(actorData.getUid()));
            memberInfo.setAnchorNum(familyMemberDao.selectMemberCount(agentData.getId()));
            memberInfo.setBenefit(benefit);
            memberInfo.setLevel(anchorWalletService.getAgentLevel(anchorWalletService.getAllAgentIncome(agentData.getId())));
            memberInfo.setMyCommission(getTotalCharmByUidAndAid(uid, aid, 7, startTime, endTime));
            list.add(memberInfo);
        }
        return list;
    }

    private int getTotalCharmByUidAndAid(String uid, String aid, int logType, int startTime, int endTime) {
        DataCenterCharmDTO dto = new DataCenterCharmDTO();
        dto.setUids(Collections.singleton(uid));
        dto.setAids(Collections.singleton(aid));
        dto.setLogTypes(Collections.singleton(logType));
        dto.setStartTime(startTime);
        dto.setEndTime(endTime);
        ApiResult<Long> result = dataCenterService.getCharmTotal(dto);
        if (result.isError()) {
            logger.error("getTotalCharmByUidAndAid error. uid={} aid={} logType={} startTime={} endTime={}", uid, aid, logType, startTime, endTime);
            return 0;
        }
        return result.getData().intValue();
    }

    private List<MemberBenefitsVO.MemberInfo> getFamilyMemberInfoList(List<FamilyMemberData> memberList, String uid, int startTime, int endTime) {
        List<MemberBenefitsVO.MemberInfo> list = new ArrayList<>();
        for (FamilyMemberData memberData : memberList) {
            String aid = memberData.getUid();
            ActorData actorData = actorDao.getActorDataFromCache(aid);
            if (actorData == null) {
                continue;
            }
            MemberBenefitsVO.MemberInfo memberInfo = new MemberBenefitsVO.MemberInfo();
            memberInfo.setAid(aid);
            memberInfo.setRid(actorData.getRid());
            memberInfo.setName(actorData.getName());
            memberInfo.setHead(ImageUrlGenerator.generateRoomUserUrl(actorData));
            memberInfo.setAge(actorData.getAge());
            memberInfo.setCountry(actorData.getCountry());
            memberInfo.setGender(actorData.getFb_gender());
            memberInfo.setOnlineStatus(playerStatusRedis.getPlayerStatus(aid));
            if (memberData.getCtime() != 0) {
                memberInfo.setLiveTime(micTimeDao.getUserUpMicTimeNew(aid, 2, DateSupport.ARABIAN.getIntDate(Math.max(startTime, memberData.getCtime())), DateSupport.ARABIAN.getIntDate(endTime)));
            } else {
                int joinTime = joinFamilyRecordDao.getJoinFamilyTime(memberData.getFamilyId(), aid);
                int quitTime = joinFamilyRecordDao.getQuitFamilyTime(memberData.getFamilyId(), aid);
                memberInfo.setLiveTime(micTimeDao.getUserUpMicTimeNew(aid, 2, DateSupport.ARABIAN.getIntDate(Math.max(startTime, joinTime)), DateSupport.ARABIAN.getIntDate(Math.min(endTime, quitTime))));
            }
            memberInfo.setInSelfRoomLiveTime(roomMicLogDao.getUpMicTotalTime(RoomUtils.formatRoomId(aid), aid, Math.max(startTime, memberData.getCtime()), endTime));

            memberInfo.setBenefit((int) charmStatDao.getUserReceivesGiftCharm(aid, memberData.getFamilyId(), startTime, endTime));
            memberInfo.setMyCommission(getTotalCharmByUidAndAid(uid, aid, 6, startTime, endTime));
            list.add(memberInfo);
        }
        return list;
    }

    public BenefitDetailVO memberBenefitDetail(AgentDTO.Members dto) {
        ActorData memberData = actorDao.getActorDataFromCache(dto.getAid());
        if (memberData == null) {
            throw new CommonException(HttpCode.PARAM_ERROR);
        }
        Integer[] timeArr = getTimeArrByStrDate(dto.getStrDate(), dto.getStartDate(), dto.getEndDate());
        int startTime = timeArr[0];
        int endTime = timeArr[1];
        int startDate = getIntDateByTimestamp(startTime);
        int endDate = getIntDateByTimestamp(endTime);
        BenefitDetailVO vo = new BenefitDetailVO();
        vo.setAid(memberData.getUid());
        vo.setRid(memberData.getRid());
        vo.setName(memberData.getName());
        vo.setHead(ImageUrlGenerator.generateRoomUserUrl(memberData.getHead()));
        vo.setAge(memberData.getAge());
        vo.setCountry(memberData.getCountry());
        vo.setGender(memberData.getFb_gender());
        AnchorWalletData anchorWalletData = anchorWalletService.getWallet(dto.getAid());
        vo.setCharmBalance(anchorWalletData != null ? anchorWalletData.getCharmBalance().intValue() : 0);
        FamilyMemberData familyMember = familyMemberDao.selectByUidFromCache(dto.getAid());
        vo.setLiveTime(micTimeDao.getUserUpMicTimeNew(dto.getAid(), 2, startDate, endDate));
        vo.setTotalGifters(dailySendGiftRecordDao.getReceiveGiftUserNum(dto.getAid(), startDate, endDate));
        vo.setTotalGameSpending(gameStatDao.getUserGameTotalCost(dto.getAid(), startTime, endTime));
        vo.setMsgCharmIncome((int) charmStatDao.getUserTotalCharm(dto.getAid(), memberData.getFamilyId(), 2, startTime, endTime));
        vo.setRoomCharmIncome((int) charmStatDao.getUserTotalCharm(dto.getAid(), memberData.getFamilyId(), 1, startTime, endTime));
        vo.setTotalCharmIncome(vo.getMsgCharmIncome() + vo.getRoomCharmIncome());
        return vo;
    }

    public int getIntDateByTimestamp(int timestamp) {
        return Integer.parseInt(DateSupport.yyyyMMdd(DateSupport.ARABIAN.getLocalDate(timestamp * 1000L)));
    }

    private Integer[] getTimeArrByStrDate(String strDate, String startDate, String endDate) {
        if (StringUtils.hasLength(strDate)) {
            return getTimeArrByStrDate(strDate);
        } else if (StringUtils.hasLength(startDate) && StringUtils.hasLength(endDate)) {
            return DateHelper.ARABIAN.getStartAndEndTime(startDate, endDate);
        } else {
            throw new CommonException(HttpCode.PARAM_ERROR);
        }
    }

    private Integer[] getTimeArrByStrDate(String strDate) {
        int nowTime = DateHelper.getNowSeconds();
        try {
            switch (strDate) {
                case "last_week" -> {
                    int startTime = DateHelper.ARABIAN.getWeekStartTime();
                    int endTime = DateHelper.ARABIAN.getWeekEndTime();
                    return new Integer[]{startTime - (int) TimeUnit.DAYS.toSeconds(7), endTime - (int) TimeUnit.DAYS.toSeconds(7)};
                }
                case "this_week" -> {
                    int startTime = DateHelper.ARABIAN.getWeekStartTime();
                    int endTime = DateHelper.ARABIAN.getWeekEndTime();
                    return new Integer[]{startTime, endTime};
                }
                case "last_month" -> {
                    return DateHelper.ARABIAN.getLastMonthStartAndEndTime();
                }
                case "30_days" -> {
                    return new Integer[]{(int) DateSupport.ARABIAN.getTimeSeconds(DateSupport.ARABIAN.getDayOffset(-29)), nowTime};
                }
                case "7_days" -> {
                    return new Integer[]{(int) DateSupport.ARABIAN.getTimeSeconds(DateSupport.ARABIAN.getDayOffset(-6)), nowTime};
                }
                default -> {
                    return DateHelper.ARABIAN.getStartAndEndTime(strDate, strDate);
                }
            }
        } catch (Exception e) {
            logger.error("strDate param error. strDate={}", strDate);
            throw new CommonException(HttpCode.PARAM_ERROR);
        }
    }

    public void sendJoinSuccessMsg(ActorData fromActor, ActorData toActor) {
        int slang = toActor.getSlang();
        String title = slang == SLangType.ARABIC ? JOIN_AGENT_TITLE_AR : JOIN_AGENT_TITLE;
        String desc = String.format(slang == SLangType.ARABIC ? JOIN_AGENT_DESC_AR : JOIN_AGENT_DESC, fromActor.getRid() + "");
        OfficialData officialData = new OfficialData();
        officialData.setTo_uid(toActor.getUid());
        officialData.setTitle(title);
        officialData.setBody(desc);
        officialData.setValid(1);
        officialData.setAtype(0);
        officialData.setNews_type(0);
        officialData.setCtime(DateHelper.getNowSeconds());
        officialMsgService.officialMsgPush(officialData);
    }

    public PageVO<HostDataVO> getHostsData(AgentDTO.HostsDTO dto) {
        if (!StringUtils.hasLength(dto.getStartDate()) || !StringUtils.hasLength(dto.getEndDate())) {
            throw new CommonException(HttpCode.PARAM_ERROR);
        }
        FamilyData familyData = familyDao.selectByOwnerUid(dto.getUid());
        if (familyData == null) {
            return new PageVO<>();
        }
        Integer[] timeArr = DateHelper.ARABIAN.getStartAndEndTime(dto.getStartDate(), dto.getEndDate());
        int startTime = timeArr[0];
        int endTime = timeArr[1];
        int liveCharmIncome = dto.getType() == 2 ? 1 : dto.getType() == 1 ? 0 : -1;
        Set<String> aidSet = new HashSet<>();
        if (StringUtils.hasLength(dto.getSearchKey())) {
            ActorData searchActor = actorDao.getActorByRidOrAlphaRid(dto.getSearchKey());
            if (searchActor != null) {
                aidSet.add(searchActor.getUid());
            }
        }
        Map<String, FamilyMemberData> memberMap = CollectionUtil.listToKeyMap(familyMemberDao.selectMemberList(familyData.getId()), FamilyMemberData::getUid);
        List<CharmIncomeData> dataList = charmStatDao.getFamilyMembersIncome(familyData.getId(), aidSet, liveCharmIncome, startTime, endTime);
        dataList = dataList.stream().filter(k -> {
                    if (dto.getType() == 0) {
                        return memberMap.containsKey(k.getUid());
                    } else {
                        return k.getTotalCharm() > 0 && memberMap.containsKey(k.getUid());
                    }
                }
        ).collect(Collectors.toList());
        int page = dto.getPage() <= 0 ? 1 : dto.getPage();
        int pageSize = dto.getSize() <= 0 ? 10 : dto.getSize();
        PageUtils.PageData<CharmIncomeData> iPage = PageUtils.getPageData(dataList, page, pageSize);
        List<HostDataVO> list = new ArrayList<>();
        for (CharmIncomeData data : iPage.list) {
            String aid = data.getUid();
            FamilyMemberData memberData = memberMap.get(aid);
            ActorData actorData = actorDao.getActorDataFromCache(aid);
            HostDataVO vo = new HostDataVO();
            vo.setAid(actorData.getUid());
            vo.setRid(actorData.getRid());
            vo.setName(actorData.getName());
            vo.setHead(ImageUrlGenerator.generateRoomUserUrl(actorData));
            vo.setAge(actorData.getAge());
            vo.setCountry(actorData.getCountry());
            vo.setGender(actorData.getFb_gender());
            vo.setOnlineStatus(playerStatusRedis.getPlayerStatus(aid));
            vo.setTotalIncome(charmStatDao.getUserReceivesGiftCharm(aid, memberData.getFamilyId(), startTime, endTime));
            vo.setMyCommission(getTotalCharmByUidAndAid(dto.getUid(), aid, CharmLogTypeEnum.HOST_EARNINGS.logType, startTime, endTime));
            vo.setTotalGifters(dailySendGiftRecordDao.getReceiveGiftUserNum(aid, getIntDateByTimestamp(startTime), getIntDateByTimestamp(endTime)));
            if (dto.getType() == 1) {
                vo.setPartyIncome(data.getTotalCharm());
                vo.setPartyHostDuration(roomMicLogDao.getUpMicTotalTime(RoomUtils.formatRoomId(aid), aid, Math.max(startTime, memberData.getCtime()), endTime));
            } else if (dto.getType() == 2) {
                vo.setLiveIncome(data.getTotalCharm());
                vo.setLiveDuration(micTimeDao.getUserUpMicTimeNew(aid, 1, DateSupport.ARABIAN.getIntDate(Math.max(startTime, memberData.getCtime())), DateSupport.ARABIAN.getIntDate(endTime)));
            } else {
                vo.setPartyIncome(charmStatDao.getChatCharmIncome(memberData.getFamilyId(), aid, startTime, endTime));
                vo.setLiveIncome(charmStatDao.getLiveCharmIncome(memberData.getFamilyId(), aid, startTime, endTime));
                vo.setPartyHostDuration(roomMicLogDao.getUpMicTotalTime(RoomUtils.formatRoomId(aid), aid, Math.max(startTime, memberData.getCtime()), endTime));
                vo.setLiveDuration(micTimeDao.getUserUpMicTimeNew(aid, 1, DateSupport.ARABIAN.getIntDate(Math.max(startTime, memberData.getCtime())), DateSupport.ARABIAN.getIntDate(endTime)));
            }
            list.add(vo);
        }
        return new PageVO<>(list, iPage.totalPage > page ? String.valueOf(page + 1) : "");
    }

    public void hostsDataDownload(AgentDTO.HostsDTO dto) {
        ActorData actorData = actorDao.getActorDataFromCache(dto.getUid());
        if (actorData == null) {
            logger.error("can not find actor data. uid={}", dto.getUid());
            throw new CommonH5Exception(HttpCode.PARAM_ERROR);
        }
        FamilyData familyData = familyDao.selectByOwnerUid(dto.getUid());
        if (familyData == null) {
            throw new CommonH5Exception(HttpCode.PARAM_ERROR);
        }
        // 校验邮箱地址格式
        if (!familyService.emailFormat(dto.getEmail())) {
            logger.error("Invalid email address.. uid={} email={}", dto.getUid(), dto.getEmail());
            throw new CommonH5Exception(RoomHttpCode.INVALID_EMAIL_ADDRESS);
        }
        dto.setPage(1);
        dto.setSize(10000);
        dto.setType(0);
        BaseTaskFactory.getFactory().addSlow(new Task() {
            @Override
            protected void execute() {
                Integer[] timeArr = DateHelper.ARABIAN.getStartAndEndTime(dto.getStartDate(), dto.getEndDate());
                int startTime = timeArr[0];
                int endTime = --timeArr[1];
                PageVO<HostDataVO> hostsData = getHostsData(dto);
                Map<String, String> receiveAccountMap = new HashMap<>(1);
                receiveAccountMap.put(dto.getEmail(), actorData.getName());
                String fileName = "_hosts_data_" + familyData.getRid() + "_" + DateHelper.ARABIAN.formatDateInDay(new Date(startTime * 1000L)) + "_"
                        + DateHelper.ARABIAN.formatDateInDay(new Date(endTime * 1000L));
                sundryService.exportHostsReport(hostsData.getList(), fileName, receiveAccountMap, actorData.getSlang());
            }
        });
    }

    public void dataDownload(AgentDTO.HostsDTO dto) {
        ActorData actorData = actorDao.getActorDataFromCache(dto.getUid());
        if (actorData == null) {
            logger.error("can not find actor data. uid={}", dto.getUid());
            throw new CommonH5Exception(HttpCode.PARAM_ERROR);
        }
        FamilyData familyData = familyDao.selectByOwnerUid(dto.getUid());
        if (familyData == null) {
            throw new CommonH5Exception(HttpCode.PARAM_ERROR);
        }
        // 校验邮箱地址格式
        if (!familyService.emailFormat(dto.getEmail())) {
            logger.error("Invalid email address.. uid={} email={}", dto.getUid(), dto.getEmail());
            throw new CommonH5Exception(RoomHttpCode.INVALID_EMAIL_ADDRESS);
        }
        dto.setPage(1);
        dto.setSize(10000);
        Integer[] timeArr = DateHelper.ARABIAN.getStartAndEndTime(dto.getStartDate(), dto.getEndDate());
        int startTime = timeArr[0];
        int endTime = --timeArr[1];
        Map<String, String> receiveAccountMap = new HashMap<>(1);
        receiveAccountMap.put(dto.getEmail(), actorData.getName());
        BaseTaskFactory.getFactory().addSlow(new Task() {
            @Override
            protected void execute() {
                if (dto.getDataType() == 1) {
                    List<FamilyData> agentFamilyList = familyDao.selectSubAgentPage(familyData.getId(), dto.getPage(), dto.getSize(), true);
                    List<MemberBenefitsVO.MemberInfo> dataList = getAgentMemberInfoList(agentFamilyList, dto.getUid(), timeArr[0], timeArr[1]);
                    List<AgentDataVO> list = new ArrayList<>();
                    dataList.forEach(k -> {
                        AgentDataVO vo = new AgentDataVO();
                        BeanUtils.copyProperties(k, vo);
                        list.add(vo);
                    });
                    String fileName = "_agents_data_" + familyData.getRid() + "_" + DateHelper.ARABIAN.formatDateInDay(new Date(startTime * 1000L)) + "_"
                            + DateHelper.ARABIAN.formatDateInDay(new Date(endTime * 1000L));
                    sundryService.exportAgentsReport(list, fileName, receiveAccountMap, actorData.getSlang());
                } else {
                    PageVO<HostDataVO> hostsData = getHostsData(dto);
                    String fileName = "_hosts_data_" + familyData.getRid() + "_" + DateHelper.ARABIAN.formatDateInDay(new Date(startTime * 1000L)) + "_"
                            + DateHelper.ARABIAN.formatDateInDay(new Date(endTime * 1000L));
                    sundryService.exportHostsReport(hostsData.getList(), fileName, receiveAccountMap, actorData.getSlang());
                }
            }
        });
    }
}
