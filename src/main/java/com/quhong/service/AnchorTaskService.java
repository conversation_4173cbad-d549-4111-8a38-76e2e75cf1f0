package com.quhong.service;

import com.google.common.collect.Interner;
import com.google.common.collect.Interners;
import com.quhong.config.AnchorTaskConfig;
import com.quhong.core.config.ServerConfig;
import com.quhong.core.date.DateSupport;
import com.quhong.core.utils.DateHelper;
import com.quhong.data.AnchorTaskData;
import com.quhong.enums.CharmLogTypeEnum;
import com.quhong.mongo.dao.CharmStatDao;
import com.quhong.mysql.dao.AnchorTaskRewardLogDao;
import com.quhong.mysql.dao.FamilyMemberDao;
import com.quhong.mysql.data.AnchorCharmLogData;
import com.quhong.mysql.data.AnchorTaskRewardLogData;
import com.quhong.mysql.data.FamilyMemberData;
import com.quhong.redis.AnchorTaskRedis;
import com.quhong.utils.ActorUtils;
import com.quhong.utils.RoomUtils;
import com.quhong.vo.AnchorTaskVO;
import jakarta.annotation.Resource;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.util.Comparator;
import java.util.concurrent.TimeUnit;

/**
 * <AUTHOR>
 * @date 2024/4/11
 */
@Service
public class AnchorTaskService {

    private final Logger logger = LoggerFactory.getLogger(getClass());

    private static final String ANCHOR_TASK_LOCK_KEY = "anchor_task_";
    private static final String VOICE_REWARD_TITLE = "Host Reward Task (Voice)";
    private static final String LIVE_REWARD_TITLE = "Host Reward Task (Live)";


    private static final Interner<String> stringPool = Interners.newWeakInterner();

    @Resource
    private AnchorTaskConfig anchorTaskConfig;
    @Resource
    private AnchorTaskRedis anchorTaskRedis;
    @Resource
    private AnchorWalletService anchorWalletService;
    @Resource
    private FamilyMemberDao familyMemberDao;
    @Resource
    private AnchorTaskRewardLogDao anchorTaskRewardLogDao;
    @Resource
    private CharmStatDao charmStatDao;

    /**
     * 最大奖励次数 (语聊房)
     */
    private int maxRewardNum;
    /**
     * 最大奖励次数 (直播房)
     */
    private int maxLiveRewardNum;

    public AnchorTaskVO taskInfo() {
        AnchorTaskVO vo = new AnchorTaskVO();
        vo.setTaskList(anchorTaskConfig.getTaskList());
        vo.setNewAnchorTask(anchorTaskConfig.getNewAnchorTask());
        vo.setLiveTaskList(anchorTaskConfig.getLiveTaskList());
        vo.setNewAnchorLiveTask(anchorTaskConfig.getNewAnchorLiveTask());
        return vo;
    }

    public void anchorUpMicTask(String roomId, String uid, int upMicMinutes) {
        logger.info("anchorUpMicTask. roomId={} uid={} upMicMinutes={}", roomId, uid, upMicMinutes);
        synchronized (stringPool.intern(ANCHOR_TASK_LOCK_KEY + uid)) {
            FamilyMemberData familyMemberData = familyMemberDao.selectByUid(uid);
            if (familyMemberData == null) {
                return;
            }
            if (RoomUtils.isLiveRoom(roomId)) {
                // 直播任务
                int gainRewardNum = anchorTaskRedis.getGainLiveRewardNum(uid);
                if (gainRewardNum >= getMaxLiveRewardNum()) {
                    return;
                }
                int upMicTime = anchorTaskRedis.incAnchorLiveTime(uid, upMicMinutes);
                int upMicHours = ServerConfig.isProduct() ? upMicTime / 60 : upMicTime / 5;
                if (upMicHours < 1 || upMicHours <= gainRewardNum) {
                    return;
                }
                int totalIncome = getAnchorLiveRoomTotalIncome(uid, familyMemberData.getFamilyId());
                AnchorTaskData taskData = getLiveTaskData(uid, totalIncome);
                if (taskData == null) {
                    return;
                }
                int rewardNum = Math.min(taskData.getRewardNum(), upMicHours) - gainRewardNum;
                if (rewardNum <= 0) {
                    return;
                }
                logger.info("anchorLiveTask. uid={} liveHours={} totalIncome={}", uid, upMicHours, totalIncome);
                for (int i = 1; i <= rewardNum; i++) {
                    // 下发魅力值奖励
                    anchorWalletService.changeCharm(uid, BigDecimal.valueOf(taskData.getRewardCharm()), CharmLogTypeEnum.getLogData(familyMemberData.getFamilyId(), uid, CharmLogTypeEnum.HOST_REWARD_TASK_LIVE));
                    anchorTaskRedis.incGainLiveRewardNum(uid);
                    logger.info("get anchor live task reward. uid={}, rewardCharm={}, level={}, target={}", uid, taskData.getRewardCharm(), taskData.getLevel(), taskData.getTarget());
                    // 保存获取奖励记录
                    saveAnchorTaskRewardLog(uid, 1, totalIncome, upMicHours, taskData.getRewardCharm(), taskData.getTarget(), taskData.getLevel());
                }
            } else {
                // 语聊房上麦任务
                int gainRewardNum = anchorTaskRedis.getGainRewardNum(uid);
                if (gainRewardNum >= getMaxRewardNum()) {
                    return;
                }
                int upMicTime = anchorTaskRedis.incAnchorUpMicTime(uid, upMicMinutes);
                int upMicHours = ServerConfig.isProduct() ? upMicTime / 60 : upMicTime / 5;
                if (upMicHours < 1 || upMicHours <= gainRewardNum) {
                    return;
                }
                int totalIncome = getAnchorChatRoomTotalIncome(uid, familyMemberData.getFamilyId());
                logger.info("getAnchorChatRoomTotalIncome. uid={} totalIncome={}", uid, totalIncome);
                AnchorTaskData taskData = getTaskData(uid, totalIncome);
                if (taskData == null) {
                    return;
                }
                int rewardNum = Math.min(taskData.getRewardNum(), upMicHours) - gainRewardNum;
                if (rewardNum <= 0) {
                    return;
                }
                logger.info("anchorUpMicTask. uid={} upMicHours={} totalIncome={}", uid, upMicHours, totalIncome);
                for (int i = 1; i <= rewardNum; i++) {
                    // 下发魅力值奖励
                    anchorWalletService.changeCharm(uid, BigDecimal.valueOf(taskData.getRewardCharm()), CharmLogTypeEnum.getLogData(familyMemberData.getFamilyId(), uid, CharmLogTypeEnum.HOST_REWARD_TASK_VOICE));
                    anchorTaskRedis.incGainRewardNum(uid);
                    logger.info("get anchor task reward. uid={}, rewardCharm={}, level={}, target={}", uid, taskData.getRewardCharm(), taskData.getLevel(), taskData.getTarget());
                    // 保存获取奖励记录
                    saveAnchorTaskRewardLog(uid, 0, totalIncome, upMicHours, taskData.getRewardCharm(), taskData.getTarget(), taskData.getLevel());
                }
            }
        }
    }

    private void saveAnchorTaskRewardLog(String uid, int type, int totalIncome, int upMicHours, int rewardCharm, int target, String rewardLevel) {
        AnchorTaskRewardLogData logData = new AnchorTaskRewardLogData();
        logData.setUid(uid);
        logData.setStrDate(DateHelper.ARABIAN.formatDateInDay());
        logData.setType(type);
        logData.setUpMicTime(upMicHours);
        logData.setTotalIncome(totalIncome);
        logData.setRewardCharm(rewardCharm);
        logData.setRewardLevel(rewardLevel);
        logData.setRewardTarget(target);
        logData.setCtime(DateHelper.getNowSeconds());
        anchorTaskRewardLogDao.insert(logData);
    }

    private AnchorTaskData getTaskData(String uid, int totalIncome) {
        // 普通主播
        for (int i = 0; i < anchorTaskConfig.getTaskList().size(); i++) {
            if (totalIncome >= anchorTaskConfig.getTaskList().get(i).getTarget()) {
                return anchorTaskConfig.getTaskList().get(i);
            }
        }
        if (ActorUtils.isNewRegisterActor(uid, 7)) {
            // 新主播
            return totalIncome >= anchorTaskConfig.getNewAnchorTask().getTarget() ? anchorTaskConfig.getNewAnchorTask() : null;
        }
        return null;
    }

    private AnchorTaskData getLiveTaskData(String uid, int totalIncome) {
        // 普通主播
        for (int i = 0; i < anchorTaskConfig.getLiveTaskList().size(); i++) {
            if (totalIncome >= anchorTaskConfig.getLiveTaskList().get(i).getTarget()) {
                return anchorTaskConfig.getLiveTaskList().get(i);
            }
        }
        if (ActorUtils.isNewRegisterActor(uid, 30)) {
            // 新主播
            for (int i = 0; i < anchorTaskConfig.getNewAnchorLiveTask().size(); i++) {
                if (totalIncome >= anchorTaskConfig.getNewAnchorLiveTask().get(i).getTarget()) {
                    return anchorTaskConfig.getNewAnchorLiveTask().get(i);
                }
            }
        }
        return null;
    }

    /**
     * 最近7天主播的语聊房总收益
     * 近7天 = 今天 + 前6天
     */
    private int getAnchorChatRoomTotalIncome(String uid, int familyId) {
        int nowSeconds = DateHelper.getNowSeconds();
        int startTime = (int)(DateSupport.ARABIAN.getTodayStartTime() - TimeUnit.DAYS.toSeconds(6));
        int totalCharmTotal = (int) charmStatDao.getUserTotalCharm(uid, familyId, 1,  startTime, nowSeconds);
        int liveCharm = (int) charmStatDao.getLiveCharmIncome(familyId, uid, startTime, nowSeconds);
        return totalCharmTotal - liveCharm;
    }

    /**
     * 最近7天主播的直播房总收益
     * 近7天 = 今天 + 前6天
     */
    private int getAnchorLiveRoomTotalIncome(String uid, int familyId) {
        int nowSeconds = DateHelper.getNowSeconds();
        int startTime = (int)(DateSupport.ARABIAN.getTodayStartTime() - TimeUnit.DAYS.toSeconds(6));
        return (int) charmStatDao.getLiveCharmIncome(familyId, uid, startTime, nowSeconds);
    }

    private int getMaxRewardNum() {
        if (maxRewardNum > 0) {
            return maxRewardNum;
        }
        this.maxRewardNum = anchorTaskConfig.getTaskList().stream().max(Comparator.comparingInt(AnchorTaskData::getRewardNum)).get().getRewardNum();
        this.maxRewardNum = Math.max(anchorTaskConfig.getNewAnchorTask().getRewardNum(), maxRewardNum);
        return maxRewardNum;
    }

    private int getMaxLiveRewardNum() {
        if (maxLiveRewardNum > 0) {
            return maxLiveRewardNum;
        }
        int maxRewardNum = anchorTaskConfig.getLiveTaskList().stream().max(Comparator.comparingInt(AnchorTaskData::getRewardNum)).get().getRewardNum();
        int newAnchorMaxRewardNum = anchorTaskConfig.getNewAnchorLiveTask().stream().max(Comparator.comparingInt(AnchorTaskData::getRewardNum)).get().getRewardNum();
        this.maxLiveRewardNum = Math.max(maxRewardNum, newAnchorMaxRewardNum);
        return maxLiveRewardNum;
    }
}
