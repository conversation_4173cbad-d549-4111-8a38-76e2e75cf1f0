package com.quhong.service;

import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.quhong.data.vo.RewardListVO;
import com.quhong.mysql.data.MomentRewardData;
import com.quhong.mysql.mapper.waho.MomentRewardMapper;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;

import java.util.Collections;
import java.util.List;

@Service
public class MomentRewardService extends ServiceImpl<MomentRewardMapper, MomentRewardData> {
    private static final Logger logger = LoggerFactory.getLogger(MomentRewardService.class);
    public static final int PAGE_SIZE = 10;

    public void deleteMomentReward(String mid) {
        try {
            QueryWrapper<MomentRewardData> queryWrapper = new QueryWrapper<>();
            queryWrapper.eq("mid", mid);
            remove(queryWrapper);
        } catch (Exception e) {
            logger.error("delete moment reward data error. mid={}", mid, e);
        }
    }

    public void saveMomentReward(MomentRewardData momentRewardData) {
        try {
            save(momentRewardData);
        } catch (Exception e) {
            logger.error("save moment reward data error. data={}", JSON.toJSONString(momentRewardData), e);
        }
    }

    public List<MomentRewardData> getLastSendList(String mid) {
        try {
            return getBaseMapper().getLastSendList(mid);
        } catch (Exception e) {
            logger.error("get last send list error mid={}", mid, e);
            return Collections.emptyList();
        }
    }

    public List<RewardListVO.GiftRewardVO> getRewardList(String mid, int page) {
        try {
            return getBaseMapper().getRewardListByValue(mid, (page == 0 ? 1 : page - 1) * PAGE_SIZE, PAGE_SIZE);
        } catch (Exception e) {
            logger.error("get reward list error mid={} page={}", mid, page, e);
            return Collections.emptyList();
        }
    }
}
