package com.quhong.service;

import com.quhong.core.concurrency.BaseTaskFactory;
import com.quhong.core.concurrency.tasks.Task;
import com.quhong.data.ActorData;
import com.quhong.data.RoomRoleData;
import com.quhong.data.dto.RoomKickDTO;
import com.quhong.data.vo.KickUserInfoVO;
import com.quhong.enums.HttpCode;
import com.quhong.enums.RoomHttpCode;
import com.quhong.enums.RoomRoleType;
import com.quhong.exception.CommonException;
import com.quhong.feign.IGameService;
import com.quhong.mongo.dao.ActorDao;
import com.quhong.mongo.dao.MongoRoomDao;
import com.quhong.mongo.dao.RoomMemberDao;
import com.quhong.mongo.data.MongoRoomData;
import com.quhong.mysql.dao.VipInfoDao;
import com.quhong.redis.MysteryRedis;
import com.quhong.redis.WhitelistRedis;
import com.quhong.room.RoomWebSender;
import com.quhong.room.redis.RoomKickRedis;
import jakarta.annotation.Resource;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2022/7/25
 */
@Service
public class RoomKickService {

    private static final Logger logger = LoggerFactory.getLogger(RoomKickService.class);
    // 正式服用户：1001224、1001225
    public static final List<String> POWER_USER = List.of("64a56280ff4d632669c8a039", "64c34444c3f5a27c5acaa586", "64e859dd35660c5d64ad1f28", "64a3c933bfce332143625a24", "64c3577b0d86d537282d51bb", "64c3577b0d86d537282d51bc");

    @Resource
    private RoomMemberDao memberDao;
    @Resource
    private VipInfoDao vipInfoDao;
    @Resource
    private ActorDao actorDao;
    @Resource
    private RoomKickRedis roomKickRedis;
    @Resource
    private RoomWebSender roomWebSender;
    @Resource
    private MongoRoomDao roomDao;
    @Resource
    private RoomBlacklistService roomBlacklistService;
    @Resource
    private RoomNewService roomNewService;
    @Resource
    private IGameService gameService;
    @Resource
    private MysteryRedis mysteryRedis;
    @Autowired
    private SvipLevelService svipLevelService;
    @Resource
    private WhitelistRedis whitelistRedis;

    /**
     * 房间内踢人
     */
    public void roomKick(RoomKickDTO req) {
        String uid = req.getUid();
        String aid = req.getAid();
        String roomId = req.getRoomId();
        MongoRoomData roomData = roomDao.getDataFromCache(roomId);
        if (roomData == null) {
            logger.error("can not find room info. roomId={}", roomId);
            throw new CommonException(RoomHttpCode.PARAM_ERROR);
        }
        if (whitelistRedis.isInvisibilityEnterRoom(req.getRoomId(), req.getUid())) {
            // 隐身进房无法操作
            throw new CommonException(HttpCode.INVISIBLE_ACCOUNT_CANNOT_BE_OPERATED);
        }
        RoomRoleData uRoleData = memberDao.getRoleDataFromCache(roomId, uid);
        boolean isOwner = uRoleData.getRole() == RoomRoleType.HOST;
        boolean isManager = uRoleData.getRole() == RoomRoleType.MANAGER;
        boolean official = RoomKickService.POWER_USER.contains(uid);
        if (!official && !isOwner) {
            if (!isManager) {
                // 既不是管理员也不是房主
                logger.info("only room owner or room admin can kick user. uid={} roomId={}", uid, roomId);
                throw new CommonException(RoomHttpCode.NO_PERMISSION_KICK_USER);
            } else {
                // 是管理员不是房主
                RoomRoleData aRoleData = memberDao.getRoleDataFromCache(roomId, aid);
                if (aRoleData.getRole() <= uRoleData.getRole()) {
                    // 非房主不能踢管理员
                    logger.info("You can not kick admin user. uid={} roomId={} aid={}", uid, roomId, aid);
                    throw new CommonException(RoomHttpCode.NOT_KICK_ADMIN_USER);
                }
            }
            // 新版只有房主才能添加进房间黑名单
            if (req.getForbid() == 1) {
                throw new CommonException(RoomHttpCode.ROOM_BLOCK_LIST_ADD_LIMIT);
            }
        }
        if (!official && svipLevelService.getSvipLevel(aid) >= 6) {
            throw new CommonException(RoomHttpCode.KICK_SVIP_LIMIT);
        }
        if (!official && vipInfoDao.getIntVipLevelFromCache(aid) >= 6) {
            // vip6 以上防踢
            if (req.getForbid() == 1) {
                roomBlacklistService.addRoomBlackList(roomId, aid, uid);
            }
            logger.info("Sorry, Tycoon 6 and above is anti-kickout from the room. uid={} roomId={} aid={}", uid, roomId, aid);
            throw new CommonException(RoomHttpCode.VIP_USER_CANNOT_BE_KICK, "VIP6");
        }
        // 官方指定内部ID可把神秘人踢出房间
        if (!official && null != mysteryRedis.getMysteryId(aid)) {
            // 尊贵的SVIP神秘人，无法踢出房间
            throw new CommonException(RoomHttpCode.KICK_MYSTERY_LIMIT);
        }
        BaseTaskFactory.getFactory().addSlow(new Task() {
            @Override
            protected void execute() {
                // 踢人
                roomKickRedis.roomKick(roomId, aid);
                // 记录踢人
                roomKickRedis.setKickRecord(roomId, uid, aid);
                if (req.getForbid() == 1) {
                    roomBlacklistService.addRoomBlackList(roomId, aid, uid);
                }
                // 踢人出房间
                sendKickFromRoom(roomId, uid, aid, official);
                // 玩家正在游戏中，执行逃跑处理，游戏结束时退还游戏费用
                String gameId = roomNewService.getGameId(roomId);
                if (!StringUtils.isEmpty(gameId)) {
                    gameService.escapeGame(aid, roomId, gameId);
                }
            }
        });
        logger.info("finish kick out actor. roomId={} uid={} aid={}", roomId, uid, aid);
    }

    private void sendKickFromRoom(String roomId, String fromUid, String toUid, boolean official) {
        String path = "kick_actor";
        Map<String, String> params = new HashMap<>();
        params.put("room_id", roomId);
        params.put("from_uid", fromUid);
        params.put("to_uid", toUid);
        params.put("official", String.valueOf(official));
        roomWebSender.sendPost(path, roomId, params);
    }

    /**
     * 查询踢出用户的人
     */
    public KickUserInfoVO kickUserInfo(RoomKickDTO req) {
        KickUserInfoVO vo = new KickUserInfoVO();
        String uid = req.getUid();
        String roomId = req.getRoom_id();
        String aid = roomKickRedis.getFromKickUid(roomId, uid);
        if (StringUtils.isEmpty(aid)) {
            logger.info("message expired. uid={} roomId={}", uid, roomId);
            throw new CommonException(RoomHttpCode.MESSAGE_EXPIRED);
        }
        ActorData actorData = actorDao.getActorDataFromCache(aid);
        if (actorData == null) {
            logger.error("not find actor info. uid={}", uid);
            throw new CommonException(HttpCode.PARAM_ERROR);
        }
        vo.setHead(actorData.getHead());
        vo.setName(actorData.getName());
        return vo;
    }

    /**
     * 检测自己是否被踢
     */
    public void kickCheck(RoomKickDTO req) {
        String uid = req.getUid();
        String roomId = req.getRoomId();
        String fromKickRoomId = roomKickRedis.getFromKickRoomId(uid);
        if (StringUtils.isEmpty(fromKickRoomId) || !roomId.equals(fromKickRoomId)) {
            logger.info("no record of kick. uid={} roomId={}", uid, roomId);
            throw new CommonException(RoomHttpCode.NO_RECORD_OF_KICK);
        }
    }
}
