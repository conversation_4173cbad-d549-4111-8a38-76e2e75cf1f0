package com.quhong.service;

import com.alibaba.fastjson.JSONObject;
import com.google.common.collect.Interner;
import com.google.common.collect.Interners;
import com.quhong.analysis.ActivitySpecialItemsChangeEvent;
import com.quhong.analysis.EventDTO;
import com.quhong.constant.ActivityHttpCode;
import com.quhong.core.config.ServerConfig;
import com.quhong.core.date.DateSupport;
import com.quhong.core.utils.DateHelper;
import com.quhong.data.ActorData;
import com.quhong.data.CommonMqTopicData;
import com.quhong.data.SendGiftData;
import com.quhong.data.vo.DrawRecordVO;
import com.quhong.data.vo.PrizeConfigVO;
import com.quhong.data.vo.RamadanStoreVO;
import com.quhong.enums.BaseDataResourcesConstant;
import com.quhong.enums.HttpCode;
import com.quhong.enums.ResTypeEnum;
import com.quhong.enums.SLangType;
import com.quhong.exception.CommonH5Exception;
import com.quhong.mongo.data.OtherRankingActivityData;
import com.quhong.mongo.data.ResourceKeyConfigData;
import com.quhong.mysql.dao.WhiteTestDao;
import com.quhong.redis.RamadanStoreRedis;
import com.quhong.utils.CollectionUtil;
import com.quhong.vo.PageVO;
import jakarta.annotation.Resource;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * 斋月活动
 *
 * <AUTHOR>
 * @date 2025/3/7
 */
@Service
public class RamadanStoreService extends OtherActivityService implements SendGiftActivity, ActivitySettlement {

    private final Logger logger = LoggerFactory.getLogger(getClass());

    private static final String ACTIVITY_ID = ServerConfig.isProduct() ? "67d3e4b2d3090000ee006a68" : "67d00942d3090000ee006a56";
    private static final String ACTIVITY_NAME = "Ramadan Store";

    private static final Interner<String> stringPool = Interners.newWeakInterner();

    private static final int EXCHANGE_POINTS_NEED_BEANS = 2000;  // 送礼x钻石获得1个兑换积分
    private static final String EXCHANGE_KEY = "RamadanStoreExchange";  // 可兑换资源key
    private static final String EXCHANGE_REWARD_TITLE = "Ramadan Store-exchange";

    private static final String GAME_RANKING = "game_ranking";
    private static final String RANKING_REWARD_KEY = "RamadanStoreTop%s";
    private static final String RANKING_REWARD_TITLE = "Ramadan Store-rank";
    private static final Integer RECORD_PAGE_SIZE = 10;

    private static final String EXCHANGE_POINTS = "exchange_points"; // 兑换积分
    private static final String EXTRA_POINTS = "extra_points"; // 礼物多余积分
    private static final String BUY_RES_DATE = "buy_res_%s"; // 在商店购买资源的日期
    private static final String BUY_RES_DAY_NUM = "buy_res_day_num"; // 在商店购买资源的天数
    private static final String VIP_EXCHANGE_NUM = "vip_exchange_num_%s"; // vip资源兑换次数

    private static final List<RamadanStoreVO.Box> BOX_LIST = List.of(
            new RamadanStoreVO.Box("RamadanStoreBuy1", 1),
            new RamadanStoreVO.Box("RamadanStoreBuy3", 3),
            new RamadanStoreVO.Box("RamadanStoreBuy5", 5)
    );
    private static final String TASK_REWARD_TITLE = "Ramadan Store-buy task";

    @Resource
    private RamadanStoreRedis ramadanStoreRedis;
    @Resource
    private RiskControlService riskControlService;
    @Resource
    private ActivityMsgService activityMsgService;
    @Resource
    private WhiteTestDao whiteTestDao;

    @Override
    public String getActivityName() {
        return ACTIVITY_NAME;
    }

    /**
     * 获取活动信息
     */
    public RamadanStoreVO getInfo(String activityId, String uid) {
        OtherRankingActivityData activity = getOtherRankingActivity(activityId);
        ActorData actorData = actorDao.getActorDataFromCache(uid);
        if (actorData == null) {
            throw new CommonH5Exception(HttpCode.PARAM_ERROR);
        }
        return buildRamadanStoreVO(activityId, activity, actorData);
    }

    /**
     * 领取宝箱奖励
     */
    public RamadanStoreVO getBoxReward(String activityId, String uid, String key) {
        OtherRankingActivityData activity = getOtherRankingActivity(activityId);
        checkActivityTime(activity);
        ActorData actorData = actorDao.getActorDataFromCache(uid);
        if (actorData == null) {
            throw new CommonH5Exception(HttpCode.PARAM_ERROR);
        }
        Map<String, RamadanStoreVO.Box> boxMap = CollectionUtil.listToKeyMap(BOX_LIST, RamadanStoreVO.Box::getKey);
        RamadanStoreVO.Box box = boxMap.get(key);
        if (box == null) {
            throw new CommonH5Exception(HttpCode.PARAM_ERROR);
        }
        synchronized (stringPool.intern(uid)) {
            long buyResDayNum = ramadanStoreRedis.getUserData(activityId, uid, BUY_RES_DAY_NUM);
            if (buyResDayNum < box.getLimit()) {
                throw new CommonH5Exception(ActivityHttpCode.PARAM_ERROR);
            }
            if (ramadanStoreRedis.incUserData(activityId, uid, box.getKey(), 1) == 1) {
                // 发送宝箱奖励
                resourceKeyHandlerService.sendResourceData(uid, box.getKey(), TASK_REWARD_TITLE, TASK_REWARD_TITLE);
            }
        }
        return buildRamadanStoreVO(activityId, activity, actorData);
    }

    /**
     * 兑换奖励
     */
    public RamadanStoreVO exchange(String activityId, String uid, String rewardKey) {
        OtherRankingActivityData activity = getOtherRankingActivity(activityId);
        checkActivityTime(activity);
        ActorData actorData = actorDao.getActorDataFromCache(uid);
        if (actorData == null) {
            throw new CommonH5Exception(HttpCode.PARAM_ERROR);
        }
        ResourceKeyConfigData resourceKeyConfigData = resourceKeyHandlerService.getConfigData(EXCHANGE_KEY);
        if (resourceKeyConfigData == null) {
            throw new CommonH5Exception(HttpCode.PARAM_ERROR.getCode(), "Not Find Exchange Item");
        }
        Map<String, ResourceKeyConfigData.ResourceMeta> metaMap = resourceKeyConfigData.getResourceMetaList().stream().collect(Collectors.toMap(ResourceKeyConfigData.ResourceMeta::getMetaId, Function.identity()));
        ResourceKeyConfigData.ResourceMeta resourceMeta = metaMap.get(rewardKey);
        if (resourceMeta == null) {
            throw new CommonH5Exception(ActivityHttpCode.PARAM_ERROR);
        }
        int costPoints;
        synchronized (stringPool.intern(uid)) {
            if (resourceMeta.getResourceType() == BaseDataResourcesConstant.TYPE_VIP_LEVEL && ramadanStoreRedis.getUserData(activityId, uid, VIP_EXCHANGE_NUM.formatted(resourceMeta.getResourceId())) >= 5) {
                throw new CommonH5Exception(new HttpCode(1, " يمكن استبدال مكافأة الـ VIP بحد أقصى 5 مرات خلال فترة النشاط."));
            }
            long curPoints = ramadanStoreRedis.getUserData(activityId, uid, EXCHANGE_POINTS);
            costPoints = Integer.parseInt(resourceMeta.getRateNumber());
            if (curPoints - costPoints < 0) {
                throw new CommonH5Exception(ActivityHttpCode.NOT_ENOUGH_POINTS_TO_EXCHANGE);
            }
            // 扣减用户兑换积分
            ramadanStoreRedis.incUserData(activityId, uid, EXCHANGE_POINTS, -costPoints);
            doReportItemsChangeEvent(uid, 2, costPoints, -1);
            // 下发兑换商品
            resourceKeyHandlerService.sendOneResourceData(uid, resourceMeta, 905, EXCHANGE_REWARD_TITLE, "", EXCHANGE_REWARD_TITLE, EXCHANGE_REWARD_TITLE, "", "", 1, activityId);
            if (resourceMeta.getResourceType() == BaseDataResourcesConstant.TYPE_VIP_LEVEL) {
                ramadanStoreRedis.incUserData(activityId, uid, VIP_EXCHANGE_NUM.formatted(resourceMeta.getResourceId()), 1);
            }
        }
        PrizeConfigVO reward = new PrizeConfigVO();
        reward.setRewardType(String.valueOf(resourceMeta.getResourceType()));
        ResTypeEnum typeEnum = ResTypeEnum.getByType(resourceMeta.getResourceType());
        if (typeEnum != null && typeEnum != ResTypeEnum.BAG_GIFT) {
            reward.setNameEn(typeEnum.formatTag(SLangType.ENGLISH, resourceMeta.getResourceTime()));
            reward.setNameAr(typeEnum.formatTag(SLangType.ARABIC, resourceMeta.getResourceTime()));
        } else {
            reward.setNameEn(ResTypeEnum.DIAMONDS.formatTag(SLangType.ENGLISH, resourceMeta.getResourceNumber()));
            reward.setNameAr(ResTypeEnum.DIAMONDS.formatTag(SLangType.ARABIC, resourceMeta.getResourceNumber()));
        }
        reward.setIconEn(resourceMeta.getResourceIcon());
        reward.setRewardTime(resourceMeta.getResourceTime());
        reward.setRewardNum(resourceMeta.getResourceNumber());
        reward.setRewardPrice(resourceMeta.getResourcePrice());
        ramadanStoreRedis.saveExchangeRecord(activityId, uid, JSONObject.toJSONString(new DrawRecordVO(reward, costPoints, "", DateHelper.getNowSeconds())));
        RamadanStoreVO vo = buildRamadanStoreVO(activityId, activity, actorData);
        return vo;
    }

    /**
     * 兑换记录
     */
    public PageVO<DrawRecordVO> getExchangeRecord(String activityId, String uid, int page) {
        PageVO<DrawRecordVO> pageVO = new PageVO<>();
        List<DrawRecordVO> list = new ArrayList<>();
        int start = (page - 1) * RECORD_PAGE_SIZE;
        int end = page * RECORD_PAGE_SIZE;
        List<String> recordList = ramadanStoreRedis.getExchangeRecord(activityId, uid, start, end);
        for (String record : recordList) {
            list.add(JSONObject.parseObject(record, DrawRecordVO.class));
        }
        pageVO.setList(list);
        pageVO.setNextUrl(list.size() < RECORD_PAGE_SIZE ? "" : String.valueOf(page + 1));
        return pageVO;
    }

    /**
     * 处理玩Greedy游戏赢的消息
     */
    public void handleMqMsg(CommonMqTopicData data) {
        OtherRankingActivityData activity = getOtherRankingActivity(ACTIVITY_ID);
        if (!activityIsRunning(activity)) {
            return;
        }
        // if (activity.getAcNameEn().startsWith("test") && !whiteTestDao.isMemberByType(data.getUid(), WhiteTestDao.WHITE_TYPE_RID)) {
        //     return;
        // }
        int value = Math.abs(data.getValue());
        int beforeRank = ramadanStoreRedis.getRankingRank(ACTIVITY_ID, GAME_RANKING, data.getUid());
        Map<String, Long> beforeRankingMap = ramadanStoreRedis.getRankingMap(ACTIVITY_ID, GAME_RANKING, 10);
        ramadanStoreRedis.incrRankingScore(ACTIVITY_ID, GAME_RANKING, data.getUid(), value);
        int afterRank = ramadanStoreRedis.getRankingRank(ACTIVITY_ID, GAME_RANKING, data.getUid());
        if (afterRank <= 10 && afterRank < beforeRank) {
            logger.info("rank change. aid={} rankType={} beforeRank={} afterRank={}", data.getUid(), GAME_RANKING, beforeRank, afterRank);
            Map<String, Long> newRankingMap = ramadanStoreRedis.getRankingMap(ACTIVITY_ID, GAME_RANKING, 10);
            activityMsgService.doSendMsg(data.getRoomId(), activity, data.getUid(), afterRank, beforeRankingMap, newRankingMap);
        }
    }

    public void buyStoreResource(String uid) {
        OtherRankingActivityData activity = getOtherRankingActivity(ACTIVITY_ID);
        if (!activityIsRunning(activity)) {
            return;
        }
        // if (activity.getAcNameEn().startsWith("test") && !whiteTestDao.isMemberByType(uid, WhiteTestDao.WHITE_TYPE_RID)) {
        //     return;
        // }
        String strToday = DateSupport.ARABIAN.getStrToday();
        if (ramadanStoreRedis.incUserData(ACTIVITY_ID, uid, BUY_RES_DATE.formatted(strToday), 1) == 1) {
            ramadanStoreRedis.incUserData(ACTIVITY_ID, uid, BUY_RES_DAY_NUM, 1);
        }
    }

    @Override
    public void sendGiftHandle(String activityId, SendGiftData giftData) {
        String uid = giftData.getFrom_uid();
        synchronized (stringPool.intern(uid)) {
            int sendBeans = giftData.getNumber() * giftData.getPrice() * giftData.getAid_list().size();
            Map<String, Long> userMapData = ramadanStoreRedis.getUserData(activityId, uid);
            int extraPoints = userMapData.getOrDefault(EXTRA_POINTS, 0L).intValue();
            long totalBeans = extraPoints + sendBeans;
            int incNum = (int)(totalBeans / EXCHANGE_POINTS_NEED_BEANS);
            extraPoints = (int)(totalBeans % EXCHANGE_POINTS_NEED_BEANS);
            if (incNum != 0) {
                // 增加兑换积分
                ramadanStoreRedis.incUserData(activityId, uid, EXCHANGE_POINTS, incNum);
                doReportItemsChangeEvent(uid, 1, incNum, 1);
            }
            ramadanStoreRedis.setUserData(activityId, uid, EXTRA_POINTS, extraPoints);
        }
    }

    @Override
    public void settlementReward(String activityId) {
        try {
            Map<String, Long> rankingMap = ramadanStoreRedis.getRankingMap(activityId, GAME_RANKING, 10);
            int rank = 1;
            for (Map.Entry<String, Long> entry : rankingMap.entrySet()) {
                String rewardKey = RANKING_REWARD_KEY.formatted(rank);
                String aid = entry.getKey();
                logger.info("sendRankingReward. aid={} rankType={} score={} rank={} rewardKey={}", aid, GAME_RANKING, entry.getValue(), rank, rewardKey);
                resourceKeyHandlerService.sendResourceData(aid, rewardKey, RANKING_REWARD_TITLE, RANKING_REWARD_TITLE);
                rank++;
            }
            // 发送活动结束榜单弹窗
            OtherRankingActivityData activity = getActivityData(activityId);
            activityMsgService.sendActivityEndRankingMsg(activity, 1, "Ramadan store", "متجر رمضان", rankingMap);
        } catch (Exception e) {
            logger.error("settlementReward error. activityId={} {}", activityId, e.getMessage(), e);
        }
    }

    private RamadanStoreVO buildRamadanStoreVO(String activityId, OtherRankingActivityData activity, ActorData actorData) {
        RamadanStoreVO vo = new RamadanStoreVO();
        Map<String, Long> userDataMap = ramadanStoreRedis.getUserData(activityId, actorData.getUid());
        vo.setStartTime(activity.getStartTime());
        vo.setEndTime(activity.getEndTime());
        vo.setExchangePoint(userDataMap.getOrDefault(EXCHANGE_POINTS, 0L).intValue());
        vo.setBcGameUser(riskControlService.checkCanSeeBcGame(actorData.getUid()));
        Map<String, Long> rankingMap = ramadanStoreRedis.getRankingMap(activityId, GAME_RANKING, 10);
        vo.setRankingList(getRankingList(rankingMap, true, true));
        int rank = ramadanStoreRedis.getRankingRank(activityId, GAME_RANKING, actorData.getUid());
        long score = ramadanStoreRedis.getRankingScore(activityId, GAME_RANKING, actorData.getUid());
        vo.setMyRank(getMyRanking(actorData, score, rank, true, true));
        vo.setBoxList(getBoxList(userDataMap));
        vo.setBuyResDay(userDataMap.getOrDefault(BUY_RES_DAY_NUM, 0L).intValue());
        return vo;
    }

    private List<RamadanStoreVO.Box> getBoxList(Map<String, Long> userDataMap) {
        long buyResDayNum = userDataMap.getOrDefault(BUY_RES_DAY_NUM, 0L);
        List<RamadanStoreVO.Box> list = new ArrayList<>();
        for (RamadanStoreVO.Box boxConfig : BOX_LIST) {
            RamadanStoreVO.Box box = new RamadanStoreVO.Box();
            BeanUtils.copyProperties(boxConfig, box);
            box.setStatus(buyResDayNum < boxConfig.getLimit() ? 0 : userDataMap.getOrDefault(box.getKey(), 0L) == 0 ? 1 : 2);
            list.add(box);
        }
        return list;
    }

    private void doReportItemsChangeEvent(String uid, int action, int num, int source) {
        ActivitySpecialItemsChangeEvent event = new ActivitySpecialItemsChangeEvent();
        event.setUid(uid);
        event.setActivity_name(ACTIVITY_NAME);
        event.setActive_id(ACTIVITY_ID);
        event.setChange_action(action);
        event.setActivity_special_items_id("1");
        event.setActivity_special_items_resource(source);
        event.setResource_desc("");
        event.setChange_nums(num);
        event.setCtime(DateHelper.getNowSeconds());
        eventReport.track(new EventDTO(event));
    }
}
