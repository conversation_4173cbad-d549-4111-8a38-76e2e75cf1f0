package com.quhong.service;

import com.quhong.constant.StoreConstant;
import com.quhong.constant.UserHttpCode;
import com.quhong.core.config.ServerConfig;
import com.quhong.core.utils.DateHelper;
import com.quhong.data.ActorData;
import com.quhong.data.dto.*;
import com.quhong.data.vo.BubbleGuideVO;
import com.quhong.data.vo.GoodsListHomeVO;
import com.quhong.data.vo.MyBubbleVO;
import com.quhong.enums.ApiResult;
import com.quhong.enums.BaseDataResourcesConstant;
import com.quhong.enums.HttpCode;
import com.quhong.enums.SLangType;
import com.quhong.exception.CommonException;
import com.quhong.feign.DataCenterService;
import com.quhong.feign.DataResourcesService;
import com.quhong.mongo.dao.ActorDao;
import com.quhong.mongo.dao.BubbleDao;
import com.quhong.mongo.dao.BuddleSourceDao;
import com.quhong.mongo.data.BubbleData;
import com.quhong.mongo.data.BuddleSourceData;
import com.quhong.mysql.dao.HeartRecordDao;
import com.quhong.mysql.dao.VipInfoDao;
import com.quhong.mysql.data.UserResourceLockData;
import com.quhong.redis.GoodsListHomeRedis;
import jakarta.annotation.PostConstruct;
import jakarta.annotation.Resource;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2022/9/16
 */
@Service
public class BubbleService extends AbstractStoreGoodsService {

    private static final Logger logger = LoggerFactory.getLogger(BubbleService.class);
    public static List<Integer> VIP_BUDDLE_LIST = Arrays.asList(5, 6, 30, 31, 71);
    @Resource
    private ActorDao actorDao;
    @Resource
    private BubbleDao bubbleDao;
    @Resource
    private BuddleSourceDao buddleSourceDao;
    @Resource
    private VipInfoDao vipInfoDao;
    @Resource
    private DataResourcesService dataResourcesService;
    @Resource
    private DataCenterService dataCenterService;
    @Resource
    private HeartRecordDao heartRecordDao;
    @Resource
    private GoodsListHomeRedis goodsListHomeRedis;

    @PostConstruct
    public void postInit() {
        if (ServerConfig.isNotProduct()) {
            VIP_BUDDLE_LIST = Arrays.asList(5, 6, 34, 35, 70);
        }
    }


    /**
     * 气泡装饰列表
     */
    @Override
    public GoodsListHomeVO getStoreGoodsList(StoreDTO req) {
        GoodsListHomeVO vo = new GoodsListHomeVO();
        ActorData actorData = actorDao.getActorDataFromCache(req.getUid());
        if (actorData == null) {
            logger.error("can not find user data. uid={}", req.getUid());
            throw new CommonException(UserHttpCode.USER_NOT_EXIST);
        }
        int page = req.getPage() != null ? req.getPage() : 1;
        int size = 20;
        int start = (page - 1) * size;

        List<GoodsListHomeVO.GoodsItemVO> list = new ArrayList<>();
        List<BuddleSourceData> bubbleSourcePage = buddleSourceDao.getBubbleSourcePage(start, size);
        int qSize = bubbleSourcePage.size();
        if (!CollectionUtils.isEmpty(bubbleSourcePage)) {
            for (BuddleSourceData sourceData : bubbleSourcePage) {
                GoodsListHomeVO.GoodsItemVO bubbleVO = new GoodsListHomeVO.GoodsItemVO();
                bubbleVO.setRes_id(sourceData.getBuddle_id());
                bubbleVO.setSource_name(req.getSlang() == SLangType.ARABIC ? sourceData.getNamear() : sourceData.getName());
                bubbleVO.setSource_icon(sourceData.getBuddle_icon());
                bubbleVO.setSource_url(getSourceUrl(sourceData, req.getOs(), req.getDensity()));
                bubbleVO.setBuddle_tl(sourceData.getBuddle_tl() != null ? sourceData.getBuddle_tl() : "");
                bubbleVO.setBuddle_tr(sourceData.getBuddle_tr() != null ? sourceData.getBuddle_tr() : "");
                bubbleVO.setBuddle_bl(sourceData.getBuddle_bl() != null ? sourceData.getBuddle_bl() : "");
                bubbleVO.setBuddle_br(sourceData.getBuddle_br() != null ? sourceData.getBuddle_br() : "");
                bubbleVO.setBuy_type(sourceData.getBuy_type());
                bubbleVO.setBeans(sourceData.getBeans());
                bubbleVO.setOriginalBeans(sourceData.getBeans());
                bubbleVO.setDays(sourceData.getDays());
                fillSvipLevelTag(bubbleVO);
                list.add(bubbleVO);
            }
        }
        vo.setList(list);
        vo.setNextUrl(qSize < size ? "" : String.valueOf(page + 1));
        return vo;
    }

    @Override
    public GoodsListHomeVO getMyGoodsList(StoreDTO dto) {
        GoodsListHomeVO vo = new GoodsListHomeVO();
        List<GoodsListHomeVO.GoodsItemVO> list = new ArrayList<>();
        int page = dto.getPage() != null ? dto.getPage() : 1;
        int size = 20;
        int start = (page - 1) * size;

        List<BubbleData> bubbleList = bubbleDao.findList(dto.getUid(), 0, 1000);
        if (!CollectionUtils.isEmpty(bubbleList)) {
            for (BubbleData data : bubbleList) {
                GoodsListHomeVO.GoodsItemVO bubbleVO = new GoodsListHomeVO.GoodsItemVO();
                BuddleSourceData sourceData = buddleSourceDao.buddleFindDataCache(data.getBuddle_id());
                if (sourceData == null) {
                    logger.error("not find source bubble_id={} uid={}", data.getBuddle_id(), data.getUid());
                    continue;
                }
                bubbleVO.setRes_id(sourceData.getBuddle_id());
                bubbleVO.setEnd_days(getEndDays(data.getEnd_time()));
                bubbleVO.setStates(data.getStatus());
                bubbleVO.setC_time(data.getC_time());
                bubbleVO.setEndTime(data.getEnd_time());
                long leftTime = data.getEnd_time() - DateHelper.getNowSeconds();
                if (leftTime < 0) {
                    continue;
                }
                bubbleVO.setLeftTime(leftTime);
                fillSourceData(bubbleVO, sourceData, dto);
                fillSvipLevelTag(bubbleVO);
                list.add(bubbleVO);
            }
        }

        List<UserResourceLockData> lockDataList = selectAllResource(dto.getUid(), dto.getRes_type());
        if (!CollectionUtils.isEmpty(lockDataList)) {
            for (UserResourceLockData data : lockDataList) {
                GoodsListHomeVO.GoodsItemVO bubbleVO = new GoodsListHomeVO.GoodsItemVO();
                BuddleSourceData sourceData = buddleSourceDao.buddleFindDataCache(data.getResourceId());
                if (sourceData == null) {
                    logger.error("not find source bubble_id={} uid={}", data.getResourceId(), data.getUid());
                    continue;
                }
                bubbleVO.setRes_id(data.getId());
                bubbleVO.setLockRes(1);
                bubbleVO.setEnd_days(data.getValidDay());
                bubbleVO.setStates(0); // 兼容老版本字段
                bubbleVO.setC_time(data.getCtime());
                fillSourceData(bubbleVO, sourceData, dto);
                fillSvipLevelTag(bubbleVO);
                list.add(bubbleVO);
            }
        }
        commonSelectPage(vo, list, page, size);
//        vo.setList(list);
//        vo.setNextUrl(bubbleList.size() < size ? "" : String.valueOf(page + 1));
        goodsListHomeRedis.removeGetResUidToRedis(dto.getUid(), BaseDataResourcesConstant.TYPE_BUDDLE);
        return vo;
    }


    private void fillSourceData(GoodsListHomeVO.GoodsItemVO itemVO, BuddleSourceData sourceData, StoreDTO dto) {
        itemVO.setSource_name(dto.getSlang() == SLangType.ENGLISH ? sourceData.getName() : sourceData.getNamear());
        itemVO.setSource_icon(sourceData.getBuddle_icon() != null ? sourceData.getBuddle_icon() : "");
        itemVO.setBuddle_color(sourceData.getBuddle_color() != null ? sourceData.getBuddle_color() : "");
        itemVO.setIs_activity(sourceData.getIs_activity());
        itemVO.setBuddle_tl(sourceData.getBuddle_tl() != null ? sourceData.getBuddle_tl() : "");
        itemVO.setBuddle_tr(sourceData.getBuddle_tr() != null ? sourceData.getBuddle_tr() : "");
        itemVO.setBuddle_bl(sourceData.getBuddle_bl() != null ? sourceData.getBuddle_bl() : "");
        itemVO.setBuddle_br(sourceData.getBuddle_br() != null ? sourceData.getBuddle_br() : "");
        itemVO.setTop(sourceData.getTop());
        itemVO.setBottom(sourceData.getBottom());
        itemVO.setLeft(sourceData.getLeft());
        itemVO.setRight(sourceData.getRight());
        itemVO.setSource_url(getSourceUrl(sourceData, dto.getOs(), dto.getDensity()));
        itemVO.setItem_type(sourceData.getItem_type());
    }


    private String getSourceUrl(BuddleSourceData sourceData, int os, int density) {
        // String sourceUrl = "";
        // if (os == ClientOS.IOS) {
        //     sourceUrl = sourceData.getIos_buddle_source();
        // } else {
        //     switch (density) {
        //         case 1:
        //         case 2:
        //         case 3:
        //             sourceUrl = sourceData.getAndroid_buddle_source_1x();
        //             break;
        //         case 4:
        //             sourceUrl = sourceData.getAndroid_buddle_source();
        //             break;
        //         case 5:
        //             sourceUrl = sourceData.getAndroid_buddle_source_3x();
        //             break;
        //         default:
        //             sourceUrl = sourceData.getAndroid_buddle_source();
        //             break;
        //     }
        // }
        return StringUtils.isEmpty(sourceData.getBuddle_preview()) ? "" : sourceData.getBuddle_preview();
    }


    private void addDefaultBubble(List<MyBubbleVO.BubbleVO> list) {
        MyBubbleVO.BubbleVO vo = new MyBubbleVO.BubbleVO();
        vo.setBuddle_id(1);
        vo.setBuddle_icon("https://cloudcdn.waho.live/test/2020_01_14_default_bubble.png");
        vo.setSource_url("");
        vo.setIs_activity(0);
        vo.setBuddle_color("");
        vo.setEnd_days(-1);
        vo.setStatus(1);
        list.add(vo);
    }

    private int saveBubbleData(String uid) {
        BubbleData wearBubble = bubbleDao.findDataByStatus(uid, 1);
        int status = wearBubble == null ? 1 : 0;
        BubbleData bubbleData = new BubbleData();
        bubbleData.setUid(uid);
        bubbleData.setBuddle_id(1);
        bubbleData.setC_time(DateHelper.getNowSeconds());
        bubbleData.setStatus(status);
        bubbleData.setEnd_time(1891067119);
        bubbleDao.save(bubbleData);
        return status;
    }

    public BubbleGuideVO bubbleGuide(StoreDTO req) {
        ActorData actorData = actorDao.getActorDataFromCache(req.getUid());
        if (actorData == null) {
            logger.error("user not exist. uid={}", req.getUid());
            throw new CommonException(UserHttpCode.USER_NOT_EXIST);
        }
        BubbleGuideVO vo = new BubbleGuideVO();
        vo.setName(actorData.getName());
        vo.setHead(actorData.getHead());
        vo.setBubbleId(8);
        vo.setBubbleUrl("https://cloudcdn.waho.live/test/2020_01_08_icon_8.png");
        vo.setPrice1(799);
        vo.setPrice30(800);
        return vo;
    }


    /**
     * 设置聊天气泡
     */
    public void setBubble(SetResourcesDTO req) {
        if (req.getBuddle_id() == null) {
            logger.info("buddle_id is invalid . uid={} buddle_id={}", req.getUid(), req.getBuddle_id());
            throw new CommonException(UserHttpCode.PARAM_ERROR);
        }

//        int level = vipInfoDao.getIntVipLevel(req.getUid());
//        if (VIP_BUDDLE_LIST.contains(req.getBuddle_id()) && level == 0) {
//            logger.info("uid={} pid={} level={}", req.getUid(), req.getBuddle_id(), level);
//            throw new CommonException(UserHttpCode.VIP_MIC_FRAME_SET_FAILED);
//        }
        ResourcesDTO resourcesDTO = new ResourcesDTO();
        resourcesDTO.setUid(req.getUid());
        resourcesDTO.setResId(String.valueOf(req.getBuddle_id()));
        resourcesDTO.setResType(BaseDataResourcesConstant.TYPE_BUDDLE);
        resourcesDTO.setActionType(BaseDataResourcesConstant.ACTION_WEAR);
        resourcesDTO.setDesc("store set bubble");
        resourcesDTO.setmTime(DateHelper.getNowSeconds());
        ApiResult<String> result = dataResourcesService.handleRes(resourcesDTO);
        if (result.isError()) {
            logger.info("set bubble handleRes failure. uid={} buddle_id={} code={} msg={}",
                    req.getUid(), req.getBuddle_id(), result.getCode(), result.getCode().getMsg());
            if (result.getCode().getCode() == StoreConstant.NOT_OWN_RESOURCES) {
                throw new CommonException(UserHttpCode.NOT_OWN_BUBBLE_RESOURCES);
            }
            throw new CommonException(UserHttpCode.BUBBLE_SET_FAILED);
        }
    }

    @Override
    public GoodsListHomeVO.GoodsItemVO getGoodsItem(StoreDTO dto, int resId) {
        if (resId == 1) {
            logger.info("return resId is 1 uid={} resId={}", dto.getUid(), resId);
            return null;
        }
        BuddleSourceData sourceData = buddleSourceDao.findStoreData(resId);
        if (sourceData == null) {
            logger.info("return sourceData is null uid={} resId={}", dto.getUid(), resId);
            return null;
        }
        GoodsListHomeVO.GoodsItemVO vo = new GoodsListHomeVO.GoodsItemVO();
        vo.setRes_type(BaseDataResourcesConstant.TYPE_BUDDLE);
        vo.setRes_id(resId);
        vo.setSource_icon(sourceData.getBuddle_icon() != null ? sourceData.getBuddle_icon() : "");
        vo.setSource_name(dto.getSlang() == SLangType.ENGLISH ? sourceData.getName() : sourceData.getNamear());
        vo.setSource_url(getSourceUrl(sourceData, dto.getOs(), dto.getDensity()));
        vo.setBeans(sourceData.getBeans());
        vo.setOriginalBeans(sourceData.getBeans());
        vo.setDays(sourceData.getDays());
        vo.setBuy_type(0);
        fillSvipLevelTag(vo);
        return vo;
    }

    private void fillSvipLevelTag(GoodsListHomeVO.GoodsItemVO vo) {
        if (vo == null) {
            return;
        }
        SvipLevelService.CHAT_BUBBLE_MAP.forEach((svipLevel, resId) -> {
            if (vo.getRes_id().equals(resId)) {
                vo.setSvipLevel(svipLevel);
                vo.setItem_type(8);
            }
        });
    }

    /**
     * 购买气泡装饰
     */
    @Override
    public String buyGoods(StoreGoodsDTO dto) {
        Integer resId = dto.getRes_id();
        if (resId == null) {
            logger.info("bubbleId is null . uid={} ", dto.getUid());
            throw new CommonException(UserHttpCode.PARAM_ERROR);
        }
        BuddleSourceData sourceData = buddleSourceDao.findData(resId);

        if (sourceData == null) {
            logger.info("sourceData is null  uid={} bubbleId={}", dto.getUid(), resId);
            throw new CommonException(UserHttpCode.BUY_RES_NOT_EXIST);
        }

        int cost = getDiscountInfo(dto.getUid(), sourceData.getBeans()).getPrice();
        int days = sourceData.getDays() <= 0 ? 7 : sourceData.getDays();

        if (cost <= 0) {
            logger.info("sourceData cost={} <=0 . uid={} bubbleId={}", cost, dto.getUid(), resId);
            throw new CommonException(UserHttpCode.BUY_RES_NOT_EXIST);
        }

        int buyType = sourceData.getBuy_type();
        if (buyType == 1) {
            ActorData actorData = actorDao.getActorData(dto.getUid());
            if (actorData == null) {
                logger.error("user not exist. uid={}", dto.getUid());
                throw new CommonException(UserHttpCode.USER_NOT_EXIST);
            }
            if (actorData.getHeartGot() < cost) {
                logger.info("sourceData is null or cost={} <=0 . uid={} bubbleId={}", cost, dto.getUid(), resId);
                throw new CommonException(UserHttpCode.COIN_NOT_ENOUGH);
            }
            boolean success = heartRecordDao.changeHeart(dto.getUid(), -cost, StoreConstant.BUY_BUBBLE_TITLE, StoreConstant.BUY_BUBBLE_TITLE);
            if (!success) {
                logger.info("change heart fail cost={} uid={} bubbleId={}", cost, dto.getUid(), resId);
                throw new CommonException(HttpCode.SERVER_ERROR);
            }
        } else {
            if (actorDao.getIntBalance(dto.getUid()) < cost) {
                logger.info("sourceData is null or cost={} <=0 . uid={} bubbleId={}", cost, dto.getUid(), resId);
                throw new CommonException(UserHttpCode.DIAMONDS_NOT_ENOUGH);
            }
            MoneyDetailReq moneyDetailReq = new MoneyDetailReq();
            moneyDetailReq.setRandomId();
            moneyDetailReq.setUid(dto.getUid());
            moneyDetailReq.setAtype(StoreConstant.BUY_BUBBLE_TYPE);
            moneyDetailReq.setChanged(-cost);
            moneyDetailReq.setTitle(StoreConstant.BUY_BUBBLE_TITLE);
            moneyDetailReq.setDesc(StoreConstant.BUY_BUBBLE_TITLE);
            ApiResult<String> reduceBeansResult = dataCenterService.reduceBeans(moneyDetailReq);
            if (!reduceBeansResult.isOk()) {
                logger.info("change beans fail cost={} uid={} bubbleId={}", cost, dto.getUid(), resId);
                throw new CommonException(HttpCode.SERVER_ERROR);
            }
        }
        goodsListHomeRedis.incrHotGoodsRankingScore(BaseDataResourcesConstant.TYPE_BUDDLE, resId);
        ResourcesDTO resourcesDTO = new ResourcesDTO();
        String toUid = dto.getUid();
        String fromUid = "";
        int getWay = BaseDataResourcesConstant.TYPE_BUY_GET;
        if (!StringUtils.isEmpty(dto.getAid())) {
            fromUid = dto.getUid();
            toUid = dto.getAid();
            getWay = BaseDataResourcesConstant.TYPE_OTHER_BUY_GET;
        }
        resourcesDTO.setUid(toUid);
        resourcesDTO.setResId(String.valueOf(resId));
        resourcesDTO.setResType(BaseDataResourcesConstant.TYPE_BUDDLE);
        resourcesDTO.setDays(days);
        resourcesDTO.setActionType(BaseDataResourcesConstant.ACTION_GET);
        resourcesDTO.setDesc(StoreConstant.BUY_BUBBLE_TITLE);
        resourcesDTO.setmTime(DateHelper.getNowSeconds());
        resourcesDTO.setGainType(BaseDataResourcesConstant.GAIN_TYPE_DAY);
        resourcesDTO.setGetWay(getWay);
        resourcesDTO.setEmptyWearType(BaseDataResourcesConstant.EMPTY_WEAR_AUTO);
        resourcesDTO.setFromUid(fromUid);
        resourcesDTO.setBeans(sourceData.getBeans());
        ApiResult<String> result = dataResourcesService.handleRes(resourcesDTO);
        if (result.isError()) {
            logger.error("buy bubble failure. cost={} uid={} bubbleId={} toUid={} days={} code={} msg={}",
                    cost, dto.getUid(), resId, toUid, days, result.getCode(), result.getCode().getMsg());
            throw new CommonException(UserHttpCode.RES_BUY_FAILED);
        }
        return sourceData.getBuddle_icon();
    }

    @Override
    public String unLockGoods(StoreGoodsDTO dto) {
        BuddleSourceData sourceData = buddleSourceDao.findData(dto.getRes_id());
        if (sourceData == null) {
            logger.info("sourceData is null  uid={} bubbleId={}", dto.getUid(), dto.getRes_id());
            return null;
        } else {
            return sourceData.getBuddle_icon();
        }
    }
}
