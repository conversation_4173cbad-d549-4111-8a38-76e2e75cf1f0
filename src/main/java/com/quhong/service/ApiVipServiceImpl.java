package com.quhong.service;


import com.quhong.api.ApiVipService;
import com.quhong.config.AsyncConfig;
import com.quhong.data.dto.VipDTO;
import com.quhong.data.dto.VipRechargeDTO;
import com.quhong.enums.HttpCode;
import com.quhong.exception.CommonException;
import jakarta.annotation.Resource;
import org.apache.dubbo.config.annotation.DubboService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.concurrent.CompletableFuture;
import java.util.concurrent.Executor;

@DubboService(timeout = 3000)
public class ApiVipServiceImpl implements ApiVipService {
    private static final Logger logger = LoggerFactory.getLogger(ApiVipServiceImpl.class);

    @Resource
    private VipService vipService;
    @Resource(name = AsyncConfig.ASYNC_TASK)
    private Executor executor;

    @Override
    public void vipCharge(String uid, int vipLevel) throws CommonException {
        VipDTO dto = new VipDTO();
        dto.setUid(uid);
        dto.setVlvlbuy(vipLevel);
        dto.setAdmin(true);
        vipService.buyVip(dto);
    }

    @Override
    public CompletableFuture<Void> vipRecharge(VipRechargeDTO dto) {
        return CompletableFuture.runAsync(() -> vipService.vipRecharge(dto), executor);
    }

    @Override
    public CompletableFuture<Void> vipRemove(String uid) {
        return CompletableFuture.runAsync(() -> {
            VipDTO dto = new VipDTO();
            dto.setUid(uid);
            vipService.removeVip(dto);
        }, executor);
    }

    @Override
    public void vipRemove(Integer rid) throws CommonException {
        VipDTO dto = new VipDTO();
        dto.setRid(rid + "");
        dto.setAdmin(true);
        vipService.removeVip(dto);
    }
}
