package com.quhong.service;

import com.quhong.analysis.EventReport;
import com.quhong.analysis.MomentSendLogEvent;
import com.quhong.config.CaffeineCacheConfig;
import com.quhong.config.MomentConfig;
import com.quhong.constant.AccountConstant;
import com.quhong.constant.DetectOriginConstant;
import com.quhong.constant.MomentConstant;
import com.quhong.constant.MomentHttpCode;
import com.quhong.core.distribution.DistributeLock;
import com.quhong.core.utils.DateHelper;
import com.quhong.dailyTask.UserLevelTaskService;
import com.quhong.data.ActorData;
import com.quhong.data.UserLevelTaskData;
import com.quhong.data.dto.*;
import com.quhong.data.vo.*;
import com.quhong.dto.TextDTO;
import com.quhong.enums.ApiResult;
import com.quhong.enums.HttpCode;
import com.quhong.enums.SLangType;
import com.quhong.enums.UserLevelConstant;
import com.quhong.exception.CommonException;
import com.quhong.feign.IDetectService;
import com.quhong.feign.IFriendService;
import com.quhong.handler.HttpEnvData;
import com.quhong.image.ImageUrlGenerator;
import com.quhong.mongo.dao.*;
import com.quhong.mongo.data.*;
import com.quhong.mysql.dao.MomentOpDao;
import com.quhong.mysql.dao.VipInfoDao;
import com.quhong.mysql.dao.WhiteTestDao;
import com.quhong.mysql.data.MomentRewardData;
import com.quhong.redis.*;
import com.quhong.room.redis.MicFrameRedis;
import com.quhong.room.redis.RoomPlayerRedis;
import com.quhong.utils.CDNUtils;
import com.quhong.utils.PageUtils;
import com.quhong.utils.PhoneNumberChecker;
import com.quhong.utils.RoomUtils;
import jakarta.annotation.Resource;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import org.springframework.util.ObjectUtils;

import java.util.*;

@Service
public class MomentService {
    private static final Logger logger = LoggerFactory.getLogger(MomentService.class);
    private static final List<Integer> SELF_SHOW = Collections.emptyList();
    private static final List<Integer> FRIEND_SHOW = Arrays.asList(1, 2);
    private static final List<Integer> STRANGER_SHOW = Collections.singletonList(1);

    private static final int MOMENT_SIZE = 10;
    private static final int FRIEND_LIST_SIZE = 10;
    private static final int LIKE_LIST_SIZE = 20;
    private static final int PERSONAL_LIST_SIZE = 10;
    private static final int NOTICE_LIST_SIZE = 10;
    private static final int COMMENT_DETAIL_LIST_SIZE = 10;
    private static final int SEND_GIFT_REWARD_ATYPE = 211;
    private static final String SEND_GIFT_REWARD_TITLE = "Moment Send Gift";
    private static final String SEND_GIFT_REWARD_DESC = "send gift to %s in moment.";
    private static final int RECEIVE_GIFT_REWARD_ATYPE = 212;
    private static final String RECEIVE_GIFT_REWARD_TITLE = "Moment Receive Gift";
    private static final String RECEIVE_GIFT_REWARD_DESC = "receive gift from %s in moment.";

    @Resource
    private MomentDao momentDao;
    @Resource
    private UserMonitorDao userMonitorDao;
    @Resource
    private BlockMomentRedis blockMomentRedis;
    @Resource
    private IDetectService detectService;
    @Resource
    private TimelineService timelineService;
    @Resource
    private MomentNoticeService momentNoticeService;
    @Resource
    private UserLevelTaskService levelTaskService;
    @Resource
    private MomentConfig momentConfig;
    @Resource
    private MomentShowRedis momentShowRedis;
    @Resource
    private CommentDao commentDao;
    @Resource
    private TopMomentRedis topMomentRedis;
    @Resource
    private ActorDao actorDao;
    @Resource
    private RoomPlayerRedis roomPlayerRedis;
    @Resource
    private FollowDao followDao;
    @Resource
    private BlackListDao blackListDao;
    @Resource
    private FriendsListRedis friendsListRedis;
    @Resource
    private LocalCacheService localCacheService;
    @Resource
    private IFriendService friendService;
    @Resource
    private VipInfoDao vipInfoDao;
    @Resource
    private MicFrameRedis micFrameRedis;
    @Resource
    private UserLevelDao userLevelDao;
    @Resource
    private MsgListService msgListService;
    @Resource
    private ImageDetectService imageDetectService;
    @Resource
    private EventReport eventReport;
    @Resource
    private MomentRewardService momentRewardService;
    @Resource
    private UserMonitorService userMonitorService;
    @Resource
    private IndexBannerDao indexBannerDao;
    @Resource
    private RoomMicRedis roomMicRedis;
    @Resource
    private MomentUnreadRedis momentUnreadRedis;
    @Resource
    private WhiteTestDao whiteTestDao;
    @Resource
    private MysteryService mysteryService;
    @Resource
    private MomentOpDao momentOpDao;

    private void checkPrivilege(PublishDTO req) {
        if (ObjectUtils.isEmpty(req.getText())) {
            // 转发朋友圈、分享链接、发布只有图片的朋友圈支持不输入内容
            if (null != req.getQuote() || null != req.getImgs()) {
                req.setText("");
            } else {
                throw new CommonException(MomentHttpCode.PARAM_ERROR);
            }
        }
        if (req.getText().length() > 1024) {
            throw new CommonException(MomentHttpCode.LENGTH_NOT_ALLOW);
        }
        if (req.getImgs() != null && req.getImgs().size() > 9) {
            throw new CommonException(MomentHttpCode.IMG_LIMIT, 9);
        }
        if (req.getAt_list() != null && req.getAt_list().size() > 10) {
            throw new CommonException(MomentHttpCode.AT_LIMIT, 10);
        }
        if (req.getQuote() != null) {
            if (ObjectUtils.isEmpty(req.getQuote().getAction()) || ObjectUtils.isEmpty(req.getQuote().getContent())) {
                throw new CommonException(MomentHttpCode.PARAM_ERROR);
            }
            if (req.getQuote().getType() == MomentConstant.QUOTE_REPOST && req.getImgs() != null && req.getImgs().size() > 1) {
                throw new CommonException(MomentHttpCode.IMG_LIMIT, 1);
            }
        }
        checkBlock(req);
        checkFroze(req);
//        if (userLevelDao.getUserLevel(req.getUid(), UserLevelConstant.ACTIVE_LEVEL) < 2) {
//            throw new CommonException(MomentHttpCode.LEVEL_LIMIT);
//        }
        checkDirtyWord(req.getUid(), req.getText(), DetectOriginConstant.MOMENT_POST);
    }

    private void checkDirtyWord(String uid, String text, String origin) {
        if (detectService.detectText(new TextDTO(text, origin, uid)).getData().getIsSafe() == 0) {
            logger.info("Dirty word in message is not allowed!. uid={} msg={}", uid, text);
            throw new CommonException(MomentHttpCode.DIRTY_WORD);
        }
    }

    private void checkBlock(HttpEnvData req) {
        if (blockMomentRedis.isBlock(req.getUid())) {
            logger.info("publish moment, actor account has block. uid={}", req.getUid());
            throw new CommonException(MomentHttpCode.MOMENT_BLOCK);
        }
    }

    private void checkFroze(HttpEnvData req) {
        if (!userMonitorDao.notFreezeOrBan(req.getUid())) {
            logger.info("publish moment, actor account has frozen. uid={}", req.getUid());
            throw new CommonException(MomentHttpCode.MOMENT_FROZEN);
        }
    }

    private List<MomentData.Image> formatImageList(List<MomentImageDTO> imgList) {
        if (CollectionUtils.isEmpty(imgList)) {
            return Collections.emptyList();
        }
        List<MomentData.Image> resultList = new ArrayList<>();
        for (MomentImageDTO imageDTO : imgList) {
            MomentData.Image image = new MomentData.Image();
            image.setOrigin(CDNUtils.getCdnUrl(imageDTO.getUrl()));
            image.setThumbnail(ImageUrlGenerator.generateUrl(image.getOrigin(), 400, 400, false));
            image.setSafe(1);
            image.setWidth(imageDTO.getWidth());
            image.setHeight(imageDTO.getHeight());
            resultList.add(image);
        }
        return resultList;
    }

    public void publish(PublishDTO req) {
        checkPrivilege(req);
        List<MomentData.Image> images = formatImageList(req.getImgs());
        int nowSeconds = DateHelper.getNowSeconds();
        MomentData momentData = new MomentData(req.getUid(), req.getText(), images, req.getLocation(), req.getShow(), req.getAt_list(), nowSeconds);
        if (null != req.getQuote()) {
            if (req.getQuote().getType() == MomentConstant.QUOTE_REPOST) {
                logger.info("repost moment uid={} mid={}", req.getUid(), req.getQuote().getAction());
                // 增加转发次数
                momentDao.incrMomentRepost(req.getQuote().getAction());
            }
            momentData.setQuote(req.getQuote());
        }
        momentDao.save(momentData);
        if (null == momentData.get_id()) {
            logger.error("save moment error. uid={}", req.getUid());
            throw new CommonException();
        }
        String mid = momentData.get_id().toString();
        if (req.getShow() == MomentConstant.MOMENT_PUBLIC || req.getShow() == MomentConstant.MOMENT_FRIENDS) {
            timelineService.makeTimeline(req.getUid(), momentData, req.getShow() == MomentConstant.MOMENT_PUBLIC);
            if (!CollectionUtils.isEmpty(req.getAt_list()) && !ObjectUtils.isEmpty(req.getText())) {
                Set<String> atSet = new HashSet<>();
                for (MomentData.AtUser atUser : req.getAt_list()) {
                    if (null != atUser && atUser.getAid().length() == 24) {
                        if (atSet.contains(atUser.getAid())) {
                            continue;
                        }
                        atSet.add(atUser.getAid());
                        MomentNotice momentNotice = new MomentNotice(atUser.getAid(), req.getUid(),
                                MomentConstant.NOTICE_PUBLISH_AT, mid, momentData.getC_time(),
                                req.getText(), req.getAt_list(), null);
                        fillMomentNotice(momentData, momentNotice);
                        momentNoticeService.addNotice(atUser.getAid(), momentNotice, null);
                    }
                }
            }
        }
        if (!images.isEmpty()) {
            imageDetectService.momentImageCheck(images, mid, req.getUid());
        }
        // 用户等级任务
        levelTaskService.sendTaskDataToMq(new UserLevelTaskData(req.getUid(), UserLevelConstant.POST_MOMENT));
        // 埋点
        MomentSendLogEvent event = new MomentSendLogEvent();
        event.setUid(momentData.getUid());
        event.setMoment_id(mid);
        event.setMoment_visible_range(momentData.getShow());
        event.setIs_at(CollectionUtils.isEmpty(momentData.getAt_list()) ? 0 : 1);
        event.setIs_picture(CollectionUtils.isEmpty(momentData.getImgs()) ? 0 : 1);
        event.setQuote_type(null == momentData.getQuote() ? 0 : momentData.getQuote().getType());
        event.setActive_id(req.getActiveId());
        event.setCtime(momentData.getC_time());
        eventReport.track(new com.quhong.analysis.EventDTO(event));
        if (PhoneNumberChecker.containsIllegalPhoneNumber(req.getText())) {
            blockMoment("[SystemDetection]", momentData.get_id().toString(), 1);
        }
    }

    public void publishPermission(HttpEnvData req) {
        checkBlock(req);
//        if (userLevelDao.getUserLevel(req.getUid()) < 2) {
//            throw new CommonException(MomentHttpCode.LEVEL_LIMIT, 2);
//        }
    }

    public PrePublishVO prePublish(HttpEnvData req) {
        publishPermission(req);
        return new PrePublishVO();
    }

    public MomentData getMoment(String mid) {
        if (ObjectUtils.isEmpty(mid)) {
            throw new CommonException(MomentHttpCode.PARAM_ERROR);
        }
        MomentData moment = momentDao.getMoment(mid);
        if (null == moment) {
            logger.info("cannot find moment mid={}", mid);
            throw new CommonException(MomentHttpCode.MOMENT_NOT_EXIST);
        }
        return moment;
    }

    private CommentVO commentToVO(HttpEnvData req, MomentData moment, Comment comment, boolean showReplayTo) {
        CommentVO commentVO = new CommentVO();
        commentVO.setComment_id(comment.get_id().toString());
        commentVO.setComment(comment.getContent());
        commentVO.setUid(comment.getCommentator());
        commentVO.setReplyCount(comment.getReply_count());
        commentVO.setIsLike(null == comment.getLikes() ? 0 : comment.getLikes().contains(req.getUid()) ? 1 : 0);
        commentVO.setLikeCount(null == comment.getLikes() ? 0 : comment.getLikes().size());
        commentVO.setC_time(comment.getC_time());

        ActorData actorData = actorDao.getActorDataFromCache(comment.getCommentator());
        if (null != actorData) {
            int vipLevel = vipInfoDao.getIntVipLevelFromCache(actorData.getUid());
            commentVO.setVipLevel(vipLevel);
            commentVO.setSvipLevel(actorData.getSvipLevel());
            commentVO.setName(actorData.getName());
            commentVO.setGender(actorData.getFb_gender());
            commentVO.setHead(ImageUrlGenerator.generateRoomUserUrl(actorData));
        }
        if (showReplayTo) {
            commentVO.setReply_to_uid(comment.getReply_to());
            if (!ObjectUtils.isEmpty(comment.getReply_to())) {
                actorData = actorDao.getActorDataFromCache(comment.getReply_to());
                if (null != actorData) {
                    commentVO.setReply_to_name(actorData.getName());
                }
            }
        }
        commentVO.setAt_list(comment.getAt_list());
        if (!ObjectUtils.isEmpty(req.getUid())) {
            commentVO.setDel((req.getUid().equals(moment.getUid()) || req.getUid().equals(comment.getCommentator())
                    || momentConfig.getPOWER_USER().contains(req.getUid())) ? 1 : 0);
        }
        return commentVO;
    }

    @Cacheable(value = "detailFromCache", cacheManager = CaffeineCacheConfig.EXPIRE_1M_AFTER_WRITE)
    public MomentInfoVO detailFromCache(MomentDTO req) {
        return detail(req);
    }

    public MomentInfoVO detail(MomentDTO req) {
        MomentData moment = getMoment(req.getMid());
        if (moment.getIs_blocked() == 1 && !req.getUid().equals(moment.getUid())) {
            throw new CommonException(MomentHttpCode.MOMENT_NOT_EXIST);
        }
        MomentInfoVO momentInfoVO;
        if (1 == req.getPage()) {
            momentInfoVO = getMomentInfoVO(moment, req, true, true);
            momentInfoVO.setIs_top(topMomentRedis.getTopMomentFromCache().contains(momentInfoVO.getMid()) ? 1 : 0);
        } else {
            momentInfoVO = new MomentInfoVO();
        }
        return momentInfoVO;
    }

    public void delete(MomentDTO req) {
        MomentData moment = getMoment(req.getMid());
        if (req.getUid().equals(moment.getUid())
                || momentConfig.getPOWER_USER().contains(req.getUid())
                || momentConfig.getOFFICIAL_USER().contains(req.getUid())) {
            // 删除朋友圈
            momentDao.delete(moment, moment.get_id().toString());
            // 删除广场数据
            momentShowRedis.deleteMomentShow(moment, req.getUid());
            // 删除Square列表数据
            momentShowRedis.removeMomentSquareList(moment, req.getUid());
            // 删除通知消息
            momentNoticeService.deleteNotice(moment);
            // 删除评论
            commentDao.removeAll(req.getMid());
            // 删除礼物打赏
            momentRewardService.deleteMomentReward(req.getMid());
        }
    }

    public MomentVO showPublish(MomentDTO req) {
        return 2 == req.getOpt() ? getPrivateMoment(req) : getPublicMoment(req);
    }

    @Cacheable(value = "getPublicMomentFromCache", key = "#p0.labelId.concat('-').concat(#p0.page)", cacheManager = CaffeineCacheConfig.EXPIRE_1M_AFTER_WRITE)
    public MomentVO getPublicMomentFromCache(MomentDTO req) {
        return getPublicMoment(req);
    }

    private MomentVO getPrivateMoment(MomentDTO req) {
        if (ObjectUtils.isEmpty(req.getUid())) {
            throw new CommonException(HttpCode.PARAM_ERROR);
        }
        int start = (req.getPage() - 1) * MOMENT_SIZE;
        MomentVO vo = new MomentVO();
        int resultLength = 0;
        List<String> timeline = timelineService.getTimeline(req.getUid(), start, start + MOMENT_SIZE - 1);
        for (String mid : timeline) {
            resultLength++;
            MomentData moment = momentDao.getMoment(mid);
            if (null == moment || blackListDao.isBlock(req.getUid(), moment.getUid())) {
                // 清除已经删除的moment
                timelineService.deleteTimeline(req.getUid(), mid);
                continue;
            }
            if (1 == moment.getIs_blocked()) {
                continue;
            }
            MomentInfoVO momentInfoVO = getMomentInfoVO(moment, req, false, true);
            if (null != momentInfoVO) {
                // 不是好友且未关注，删除记录
                if (1 != momentInfoVO.getIs_friend() && !req.getUid().equals(momentInfoVO.getAid())) {
                    if (moment.getShow() == MomentConstant.MOMENT_FRIENDS || 1 != momentInfoVO.getIs_followed()) {
                        timelineService.deleteTimeline(req.getUid(), mid);
                        continue;
                    }
                }
                vo.getList().add(momentInfoVO);
            }
        }
        if (req.getPage() == 1) {
            if (!localCacheService.isCleanToday(req.getUid()) && timelineService.getTimelineSize(req.getUid()) > 500) {
                // 每超过500条好友圈消息时，清除最先的300条记录
                timelineService.cleanTimeline(req.getUid(), 200);
                localCacheService.addCleanToday(req.getUid());
            }
            actorDao.clearMomentUnread(req.getUid());
        }
        vo.setNextUrl(resultLength < MOMENT_SIZE ? "" : req.getPage() + 1 + "");
        vo.setRicky(momentNoticeService.getNoticeCount(req.getUid()));
        vo.setIs_official(momentConfig.getOFFICIAL_USER().contains(req.getUid()) ? 1 : 0);
        return vo;
    }

    private MomentVO getPublicMoment(MomentDTO req) {
        MomentVO vo = new MomentVO();
        Set<String> topMidSet = new HashSet<>();
        // 官方置顶
        // && req.getHideTop() == 0
        if (req.getPage() == 1) {
            for (String topMid : topMomentRedis.getTopMomentFromCache()) {
                MomentInfoVO momentInfoVO = getMomentInfoVO(momentDao.getMoment(topMid), req, false, false);
                if (null == momentInfoVO) {
                    // 清理脏数据
                    logger.error("cannot find top moment, remove top moment. mid={}", topMid);
                    topMomentRedis.deleteTopMoment("[system]", topMid);
                } else {
                    topMidSet.add(momentInfoVO.getMid());
                    momentInfoVO.setIs_top(1);
                    vo.getList().add(momentInfoVO);
                }
            }
        }
//        if (AppVersionUtils.versionCheck(836, req)) {
//            fillRecommendMoment(req, vo, topMidSet);
//        } else {
        // 旧版广场列表
        fillPublicMoment(req, vo, topMidSet);
//        }
        vo.setNextUrl(CollectionUtils.isEmpty(vo.getList()) ? "" : req.getPage() + 1 + "");
        vo.setRicky(momentNoticeService.getNoticeCount(req.getUid()));
        vo.setIs_official(momentConfig.getOFFICIAL_USER().contains(req.getUid()) ? 1 : 0);
        return vo;
    }

//    private void fillRecommendMoment(MomentDTO req, MomentVO vo, Set<String> topMidSet) {
//        List<String> recommendList = recommendService.getRecommendList(req.getUid(), topMidSet);
//        for (String mid : recommendList) {
//            MomentInfoVO momentInfoVO = getMomentInfoVO(momentDao.getMoment(mid), req, false, false, true);
//            if (null != momentInfoVO) {
//                vo.getList().add(momentInfoVO);
//            }
//        }
//    }

    private void fillPublicMoment(MomentDTO req, MomentVO vo, Set<String> topMidSet) {
        int labelNum = req.getLabelId() == 0 ? 1 : 6;
        int start = (req.getPage() - 1) * MOMENT_SIZE * labelNum;
        List<String> momentShow = momentShowRedis.getMomentShow(start, start + (MOMENT_SIZE * labelNum) - 1);
        int index = 0;
        for (String mixMid : momentShow) {
            ++index;
            if (req.getLabelId() > 0) {
                if (index % labelNum != (req.getLabelId() != 6 ? req.getLabelId() : 0)) {
                    continue;
                }
            }
            String[] arrays = mixMid.split("_");
            if (arrays.length != 2) {
                continue;
            }
            // 防止朋友圈数据被清掉暂时去掉
            // if (Integer.parseInt(arrays[1]) < DateHelper.getNowSeconds() - TimeUnit.DAYS.toSeconds(7)) {
            //     // 清除过期的moment
            //     momentShowRedis.deleteMomentShow(mixMid);
            //     continue;
            // }
            MomentData moment = momentDao.getMoment(arrays[0]);
            if (null == moment) {
                // 清除已经删除的moment
                momentShowRedis.deleteMomentShow(mixMid);
                continue;
            }
            if (moment.getIs_blocked() == 1 || topMidSet.contains(moment.get_id().toString()) || userMonitorService.isBan(moment.getUid())) {
                continue;
            }
            MomentInfoVO momentInfoVO = getMomentInfoVO(moment, req, false, false);
            if (null != momentInfoVO) {
                vo.getList().add(momentInfoVO);
            }
        }
    }

    private void fillSquareMoment(MomentDTO req, MomentVO vo, Set<String> topMidSet) {
        int start = (req.getPage() - 1) * MOMENT_SIZE;
        List<String> momentShow = momentShowRedis.getMomentSquareList(start, start + MOMENT_SIZE - 1);
        for (String mixMid : momentShow) {
            String[] arrays = mixMid.split("_");
            if (arrays.length != 2) {
                continue;
            }
            MomentData moment = momentDao.getMoment(arrays[0]);
            if (null == moment) {
                // 清除已经删除的moment
                momentShowRedis.removeMomentSquareList(mixMid);
                continue;
            }
            if (1 == moment.getIs_blocked() || topMidSet.contains(moment.get_id().toString()) || userMonitorService.isBan(moment.getUid())) {
                continue;
            }
            MomentInfoVO momentInfoVO = getMomentInfoVO(moment, req, false, false);
            if (null != momentInfoVO) {
                vo.getList().add(momentInfoVO);
            }
        }
    }


    /**
     * 格式化礼物打赏数值
     * if (gifted >= 1000) {
     * return new BigDecimal(gifted / 1000f).setScale(1, RoundingMode.HALF_UP) + "K";
     * } else {
     * return String.valueOf(gifted);
     * }
     */
    private String formatGifted(int gifted) {
        return String.valueOf(gifted);
    }

    /**
     * 获取朋友圈详情对象
     *
     * @param moment     朋友圈对象
     * @param req        请求
     * @param likeDetail 是否填充like详情
     * @param fillFollow 是否填充follow关系
     * @return 朋友圈详情对象
     */
    private MomentInfoVO getMomentInfoVO(MomentData moment, HttpEnvData req, boolean likeDetail, boolean fillFollow) {
        if (moment == null) {
            return null;
        }
        String uid = req.getUid();
        MomentInfoVO momentInfoVO = new MomentInfoVO();
        momentInfoVO.setMid(moment.get_id().toString());
        momentInfoVO.setAid(moment.getUid());
        momentInfoVO.setText(moment.getText());
        momentInfoVO.setImgs_list(null == moment.getImgs() ? Collections.emptyList() : moment.getImgs());
        momentInfoVO.setAt_list(null == moment.getAt_list() ? Collections.emptyList() : moment.getAt_list());
        momentInfoVO.setQuote(moment.getQuote());
        momentInfoVO.setTheme(moment.getTheme());
        momentInfoVO.setDel((uid.equals(moment.getUid()) || momentConfig.getPOWER_USER().contains(uid)) ? 1 : 0);
        momentInfoVO.setAid(moment.getUid());
        momentInfoVO.setLikes_count(null == moment.getLikes() ? 0 : moment.getLikes().size());
        momentInfoVO.setIs_like(null == moment.getLikes() ? 0 : moment.getLikes().contains(uid) ? 1 : 0);
        momentInfoVO.setOfficial(momentConfig.getOFFICIAL_USER().contains(moment.getUid()) ? 1 : 0);
        momentInfoVO.setIs_official(momentConfig.getOFFICIAL_USER().contains(uid) ? 1 : 0);
        momentInfoVO.setComments(moment.getComments());
        momentInfoVO.setRepost(moment.getRepost());
        momentInfoVO.setTime(moment.getC_time());
        momentInfoVO.setShow(moment.getShow());
        List<MomentInfoVO.MomentLikeVO> likeVOList = new ArrayList<>();
        // 最多8个点赞用户头像
        if (null != moment.getLikes() && likeDetail) {
            List<String> likes = new ArrayList<>(moment.getLikes());
            Collections.reverse(likes);
            for (int i = 0; i < likes.size(); i++) {
                if (i == 8) {
                    break;
                }
                ActorData actorData = actorDao.getActorDataFromCache(likes.get(i));
                if (null == actorData) {
                    continue;
                }
                likeVOList.add(new MomentInfoVO.MomentLikeVO(ImageUrlGenerator.generateRoomUserUrl(actorData)));
            }
        }
        momentInfoVO.setGifted(formatGifted(moment.getGifted()));
        if (moment.getGifted() > 0) {
            List<MomentRewardData> lastSendList = momentRewardService.getLastSendList(momentInfoVO.getMid());
            List<String> giftedList = new ArrayList<>();
            for (MomentRewardData momentRewardData : lastSendList) {
                ActorData actorData = actorDao.getActorDataFromCache(momentRewardData.getUid());
                if (null == actorData) {
                    continue;
                }
                giftedList.add(ImageUrlGenerator.generateMiniUrl(actorData.getHead()));
            }
            momentInfoVO.setGiftedList(giftedList);
        }
        momentInfoVO.setLikes_list(likeVOList);
        ActorData actorData = actorDao.getActorDataFromCache(moment.getUid());
        MomentInfoVO.UserInfoVO userInfoVO = new MomentInfoVO.UserInfoVO();
        if (null != actorData) {
            userInfoVO.setVip_level(vipInfoDao.getIntVipLevelFromCache(actorData.getUid()));
            userInfoVO.setSvipLevel(actorData.getSvipLevel());
            userInfoVO.setHead(ImageUrlGenerator.generateRoomUserUrl(actorData));
            userInfoVO.setAge(actorData.getAge());
            userInfoVO.setName(actorData.getName());
            userInfoVO.setGender(actorData.getFb_gender());
            userInfoVO.setIdentify(userInfoVO.getVip_level() > 0 ? 1 : 0);
            userInfoVO.setCharmLevel(userLevelDao.getUserLevel(actorData.getUid(), UserLevelConstant.CHARM_LEVEL));
            userInfoVO.setWealthLevel(userLevelDao.getUserLevel(actorData.getUid(), UserLevelConstant.WEALTH_LEVEL));
            String inRoomId = !ObjectUtils.isEmpty(mysteryService.getMysteryId(moment.getUid())) ? null : roomPlayerRedis.getActorRoomStatus(moment.getUid());
            if (1 == actorData.getAccept_talk()) {
                userInfoVO.setSstatus(ObjectUtils.isEmpty(inRoomId) ? 0 : 1);
            }
            userInfoVO.setLabelType(getLabelType(req.getUid(), moment.getUid(), inRoomId));
            userInfoVO.setInRoomId(inRoomId);
        }
        momentInfoVO.setUser_info(userInfoVO);
        if (!ObjectUtils.isEmpty(uid)) {
            if (fillFollow) {
                momentInfoVO.setIs_followed(uid.equals(moment.getUid()) ? 1 : followDao.isFollowed(uid, moment.getUid()) ? 1 : 0);
            }
            momentInfoVO.setIs_friend(uid.equals(moment.getUid()) ? 0 : friendsListRedis.isFriend(moment.getUid(), uid) ? 1 : 0);
        }
        if (moment.getComments() > 0) {
            fillLastCommentList(momentInfoVO, moment, req);
        }
        return momentInfoVO;
    }

    private int getLabelType(String uid, String aid, String inRoomId) {
        if (!ObjectUtils.isEmpty(inRoomId) && !whiteTestDao.isMemberByType(inRoomId, WhiteTestDao.WHITE_TYPE_ROOM_ID)) {
            if (RoomUtils.isLiveRoom(inRoomId)) {
                return 3;
            }
            if (roomMicRedis.getRoomMicSetRedis(inRoomId).contains(aid)) {
                return 2;
            }
        }
        return uid.equals(aid) || followDao.isFollowed(uid, aid) ? 0 : 1;
    }

    private void fillLastCommentList(MomentInfoVO momentInfoVO, MomentData moment, HttpEnvData req) {
        List<Comment> commentDetails = commentDao.getMergedPageComments(moment.get_id().toString(), 0, 4);
        List<CommentVO> commentVOList = new ArrayList<>();
        for (Comment comment : commentDetails) {
            if (commentVOList.size() == 3) {
                momentInfoVO.setMoreComment(1);
                break;
            }
            commentVOList.add(commentToVO(req, moment, comment, true));
        }
        momentInfoVO.setLastCommentList(commentVOList);
    }

    @Cacheable(value = "getFriendsList", key = "#p0.uid+'-'+#p0.page", cacheManager = CaffeineCacheConfig.EXPIRE_1M_AFTER_WRITE)
    public AtListVO getFriendsList(MomentDTO req) {
        AtListVO vo = new AtListVO();
        int start = (req.getPage() - 1) * FRIEND_LIST_SIZE;
        ApiResult<List<String>> pageFriendList = friendService.getPageFriendList(req.getUid(), start, start + FRIEND_LIST_SIZE);
        if (pageFriendList.isOk()) {
            List<String> friendListData = pageFriendList.getData();
            if (req.getPage() == 1) {
                List<String> friendsList = msgListService.getRecentlyChat(req.getUid(), 20);
                friendListData.addAll(0, friendsList);
                vo.setNums(friendService.getFriendCount(req.getUid()).getData());
            }
            vo.setList(getActorVOList(friendListData));
        }
        vo.setNextUrl(vo.getList().isEmpty() ? "" : req.getPage() + 1 + "");
        return vo;
    }

    private List<Object> getActorVOList(Collection<String> aidList) {
        List<Object> friendVOList = new ArrayList<>();
        Set<String> distinctSet = new HashSet<>();
        for (String aid : aidList) {
            if (distinctSet.contains(aid)) {
                continue;
            }
            distinctSet.add(aid);
            ActorData actorData = actorDao.getActorDataFromCache(aid);
            if (null == actorData || AccountConstant.DELETED == actorData.getAccountStatus()) {
                logger.info("getActorVOList cannot find actor uid={} accountStatus={}", aid, actorData != null ? actorData.getAccountStatus() : 0);
                continue;
            }
            friendVOList.add(copyActorVO(actorData));
        }
        return friendVOList;
    }

    private ActorVO copyActorVO(ActorData actorData) {
        ActorVO actorVO = new ActorVO();
        actorVO.setAid(actorData.getUid());
        actorVO.setRid(actorData.getRid());
        actorVO.setName(actorData.getName());
        actorVO.setHead(ImageUrlGenerator.generateRoomUserUrl(actorData));
        actorVO.setGender(actorData.getFb_gender());
        actorVO.setViplevel(vipInfoDao.getIntVipLevelFromCache(actorData.getUid()));
        actorVO.setSvipLevel(actorData.getSvipLevel());
        actorVO.setHead(ImageUrlGenerator.generateRoomUserUrl(actorData));
        actorVO.setMicFrame(micFrameRedis.getMicSourceFromCache(actorData.getUid()));
        return actorVO;
    }

    @Cacheable(value = "search", key = "#p0.uid+'-'+#p0.key", cacheManager = CaffeineCacheConfig.EXPIRE_1M_AFTER_WRITE)
    public FriendSearchVO search(MomentDTO req) {
        FriendSearchVO vo = new FriendSearchVO();
        if (null == req.getKey() || ObjectUtils.isEmpty(req.getKey().trim())) {
            throw new CommonException(HttpCode.PARAM_ERROR);
        }
        String key = req.getKey().trim();
        try {
            ActorData actorData = actorDao.getActorByRidOrAlphaRid(key);
            if (null != actorData && friendsListRedis.isFriend(req.getUid(), actorData.getUid())) {
                vo.getUsers().add(copyActorVO(actorData));
            }
        } catch (NumberFormatException e) {
            ApiResult<Set<String>> friendSet = friendService.getFriendSet(req.getUid());
            if (friendSet.isOk()) {
                Set<String> friends = friendSet.getData();
                if (!CollectionUtils.isEmpty(friends)) {
                    List<MongoActorData> actors = actorDao.getActorsByName(key, friends);
                    for (MongoActorData actor : actors) {
                        ActorVO actorVO = new ActorVO();
                        actorVO.setAid(actor.get_id().toString());
                        actorVO.setRid(actor.getRid());
                        actorVO.setName(actor.getName());
                        actorVO.setViplevel(vipInfoDao.getIntVipLevelFromCache(actorVO.getAid()));
                        actorVO.setSvipLevel(actor.getSvipLevel());
                        actorVO.setHead(ImageUrlGenerator.generateRoomUserUrl(actor.getHead(), actor.getSvipLevel()));
                        actorVO.setGender(actor.getFb_gender());
                        vo.getUsers().add(actorVO);
                    }
                }
            }
        }
        return vo;
    }

    public CommentResultVO comment(CommentDTO req) {
        if (ObjectUtils.isEmpty(req.getContent())) {
            throw new CommonException(MomentHttpCode.PARAM_ERROR);
        }
        if (req.getContent().length() > 300) {
            throw new CommonException(MomentHttpCode.COMMENT_TOO_LENGTH);
        }
        if (req.getAt_list() != null && req.getAt_list().size() > 10) {
            throw new CommonException(MomentHttpCode.AT_LIMIT, 10);
        }
        checkFroze(req);
        checkDirtyWord(req.getUid(), req.getContent(), DetectOriginConstant.MOMENT_COMMENT);
        MomentData moment = getMoment(req.getMid());
        if (blackListDao.isBlock(moment.getUid(), req.getUid())) {
            throw new CommonException(MomentHttpCode.BLOCKED_BY_ACTOR);
        }
        // 获取原始评论id
        String originalId = null;
        String sourceText = null;
        if (!ObjectUtils.isEmpty(req.getComment_id())) {
            Comment comment = commentDao.getComment(req.getComment_id());
            if (null == comment) {
                logger.info("cannot find comment uid={} commentId={}", req.getUid(), req.getComment_id());
                throw new CommonException(MomentHttpCode.COMMENT_NOT_EXIST);
            }
            sourceText = comment.getContent();
            commentDao.incrCommentReplyCount(req.getComment_id(), 1);
            if (!ObjectUtils.isEmpty(comment.getOriginal_id())) {
                originalId = comment.getOriginal_id();
                commentDao.incrCommentReplyCount(originalId, 1);
            } else {
                originalId = req.getComment_id();
            }
        }
        Comment comment = new Comment(req.getMid(), req.getUid(), req.getContent(), req.getReply_to(), req.getComment_id(), req.getAt_list(), originalId);
        commentDao.save(comment);
        if (null == comment.get_id()) {
            logger.error("save comment error, uid={} mid={}", req.getUid(), req.getMid());
            throw new CommonException();
        }
        // 用户等级任务
        levelTaskService.sendTaskDataToMq(new UserLevelTaskData(moment.getUid(), UserLevelConstant.MOMENT_COMMENT, req.getUid()));
        levelTaskService.sendTaskDataToMq(new UserLevelTaskData(req.getUid(), UserLevelConstant.COMMENT_MOMENT, req.getMid()));
        // 评论通知
        sendCommentNotice(moment, comment, req, sourceText);
        momentDao.incrMomentComments(req.getMid(), 1);
        if (PhoneNumberChecker.containsIllegalPhoneNumber(req.getContent())) {
            blockComment("[SystemDetection]", comment.get_id().toString(), 1);
        }
        return new CommentResultVO(req.getComment_id(), commentToVO(req, moment, comment, true));
    }

    private void sendCommentNotice(MomentData moment, Comment comment, CommentDTO req, String sourceText) {
        if (ObjectUtils.isEmpty(req.getReply_to())) {
            // 通知博主
            if (!req.getUid().equals(moment.getUid())) {
                MomentNotice momentNotice = new MomentNotice(moment.getUid(), req.getUid(),
                        MomentConstant.NOTICE_COMMENT, req.getMid(), comment.getC_time(), req.getContent(),
                        req.getAt_list(), comment.get_id().toString());
                fillMomentNotice(moment, momentNotice);
                momentNoticeService.addNotice(moment.getUid(), momentNotice, moment.getUid());
            }
        } else {
            // 通知回复用户
            MomentNotice momentNotice = new MomentNotice(req.getReply_to(), req.getUid(),
                    MomentConstant.NOTICE_COMMENT_REPLAY, req.getMid(), comment.getC_time(), req.getContent(),
                    req.getAt_list(), comment.get_id().toString());
            momentNotice.setSourceText(sourceText);
            momentNoticeService.addNotice(req.getReply_to(), momentNotice, moment.getUid());
        }
        // 通知@用户
        if (moment.getShow() != MomentConstant.MOMENT_PRIVATE && !CollectionUtils.isEmpty(req.getAt_list())) {
            Set<String> ownerFriends = Collections.emptySet();
            if (moment.getShow() == MomentConstant.MOMENT_FRIENDS) {
                ApiResult<Set<String>> friendSet = friendService.getFriendSet(moment.getUid());
                if (friendSet.isOk()) {
                    ownerFriends = friendSet.getData();
                }
            }
            Set<String> atSet = new HashSet<>();
            for (MomentData.AtUser atUser : req.getAt_list()) {
                if (null != atUser && atUser.getAid().length() == 24) {
                    if (atSet.contains(atUser.getAid())) {
                        continue;
                    }
                    atSet.add(atUser.getAid());
                    if (moment.getShow() == MomentConstant.MOMENT_FRIENDS) {
                        if (!ownerFriends.contains(atUser.getAid())) {
                            logger.info("send comment notice uid={} is not aid={} friend.", atUser.getAid(), moment.getUid());
                            continue;
                        }
                    }
                    MomentNotice momentNotice = new MomentNotice(atUser.getAid(), req.getUid(),
                            MomentConstant.NOTICE_COMMENT_AT, req.getMid(), comment.getC_time(), req.getContent(),
                            req.getAt_list(), comment.get_id().toString());
                    momentNotice.setSourceText(req.getContent());
                    momentNoticeService.addNotice(atUser.getAid(), momentNotice, moment.getUid());
                }
            }
        }
    }

    public CommentListVO commentDetail(CommentDTO req) {
        MomentData moment = getMoment(req.getMid());
        CommentListVO vo = new CommentListVO();
        int start = (req.getPage() - 1) * COMMENT_DETAIL_LIST_SIZE;
        Comment originalComment = commentDao.getComment(req.getComment_id());
        if (null == originalComment) {
            logger.info("cannot find comment uid={} commentId={}", req.getUid(), req.getComment_id());
            throw new CommonException(MomentHttpCode.COMMENT_NOT_EXIST);
        }
        List<Comment> commentDetails = commentDao.getPageCommentDetails(req.getComment_id(), start, COMMENT_DETAIL_LIST_SIZE);
        List<CommentVO> commentVOList = new ArrayList<>();
        for (Comment comment : commentDetails) {
            CommentVO commentVO = commentToVO(req, moment, comment, !req.getComment_id().equals(comment.getComment_id()));
            commentVOList.add(commentVO);
        }
        if (1 == req.getPage()) {
            vo.setComment(commentToVO(req, moment, originalComment, true));
        }
        vo.setList(commentVOList);
        vo.setNextUrl(commentVOList.size() < COMMENT_DETAIL_LIST_SIZE ? "" : req.getPage() + 1 + "");
        return vo;
    }

    public MomentVO commentList(CommentDTO req) {
        MomentData moment = getMoment(req.getMid());
        MomentVO vo = new MomentVO();
        int start = (req.getPage() - 1) * COMMENT_DETAIL_LIST_SIZE;
        List<Comment> commentDetails;
        if (req.getSortBy() == 0) {
            commentDetails = commentDao.getCommentsByPopular(req.getMid(), start, COMMENT_DETAIL_LIST_SIZE);
        } else {
            commentDetails = commentDao.getMergedPageComments(req.getMid(), start, COMMENT_DETAIL_LIST_SIZE);
        }
        List<Object> commentVOList = new ArrayList<>();
        for (Comment comment : commentDetails) {
            CommentVO commentVO = commentToVO(req, moment, comment, true);
            if (comment.getReply_count() > 0) {
                // 获取最新的一条回复
                Comment latestComment = commentDao.getLatestComment(comment.get_id().toString());
                if (null != latestComment) {
                    commentVO.setSubList(Collections.singletonList(commentToVO(req, moment, latestComment, true)));
                }
            }
            commentVOList.add(commentVO);
        }
        vo.setList(commentVOList);
        vo.setNextUrl(commentVOList.size() < COMMENT_DETAIL_LIST_SIZE ? "" : req.getPage() + 1 + "");
        return vo;
    }

    public void commentLike(CommentDTO req) {
        MomentData moment = getMoment(req.getMid());
        Comment comment = commentDao.getComment(req.getComment_id());
        if (null == comment) {
            logger.info("cannot find comment uid={} commentId={}", req.getUid(), req.getComment_id());
            throw new CommonException(MomentHttpCode.COMMENT_NOT_EXIST);
        }
        if (1 == req.getOpt()) {
            // 点赞评论
            commentDao.addLikes(req.getComment_id(), req.getUid());
            if (!req.getUid().equals(comment.getCommentator())) {
                momentNoticeService.addNotice(comment.getCommentator(), new MomentNotice(comment.getCommentator(), req.getUid(),
                        MomentConstant.NOTICE_COMMENT_LIKE, req.getMid(), null, comment.getContent(), req.getComment_id()), moment.getUid());
            }
        } else if (0 == req.getOpt()) {
            // 取消点赞评论
            commentDao.removeLikes(req.getComment_id(), req.getUid());
            momentNoticeService.deleteNotice(req.getUid(), comment.getCommentator(), req.getMid(), MomentConstant.NOTICE_COMMENT_LIKE, req.getComment_id());
        }
    }

    public void delComment(CommentDTO req) {
        MomentData moment = getMoment(req.getMid());
        Comment comment = commentDao.findAndRemove(req.getComment_id());
        if (null == comment) {
            throw new CommonException(MomentHttpCode.COMMENT_NOT_EXIST);
        }
        if (!req.getUid().equals(moment.getUid()) && !req.getUid().equals(comment.getCommentator())
                && !momentConfig.getPOWER_USER().contains(req.getUid())) {
            throw new CommonException(MomentHttpCode.COMMENT_AUTH_ERROR);
        }
        momentDao.incrMomentComments(req.getMid(), -1);
        if (null != comment.getOriginal_id()) {
            commentDao.incrCommentReplyCount(comment.getOriginal_id(), -1);
        }
        momentNoticeService.deleteNoticeByCommentId(moment.getUid(), req.getMid(), req.getComment_id());
    }

    public void like(LikeDTO req) {
        try (DistributeLock lock = new DistributeLock("momentLike_" + req.getMid())) {
            lock.lock();
            MomentData moment = getMoment(req.getMid());
            if (blackListDao.isBlock(moment.getUid(), req.getUid())) {
                throw new CommonException(MomentHttpCode.BLOCKED_BY_ACTOR);
            }
            if (1 == req.getOpt()) {
                if (null != moment.getLikes() && moment.getLikes().contains(req.getUid())) {
                    return;
                }
                // 用户等级任务
                levelTaskService.sendTaskDataToMq(new UserLevelTaskData(req.getUid(), UserLevelConstant.LIKE_MOMENT, req.getMid()));
                momentDao.addLikes(req.getMid(), req.getUid());
                if (!req.getUid().equals(moment.getUid())) {
                    MomentNotice momentNotice = new MomentNotice(moment.getUid(), req.getUid(),
                            MomentConstant.NOTICE_LIKE, req.getMid(), null, moment.getText(), null);
                    fillMomentNotice(moment, momentNotice);
                    momentNoticeService.addNotice(moment.getUid(), momentNotice, moment.getUid());
                    // 用户等级任务
                    levelTaskService.sendTaskDataToMq(new UserLevelTaskData(moment.getUid(), UserLevelConstant.MOMENT_LIKED, req.getUid()));
                }
            } else if (0 == req.getOpt()) {
                // 取消点赞
                momentDao.removeLikes(req.getMid(), req.getUid());
                momentNoticeService.deleteNotice(req.getUid(), moment.getUid(), req.getMid(), MomentConstant.NOTICE_LIKE);
            }
        }
    }

    public RewardListVO rewardList(RewardListDTO req) {
        RewardListVO vo = new RewardListVO();
        List<RewardListVO.GiftRewardVO> rewardList = momentRewardService.getRewardList(req.getMid(), req.getPage());
        for (RewardListVO.GiftRewardVO giftRewardVO : rewardList) {
            ActorData actorData = actorDao.getActorDataFromCache(giftRewardVO.getAid());
            if (null == actorData) {
                continue;
            }
            giftRewardVO.setRid(actorData.getRid());
            giftRewardVO.setName(actorData.getName());
            giftRewardVO.setGender(actorData.getFb_gender());
            giftRewardVO.setViplevel(vipInfoDao.getIntVipLevelFromCache(actorData.getUid()));
            giftRewardVO.setSvipLevel(actorData.getSvipLevel());
            giftRewardVO.setHead(ImageUrlGenerator.generateRoomUserUrl(actorData));
        }
        vo.setList(rewardList);
        vo.setNextUrl(vo.getList().isEmpty() ? "" : String.valueOf(req.getPage() + 1));
        return vo;
    }

    /**
     * 右边动态内容显示规则：有图片动态显示第一张图、链接动态显示链接图标、视频动态显示视频封面、纯文本动态显示文本内容，最多显示3行。
     * 优先级：img -> link -> sourceText
     */
    public void fillMomentNotice(MomentData moment, MomentNotice momentNotice) {
        momentNotice.setSourceText(moment.getText());
        if (!CollectionUtils.isEmpty(moment.getImgs())) {
            momentNotice.setImg(moment.getImgs().get(0).getThumbnail());
            return;
        }
        if (null != moment.getQuote()) {
            if (MomentConstant.QUOTE_YOUTUBE_LINK == moment.getQuote().getType()) {
                momentNotice.setImg(moment.getQuote().getIcon());
            } else if (MomentConstant.QUOTE_LINK == moment.getQuote().getType()) {
                momentNotice.setLink(1);
            }
        }
    }

    public MomentVO likeList(MomentDTO req) {
        MomentData moment = getMoment(req.getMid());
        MomentVO vo = new MomentVO();
        vo.setCode(0);
        if (null == moment.getLikes()) {
            vo.setNums(0);
            vo.setNextUrl("");
            vo.setList(Collections.emptyList());
        } else {
            List<String> likes = new ArrayList<>(moment.getLikes());
            Collections.reverse(likes);
            PageUtils.PageData<String> pageData = PageUtils.getPageData(likes, req.getPage(), LIKE_LIST_SIZE);
            vo.setNextUrl(pageData.nextPage == 0 ? "" : String.valueOf(pageData.nextPage));
            vo.setList(getActorVOList(pageData.list));
            vo.setNums(vo.getList().size());
        }
        return vo;
    }

    public void report(MomentDTO req) {
        momentDao.incrReports(req.getMid());
    }

    /**
     * 管理员直接拉黑该用户，该用户无法再发动态；普通用户直接拉黑该用户
     */
    public void block(MomentDTO req) {
        MomentData moment = getMoment(req.getMid());
        if (req.getUid().equals(moment.getUid())) {
            return;
        }
        if (momentConfig.getOFFICIAL_USER().contains(req.getUid()) || momentConfig.getPOWER_USER().contains(req.getUid())) {
            momentShowRedis.deleteMomentShow(moment, req.getUid());
            momentShowRedis.removeMomentSquareList(moment, req.getUid());
            momentDao.delete(moment, moment.get_id().toString());
        } else {
            blackListDao.addBlock(req.getUid(), moment.getUid());
        }
    }

    public UnreadCheckVO unreadCheck(MomentDTO req) {
        return new UnreadCheckVO(actorDao.getMomentUnread(req.getUid()), momentNoticeService.getNoticeCount(req.getUid()));
    }

    public MomentVO showPersonalMoment(String uid, String aid, Integer page, HttpEnvData envData) {
        MomentVO vo = new MomentVO();
        int start = (page - 1) * PERSONAL_LIST_SIZE;
        List<MomentData> momentDataList;
        if (Objects.equals(uid, aid)) {
            momentDataList = momentDao.getMomentList(aid, SELF_SHOW, start, PERSONAL_LIST_SIZE);
        } else if (friendsListRedis.isFriend(uid, aid)) {
            momentDataList = momentDao.getMomentList(aid, FRIEND_SHOW, start, PERSONAL_LIST_SIZE);
        } else {
            momentDataList = momentDao.getMomentList(aid, STRANGER_SHOW, start, PERSONAL_LIST_SIZE);
        }
        for (MomentData moment : momentDataList) {
            MomentInfoVO momentInfoVO = getMomentInfoVO(moment, envData, false, true);
            if (null != momentInfoVO) {
                vo.getList().add(momentInfoVO);
            }
        }
        vo.setNextUrl(momentDataList.size() < PERSONAL_LIST_SIZE ? "" : page + 1 + "");
        vo.setIs_official(momentConfig.getOFFICIAL_USER().contains(uid) ? 1 : 0);
        return vo;
    }

    private List<Object> noticeToVO(List<MomentNotice> notices) {
        List<Object> noticeVOList = new ArrayList<>();
        for (MomentNotice momentNotice : notices) {
            ActorData actorData = actorDao.getActorDataFromCache(momentNotice.getAid());
            if (null == actorData) {
                continue;
            }
            NoticeVO noticeVO = new NoticeVO();
            noticeVO.setMid(momentNotice.getMoment_id());
            noticeVO.setAid(momentNotice.getAid());
            noticeVO.setName(actorData.getName());
            noticeVO.setHead(ImageUrlGenerator.generateRoomUserUrl(actorData));
            noticeVO.setText(momentNotice.getText());
            noticeVO.setSourceText(momentNotice.getSourceText());
            noticeVO.setLink(momentNotice.getLink());
            noticeVO.setType(momentNotice.getAction_atype());
            noticeVO.setTime(momentNotice.getC_time());
            noticeVO.setComment_id(momentNotice.getComment_id());
            noticeVO.setAt_list(momentNotice.getAt_list());
            noticeVO.setGender(actorData.getFb_gender());
            noticeVO.setImg(momentNotice.getImg());
            noticeVO.setGiftReward(momentNotice.getGiftReward());
            noticeVOList.add(noticeVO);
        }
        return noticeVOList;
    }

    public MomentVO noticeList(MomentDTO req) {
        if (req.getPage() == 1) {
            momentNoticeService.clearUnread(req.getUid());
        }
        MomentVO vo = new MomentVO();
        vo.setList(noticeToVO(momentNoticeService.gePageNotice(req.getUid(), req.getOpt(), req.getPage(), NOTICE_LIST_SIZE)));
        vo.setNextUrl(vo.getList().size() < NOTICE_LIST_SIZE ? "" : req.getPage() + 1 + "");
        return vo;
    }

    public void clearNotice(MomentDTO req) {
        momentNoticeService.clearNotice(req.getUid(), req.getOpt());
    }

    public void setTop(MomentDTO req) {
        if (ObjectUtils.isEmpty(req.getMid())) {
            throw new CommonException(MomentHttpCode.PARAM_ERROR);
        }
        if (!momentConfig.getOFFICIAL_USER().contains(req.getUid())) {
            logger.error("set top moment error, actor is not official user uid={}", req.getUid());
            throw new CommonException(MomentHttpCode.AUTH_ERROR);
        }
        if (1 == req.getOpt()) {
            // 置顶
            if (!topMomentRedis.getTopMoment().contains(req.getMid())) {
                topMomentRedis.addTopMoment(req.getUid(), req.getMid());
            }
        } else if (2 == req.getOpt()) {
            // 取消置顶
            topMomentRedis.deleteTopMoment(req.getUid(), req.getMid());
        }
    }

    @Cacheable(value = "getBanner", key = "#p0", cacheManager = CaffeineCacheConfig.EXPIRE_1M_AFTER_WRITE)
    public BannerVO getBanner(int slang) {
        BannerVO vo = new BannerVO();
        List<IndexBannerData> indexBannerList = indexBannerDao.findAllData(2);
        List<BannerVO.Banner> list = new ArrayList<>();
        for (IndexBannerData data : indexBannerList) {
            int nowTime = DateHelper.getNowSeconds();
            if (data.getStart_time() > 0 && data.getStart_time() > nowTime) {
                continue;
            }
            if (data.getEnd_time() > 0 && data.getEnd_time() < nowTime) {
                continue;
            }
            BannerVO.Banner banner = new BannerVO.Banner();
            banner.setTitle(slang == SLangType.ENGLISH ? data.getTitle() : data.getTitle_ar());
            banner.setPreview(slang == SLangType.ENGLISH ? data.getPreview() : data.getPreview_ar());
            String url = "";
            if (!ObjectUtils.isEmpty(data.getImage_url())) {
                url = data.getImage_url();
            } else if (!ObjectUtils.isEmpty(data.getUrl())) {
                url = data.getUrl();
            }
            banner.setUrl(url);
            list.add(banner);
        }
        vo.setList(list);
        return vo;
    }

    public MomentVO showMomentList(MomentDTO req) {
        if ("follow".equals(req.getKey())) {
            momentUnreadRedis.clearFollowUnread(req.getUid());
            return getPrivateMoment(req);
        } else {
            MomentVO vo = new MomentVO();
            Set<String> topMidSet = new HashSet<>();
            // 官方置顶
            if (req.getPage() == 1) {
                for (String topMid : topMomentRedis.getTopMomentFromCache()) {
                    MomentInfoVO momentInfoVO = getMomentInfoVO(momentDao.getMoment(topMid), req, false, false);
                    if (null == momentInfoVO) {
                        // 清理脏数据
                        logger.error("cannot find top moment, remove top moment. mid={}", topMid);
                        topMomentRedis.deleteTopMoment("[system]", topMid);
                    } else {
                        topMidSet.add(momentInfoVO.getMid());
                        momentInfoVO.setIs_top(1);
                        vo.getList().add(momentInfoVO);
                    }
                }
            }
            fillSquareMoment(req, vo, topMidSet);
            vo.setNextUrl(CollectionUtils.isEmpty(vo.getList()) ? "" : req.getPage() + 1 + "");
            vo.setRicky(momentNoticeService.getNoticeCount(req.getUid()));
            vo.setIs_official(momentConfig.getOFFICIAL_USER().contains(req.getUid()) ? 1 : 0);
            return vo;
        }
    }

    private void addOptNotice(MomentData moment, int noticeType) {
        MomentNotice momentNotice = new MomentNotice(moment.getUid(), moment.getUid(),
                noticeType, moment.get_id().toString(), null, moment.getText(), null);
        fillMomentNotice(moment, momentNotice);
        momentNoticeService.addNotice(moment.getUid(), momentNotice, moment.getUid());
    }

    public void pinMoment(String opName, String mid, Integer opType, Integer endTime) {
        MomentData moment = momentDao.getMoment(mid);
        if (null == moment) {
            throw new CommonException(HttpCode.PARAM_ERROR);
        }
        if (1 == opType) {
            topMomentRedis.addTopMoment("[system]", mid);
            if (null != endTime) {
                topMomentRedis.addTopMomentEndTime(mid, endTime);
            }
            addOptNotice(moment, MomentConstant.NOTICE_MOMENT_PINED);
        } else {
            topMomentRedis.deleteTopMoment("[system]", mid);
            addOptNotice(moment, MomentConstant.NOTICE_MOMENT_UNPIN);
        }
        momentOpDao.addMomentOpLog(opName, moment, null, opType == 1 ? 1 : 2);
    }

    public void blockMoment(String opName, String mid, Integer opType) {
        MomentData moment = momentDao.getMoment(mid);
        if (null == moment) {
            throw new CommonException(HttpCode.PARAM_ERROR);
        }
        momentDao.updateField(mid, "is_blocked", 1 == opType ? 1 : 2);
        addOptNotice(moment, 1 == opType ? MomentConstant.NOTICE_MOMENT_BLOCKED : MomentConstant.NOTICE_MOMENT_UNBLOCK);
        momentOpDao.addMomentOpLog(opName, moment, null, opType == 1 ? 3 : 4);
    }

    public void blockComment(String opName, String cid, Integer opType) {
        Comment comment = commentDao.getComment(cid);
        if (null == comment) {
            throw new CommonException(HttpCode.PARAM_ERROR);
        }
        commentDao.updateField(cid, "is_blocked", 1 == opType ? 1 : 2);
        MomentNotice momentNotice = new MomentNotice(comment.getCommentator(), comment.getCommentator(),
                1 == opType ? MomentConstant.NOTICE_COMMENT_BLOCKED : MomentConstant.NOTICE_COMMENT_UNBLOCK,
                comment.getMoment_id(), DateHelper.getNowSeconds(), comment.getContent(), comment.getAt_list(), comment.get_id().toString());
        momentNoticeService.addNotice(comment.getCommentator(), momentNotice, comment.getCommentator());
        momentOpDao.addMomentOpLog(opName, null, comment, opType == 1 ? 5 : 6);
    }

    public void expiredTopMoment() {
        int now = DateHelper.getNowSeconds();
        Set<String> midSet = topMomentRedis.getTopMomentIdByTime(now);
        if (!CollectionUtils.isEmpty(midSet)) {
            midSet.forEach(item -> {
                topMomentRedis.removeTopMomentEndTime(item);
                topMomentRedis.deleteTopMoment("", item);
                logger.info("delete moment id success mid:{}", item);
            });
        }
    }
}
