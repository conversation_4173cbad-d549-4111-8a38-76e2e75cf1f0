package com.quhong.service;

import com.quhong.core.utils.DateHelper;
import com.quhong.data.ActorData;
import com.quhong.data.dto.BadgeListDTO;
import com.quhong.data.dto.HonorQueryDTO;
import com.quhong.data.vo.BadgeListVO;
import com.quhong.data.vo.HonorQueryInfoVO;
import com.quhong.enums.*;
import com.quhong.exception.CommonException;
import com.quhong.handler.HttpEnvData;
import com.quhong.mongo.dao.*;
import com.quhong.mongo.data.*;
import com.quhong.msg.chat.OfficialPushMsg;
import com.quhong.mysql.dao.UserHonorLogDao;
import com.quhong.mysql.data.UserHonorLogData;
import com.quhong.room.RoomWebSender;
import jakarta.annotation.PostConstruct;
import jakarta.annotation.Resource;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.data.mongodb.core.query.Update;
import org.springframework.stereotype.Service;

import java.util.*;


/**
 * <AUTHOR>
 * @date 2022/11/16
 */
@Service
public class HonorService {

    private static final Logger logger = LoggerFactory.getLogger(HonorService.class);
    private static final String HONOR_OFFICIAL_TITLE_EN = "Thank You";
    private static final String HONOR_OFFICIAL_TITLE_AR = "شكرا لك";
    private static final String HONOR_OFFICIAL_BODY_EN = "Dear honored guest, kindly accept a thanks from waho management for your recharge, and wish you enjoy your time in waho!";
    private static final String HONOR_OFFICIAL_BODY_AR = "ضيفنا العزيز، نود أن تقبل شكر من إدارة واهو لشحنك، و نتمنى أن تستمتع بوقتك  في واهو!";
    private final List<Long> honorChargeBeansList = new ArrayList<>();
    private int honorMaxPos;

    @PostConstruct
    public void postInit() {
        for (long i = 0; i < 100; i++) {
            honorChargeBeansList.add(i * 100 * 1000);
        }
        for (long i = 0; i < 10; i++) {
            honorChargeBeansList.add((50 + i * 1000) * 1000);
        }
        for (long i = 10; i <= 50; i++) {
            honorChargeBeansList.add(i * 1000 * 1000);
        }
        Comparator<Long> comparator = Comparator.comparing(o -> o);
        honorChargeBeansList.sort(comparator);
        honorMaxPos = honorChargeBeansList.size() - 1;
        logger.info("maxPos:{} honorChargeBeansList size:{}", honorMaxPos, honorChargeBeansList.size());
    }


    @Resource
    private ResourceDistributionService distributionService;
    @Resource
    private ActorDao actorDao;
    @Resource
    private OfficialDao officialDao;
    @Resource
    private NoticeNewDao noticeNewDao;
    @Resource
    private NewUserHonorDao newUserHonorDao;
    @Resource
    protected RoomWebSender roomWebSender;
    @Resource
    private HonorAwardDao honorAwardDao;
    @Resource
    private BadgeDao badgeDao;
    @Resource
    private BadgeListDao badgeListDao;
    @Resource
    private JoinSourceDao joinSourceDao;
    @Resource
    private JoinCartonDao joinCartonDao;
    @Resource
    private UserHonorLogDao userHonorLogDao;

    private void officialMsgPush(String uid) {
        ActorData actorData = actorDao.getActorDataFromCache(uid);
        if (actorData == null) {
            logger.error("can not find actor. uid={}", uid);
            return;
        }
        int slang = actorData.getSlang() == 1 ? 1 : 2;
        OfficialData officialData = new OfficialData();
        officialData.setSubTitle("");
        officialData.setTo_uid(uid);
        officialData.setValid(1);
        officialData.setCtime(DateHelper.getNowSeconds());
        officialData.setAtype(1);
        officialData.setTitle(slang == SLangType.ARABIC ? HONOR_OFFICIAL_TITLE_AR: HONOR_OFFICIAL_TITLE_EN);
        officialData.setBody(slang == SLangType.ARABIC ? HONOR_OFFICIAL_BODY_AR : HONOR_OFFICIAL_BODY_EN);
        officialDao.save(officialData);
        if (officialData.get_id() != null) {
            noticeNewDao.save(new NoticeNewData(uid, officialData.get_id().toString()));

            OfficialPushMsg msg = new OfficialPushMsg();
            msg.setTitle(officialData.getTitle());
            msg.setBody(officialData.getBody());
            roomWebSender.sendPlayerWebMsg("", uid, uid, msg, false);
        }
    }

    /**
     * 通过钻石数获取荣耀等级
     */

    private int getLevelByRechargeDiamond(long totalDiamond){
       List<Long> tempChargeBeansList = new ArrayList<>(honorChargeBeansList);
        if(tempChargeBeansList.contains(totalDiamond)){
            return tempChargeBeansList.indexOf(totalDiamond);
        }

        tempChargeBeansList.add(totalDiamond);
        tempChargeBeansList.sort(Long::compare);
       return tempChargeBeansList.indexOf(totalDiamond) - 1;
    }

    /**
     * 通过荣耀等级获取靓号延续时长
     */
    private int getBeautifulRidContinueDay(int honorLevel){
        int continueDay = 0;
        
        if (honorLevel == 1) {
            continueDay = 0;
        } else if (honorLevel == 2) {
            continueDay = 60;
        } else if (honorLevel == 3) {
            continueDay = 90;
        } else if (honorLevel == 4) {
            continueDay = 120;
        } else if (honorLevel >= 5 && honorLevel <= 7) {
            continueDay = 150;
        } else if (honorLevel >= 8) {
            continueDay = 10000;
        }
        
        return continueDay;
    }


    /**
     * 通过荣耀等级获取坐骑时长
     */
    private int getJoinContinueDay(int honorLevel){
        int continueDay;

        if (honorLevel == 1) {
            continueDay = 7;
        } else if (honorLevel >= 2 && honorLevel <= 10) {
            continueDay = 15;
        } else {
            continueDay = 30;
        }

        return continueDay;
    }

    private List<String> getHonorLevelBeautifulRid(int level){
        if (level >= 2 && level <= 3) {
            return Collections.singletonList("AABBCC");
        } else if (level == 4) {
            return Arrays.asList("AAABBB", "AABBCC");
        } else if (level >= 5) {
            return Collections.singletonList("ALL");
        }else {
            return new ArrayList<>();
        }
    }


    public void handleHonorJoin(String uid, int honorLevel, HonorAwardData honorAwardData) {
        try {
            List<Integer> joinList = honorAwardData.getJoinList();
            int joinContinueDay = getJoinContinueDay(honorLevel);

            if(joinList == null || joinList.isEmpty()){
                return;
            }

            for(int joinId : joinList){
                distributionService.sendResourceReward(uid, joinId, RewardTypeEnum.RIDE, BaseDataResourcesConstant.ACTION_GET, joinContinueDay, 1, "honor");
            }
        } catch (Exception e) {
            logger.error("handleHonorJoin error, uid={}, e={}", uid, e.getMessage(), e);
        }

    }


    /**
     *
     * @param uid 用户id
     * @param rechargeType 充值类型
     * @param rechargeDiamond 充值钻石数
     */
    public void handleHonorRecharge(String uid, int rechargeType, int rechargeDiamond){
        if (rechargeType == 1 && rechargeDiamond >= 2000) {
            officialMsgPush(uid);
        }

        int preHonorLevel = 0; // 加钻石之前的荣耀等级
        int currentHonorLevel = 0; // 加钻石之后的荣耀等级
        long totalDiamond = 0; // 总充值钻石数
        long oldDiamond = 0; // 加钻石之前钻石数
        int currentTime = DateHelper.getNowSeconds();

        NewUserHonorData honorData = newUserHonorDao.findData(uid);
        if(honorData == null){
            totalDiamond = rechargeDiamond;
            currentHonorLevel = getLevelByRechargeDiamond(totalDiamond);

            honorData = new NewUserHonorData();
            honorData.set_id(uid);
            honorData.setBeans(totalDiamond);
            honorData.setGet_time(currentTime);
            honorData.setHonor_level(currentHonorLevel);
            newUserHonorDao.insert(honorData);

        }else {
            preHonorLevel = honorData.getHonor_level();
            oldDiamond = honorData.getBeans();
            totalDiamond = oldDiamond + rechargeDiamond;
            currentHonorLevel = getLevelByRechargeDiamond(totalDiamond);


            Update update = new Update();
            update.set("beans", totalDiamond);
            if( currentHonorLevel > preHonorLevel){
                update.set("get_time", currentTime);
                update.set("honor_level", currentHonorLevel);
            }
            newUserHonorDao.update(uid, preHonorLevel, currentHonorLevel, totalDiamond);
        }

        logger.info("handleHonorRecharge info uid:{}, preHonorLevel: {}, currentHonorLevel: {}, oldDiamond:{}, rechargeDiamond:{}, totalDiamond:{}", uid, preHonorLevel, currentHonorLevel, oldDiamond, rechargeDiamond, totalDiamond);
        UserHonorLogData userHonorLogData = new UserHonorLogData();
        userHonorLogData.setUid(uid);
        userHonorLogData.setRechargeType(rechargeType);
        userHonorLogData.setBeforeHonorLevel(preHonorLevel);
        userHonorLogData.setAfterHonorLevel(currentHonorLevel);
        userHonorLogData.setBeforeBean(oldDiamond);
        userHonorLogData.setChangeBean(rechargeDiamond);
        userHonorLogData.setAfterBean(totalDiamond);
        userHonorLogData.setCtime(DateHelper.getNowSeconds());
        userHonorLogDao.insert(userHonorLogData);


        if(currentHonorLevel > preHonorLevel){
            // 荣耀勋章处理
            // 荣耀入场动画处理
            HonorAwardData honorAwardData = honorAwardDao.findDataByHonorLevel(currentHonorLevel);
            if(honorAwardData == null){
                logger.info("honorAwardData not find uid:{}, currentHonorLevel: {}", uid, currentHonorLevel);
                return;
            }
            // handleHonorBadge(uid, currentHonorLevel, honorAwardData);
            handleHonorJoin(uid, currentHonorLevel, honorAwardData);
        }
    }


    /**
     *  获取荣誉等级名称
     */
    private String getLevelName(int level){

        if (level == 0) {
            return "RS0";
        }
        int nowLevel = Math.min(level, honorMaxPos);
        long baseBeans = honorChargeBeansList.get(nowLevel) / 1000;
        return baseBeans > 0 ? String.format("RS%dK", baseBeans) : "RS0";
    }


    /**
     * 获取勋章图标
     * @param queryLevel  查询荣誉等级
     * @param currentLevel 当前荣誉等级
     * @return 勋章图标列表
     */
    private List<String> getBadgeIconListByLevel(int queryLevel, int currentLevel, HonorAwardData honorAwardData){

        List<String> badgeIconList = new ArrayList<>();
        List<Integer> badgeList = honorAwardData.getBadgeList();

        for(Integer badgeId : badgeList){
            BadgeListData badgeObj = badgeListDao.getBadgeListData(badgeId);

            String iconUrl = queryLevel <= currentLevel?badgeObj.getIcon(): badgeObj.getIcon_back();
            badgeIconList.add(iconUrl);
        }

        return badgeIconList;
    }

    private void fillRideInfoByLevel(String uid, int queryLevel, int currentLevel, int slang,
                                     HonorQueryInfoVO.QueryLevelInfo levelInfo, HonorAwardData honorAward){

        List<HonorQueryInfoVO.RideInfo> rideInfoList = new ArrayList<>();
        List<Integer> joinList = honorAward.getJoinList();
        List<Integer> rideRemainingList = new ArrayList<>();
        int currentTime = DateHelper.getNowSeconds();
        // 填充坐骑信息
        int joinCartonDays = getJoinContinueDay(queryLevel);
        levelInfo.setRide_validity(joinCartonDays < 10000 ? joinCartonDays : 0);

        for (Integer joinId : joinList) {
            JoinSourceData joinObj = joinSourceDao.getSourceData(joinId);
            HonorQueryInfoVO.RideInfo rideInfo = new HonorQueryInfoVO.RideInfo();
            rideInfo.setJoin_carton_id(joinObj.getJoin_carton_id());
            rideInfo.setJoin_icon(joinObj.getJoin_icon());
            rideInfo.setSource_type(joinObj.getSource_type());
            rideInfo.setSource_url(joinObj.getSource_url());
            rideInfo.setSource_md5(joinObj.getSource_md5());

            rideInfoList.add(rideInfo);

            if (queryLevel == currentLevel) {
                JoinCartonData userJoin = joinCartonDao.findCartoonData(uid, joinId);
                if (userJoin != null) {

                    long leftTime = userJoin.getEnd_time() - currentTime;
                    int endDays = leftTime > 0 ? (int) (leftTime / 86400) : 0;
                    rideRemainingList.add(endDays);
                }
            }
        }

        levelInfo.setRide_list(rideInfoList);
        levelInfo.setRide_remaining(rideRemainingList.size() > 0 ? rideRemainingList.stream().min(Integer::compareTo).get() : 0);
    }

    private void fillBadgeInfo(int queryLevel, int currentLevel, HonorQueryInfoVO.QueryLevelInfo levelInfo, HonorAwardData honorAward){
        List<String> badgeIconList = getBadgeIconListByLevel(queryLevel, currentLevel, honorAward);
        levelInfo.setBadges_list(badgeIconList);
    }


    private void fillQueryLevelInfo(int queryLevel, HonorQueryInfoVO.QueryLevelInfo levelInfo){
        if(queryLevel >= honorChargeBeansList.size()){
            queryLevel = honorChargeBeansList.size() - 1;
        }
        long maxDiamonds = honorChargeBeansList.get(queryLevel);
        levelInfo.setMax_diamonds(maxDiamonds);
        levelInfo.setQuery_level(queryLevel);
    }


    /**
     * 查询不同荣誉等级信息【当前荣誉值、所查荣誉值及相关奖励】
     */
    public HonorQueryInfoVO queryHonorLevelInfos(HonorQueryDTO dto){
        int queryLevel = dto.getQuery_level();
        int slang = dto.getSlang();
        String uid = dto.getUid();
        if(queryLevel < 0){
            throw new CommonException(HttpCode.PARAM_ERROR);
        }

        int currentLevel = 0;
        String currentName = "RS0";
        long currentDiamonds = 0;

        HonorQueryInfoVO infoVO = new HonorQueryInfoVO();
        NewUserHonorData honorData = newUserHonorDao.findData(uid);
        if(honorData != null){
            currentLevel = honorData.getHonor_level();
            currentDiamonds = honorData.getBeans();
            currentName = getLevelName(currentLevel);
        }

        infoVO.setCurrent_level(currentLevel);
        infoVO.setCurrent_diamonds(currentDiamonds);
        infoVO.setCurrent_name(currentName);

        logger.info("queryHonorLevelInfos uid: {}, currentLevel:{}, currentDiamonds:{}, currentName:{}", uid, currentLevel, currentDiamonds, currentName);


        HonorQueryInfoVO.QueryLevelInfo levelInfo = new HonorQueryInfoVO.QueryLevelInfo();
        if(queryLevel == 0){
            queryLevel = currentLevel > 0 ? currentLevel : 1;
        }



        fillQueryLevelInfo(queryLevel, levelInfo);   // 填充查询honor等级信息
        queryLevel = levelInfo.getQuery_level();


        HonorAwardData honorAward = honorAwardDao.findDataByHonorLevel(queryLevel);
        if(honorAward == null){
            logger.error("getBadgeIconListByLevel not find honorAwardData queryLevel: {}", queryLevel);
            throw new CommonException(HttpCode.PARAM_ERROR);
        }

        fillBadgeInfo(queryLevel, currentLevel, levelInfo, honorAward);
        fillRideInfoByLevel(uid, queryLevel, currentLevel, slang, levelInfo, honorAward);
        levelInfo.setQuery_level_title(getLevelName(queryLevel));
        infoVO.setQuery_level_info(levelInfo);

        return infoVO;
    }


    public BadgeListVO badgeListInfo(BadgeListDTO dto){

        String aid = dto.getAid();
        int slang = dto.getSlang();
        BadgeListVO vo = new BadgeListVO();

        List<BadgeListVO.BadgeInfo> ownList = new ArrayList<>();

        List<BadgeData> ownedBadgeList = badgeDao.getOwnedBadgeListOrderStatus(aid);
        for (BadgeData badge : ownedBadgeList) {
            BadgeListData badgeDBInfo = badgeListDao.findData(badge.getBadge_id());
            BadgeListVO.BadgeInfo badgeInfo = new BadgeListVO.BadgeInfo();
            badgeInfo.setBid(badgeDBInfo.getBadge_id());
            badgeInfo.setIcon(badgeDBInfo.getIcon());
            badgeInfo.setName(slang == 1 ? badgeDBInfo.getName() : badgeDBInfo.getAr_name());
            badgeInfo.setDesc(slang == 1 ? badgeDBInfo.getDesc() : badgeDBInfo.getAr_desc());
            badgeInfo.setStatus(badge.getStatus());
            badgeInfo.setSvga_icon("");
            ownList.add(badgeInfo);

        }
        vo.setList(ownList);
        return vo;
    }


    public void needUpdateAppInfo(HttpEnvData dto) {
        logger.info("notice update app dto:{}", dto);
        throw new CommonException(HonorHttpCode.PARAMETER_UPDATE_APP);
    }


}
