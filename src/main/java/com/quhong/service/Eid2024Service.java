package com.quhong.service;

import com.alibaba.fastjson.JSONObject;
import com.google.common.collect.Interner;
import com.google.common.collect.Interners;
import com.quhong.analysis.*;
import com.quhong.constant.ActivityHttpCode;
import com.quhong.core.config.ServerConfig;
import com.quhong.core.utils.DateHelper;
import com.quhong.data.ActorData;
import com.quhong.data.CommonMqTopicData;
import com.quhong.data.ResKeyRewardData;
import com.quhong.data.SendGiftData;
import com.quhong.data.vo.Eid2024VO;
import com.quhong.data.vo.OtherRankingListVO;
import com.quhong.enums.HttpCode;
import com.quhong.enums.ResTypeEnum;
import com.quhong.exception.CommonH5Exception;
import com.quhong.image.ImageUrlGenerator;
import com.quhong.mongo.data.OtherRankingActivityData;
import com.quhong.mongo.data.PackData;
import com.quhong.mongo.data.ResourceKeyConfigData;
import com.quhong.msg.obj.HighlightTextObject;
import com.quhong.msg.room.RoomCommonScrollMsg;
import com.quhong.msg.room.RoomNotificationMsg;
import com.quhong.mysql.dao.WhiteTestDao;
import com.quhong.room.TestRoomService;
import com.quhong.room.redis.RoomPlayerRedis;
import jakarta.annotation.Resource;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import java.util.*;

/**
 * 宰牲节
 */
@Service
public class Eid2024Service extends OtherActivityService implements SendGiftActivity, ActivitySettlement {


    private static final Logger logger = LoggerFactory.getLogger(Eid2024Service.class);
    private static final String ACTIVITY_TITLE_EN = "Sheep Catching";
    private static final String ACTIVITY_TITLE_AR = "إمساك الخراف";
    private static final String ACTIVITY_PRIZE_ICON = "https://cloudcdn.waho.live/common/op_1723711017_FJRK.png";
    private static final String ACTIVITY_DESC = "Sheep Catching reward";
    private static final String ACTIVITY_ID = ServerConfig.isProduct() ? "682c4ac45b3c000076001687" : "6789c63dda330000df001083";
    private static final String ACTIVITY_URL = ServerConfig.isProduct() ? String.format("https://statics.waho.live/shining_eid2024/?activityId=%s", ACTIVITY_ID) : String.format("https://api.opswaho.com/shining_eid2024/?activityId=%s", ACTIVITY_ID);
    private static final String TOTAL_INTEGRAL = "totalIntegral";

    // 玩嘉年华收集指定礼物任务
    private static final List<Integer> GIFT_COLLECT_1_LIST = Arrays.asList(199, 200, 230);
    private static final List<Integer> GIFT_COLLECT_2_LIST = Arrays.asList(386, 24, 94);
    private static final List<Integer> GIFT_COLLECT_3_LIST = Arrays.asList(75, 81, 90);
    private static final String COPPER_BOX = "copperBox";   // 铜宝箱
    private static final String BLUE_BOX = "blueBox";   // 兰宝箱
    private static final String GOLD_BOX = "goldBox";       // 金宝箱
    private static final Map<String, String> GIFT_COLLECT_MAP = new HashMap<>();

    // 抓羊任务
    private static final List<Integer> CATCH_SHEEP_LEVEL_LIST = Arrays.asList(10, 50, 200, 500, 1000);
    private static final List<String> CATCH_SHEEP_KEY_LIST = Arrays.asList("SheepCatching10", "SheepCatching50", "SheepCatching200", "SheepCatching500", "SheepCatching1000");

    private static final String RANK_TOP_REWARD_KEY = "SheepCatchingTop%s"; // 抓羊总榜排名奖励

    private static final Interner<String> stringPool = Interners.newWeakInterner();

    static {
        GIFT_COLLECT_MAP.put(COPPER_BOX, "SheepCatchingTask1");
        GIFT_COLLECT_MAP.put(BLUE_BOX, "SheepCatchingTask2");
        GIFT_COLLECT_MAP.put(GOLD_BOX, "SheepCatchingTask3");
    }

    @Resource
    private WhiteTestDao whiteTestDao;
    @Resource
    private ResourceKeyHandlerService resourceKeyHandlerService;
    @Resource
    private EventReport eventReport;
    @Resource
    private RoomPlayerRedis roomPlayerRedis;
    @Resource
    private OtherActivityService otherActivityService;

    private String getHashActivityId(String activityId, String uid, String dateStr) {
        return String.format("%s:%s:%s", activityId, uid, dateStr);
    }

    // 玩星动嘉年华每日key
    private String getCurrentDate(String activityId) {
        return String.format("playGameDate:%s", activityId);
    }

    private String getPlayGameKey(String activityId, String dateStr) {
        return String.format("playGame:%s:%s", activityId, dateStr);
    }

    // 每日抓羊key
    private String getCatchSleepKey(String activityId, String dateStr) {
        return String.format("catchSleep:%s:%s", activityId, dateStr);
    }

    // 抓羊榜单key
    private String getTotalRankKey(String activityId) {
        return String.format("totalRank:%s", activityId);
    }

    private String getGiftStatus(String box, Integer giftId) {
        return String.format("%s:%s", box, giftId);
    }

    public Eid2024VO eid2024Config(String activityId, String uid) {

        OtherRankingActivityData activity = otherActivityService.getOtherRankingActivity(activityId);

        // String currentDate = activityCommonRedis.getCommonStrValue(getCurrentDate(activityId));
        String currentDate = DateHelper.ARABIAN.formatDateInDay();
        Map<String, Integer> dailyConfigMap = activityCommonRedis.getCommonHashAll(getHashActivityId(activityId, uid, currentDate));
        Eid2024VO vo = new Eid2024VO();
        vo.setTotalIntegral(dailyConfigMap.getOrDefault(TOTAL_INTEGRAL, 0));
        vo.setStartTime(activity.getStartTime());
        vo.setEndTime(activity.getEndTime());
        vo.setCatchSleepNum(activityCommonRedis.getCommonZSetRankingScore(getCatchSleepKey(activityId, currentDate), uid));
        vo.setRoomId(otherActivityService.getPopularRoomId());

        // 设置收集礼物状态
        List<Integer> copperBoxStatus = new ArrayList<>();
        for (Integer giftId : GIFT_COLLECT_1_LIST) {
            copperBoxStatus.add(dailyConfigMap.getOrDefault(getGiftStatus(COPPER_BOX, giftId), 0));
        }
        vo.setCopperBoxStatus(copperBoxStatus);
        vo.setCopperBox(dailyConfigMap.getOrDefault(COPPER_BOX, 0));

        List<Integer> blueBoxStatus = new ArrayList<>();
        for (Integer giftId : GIFT_COLLECT_2_LIST) {
            blueBoxStatus.add(dailyConfigMap.getOrDefault(getGiftStatus(BLUE_BOX, giftId), 0));
        }
        vo.setBlueBoxStatus(blueBoxStatus);
        vo.setBlueBox(dailyConfigMap.getOrDefault(BLUE_BOX, 0));

        List<Integer> goldBoxStatus = new ArrayList<>();
        for (Integer giftId : GIFT_COLLECT_3_LIST) {
            goldBoxStatus.add(dailyConfigMap.getOrDefault(getGiftStatus(GOLD_BOX, giftId), 0));
        }
        vo.setGoldBoxStatus(goldBoxStatus);
        vo.setGoldBox(dailyConfigMap.getOrDefault(GOLD_BOX, 0));

        // 设置抓羊榜
        List<OtherRankingListVO> totalRankingList = new ArrayList<>();
        OtherRankingListVO mytTotalRank = new OtherRankingListVO();
        makeOtherRankingData(totalRankingList, mytTotalRank, getTotalRankKey(activityId), uid, 10);
        vo.setTotalRankingList(totalRankingList);
        vo.setMyTotalRank(mytTotalRank);

        return vo;
    }

    public void catchSheep(String activityId, String uid, int zone, int amount, String roomId) {
        checkActivityTime(activityId);
        if (amount != 1 && amount != 10) {
            throw new CommonH5Exception(HttpCode.PARAM_ERROR);
        }
        int debutNum = amount == 1 ? 2000 : 20000;

        // String currentDate = activityCommonRedis.getCommonStrValue(getCurrentDate(activityId));
        String currentDate = DateHelper.ARABIAN.formatDateInDay();
        String hashActivityId = getHashActivityId(activityId, uid, currentDate);
        Map<String, Integer> dailyConfigMap = activityCommonRedis.getCommonHashAll(hashActivityId);
        int totalIntegral = dailyConfigMap.getOrDefault(TOTAL_INTEGRAL, 0);
        if (totalIntegral <= 0 || totalIntegral < debutNum) {
            throw new CommonH5Exception(ActivityHttpCode.INSUFFICIENT_NUMBER);
        }
        synchronized (stringPool.intern(ACTIVITY_ID + "_" + uid)) {
            activityCommonRedis.incCommonHashNum(hashActivityId, TOTAL_INTEGRAL, -debutNum);
            catchSheepReportEvent(activityId, uid, roomId, amount, zone, debutNum);
            if (zone > 0) {
                // 总榜单
                String totalRankKey = getTotalRankKey(ACTIVITY_ID);
                activityCommonRedis.incrCommonZSetRankingScore(totalRankKey, uid, amount);

                // 每日刷新抓羊数量
                String dailyCatchSleepKey = getCatchSleepKey(activityId, currentDate);
                int currentNum = activityCommonRedis.getCommonZSetRankingScore(dailyCatchSleepKey, uid);
                while (amount > 0) {
                    List<Integer> tempLevelNumList = new ArrayList<>(CATCH_SHEEP_LEVEL_LIST);
                    int currentLevelIndex = 0;
                    if (tempLevelNumList.contains(currentNum)) {
                        currentLevelIndex = tempLevelNumList.indexOf(currentNum);
                    } else {
                        tempLevelNumList.add(currentNum);
                        tempLevelNumList.sort(Integer::compare);
                        currentLevelIndex = tempLevelNumList.indexOf(currentNum) - 1;
                    }

                    int upLevelIndex = currentLevelIndex + 1;
                    if (upLevelIndex >= CATCH_SHEEP_LEVEL_LIST.size()) {
                        activityCommonRedis.incrCommonZSetRankingScore(dailyCatchSleepKey, uid, amount);
                        amount = 0;
                    } else {
                        int upLevelNum = CATCH_SHEEP_LEVEL_LIST.get(upLevelIndex);     // 下一级的数量
                        int needUpNum = upLevelNum - currentNum;                     // 需要升级到下一级的数量
                        if (needUpNum <= amount) {                                      // 如果【增加的数量】大于等于【需要升级到下一级的数量】则升级, 否则不升级直接增加数量
                            currentNum = currentNum + needUpNum;
                            amount = amount - needUpNum;
                            activityCommonRedis.incrCommonZSetRankingScore(dailyCatchSleepKey, uid, needUpNum);

                            // 下发奖励
                            String resKey = CATCH_SHEEP_KEY_LIST.get(upLevelIndex);
                            resourceKeyHandlerService.sendResourceData(uid, resKey, ACTIVITY_DESC, ACTIVITY_DESC);
                            doReportEvent(activityId, uid, 2, upLevelIndex + 1);
                        } else {
                            activityCommonRedis.incrCommonZSetRankingScore(dailyCatchSleepKey, uid, amount);
                            amount = 0;
                        }
                    }
                }
            }
        }
    }

    private void catchSheepReportEvent(String activityId, String uid, String roomId, int amount, int zone, int debutNum) {
        ActiveDataEvent activeDataEvent = new ActiveDataEvent();
        activeDataEvent.setActive_id(activityId);
        activeDataEvent.setDate(DateHelper.ARABIAN.formatDateInDay());
        activeDataEvent.setUid(uid);
        activeDataEvent.setActive_data_desc(zone > 0 ? "sheep_catch_success_number" : "sheep_catch_fail_number");
        activeDataEvent.setNumber(amount);
        activeDataEvent.setCtime(DateHelper.getNowSeconds());
        eventReport.track(new EventDTO(activeDataEvent));
        DrawPrizeRecordEvent event = new DrawPrizeRecordEvent();
        event.setUid(uid);
        event.setCtime(DateHelper.getNowSeconds());
        event.setSence(ACTIVITY_TITLE_EN);
        event.setSence_detail(0);
        event.setRoom_id(roomId);
        event.setCost_score(debutNum);
        event.setDraw_nums(amount);
        event.setDraw_success_nums(zone > 0 ? amount : 0);
        eventReport.track(new EventDTO(event));
    }

    // 总榜排行榜奖励
    @Override
    public void settlementReward(String activityId) {
        try {
            Map<String, Integer> totalRankingMap = activityCommonRedis.getCommonRankingMap(getTotalRankKey(activityId), 10);
            int rank = 1;
            List<ResKeyRewardData> diamondsRewardList = new ArrayList<>();
            for (Map.Entry<String, Integer> entry : totalRankingMap.entrySet()) {
                String rankUid = entry.getKey();
                String resourceKey = "";
                if (rank <= 6) {
                    resourceKey = RANK_TOP_REWARD_KEY.formatted(rank);
                } else if (rank <= 8) {
                    resourceKey = RANK_TOP_REWARD_KEY.formatted("7-8");
                } else if (rank <= 10) {
                    resourceKey = RANK_TOP_REWARD_KEY.formatted("9-10");
                }
                if (StringUtils.hasLength(resourceKey)) {
                    logger.info("distributionTotalRanking. rank={} rankUid={} score={} resourceKey={}", rank, rankUid, entry.getValue(), resourceKey);
                    ResourceKeyConfigData configData = resourceKeyHandlerService.getConfigData(resourceKey);
                    if(configData != null && !CollectionUtils.isEmpty(configData.getResourceMetaList())){
                        for (ResourceKeyConfigData.ResourceMeta resource: configData.getResourceMetaList()) {
                            if (resource.getResourceType() == -2) {
                                if (entry.getValue() >= 2500) {
                                    diamondsRewardList.add(new ResKeyRewardData(activityId, rankUid, resource, 905, ACTIVITY_DESC, ACTIVITY_DESC));
                                }
                            } else {
                                resourceKeyHandlerService.sendOneResourceData(rankUid, resource,
                                        905,
                                        ACTIVITY_DESC,
                                        ACTIVITY_DESC,
                                        ACTIVITY_DESC,
                                        ACTIVITY_DESC,
                                        "", "", 1, activityId);
                            }
                        }
                    }
                }
                rank += 1;
            }
            sendDiamondsReward(activityId, ACTIVITY_TITLE_EN, ACTIVITY_TITLE_EN, diamondsRewardList, otherActivityService.convertToLongMap(totalRankingMap));
        } catch (Exception e) {
            logger.error("distributionTotalRanking error: {}", e.getMessage(), e);
        }
    }

    private void distributeGiftCollect(String fromUid, Map<String, Integer> dailyConfigMap, String dailyBoxKey, String boxType, List<Integer> giftList) {
        int total = 0;
        for (Integer giftId : giftList) {
            String giftStatusKey = getGiftStatus(boxType, giftId);
            total += dailyConfigMap.getOrDefault(giftStatusKey, 0);
        }
        if (total >= giftList.size()) {
            activityCommonRedis.setCommonHashNum(dailyBoxKey, boxType, 1);
            String resourceKey = GIFT_COLLECT_MAP.get(boxType);
            resourceKeyHandlerService.sendResourceData(fromUid, resourceKey, ACTIVITY_DESC, ACTIVITY_DESC);
            int stage = COPPER_BOX.equals(boxType) ? 1 : BLUE_BOX.equals(boxType) ? 2 : 3;
            doReportEvent(ACTIVITY_ID, fromUid, 1, stage);
        }
    }


    @Override
    public void sendGiftHandle(String activityId, SendGiftData giftData) {
        String fromUid = giftData.getFrom_uid();
        synchronized (stringPool.intern(activityId + "_" + fromUid)) {
            int totalBeans = giftData.getNumber() * giftData.getPrice() * giftData.getAid_list().size();
            // String currentDate = activityCommonRedis.getCommonStrValue(getCurrentDate(ACTIVITY_ID));
            String currentDate = DateHelper.ARABIAN.formatDateInDay();
            activityCommonRedis.incCommonHashNum(getHashActivityId(activityId, fromUid, currentDate), TOTAL_INTEGRAL, totalBeans);
        }
    }

    public void handleMqMsg(CommonMqTopicData data) {
        String fromUid = data.getUid();
        if (!inActivityTime(ACTIVITY_ID)) {
            return;
        }
//        if (!whiteTestDao.isMemberByType(fromUid, WhiteTestDao.WHITE_TYPE_RID)) {
//            return;
//        }
        synchronized (stringPool.intern(ACTIVITY_ID + "_" + fromUid)) {
            int value = data.getValue();
            // 增加积分值
            // String currentDate = activityCommonRedis.getCommonStrValue(getCurrentDate(ACTIVITY_ID));
            String currentDate = DateHelper.ARABIAN.formatDateInDay();
            activityCommonRedis.incCommonHashNum(getHashActivityId(ACTIVITY_ID, fromUid, currentDate), TOTAL_INTEGRAL, value);
        }
    }

    // 统计嘉年华收集礼物id
    public void handleCommonMqGiftMsg(CommonMqTopicData data) {
        try {
            String fromUid = data.getUid();
            if (!activityIsRunning(ACTIVITY_ID)) {
                return;
            }
//            if (!whiteTestDao.isMemberByType(fromUid, WhiteTestDao.WHITE_TYPE_RID)) {
//                return;
//            }
            synchronized (stringPool.intern(ACTIVITY_ID + "_" + fromUid)) {
                // String currentDate = activityCommonRedis.getCommonStrValue(getCurrentDate(ACTIVITY_ID));
                String currentDate = DateHelper.ARABIAN.formatDateInDay();
                int giftId = data.getValue();
                String dailyBoxKey = getHashActivityId(ACTIVITY_ID, fromUid, currentDate);
                Map<String, Integer> dailyConfigMap = activityCommonRedis.getCommonHashAll(dailyBoxKey);
                if (GIFT_COLLECT_1_LIST.contains(giftId) && dailyConfigMap.getOrDefault(COPPER_BOX, 0) <= 0) {
                    String giftStatusKey = getGiftStatus(COPPER_BOX, giftId);
                    dailyConfigMap.put(giftStatusKey, 1);
                    activityCommonRedis.setCommonHashNum(dailyBoxKey, giftStatusKey, 1);
                    distributeGiftCollect(fromUid, dailyConfigMap, dailyBoxKey, COPPER_BOX, GIFT_COLLECT_1_LIST);
                }
                if (GIFT_COLLECT_2_LIST.contains(giftId) && dailyConfigMap.getOrDefault(BLUE_BOX, 0) <= 0) {
                    String giftStatusKey = getGiftStatus(BLUE_BOX, giftId);
                    dailyConfigMap.put(giftStatusKey, 1);
                    activityCommonRedis.setCommonHashNum(dailyBoxKey, giftStatusKey, 1);
                    distributeGiftCollect(fromUid, dailyConfigMap, dailyBoxKey, BLUE_BOX, GIFT_COLLECT_2_LIST);
                }

                if (GIFT_COLLECT_3_LIST.contains(giftId) && dailyConfigMap.getOrDefault(GOLD_BOX, 0) <= 0) {
                    String giftStatusKey = getGiftStatus(GOLD_BOX, giftId);
                    dailyConfigMap.put(giftStatusKey, 1);
                    activityCommonRedis.setCommonHashNum(dailyBoxKey, giftStatusKey, 1);
                    distributeGiftCollect(fromUid, dailyConfigMap, dailyBoxKey, GOLD_BOX, GIFT_COLLECT_3_LIST);
                }
            }
        } catch (Exception e) {
            logger.error("handleCommonMqGiftMsg: {} {}", JSONObject.toJSONString(data), e.getMessage(), e);
        }
    }

    /**
     * 中奖推送广播及公屏消息
     */
    public void pushBroadcastScreenMsg(String uid, PackData packData) {
        ActorData actorData = actorDao.getActorDataFromCache(uid);
        if (actorData == null) {
            return;
        }
        String roomId = null;
        int allRoomScreen = packData.getAllRoomScreen();
        int inRoomScreen = packData.getInRoomScreen();
        int broadcast = packData.getBroadcast();
        if (ACTIVITY_TITLE_EN.startsWith("test") && (allRoomScreen > 0 || inRoomScreen > 0 || broadcast > 0)) {
            roomId = TestRoomService.getTestRoom();
        }
        if (allRoomScreen > 0 && StringUtils.isEmpty(roomId)) {
            roomId = "all";
        }
        if (inRoomScreen > 0 && StringUtils.isEmpty(roomId)) {
            roomId = roomPlayerRedis.getActorRoomStatus(uid);
        }
        ResTypeEnum typeEnum = ResTypeEnum.getByType(packData.getResType());
        String resNameEn = typeEnum != null ? typeEnum.getNameEn() : "";
        String resNameAr = typeEnum != null ? typeEnum.getNameAr() : "";
        // 推送公屏消息
        if (StringUtils.hasLength(roomId)) {
            List<HighlightTextObject> list = new ArrayList<>();
            HighlightTextObject object = new HighlightTextObject();
            object.setText(actorData.getName());
            object.setHighlightColor("#FFE200");
            list.add(object);
            object = new HighlightTextObject();
            object.setText(resNameEn);
            object.setHighlightColor("#FFE200");
            list.add(object);
            object = new HighlightTextObject();
            object.setText(resNameAr);
            object.setHighlightColor("#FFE200");
            list.add(object);
            object = new HighlightTextObject();
            object.setText("GO>>");
            object.setHighlightColor("#FFE200");
            list.add(object);
            object = new HighlightTextObject();
            object.setText("هيا بنا>>");
            object.setHighlightColor("#FFE200");
            list.add(object);

            RoomNotificationMsg msg = new RoomNotificationMsg();
            msg.setUid(uid);
            msg.setUser_name(actorData.getName());
            msg.setUser_head(ImageUrlGenerator.generateRoomUserUrl(actorData));
            msg.setText(String.format("Congratulations \u202C%s getting \u202C%s. GO>>", actorData.getName(), resNameEn));
            msg.setText_ar(String.format("  تهانينا ل \u202b%s لحصوله على \u202b%s. هيا بنا>>", actorData.getName(), resNameAr));
            msg.setHighlight_text(list);
            msg.setHighlight_text_ar(list);
            msg.setWeb_url(ACTIVITY_URL);
            msg.setWeb_type(1);
            msg.setWidth(375);
            msg.setHeight(560);
            msg.setFromRoomId(roomId);
            roomWebSender.sendRoomWebMsg(roomId, null, msg, false);
        }

        if (broadcast > 0) {
            RoomCommonScrollMsg scrollMsg = new RoomCommonScrollMsg();
            scrollMsg.setUid(uid);
            scrollMsg.setPrizeIcon(ACTIVITY_PRIZE_ICON);
            scrollMsg.setPrizeTextEn(String.format("Congratulations to %s for getting %s in %s", actorData.getName(), resNameEn, ACTIVITY_TITLE_EN));
            scrollMsg.setPrizeTextAr(String.format("تهانينا لـ %s للحصول على %s في %s", actorData.getName(), resNameAr, ACTIVITY_TITLE_AR));

            List<HighlightTextObject> list = new ArrayList<>();
            HighlightTextObject object = new HighlightTextObject();
            object.setText(actorData.getName());
            object.setHighlightColor("#FFE200");
            list.add(object);
            object = new HighlightTextObject();
            object.setText(ACTIVITY_TITLE_EN);
            object.setHighlightColor("#FFE200");
            list.add(object);
            object = new HighlightTextObject();
            object.setText(ACTIVITY_TITLE_AR);
            object.setHighlightColor("#FFE200");
            list.add(object);
            scrollMsg.setHighlightTextEn(list);
            scrollMsg.setHighlightTextAr(list);
            scrollMsg.setActionType(StringUtils.isEmpty(ACTIVITY_URL) ? 998 : 19);
            scrollMsg.setActionValue(ACTIVITY_URL);
            logger.info("RoomCommonScrollMsg: {}, roomId:{}", JSONObject.toJSONString(scrollMsg), roomId);
            roomWebSender.sendRoomWebMsg(StringUtils.isEmpty(roomId) ? "all" : roomId, uid, scrollMsg, false);
        }
    }

    private void doReportEvent(String activityId, String uid, int subtype, int stage) {
        ActivityParticipationRecordEvent event = new ActivityParticipationRecordEvent();
        event.setUid(uid);
        event.setActivity_name(ACTIVITY_TITLE_EN);
        event.setActive_id(activityId);
        event.setChannel("");
        event.setActive_subtype(subtype);
        event.setActivity_stage(stage);
        event.setCost_activity_ticket(0);
        event.setCtime(DateHelper.getNowSeconds());
        eventReport.track(new EventDTO(event));
    }

    @Override
    public String getActivityName() {
        return ACTIVITY_TITLE_EN;
    }
}
