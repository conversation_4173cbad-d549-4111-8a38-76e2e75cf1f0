package com.quhong.service;

import com.quhong.core.utils.DateHelper;
import com.quhong.data.vo.EventCenterVO;
import com.quhong.mongo.dao.EventCenterDao;
import com.quhong.mongo.data.EventCenterData;
import jakarta.annotation.Resource;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.Comparator;
import java.util.List;

@Service
public class EventCenterService {

    private static final Logger logger = LoggerFactory.getLogger(EventCenterService.class);
    @Resource
    private EventCenterDao eventCenterDao;

    public EventCenterVO eventCenterList(String uid, int eventType) {

        List<EventCenterData> dataList = eventCenterDao.selectListByType(eventType);;
        int currentTime = DateHelper.getNowSeconds();
        List<EventCenterData> totalList = new ArrayList<>();   // 置顶列表
        List<EventCenterData> stickyList = new ArrayList<>();   // 置顶列表
        List<EventCenterData> noStartList = new ArrayList<>();   // 未开始列表
        List<EventCenterData> runList = new ArrayList<>();   // 进行中列表
        List<EventCenterData> endList = new ArrayList<>();   // 已结束列表
        for (EventCenterData item : dataList) {
            if (item.getCycle() == 1 && item.getEndTime() != item.getStartTime()) {
                // 周期活动
                int cycleTime = item.getEndTime() - item.getStartTime();
                int nowTime = DateHelper.getNowSeconds();
                int cycleNum = (nowTime - item.getStartTime()) / cycleTime;
                item.setStartTime(item.getStartTime() + cycleNum * cycleTime);
                item.setEndTime(item.getEndTime() + cycleNum * cycleTime);
            }
            int startTime = item.getStartTime();
            int endTime = item.getEndTime();

            // 置顶
            if(item.getSticky() > 0){
                stickyList.add(item);
                continue;
            }

            // 未开始
            if(currentTime < startTime){
                noStartList.add(item);
                continue;
            }

            // 进行中
            if(currentTime > startTime && currentTime < endTime){
                runList.add(item);
                continue;
            }

            if (currentTime > endTime){
                endList.add(item);
            }
        }
        stickyList.sort(Comparator.comparing(EventCenterData::getMtime).reversed());
        totalList.addAll(stickyList);
        totalList.addAll(runList);
        totalList.addAll(noStartList);
        totalList.addAll(endList);
        EventCenterVO vo = new EventCenterVO();
        vo.setList(totalList);
        return vo;
    }

}
