package com.quhong.service;

import com.alibaba.fastjson.JSONObject;
import com.quhong.config.LudoConfig;
import com.quhong.core.concurrency.BaseTaskFactory;
import com.quhong.core.concurrency.tasks.Task;
import com.quhong.core.utils.DateHelper;
import com.quhong.data.dto.MoneyDetailReq;
import com.quhong.enums.*;
import com.quhong.exception.GameException;
import com.quhong.feign.DataCenterService;
import com.quhong.image.ImageUrlGenerator;
import com.quhong.ludo.constant.LudoConstant;
import com.quhong.ludo.constant.NodeConstant;
import com.quhong.ludo.data.*;
import com.quhong.ludo.game.Game;
import com.quhong.ludo.game.NormalGame;
import com.quhong.ludo.service.BoardNodeService;
import com.quhong.ludo.service.ChessmanService;
import com.quhong.ludo.service.GameService;
import com.quhong.mongo.dao.MongoRoomDao;
import com.quhong.mongo.dao.RoomMemberDao;
import com.quhong.mongo.dao.UserLevelDao;
import com.quhong.mongo.data.Actor;
import com.quhong.mongo.data.MongoRoomData;
import com.quhong.mq.CommonData;
import com.quhong.mq.MqItemConstant;
import com.quhong.mq.MqSenderService;
import com.quhong.msg.room.RoomLudoChangePushMsg;
import com.quhong.msg.room.RoomLudoKitOutMsg;
import com.quhong.msg.room.RoomLudoStatusChangedMsg;
import com.quhong.mysql.dao.HeartRecordDao;
import com.quhong.mysql.data.LudoLogData;
import com.quhong.mysql.mapper.waho.RoomMicInfoMapper;
import com.quhong.redis.LudoRedis;
import com.quhong.room.RoomWebSender;
import com.quhong.room.cache.RoomActorCache;
import com.quhong.room.data.RoomActorDetailData;
import com.quhong.room.redis.RoomPlayerRedis;
import com.quhong.utils.PageUtils;
import com.quhong.vo.GiftsMqVo;
import jakarta.annotation.Resource;
import org.bson.types.ObjectId;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import java.util.*;
import java.util.stream.Collectors;

@SuppressWarnings("SynchronizationOnLocalVariableOrMethodParameter")
@Service
public class LudoService {

    private static final Logger logger = LoggerFactory.getLogger(LudoService.class);
    private static final int HEART = 1;
    private static final int DIAMOND = 2;
    private static final int A_TYPE = 908;
    private static final int DICE_TIMES = 3;
    private static final int START_POINT = -1;

    private static final String TITLE = "Play Game";
    private static final String DESC = "play ludo game";
    private static final String RETURN_DESC = "play ludo game return";
    private static final String REWARD_DESC = "play ludo game reward";

    @Resource
    private RoomMicInfoMapper roomMicInfoMapper;
    @Resource
    private HeartRecordDao heartRecordDao;
    @Resource
    private ActorService actorService;
    @Resource
    private DataCenterService dataCenterService;
    @Resource
    private LudoRedis ludoRedis;
    @Resource
    private GameService gameService;
    @Resource
    private RoomWebSender roomWebSender;
    @Resource
    private LudoConfig config;
    @Resource
    private ChessmanService chessmanService;
    @Resource
    private BoardNodeService boardNodeService;
    @Resource
    private LudoLogService ludoLogService;
    @Resource
    private RoomPlayerRedis roomPlayerRedis;
    @Resource
    private MongoRoomDao mongoRoomDao;
    @Resource
    private GiftsService giftsService;
    @Resource
    private UserLevelDao userLevelDao;
    @Resource
    private RoomMemberDao memberDao;
    @Value("${ludo.onlyRoomOwn}")
    private boolean onlyRoomOwn;
    @Value("${ludo.createGameLevelLimit}")
    private int createGameLevelLimit;
    @Resource
    private RoomActorCache roomActorCache;

    public GameInfo createGame(String uid, GameInfo gameInfo) {
        if (onlyRoomOwn && unauthorized(gameInfo.getRoomId(), uid)) {
            logger.info("actor is audience. can not create ludo game. roomId={} uid={}", gameInfo.getRoomId(), uid);
            throw new GameException(GameHttpCode.ROOM_OWNER, null);
        } else if (createGameLevelLimit > 0) {
            int userLevel = userLevelDao.getUserLevel(uid, UserLevelConstant.ACTIVE_LEVEL);
            if (userLevel < createGameLevelLimit) {
                logger.info("user level limit. can not create ludo game. roomId={} uid={} user level={}",
                        gameInfo.getRoomId(), uid, userLevel);
                throw new GameException(GameHttpCode.LEVEL_LIMIT, new Object[]{createGameLevelLimit});
            }
        }
        GameInfo roomGameInfo = gameService.getGameInfoByRoomId(gameInfo.getRoomId());
        if (null != roomGameInfo && roomGameInfo.getStatus() != LudoConstant.FINISH) {
            logger.info("room has processing game. roomId={} uid={}", gameInfo.getRoomId(), uid);
            throw new GameException(GameHttpCode.ONE_GAME, roomGameInfo.toWebData());
        }
        GameInfo createGameInfo = createOrJoinGame(uid, gameInfo);
        // 发送创建游戏公屏消息
        sendLudoStatusChangeMsg(createGameInfo.getRoomId(), uid, LudoConstant.CREATE_GAME);
        return createGameInfo;
    }

    public GameInfo joinGame(String uid, String gameId) {
        GameInfo gameInfo = gameService.getGame(gameId).getGameInfo();
        synchronized (gameInfo) {
            checkGame(gameInfo, uid);
            if (gameInfo.getPlayerList().size() == 4 || gameInfo.getPlayerNumber() == gameInfo.getPlayerList().size()) {
                logger.info("cannot join game. game play is full gameId={} uid={}", gameId, uid);
                throw new GameException(GameHttpCode.PLAYER_FULL, gameInfo.toWebData());
            }
            return createOrJoinGame(uid, gameInfo);
        }
    }

    public GameInfo startGame(String uid, String gameId) {
        Game game = gameService.getGame(gameId);
        GameInfo gameInfo = game.getGameInfo();
        synchronized (gameInfo) {
            if (!gameInfo.getSelfUid().equals(uid)) {
                logger.info("cannot start game. no right to operate gameId={} uid={}", gameId, uid);
                throw new GameException(HttpCode.AUTH_ERROR, gameInfo.toWebData());
            }
            checkGame(gameInfo, uid);
            if (gameInfo.getPlayerList().size() < 2) {
                logger.info("cannot start game. player not ready gameId={} uid={}", gameId, uid);
                throw new GameException(GameHttpCode.PLAYER_NOT_READY, gameInfo.toWebData());
            }
            // 由蓝方先投掷骰子
            OpData opData = new OpData();
            opData.setOpUid(uid);
            opData.setOpType(LudoConstant.THROW_DICE);
            int nowSeconds = DateHelper.getNowSeconds();
            // 刚开始的游戏提供10s缓冲时间
            opData.setActionTime(nowSeconds + 10);
            gameInfo.setOpData(opData);
            gameInfo.setStatus(LudoConstant.PROCESSING);
            // 设置玩家数量
            gameInfo.setPlayerNumber(game.getPlayerMap().size());
            gameInfo.setStartTime(nowSeconds);
            gameInfo.setModifyTime(DateHelper.getNowSeconds());
            // 初始化棋子
            List<GamePlayer> playerList = gameInfo.getPlayerList();
            for (int i = 0; i < playerList.size(); i++) {
                playerList.get(i).initChessman(i + 1);
            }
            gameInfoChange(game);
            // 保存游戏数据
//            ludoDao.save(game);
            // 发送ludo任务消息
            return gameInfo;
        }
    }

    /**
     * 游戏中玩家退出游戏，不退还游戏币
     */
    public GameInfo exitGame(String uid, String gameId) {
        Game game = gameService.getGame(gameId);
        GameInfo gameInfo = game.getGameInfo();
        synchronized (gameInfo) {
            gameInfo.setStep(gameInfo.getStep() + 1);
            GamePlayer gamePlayer = game.getPlayerMap().get(uid);
            if (gamePlayer == null) {
                logger.info("player not found gameId={} uid={}", gameId, uid);
                throw new GameException(GameHttpCode.PLAYER_NOT_FOUND, gameInfo.toWebData());
            }
            int players = gameInfo.getPlayerNumber();
            int finishedPlayers = 0;
            for (GamePlayer player : gameInfo.getPlayerList()) {
                if (player.getStatus() == LudoConstant.VICTORY || player.getStatus() == LudoConstant.EXIT) {
                    finishedPlayers++;
                }
            }
            // 只有一个玩家时关闭游戏
            if (players - finishedPlayers - 1 == 1 || players - 1 == 1) {
                gamePlayer.setStatus(LudoConstant.EXIT);
                int currency = config.getCurrencyById(gameInfo.getCurrencyId());
                WinnerData winnerData = gameInfo.getWinnerData();
                // 没有顺利完成游戏的玩家
                if (null == winnerData) {
                    int reword = getWinnerReward(gameInfo);
                    winnerData = new WinnerData();
                    List<WinnerPlayer> winnerList = winnerData.getWinnerList();
                    GamePlayer winnerPlayer = null;
                    for (GamePlayer player : gameInfo.getPlayerList()) {
                        if (player.getStatus() == LudoConstant.EXIT) {
                            winnerList.add(new WinnerPlayer(player, "-" + currency, getAuto(gameInfo, player)));
                        } else {
                            if (!player.getUid().equals(uid)) {
                                winnerPlayer = player;
                            }
                        }
                    }
                    if (null != winnerPlayer) {
                        int auto = getAuto(gameInfo, winnerPlayer);
                        winnerList.add(0, new WinnerPlayer(winnerPlayer, "+" + reword, auto));
                    }
                    gameInfo.setWinnerData(winnerData);
                } else {
                    // 有一个或者两个胜利的玩家，增加最后一个赢家和退出的失败玩家
                    List<WinnerPlayer> winnerList = winnerData.getWinnerList();
                    for (GamePlayer player : gameInfo.getPlayerList()) {
                        // 最后一个赢家
                        if (player.getStatus() != LudoConstant.VICTORY && player.getStatus() != LudoConstant.EXIT) {
                            winnerList.add(new WinnerPlayer(player, "+" + currency, getAuto(gameInfo, player)));
                        }
                    }
                    // 增加退出游戏的失败玩家
                    for (GamePlayer player : gameInfo.getPlayerList()) {
                        if (player.getStatus() == LudoConstant.EXIT) {
                            winnerList.add(new WinnerPlayer(player, "-" + currency, getAuto(gameInfo, player)));
                        }
                    }
                }
                finishGame(game);
            } else {
                playerExit(game, gamePlayer);
            }
            gameInfoChange(game);
            return gameInfo;
        }
    }

    /**
     * 游戏中玩家退出游戏
     * <p>
     * 中途退出游戏的玩家，该玩家所有棋子返回基地。该颜色玩家基地展示退出游戏状态。
     * 游戏费用处理：平台回收。
     */
    private void playerExit(Game game, GamePlayer gamePlayer) {
        logger.info("player exit remove player from game. gameId={} uid={} status={}",
                game.getGameId(), gamePlayer.getUid(), gamePlayer.getStatus());
        GameInfo gameInfo = game.getGameInfo();
        GamePlayer player = getGamePlayer(gamePlayer.getUid(), gameInfo);
        if (player.getStatus() == LudoConstant.VICTORY) {
            return;
        }
        game.removePlayer(gamePlayer.getUid());
        ludoRedis.removePlayerData(gamePlayer.getUid());
        // 更新玩家状态
        player.setStatus(LudoConstant.EXIT);
        // 玩家所有棋子返回基地
        for (Chessman chessman : gamePlayer.getChessmanList()) {
            boardNodeService.removeChessman(gameInfo, chessman.getNodeId(), chessman.getChessmanId());
            chessman.setNodeId(chessman.getStartNodeId());
        }
        // 设置下一个玩家
        if (gameInfo.getOpData().getOpUid().equals(gamePlayer.getUid())) {
            setNextGameTurn(gameInfo);
        }
    }

    /**
     * 组队界面退出游戏，移除gameInfo中的玩家数据，并退还游戏币
     */
    private void cancelGame(Game game, String uid) {
        logger.info("cancel game remove player from game. uid={} gameId={}", uid, game.getGameId());
        GameInfo gameInfo = game.getGameInfo();
        game.removePlayer(uid);
        ludoRedis.removePlayerData(uid);
        // 不在游戏中退出游戏时删除玩家数据
        if (gameInfo.getStatus() != LudoConstant.PROCESSING) {
            List<GamePlayer> playerList = gameInfo.getPlayerList();
            playerList.removeIf(player -> uid.equals(player.getUid()));
        }
        // 退还游戏币
        returnGameCurrency(gameInfo, uid);
    }

    /**
     * 获取赢家奖励
     * 正常情况：赢家费用 = 总游戏费 * 90%
     * 有中途退出玩家：赢家费用 = 总游戏费 * 90% - 退出者加入费 * N%，N为动态百分比。
     * 游戏进行时长小于等于1分钟，N=60%。1-2分钟，N=50%。2-10分钟，N=40%。10分钟以上，N=30%。
     * 例如：该局游戏费是20钻。 有人退出，则40 * 90% = 36钻，36 - (20*60%) = 24钻
     */
    private int getWinnerReward(GameInfo gameInfo) {
        long exitCount = gameInfo.getPlayerList().stream()
                .filter(gamePlayer -> gamePlayer.getStatus() == LudoConstant.EXIT).count();
        if (exitCount > 0) {
            int nowSeconds = DateHelper.getNowSeconds();
            int startTime = gameInfo.getStartTime();
            // 游戏费用
            int currency = config.getCurrencyById(gameInfo.getCurrencyId());
            // 总游戏费用
            int totalCurrency = gameInfo.getPlayerNumber() * currency;
            // 动态百分比
            double rate;
            if (nowSeconds - startTime >= 10 * 60) {
                rate = 0.3;
            } else if (nowSeconds - startTime >= 2 * 60 && nowSeconds - startTime < 10 * 60) {
                rate = 0.4;
            } else if (nowSeconds - startTime >= 60 && nowSeconds - startTime < 2 * 60) {
                rate = 0.5;
            } else {
                rate = 0.6;
            }
            int exitTax = (int) (exitCount * currency * rate);
            int winnerTax = (int) (totalCurrency * 0.1);
            gameInfo.setTax(exitTax + winnerTax);
            return totalCurrency - winnerTax - exitTax;
        } else {
            int tax = (int) (gameInfo.getTotalCurrency() * 0.1);
            gameInfo.setTax(tax);
            return gameInfo.getTotalCurrency() - tax;
        }
    }

    /**
     * 执行游戏结果
     */
    public void finishGame(Game game) {
        logger.info("ludo finish game gameId={}", game.getGameId());
        GameInfo gameInfo = game.getGameInfo();
        gameInfo.setEndTime(DateHelper.getNowSeconds());
        gameInfo.setStatus(LudoConstant.FINISH);
        gameService.removeGame(gameInfo, false);
        WinnerData winnerData = gameInfo.getWinnerData();
        if (null == winnerData) {
            winnerData = new WinnerData();
            int currency = config.getCurrencyById(gameInfo.getCurrencyId());
            for (GamePlayer player : gameInfo.getPlayerList()) {
                winnerData.getWinnerList().add(new WinnerPlayer(player, "-" + currency, 1));
            }
        }
        winnerData.setCurrencyType(gameInfo.getCurrencyType());
        List<WinnerPlayer> playerList = winnerData.getWinnerList();
        BaseTaskFactory.getFactory().addSlow(new Task() {
            @Override
            protected void execute() {
                List<LudoLogData> winnerDataList = new ArrayList<>();
                for (WinnerPlayer winnerPlayer : playerList) {
                    int rewards = Integer.parseInt(winnerPlayer.getRewardValue());
                    if (rewards > 0 && winnerPlayer.getAuto() != 1) {
                        winnerDataList.add(new LudoLogData(winnerPlayer.getUid(), gameInfo.getGameId(), rewards));
                        // 下发奖励
                        gameReward(gameInfo, winnerPlayer.getUid(), rewards);
                    }
                    logger.info("game result: {}", winnerPlayer);
                }
                ludoLogService.saveBatch(winnerDataList);
                // 更新mongo数据
//                ludoDao.updateLudo(gameInfo, playerList);
            }
        });
    }

    /**
     * 退出游戏或踢出游戏，并退还游戏币
     * 玩家手动退出时aid和uid相同
     */
    public GameInfo kickOut(String uid, String aid, String gameId) {
        Game game = gameService.getGame(gameId);
        GameInfo gameInfo = game.getGameInfo();
        synchronized (gameInfo) {
            gameInfo.setStep(gameInfo.getStep() + 1);
            checkGame(gameInfo, uid);
            // 游戏创建者退出游戏，解散游戏
            if (gameInfo.getSelfUid().equals(uid) && uid.equals(aid)) {
                // 关闭游戏，并返还游戏币
                gameService.removeGame(gameInfo, true);
                gameInfo.setStatus(LudoConstant.CLOSE_GAME);
                // 发送游戏关闭消息
                sendLudoStatusChangeMsg(gameInfo.getRoomId(), uid, LudoConstant.MANUALLY_CLOSE);
            } else {
                // 踢出玩家时判断权限，仅创建者可以踢出玩家
                if (!uid.equals(aid)) {
                    if (!gameInfo.getSelfUid().equals(uid)) {
                        logger.info("cannot kick out player. no right to operate gameId={} uid={} aid={}", gameId, uid, aid);
                        throw new GameException(HttpCode.AUTH_ERROR, gameInfo.toWebData());
                    }
                    // 发送玩家被踢消息
                    sendKickOutMsg(gameInfo.getRoomId(), uid, aid);
                }
                cancelGame(game, aid);
            }
            gameInfoChange(game);
            return gameInfo;
        }
    }

    public void returnGameCurrency(GameInfo gameInfo, String uid) {
        // 返还游戏币
        int currency = config.getCurrencyById(gameInfo.getCurrencyId());
        if (HEART == gameInfo.getCurrencyType()) {
            boolean success = heartRecordDao.changeHeart(uid, currency, TITLE, RETURN_DESC);
            logger.info("return heart uid={} heart={} success={}", uid, currency, success);
        } else if (DIAMOND == gameInfo.getCurrencyType()) {
            giftsService.sendGiftToMq(new GiftsMqVo(A_TYPE, uid, currency, TITLE, RETURN_DESC));
            logger.info("return diamond uid={} diamond={}", uid, currency);
        }
    }

    public void gameReward(GameInfo gameInfo, String uid, int changed) {
        // 返还游戏币
        if (HEART == gameInfo.getCurrencyType()) {
            boolean success = heartRecordDao.changeHeart(uid, changed, TITLE, REWARD_DESC);
            logger.info("game reward heart uid={} heart={} success={}", uid, changed, success);
        } else if (DIAMOND == gameInfo.getCurrencyType()) {
            giftsService.sendGiftToMq(new GiftsMqVo(A_TYPE, uid, changed, TITLE, REWARD_DESC));
            logger.info("game reward diamond uid={} diamond={}", uid, changed);
        }
    }

    private GameInfo createOrJoinGame(String uid, GameInfo gameInfo) {
        Actor actor = actorService.getActor(uid);
        if (null == actor) {
            logger.info("player not found uid={}", uid);
            throw new GameException(GameHttpCode.PLAYER_NOT_FOUND, gameInfo.toWebData());
        }
        // 创建时校验玩家是否已上麦
        if (StringUtils.isEmpty(gameInfo.getGameId())) {
            List<String> roomMicUidDataList = roomMicInfoMapper.getUidDataList(gameInfo.getRoomId());
            if (!roomMicUidDataList.contains(uid)) {
                logger.info("player not in mic. uid={} roomId={}", uid, gameInfo.getRoomId());
                throw new GameException(GameHttpCode.NOT_IN_MIC, gameInfo.toWebData());
            }
        }
        // 校验玩家是否有未退出的游戏
        String inGameId = ludoRedis.getPlayerData(uid);
        if (null != inGameId) {
            try {
                gameService.getGame(inGameId);
            } catch (GameException e) {
                // 游戏不存在，是脏数据，需要删除
                ludoRedis.removePlayerData(uid);
                logger.error("dirty data clash uid={} inGameId={}", uid, inGameId);
            }
            logger.info("player has in game uid={}", uid);
            if (StringUtils.isEmpty(gameInfo.getGameId())) {
                throw new GameException(GameHttpCode.PLAYER_IN_GAME_C, gameInfo.toWebData());
            } else {
                throw new GameException(GameHttpCode.PLAYER_IN_GAME_J, gameInfo.toWebData());
            }
        }
        int currency = config.getCurrencyById(gameInfo.getCurrencyId());
        if (0 == currency) {
            logger.info("currency id not found currency id={}", gameInfo.getCurrencyId());
            throw new GameException(GameHttpCode.SERVER_ERROR, gameInfo.toWebData());
        }
        // 校验玩家是否有足够的游戏币并扣除游戏费用
        if (HEART == gameInfo.getCurrencyType()) {
            if (currency > actor.getHeartGot()) {
                logger.info("player not enough heart. uid={} roomId={}",
                        actor.get_id().toString(), gameInfo.getRoomId());
                throw new GameException(GameHttpCode.NOT_ENOUGH_HEART, gameInfo.toWebData());
            }
            heartRecordDao.changeHeart(uid, -currency, TITLE, DESC);
        } else if (DIAMOND == gameInfo.getCurrencyType()) {
            // 旧版ludo已停止使用钻石
            throw new GameException(GameHttpCode.SERVER_ERROR, gameInfo.toWebData());
        } else {
            logger.info("not support currency type. uid={} roomId={} currency type={}",
                    uid, gameInfo.getRoomId(), gameInfo.getCurrencyType());
            throw new GameException(GameHttpCode.SERVER_ERROR, gameInfo.toWebData());
        }
        Game game = saveOrUpdateGameInfo(actor, gameInfo);
        gameInfoChange(game);
        return gameInfo;
    }

    private Game saveOrUpdateGameInfo(Actor actor, GameInfo gameInfo) {
        GamePlayer gamePlayer = new GamePlayer();
        String uid = actor.get_id().toString();
        gamePlayer.setUid(uid);
        gamePlayer.setRid(actor.getRid() + "");
        // 前端解析会报错
        String name = actor.getName().replace("'", "");
        gamePlayer.setName(StringUtils.isEmpty(name) ? "Player" : name);
        gamePlayer.setHead(ImageUrlGenerator.generateRoomUserUrl(actor.getHead()));
        gamePlayer.setDiceType(START_POINT);
        // 创建游戏
        if (StringUtils.isEmpty(gameInfo.getGameId())) {
            // 创建者为蓝方，第二个加入的玩家为红方，第三个玩家为绿方，第四个玩家为黄方
            String gameId = new ObjectId().toString();
            gameInfo.setGameId(gameId);
            gameInfo.setStatus(LudoConstant.MATCHING);
            gameInfo.getPlayerList().add(gamePlayer);
            gameInfo.setSelfUid(uid);
            gameInfo.initBoardNodeList();
            gameInfo.setCreateTime(DateHelper.getNowSeconds());
            // 保存玩家信息
            Game game = new NormalGame(gameInfo);
            game.addPlayer(gamePlayer);
            ludoRedis.savePlayerData(gamePlayer.getUid(), gameInfo.getGameId());
            gameService.addGame(game);
            logger.info("create ludo game, gameId={} uid={}", gameId, uid);
            return game;
        } else {
            gameInfo.getPlayerList().add(gamePlayer);
            Game game = gameService.getGame(gameInfo.getGameId());
            game.addPlayer(gamePlayer);
            ludoRedis.savePlayerData(gamePlayer.getUid(), gameInfo.getGameId());
            return game;
        }
    }

    /**
     * 只针对玩家和观众进行消息下发
     */
    private void sendLudoMsg(Game game) {
        BaseTaskFactory.getFactory().addSlow(new Task() {
            @Override
            protected void execute() {
                GameInfo gameInfo = game.getGameInfo();
                Set<String> uidSet = new HashSet<>();
                uidSet.addAll(game.getWatchUserSet());
                uidSet.addAll(game.getPlayerMap().keySet());
                // roomWebSender.sendLudoMsg(gameInfo.getRoomId(), null, gameInfo.toMarsMsg(), uidSet);
            }
        });
    }

    /**
     * 发送给全房间用户
     */
    private void sendRoomMsg(Game game) {
        BaseTaskFactory.getFactory().addSlow(new Task() {
            @Override
            protected void execute() {
                GameInfo gameInfo = game.getGameInfo();
                roomWebSender.sendRoomWebMsg(gameInfo.getRoomId(), null, gameInfo.toMarsMsg(), true);
            }
        });
    }

    /**
     * ludo状态变更公屏消息
     */
    public void sendLudoStatusChangeMsg(String roomId, String uid, int status) {
        BaseTaskFactory.getFactory().addSlow(new Task() {
            @Override
            protected void execute() {
                RoomLudoStatusChangedMsg msg = new RoomLudoStatusChangedMsg();
                msg.setAid(uid);
                if (!StringUtils.isEmpty(uid)) {
                    RoomActorDetailData actor = roomActorCache.getData(roomId, uid, false);
                    msg.setName(actor.getName());
                    msg.setHead(actor.getHead());
                    msg.setLevel(actor.getActiveLevel());
                    msg.setRole(actor.getNewRole());
                    msg.setVip(actor.getVipLevel());
                    msg.setBadge(actor.getBadgeList());
                }
                msg.setStatus(status);
                roomWebSender.sendRoomWebMsg(roomId, "", msg, true);
            }
        });
    }

    public void sendLudoChangeMsg(GameInfo gameInfo, String uid, int status) {
        BaseTaskFactory.getFactory().addSlow(new Task() {
            @Override
            protected void execute() {
                RoomLudoChangePushMsg msg = new RoomLudoChangePushMsg();
                msg.setStatus(status);
                msg.setGameId(gameInfo.getGameId());
                roomWebSender.sendRoomWebMsg(gameInfo.getRoomId(), uid, msg, true);
            }
        });
    }

    private void sendKickOutMsg(String roomId, String uid, String aid) {
        BaseTaskFactory.getFactory().addSlow(new Task() {
            @Override
            protected void execute() {
                RoomLudoKitOutMsg msg = new RoomLudoKitOutMsg();
                msg.setAid(uid);
                roomWebSender.sendPlayerWebMsg(roomId, uid, aid, msg, true);
            }
        });
    }

    private void reduceBeans(GameInfo gameInfo, String uid, int changed) {
        MoneyDetailReq moneyDetailReq = new MoneyDetailReq();
        moneyDetailReq.setRandomId();
        moneyDetailReq.setUid(uid);
        moneyDetailReq.setAtype(A_TYPE);
        moneyDetailReq.setChanged(changed);
        moneyDetailReq.setTitle(TITLE);
        moneyDetailReq.setDesc(DESC);
        ApiResult<String> result = dataCenterService.reduceBeans(moneyDetailReq);
        if (result.isError()) {
            logger.error("reduce beans error, msg={}", result.getData());
            throw new GameException(GameHttpCode.SERVER_ERROR, gameInfo.toWebData());
        }
    }

    private void checkGame(GameInfo gameInfo, String uid) {
        if (gameInfo.getStatus() == LudoConstant.PROCESSING) {
            logger.info("game is running gameId={} uid={}", gameInfo.getGameId(), uid);
            throw new GameException(GameHttpCode.GAME_RUNNING, gameInfo.toWebData());
        }
    }

    public GameInfo throwDice(String uid, String gameId) {
        Game game = gameService.getGame(gameId);
        GameInfo gameInfo = game.getGameInfo();
        synchronized (gameInfo) {
            return throwDice(uid, game, false);
        }
    }

    public GameInfo throwDice(String uid, Game game, boolean hosting) {
        GameInfo gameInfo = game.getGameInfo();
        gameInfo.setStep(gameInfo.getStep() + 1);
        gameInfo.getPlayDataList().clear();
        if (checkGameTurn(gameInfo, uid, LudoConstant.THROW_DICE)) {
            return gameInfo;
        }
        GamePlayer gamePlayer = getGamePlayer(uid, gameInfo);
        if (!hosting) {
            stopHosting(gameInfo, gamePlayer);
        }
        if (gameInfo.getGameType() == LudoConstant.CLASSIC_MODE) {
            throwDice(game, gamePlayer, false);
        } else if (gameInfo.getGameType() == LudoConstant.FAST_MODE) {
            throwDice(game, gamePlayer, true);
        }
        gameInfoChange(game);
        return gameInfo;
    }

    private void throwDice(Game game, GamePlayer gamePlayer, boolean fastMode) {
        // 设置操作数据
        GameInfo gameInfo = game.getGameInfo();
        OpData opData = gameInfo.getOpData();
        // 重置数据
        opData.setNext(LudoConstant.NONE);
        List<Integer> diceList = opData.getDiceValueList();
        int dice = throwFakeDice(game, gamePlayer);
        int fastModeDice;
        diceList.add(dice);
        if (fastMode) {
            fastModeDice = throwFakeDice(game, gamePlayer);
            diceList.add(fastModeDice);
        }
        // 设置播放数据
        PlayData playData = new PlayData();
        playData.setOpUid(gamePlayer.getUid());
        playData.setOpType(LudoConstant.THROW_DICE);
        if (gameInfo.getGameType() == LudoConstant.CLASSIC_MODE) {
            playData.setDiceValueList(Collections.singletonList(dice));
        } else if (gameInfo.getGameType() == LudoConstant.FAST_MODE) {
            playData.setDiceValueList(new ArrayList<>(diceList));
        }
        // 每次掷骰子时清空播放数据
        gameInfo.getPlayDataList().clear();
        gameInfo.getPlayDataList().add(playData);
        checkDice(game, gamePlayer, opData, fastMode);
    }

    private void checkDice(Game game, GamePlayer gamePlayer, OpData opData, boolean fastMode) {
        GameInfo gameInfo = game.getGameInfo();
        if (fastMode) {
            // 出基地
            if (START_POINT == gamePlayer.getDiceType()) {
                // 1）投掷的骰子中有一个是6点时，点击基地棋子自动移动到第1格。
                if (opData.getDiceValueList().contains(6)) {
                    if (opData.getDiceValueList().get(0).equals(opData.getDiceValueList().get(1))) {
                        gamePlayer.setFastDiceType(gamePlayer.getFastDiceType() + 1);
                        setDiceAgain(gameInfo, gamePlayer);
                    }
                    gamePlayer.setDiceType(0);
                    setChessmanMove(gameInfo);
                } else {
                    if (opData.getDiceValueList().get(0).equals(opData.getDiceValueList().get(1))) {
                        gamePlayer.setFastDiceType(gamePlayer.getFastDiceType() + 1);
                        gamePlayer.setFastDiceCount(-1);
                        opData.getDiceValueList().clear();
                        setDiceAgain(gameInfo, gamePlayer);
                    } else {
                        // 3）如果投掷到非6点的骰子，点击基地的棋子无响应。
                        setNextGameTurn(gameInfo);
                    }
                }
            } else {
                // 快速模式投掷两个骰子的点数相同
                if (opData.getDiceValueList().get(0).equals(opData.getDiceValueList().get(1))) {
                    gamePlayer.setFastDiceType(gamePlayer.getFastDiceType() + 1);
                    setDiceAgain(gameInfo, gamePlayer);
                } else {
                    gamePlayer.setFastDiceType(0);
                    setChessmanMove(gameInfo);
                }
            }
        } else {
            // 经典模式
            // 投掷6点可连续再投掷一次，最多投掷3次
            if (opData.getDiceValueList().lastIndexOf(6) == opData.getDiceValueList().size() - 1) {
                gamePlayer.setDiceType(0);
                // 将棋子移出基地必须要投掷6点的骰子。连续投掷3个6点，该轮投掷无效
                if (opData.getDiceValueList().size() == DICE_TIMES) {
                    // 设置播放数据
                    PlayData playData = new PlayData();
                    playData.setOpUid(gamePlayer.getUid());
                    playData.setOpType(LudoConstant.DICE_SIX_THREE_TIMES);
                    gameInfo.getPlayDataList().add(playData);
                    if (baseChessCount(gamePlayer) == 4) {
                        gamePlayer.setDiceType(START_POINT);
                    }
                    setNextGameTurn(gameInfo);
                } else {
                    setDiceAgain(gameInfo, gamePlayer);
                }
            } else {
                if (START_POINT == gamePlayer.getDiceType()) {
                    setNextGameTurn(gameInfo);
                } else {
                    setChessmanMove(gameInfo);
                }
            }
        }
    }

    /**
     * 设置再掷一次骰子
     */
    private void setDiceAgain(GameInfo gameInfo, GamePlayer gamePlayer) {
        if (gameInfo.getGameType() == LudoConstant.FAST_MODE) {
            gamePlayer.setFastDiceCount(gamePlayer.getFastDiceCount() + 1);
            if (gameInfo.getOpData().getDiceValueList().isEmpty()) {
                setThrowDice(gameInfo, gamePlayer);
            } else {
                setChessmanMove(gameInfo);
            }
        } else if (gameInfo.getGameType() == LudoConstant.CLASSIC_MODE) {
            OpData opData = gameInfo.getOpData();
            opData.setNext(LudoConstant.THROW_DICE_AGAIN);
            opData.setActionTime(DateHelper.getNowSeconds());
        }
    }

    /**
     * 设置掷骰子
     */
    public void setThrowDice(GameInfo gameInfo, GamePlayer gamePlayer) {
        OpData opData = gameInfo.getOpData();
        opData.setOpUid(gamePlayer.getUid());
        opData.setOpType(LudoConstant.THROW_DICE);
        opData.setActionTime(DateHelper.getNowSeconds());
    }

    /**
     * 设置棋子移动
     */
    private void setChessmanMove(GameInfo gameInfo) {
        OpData opData = gameInfo.getOpData();
        opData.setOpType(LudoConstant.CHESSMAN_MOVE);
        opData.setActionTime(DateHelper.getNowSeconds());
    }

    /**
     * 设置下一回合的玩家
     */
    public void setNextGameTurn(GameInfo gameInfo) {
        OpData opData = gameInfo.getOpData();
        String nextGamePlayer;
        List<String> nextPlayerUidList = new ArrayList<>();
        for (GamePlayer player : gameInfo.getPlayerList()) {
            // 已退出的玩家或已经结束的玩家不进行操作
            if (player.getStatus() == LudoConstant.GENERAL || player.getStatus() == LudoConstant.HOSTING) {
                nextPlayerUidList.add(player.getUid());
            }
        }
        int index = nextPlayerUidList.indexOf(opData.getOpUid());
        if (index == nextPlayerUidList.size() - 1) {
            nextGamePlayer = nextPlayerUidList.get(0);
        } else {
            nextGamePlayer = nextPlayerUidList.get(index + 1);
        }
        opData.setOpUid(nextGamePlayer);
        opData.setOpType(LudoConstant.THROW_DICE);
        opData.setActionTime(DateHelper.getNowSeconds());
        // 重置数据
        opData.getDiceValueList().clear();
        opData.setNext(LudoConstant.NONE);
    }

    public GameInfo action(String uid, String gameId, int diceIndex, String chessmanId) {
        Game game = gameService.getGame(gameId);
        GameInfo gameInfo = game.getGameInfo();
        synchronized (gameInfo) {
            if (checkGameTurn(gameInfo, uid, LudoConstant.CHESSMAN_MOVE)) {
                return gameInfo;
            }
            GamePlayer gamePlayer = getGamePlayer(uid, gameInfo);
            stopHosting(gameInfo, gamePlayer);
            action(game, gamePlayer, diceIndex, chessmanId, true);
            gameInfoChange(game);
            return gameInfo;
        }
    }

    public void action(Game game, GamePlayer gamePlayer, int diceIndex, String chessmanId, boolean clearPlayData) {
        GameInfo gameInfo = game.getGameInfo();
        gameInfo.setStep(gameInfo.getStep() + 1);
        List<Integer> diceValueList = gameInfo.getOpData().getDiceValueList();
        if (diceIndex >= diceValueList.size()) {
            logger.info("index out of range uid={} diceValueList size={} diceIndex={}",
                    gamePlayer.getUid(), diceValueList.size(), diceIndex);
            return;
        }
        int dice = diceValueList.remove(diceIndex);
        if (clearPlayData) {
            gameInfo.getPlayDataList().clear();
        }
        List<PlayData> playDataList = gameInfo.getPlayDataList();
        // 处理玩家的棋子信息
        Chessman chessman = chessmanService.getChessman(gamePlayer, chessmanId);
        if (null == chessman) {
            return;
        }
        int fromNodeId = chessman.getNodeId();
        int side = gamePlayer.getSide();
        long baseChessCount = baseChessCount(gamePlayer);
        boolean outBase = false;
        if (chessman.getNodeId() < 0 && dice == 6) {
            chessman.setNodeId(NodeConstant.getSideOffset(side));
            // 棋子移出基地时，在棋子的上方显示酷的动态表情
            PlayData playData = new PlayData();
            playData.setOpUid(gamePlayer.getUid());
            playData.setChessmanId(chessman.getChessmanId());
            playData.setOpType(LudoConstant.FIRST_OUT_BASE);
            playData.setFromNodeId(chessman.getStartNodeId());
            playData.setToNodeId(chessman.getNodeId());
            playDataList.add(playData);
            outBase = true;
        } else {
            if (chessman.getNodeId() < 0) {
                return;
            }
            int nextNode = chessman.getNodeId() + dice;
            if (side == LudoConstant.BLUE_SIDE) {
                if (nextNode > NodeConstant.BLUE_TURNING) {
                    if (chessman.getNodeId() < NodeConstant.BLUE_FINAL_START) {
                        int overstep = nextNode - NodeConstant.BLUE_TURNING;
                        nextNode = NodeConstant.BLUE_FINAL_START + overstep - 1;
                        chessman.setFinialCount(1);
                    }
                }
            } else if (side == LudoConstant.RED_SIDE) {
                if (nextNode > NodeConstant.REVERSION_NODE && chessman.getNodeId() <= NodeConstant.REVERSION_NODE) {
                    nextNode = nextNode - NodeConstant.REVERSION_NODE;
                } else if (nextNode > NodeConstant.RED_TURNING && chessman.getNodeId() < NodeConstant.RED_OFFSET) {
                    int overstep = nextNode - NodeConstant.RED_TURNING;
                    nextNode = NodeConstant.RED_FINAL_START + overstep - 1;
                    chessman.setFinialCount(1);
                }
            } else if (side == LudoConstant.GREEN_SIDE) {
                if (nextNode > NodeConstant.REVERSION_NODE && chessman.getNodeId() <= NodeConstant.REVERSION_NODE) {
                    nextNode = nextNode - NodeConstant.REVERSION_NODE;
                } else if (nextNode > NodeConstant.GREEN_TURNING && chessman.getNodeId() < NodeConstant.GREEN_OFFSET) {
                    int overstep = nextNode - NodeConstant.GREEN_TURNING;
                    nextNode = NodeConstant.GREEN_FINAL_START + overstep - 1;
                    chessman.setFinialCount(1);
                }
            } else {
                if (nextNode > NodeConstant.REVERSION_NODE && chessman.getNodeId() <= NodeConstant.REVERSION_NODE) {
                    nextNode = nextNode - NodeConstant.REVERSION_NODE;
                } else if (nextNode > NodeConstant.YELLOW_TURNING && chessman.getNodeId() < NodeConstant.YELLOW_OFFSET) {
                    int overstep = nextNode - NodeConstant.YELLOW_TURNING;
                    nextNode = NodeConstant.YELLOW_FINAL_START + overstep - 1;
                    chessman.setFinialCount(1);
                }
            }
            // 当棋子距离终点格子数小于等于6个格子时，投掷的点数必须等于格子数量才可以到达终点
            if (!NodeConstant.isFinalPoint(side, nextNode) && nextNode > NodeConstant.getFinalPoint(side)) {
                finialNodeMove(game, gamePlayer, dice, chessman);
                return;
            }
            chessman.setNodeId(nextNode);
        }
        // 处理棋盘数据
        int nodeId = chessman.getNodeId();
        if (!outBase) {
            PlayData playData = new PlayData();
            playData.setOpUid(gamePlayer.getUid());
            playData.setChessmanId(chessman.getChessmanId());
            playData.setOpType(LudoConstant.CHESSMAN_MOVE);
            playData.setFromNodeId(fromNodeId);
            playData.setToNodeId(nodeId);
            playData.setDiceValueList(null);
            playDataList.add(playData);
        }

        // 先移除原先的棋子
        boardNodeService.removeChessman(gameInfo, fromNodeId, chessmanId);
        NodeData nodeData = boardNodeService.getNodeData(gameInfo, nodeId);
        List<Chessman> chessmanList = nodeData.getChessmanList();
        chessmanList.add(chessman);
        // 到达终点
        if (NodeConstant.isFinalPoint(side, nodeId)) {
            handleFinialMove(game, gamePlayer, chessman, fromNodeId, nodeId);
        } else {
            // 第1、14、27、40格为地球，9、22、35、48格为五角星，都具有保护棋子不被撞的作用。
            if (!NodeConstant.isProtectedNode(nodeId)) {
                // 如果有2个相同颜色的棋子在一个格子里，这个格子为任意格子，则不可以撞子。
                Map<String, Long> statMap = chessmanList.stream()
                        .filter(chess -> !chess.getUid().equals(gamePlayer.getUid()))
                        .collect(Collectors.groupingBy(Chessman::getUid, Collectors.counting()));
                for (Long value : statMap.values()) {
                    if (value < 2) {
                        // 吃棋子
                        if (hitChessman(gameInfo, gamePlayer, chessmanList)) {
                            setDiceAgain(gameInfo, gamePlayer);
                        }
                    }
                }
            }
            checkNextGameTurn(gameInfo, gamePlayer, diceValueList);
        }
        // 首次出基地时自动执行下一个棋子
        if (baseChessCount == 4 && baseChessCount(gamePlayer) != baseChessCount) {
            List<Integer> diceList = gameInfo.getOpData().getDiceValueList();
            if (diceList.size() == 1 && diceList.get(0) != 6) {
                List<Chessman> playerChessmanList = gamePlayer.getChessmanList();
                if (playerChessmanList.stream().filter(chess -> chess.getNodeId() < 0).count() == 3) {
                    action(game, gamePlayer, 0, chessmanId, false);
                }
            }
        }
    }

    private void checkNextGameTurn(GameInfo gameInfo, GamePlayer gamePlayer, List<Integer> diceValueList) {
        if (diceValueList.isEmpty()) {
            if (gameInfo.getGameType() == LudoConstant.FAST_MODE) {
                int fastDiceCount = gamePlayer.getFastDiceCount();
                if (fastDiceCount > 0) {
                    gamePlayer.setFastDiceCount(gamePlayer.getFastDiceCount() - 1);
                    setThrowDice(gameInfo, gamePlayer);
                } else {
                    setNextGameTurn(gameInfo);
                }
            } else if (gameInfo.getGameType() == LudoConstant.CLASSIC_MODE) {
                if (gameInfo.getOpData().getNext() != LudoConstant.THROW_DICE_AGAIN) {
                    setNextGameTurn(gameInfo);
                }
            }
        }
    }

    /**
     * 当棋子距离终点格子数小于等于6个格子时，投掷的点数必须等于格子数量才可以到达终点
     */
    private void finialNodeMove(Game game, GamePlayer gamePlayer, int dice, Chessman chessman) {
        GameInfo gameInfo = game.getGameInfo();
        List<Integer> diceValueList = gameInfo.getOpData().getDiceValueList();
        long finishChessCount = finishedChessCount(gamePlayer);
        long baseChessCount = baseChessCount(gamePlayer);
        if (finishChessCount == 3 && baseChessCount == 0) {
            for (Integer value : diceValueList) {
                // 如果剩余棋子可以到达终点则棋子到达终点
                if (NodeConstant.isFinalPoint(gamePlayer.getSide(), chessman.getNodeId() + value)) {
                    handleFinialMove(game, gamePlayer, chessman, chessman.getNodeId(), chessman.getNodeId() + value);
                    return;
                }
            }
        }
        if (activityChessCount(gamePlayer) > 0) {
            // 玩家还有别的选择，当前操作无效
            diceValueList.add(dice);
        } else {
            checkNextGameTurn(gameInfo, gamePlayer, diceValueList);
        }
    }

    private void handleFinialMove(Game game, GamePlayer gamePlayer, Chessman chessman, int fromNodeId, int nodeId) {
        // 设置播放数据
        GameInfo gameInfo = game.getGameInfo();
        PlayData playData = new PlayData();
        playData.setOpUid(gamePlayer.getUid());
        playData.setChessmanId(chessman.getChessmanId());
        playData.setOpType(LudoConstant.CHESSMAN_FINISH);
        playData.setFromNodeId(fromNodeId);
        playData.setToNodeId(nodeId);
        gameInfo.getPlayDataList().add(playData);
        if (finishChessman(game, gamePlayer)) {
            finishGame(game);
        } else {
            // 等待其他玩家完成游戏
            if (chessmanFinished(gamePlayer)) {
                // 回写数据防止出现下一位玩家错乱
                gamePlayer.setStatus(LudoConstant.GENERAL);
                setNextGameTurn(gameInfo);
                gamePlayer.setStatus(LudoConstant.VICTORY);
            } else {
                checkCanMove(gamePlayer, gameInfo, false);
                setDiceAgain(gameInfo, gamePlayer);
                checkNextGameTurn(gameInfo, gamePlayer, gameInfo.getOpData().getDiceValueList());
            }
        }
    }

    /**
     * 判断玩家是否所有棋子到达终点，到达终点后增加胜利玩家
     */
    private boolean finishChessman(Game game, GamePlayer gamePlayer) {
        GameInfo gameInfo = game.getGameInfo();
        int actualPlayerNumber = gameInfo.getPlayerNumber();
        // 1、2个玩家：其中1个玩家4个棋子都到达终点，该局游戏结束；1个玩家中途强制退出游戏，该局游戏结束。
        if (actualPlayerNumber == 2) {
            if (chessmanFinished(gamePlayer)) {
                gamePlayer.setStatus(LudoConstant.VICTORY);
                WinnerData winnerData = gameInfo.getWinnerData();
                if (null == winnerData) {
                    winnerData = new WinnerData();
                }
                int currency = config.getCurrencyById(gameInfo.getCurrencyId());
                List<WinnerPlayer> playerList = winnerData.getWinnerList();
                // 2个玩家结算公式：总加入游戏费 - 平台抽成10% = 赢家奖金
                int reword = getWinnerReward(gameInfo);
                fullWinnerData(gameInfo, gamePlayer, playerList, reword);
                // 增加失败玩家
                fullDefeatData(gameInfo, playerList, currency);
                gameInfo.setWinnerData(winnerData);
                return true;
            }
        } else if (actualPlayerNumber == 3 || actualPlayerNumber == 4) {
            // 2、3个玩家：其中最先2个玩家的所有棋子都到达终点，该局游戏结束；1个玩家中途退出游戏，其中1个玩家所有棋子最先全部到达终点，该局游戏结束。2个玩家中途退出游戏，该局游戏结束。
            // 3、4个玩家：其中最先2个玩家的所有棋子都到达终点，该局游戏结束。若1个玩家中途退出游戏，规则同第2条一样，若2个玩家中途退出游戏第1条一样。
            if (chessmanFinished(gamePlayer)) {
                WinnerData winnerData = gameInfo.getWinnerData();
                if (null == winnerData) {
                    winnerData = new WinnerData();
                }
                List<WinnerPlayer> playerList = winnerData.getWinnerList();
                // 第一个胜利玩家
                int currency = config.getCurrencyById(gameInfo.getCurrencyId());
                if (playerList.size() == 0) {
                    // 第一个胜利玩家
                    // 3个玩家结算公式：总加入游戏费 - 平台抽成10% = 赢家奖金，赢家奖金 - 第二名游戏加入费（相当于把第二名的游戏费返还）= 第一名奖金
                    gamePlayer.setStatus(LudoConstant.VICTORY);
                    int reword = getWinnerReward(gameInfo);
                    fullWinnerData(gameInfo, gamePlayer, playerList, reword);
                    gameInfo.setWinnerData(winnerData);
                    int finishedPlayers = 0;
                    for (GamePlayer player : gameInfo.getPlayerList()) {
                        if (player.getStatus() == LudoConstant.VICTORY || player.getStatus() == LudoConstant.EXIT) {
                            finishedPlayers++;
                        }
                    }
                    if (actualPlayerNumber - finishedPlayers == 1) {
                        // 只剩下最后一个玩家，增加失败玩家，游戏结束
                        fullDefeatData(gameInfo, playerList, currency);
                        return true;
                    } else {
                        return false;
                    }
                } else if (playerList.size() == 1) {
                    // 第二个胜利玩家，游戏结束
                    gamePlayer.setStatus(LudoConstant.VICTORY);
                    fullWinnerData(gameInfo, gamePlayer, playerList, currency);
                    // 增加失败玩家
                    fullDefeatData(gameInfo, playerList, currency);
                    gameInfo.setWinnerData(winnerData);
                    return true;
                }
            }
        }
        return false;
    }

    private int getAuto(GameInfo gameInfo, GamePlayer gamePlayer) {
        if (gamePlayer.getHostingStartTime() > 0) {
            gamePlayer.addHostingTime();
            gamePlayer.setHostingStartTime(0);
        }
        int auto = 0;
        if (gamePlayer.getHostingTime() > 0) {
            if ((DateHelper.getNowSeconds() - gameInfo.getStartTime()) / gamePlayer.getHostingTime() < 2) {
                auto = 1;
            }
        }
        return auto;
    }

    private void fullWinnerData(GameInfo gameInfo, GamePlayer gamePlayer, List<WinnerPlayer> playerList, int reword) {
        playerList.add(new WinnerPlayer(gamePlayer, "+" + reword, getAuto(gameInfo, gamePlayer)));
    }

    private void fullDefeatData(GameInfo gameInfo, List<WinnerPlayer> playerList, int currency) {
        for (GamePlayer player : gameInfo.getPlayerList()) {
            if (player.getStatus() != LudoConstant.VICTORY) {
                playerList.add(new WinnerPlayer(player, "-" + currency, getAuto(gameInfo, player)));
            }
        }
    }

    private boolean chessmanFinished(GamePlayer gamePlayer) {
        return finishedChessCount(gamePlayer) == 4;
    }

    /**
     * 获取已到达终点的棋子数量
     */
    public long finishedChessCount(GamePlayer gamePlayer) {
        return gamePlayer.getChessmanList().stream()
                .filter(chessman -> chessman.getNodeId() == NodeConstant.getFinalPoint(gamePlayer.getSide())).count();
    }

    /**
     * 获取在基地的棋子数量
     */
    public long baseChessCount(GamePlayer gamePlayer) {
        return gamePlayer.getChessmanList().stream()
                .filter(chessman -> chessman.getNodeId() == chessman.getStartNodeId()).count();
    }

    /**
     * 获取活跃棋子的数量
     */
    public long activityChessCount(GamePlayer gamePlayer) {
        return gamePlayer.getChessmanList().stream()
                .filter(chess -> chess.getNodeId() > 0
                        && chess.getNodeId() < NodeConstant.getFinialStart(chess.getSide())).count();
    }

    /**
     * 撞棋
     */
    private boolean hitChessman(GameInfo gameInfo, GamePlayer gamePlayer, List<Chessman> chessmanList) {
        boolean isHit = false;
        Iterator<Chessman> iterator = chessmanList.iterator();
        while (iterator.hasNext()) {
            Chessman chess = iterator.next();
            if (!chess.getUid().equals(gamePlayer.getUid())) {
                // 增加播放动画
                PlayData playData = new PlayData();
                playData.setOpUid(gamePlayer.getUid());
                playData.setOpType(LudoConstant.CHESSMAN_RETURN);
                playData.setChessmanId(chess.getChessmanId());
                playData.setFromNodeId(chess.getNodeId());
                playData.setToNodeId(chess.getStartNodeId());
                playData.setDiceValueList(null);
                gameInfo.getPlayDataList().add(playData);
                iterator.remove();
                // 棋子返回基地
                chess.setNodeId(chess.getStartNodeId());

                // 处理所有棋子都返回基地
                checkCanMove(getGamePlayer(chess.getUid(), gameInfo), gameInfo, true);
                isHit = true;
            }
        }
        return isHit;
    }

    private void checkCanMove(GamePlayer gamePlayer, GameInfo gameInfo, boolean isHit) {
        // 处理所有棋子都返回基
        long finishedChessCount = finishedChessCount(gamePlayer);
        long baseChessCount = baseChessCount(gamePlayer);
        if (finishedChessCount + baseChessCount == 4) {
            gamePlayer.setDiceType(START_POINT);
            // 防止快速模式出现无效骰子卡死
            if (gameInfo.getGameType() == LudoConstant.FAST_MODE && !isHit) {
                if (!gameInfo.getOpData().getDiceValueList().contains(6)) {
                    gameInfo.getOpData().getDiceValueList().clear();
                }
            }
        }
    }

    /**
     * 获取下一步的格子id,超出时到达终点，仅作判断
     *
     * @param nextNode 下一节点
     * @param curNode  当前节点
     * @param side     游戏方位
     */
    public int getNextNodeId(int nextNode, int curNode, int side) {
        if (side == LudoConstant.BLUE_SIDE) {
            if (nextNode > NodeConstant.BLUE_TURNING) {
                if (curNode >= NodeConstant.BLUE_FINAL_START) {
                    if (nextNode >= NodeConstant.getFinalPoint(side)) {
                        return NodeConstant.BLUE_FINAL;
                    }
                } else {
                    int overstep = nextNode - NodeConstant.BLUE_TURNING;
                    nextNode = NodeConstant.BLUE_FINAL_START + overstep - 1;
                }
            }
        } else {
            if (nextNode > NodeConstant.REVERSION_NODE && curNode <= NodeConstant.REVERSION_NODE) {
                nextNode = nextNode - NodeConstant.REVERSION_NODE;
            } else if (nextNode > NodeConstant.getTurning(side) && curNode < NodeConstant.getSideOffset(side)) {
                int overstep = nextNode - NodeConstant.getTurning(side);
                nextNode = NodeConstant.getFinialStart(side) + overstep - 1;
                return nextNode;
            }
            if (curNode >= NodeConstant.getFinialStart(side) && nextNode >= NodeConstant.getFinalPoint(side)) {
                return NodeConstant.getFinalPoint(side);
            }
        }
        return nextNode;
    }

    private boolean checkGameTurn(GameInfo gameInfo, String uid, int opType) {
        OpData opData = gameInfo.getOpData();
        if (!opData.getOpUid().equals(uid) || opData.getOpType() != opType) {
            if (opType == LudoConstant.THROW_DICE && opData.getNext() == LudoConstant.THROW_DICE_AGAIN) {
                return false;
            }
            logger.info("not players game turn uid={}", uid);
            return true;
        }
        return false;
    }

    /**
     * 取消托管
     */
    private void stopHosting(GameInfo gameInfo, GamePlayer gamePlayer) {
        if (gamePlayer.getHostingStartTime() > 0) {
            gamePlayer.addHostingTime();
            gamePlayer.setHostingStartTime(0);
        }
        gamePlayer.setStatus(LudoConstant.GENERAL);
        gameInfo.setModifyTime(DateHelper.getNowSeconds());
    }

    private GamePlayer getGamePlayer(String uid, GameInfo gameInfo) {
        for (GamePlayer gamePlayer : gameInfo.getPlayerList()) {
            if (gamePlayer.getUid().equals(uid)) {
                return gamePlayer;
            }
        }
        logger.info("player not found uid={}", uid);
        throw new GameException(GameHttpCode.PLAYER_NOT_FOUND, gameInfo.toWebData());
    }

    private int throwFakeDice(Game game, GamePlayer gamePlayer) {
        // 快速模式不能出现连续三次相同的骰子数
        if (game.getGameInfo().getGameType() == LudoConstant.FAST_MODE) {
            if (gamePlayer.getFastDiceType() == 2) {
                if (!game.getGameInfo().getOpData().getDiceValueList().isEmpty()) {
                    while (true) {
                        int dice = (int) (Math.random() * 6) + 1;
                        if (!game.getGameInfo().getOpData().getDiceValueList().contains(dice)) {
                            return dice;
                        }
                    }
                }
            }
        }

        // 每局游戏的第一颗棋子出基地，前4次投掷骰子中至少必出一个6点
        if (START_POINT == gamePlayer.getDiceType()) {
            if (gamePlayer.getStartNodeCount() == 3) {
                if (!game.getGameInfo().getOpData().getDiceValueList().contains(6)) {
                    gamePlayer.setStartNodeCount(0);
                    return 6;
                }
            } else {
                gamePlayer.setStartNodeCount(gamePlayer.getStartNodeCount() + 1);
            }
        }

        // 距离终点的棋子小于等于6点时，投掷5次内必中到达终点的点数。
        List<Chessman> playerChessmanList = gamePlayer.getChessmanList();
        for (Chessman chessman : playerChessmanList) {
            if (chessman.getFinialCount() > 0
                    && !NodeConstant.isFinalPoint(gamePlayer.getSide(), chessman.getNodeId())) {
                chessman.setFinialCount(chessman.getFinialCount() + 1);
                if (chessman.getFinialCount() > 5) {
                    chessman.setFinialCount(0);
                    return NodeConstant.getFinalPoint(gamePlayer.getSide()) - chessman.getNodeId();
                }
            }
        }

        // 如果对手的棋子距离自己的棋子小于等于6点时，60%的概率投掷的点数刚好可以撞对手的棋子。
        List<Integer> targetDiceList = new ArrayList<>();
        for (Chessman chessman : gamePlayer.getChessmanList()) {
            if (chessman.getStartNodeId() < 0) {
                continue;
            }
            for (int i = 1; i <= 6; i++) {
                int nextNode = chessman.getNodeId() + i;
                if (nextNode > NodeConstant.REVERSION_NODE) {
                    nextNode = nextNode - NodeConstant.REVERSION_NODE;
                }
                if (nextNode > NodeConstant.getTurning(gamePlayer.getSide())
                        && chessman.getNodeId() < NodeConstant.getSideOffset(gamePlayer.getSide())) {
                    List<Chessman> chessmanList = boardNodeService
                            .getNodeData(game.getGameInfo(), chessman.getNodeId() + i).getChessmanList();
                    if (chessmanList.stream().anyMatch(chess -> !chess.getUid().equals(gamePlayer.getUid()))) {
                        targetDiceList.add(i);
                    }
                }
            }
        }
        if (!targetDiceList.isEmpty()) {
            if (targetDiceList.size() == 1) {
                if ((int) (Math.random() * 10) + 1 <= 5) {
                    return targetDiceList.get(0);
                }
            } else if (targetDiceList.size() == 2) {
                if ((int) (Math.random() * 10) + 1 <= 6) {
                    return targetDiceList.get(0);
                }
            } else if (targetDiceList.size() == 3) {
                if ((int) (Math.random() * 10) + 1 <= 7) {
                    return targetDiceList.get(0);
                }
            } else {
                if ((int) (Math.random() * 10) + 1 <= 8) {
                    return targetDiceList.get(0);
                }
            }
        }
        return (int) (Math.random() * 6) + 1;
    }

    public GameInfo check(String roomId, String uid) {
        return gameService.check(roomId, uid);
    }

    public void gameInfoChange(Game game) {
        GameInfo gameInfo = game.getGameInfo();
        gameInfo.setTotalCurrency(config.getCurrencyById(gameInfo.getCurrencyId()) * game.getPlayerMap().size());
        if (gameInfo.getStatus() == LudoConstant.MATCHING || gameInfo.getStatus() == LudoConstant.PROCESSING) {
            ludoRedis.saveGameInfo(gameInfo);
        }
        if (gameInfo.getStatus() == LudoConstant.PROCESSING) {
            sendLudoMsg(game);
        } else {
            sendRoomMsg(game);
        }
    }

    public GameInfo quitGame(String uid, String gameId) {
        GameInfo gameInfo = gameService.getGame(gameId).getGameInfo();
        if (gameInfo.getStatus() == LudoConstant.PROCESSING) {
            return exitGame(uid, gameId);
        } else {
            return kickOut(uid, uid, gameId);
        }
    }

    public ApiResult<Object> getLudoSquare(int page) {
        try {
            List<LudoSquareData> resultList = new ArrayList<>();
            List<GameInfo> allGameInfo = ludoRedis.getAllGameInfo();
            LudoSquareData ludoSquareData;
            for (GameInfo gameInfo : allGameInfo) {
                if (CollectionUtils.isEmpty(gameInfo.getPlayerList())) {
                    continue;
                }
                ludoSquareData = new LudoSquareData();
                ludoSquareData.setRoomId(gameInfo.getRoomId());
                MongoRoomData roomData = mongoRoomDao.getDataFromCache(gameInfo.getRoomId());
                if (roomData == null) {
                    continue;
                }
                // 房间用户数量
                int personCount = roomPlayerRedis.getRoomActorsCount(gameInfo.getRoomId());
                if (0 == personCount) {
                    continue;
                }

                ludoSquareData.setPersonCount(personCount);
                ludoSquareData.setRoomHead(roomData.getHead());
                ludoSquareData.setRoomName(roomData.getName());
                try {
                    ludoSquareData.setRoomCountry(config.getCountryFlagMap()
                            .get(roomData.getCountry().split("_")[0].toLowerCase()));
                } catch (Exception e) {
                    logger.info("cannot parse country orig country={}", roomData.getCountry());
                    // 默认阿拉伯联合酋长国
                    ludoSquareData.setRoomCountry(config.getCountryFlagMap().get("ae"));
                }

                ludoSquareData.setGameType(gameInfo.getGameType());
                ludoSquareData.setCurrency(config.getCurrencyById(gameInfo.getCurrencyId()));
                ludoSquareData.setCurrencyType(gameInfo.getCurrencyType());
                ludoSquareData.setStart(gameInfo.getStatus() == LudoConstant.PROCESSING);
                ludoSquareData.setCreateTime(gameInfo.getCreateTime());

                List<PlayerData> playerDataList = new ArrayList<>();
                for (GamePlayer gamePlayer : gameInfo.getPlayerList()) {
                    PlayerData playerData = new PlayerData();
                    playerData.setUid(gamePlayer.getUid());
                    playerData.setUserHead(gamePlayer.getHead());
                    playerDataList.add(playerData);
                }
                ludoSquareData.setPlayerDataList(playerDataList);
                resultList.add(ludoSquareData);
            }
            Comparator<LudoSquareData> startAsc = Comparator.comparing(LudoSquareData::isStart);
            Comparator<LudoSquareData> createTimeAsc = Comparator.comparing(LudoSquareData::getCreateTime);
            resultList.sort(startAsc.thenComparing(createTimeAsc));
            PageUtils.PageData<LudoSquareData> pageData = PageUtils.getPageData(resultList, page, 20);
            JSONObject jsonObject = new JSONObject();
            jsonObject.put("nextUrl", pageData.nextPage == 0 ? "" : pageData.nextPage + "");
            if (pageData.nextPage == 0 && page != 1) {
                jsonObject.put("list", new ArrayList<>());
            } else {
                jsonObject.put("list", pageData.list);
            }
            return new ApiResult<>().ok(jsonObject);
        } catch (Exception e) {
            logger.error("get ludo square list error {}", e.getMessage(), e);
            return new ApiResult<>().error(HttpCode.SERVER_ERROR);
        }
    }

    public ApiResult<String> match() {
        String roomId = "";
        List<GameInfo> allGameInfo = ludoRedis.getAllGameInfo();
        for (GameInfo gameInfo : allGameInfo) {
            if (gameInfo.getStatus() == LudoConstant.MATCHING
                    && gameInfo.getPlayerList().size() < gameInfo.getPlayerNumber()) {
                roomId = gameInfo.getRoomId();
                break;
            }
        }
        return new ApiResult<String>().ok(roomId);
    }

    public void stopWatch(String uid, String roomId) {
        Game game = gameService.getGameByRoomId(roomId);
        if (null != game) {
            watch(uid, game, LudoConstant.STOP_WATCH);
        }
    }

    public GameInfo watch(String uid, Game game, int type) {
        if (!game.getPlayerMap().containsKey(uid) && type == LudoConstant.WATCH) {
            game.addWatchUserSet(uid);
        } else if (type == LudoConstant.STOP_WATCH) {
            game.removeWatchUserSet(uid);
            return null;
        }
        return game.getGameInfo();
    }

    public boolean unauthorized(String roomId, String uid) {
        int role = memberDao.getRoleData(roomId, uid).getRole();
        return role == RoomRoleType.AUDIENCE || role == RoomRoleType.MEMBER;
    }
}
