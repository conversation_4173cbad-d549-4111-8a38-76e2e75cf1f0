package com.quhong.service;

import com.quhong.analysis.EventDTO;
import com.quhong.analysis.EventReport;
import com.quhong.analysis.LiveMicApplyEvent;
import com.quhong.analysis.LiveSettlementRecordEvent;
import com.quhong.config.CaffeineCacheConfig;
import com.quhong.config.RoomConfig;
import com.quhong.constant.DetectOriginConstant;
import com.quhong.core.config.ServerConfig;
import com.quhong.core.timers.DelayTask;
import com.quhong.core.timers.TimerService;
import com.quhong.core.utils.DateHelper;
import com.quhong.dailyTask.DailyTaskService;
import com.quhong.data.ActorData;
import com.quhong.data.DailyTaskMqData;
import com.quhong.data.dto.FaceDetection;
import com.quhong.data.dto.LiveDevoteRankDTO;
import com.quhong.data.dto.LiveRoomDTO;
import com.quhong.data.dto.LiveStatusDTO;
import com.quhong.data.vo.*;
import com.quhong.dto.ImageDTO;
import com.quhong.dto.TextDTO;
import com.quhong.enums.*;
import com.quhong.exception.CommonException;
import com.quhong.feign.IDetectService;
import com.quhong.handler.HttpEnvData;
import com.quhong.image.ImageUrlGenerator;
import com.quhong.mongo.dao.*;
import com.quhong.mongo.data.*;
import com.quhong.msg.room.*;
import com.quhong.mysql.dao.FamilyMemberDao;
import com.quhong.mysql.dao.RoomBlacklistDao;
import com.quhong.mysql.dao.VipInfoDao;
import com.quhong.mysql.dao.WhiteTestDao;
import com.quhong.mysql.data.FamilyMemberData;
import com.quhong.redis.*;
import com.quhong.room.*;
import com.quhong.room.api.zego.ZegoApi;
import com.quhong.room.cache.RoomActorCache;
import com.quhong.room.data.EnterCartonData;
import com.quhong.room.data.RoomActorDetailData;
import com.quhong.room.mic.RoomMicService;
import com.quhong.room.recommend.RecommendRedis;
import com.quhong.room.recommend.hash.BloomFilter;
import com.quhong.room.redis.MicApplyRedis;
import com.quhong.room.redis.RoomKickRedis;
import com.quhong.room.redis.RoomPlayerRedis;
import com.quhong.room.redis.RoomRedis;
import com.quhong.sdk.zego.ZegoService;
import com.quhong.utils.*;
import com.quhong.vo.RoomMicListVo;
import jakarta.annotation.Resource;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.stereotype.Service;
import org.springframework.util.ObjectUtils;
import org.springframework.util.StringUtils;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.*;
import java.time.temporal.ChronoUnit;
import java.time.temporal.TemporalAdjusters;
import java.util.*;
import java.util.concurrent.TimeUnit;

/**
 * <AUTHOR>
 * @date 2024/5/27
 */
@Service
public class LiveRoomService {
    public static final int ACTOR_PAGE_SIZE = 20;
    public static final int FACE_DETECTION = 10;
    public static final ZoneId zoneId = ZoneId.of("+3");
    public static final Set<String> GCC_COUNTRY_SET = new HashSet<>(Arrays.asList("SA", "KW", "AE", "QA", "OM", "BH"));
    private static final Logger logger = LoggerFactory.getLogger(LiveRoomService.class);
    private static final Set<String> FACE_WHITELIST_COUNTRY_SET = new HashSet<>(Arrays.asList("SA", "KW", "AE", "QA", "OM", "BH", "YE", "JO"));
    public static final Set<String> RANK_WHITELIST = ServerConfig.isNotProduct() ? Set.of("64c34444c3f5a27c5acaa586", "64c34444c3f5a27c5acaa587", "64a537281698290105e16343") : Set.of("64c3577b0d86d537282d51bb", "64c3577b0d86d537282d51bc");

    @Resource
    private MongoRoomDao roomDao;
    @Resource
    private ActorDao actorDao;
    @Resource
    private RoomTags roomTags;
    @Resource
    private IDetectService detectService;
    @Resource
    private WhitelistRedis whitelistRedis;
    @Resource
    private RoomActorCache roomActorCache;
    @Resource
    private BlockRedis blockRedis;
    @Resource
    private RoomKickRedis roomKickRedis;
    @Resource
    private RoomBlacklistDao roomBlacklistDao;
    @Resource
    private ZegoApi zegoApi;
    @Resource
    private MicApplyRedis micApplyRedis;
    @Resource
    private CommonDao commonDao;
    @Resource
    private RoomMemberDao roomMemberDao;
    @Resource
    private FollowDao followDao;
    @Resource
    private DailyTaskService dailyTaskService;
    @Resource
    private ZegoService zegoService;
    @Resource
    private RoomRedis roomRedis;
    @Resource
    private RoomWebSender roomWebSender;
    @Resource
    private RoomActorListService roomActorListService;
    @Resource
    private RoomMicService roomMicService;
    @Resource
    private EnterRoomContent enterRoomContent;
    @Autowired
    private RoomPlayerRedis roomPlayerRedis;
    @Autowired
    private LiveRoomDevoteDao liveRoomDevoteDao;
    @Resource
    private BannerListService bannerListService;
    @Resource(name = DataRedisBean.MAIN_CLUSTER)
    private StringRedisTemplate clusterRedis;
    @Resource
    private LiveDataRedis liveDataRedis;
    @Autowired
    private VipInfoDao vipInfoDao;
    @Autowired
    private LiveRoomRedis liveRoomRedis;
    @Autowired
    private RecommendRedis recommendRedis;
    @Resource
    private UserMonitorDao userMonitorDao;
    @Resource
    private PackService packService;
    @Autowired
    private HotRoomRedis hotRoomRedis;
    @Autowired
    private ActorConfigDao actorConfigDao;
    @Resource
    private RoomConfigDao roomConfigDao;
    @Resource
    private RoomAdminRedis roomAdminRedis;
    @Resource
    private WhiteTestDao whiteTestDao;
    @Resource
    private EventReport eventReport;
    @Resource
    private RoomStreamService roomStreamService;
    @Resource
    private RoomConfig roomConfig;
    @Resource
    private RecentlyRoomRedis recentlyRoomRedis;
    @Resource
    private LiveAuditDao liveAuditDao;
    @Resource
    private RoomLastMsgRedis roomLastMsgRedis;
    @Resource
    private LiveDailyDevoteDao liveDailyDevoteDao;
    @Resource
    private LivePkRedis livePkRedis;
    @Resource
    private AdvancedGameRedis advancedGameRedis;
    @Resource
    private MysteryService mysteryService;
    @Resource
    private MysteryRedis mysteryRedis;
    @Resource
    private SvipLevelService svipLevelService;
    @Resource
    private LivePkDao livePkDao;
    @Resource
    private RoomNewService roomNewService;
    @Resource
    private FreezeInfoRedis freezeInfoRedis;
    @Resource
    private CameraPerAnchorRedis cameraPerAnchorRedis;
    @Resource
    private DirtyPictureRedis dirtyPictureRedis;
    @Resource
    private FamilyMemberDao familyMemberDao;
    @Resource
    private LiveScreenshotListDao liveScreenshotListDao;
    @Resource
    private LiveScreenshotRecordDao liveScreenshotRecordDao;

    public NewPreCreateRoomVO preCreate(HttpEnvData req) {
        ActorData actorData = actorDao.getActorData(req.getUid());
        if (actorData == null) {
            throw new CommonException(HttpCode.PARAM_ERROR);
        }
        NewPreCreateRoomVO vo = new NewPreCreateRoomVO();
        MongoRoomData roomData = roomDao.findData(RoomUtils.formatRoomId(req.getUid()));
        NewPreCreateRoomVO.Info chatRoomInfo = new NewPreCreateRoomVO.Info();
        chatRoomInfo.setTags(req.getSlang() == SLangType.ARABIC ? roomTags.getArTags() : roomTags.getEnTags());
        vo.setChatRoomInfo(chatRoomInfo);
        chatRoomInfo.setHead(ImageUrlGenerator.generateRoomUserUrl(actorData));
        fillRoomInfo(req, roomData, chatRoomInfo);

        MongoRoomData liveRoomData = roomDao.findData(RoomUtils.formatLiveRoomId(req.getUid()));
        NewPreCreateRoomVO.Info liveRoomInfo = new NewPreCreateRoomVO.Info();
        liveRoomInfo.setTags(req.getSlang() == SLangType.ARABIC ? roomTags.getArTags() : roomTags.getEnTags());
        liveRoomInfo.setBeautySwitch(1);
        liveRoomInfo.setBeautyFileBytecode(req.getOs() == ClientOS.IOS ? BeautyAuthUtils.IOS_FILE_BYTECODE : BeautyAuthUtils.ANDROID_FILE_BYTECODE);
        liveRoomInfo.setHead(ImageUrlGenerator.generateRoomUserUrl(actorData));
        liveRoomInfo.setFaceAuthStatus(ObjectUtils.isEmpty(actorData.getFaceId()) && !whitelistRedis.inWhitelist(WhitelistRedis.FACE_AUTH, req.getUid()) ? 0 : 1);
        liveRoomInfo.setLiveAudit(liveAuditDao.getLiveAuditRecordNum(req.getUid()) == 0 ? 1 : 0);
        liveRoomInfo.setShowCameraSwitch(cameraPerAnchorRedis.isCameraPerAnchor(req.getUid()) ? 1 : 0);
        vo.setLiveRoomInfo(liveRoomInfo);
        fillRoomInfo(req, liveRoomData, liveRoomInfo);
        return vo;
    }

    private void fillRoomInfo(HttpEnvData req, MongoRoomData roomData, NewPreCreateRoomVO.Info chatRoomInfo) {
        if (null != roomData) {
            chatRoomInfo.setRoomId(roomData.getRid());
            chatRoomInfo.setRoomName(roomData.getName());
            chatRoomInfo.setRoomHead(ImageUrlGenerator.generateRoomUrl(roomData.getHead()));
            chatRoomInfo.setTagId(roomData.getTag());
            chatRoomInfo.setTagName(roomTags.getTagNameById(roomData.getTag(), req.getSlang()));
        }
    }

    /**
     * 房主创建房间
     */
    public LiveRoomVO create(LiveRoomDTO.CreateDTO req) {
        ActorData actorData = actorDao.getActorDataFromCache(req.getUid());
        if (null == actorData) {
            logger.error("cannot find actor, uid={}", req.getUid());
            throw new CommonException(HttpCode.PARAM_ERROR);
        }
        if (liveAuditDao.isLiveAuditFailedUser(req.getUid())) {
            // 审核不通过的用户无法开直播
            throw new CommonException(RoomHttpCode.NO_PERMISSION_TO_START_LIVE);
        }
        if (null != req.getMicType()) {
            if (req.getMicType() != 0 && req.getMicType() != 1) {
                throw new CommonException(HttpCode.PARAM_ERROR);
            }
            if (0 == req.getMicType() && !cameraPerAnchorRedis.isCameraPerAnchor(req.getUid())) {
                throw new CommonException(RoomHttpCode.NO_PERMISSION_TO_START_LIVE);
            }
        }
        String roomId = RoomUtils.formatLiveRoomId(req.getUid());
        // 封禁检测
        if (userMonitorDao.isLiveRoomBanned(roomId)) {
            CommonException e = new CommonException(RoomHttpCode.ROOM_BANNED);
            RoomBannedVO vo = roomNewService.getRoomBannedVO(req.getUid(), UserMonitorState.BAN_LIVE_ROOM, actorData.getRid(), req.getSlang(), actorData.getRidData());
            e.setData(vo);
            throw e;
        }
        req.setRoomId(roomId);
        MongoRoomData mongoRoomData = roomDao.findData(roomId);
        // 脏词检测
        if (!ObjectUtils.isEmpty(req.getRoomName())) {
            if (mongoRoomData != null && !mongoRoomData.getName().equals(req.getRoomName()) && freezeInfoRedis.isFrozenUpdateInfo(FreezeInfoRedis.LIVE_ROOM_NAME, roomId)) {
                throw new CommonException(RoomHttpCode.FEATURE_HAS_FROZEN);
            }
            checkDirtyWord(req.getUid(), req.getRoomName().toLowerCase());
        }
        if (!ObjectUtils.isEmpty(req.getRoomHead())) {
            String roomHeadUrl = req.getRoomHead().startsWith("http") ? CDNUtils.getHttpCdnUrl(req.getRoomHead()) :
                    ImageUrlGenerator.createCdnUrl(req.getRoomHead());
            req.setRoomHead(roomHeadUrl);
            if (mongoRoomData != null && !mongoRoomData.getHead().equals(req.getRoomHead())) {
                if (freezeInfoRedis.isFrozenUpdateInfo(FreezeInfoRedis.LIVE_ROOM_HEAD, roomId)) {
                    throw new CommonException(RoomHttpCode.HAS_SUSPENDED_THIS_FEATURE);
                }
                if (dirtyPictureRedis.isDirtyPicture(roomHeadUrl)) {
                    throw new CommonException(HttpCode.IS_DIRTY_PICTURE);
                }
            }
            checkImage(req.getUid(), roomHeadUrl);
        }
        if (null == mongoRoomData) {
            createRoom(actorData, roomId, req);
        } else {
            updateRoom(mongoRoomData, req, actorData);
        }
        String voiceRoomId = RoomUtils.formatRoomId(req.getUid());
        if (roomPlayerRedis.getRoomActorCount(voiceRoomId) > 0) {
            roomWebSender.sendRoomWebMsg(voiceRoomId, req.getUid(), new RoomChangeMsg(roomId, RoomConstant.LIVE_ROOM_MODE, 1), false);
        }
        // 调用加入房间接口
        return enterRoom(req);
    }

    public LiveRoomCloseVO close(String roomId, String uid, boolean systemClose) {
        if (!RoomUtils.isHomeowner(uid, roomId)) {
            throw new CommonException(HttpCode.AUTH_ERROR);
        }
        long liveOpenTime = liveRoomRedis.getLiveOpenTime(roomId);
        Map<String, Long> liveDataMap = liveDataRedis.getLiveDataMap(roomId);
        LiveRoomCloseVO liveRoomCloseVO = new LiveRoomCloseVO(buildLiveDataVO(liveOpenTime, liveDataMap));
        // 主播退出房间room后会触发关闭直播的逻辑
        roomWebSender.sendLeaveRoom(roomId, uid);
        if (!systemClose) {
            // 收到2262后客户端获取开播数据依赖此状态，room中增加了200ms关闭方法
            liveRoomRedis.removeLiveRoom(roomId);
        }
        doReportCloseLiveEvent(roomId, uid, liveOpenTime, liveDataMap);
        return liveRoomCloseVO;
    }

    private void sendLiveAnnouncementMsg(String roomId, String uid) {
        // 正式上线需去掉
        if (ServerConfig.isProduct()) {
            return;
        }
        TimerService.getService().addDelay(new DelayTask(5 * 1000) {
            @Override
            protected void execute() {
                RoomNotificationMsg msg = new RoomNotificationMsg();
                msg.setUid(uid);
                msg.setHide_head(1);
                msg.setText(roomConfig.getLiveAnnouncementEn());
                msg.setText_ar(roomConfig.getLiveAnnouncementAr());
                msg.setFromRoomId(roomId);
                roomWebSender.sendPlayerWebMsg(roomId, "", uid, msg, false);
            }
        });

    }

    private void doReportCloseLiveEvent(String roomId, String uid, long liveOpenTime, Map<String, Long> liveDataMap) {
        if (liveOpenTime == 0) {
            // 避免重复上报
            return;
        }
        LiveSettlementRecordEvent event = new LiveSettlementRecordEvent();
        event.setUid(uid);
        event.setRoom_id(roomId);
        event.setLive_id(liveDataRedis.getLiveId(roomId));
        event.setStart_time((int) (liveOpenTime / 1000));
        event.setEnd_time(DateHelper.getNowSeconds());
        event.setDuration(event.getEnd_time() - event.getStart_time());
        event.setContribution(liveDataMap.getOrDefault(LiveDataRedis.GIFT_TOTAL_PRICE, 0L));
        event.setSend_gift_nums(liveDataMap.getOrDefault(LiveDataRedis.GIFT_SENDER_NUM, 0L).intValue());
        event.setSend_gift_host_nums(liveDataMap.getOrDefault(LiveDataRedis.GIFT_SENDER_NUM, 0L).intValue());
        event.setSend_gift_count(liveDataMap.getOrDefault(LiveDataRedis.SEND_GIFT_COUNT, 0L).intValue());
        event.setViewers(liveDataMap.getOrDefault(LiveDataRedis.VIEWER_NUM, 0L).intValue());
        event.setNew_fans(liveDataMap.getOrDefault(LiveDataRedis.NEW_FAN_NUM, 0L).intValue());
        event.setSend_message_nums(liveDataMap.getOrDefault(LiveDataRedis.SEND_MSG_NUM, 0L).intValue());
        event.setTemperature(hotRoomRedis.getHotScore(roomId));
        event.setIncome(liveDataMap.getOrDefault(LiveDataRedis.CHARM_INCOME, 0L));
        eventReport.track(new EventDTO(event));
    }

    /**
     * 更新房间
     */
    public void updateRoom(MongoRoomData mongoRoomData, LiveRoomDTO.CreateDTO req, ActorData actorData) {
        String roomId = mongoRoomData.getRid();
        // 更新房间信息
        mongoRoomData.setOwnerRid(actorData.getRid());
        mongoRoomData.setRoomMode(2);
        mongoRoomData.setStatus(1);
        mongoRoomData.setStream_id(roomStreamService.genLiveStreamId(roomId));
        mongoRoomData.setCountry(actorData.getCountry());
        if (null != req.getTagId()) {
            mongoRoomData.setTag(roomTags.hasTag(req.getTagId()) ? req.getTagId() : 2);
        }
        if (!ObjectUtils.isEmpty(req.getRoomHead())) {
            mongoRoomData.setHead(req.getRoomHead());
        }
        if (!ObjectUtils.isEmpty(req.getRoomName())) {
            mongoRoomData.setName(req.getRoomName());
        }
        roomDao.save(mongoRoomData);
    }

    private void checkDirtyWord(String uid, String roomName) {
        if (detectService.detectText(new TextDTO(roomName, DetectOriginConstant.ROOM_NAME_RELATED, uid)).getData().getIsSafe() == 0) {
            throw new CommonException(RoomHttpCode.DIRTY_WORD_TITLE);
        }
    }

    public void checkImage(String uid, String roomHead) {
        String roomHeadUrl = roomHead.startsWith("http") ? CDNUtils.getHttpCdnUrl(roomHead) :
                ImageUrlGenerator.createCdnUrl(roomHead);
        if (detectService.detectImage(new ImageDTO(roomHeadUrl, DetectOriginConstant.LIVE_ROOM_HEAD, uid)).getData().getIsSafe() == 0) {
            throw new CommonException(RoomHttpCode.COVER_UNAVAILABLE);
        }
    }

    /**
     * 房主第一次创建房间
     */
    public void createRoom(ActorData actorData, String roomId, LiveRoomDTO.CreateDTO req) {
        logger.info("first time create room uid={}", req.getUid());
        int nowSeconds = DateHelper.getNowSeconds();
        MongoRoomData mongoRoomData = new MongoRoomData();
        mongoRoomData.setName(ObjectUtils.isEmpty(req.getRoomName()) ? actorData.getName() : req.getRoomName());
        mongoRoomData.setRid(roomId);
        mongoRoomData.setOwnerRid(actorData.getRid());
        mongoRoomData.setPrivi(3);
        mongoRoomData.setMicSize(3);
        mongoRoomData.setFee(0);
        mongoRoomData.setTextLimit(-1);
        mongoRoomData.setHead(ObjectUtils.isEmpty(req.getRoomHead()) ? actorData.getHead() : req.getRoomHead());
        mongoRoomData.setStream_id(roomStreamService.genLiveStreamId(roomId));
        mongoRoomData.setCountry(ObjectUtils.isEmpty(actorData.getCountry()) ? "AE_United Arab Emirates" : actorData.getCountry());
        mongoRoomData.setStatus(1);
        mongoRoomData.setMtime(nowSeconds);
        mongoRoomData.setCtime(nowSeconds);
        mongoRoomData.setTag(null == req.getTagId() || !roomTags.hasTag(req.getTagId()) ? 1 : req.getTagId());
        mongoRoomData.setRoomMode(2);
        roomDao.save(mongoRoomData);
    }

    /**
     * 加入房间，返回房间信息、麦位信息
     */
    public LiveRoomVO enterRoom(LiveRoomDTO.EnterDTO req) {
        if (!RoomUtils.isLiveRoom(req.getRoomId())) {
            logger.error("roomId invalid, roomId={} uid={}", req.getRoomId(), req.getUid());
            throw new CommonException(HttpCode.PARAM_ERROR);
        }
        String ownerUid;
        try {
            ownerUid = RoomUtils.getRoomHostId(req.getRoomId());
        } catch (Exception e) {
            logger.error("roomId invalid, roomId={} uid={}", req.getRoomId(), req.getUid());
            throw new CommonException(HttpCode.PARAM_ERROR);
        }
        RoomActorDetailData myData = roomActorCache.getData(req.getRoomId(), req.getUid(), false);
        RoomActorDetailData ownerData = roomActorCache.getData(req.getRoomId(), ownerUid, true, false);
        if (null == myData || null == ownerData) {
            logger.error("cannot find actor, uid={} ownerUid={}", req.getUid(), ownerUid);
            throw new CommonException(HttpCode.PARAM_ERROR);
        }
        MongoRoomData roomData = roomDao.findData(req.getRoomId());
        if (null == roomData) {
            logger.error("cannot find room roomId={} uid={}", req.getRoomId(), req.getUid());
            throw new CommonException(RoomHttpCode.ROOM_NOT_EXIST);
        }
        // 2v2PK版本控制
        if (AppVersionUtils.versionCheck(128, ownerData.getVersionCode(), ownerData.getOs())) {
            if (!AppVersionUtils.versionCheck(128, req)) {
                throw new CommonException(RoomHttpCode.UPDATE_APP);
            }
        }
        // 进入房间权限校验
        checkJoinRoomPrivilege(myData, ownerData, req);
        return getJoinRoomResp(req, roomData, myData, ownerData);
    }

    /**
     * 进入房间权限判断
     */
    private void checkJoinRoomPrivilege(RoomActorDetailData myData, RoomActorDetailData ownerData, LiveRoomDTO.EnterDTO req) {
        if (myData.getValid() != 1) {
            throw new CommonException(RoomHttpCode.USER_INVALID);
        }
        if (ownerData.getValid() != 1 || userMonitorDao.isLiveRoomBanned(req.getRoomId())) {
            throw new CommonException(RoomHttpCode.ROOM_BLOCK);
        }
        if (myData.getAid().equals(ownerData.getAid())) {
            // 房主被禁，不能创建和进入房间
            String blockTime = blockRedis.checkBlock(actorDao.getActorDataFromCache(ownerData.getAid()).getTn_id(), BlockTnConstant.BLOCK_CREATE_ROOM);
            if (!ObjectUtils.isEmpty(blockTime)) {
                throw new CommonException(RoomHttpCode.BLOCK_CREATE_ROOM, blockTime);
            }
            if (null != mysteryService.getMysteryId(req.getUid())) {
                throw new CommonException(RoomHttpCode.MYSTERY_LIVE_ROOM_LIMIT);
            }
        } else {
            if (roomBlacklistDao.isBlock(RoomUtils.getRoomId(req.getRoomId()), myData.getAid())) {
                throw new CommonException(RoomHttpCode.BLOCKED);
            }
        }
        int kickTime = roomKickRedis.getKickTime(req.getRoomId(), myData.getAid());
        if (kickTime > 0) {
            int leftTime = (kickTime + (int) TimeUnit.DAYS.toSeconds(1)) - DateHelper.getNowSeconds();
            if (leftTime > 0) {
                if (leftTime >= TimeUnit.HOURS.toSeconds(1)) {
                    throw new CommonException(RoomHttpCode.KICKED_HOURS, leftTime / TimeUnit.HOURS.toSeconds(1));
                } else {
                    throw new CommonException(RoomHttpCode.KICKED_MINUTES, leftTime / TimeUnit.MINUTES.toSeconds(1));
                }
            }
        }
    }

    /**
     * 处理进场对象
     */
    private void fillJoinCartoon(RoomActorDetailData myData, LiveRoomVO resp, String roomId) {
        EnterCartonData cartonData = enterRoomContent.fillJoinCartoon(myData.getRideOption(), myData.getAid(), 1 == myData.getMystery(), roomId);
        resp.getRoomVisitor().setSmallIcon(cartonData.getSmallIcon());
        resp.getRoomVisitor().setJoinCartoonId(cartonData.getJoinCartoonId());
        resp.getRoomVisitor().setJoinCartoonType(cartonData.getJoinCartoonType());
        resp.getRoomVisitor().setSourceType(cartonData.getSourceType());
        resp.getRoomVisitor().setSourceUrl(cartonData.getSourceUrl());
        resp.getRoomVisitor().setSourceMd5(cartonData.getSourceMd5());
        resp.getRoomVisitor().setCartoonTimes(cartonData.getCartoonTimes());
        // 处理进房通知
        resp.getRoomVisitor().setEntryEffectUrl(cartonData.getEntryEffectUrl());
        resp.getRoomVisitor().setEntryEffectUrlAr(cartonData.getEntryEffectUrlAr());
    }

    /**
     * 格式化房间贡献数值
     */
    private String formatDevotes(long devotes) {
        if (devotes >= 1000000) {
            return new BigDecimal(devotes / 1000000f + "").setScale(1, RoundingMode.HALF_UP) + "M";
        } else if (devotes >= 1000) {
            return new BigDecimal(devotes / 1000f + "").setScale(1, RoundingMode.HALF_UP) + "K";
        } else {
            return devotes + "";
        }
    }

    /**
     * 填充返回数据
     */
    private void fillRoomResp(RoomActorDetailData ownerData, RoomActorDetailData myData, MongoRoomData roomData, LiveRoomVO resp, LiveRoomDTO.EnterDTO req) {
        // 房主信息
        resp.copyFromActorData(ownerData);
        resp.getRoomOwner().setIsBeautifulRid(AppVersionUtils.versionCheck(107, req) ? ownerData.getBeautifulRid() : 0);
        resp.getRoomOwner().setUserLevel(ownerData.getActiveLevel());
        resp.getRoomOwner().setBadgeList(ownerData.getBadgeList());
        resp.getRoomOwner().setIdentify(ownerData.getIdentify());
        resp.getRoomOwner().setCountry(ownerData.getCountry());
        resp.getRoomOwner().setName(ownerData.getName());
        resp.getRoomOwner().setVip(ownerData.getVipLevel());
        resp.getRoomOwner().setHead(ownerData.getHead());
        resp.getRoomOwner().setMicFrame(ownerData.getMicFrame());
        // 用户信息
        resp.getRoomVisitor().setRidInfo(myData.getRidData());
        resp.getRoomVisitor().setUid(myData.getAid());
        resp.getRoomVisitor().setMyName(myData.getName());
        resp.getRoomVisitor().setMyHead(myData.getHead());
        resp.getRoomVisitor().setIdentify(myData.getIdentify());
        resp.getRoomVisitor().setBubbleId(myData.getBubbleId());
        resp.getRoomVisitor().setUserLevel(myData.getActiveLevel());
        resp.getRoomVisitor().setWealthLevel(myData.getWealthLevel());
        resp.getRoomVisitor().setCharmLevel(myData.getCharmLevel());
        resp.getRoomVisitor().setBadgeList(myData.getBadgeList());
        resp.getRoomVisitor().setStreamId(zegoApi.generateActorStreamId(roomData.getOwnerRid(), myData.getRid(), myData.getAid()));
        if (roomData.getPrivi() == 3) {
            resp.getRoomVisitor().setApplyCount(micApplyRedis.getMicApplyCount(req.getRoomId()));
        }
        resp.getRoomVisitor().setOfficialUser(RoomKickService.POWER_USER.contains(myData.getAid()) ? 1 : 0);
        // svip
        SvipLevelData svipLevelData = svipLevelService.getSvipLevelData(myData.getAid());
        if (null != svipLevelData) {
            resp.getRoomVisitor().setSvipLevel(svipLevelData.getLevel());
            if (svipLevelData.getConfig().getMystery() == 1) {
                resp.getRoomVisitor().setMystery(1);
                resp.getRoomVisitor().setMysteryVoice(svipLevelData.getConfig().getMysteryVoice());
            }
        }
        // 处理进场动画
        fillJoinCartoon(myData, resp, req.getRoomId());
        // 房间配置信息
        resp.copyFromMongoRoomData(roomData);
        resp.getRoomConfig().setOnline(roomPlayerRedis.getRoomActorsCount(roomData.getRid()));
        resp.getRoomConfig().setTag(roomData.getTag());
        resp.getRoomConfig().setTagName(roomTags.getTagNameById(roomData.getTag(), req.getSlang()));
        resp.getRoomConfig().setRoomHead(ImageUrlGenerator.generateRoomUserUrl(roomData.getHead(), ownerData.getSvipLevel()));
        resp.getRoomConfig().setMemberFeeList(RoomSettingsService.MEMBER_FEE_LIST);
        resp.getRoomConfig().setCloseCameraPer(RoomUtils.isHomeowner(req.getUid(), req.getRoomId()) && cameraPerAnchorRedis.isCameraPerAnchor(req.getUid()) ? 1 : 0);
        // 房间贡献度
        long devotes = commonDao.getRoomTotalDevote(roomData.getRid());
        resp.getRoomConfig().setDevotes(devotes);
        resp.getRoomConfig().setGuestLive(1);
        resp.getRoomConfig().setLivePk(roomData.getPkTime() == 0 ? 5 : roomData.getPkTime());
        resp.getRoomConfig().setDevotesStr(formatDevotes(devotes));
        // 房间会员数
        resp.getRoomConfig().setMemberCount(roomMemberDao.getMemberCount(req.getRoomId()));
        // 房间热度值
        resp.getRoomConfig().setHotScore(MatchUtils.formatDevotes(hotRoomRedis.getHotScore(req.getRoomId())));
        resp.getRoomConfig().setScreenshot(whitelistRedis.inWhitelist(WhitelistRedis.LIVE_SCREENSHOT, myData.getAid()) ? 1 : 0);
        boolean isFaceDetectWhitelist = FACE_WHITELIST_COUNTRY_SET.contains(ActorUtils.getUpperCaseCountryCode(ownerData.getCountry()))
                || whitelistRedis.inWhitelist(WhitelistRedis.LIVE_FACE_DETECTION, req.getUid());
        if (RoomUtils.isHomeowner(req.getUid(), req.getRoomId()) && isFaceDetectWhitelist) {
            resp.getRoomConfig().setFaceDetectInterval(0);
            resp.getRoomConfig().setFaceDetectNotice(0);
        } else {
            resp.getRoomConfig().setFaceDetectInterval(FACE_DETECTION);
            resp.getRoomConfig().setFaceDetectNotice(30);
        }
        // 房间最近消息
        resp.getRoomConfig().setLastRoomTextMsgList(roomLastMsgRedis.getLastRoomMsgList(req.getRoomId(), 3));
    }

    private void fillRoomDevoteRanking(String roomId, LiveRoomVO resp) {
        // 本场直播房间总贡献值
        long liveOpenTime = liveRoomRedis.getLiveOpenTime(roomId);
        if (liveOpenTime > 0) {
            resp.getRoomConfig().setRoomDevote(liveRoomDevoteDao.getRoomDevoteFromRedis(roomId, liveOpenTime));
        }
        // 房间排名
        List<Long> timeList = getRoomRankTimeByRankType(0);
        List<LiveRoomDevoteDao.DevoteRanking> ranking = liveRoomDevoteDao.getRoomDevoteRanking(timeList.get(0), timeList.get(1), 99);
        for (int i = 0; i < ranking.size(); i++) {
            LiveRoomDevoteDao.DevoteRanking devoteRanking = ranking.get(i);
            if (devoteRanking.getRoomId().equals(roomId)) {
                resp.getRoomConfig().setRoomRank(String.valueOf(i + 1));
                resp.getRoomConfig().setDiffPrev(i == 0 ? 0 : ranking.get(i - 1).getTotalDevote() - devoteRanking.getTotalDevote());
                return;
            }
        }
        resp.getRoomConfig().setRoomRank("99+");
        resp.getRoomConfig().setDiffPrev(ranking.isEmpty() ? 0 : ranking.get(ranking.size() - 1).getTotalDevote());
    }

    /**
     * 其他用户进入房间
     */
    private void otherJoinRoom(RoomActorDetailData ownerData, LiveRoomDTO.EnterDTO req, LiveRoomVO resp) {
        // 设置房间角色
        RoomMemberData managerData = roomMemberDao.findData(req.getRoomId(), req.getUid());
        // 0 房主 1 管理员 2 观众 3 会员
        int roomRoleType = roomMemberDao.getRoleContainMember(managerData, req.getRoomId(), req.getUid());
        resp.getRoomVisitor().setRole(roomRoleType);
        resp.getRoomVisitor().setAdmin(roomRoleType == RoomRoleType.HOST || roomRoleType == RoomRoleType.MANAGER ? 1 : 2);
        resp.getRoomVisitor().setIsMember(roomRoleType != RoomRoleType.AUDIENCE ? 1 : 0);
        resp.getRoomVisitor().setIsFollowHost(followDao.isFollowed(req.getUid(), ownerData.getAid()) ? 1 : 0);
        resp.setSwipeNextRoom(req.getNextRoom() == 1 ? getSwipeRoomOne(req.getUid(), req.getRoomId()) : null);
    }

    /**
     * 房主进入房间
     */
    private void ownerJoinRoom(LiveRoomDTO.EnterDTO req, LiveRoomVO resp) {
        resp.getRoomVisitor().setAdmin(1);
        resp.getRoomOwner().setName(resp.getRoomVisitor().getMyName());
        resp.getRoomOwner().setHead(resp.getRoomVisitor().getMyHead());
        // 每日任务房主进入自己的房间
        dailyTaskService.sendToMq(new DailyTaskMqData(req.getUid(), 3, DateHelper.ARABIAN.formatDateInDay2(), 1));
    }

    /**
     * 判断房间是否有密码，有密码时不返回流信息，app弹窗输入密码后再返回(pwd_check)
     */
    private boolean pwdCheck(MongoRoomData roomData, RoomActorDetailData ownerData, RoomActorDetailData myData, LiveRoomVO resp) {
        // 房间密码处理
        if (!ObjectUtils.isEmpty(roomData.getPwd()) && !ownerData.getAid().equals(myData.getAid())) {
            // 删除旧的进房记录，py回重新写入带密码的值
            roomRedis.deleteRoomJoin(myData.getAid());
            return false;
        } else {
            // 这里增加进房记录
            roomRedis.addRoomJoin(myData.getAid(), roomData.getRid());
            // 第三方数据
            String streamId = roomStreamService.genLiveStreamId(roomData.getRid());
            resp.getRoomConfig().setStreamId(streamId);
            resp.getRoomConfig().setZegoToken(zegoService.getZegoToken(myData.getAid(), streamId));
            return true;
        }
    }

    private LiveRoomVO getJoinRoomResp(LiveRoomDTO.EnterDTO req, MongoRoomData roomData, RoomActorDetailData myData, RoomActorDetailData ownerData) {
        LiveRoomVO resp = new LiveRoomVO();
        resp.getRoomVisitor().setVip(myData.getVipLevel());
        resp.getRoomVisitor().setSvipLevel(myData.getSvipLevel());
        // 基础信息填充
        fillRoomResp(ownerData, myData, roomData, resp, req);
        resp.getRoomVisitor().setRid(myData.getRid());
        resp.getRoomVisitor().setMyName(myData.getName());
        fillStreamMode(myData, resp);
        if (myData.getAid().equals(ownerData.getAid())) {
            if (roomData.getOwnerRid() != ownerData.getRid()) {
                // 同步房主最新的rid
                roomDao.updateField(req.getRoomId(), "ownerRid", ownerData.getRid());
            }
            ownerJoinRoom(req, resp);
        } else {
            otherJoinRoom(ownerData, req, resp);
        }
        if (!ObjectUtils.isEmpty(myData.getMysteryId())) {
            resp.getRoomVisitor().setRole(RoomRoleType.AUDIENCE);
            resp.getRoomVisitor().setAdmin(2);
        }
        if (pwdCheck(roomData, ownerData, myData, resp)) {
            // 进入房间后置处理
            afterJoinRoom(req, roomData, myData);
            // 最后才填充麦位信息
            fillMicData(roomData, req, resp);
        }
        // 房间排名
        fillRoomDevoteRanking(roomData.getRid(), resp);
        // 房间配置
        resp.getRoomConfig().setActorRoomConfig(actorConfigDao.findAndInitData(myData.getAid()).getLive_room_config());
        return resp;
    }

    /**
     * 1仅从CDN拉流 2仅从L3拉流 3仅从RTC拉流 4CDNPlus拉流
     * 财富等级≥20级或海湾6国IP=L3
     * 非付费观众、普通观众=CDN
     */
    private void fillStreamMode(RoomActorDetailData myData, LiveRoomVO resp) {
        int streamMode = 1;
        String countryCode = ActorUtils.getUpperCaseCountryCode(myData.getCountry());
        if (whiteTestDao.isMemberByType(myData.getAid(), WhiteTestDao.WHITE_TYPE_RID)
                || GCC_COUNTRY_SET.contains(countryCode)
                || myData.getWealthLevel() >= 20) {
            streamMode = 2;
        }
        resp.getZegoConfig().setStreamMode(streamMode);
    }

    /**
     * 填充麦位信息
     */
    private void fillMicData(MongoRoomData roomData, LiveRoomDTO.EnterDTO req, LiveRoomVO resp) {
        // 处理actor信息
        PageUtils.PageData<RoomActorDetailData> pageData = roomActorListService.roomActorDevoteList(req.getRoomId(), req.getUid(), 1, ACTOR_PAGE_SIZE);
        resp.getRoomActor().setNextPage(pageData.nextPage);
        resp.getRoomActor().setPageSize(pageData.pageSize);
        resp.getRoomActor().setTotalActors(pageData.totalSize);
        resp.getRoomActor().setRoomActorList(pageData.list);
        RoomMicListVo vo = roomMicService.autoUpRoomMic(roomData, req.getUid(), null == req.getMicType() ? 1 : req.getMicType());
        resp.setRoomMic(null == vo ? roomMicService.getRoomMicListFromRedis(req.getRoomId()) : vo);
    }

    /**
     * 进入房间后置处理
     */
    private void afterJoinRoom(LiveRoomDTO.EnterDTO dto, MongoRoomData roomData, RoomActorDetailData myData) {
        AsyncUtils.execute(() -> {
            boolean homeowner = RoomUtils.isHomeowner(myData.getAid(), roomData.getRid());
            long liveOpenTime = liveRoomRedis.getLiveOpenTime(roomData.getRid());
            if (homeowner || liveOpenTime > 0) {
                // 开播才进入房间
                roomWebSender.sendForceEnterRoom(dto.getRoomId(), dto.getUid());
                // 观看直播人数
                liveDataRedis.addWatchLiveUser(dto.getRoomId(), dto.getUid());
            }
            // 通知语聊房间的用户
            if (homeowner) {
                if (0 == liveOpenTime) {
                    // 直播数据清零
                    liveDataRedis.clearLiveData(roomData.getRid());
                    // 房主还未开播，重新开始计算开播
                    liveRoomRedis.addLiveRoom(roomData.getRid());
                }
                // 推送直播公告
                sendLiveAnnouncementMsg(roomData.getRid(), myData.getAid());
                roomRedis.setLastCreateRoom(myData.getAid(), 0);
                 FamilyMemberData memberData = familyMemberDao.selectByUidFromCache(myData.getAid());
                 int familyId = memberData != null ? memberData.getFamilyId() : 0;
                 int userType = memberData == null ? 0 : memberData.getRole() == 1 ? 2 : 1;
                 if (liveAuditDao.getLiveAuditRecordNum(myData.getAid()) == 0) {
                     // 首次开播生成一条直播视频审核记录
                     String countryCode = ActorUtils.getUpperCaseCountryCode(myData.getCountry());
                     liveAuditDao.save(new LiveAuditData(myData.getAid(), userType, familyId, DateHelper.getNowSeconds(), 1, "", countryCode));
                 }
                liveScreenshotRecordDao.save(new LiveScreenshotRecordData(myData.getAid(), userType, familyId, DateHelper.getNowSeconds(), 1));
                // 清除匹配状态
                livePkRedis.removeMatch(roomData.getRid());
                // 清除请勿打扰状态
                livePkRedis.removeDoNotDisturb(myData.getAid());
                // micApplyEvent
                micApplyEvent(dto.getRoomId(), micApplyRedis.getMicApplyList(dto.getRoomId()));
            } else {
                Long removed = micApplyRedis.removeMicApply(dto.getRoomId(), dto.getUid());
                if (null != removed && removed > 0) {
                    eventReport.track(new EventDTO(new LiveMicApplyEvent(dto.getUid(), dto.getRoomId(), liveDataRedis.getLiveId(dto.getRoomId()), 3, DateHelper.getNowSeconds())));
                }
            }
            if (dto.getSwipe() == 1) {
                recommendRedis.addSwiped(dto.getUid(), dto.getRoomId());
            }
            // 记录进入房间的时间
            recentlyRoomRedis.addEnterRoomClientTime(dto.getUid(), dto.getRequestTime());
            if (dto.getSvipOnlineScene() == 1 && myData.getSvipLevel() >= 11) {
                svipLevelService.onlineBroadcastMsg(dto.getUid());
            }
            // pk消息互通
            List<LivePkData.Player> livePkPlayer = livePkDao.getLivePkPlayer(dto.getRoomId());
            int fromTeam = ObjectUtils.isEmpty(livePkPlayer) ? 0 : livePkPlayer.stream().filter(player -> player.getAid()
                    .equals(RoomUtils.getRoomHostId(dto.getRoomId()))).findFirst().orElse(new LivePkData.Player()).getTeam();
            if (0 != fromTeam) {
                for (LivePkData.Player player : livePkPlayer) {
                    if (player.getStatus() != 0 || roomData.getRid().equals(RoomUtils.formatLiveRoomId(player.getAid()))) {
                        continue;
                    }
                    PkEnterRoomPushMsg msg = new PkEnterRoomPushMsg();
                    msg.setUname(myData.toUNameObject());
                    msg.setPkTeam(player.getTeam() == fromTeam ? 1 : 2);
                    msg.setOwnerAid(RoomUtils.getRoomHostId(dto.getRoomId()));
                    roomWebSender.sendRoomWebMsg(RoomUtils.formatLiveRoomId(player.getAid()), null, msg, false);
                }
            }
        });
    }

    private void micApplyEvent(String roomId, List<String> micApplyList) {
        if (ObjectUtils.isEmpty(micApplyList)) {
            return;
        }
        String liveId = liveDataRedis.getLiveId(roomId);
        int nowSeconds = DateHelper.getNowSeconds();
        for (String aid : micApplyList) {
            eventReport.track(new EventDTO(new LiveMicApplyEvent(aid, roomId, liveId, 4, nowSeconds)));
        }
    }

    /**
     * 获取房间用户贡献日期时间参数
     *
     * @param roomId   房间id
     * @param rankType 排行榜类型 0Live 1Daily 2Weekly 3Monthly
     * @param lastTerm 是否上一期数据
     * @return 房间贡献日期时间参数，0startStr 1endStr 2endTime
     */
    public List<String> getTimeStrByRankType(String roomId, int rankType, boolean lastTerm) {
        String startStr;
        String endStr;
        String endTime = "0";
        LocalDate today = LocalDate.now(zoneId);
        if (rankType == 0) {
            long liveOpenTime = liveRoomRedis.getLiveOpenTime(roomId);
            long now = System.currentTimeMillis();
            startStr = String.valueOf(liveOpenTime == 0 ? now : liveOpenTime);
            endStr = String.valueOf(now);
        } else if (rankType == 1) {
            if (lastTerm) {
                today = today.plusDays(-1);
            }
            startStr = today.toString();
            endStr = startStr;
            endTime = String.valueOf(today.atTime(23, 59, 59).atZone(zoneId).toEpochSecond());
        } else if (rankType == 2) {
            today = today.with(TemporalAdjusters.previousOrSame(DayOfWeek.SUNDAY));
            if (lastTerm) {
                today = today.plusWeeks(-1);
            }
            startStr = today.toString();
            LocalDate endOfWeek = today.with(TemporalAdjusters.nextOrSame(DayOfWeek.SATURDAY));
            endStr = endOfWeek.toString();
            endTime = String.valueOf(endOfWeek.atTime(23, 59, 59).atZone(zoneId).toEpochSecond());
        } else if (rankType == 3) {
            today = today.withDayOfMonth(1);
            if (lastTerm) {
                today = today.plusMonths(-1);
            }
            startStr = today.toString();
            LocalDate endOfMonth = today.with(TemporalAdjusters.lastDayOfMonth());
            endStr = endOfMonth.toString();
            endTime = String.valueOf(endOfMonth.atTime(23, 59, 59).atZone(zoneId).toEpochSecond());
        } else {
            throw new CommonException(HttpCode.PARAM_ERROR);
        }
        return List.of(startStr, endStr, endTime);
    }

    @Cacheable(value = "roomDevoteCache", cacheManager = CaffeineCacheConfig.EXPIRE_15S_AFTER_WRITE,
            key = "#root.method.name+T(org.springframework.util.StringUtils).arrayToDelimitedString(args,'-')")
    public LiveRoomActorDevoteRankingVO devoteRanking(String roomId, String uid, int rankType, int lastTerm, int slang) {
        List<String> timeStr = getTimeStrByRankType(roomId, rankType, lastTerm == 1);
        List<LiveRoomDevoteDao.DevoteRanking> devoteRanking = liveRoomDevoteDao.getActorDevoteRanking(roomId, timeStr.get(0), timeStr.get(1), 30);
        LiveRoomActorDevoteRankingVO vo = new LiveRoomActorDevoteRankingVO();
        List<LiveRoomActorDevoteRankingVO.User> list = new ArrayList<>();
        int rank = 0;
        String myRank = "30+";
        long myDevote = 0;
        for (LiveRoomDevoteDao.DevoteRanking ranking : devoteRanking) {
            rank++;
            if (uid.equals(ranking.getAid())) {
                myRank = String.valueOf(rank);
                myDevote = ranking.getTotalDevote();
            }
            list.add(getRankingUserInfo(roomId, ranking.getAid(), rank + "", ranking.getTotalDevote(), slang));
        }
        vo.setRankEndTime(Integer.parseInt(timeStr.get(2)));
        vo.setRankingList(list);
        if (myDevote == 0) {
            myDevote = liveRoomDevoteDao.getActorDevote(roomId, uid, timeStr.get(0), timeStr.get(1));
        }
        vo.setMyRank(getRankingUserInfo(roomId, uid, myRank, myDevote, slang));
        return vo;
    }

    private LiveRoomActorDevoteRankingVO.User getRankingUserInfo(String roomId, String aid, String rank, long devote, int slang) {
        LiveRoomActorDevoteRankingVO.User user = new LiveRoomActorDevoteRankingVO.User();
        user.setRank(rank);
        if (devote >= 1000000) {
            user.setDevote(new BigDecimal(devote / 1000000f + "").setScale(2, RoundingMode.HALF_UP) + "M");
        } else if (devote == 0) {
            user.setDevote(SLangType.ENGLISH == slang ? "Gift giving board" : "لوحة ارسال الهدايا");
            user.setRank("-");
        } else {
            user.setDevote(devote + "");
        }
        RoomActorDetailData detailData = roomActorCache.getData(roomId, aid, false);
        BeanUtils.copyProperties(detailData, user);
        // 同时开启神秘人，榜单隐身，优先神秘人
        if (detailData.getMystery() == 0) {
            detailData = mysteryService.getStealthActorDetail(aid);
            BeanUtils.copyProperties(detailData, user);
            user.setMystery(detailData.getMystery() == 1 ? 2 : 0);
        }
        return user;
    }

    private LiveRoomDevoteRankingVO.RoomDevote getRankingRoomInfo(String roomId, String aid, String rank, long devote) {
        LiveRoomDevoteRankingVO.RoomDevote roomDevote = new LiveRoomDevoteRankingVO.RoomDevote();
        roomDevote.setRank(devote == 0 ? "-" : rank);
        roomDevote.setDevote(devote);
        MongoRoomData roomData = StringUtils.hasLength(roomId) ? roomDao.getDataFromCache(roomId) : null;
        ActorData hostActor = StringUtils.hasLength(roomId) ? actorDao.getActorDataFromCache(RoomUtils.getRoomHostId(roomId)) : null;
        if (null != roomData) {
            roomDevote.setLive(roomData.getStatus() == 1 || roomData.getStatus() == 2 ? 1 : 0);
        }
        if (null != hostActor) {
            roomDevote.setHostAid(hostActor.getUid());
            roomDevote.setHostName(hostActor.getName());
            roomDevote.setHostHead(ImageUrlGenerator.generateRoomUserUrl(hostActor));
            roomDevote.setHostSvipLevel(hostActor.getSvipLevel());
        }
        ActorData devoteActor = mysteryService.getMysteryActor(aid);
        if (null != devoteActor) {
            // 同时开启神秘人，榜单隐身，优先神秘人
            int mystery = devoteActor.getMystery();
            if (devoteActor.getMystery() == 0) {
                devoteActor = mysteryService.getStealthActor(aid);
                mystery = devoteActor.getMystery() == 1 ? 2 : 0;
            }
            roomDevote.setAid(devoteActor.getUid());
            roomDevote.setName(devoteActor.getName());
            roomDevote.setHead(ImageUrlGenerator.generateRoomUserUrl(devoteActor));
            roomDevote.setMystery(mystery);
            roomDevote.setSvipLevel(devoteActor.getSvipLevel());
        }
        return roomDevote;
    }

    /**
     * 获取房间贡献日期时间参数
     *
     * @param rankType 排行榜类型 -1上一小时 0Live
     * @return 房间贡献日期时间参数，0startTime 1endTime
     */
    public List<Long> getRoomRankTimeByRankType(int rankType) {
        long startTime;
        long endTime;
        ZonedDateTime now = ZonedDateTime.now(zoneId);
        ZonedDateTime startOfHour = now.truncatedTo(ChronoUnit.HOURS);
        if (rankType == 0) {
            ZonedDateTime endOfHour = startOfHour.plusHours(1).minusSeconds(1);
            startTime = startOfHour.toInstant().toEpochMilli();
            endTime = endOfHour.toInstant().toEpochMilli();
        } else if (rankType == -1) {
            ZonedDateTime startOfPreviousHour = startOfHour.minusHours(1);
            ZonedDateTime endOfPreviousHour = startOfPreviousHour.plusHours(1).minusSeconds(1);
            startTime = startOfPreviousHour.toInstant().toEpochMilli();
            endTime = endOfPreviousHour.toInstant().toEpochMilli();
        } else {
            throw new CommonException(HttpCode.PARAM_ERROR);
        }
        return List.of(startTime, endTime);
    }

    @Cacheable(value = "roomDevoteCache", cacheManager = CaffeineCacheConfig.EXPIRE_15S_AFTER_WRITE,
            key = "#root.method.name+T(org.springframework.util.StringUtils).arrayToDelimitedString(args,'-')")
    public LiveRoomDevoteRankingVO roomDevoteRanking(String roomId, int rankType) {
        List<Long> timeList = getRoomRankTimeByRankType(rankType);
        List<LiveRoomDevoteDao.DevoteRanking> roomDevoteRanking = liveRoomDevoteDao.getRoomDevoteRankingWithAidNewFromCache(null, timeList.get(0), timeList.get(1), 30);
        LiveRoomDevoteRankingVO vo = new LiveRoomDevoteRankingVO();
        List<LiveRoomDevoteRankingVO.RoomDevote> list = new ArrayList<>();
        int rank = 0;
        String myRank = "30+";
        LiveRoomDevoteDao.DevoteRanking myDevote = null;
        for (LiveRoomDevoteDao.DevoteRanking ranking : roomDevoteRanking) {
            rank++;
            if (ranking.getRoomId().equals(roomId)) {
                myRank = String.valueOf(rank);
                myDevote = ranking;
            }
            list.add(getRankingRoomInfo(ranking.getRoomId(), ranking.getAid(), String.valueOf(rank), ranking.getTotalDevote()));
        }
        vo.setRankEndTime((int) (timeList.get(1) / 1000));
        vo.setRankingList(list);
        if (myDevote == null) {
            List<LiveRoomDevoteDao.DevoteRanking> myRankList = liveRoomDevoteDao.getRoomDevoteRankingWithAidNewFromCache(roomId, timeList.get(0), timeList.get(1), 1);
            if (ObjectUtils.isEmpty(myRankList)) {
                vo.setMyRank(getRankingRoomInfo(roomId, null, myRank, 0));
            } else {
                vo.setMyRank(getRankingRoomInfo(roomId, myRankList.get(0).getAid(), myRank, myRankList.get(0).getTotalDevote()));
            }
        } else {
            vo.setMyRank(getRankingRoomInfo(roomId, myDevote.getAid(), myRank, myDevote.getTotalDevote()));
        }
        return vo;
    }

    /**
     * 房间内banner图标按钮
     */
    public LiveRoomBannerVO getRoomBanner(LiveRoomDTO.BannerDTO dto) {
        ActorData actorData = actorDao.getActorDataFromCache(dto.getUid());
        if (actorData == null) {
            logger.error("can not find actor data. uid={}", dto.getUid());
            throw new CommonException(HttpCode.PARAM_ERROR);
        }
        LiveRoomBannerVO vo = new LiveRoomBannerVO();
        vo.setList(bannerListService.getRoomBanner(dto, 10, dto.getToken(), actorData));
        vo.setFunctionList(bannerListService.getRoomBanner(dto, 11, dto.getToken(), actorData));
        if (advancedGameRedis.hasAdvancedGamePer(dto.getUid())) {
            vo.setAdvancedGameList(bannerListService.getRoomBanner(dto, 12, dto.getToken(), actorData));
        }
        // 红包开关
        vo.setLuckyBoxSwitch(1);
        vo.setLuckyBox(checkLuckyBox(dto.getRoomId()));  // 进房间是否检查红包
        fillLuckyNumConfig(vo, dto.getUid(), dto.getRoomId());
        return vo;
    }

    private void fillLuckyNumConfig(LiveRoomBannerVO vo, String uid, String roomId) {
        RoomConfigData roomConfig = roomConfigDao.findData(roomId);
        Map<String, Object> config = roomConfig.getRoom_config();
        int luckyNumAdmin = (int) config.getOrDefault("luckyNumAdmin", 0);
        LiveRoomBannerVO.LuckyNumConfig luckyNumConfig = new LiveRoomBannerVO.LuckyNumConfig();
        luckyNumConfig.setLuckyNumCost(WalletUtils.diamondsForDisplay((int) config.getOrDefault("luckyNumCost", 0)));
        luckyNumConfig.setLuckyNumRange((int) config.getOrDefault("luckyNumRange", 9));
        luckyNumConfig.setLuckyNumSwitch((int) config.getOrDefault("luckyNumSwitch", 0));
        luckyNumConfig.setLuckyNumData((int) config.getOrDefault("luckyNumData", 0));
        luckyNumConfig.setLuckyNumAdmin(luckyNumAdmin);
        int hostOrAdmin = hostOrAdminByChoice(uid, roomId);
        if ((hostOrAdmin == 1) || (luckyNumAdmin == 1 && hostOrAdminByChoice(uid, roomId) > 0)) {
            luckyNumConfig.setEnableSetting(1);
        } else {
            luckyNumConfig.setEnableSetting(0);
        }
        vo.setLuckyNumV2Config(luckyNumConfig);
    }

    /**
     * 0: 普通用户 1: 【房主】、 2: 管理员
     */
    private int hostOrAdminByChoice(String uid, String roomId) {
        int hostOrAdmin;
        hostOrAdmin = RoomUtils.isHomeowner(uid, roomId) ? 1 : 0;
        if (hostOrAdmin == 0) {
            hostOrAdmin = roomAdminRedis.isRoomAdmin(roomId, uid) ? 2 : 0;
        }
        return hostOrAdmin;
    }

    /**
     * 检查房间是否有红包
     */
    private int checkLuckyBox(String roomId) {
        try {
            Long luckyBoxNum = clusterRedis.opsForHash().size("hash:luckyBoxInRoom_" + roomId);
            if (luckyBoxNum != 0) {
                return 1;
            }
        } catch (Exception e) {
            logger.error("checkLuckyBox error. roomId={} {}", roomId, e.getMessage(), e);
        }
        return 0;
    }

    public LiveDataVO getLiveData(String roomId) {
        long liveOpenTime = liveRoomRedis.getLiveOpenTime(roomId);
        Map<String, Long> liveDataMap = liveDataRedis.getLiveDataMap(roomId);
        return buildLiveDataVO(liveOpenTime, liveDataMap);
    }

    public LiveDataVO buildLiveDataVO(long liveOpenTime, Map<String, Long> liveDataMap) {
        LiveDataVO vo = new LiveDataVO();
        if (0 < liveOpenTime) {
            vo.setLiveTime((int) ((System.currentTimeMillis() - liveOpenTime) / 1000));
        }
        long charmIncome = liveDataMap.getOrDefault(LiveDataRedis.CHARM_INCOME, 0L);
        if (charmIncome < 1000) {
            vo.setCharmIncome(charmIncome + "");
        } else {
            vo.setCharmIncome(new BigDecimal(charmIncome / 1000f + "").setScale(1, RoundingMode.HALF_UP) + "K");
        }
        vo.setNewFanNum(liveDataMap.getOrDefault(LiveDataRedis.NEW_FAN_NUM, 0L).intValue());
        vo.setViewerNum(liveDataMap.getOrDefault(LiveDataRedis.VIEWER_NUM, 0L).intValue());
        vo.setGiftSenderNum(liveDataMap.getOrDefault(LiveDataRedis.GIFT_SENDER_NUM, 0L).intValue());
        return vo;
    }

    /**
     * 观众端点击直播间右上角关闭按钮增加推荐直播弹窗
     *
     * @return 弹窗共推荐10个直播间，循环显示
     */
    @Cacheable(value = "cache", cacheManager = CaffeineCacheConfig.EXPIRE_5S_AFTER_WRITE,
            key = "#root.method.name+T(org.springframework.util.StringUtils).arrayToDelimitedString(args,'-')")
    public List<QuitRoomsVO> getQuitRooms(String roomId, int versioncode) {
        List<QuitRoomsVO> quitRooms = new ArrayList<>();
        List<String> randomRooms = liveRoomRedis.randomRooms(10);
        // 关闭直播推荐弹窗：当直播间不足2个时，推荐语聊房
        // 暂时处理安卓旧版bug
        if (randomRooms.size() < 3 && versioncode >= 60) {
            randomRooms.addAll(roomRedis.randomRooms(10 - randomRooms.size()));
        }
        Set<String> whitelistRoomIds = whiteTestDao.getAllIdByTypeCache(WhiteTestDao.WHITE_TYPE_ROOM_ID);
        Map<String, Long> hotScoreMap = hotRoomRedis.getHotScoreMapFromCache();
        for (String randomRoomId : randomRooms) {
            if (randomRoomId.equals(roomId)) {
                continue;
            }
            if (whitelistRoomIds.contains(RoomUtils.getRoomId(randomRoomId))) {
                continue;
            }
            MongoRoomData roomData = roomDao.getDataFromCache(randomRoomId);
            if (null == roomData) {
                continue;
            }
            QuitRoomsVO vo = new QuitRoomsVO();
            vo.setRoomId(randomRoomId);
            vo.setHead(ImageUrlGenerator.generateRoomUrl(roomData.getHead()));
            vo.setName(roomData.getName());
            vo.setCountry(roomData.getCountry());
            vo.setHotValue(MatchUtils.formatDevotes(hotScoreMap.getOrDefault(randomRoomId, 0L)));
            quitRooms.add(vo);
        }
        return quitRooms;
    }

    /**
     * 获取推荐房间
     *
     * @return 页面显示8个推荐的房间（包括语音房）
     */
    @Cacheable(value = "cache", cacheManager = CaffeineCacheConfig.EXPIRE_5S_AFTER_WRITE,
            key = "#root.method.name+T(org.springframework.util.StringUtils).arrayToDelimitedString(args,'-')")
    public List<RecommendRoomsVO> getRecommendRooms(String roomId) {
        List<RecommendRoomsVO> quitRooms = new ArrayList<>();
        List<String> randomRooms = roomRedis.randomRooms(8);
        Set<String> whitelistRoomIds = whiteTestDao.getAllIdByTypeCache(WhiteTestDao.WHITE_TYPE_ROOM_ID);
        for (String randomRoomId : randomRooms) {
            if (randomRoomId.equals(roomId)) {
                continue;
            }
            if (whitelistRoomIds.contains(RoomUtils.getRoomId(randomRoomId))) {
                continue;
            }
            MongoRoomData roomData = roomDao.getDataFromCache(randomRoomId);
            if (null == roomData) {
                continue;
            }
            quitRooms.add(roomDataToRecommendRoom(roomData));
        }
        return quitRooms;
    }

    private RecommendRoomsVO roomDataToRecommendRoom(MongoRoomData roomData) {
        RecommendRoomsVO vo = new RecommendRoomsVO();
        vo.setRoomId(roomData.getRid());
        vo.setHead(ImageUrlGenerator.generateRoomUrl(roomData.getHead()));
        vo.setCountry(roomData.getCountry());
        vo.setName(roomData.getName());
        vo.setStreamId(zegoApi.generateActorStreamId(roomData.getOwnerRid(), roomData.getOwnerRid(), RoomUtils.getRoomHostId(roomData.getRid())));
        return vo;
    }

    public RecommendRoomsVO getSwipeRoomOne(String uid, String roomId) {
        List<String> randomRooms = liveRoomRedis.randomRoomsFromCache(1000);
        if (randomRooms.isEmpty()) {
            return null;
        }
        BloomFilter<String> bloomFilter = recommendRedis.getRecommendBloomFilter(uid);
        for (String randomRoomId : randomRooms) {
            if (RoomUtils.getRoomHostId(randomRoomId).equals(uid) || randomRoomId.equals(roomId)
                    || roomBlacklistDao.isBlock(RoomUtils.getRoomId(roomId), uid)) {
                continue;
            }
            if (whiteTestDao.isMemberByType(randomRoomId, WhiteTestDao.WHITE_TYPE_ROOM_ID)) {
                continue;
            }
            MongoRoomData roomData = roomDao.getDataFromCache(randomRoomId);
            if (null == roomData) {
                continue;
            }
            if (!bloomFilter.mightContain(randomRoomId)) {
                return roomDataToRecommendRoom(roomData);
            }
        }
        return null;
    }

    public Object liveStatusChange(LiveStatusDTO dto) {
        if (!RoomUtils.isHomeowner(dto.getUid(), dto.getRoomId()) || !RoomUtils.isLiveRoom(dto.getRoomId())) {
            throw new CommonException(RoomHttpCode.AUTH_ERROR);
        }
        // 房间状态 0未开播 1开播中 2房主离开
        Map<String, Object> updateRoomMap = new HashMap<>();
        updateRoomMap.put("status", dto.getStatus() == 1 ? 1 : 2);
        roomDao.updateRoom(dto.getRoomId(), updateRoomMap);
        roomWebSender.sendRoomWebMsg(dto.getRoomId(), dto.getUid(), new RoomChangeMsg(dto.getRoomId(), RoomConstant.LIVE_ROOM_MODE, dto.getStatus() == 1 ? 3 : 4), false);
        return null;
    }

    private void sendRoomRankChangeMsg(String roomId, String rank, int diffPrev, int type) {
        ActorData actorData = actorDao.getActorDataFromCache(RoomUtils.getRoomHostId(roomId));
        if (null == actorData) {
            return;
        }
        roomWebSender.sendRoomWebMsg(roomId, null, new RoomDevoteRankChangeMsg(actorData.getName(), rank, diffPrev, type, actorData.getUid()), false);
    }

    public void roomDevoteRankingChange() {
        Map<String, Integer> lastRankMap = liveRoomDevoteDao.getRoomDevoteRanking();
        Map<String, String> curRankMap = new LinkedHashMap<>(100);
        List<Long> timeList = getRoomRankTimeByRankType(0);
        List<LiveRoomDevoteDao.DevoteRanking> ranking = liveRoomDevoteDao.getRoomDevoteRanking(timeList.get(0), timeList.get(1), 30);
        for (int i = 0; i < ranking.size(); i++) {
            LiveRoomDevoteDao.DevoteRanking devoteRanking = ranking.get(i);
            String roomId = devoteRanking.getRoomId();
            int rank = i + 1;
            curRankMap.put(roomId, String.valueOf(rank));
            int lastRank = lastRankMap.getOrDefault(roomId, 99);
            int diffPrev = i == 0 ? 0 : (int) (ranking.get(i - 1).getTotalDevote() - devoteRanking.getTotalDevote());
            if (rank < lastRank) {
                sendRoomRankChangeMsg(roomId, String.valueOf(rank), diffPrev, 1);
            } else if (rank > lastRank) {
                sendRoomRankChangeMsg(roomId, String.valueOf(rank), diffPrev, 2);
            }
        }
        liveRoomDevoteDao.saveRoomDevoteRanking(curRankMap);
    }

    private void sendActorRankRiseMsg(String roomId, String aid, String rank) {
        ActorData actorData = mysteryService.getMysteryActor(aid);
        if (null == actorData) {
            return;
        }
        if (!ObjectUtils.isEmpty(actorData.getMysteryId()) || !ObjectUtils.isEmpty(mysteryRedis.getStealthFromCache(aid))) {
            return;
        }
        roomWebSender.sendRoomWebMsg(roomId, null, new ActorDevoteRankRiseMsg(actorData.getName(), rank, actorData.getUid()), false);
    }

    public void actorRankRiseMsg() {
        Map<String, Long> allLiveRoom = liveRoomRedis.getAllLiveRoom();
        String endTime = String.valueOf(System.currentTimeMillis());
        allLiveRoom.forEach((roomId, openTime) -> {
            List<LiveRoomDevoteDao.DevoteRanking> devoteRanking = liveRoomDevoteDao.getActorDevoteRanking(roomId, String.valueOf(openTime), endTime, 3);
            if (!devoteRanking.isEmpty()) {
                String cacheKey = "roomActorDevoteRank:" + roomId;
                Map<String, Integer> lastRankMap = CacheUtils.get(cacheKey);
                Map<String, Integer> curRankMap = new LinkedHashMap<>(3);
                for (int i = 0; i < devoteRanking.size(); i++) {
                    int rank = i + 1;
                    String aid = devoteRanking.get(i).getAid();
                    curRankMap.put(aid, rank);
                    if (!ObjectUtils.isEmpty(lastRankMap)) {
                        if (rank < lastRankMap.getOrDefault(aid, 99)) {
                            sendActorRankRiseMsg(roomId, aid, String.valueOf(rank));
                        }
                    }
                }
                CacheUtils.put(cacheKey, curRankMap);
            }
        });
    }

    public void roomDevoteRankingReward() {
        List<Long> timeList = getRoomRankTimeByRankType(-1);
        List<LiveRoomDevoteDao.DevoteRanking> ranking = liveRoomDevoteDao.getRoomDevoteRankingWithAidNew(null, timeList.get(0), timeList.get(1), 1);
        if (ranking.isEmpty()) {
            return;
        }
        LiveRoomDevoteDao.DevoteRanking devoteRanking = ranking.get(0);
        if (devoteRanking.getAid().length() < 24) {
            logger.info("skip roomDevoteRankingReward to mystery. id={}", devoteRanking.getAid());
            return;
        }
        ActorData actorData = actorDao.getActorDataFromCache(devoteRanking.getAid());
        ActorData ownerData = actorDao.getActorDataFromCache(RoomUtils.getRoomHostId(devoteRanking.getRoomId()));
        if (null == actorData || null == ownerData) {
            return;
        }
        String roomId = devoteRanking.getRoomId();
        int live = liveRoomRedis.getLiveOpenTime(roomId) > 0 ? 1 : 0;
        roomWebSender.sendAllRoomMsg(roomId, new RoomDevoteRank1Msg(roomId, ownerData.getName(), ImageUrlGenerator.generateRoomUserUrl(ownerData.getHead()), live), true);
        packService.sendPackage(actorData.getUid(), "room_devote_rank_top1", 8);
    }

    public LiveRoomSettingVO setting(LiveRoomDTO.SettingDTO req) {
        if (!ActorConfigDao.DEF_LIVE_ROOM_CONFIG.containsKey(req.getKey())) {
            throw new CommonException(HttpCode.PARAM_ERROR);
        }
        ActorConfigData actorConfigData = actorConfigDao.findAndInitData(req.getUid());
        if (null == actorConfigData.getLive_room_config()) {
            actorConfigData.setLive_room_config(new HashMap<>());
        }
        actorConfigData.getLive_room_config().put(req.getKey(), req.getValue());
        actorConfigDao.save(actorConfigData);
        return new LiveRoomSettingVO(actorConfigData.getLive_room_config());
    }

    public Object faceDetection(FaceDetection dto) {
        if (0 == FACE_DETECTION) {
            return null;
        }
        if (!RoomUtils.isHomeowner(dto.getUid(), dto.getRoomId())) {
            throw new CommonException(HttpCode.AUTH_ERROR);
        }
        if (!ObjectUtils.isEmpty(livePkRedis.getLivePkId(dto.getRoomId()))) {
            return null;
        }
        ActorData actorData = actorDao.getActorDataFromCache(dto.getUid());
        if (FACE_WHITELIST_COUNTRY_SET.contains(ActorUtils.getUpperCaseCountryCode(actorData.getCountry()))) {
            return null;
        }
        if (whitelistRedis.inWhitelist(WhitelistRedis.LIVE_FACE_DETECTION, dto.getUid())) {
            return null;
        }
        if (0 == dto.getStatus()) {
            liveRoomRedis.removeFaceDetection(dto.getRoomId());
        } else if (1 == dto.getStatus()) {
            if (liveRoomRedis.incrFaceDetectionCount(dto.getRoomId(), FACE_DETECTION) > 3 * 60) {
                logger.info("faceDetection close room. roomId={}", dto.getRoomId());
                LiveRoomForceCloseMsg msg = new LiveRoomForceCloseMsg();
                msg.setReason(actorData.getSlang() == SLangType.ENGLISH ? "No faces detected, the system has ended this live stream." : "لم يتم الكشف عن أي وجوه، وقد أنهى النظام هذا البث المباشر.");
                roomWebSender.sendPlayerWebMsg(dto.getRoomId(), "", actorData.getUid(), msg, true);
                close(dto.getRoomId(), dto.getUid(), true);
            }
        }
        return null;
    }

    public LiveDevoteRankVO liveDevoteRank(LiveDevoteRankDTO dto) {
        if (null == dto.getStartDate() || null == dto.getEndDate()) {
            throw new CommonException(HttpCode.PARAM_ERROR);
        }
        int startDate = Integer.parseInt(dto.getStartDate().replace("-", "").replace("/", ""));
        int endDate = Integer.parseInt(dto.getEndDate().replace("-", "").replace("/", ""));
        LiveDevoteRankVO vo = new LiveDevoteRankVO();
        vo.setRuleUrl("https://api.opswaho.com/live_rank_rewards/?rankType=");
        Map<String, Long> rankMap = liveDailyDevoteDao.getRankMapFromCache(startDate, endDate, dto.getType());
        int rank = 0;
        for (Map.Entry<String, Long> entry : rankMap.entrySet()) {
            rank++;
            LiveDevoteRankVO.User rankUser = getRankUser(entry.getKey(), MatchUtils.formatDevotes(entry.getValue()), String.valueOf(rank));
            vo.getRankList().add(rankUser);
            if (dto.getUid().equals(entry.getKey())) {
                vo.setMyRank(rankUser);
            }
        }
        if (null == vo.getMyRank()) {
            long myDevote = liveDailyDevoteDao.getDevoteFromCache(dto.getUid(), startDate, endDate, dto.getType());
            vo.setMyRank(getRankUser(dto.getUid(), MatchUtils.formatDevotes(myDevote), "50+"));
        }
        LocalDateTime endOfDay = LocalDateTime.of(endDate / 10000, (endDate % 10000) / 100, endDate % 100, 23, 59, 59);
        ZonedDateTime zonedDateTime = endOfDay.atZone(zoneId);
        vo.setRankEndTime(zonedDateTime.toEpochSecond());
        vo.setRole(RANK_WHITELIST.contains(dto.getUid()) ? 1 : 0);
        return vo;
    }

    private LiveDevoteRankVO.User getRankUser(String aid, String devote, String rank) {
        LiveDevoteRankVO.User user = new LiveDevoteRankVO.User();
        RoomActorDetailData data = mysteryService.getStealthActorDetail(aid);
        if (null == data) {
            return user;
        }
        user.setRank(rank);
        user.setAid(data.getAid());
        user.setHead(data.getHead());
        user.setName(data.getName());
        user.setDevote(devote);
        user.setGender(data.getGender());
        user.setVipLevel(data.getVipLevel());
        user.setSvipLevel(data.getSvipLevel());
        user.setActiveLevel(data.getActiveLevel());
        user.setCharmLevel(data.getCharmLevel());
        user.setWealthLevel(data.getWealthLevel());
        user.setBadgeList(data.getBadgeList());
        user.setMystery(data.getMystery());
        user.setAge(data.getAge());
        return user;
    }

    @Cacheable(value = "cache", cacheManager = CaffeineCacheConfig.EXPIRE_10M_AFTER_WRITE,
            key = "#root.method.name+T(org.springframework.util.StringUtils).arrayToDelimitedString(args,'-')")
    public BeautyFileDownloadVO beautyFileDownload() {
        BeautyFileDownloadVO vo = new BeautyFileDownloadVO();
        vo.setIosHairBeauty(new BeautyFileDownloadVO.FileInfo("https://cloudcdn.waho.live/resource/op_sys_1749094662_HairBeauty.zip", "739957fa4fd722c4917bb796ef14c07c"));
        vo.setIosMakeup(new BeautyFileDownloadVO.FileInfo("https://cloudcdn.waho.live/resource/op_sys_1749094662_Makeup.zip", "bfa585ca45f9c7fc8f98b01b3742756e"));
        vo.setAndroidBaseBundle(new BeautyFileDownloadVO.FileInfo("https://cloudcdn.waho.live/resource/op_sys_1749178665_effect.zip", "c13cfae4da1aecd556527dea82c2005c"));
        List<BeautyFileDownloadVO.Sticker> stickerList = new ArrayList<>();
        stickerList.add(new BeautyFileDownloadVO.Sticker("CatSparks", "https://cloudcdn.waho.live/resource/op_sys_1749095401_CatSparks.png", "https://cloudcdn.waho.live/resource/op_sys_1749106742_CatSparks.bundle", "937ce3ce3e72977051445ac0e29166a6"));
        stickerList.add(new BeautyFileDownloadVO.Sticker("xlong_zh_fu", "https://cloudcdn.waho.live/resource/op_sys_1749105482_xlong_zh_fu.png", "https://cloudcdn.waho.live/resource/op_sys_1749106743_xlong_zh_fu.bundle", "6884ca7b6b290a413e1db10d504e9946"));
        stickerList.add(new BeautyFileDownloadVO.Sticker("newy1", "https://cloudcdn.waho.live/resource/op_sys_1749102800_newy1.png", "https://cloudcdn.waho.live/resource/op_sys_1749106743_newy1.bundle", "cdf40c0988da9a06c9095a8346cff052"));
        stickerList.add(new BeautyFileDownloadVO.Sticker("redribbt", "https://cloudcdn.waho.live/resource/op_sys_1749102770_redribbt.png", "https://cloudcdn.waho.live/resource/op_sys_1749106743_redribbt.bundle", "3aba3b162389640fd22b73863374c40a"));
        stickerList.add(new BeautyFileDownloadVO.Sticker("sdlu", "https://cloudcdn.waho.live/resource/op_sys_1749106248_sdlu.png", "https://cloudcdn.waho.live/resource/op_sys_1749106743_sdlu.bundle", "ae3ab84797c9bc0a8955475ca917430c"));
        stickerList.add(new BeautyFileDownloadVO.Sticker("Heart_float", "https://cloudcdn.waho.live/resource/op_sys_1749107039_Heart_float2x.png", "https://cloudcdn.waho.live/resource/op_sys_1749106742_Heart_float.bundle", "ec4ba1902afc0cec7b5294edbf988999"));
        stickerList.add(new BeautyFileDownloadVO.Sticker("Devil_Corner", "https://cloudcdn.waho.live/resource/op_sys_1749106660_Devil_Corner2x.png", "https://cloudcdn.waho.live/resource/op_sys_1749106742_Devil_Corner.bundle", "ddc8db35d63cb40bfe0f6311a3d6353d"));
        stickerList.add(new BeautyFileDownloadVO.Sticker("Demon_horn", "https://cloudcdn.waho.live/resource/op_sys_1749106601_Demon_horn2x.png", "https://cloudcdn.waho.live/resource/op_sys_1749106742_Demon_horn.bundle", "b868506ef3c4073d9c8f735ce0beaeb1"));
        stickerList.add(new BeautyFileDownloadVO.Sticker("Sexy_beard", "https://cloudcdn.waho.live/resource/op_sys_1749107184_Sexy_beard2x.png", "https://cloudcdn.waho.live/resource/op_sys_1749106743_Sexy_beard.bundle", "873f533d2acb7e5219fed53540339668"));
        stickerList.add(new BeautyFileDownloadVO.Sticker("Pearl_hairband", "https://cloudcdn.waho.live/resource/op_sys_1749106502_Pearl_hairband2x.png", "https://cloudcdn.waho.live/resource/op_sys_1749106743_Pearl_hairband.bundle", "0030cba75294ffa420fe5d442a61d396"));
        stickerList.add(new BeautyFileDownloadVO.Sticker("Butterfly_heart", "https://cloudcdn.waho.live/resource/op_sys_1749095379_Butterfly_heart2x.png", "https://cloudcdn.waho.live/resource/op_sys_1749106742_Butterfly_heart.bundle", "86d025e9582a27ebad35228af6441952"));

        vo.setStickerList(stickerList);
        return vo;
    }
}
