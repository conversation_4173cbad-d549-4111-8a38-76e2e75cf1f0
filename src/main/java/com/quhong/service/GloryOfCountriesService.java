package com.quhong.service;

import com.quhong.constant.ActivityConstant;
import com.quhong.data.ActorData;
import com.quhong.data.SendGiftData;
import com.quhong.data.vo.*;
import com.quhong.image.ImageUrlGenerator;
import com.quhong.mongo.dao.ResourceKeyConfigDao;
import com.quhong.mongo.data.OtherRankingActivityData;
import com.quhong.mongo.data.ResourceKeyConfigData;
import jakarta.annotation.PostConstruct;
import jakarta.annotation.Resource;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * 国家荣耀比赛
 */
@Service
public class GloryOfCountriesService extends OtherActivityService implements SendGiftActivity {

    private static final Logger logger = LoggerFactory.getLogger(GloryOfCountriesService.class);

    private static final String ACTIVITY_NAME = "Glory Of Countries";
    private static final String ALL_COUNTRIES_FLAG = "allCountriesFlag";

    private static final Map<Integer, Integer> GIFT_ID_MAP = new HashMap<>(16);

    @Resource
    private OtherActivityService otherActivityService;
    @Resource
    private ResourceKeyConfigDao resourceKeyConfigDao;

    @PostConstruct
    public void init() {
        GIFT_ID_MAP.put(169, 135);
        GIFT_ID_MAP.put(170, 136);
        GIFT_ID_MAP.put(168, 134);
        GIFT_ID_MAP.put(167, 133);
        GIFT_ID_MAP.put(165, 132);
        GIFT_ID_MAP.put(137, 131);
        GIFT_ID_MAP.put(164, 130);
        GIFT_ID_MAP.put(163, 129);
        GIFT_ID_MAP.put(162, 128);
        GIFT_ID_MAP.put(161, 127);
        GIFT_ID_MAP.put(160, 126);
        GIFT_ID_MAP.put(159, 125);
        GIFT_ID_MAP.put(158, 124);
        GIFT_ID_MAP.put(157, 123);
        GIFT_ID_MAP.put(138, 122);
        GIFT_ID_MAP.put(155, 121);
        GIFT_ID_MAP.put(154, 120);
        GIFT_ID_MAP.put(156, 119);
    }

    private String getGloryOfCountriesKey(String activityId) {
        return String.format("gloryOfCountries:%s", activityId);
    }

    private String getSupportCountriesKey(String activityId, int giftId) {
        return String.format("gloryOfCountries:%s:%s", activityId, giftId);
    }

    private Map<String, ResourceKeyConfigData.ResourceMeta> getGiftIdMap() {
        ResourceKeyConfigData resourceKeyConfigData = resourceKeyConfigDao.findByKey(ALL_COUNTRIES_FLAG);
        if (resourceKeyConfigData == null || CollectionUtils.isEmpty(resourceKeyConfigData.getResourceMetaList())) {
            logger.info("not find; resourceKey={}", ALL_COUNTRIES_FLAG);
            return Collections.emptyMap();
        }
        return resourceKeyConfigData.getResourceMetaList().stream()
                .filter(item -> item.getResourceType() == -1)
                .collect(Collectors.toMap(ResourceKeyConfigData.ResourceMeta::getMetaId, Function.identity()));
    }

    public GloryOfCountriesVO gloryOfCountriesInfo(String activityId, String uid) {
        OtherRankingActivityData activityData = otherActivityService.getOtherRankingActivity(activityId);
        GloryOfCountriesVO vo = new GloryOfCountriesVO();
        vo.setStartTime(activityData.getStartTime());
        vo.setEndTime(activityData.getEndTime());

        List<OtherRankingListVO> otherRankingList = new ArrayList<>();
        String operationRoomWashRankKey = getGloryOfCountriesKey(activityId);
        Map<String, Integer> rankingMap = activityCommonRedis.getCommonRankingMap(operationRoomWashRankKey, 10);
        int rank = 1;
        Map<String, ResourceKeyConfigData.ResourceMeta> giftIdMap = getGiftIdMap();
        for (Map.Entry<String, Integer> entry : rankingMap.entrySet()) {
            String giftId = entry.getKey();
            int intGiftId = Integer.parseInt(giftId);
            if (GIFT_ID_MAP.containsKey(intGiftId)) {
                continue;
            }
            OtherRankingListVO rankingListVO = new OtherRankingListVO();
            rankingListVO.setScoreStr(entry.getValue().toString());
            rankingListVO.setRank(rank);
            ResourceKeyConfigData.ResourceMeta oneResourceMeta = giftIdMap.getOrDefault(giftId, null);
            rankingListVO.setCountryFlag(oneResourceMeta != null ? oneResourceMeta.getResourceIcon() : "");
            String supportUserKey = getSupportCountriesKey(activityId, intGiftId);
            List<String> supportUserList = activityCommonRedis.getCommonRankingList(supportUserKey, 3);

            List<OtherSupportUserVO> supportUserVOList = new ArrayList<>();
            for (String supportUid : supportUserList) {
                OtherSupportUserVO supportUserVO = new OtherSupportUserVO();
                ActorData supportActorData = actorDao.getActorDataFromCache(supportUid);
                supportUserVO.setName(supportActorData.getName());
                supportUserVO.setHead(ImageUrlGenerator.generateRoomUserUrl(supportActorData.getHead()));
                supportUserVO.setUid(supportUid);
                supportUserVOList.add(supportUserVO);
            }
            rankingListVO.setSupportUserList(supportUserVOList);
            rank += 1;
            otherRankingList.add(rankingListVO);
        }
        vo.setCountryRankingList(otherRankingList);
        OtherRankingVO sendRankVO = otherActivityService.otherRanking(uid, activityId,
                ActivityConstant.SEND_RANK, 10, 1);
        OtherMyRankVO myRankVO = sendRankVO.getMyRank();
        myRankVO.setReceiveScore(activityOtherRedis.getOtherReachingScore(activityId, uid, ActivityConstant.RECEIVE_RANK, 0, activityData.getRoundNum()));
        vo.setSendRankVO(sendRankVO);
        return vo;
    }

    @Override
    public void sendGiftHandle(String activityId, SendGiftData giftData) {
        String fromUid = giftData.getFrom_uid();
        int totalBean = giftData.getNumber() * giftData.getAid_list().size() * giftData.getPrice();
        int giftId = GIFT_ID_MAP.getOrDefault(giftData.getGid(), giftData.getGid());

        String gloryOfCountriesKey = getGloryOfCountriesKey(activityId);
        String supportCountriesKey = getSupportCountriesKey(activityId, giftId);
        activityCommonRedis.incrCommonZSetRankingScore(gloryOfCountriesKey, String.valueOf(giftId), totalBean);
        activityCommonRedis.incrCommonZSetRankingScore(supportCountriesKey, fromUid, totalBean);
    }

    @Override
    public String getActivityName() {
        return ACTIVITY_NAME;
    }
}
