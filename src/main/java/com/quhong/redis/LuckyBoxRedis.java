package com.quhong.redis;

import com.alibaba.fastjson.JSONObject;
import com.quhong.config.CaffeineCacheConfig;
import com.quhong.constant.ExpireTimeConstant;
import com.quhong.core.utils.DateHelper;
import com.quhong.data.LuckyBoxInfo;
import com.quhong.utils.CacheUtils;
import jakarta.annotation.Resource;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;
import org.springframework.util.ObjectUtils;
import org.springframework.util.StringUtils;

import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2022/12/21
 */
@Component
public class LuckyBoxRedis {

    private static final Logger logger = LoggerFactory.getLogger(LuckyBoxRedis.class);

    public static final String SEND_RECORD = "send";
    public static final String RECEIVE_RECORD = "receive";

    @Resource(name = DataRedisBean.MAIN_CLUSTER)
    private StringRedisTemplate redisTemplate;


    /**
     * 保存红包到池子
     *
     * @param poolType 1付费用户 2非付费用户 3机器人
     */
    public void addBoxMoneyToPool(String boxId, List<Integer> moneyList, int poolType) {
        if (CollectionUtils.isEmpty(moneyList)) {
            logger.info("moneyList is empty.");
            return;
        }
        String key = getBoxMoneyKey(boxId, poolType);
        try {
            List<String> strMoneyList = moneyList.stream().map(String::valueOf).collect(Collectors.toList());
            redisTemplate.opsForList().leftPushAll(key, strMoneyList);
            redisTemplate.expire(key, ExpireTimeConstant.EXPIRE_DAYS_SEVEN, TimeUnit.DAYS);
            logger.info("addBoxMoneyToPool boxId={} boxNum={} sumMoney={} moneyList={}", boxId, moneyList.size(), moneyList.stream().reduce(Integer::sum).orElse(0), Arrays.toString(moneyList.toArray()));
        } catch (Exception e) {
            logger.error("add box money to poor error. boxId={} {}", boxId, e.getMessage(), e);
        }
    }

    /**
     * 获取红包
     *
     * @param poolType 1付费用户 2非付费用户 3机器人
     */
    public int getBoxMoney(String boxId, int poolType) {
        int boxMoney;
        if (1 == poolType) {
            boxMoney = getBoxMoneyFromPool(boxId, 1);
            if (boxMoney == 0) {
                boxMoney = getBoxMoneyFromPool(boxId, 2);
            }
        } else if (3 == poolType) {
            boxMoney = getBoxMoneyFromPool(boxId, 3);
            if (boxMoney == 0) {
                boxMoney = getBoxMoneyFromPool(boxId, 2);
            }
        } else {
            boxMoney = getBoxMoneyFromPool(boxId, 2);
        }
        return boxMoney;
    }

    private int getBoxMoneyFromPool(String boxId, int poolType) {
        try {
            String value = redisTemplate.opsForList().rightPop(getBoxMoneyKey(boxId, poolType));
            return !ObjectUtils.isEmpty(value) ? Integer.parseInt(value) : 0;
        } catch (Exception e) {
            logger.error("get box money from pool error. boxId={} {}", boxId, e.getMessage(), e);
            return 0;
        }
    }

    /**
     * 获取所有未领取的红包
     */
    public List<Integer> getAllBoxMoneyAndRemove(String boxId) {
        String userPoolKey = getBoxMoneyKey(boxId, 2);
        String payingPoolKey = getBoxMoneyKey(boxId, 1);
        String robotPoolKey = getBoxMoneyKey(boxId, 3);
        try {
            List<String> list = new ArrayList<>();
            List<String> userPoolMoneyList = redisTemplate.opsForList().range(userPoolKey, 0, -1);
            // 获取后立即清除
            redisTemplate.delete(userPoolKey);
            if (!CollectionUtils.isEmpty(userPoolMoneyList)) {
                list.addAll(userPoolMoneyList);
            }
            logger.info("userPoolMoneyList={}", Arrays.toString(list.toArray()));
            List<String> payingPoolMoneyList = redisTemplate.opsForList().range(payingPoolKey, 0, -1);
            // 获取后立即清除
            redisTemplate.delete(payingPoolKey);
            if (!CollectionUtils.isEmpty(payingPoolMoneyList)) {
                list.addAll(payingPoolMoneyList);
            }
            logger.info("payingPoolMoneyList={}", Arrays.toString(list.toArray()));
            List<String> robotPoolMoneyList = redisTemplate.opsForList().range(robotPoolKey, 0, -1);
            // 获取后立即清除
            redisTemplate.delete(robotPoolKey);
            if (!CollectionUtils.isEmpty(robotPoolMoneyList)) {
                list.addAll(robotPoolMoneyList);
            }
            logger.info("robotPoolMoneyList={}", Arrays.toString(list.toArray()));
            return list.stream().map(Integer::parseInt).collect(Collectors.toList());
        } catch (Exception e) {
            logger.error("get all box money from redis error. boxId={} {}", boxId, e.getMessage(), e);
            return Collections.emptyList();
        }
    }

    /**
     * 获取指定红包数量
     */
    public int getBoxNum(String boxId, boolean all) {
        try {
            Long userPoolSize = redisTemplate.opsForList().size(getBoxMoneyKey(boxId, 2));
            int num = userPoolSize != null ? userPoolSize.intValue() : 0;
            if (all) {
                Long payingPoolKey = redisTemplate.opsForList().size(getBoxMoneyKey(boxId, 1));
                int payingPoolNum = payingPoolKey != null ? payingPoolKey.intValue() : 0;
                num = num + payingPoolNum;
            }
            return num;
        } catch (Exception e) {
            logger.error("get box num from redis error. boxId={} {}", boxId, e.getMessage(), e);
            return -1;
        }
    }

    /**
     * 保存当天红包发送或领取总金额
     */
    public void saveSendOrReceiveRecord(String uid, String recordType, int money) {
        try {
            String key = getBoxMoneyRecordKey(uid);
            redisTemplate.opsForHash().increment(key, recordType, money);
            redisTemplate.expire(key, ExpireTimeConstant.EXPIRE_DAYS_THREE, TimeUnit.DAYS);
        } catch (Exception e) {
            logger.error("saveSendOrReceiveRecord error. uid={}, recordType={}, money={} {}", uid, recordType, money, e.getMessage(), e);
        }
    }

    /**
     * 获取当天红包发送或领取总金额
     */
    @Cacheable(value = "cache", cacheManager = CaffeineCacheConfig.EXPIRE_1M_AFTER_WRITE,
            key = "#root.method.name+T(org.springframework.util.StringUtils).arrayToDelimitedString(args,'-')")
    public int getSendOrReceiveMoney(String uid, String recordType) {
        try {
            String value = (String) redisTemplate.opsForHash().get(getBoxMoneyRecordKey(uid), recordType);
            return !StringUtils.isEmpty(value) ? Integer.parseInt(value) : 0;
        } catch (Exception e) {
            logger.error("getSendOrReceiveMoney error. uid={}, recordType={} {}", uid, recordType, e.getMessage(), e);
            return 0;
        }
    }

    /**
     * 保存用户领取红包记录
     */
    public void saveGainLuckyBoxReward(String uid, String boxId) {
        try {
            String key = getGainLuckyBoxRewardKey(uid);
            redisTemplate.opsForZSet().add(key, boxId, DateHelper.getNowSeconds());
            redisTemplate.expire(key, ExpireTimeConstant.EXPIRE_DAYS_THREE, TimeUnit.DAYS);
        } catch (Exception e) {
            logger.error("saveGainLuckyBoxReward error. uid={} boxId={} {}", uid, boxId, e.getMessage(), e);
        }
    }

    /**
     * 获取用户领取红包记录
     */
    public int getUserGainLuckyBoxNum(String uid, int startTime, int endTime) {
        try {
            Long count = redisTemplate.opsForZSet().count(getGainLuckyBoxRewardKey(uid), startTime, endTime);
            return count != null ? count.intValue() : 0;
        } catch (Exception e) {
            logger.error("getUserGainLuckyBoxNum error. uid={} startTime={} endTime={} {}", uid, startTime, endTime, e.getMessage(), e);
            return 0;
        }
    }

    public void saveRoomIdByBoxId(String boxId, String roomId) {
        String key = getLuckyBoxRoomKey();
        try {
            redisTemplate.opsForHash().put(key, boxId, roomId);
            redisTemplate.expire(key, ExpireTimeConstant.EXPIRE_DAYS_THREE, TimeUnit.DAYS);
        } catch (Exception e) {
            logger.error("saveRoomIdByBoxId error. boxId={} roomId={} {}", boxId, roomId, e.getMessage(), e);
        }
    }

    /**
     * 通过boxId查找roomId
     */
    @Cacheable(value = "getRoomIdByBoxId", cacheManager = CaffeineCacheConfig.EXPIRE_1M_AFTER_WRITE, key = "#p0", sync = true)
    public String getRoomIdByBoxId(String boxId) {
        try {
            return (String) redisTemplate.opsForHash().get(getLuckyBoxRoomKey(), boxId);
        } catch (Exception e) {
            logger.error("getRoomIdByBoxId error. boxId={} {}", boxId, e.getMessage(), e);
            return "";
        }
    }

    public void removeLuckyBoxRoom(String roomId, String boxId) {
        try {
            redisTemplate.opsForHash().delete(getLuckyBoxRoomKey(), boxId, roomId);
        } catch (Exception e) {
            logger.error("removeLuckyBoxRoom error. roomId={} boxId={} {}", roomId, boxId, e.getMessage(), e);
        }
    }

    /**
     * 保存红包信息
     */
    public void saveLuckyBoxInfo(LuckyBoxInfo luckyBoxInfo) {
        String key = getRoomLuckyBoxDataKey(luckyBoxInfo.getRoom_id());
        try {
            redisTemplate.opsForHash().put(key, luckyBoxInfo.getBoxId(), JSONObject.toJSONString(luckyBoxInfo));
            redisTemplate.expire(key, ExpireTimeConstant.EXPIRE_DAYS_THREE, TimeUnit.DAYS);
        } catch (Exception e) {
            logger.error("saveLuckyBoxInfo error. roomId={} boxId={} {}", luckyBoxInfo.getRoom_id(), luckyBoxInfo.getBoxId(), e.getMessage(), e);
        }
    }

    /**
     * 获取红包信息
     */
    public LuckyBoxInfo getLuckyBoxInfo(String roomId, String boxId) {
        try {
            String value = (String) redisTemplate.opsForHash().get(getRoomLuckyBoxDataKey(roomId), boxId);
            if (StringUtils.isEmpty(value)) {
                return null;
            }
            return JSONObject.parseObject(value, LuckyBoxInfo.class);
        } catch (Exception e) {
            logger.error("getLuckyBoxInfo error. roomId={} boxId={} {}", roomId, boxId, e.getMessage(), e);
            return null;
        }
    }

    /**
     * 移除红包信息
     */
    public void removeLuckyBoxInfo(String roomId, String boxId) {
        try {
            redisTemplate.opsForHash().delete(getRoomLuckyBoxDataKey(roomId), boxId);
        } catch (Exception e) {
            logger.error("removeLuckyBoxInfo error. roomId={} boxId={} {}", roomId, boxId, e.getMessage(), e);
        }
    }

    /**
     * 获取房间内所有红包信息
     */
    public Map<String, LuckyBoxInfo> getRoomAllLuckyBoxInfo(String roomId) {
        try {
            Map<Object, Object> entries = redisTemplate.opsForHash().entries(getRoomLuckyBoxDataKey(roomId));
            if (ObjectUtils.isEmpty(entries)) {
                return Collections.emptyMap();
            }
            Map<String, LuckyBoxInfo> resultMap = new HashMap<>();
            for (Map.Entry<Object, Object> entry : entries.entrySet()) {
                resultMap.put((String) entry.getKey(), JSONObject.parseObject((String) entry.getValue(), LuckyBoxInfo.class));
            }
            return resultMap;
        } catch (Exception e) {
            logger.error("getRoomAllLuckyBoxInfo error. roomId={} {}", roomId, e.getMessage(), e);
            return Collections.emptyMap();
        }
    }

    public int incrBoxRushCount(String boxId, String uid) {
        String key = "hash:boxRushCount:" + boxId;
        Long increment = redisTemplate.opsForHash().increment(key, uid, 1);
        if (!CacheUtils.hasKey(key)) {
            redisTemplate.expire(key, 1, TimeUnit.DAYS);
            CacheUtils.put(key, 1);
        }
        return increment.intValue();
    }

    public int getBoxRushCount(String boxId, String uid) {
        String key = "hash:boxRushCount:" + boxId;
        Object value = redisTemplate.opsForHash().get(key, uid);
        return value == null ? 0 : Integer.parseInt(value.toString());
    }

    public void setBoxTimerWaiting(String boxId, int endTime) {
        try {
            redisTemplate.opsForZSet().add(getBoxTimerWaitingKey(), boxId, endTime);
        } catch (Exception e) {
            logger.error("set lucky box timer waiting error. boxId={} endTime={} {}", boxId, endTime, e.getMessage());
        }
    }

    public Set<String> getWaitingEndBoxIds(int timestamp) {
        try {
            return redisTemplate.opsForZSet().rangeByScore(getBoxTimerWaitingKey(), 0, timestamp);
        } catch (Exception e) {
            logger.error("get waiting end lucky box ids error. timestamp={} {}", timestamp, e.getMessage());
            return null;
        }
    }

    public void removeBoxTimerWaiting(String boxId) {
        try {
            redisTemplate.opsForZSet().remove(getBoxTimerWaitingKey(), boxId);
        } catch (Exception e) {
            logger.error("remove lucky box timer waiting error. boxId={} {}", boxId, e.getMessage(), e);
        }
    }

    public int incrGainLuckyBoxCount(String boxId) {
        String key = getGainLuckyBoxCountKey(boxId);
        Long increment = redisTemplate.opsForValue().increment(key, 1);
        if (null == increment || increment == 1) {
            redisTemplate.expire(key, 1, TimeUnit.DAYS);
            return 1;
        }
        return increment.intValue();
    }

    /**
     * 红包队列
     *
     * @param poolType 1付费用户 2非付费用户 3机器人
     */
    private String getBoxMoneyKey(String boxId, int poolType) {
        return "list:boxMoney:" + (1 == poolType ? "payingUsers:" : 2 == poolType ? "unpaidUsers" : "robot") + boxId;
    }

    /**
     * 当天红包领取或发送总金额记录
     */
    private String getBoxMoneyRecordKey(String uid) {
        return "hash:boxMoneyRecord_" + DateHelper.ARABIAN.formatDateInDay() + "_" + uid;
    }

    /**
     * 用户领取红包记录
     */
    private String getGainLuckyBoxRewardKey(String uid) {
        return "zset:gainLuckyBoxReward_" + uid;
    }

    /**
     * 红包映射房间 key:boxId value:roomId
     */
    private String getLuckyBoxRoomKey() {
        return "hash:luckyBoxRoom";
    }

    /**
     * 红包数据信息
     */
    private String getRoomLuckyBoxDataKey(String roomId) {
        return "hash:luckyBoxInRoom_" + roomId;
    }

    /**
     * 红包被领取记录
     */
    private String getLuckyBoxGainRecord(String boxId) {
        return "set:luckyBoxGainRecord_" + boxId;
    }

    /**
     * 红包过期时间
     */
    private String getBoxTimerWaitingKey() {
        return "zset:LuckyBoxTimerWaiting";
    }

    /**
     * 红包领取次数
     */
    private String getGainLuckyBoxCountKey(String boxId) {
        return "str:gainLuckyBoxCount:" + boxId;
    }
}
