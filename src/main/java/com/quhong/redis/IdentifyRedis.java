package com.quhong.redis;

import jakarta.annotation.Resource;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.context.annotation.Lazy;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.stereotype.Component;

@Lazy
@Component
public class IdentifyRedis {
    private static final Logger logger = LoggerFactory.getLogger(IdentifyRedis.class);

    @Resource(name = DataRedisBean.MAIN_CLUSTER)
    private StringRedisTemplate clusterTemplate;

    private String identifyKey() {
        return "uid:identify";
    }


    public void setUserIdentify(String uid, int status) {
        try {
            clusterTemplate.opsForHash().put(identifyKey(), uid, String.valueOf(status));
        } catch (Exception e) {
            logger.info("setUserIdentify error uid={}, e={}", uid, e.getMessage());
        }
    }


    private String vipExpirePushDateKey() {
        return "vip_expire_push_date";
    }

    public String getVipExpirePushDate() {
        try {
            return clusterTemplate.opsForValue().get(vipExpirePushDateKey());
        } catch (Exception e) {
            logger.info("getVipExpirePushDate error e={}", e.getMessage());
        }
        return "";
    }

    public void setVipExpirePushDate(String checkDate) {
        try {
            clusterTemplate.opsForValue().set(vipExpirePushDateKey(), checkDate);
        } catch (Exception e) {
            logger.info("setVipExpirePushDate error e={}", e.getMessage());
        }
    }

}
