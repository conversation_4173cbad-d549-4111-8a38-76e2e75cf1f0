package com.quhong.redis;

import org.springframework.stereotype.Component;

import java.util.Map;
import java.util.Set;

@Component
public class HostCompetitionRedis extends CommonRedis {

    private String getAutoDivideKey(String activityId){
        return String.format("str:hostCompetitionAutoDivide:%s", activityId);
    }

    private String getHashActivityKey(String activityId){
        return String.format("hash:hostCompetitionActivity:%s", activityId);
    }

    private String getDailyHashActivityKey(String activityId, String dateStr){
        return String.format("hash:hostCompetitionActivity:%s:%s", activityId, dateStr);
    }

    private String getHashUserDataKey(String activityId, String uid){
        return String.format("hash:hostCompetitionUserDataKey:%s:%s", activityId, uid);
    }

    private String getDailyDate(String activityId){
        return String.format("str:dailyDate:%s", activityId);
    }

    private String getDailySendRankKey(String activityId, String team, String dateStr) {
        return String.format("ZSet:dailySendRank:%s:%s:%s", activityId, team, dateStr);
    }

    private String getDailyReceiveRankKey(String activityId, String team, String dateStr) {
        return String.format("ZSet:dailyReceiveRank:%s:%s:%s", activityId, team, dateStr);
    }

    private String getDailyReceiveSupportRankKey(String activityId, String dateStr, String aid) {
        return String.format("ZSet:dailyReceiveSupportRank:%s:%s:%s", activityId, dateStr, aid);
    }

    private String getTotalReceiveRankKey(String activityId, String team) {
        return String.format("ZSet:totalReceiveRank:%s:%s", activityId, team);
    }

    private String getTotalReceiveSupportRankKey(String activityId, String aid) {
        return String.format("ZSet:totalReceiveSupportRank:%s:%s", activityId, aid);
    }

    private String getReceiveLuckySetKey(String activityId, String team) {
        return String.format("set:ReceiveLucky:%s:%s", activityId, team);
    }

    public String getCurrentDay(String activityId){
        // return DateHelper.ARABIAN.formatDateInDay();
        return getValue(getDailyDate(activityId));
    }


    public String getAutoDivideTeam(String activityId){
        return getValue(getAutoDivideKey(activityId));
    }

    public void setAutoDivideTeam(String activityId, String team){
        setValue(getAutoDivideKey(activityId), team);
    }

    // 获取用户hash数据
    public Map<String, String> getHashUserMap(String activity, String uid){
        return getHashMap(getHashUserDataKey(activity, uid));
    }

    public void setHashUserMap(String activity, String uid, String hashKey, String hashValue){
        setHashValue(getHashUserDataKey(activity, uid), hashKey, hashValue);
    }

    // 设置活动每日hash数据
    public Map<String, String> getDailyHashActivityMap(String activity, String dateStr){
        return getHashMap(getDailyHashActivityKey(activity, dateStr));
    }

    public void incrDailyHashActivityMap(String activity, String dateStr, String hashKey, int hashValue){
        incHashValue(getDailyHashActivityKey(activity, dateStr), hashKey, hashValue);
    }

    public void setDailyHashActivityMap(String activity, String dateStr, String hashKey, String hashValue){
        setHashValue(getDailyHashActivityKey(activity, dateStr), hashKey, hashValue);
    }

    // 设置活动hash数据
    public Map<String, String> getHashActivityMap(String activity){
        return getHashMap(getHashActivityKey(activity));
    }

    public void incrHashActivityMap(String activity, String hashKey, int hashValue){
        incHashValue(getHashActivityKey(activity), hashKey, hashValue);
    }



    // 增加发送榜
    public void incrDailySendRankScore(String activityId, String team, String dateStr, String uid, int score) {
        incrRankingScore(getDailySendRankKey(activityId, team, dateStr), uid, score);
    }

    public Map<String, Long> getDailySendRankMap(String activityId, String team, String dateStr, int length) {
        return getRankingMap(getDailySendRankKey(activityId, team, dateStr), length);
    }

    public long getDailySendRankScore(String activityId, String team, String dateStr, String uid) {
        return getRankingScore(getDailySendRankKey(activityId, team, dateStr), uid);
    }

    // 增加接收榜
    public void incrDailyReceiveRankScore(String activityId, String team, String dateStr, String uid, int score) {
        incrRankingScore(getDailyReceiveRankKey(activityId, team, dateStr), uid, score);
    }

    public Map<String, Long> getDailyReceiveRankMap(String activityId, String team, String dateStr, int length) {
        return getRankingMap(getDailyReceiveRankKey(activityId, team, dateStr), length);
    }

    public long getDailyReceiveRankScore(String activityId, String team, String dateStr, String uid) {
        return getRankingScore(getDailyReceiveRankKey(activityId, team, dateStr), uid);
    }

    // 接收榜支持者
    public void incrDailyReceiveSupportRankScore(String activityId, String dateStr, String aid, String fromUid, int score) {
        incrRankingScore(getDailyReceiveSupportRankKey(activityId, dateStr, aid), fromUid, score);
    }

    public Map<String, Long> getDailySupportReceiveRankMap(String activityId, String dateStr, String aid, int length) {
        return getRankingMap(getDailyReceiveSupportRankKey(activityId, dateStr, aid), length);
    }


    // 增加总接收榜
    public void incrTotalReceiveRankScore(String activityId, String team, String uid, int score) {
        incrRankingScore(getTotalReceiveRankKey(activityId, team), uid, score);
    }

    public Map<String, Long> getTotalReceiveRankMap(String activityId, String team, int length) {
        return getRankingMap(getTotalReceiveRankKey(activityId, team), length);
    }

    public long getTotalReceiveRankScore(String activityId, String team, String uid) {
        return getRankingScore(getTotalReceiveRankKey(activityId, team), uid);
    }

    // 总接支持者
    public void incrTotalReceiveSupportRankScore(String activityId, String aid, String fromUid, int score) {
        incrRankingScore(getTotalReceiveSupportRankKey(activityId, aid), fromUid, score);
    }

    public Map<String, Long> getTotalReceiveSupportRankMap(String activityId, String aid, int length) {
        return getRankingMap(getTotalReceiveSupportRankKey(activityId, aid), length);
    }

    // 幸运儿抽奖
    public Set<String> getReceiveLuckySetValues(String activityId, String team){
        return getSetValues(getReceiveLuckySetKey(activityId, team));
    }

    public void  addReceiveLuckySetValues(String activityId, String team, String aid){
        addSetValue(getReceiveLuckySetKey(activityId, team), aid);
    }


}
