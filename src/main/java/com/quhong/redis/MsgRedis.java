package com.quhong.redis;

import com.quhong.core.date.DateSupport;
import jakarta.annotation.Resource;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.stereotype.Component;

import java.util.concurrent.TimeUnit;

@Component
public class MsgRedis {

    private static final Logger logger = LoggerFactory.getLogger(MsgRedis.class);

    public static final boolean ENABLE_PAID_MSG = true; // 是否开启付费私信

    @Resource(name = DataRedisBean.MAIN_CLUSTER)
    private StringRedisTemplate redisTemplate;

    public int incPayMsg(String msgId) {
        try {
            String key = getPayMsgKey(msgId);
            Long increment = redisTemplate.opsForValue().increment(key);
            redisTemplate.expire(key, 1, TimeUnit.DAYS);
            return increment != null ? increment.intValue() : -1;
        } catch (Exception e) {
            logger.error("incPayMsg error. msgId={} {}", msgId, e.getMessage(), e);
            return -1;
        }
    }

    public int decPayMsg(String msgId) {
        try {
            String key = getPayMsgKey(msgId);
            Long increment = redisTemplate.opsForValue().decrement(key);
            int afterValue = increment != null ? increment.intValue() : -1;
            if (afterValue == 0) {
                redisTemplate.delete(key);
            }
            return afterValue;
        } catch (Exception e) {
            logger.error("decPayMsg error. msgId={} {}", msgId, e.getMessage(), e);
            return -1;
        }
    }

    private String getPayMsgKey(String msgId) {
        return "str:payMsg:" + msgId;
    }

    public int incSvipMsgCount(String uid) {
        try {
            String key = getSvipMsgCountKey(uid);
            Long increment = redisTemplate.opsForValue().increment(key);
            redisTemplate.expire(key, 31, TimeUnit.DAYS);
            return increment != null ? increment.intValue() : 100;
        } catch (Exception e) {
            logger.error("incSvipMsgCount error. uid={} {}", uid, e.getMessage(), e);
            return 100;
        }
    }

    private String getSvipMsgCountKey(String uid) {
        return "str:svipMsgCount:" + DateSupport.formatYYYY_MM(DateSupport.ARABIAN.getToday()) + ":" + uid;
    }

    public void setTipMsgRecord(String uid, String aid) {
        try {
            String key = getTipMsgRecordKey(uid);
            redisTemplate.opsForSet().add(key, aid);
            redisTemplate.expire(key, 180, TimeUnit.DAYS);
        } catch (Exception e) {
            logger.error("setTipMsgRecord error. uid={} aid={} {}", uid, aid, e.getMessage(), e);
        }
    }

    public boolean hasTipMsgRecord(String uid, String aid) {
        try {
            return Boolean.TRUE.equals(redisTemplate.opsForSet().isMember(getTipMsgRecordKey(uid), aid));
        } catch (Exception e) {
            logger.error("isTipMsgRecord error. uid={} aid={} {}", uid, aid, e.getMessage(), e);
            return true;
        }
    }

    private String getTipMsgRecordKey(String uid) {
        return "set:tipMsgRecord:" + uid;
    }
}
