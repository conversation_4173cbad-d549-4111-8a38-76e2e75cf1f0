package com.quhong.redis;

import com.quhong.core.utils.DateHelper;
import jakarta.annotation.Resource;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.data.redis.core.ZSetOperations;
import org.springframework.stereotype.Component;

import java.util.HashMap;
import java.util.Map;
import java.util.Set;
import java.util.concurrent.TimeUnit;

@Component
public class NewRookieEnterRoomRedis {
    private static final Logger logger = LoggerFactory.getLogger(NewRookieEnterRoomRedis.class);
    private static final int ALL = 1;
    private int beforeSetTime;
    private static final int EXPIRE_TIME = 10 * 24 * 60 * 60;
    private static final int EXPIRE_DAY = 7;


    @Resource(name = DataRedisBean.MAIN_CLUSTER)
    private StringRedisTemplate redisTemplate;


    public Map<String, Integer> getAllNewRookieRoom() {
        try {
            Map<String, Integer> allMap = new HashMap<>();
            Set<ZSetOperations.TypedTuple<String>> rangeWithScores = redisTemplate.opsForZSet().rangeWithScores(getKey(), 0, 100);
            if (null == rangeWithScores) {
                return allMap;
            }
            for (ZSetOperations.TypedTuple<String> rangeWithScore : rangeWithScores) {
                if (null == rangeWithScore.getValue() || null == rangeWithScore.getScore()) {
                    continue;
                }
                allMap.put(rangeWithScore.getValue(), rangeWithScore.getScore().intValue());
            }
            return allMap;
        } catch (Exception e) {
            logger.error("getAllNewRookieRoom.", e);
        }
        return new HashMap<>();
    }

    public void incEnterRoomRedis(String roomId, int num) {
        try {
            redisTemplate.opsForZSet().incrementScore(getKey(), roomId, num);
            int now = DateHelper.getNowSeconds();
            int dTime = now - beforeSetTime;
            if (dTime > EXPIRE_TIME) {
                redisTemplate.expire(getKey(), EXPIRE_DAY, TimeUnit.DAYS);
                beforeSetTime = now;
            }
        } catch (Exception e) {
            logger.error("get is party girl error.", e);
        }
    }

    private String getKey() {
        return "zset:843:rookie:enter:room";
    }

}
