package com.quhong.redis;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.quhong.data.RedisUserBetResultData;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.stereotype.Component;
import org.springframework.util.StringUtils;

import jakarta.annotation.Resource;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.stream.Collectors;

@Component
public class UserBetResultRedis {
    private final static Logger logger = LoggerFactory.getLogger(UserBetResultRedis.class);

    @Resource(name = DataRedisBean.MAIN_CLUSTER)
    private StringRedisTemplate clusterRedis;


    public RedisUserBetResultData rPopOne() {
        try {
            String json = clusterRedis.opsForList().rightPop(getKey());
            if (!StringUtils.isEmpty(json)) {
                return JSON.parseObject(json, RedisUserBetResultData.class);
            }
            return null;
        } catch (Exception e) {
            logger.error("rPopOne error msg={} ", e.getMessage(), e);
        }
        return null;
    }

    /**
     * 获取所有投注数据
     */
    public List<RedisUserBetResultData> getAllUserBetResultData() {
        try {
            List<String> members = clusterRedis.opsForList().range(getKey(), 0, -1);
            if (null == members) {
                return Collections.emptyList();
            }
            List<RedisUserBetResultData> resultList = new ArrayList<>(members.size());
            for (String json : members) {
                resultList.add(JSON.parseObject(json, RedisUserBetResultData.class));
            }
            return resultList;
        } catch (Exception e) {
            logger.error("getAllUserBetResultData error msg={}", e.getMessage(), e);
            return Collections.emptyList();
        }
    }


    public void addAllData(List<RedisUserBetResultData> dataList) {
        try {
            List<String> strList = dataList.stream().map(JSONObject::toJSONString).collect(Collectors.toList());
            clusterRedis.opsForList().leftPushAll(getKey(), strList);
        } catch (Exception e) {
            logger.error("addAllData error msg={} ", e.getMessage(), e);
        }
    }


    public boolean isExistData() {
        try {
            Long size = clusterRedis.opsForList().size(getKey());
            return size != null && size != 0;
        } catch (Exception e) {
            logger.error("rPopOne error msg={} ", e.getMessage(), e);
        }
        return false;
    }


    private String getKey() {
        return "list:fruit:play_fruit_party";
    }
}
