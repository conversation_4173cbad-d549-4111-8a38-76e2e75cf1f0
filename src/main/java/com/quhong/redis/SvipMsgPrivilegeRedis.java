package com.quhong.redis;

import com.quhong.core.utils.DateHelper;
import jakarta.annotation.Resource;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.stereotype.Component;

import java.util.concurrent.TimeUnit;


@Component
public class SvipMsgPrivilegeRedis {
    private static final Logger logger = LoggerFactory.getLogger(SvipMsgPrivilegeRedis.class);

    @Resource(name = DataRedisBean.MAIN_CLUSTER)
    private StringRedisTemplate redisTemplate;


    /**
     * SVP特权增加陌生人发私信
     */
    public int incrSendCount(String uid, String aid, int svipLevel) {
        String key = getKey(uid, svipLevel);
        Long increment = redisTemplate.opsForHash().increment(key, aid, 1);
        redisTemplate.expire(key, 30, TimeUnit.DAYS);
        return increment.intValue();
    }

    public int getSendMsgCount(String uid, String aid, int svipLevel) {
        Object object = redisTemplate.opsForHash().get(getKey(uid, svipLevel), aid);
        return object == null ? 0 : Integer.parseInt(object.toString());
    }

    public int getSendPersonCount(String uid, int svipLevel) {
        Long size = redisTemplate.opsForHash().size(getKey(uid, svipLevel));
        return Integer.parseInt(size.toString());
    }

    public boolean isMember(String uid, String aid, int svipLevel) {
        return redisTemplate.opsForHash().hasKey(getKey(uid, svipLevel), aid);
    }

    private String getKey(String uid, int svipLevel) {
        return "hash:svipMsgSend:" + DateHelper.ARABIAN.formatDateInMonth() + ":" + uid + "-" + svipLevel;
    }
}
