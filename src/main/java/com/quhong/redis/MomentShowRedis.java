package com.quhong.redis;

import com.quhong.core.utils.DateHelper;
import com.quhong.mongo.data.MomentData;
import jakarta.annotation.Resource;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.stereotype.Component;
import org.springframework.util.StringUtils;

import java.util.Collections;
import java.util.List;
import java.util.concurrent.TimeUnit;

@Component
public class MomentShowRedis {
    private static final Logger logger = LoggerFactory.getLogger(MomentShowRedis.class);

    @Resource
    private StringRedisTemplate redisTemplate;

    public void addMomentShow(MomentData momentData) {
        try {
            redisTemplate.opsForList().leftPush(getMomentShowKey(), getValue(momentData));
        } catch (Exception e) {
            logger.error("addMomentShow error, uid={} mid={}", momentData.getUid(), momentData.get_id().toString(), e);
        }
    }

    public void deleteMomentShow(MomentData momentData, String uid) {
        try {
            logger.info("deleteMomentShow mid={} uid={}", momentData.get_id().toString(), uid);
            redisTemplate.opsForList().remove(getMomentShowKey(), 1, getValue(momentData));
        } catch (Exception e) {
            logger.error("addMomentShow error, uid={} mid={}", momentData.getUid(), momentData.get_id().toString(), e);
        }
    }

    public void deleteMomentShow(String mixMid) {
        try {
            logger.info("deleteMomentShow mixMid={}", mixMid);
            redisTemplate.opsForList().remove(getMomentShowKey(), 1, mixMid);
        } catch (Exception e) {
            logger.error("addMomentShow error, mixMid={}", mixMid, e);
        }
    }

    public List<String> getMomentShow(int start, int end) {
        try {
            return redisTemplate.opsForList().range(getMomentShowKey(), start, end);
        } catch (Exception e) {
            logger.error("get moment show error, start={} end={}", start, end, e);
            return Collections.emptyList();
        }
    }

    public void addMomentSquareList(MomentData momentData) {
        String key = getMomentSquareKey();
        try {
            boolean flag = true;
            while (flag) {
                // 将5天内的最后一条数据放到第二条的位置
                String value = redisTemplate.opsForList().rightPop(key);
                if (StringUtils.hasLength(value)) {
                    String[] arrays = value.split("_");
                    if (arrays.length == 2) {
                        if (Integer.parseInt(arrays[1]) > DateHelper.getNowSeconds() - TimeUnit.DAYS.toSeconds(5)) {
                            redisTemplate.opsForList().leftPush(key, value);
                            flag = false;
                        }
                    }
                } else {
                    flag = false;
                }
            }
            redisTemplate.opsForList().leftPush(key, getValue(momentData));
        } catch (Exception e) {
            logger.error("addMomentShow error, uid={} mid={}", momentData.getUid(), momentData.get_id().toString(), e);
        }
    }

    public void removeMomentSquareList(MomentData momentData, String uid) {
        try {
            logger.info("removeMomentSquareList mid={} uid={}", momentData.get_id().toString(), uid);
            redisTemplate.opsForList().remove(getMomentSquareKey(), 1, getValue(momentData));
        } catch (Exception e) {
            logger.error("removeMomentSquareList error, uid={} mid={}", momentData.getUid(), momentData.get_id().toString(), e);
        }
    }

    public void removeMomentSquareList(String mixMid) {
        try {
            logger.info("removeMomentSquareList mixMid={}", mixMid);
            redisTemplate.opsForList().remove(getMomentSquareKey(), 1, mixMid);
        } catch (Exception e) {
            logger.error("removeMomentSquareList error, mixMid={}", mixMid, e);
        }
    }

    public List<String> getMomentSquareList(int start, int end) {
        try {
            return redisTemplate.opsForList().range(getMomentSquareKey(), start, end);
        } catch (Exception e) {
            logger.error("get moment square list error, start={} end={}", start, end, e);
            return Collections.emptyList();
        }
    }

    private String getValue(MomentData momentData) {
        return momentData.get_id().toString() + "_" + momentData.getC_time();
    }

    private String getMomentShowKey() {
        return "list:moment_id_rds";
    }

    private String getMomentSquareKey() {
        return "list:momentSquare";
    }
}
