package com.quhong.redis;

import com.alibaba.fastjson.JSONObject;
import com.quhong.constant.ExpireTimeConstant;
import com.quhong.data.GiftBoxInfo;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import jakarta.annotation.Resource;
import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2023/2/8
 */
@Component
public class GiftBoxRedis {

    private static final Logger logger = LoggerFactory.getLogger(GiftBoxRedis.class);

    @Resource(name = DataRedisBean.MAIN_CLUSTER)
    private StringRedisTemplate redisTemplate;

    /**
     * 保存礼物红包到队列
     */
    public void addGiftBox(int boxId, List<Integer> giftIdList) {
        if (CollectionUtils.isEmpty(giftIdList)) {
            return;
        }
        List<String> strGiftIdList = giftIdList.stream().map(String::valueOf).collect(Collectors.toList());
        String key = getGiftBoxKey(boxId);
        try {
            redisTemplate.opsForList().leftPushAll(key, strGiftIdList);
            redisTemplate.expire(key, ExpireTimeConstant.EXPIRE_DAYS_SEVEN, TimeUnit.DAYS);
        } catch (Exception e) {
            logger.error("add gift box in redis error. boxId={} giftIdList={} {}", boxId, Arrays.toString(giftIdList.toArray()), e.getMessage(), e);
        }
    }

    /**
     * 获取礼物红包
     */
    public int getGiftBox(int boxId) {
        try {
            String value = redisTemplate.opsForList().rightPop(getGiftBoxKey(boxId));
            return !StringUtils.isEmpty(value) ? Integer.parseInt(value) : 0;
        } catch (Exception e) {
            logger.error("get gift box from redis error. boxId={} {}", boxId, e.getMessage(), e);
            return 0;
        }
    }

    public int getBoxNum(int boxId) {
        try {
            Long size = redisTemplate.opsForList().size(getGiftBoxKey(boxId));
            return size != null ? size.intValue() : 0;
        } catch (Exception e) {
            logger.error("get box num from redis error. boxId={} {}", boxId, e.getMessage(), e);
            return 0;
        }
    }

    /**
     * 获取所有未领取的礼物红包
     */
    public List<Integer> getAllBoxGiftAndRemove(int boxId) {
        String key = getGiftBoxKey(boxId);
        try {
            List<String> values = redisTemplate.opsForList().range(key, 0, -1);
            // 获取后立即清除
            redisTemplate.delete(key);
            if (CollectionUtils.isEmpty(values)) {
                return Collections.emptyList();
            }
            return values.stream().map(Integer::parseInt).collect(Collectors.toList());
        } catch (Exception e) {
            logger.error("get all box gift from redis error. boxId={} {}", boxId, e.getMessage(), e);
            return Collections.emptyList();
        }
    }

    public void setBoxTimerWaiting(int boxId, int endTime) {
        try {
            redisTemplate.opsForZSet().add(getBoxTimerWaitingKey(), String.valueOf(boxId), endTime);
        } catch (Exception e) {
            logger.error("set gift box timer waiting error. boxId={} endTime={} {}", boxId, endTime, e.getMessage(), e);
        }
    }

    /**
     * 保存红包信息
     */
    public void saveGiftBoxInfo(GiftBoxInfo luckyBoxInfo) {
        String key = getGiftBoxInfoKey(luckyBoxInfo.getRoomId());
        try {
            redisTemplate.opsForHash().put(key, luckyBoxInfo.getBoxId() + "", JSONObject.toJSONString(luckyBoxInfo));
            redisTemplate.expire(key, ExpireTimeConstant.EXPIRE_DAYS_THREE, TimeUnit.DAYS);
        } catch (Exception e) {
            logger.error("saveGiftBoxInfo error. roomId={} boxId={} {}", luckyBoxInfo.getRoomId(), luckyBoxInfo.getBoxId(), e.getMessage(), e);
        }
    }

    /**
     * 获取红包信息
     */
    public GiftBoxInfo getGiftBoxInfo(String roomId, int boxId) {
        try {
            String value = (String) redisTemplate.opsForHash().get(getGiftBoxInfoKey(roomId), String.valueOf(boxId));
            if (StringUtils.isEmpty(value)) {
                return null;
            }
            return JSONObject.parseObject(value, GiftBoxInfo.class);
        } catch (Exception e) {
            logger.error("getGiftBoxInfo error. roomId={} boxId={} {}", roomId, boxId, e.getMessage(), e);
            return null;
        }
    }

    /**
     * 移除红包信息
     */
    public void removeGiftBoxInfo(String roomId, int boxId) {
        try {
            redisTemplate.opsForHash().delete(getGiftBoxInfoKey(roomId), String.valueOf(boxId));
        } catch (Exception e) {
            logger.error("removeGiftBoxInfo error. roomId={} boxId={} {}", roomId, boxId, e.getMessage(), e);
        }
    }

    /**
     * 获取房间内所有红包信息
     */
    public Map<String, GiftBoxInfo> getRoomAllGiftBoxInfo(String roomId) {
        Map<String, GiftBoxInfo> resultMap = new HashMap<>();
        try {
            Map<Object, Object> entries = redisTemplate.opsForHash().entries(getGiftBoxInfoKey(roomId));
            for (Map.Entry<Object, Object> entry : entries.entrySet()) {
                resultMap.put((String) entry.getKey() , JSONObject.parseObject((String)entry.getValue(), GiftBoxInfo.class));
            }
            return resultMap;
        } catch (Exception e) {
            logger.error("getRoomAllGiftBoxInfo error. roomId={} {}", roomId, e.getMessage(), e);
            return resultMap;
        }
    }

    public void saveRoomGiftBox(String roomId, int boxId) {
        String key = getRoomGiftBoxKey(roomId);
        try {
            redisTemplate.opsForSet().add(key, String.valueOf(boxId));
            redisTemplate.expire(key, ExpireTimeConstant.EXPIRE_DAYS_THREE, TimeUnit.DAYS);
        } catch (Exception e) {
            logger.error("saveRoomGiftBox error. roomId={} boxId={} {}", roomId, boxId, e.getMessage(), e);
        }
    }

    public void removeRoomGiftBox(String roomId, int boxId) {
        try {
            redisTemplate.opsForSet().remove(getRoomGiftBoxKey(roomId), String.valueOf(boxId));
        } catch (Exception e) {
            logger.error("removeRoomGiftBox error. roomId={} boxId={} {}", roomId, boxId, e.getMessage(), e);
        }
    }

    /**
     * 保存红包领取记录
     */
    public void saveLuckyBoxGainRecord(int boxId, String uid) {
        String key = getGiftBoxGainRecord(boxId);
        try {
            redisTemplate.opsForSet().add(key, uid);
            redisTemplate.expire(key, ExpireTimeConstant.EXPIRE_DAYS_SEVEN, TimeUnit.DAYS);
        } catch (Exception e) {
            logger.error("saveLuckyBoxGainRecord error. boxId={} uid={} {}", boxId, uid, e.getMessage());
        }
    }


    public boolean hasGainedGiftBox(int boxId, String uid) {
        try {
            return Boolean.TRUE.equals(redisTemplate.opsForSet().isMember(getGiftBoxGainRecord(boxId), uid));
        } catch (Exception e) {
            logger.error("check hasGainedGiftBox error. boxId={} uid={} {}", boxId, uid, e.getMessage(), e);
            return false;
        }
    }

    public Set<String> getWaitingEndBoxIds(int timestamp) {
        try {
            return redisTemplate.opsForZSet().rangeByScore(getBoxTimerWaitingKey(), 0, timestamp);
        } catch (Exception e) {
            logger.error("get waiting end gift box ids error. timestamp={} {}", timestamp, e.getMessage(), e);
            return null;
        }
    }

    public void removeBoxTimerWaiting(int boxId) {
        try {
            redisTemplate.opsForZSet().remove(getBoxTimerWaitingKey(), String.valueOf(boxId));
        } catch (Exception e) {
            logger.error("remove gift box timer waiting error. boxId={} {}", boxId, e.getMessage(), e);
        }
    }

    /**
     * 礼物红包队列
     */
    private String getGiftBoxKey(int boxId) {
        return "list:giftBox" + boxId;
    }

    /**
     * 房间礼物红包
     */
    private String getRoomGiftBoxKey(String roomId) {
        return "set:roomGiftBox_" + roomId;
    }

    /**
     * 礼物红包数据信息
     */
    private String getGiftBoxInfoKey(String roomId) {
        return "hash:giftBoxInfo_" + roomId;
    }

    /**
     * 礼物红包过期时间
     */
    private String getBoxTimerWaitingKey() {
        return "zset:giftBoxTimerWaiting";
    }

    /**
     * 红包被领取记录
     */
    private String getGiftBoxGainRecord(int boxId) {
        return "set:giftBoxGainRecord_" + boxId;
    }
}
