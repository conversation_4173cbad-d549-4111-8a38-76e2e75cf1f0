package com.quhong.redis;

import com.quhong.core.utils.DateHelper;
import jakarta.annotation.Resource;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.stereotype.Component;
import org.springframework.util.StringUtils;

import java.util.concurrent.TimeUnit;

/**
 * <AUTHOR>
 * @date 2024/4/12
 */
@Component
public class AnchorTaskRedis {

    private final Logger logger = LoggerFactory.getLogger(getClass());

    @Resource(name = DataRedisBean.MAIN_CLUSTER)
    private StringRedisTemplate clusterTemplate;

    private String expireDate;
    private String expireDate1;
    private String expireDate2;
    private String expireDate3;

    public void incGainRewardNum(String uid) {
        String key = getGainRewardNumKey();
        String dateStr = DateHelper.ARABIAN.formatDateInDay();
        clusterTemplate.opsForHash().increment(key, uid, 1);
        if (!dateStr.equals(expireDate)) {
            clusterTemplate.expire(key, 3, TimeUnit.DAYS);
            expireDate = dateStr;
        }
    }

    public int getGainRewardNum(String uid) {
        String key = getGainRewardNumKey();
        try {
            String value = (String)clusterTemplate.opsForHash().get(key, uid);
            return StringUtils.hasLength(value) ? Integer.parseInt(value) : 0;
        } catch (Exception e) {
            logger.error("getGainRewardNum error. uid={} {}", uid, e.getMessage(), e);
            return 100;
        }
    }

    public int incAnchorUpMicTime(String uid, int upMicMinutes) {
        try {
            String key = getAnchorUpMicTimeKey();
            String dateStr = DateHelper.ARABIAN.formatDateInDay();
            Long increment = clusterTemplate.opsForHash().increment(key, uid, upMicMinutes);
            if (!dateStr.equals(expireDate1)) {
                clusterTemplate.expire(key, 3, TimeUnit.DAYS);
                expireDate1 = dateStr;
            }
            return increment.intValue();
        } catch (Exception e) {
            logger.error("incAnchorUpMicTime error. uid={} upMicMinutes={} {}", uid, upMicMinutes, e.getMessage(), e);
            return 0;
        }
    }

    public void incGainLiveRewardNum(String uid) {
        String key = getGainLiveRewardNumKey();
        String dateStr = DateHelper.ARABIAN.formatDateInDay();
        clusterTemplate.opsForHash().increment(key, uid, 1);
        if (!dateStr.equals(expireDate2)) {
            clusterTemplate.expire(key, 3, TimeUnit.DAYS);
            expireDate2 = dateStr;
        }
    }

    public int getGainLiveRewardNum(String uid) {
        String key = getGainLiveRewardNumKey();
        try {
            String value = (String)clusterTemplate.opsForHash().get(key, uid);
            return StringUtils.hasLength(value) ? Integer.parseInt(value) : 0;
        } catch (Exception e) {
            logger.error("getGainLiveRewardNum error. uid={} {}", uid, e.getMessage(), e);
            return 100;
        }
    }

    public int incAnchorLiveTime(String uid, int liveMinutes) {
        try {
            String key = getAnchorLiveTimeKey();
            String dateStr = DateHelper.ARABIAN.formatDateInDay();
            Long increment = clusterTemplate.opsForHash().increment(key, uid, liveMinutes);
            if (!dateStr.equals(expireDate3)) {
                clusterTemplate.expire(key, 3, TimeUnit.DAYS);
                expireDate3 = dateStr;
            }
            return increment.intValue();
        } catch (Exception e) {
            logger.error("incAnchorLiveTime error. uid={} liveMinutes={} {}", uid, liveMinutes, e.getMessage(), e);
            return 0;
        }
    }

    private String getGainRewardNumKey() {
        return "hash:anchorTaskGainRewardNum_" + DateHelper.ARABIAN.formatDateInDay();
    }

    private String getAnchorUpMicTimeKey() {
        return "hash:anchorUpMicTime_" + DateHelper.ARABIAN.formatDateInDay();
    }

    private String getGainLiveRewardNumKey() {
        return "hash:anchorLiveTaskGainRewardNum_" + DateHelper.ARABIAN.formatDateInDay();
    }

    private String getAnchorLiveTimeKey() {
        return "hash:anchorLiveTime_" + DateHelper.ARABIAN.formatDateInDay();
    }
}
