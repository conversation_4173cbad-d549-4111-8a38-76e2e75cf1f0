package com.quhong.redis;

import jakarta.annotation.Resource;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.stereotype.Component;

@Component
public class RoomDevoteRedis {
    private static final Logger logger = LoggerFactory.getLogger(RoomDevoteRedis.class);

    @Resource
    private StringRedisTemplate redisTemplate;

    public void incrRoomMemberDevote(String roomId, String uid, int devote) {
        try {
            redisTemplate.opsForHash().increment(getMemberDevoteKey(), getMemberDevoteHashKey(roomId, uid), devote);
        } catch (Exception e) {
            logger.error("incr room member devote error. roomId={} uid={} {}", roomId, uid, e.getMessage(), e);
        }
    }

    private String getMemberDevoteKey() {
        return "room_member_cost";
    }

    private String getMemberDevoteHashKey(String roomId, String uid) {
        return roomId + "_" + uid;
    }

    public void incrRoomBeansCost(String roomId, String uid, int devote) {
        try {
            redisTemplate.opsForHash().increment(getBeansCostKey(), getBeansCostHashKey(roomId, uid), devote);
        } catch (Exception e) {
            logger.error("incr room beans cost error. roomId={} uid={} {}", roomId, uid, devote, e);
        }
    }

    private String getBeansCostKey() {
        return "room_beans_cost";
    }

    private String getBeansCostHashKey(String roomId, String uid) {
        return roomId + "_" + uid;
    }

    public void incrRoomBeansDevote(String roomId, String uid, int devote) {
        try {
            incrRoomBeansDevoteTotal(roomId, devote);
            incrRoomBeansDevoteOne(roomId, uid, devote);
        } catch (Exception e) {
            logger.error("incr room beans devote error. roomId={} uid={} devote={}", roomId, uid, devote, e);
        }
    }

    public void incrRoomBeansDevoteTotal(String roomId, int devote) {
        try {
            redisTemplate.opsForHash().increment("room_beans_devote_total", roomId, devote);
        } catch (Exception e) {
            logger.error("incr room beans devote total error. roomId={} uid={}", roomId, devote, e);
        }
    }

    public void incrRoomBeansDevoteOne(String roomId, String uid, int devote) {
        try {
            redisTemplate.opsForHash().increment("room_beans_devote_one", getRoomBeansOneHashKey(roomId, uid), devote);
        } catch (Exception e) {
            logger.error("incr room beans devote one error. roomId={} uid={} devote={}", roomId, uid, devote, e);
        }
    }

    private String getRoomBeansOneHashKey(String roomId, String uid) {
        return roomId + "_" + uid;
    }
}
