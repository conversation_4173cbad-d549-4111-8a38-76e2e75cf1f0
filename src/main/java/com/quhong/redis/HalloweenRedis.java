package com.quhong.redis;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2024/10/25
 */
@Component
public class HalloweenRedis extends CommonRedis{

    private static final Logger logger = LoggerFactory.getLogger(HalloweenRedis.class);

    public long getRankingScore(String activityId, int rankType, String aid) {
        return getRankingScore(getRankingKey(activityId, rankType), aid);
    }

    public int getRankingRank(String activityId, int rankType, String aid) {
        return getRankingRank(getRankingKey(activityId, rankType), aid);
    }

    public void incrRankingScore(String activityId, int rankType, String aid, int score) {
        incrRankingScore(getRankingKey(activityId, rankType), aid, score);
    }

    public Map<String, Long> getRankingMap(String activityId, int rankType, int length) {
        return getRankingMap(getRankingKey(activityId, rankType), length);
    }

    private String getRankingKey(String activityId, int rankType) {
        return "zset:halloweenRanking_" + activityId + "_" + rankType;
    }


    public void setUserData(String activityId, String uid, String hashKey, String hashValue) {
        setHashValue(getUserDataKey(activityId, uid), hashKey, hashValue);
    }

    public void setUserData(String activityId, String uid, String hashKey, int hashValue) {
        setHashValue(getUserDataKey(activityId, uid), hashKey, String.valueOf(hashValue));
    }

    public void incUserData(String activityId, String uid, String hashKey, int incValue) {
        incHashValue(getUserDataKey(activityId, uid), hashKey, incValue);
    }

    public Map<String, Long> getUserData(String activityId, String uid) {
        Map<String, String> hashMap = getHashMap(getUserDataKey(activityId, uid));
        return hashMap.entrySet().stream().collect(Collectors.toMap(Map.Entry::getKey, entry -> Long.parseLong(entry.getValue())));
    }

    public long getUserData(String activityId, String uid, String hashKey) {
        return getHashIntValue(getUserDataKey(activityId, uid), hashKey);
    }

    private String getUserDataKey(String activityId, String uid) {
        return "hash:userData_" + activityId + "_" + uid;
    }

    public int getPoolSize(String activityId, String poolName) {
        return getListSize(getDrawPoolKey(activityId, poolName));
    }

    public void pushRewardInPool(String activityId, String poolName, List<String> rewardList) {
        listRightPushAll(getDrawPoolKey(activityId, poolName), rewardList);
    }

    public List<String> popRewardFromPool(String activityId, String poolName, int num) {
        return listLeftPop(getDrawPoolKey(activityId, poolName), num);
    }

    private String getDrawPoolKey(String activityId, String poolName) {
        return "list:drawPool_" + activityId + "_" + poolName;
    }

    public void addRewardNotify(String activityId, String jsonRecord) {
        String key = getRewardNotifyKey(activityId);
        int listSize = listLeftPush(key, jsonRecord);
        if (listSize > 100) {
            listTrim(key, 0, 10);
        }
    }

    public List<String> getRewardNotifyList(String activityId) {
        return listRange(getRewardNotifyKey(activityId), 0, 9);
    }

    private String getRewardNotifyKey(String activityId) {
        return "list:drawRewardNotify_" + activityId;
    }

    public void saveDrawRecord(String activityId, String uid, String json) {
        listLeftPush(getDrawRecordKey(activityId, uid), json);
    }

    public List<String> getDrawRecordList(String activityId, String uid, int start, int end) {
        return listRange(getDrawRecordKey(activityId, uid), start, end);
    }

    private String getDrawRecordKey(String activityId, String uid) {
        return "list:drawRecord_" + activityId + "_" + uid;
    }
}
