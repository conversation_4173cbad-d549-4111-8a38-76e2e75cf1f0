package com.quhong.redis;

import com.quhong.constant.ExpireTimeConstant;
import com.quhong.core.utils.DateHelper;
import jakarta.annotation.Resource;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.context.annotation.Lazy;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.data.redis.core.ZSetOperations;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import java.math.BigDecimal;
import java.util.*;
import java.util.concurrent.TimeUnit;

@Lazy
@Component
public class WorldTravelRedis {

    private static final Logger logger = LoggerFactory.getLogger(WorldTravelRedis.class);

    private static final int COMMON_EXPIRE_DAYS = 30;

    private String rankingExpireDate = "";

    @Resource(name = DataRedisBean.MAIN_CLUSTER)
    private StringRedisTemplate clusterTemplate;

    public int addLikeRecord(String activityId, String uid, String worldKey) {
        try {
            String key = getLikeRecordKey(activityId, worldKey);
            Long add = clusterTemplate.opsForSet().add(key, uid);
            clusterTemplate.expire(key, COMMON_EXPIRE_DAYS, TimeUnit.DAYS);
            return add != null ? add.intValue() : 0;
        } catch (Exception e) {
            logger.error("addLikeRecord error. activityId={} uid={} worldKey e={}", activityId, uid, worldKey, e);
            return 0;
        }
    }

    public boolean hasLikeRecord(String activityId, String uid, String worldKey) {
        try {
            return Boolean.TRUE.equals(clusterTemplate.opsForSet().isMember(getLikeRecordKey(activityId, worldKey), uid));
        } catch (Exception e) {
            logger.error("hasLikeRecord error. activityId={} uid={} worldKey e={}", activityId, uid, worldKey, e);
            return false;
        }
    }

    public int getLikeNum(String activityId, String worldKey) {
        try {
            Set<String> members = clusterTemplate.opsForSet().members(getLikeRecordKey(activityId, worldKey));
            return CollectionUtils.isEmpty(members) ? 0 : members.size();
        } catch (Exception e) {
            logger.error("getLikeNum error. activityId={} worldKey e={}", activityId, worldKey, e);
            return 0;
        }
    }

    public String getLikeRecordKey(String activityId, String worldKey) {
        return "set:likeReward_" + activityId + "_" + worldKey;
    }

    private String getRankingKey(String activityId) {
        return "zset:punchCardRank_" + activityId + "_" + DateHelper.ARABIAN.formatDateInDay();
    }

    private String getRankingKey(String activityId, String strDate) {
        return "zset:punchCardRank_" + activityId + "_" + strDate;
    }

    /**
     * 获取分数
     */
    public int getRankingScore(String activityId, String uid) {
        try{
            Double score = clusterTemplate.opsForZSet().score(getRankingKey(activityId), uid);
            return score != null ? score.intValue() : 0;
        } catch (Exception e) {
            logger.error("getRankingScore error uid={}", uid, e);
            return 0;
        }
    }

    public int getRankingScore(String activityId, String strDate, String uid) {
        try{
            Double score = clusterTemplate.opsForZSet().score(getRankingKey(activityId, strDate), uid);
            return score != null ? score.intValue() : 0;
        } catch (Exception e) {
            logger.error("getRankingScore error uid={} strDate={}", uid, strDate, e);
            return 0;
        }
    }

    public void updateRankingScore(String activityId, String uid, int score) {
        try {
            String key = getRankingKey(activityId);
            double rankScore = new BigDecimal(score + "." + (Integer.MAX_VALUE - DateHelper.getNowSeconds())).doubleValue();
            Double oldScore = clusterTemplate.opsForZSet().score(key, uid);
            if (oldScore != null && oldScore >= rankScore) {
                return;
            }
            logger.info("updateRankingScore aid={} score={} total={}", uid, score, rankScore);
            clusterTemplate.opsForZSet().add(key, uid, rankScore);
            String dateStr = DateHelper.ARABIAN.formatDateInDay();
            if (!dateStr.equals(rankingExpireDate)) {
                clusterTemplate.expire(key, COMMON_EXPIRE_DAYS, TimeUnit.DAYS);
                rankingExpireDate = dateStr;
            }
        } catch (Exception e) {
            logger.error("updateRankingScore error aid={} giftId={} score={}", uid, score, e);
        }
    }

    /**
     * 获取排行榜
     */
    public List<String> getRankingList(String activityId, int length) {
        List<String> rankingList = new ArrayList<>();
        String key = getRankingKey(activityId);
        Set<ZSetOperations.TypedTuple<String>> rangeWithScores = clusterTemplate.opsForZSet().reverseRangeWithScores(key, 0, length - 1);
        if (null == rangeWithScores) {
            return rankingList;
        }
        for (ZSetOperations.TypedTuple<String> rangeWithScore : rangeWithScores) {
            if (null == rangeWithScore.getValue() || null == rangeWithScore.getScore()) {
                continue;
            }
            rankingList.add(rangeWithScore.getValue());
        }
        return rankingList;
    }

    /**
     * 获取带分数排行榜
     *
     * @param length     排行榜长度
     */
    public Map<String, Integer> getRankingMap(String activityId, int length) {
        Map<String, Integer> linkedRankMap = new LinkedHashMap<>();
        String key = getRankingKey(activityId);
        Set<ZSetOperations.TypedTuple<String>> rangeWithScores = clusterTemplate.opsForZSet().reverseRangeWithScores(key, 0, length - 1);
        if (null == rangeWithScores) {
            return linkedRankMap;
        }
        for (ZSetOperations.TypedTuple<String> rangeWithScore : rangeWithScores) {
            if (null == rangeWithScore.getValue() || null == rangeWithScore.getScore()) {
                continue;
            }
            linkedRankMap.put(rangeWithScore.getValue(), rangeWithScore.getScore().intValue());
        }
        return linkedRankMap;
    }

    public Map<String, Integer> getRankingMap(String activityId, String strDate, int length) {
        Map<String, Integer> linkedRankMap = new LinkedHashMap<>();
        String key = getRankingKey(activityId, strDate);
        Set<ZSetOperations.TypedTuple<String>> rangeWithScores = clusterTemplate.opsForZSet().reverseRangeWithScores(key, 0, length - 1);
        if (null == rangeWithScores) {
            return linkedRankMap;
        }
        for (ZSetOperations.TypedTuple<String> rangeWithScore : rangeWithScores) {
            if (null == rangeWithScore.getValue() || null == rangeWithScore.getScore()) {
                continue;
            }
            linkedRankMap.put(rangeWithScore.getValue(), rangeWithScore.getScore().intValue());
        }
        return linkedRankMap;
    }

    /**
     * 获取排名，返回的数据=redis的排行+1
     */
    public int getRank(String activityId, String uid) {
        try {
            String key = getRankingKey(activityId);
            Long rank = clusterTemplate.opsForZSet().reverseRank(key, uid);
            return rank != null ? rank.intValue() + 1 : 0;
        } catch (Exception e) {
            logger.error("getRank error aid={}", uid, e);
            return 0;
        }
    }

    public int getRank(String activityId, String strDate, String uid) {
        try {
            String key = getRankingKey(activityId, strDate);
            Long rank = clusterTemplate.opsForZSet().reverseRank(key, uid);
            return rank != null ? rank.intValue() + 1 : 0;
        } catch (Exception e) {
            logger.error("getRank error aid={} strDate={}", uid, strDate, e);
            return 0;
        }
    }

    public String getRankingTop1(String activityId, String strDate) {
        String key = getRankingKey(activityId, strDate);
        Set<ZSetOperations.TypedTuple<String>> rangeWithScores = clusterTemplate.opsForZSet().reverseRangeWithScores(key, 0, 0);
        if (null == rangeWithScores) {
            return "";
        }
        for (ZSetOperations.TypedTuple<String> rangeWithScore : rangeWithScores) {
            if (null == rangeWithScore.getValue() || null == rangeWithScore.getScore()) {
                continue;
            }
            return rangeWithScore.getValue();
        }
        return "";
    }

    public int incSendGiftBeans(String activityId, String uid, int beans) {
        try {
            String key = getSendGiftBeansKey(activityId);
            Long increment = clusterTemplate.opsForHash().increment(key, uid, beans);
            clusterTemplate.expire(key, COMMON_EXPIRE_DAYS, TimeUnit.DAYS);
            return increment.intValue();
        } catch (Exception e) {
            logger.error("incSendGiftBeans error. activityId={} uid={} beans={} {}", activityId, uid, beans, e.getMessage(), e);
            return 0;
        }
    }

    public int getSendGiftBeans(String activityId, String uid) {
        String key = getSendGiftBeansKey(activityId);
        try {
            String strValue = (String) clusterTemplate.opsForHash().get(key, uid);
            return StringUtils.hasLength(strValue) ? Integer.parseInt(strValue) : 0;
        } catch (Exception e) {
            logger.error("getSendGiftBeans error. activityId={} uid={} {}", activityId, uid, e.getMessage(), e);
            return 0;
        }
    }

    private String getSendGiftBeansKey(String activityId) {
        return "hash:activitySendGiftBeans_" + activityId + "_" + DateHelper.ARABIAN.formatDateInDay();
    }

    public void setRewardLevel(String activityId, String uid, int level) {
        String key = getRewardLevelKey(activityId);
        clusterTemplate.opsForHash().put(key, uid, level + "");
        clusterTemplate.expire(key, COMMON_EXPIRE_DAYS, TimeUnit.DAYS);
    }

    public int getRewardLevel(String activityId, String uid) {
        String key = getRewardLevelKey(activityId);
        String strValue = (String) clusterTemplate.opsForHash().get(key, uid);
        return StringUtils.hasLength(strValue) ? Integer.parseInt(strValue) : 0;
    }

    private String getRewardLevelKey(String activityId) {
        return "hash:userRewardLevel"+ "_" + activityId + "_" + DateHelper.ARABIAN.formatDateInDay();
    }

    public void addLoginRecord(String activityId, String uid) {
        String key = getLoginRecordKey(activityId);
        clusterTemplate.opsForSet().add(key, uid);
        clusterTemplate.expire(key, COMMON_EXPIRE_DAYS, TimeUnit.DAYS);
    }

    public boolean hasLoginRecord(String activityId, String uid) {
        return Boolean.TRUE.equals(clusterTemplate.opsForSet().isMember(getLoginRecordKey(activityId), uid));
    }

    private String getLoginRecordKey(String activityId) {
        return "set:activityLoginRecord_" + activityId + "_" + DateHelper.ARABIAN.formatDateInDay();
    }

    public void addAwardNotify(String activityId, int slang, String strNotify) {
        String key = getAwardNotifyKey(activityId, slang);
        try {
            Long listSize = clusterTemplate.opsForList().leftPush(key, strNotify);
            clusterTemplate.expire(key, ExpireTimeConstant.EXPIRE_DAYS_THIRTY, TimeUnit.DAYS);
            if (null != listSize && listSize > 100) {
                clusterTemplate.opsForList().trim(key, 0, 10);
            }
        } catch (Exception e) {
            logger.error("addAwardNotify error. activityId={} strNotify={} {}", activityId, strNotify, e);
        }
    }

    public List<String> getAwardNotifyList(String activityId, int slang) {
        try {
            String key = getAwardNotifyKey(activityId, slang);
            return clusterTemplate.opsForList().range(key, 0, 9);
        } catch (Exception e) {
            logger.error("getAwardNotifyList error. activityId={}", activityId, e);
            return Collections.emptyList();
        }
    }

    private String getAwardNotifyKey(String activityId, int slang) {
        return "list:activityAwardNotify_" + activityId + "_" + slang;
    }
}
