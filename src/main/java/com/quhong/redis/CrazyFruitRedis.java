package com.quhong.redis;

import org.springframework.stereotype.Component;

import java.util.Map;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2025/4/9
 */
@Component
public class CrazyFruitRedis extends CommonRedis {

    // ====================      用户个人数据      ===================

    public void setUserData(String activityId, String uid, String hashKey, String hashValue) {
        setHashValue(getUserDataKey(activityId, uid), hashKey, hashValue);
    }

    public void setUserData(String activityId, String uid, String hashKey, int hashValue) {
        setHashValue(getUserDataKey(activityId, uid), hashKey, String.valueOf(hashValue));
    }

    public long incUserData(String activityId, String uid, String hashKey, int incValue) {
        return incHashValue(getUserDataKey(activityId, uid), hashKey, incValue);
    }

    public Map<String, Long> getUserData(String activityId, String uid) {
        Map<String, String> hashMap = getHashMap(getUserDataKey(activityId, uid));
        return hashMap.entrySet().stream().collect(Collectors.toMap(Map.Entry::getKey, entry -> Long.parseLong(entry.getValue())));
    }

    public long getUserData(String activityId, String uid, String hashKey) {
        return getHashIntValue(getUserDataKey(activityId, uid), hashKey);
    }

    private String getUserDataKey(String activityId, String uid) {
        return "hash:userData_" + activityId + "_" + uid;
    }

    // ====================      活动榜单      ===================

    public long getRankingScore(String activityId, String rankType, String aid) {
        return getRankingScore(getRankingKey(activityId, rankType), aid);
    }

    public int getRankingRank(String activityId, String rankType, String aid) {
        return getRankingRank(getRankingKey(activityId, rankType), aid);
    }

    public long incrRankingScore(String activityId, String rankType, String aid, int score) {
        return incrRankingScore(getRankingKey(activityId, rankType), aid, score);
    }

    public void setRankingScore(String activityId, String rankType, String aid, int score) {
        setRankingScore(getRankingKey(activityId, rankType), aid, score);
    }

    public Map<String, Long> getRankingMap(String activityId, String rankType, int length) {
        return getRankingMap(getRankingKey(activityId, rankType), length);
    }

    private String getRankingKey(String activityId, String rankType) {
        return "zset:activityRanking_" + activityId + "_" + rankType;
    }

}
