package com.quhong.redis;

import com.alibaba.fastjson.JSON;
import com.quhong.data.RedisUserBetData;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.stereotype.Component;

import jakarta.annotation.Resource;
import java.util.*;
import java.util.concurrent.TimeUnit;

@Component
public class UserBetRedis {
    private final static Logger logger = LoggerFactory.getLogger(UserBetRedis.class);
    private static final long EXPIRE_DAY = 2;
    private String expireDate = "";
    private String warnDate = "";

    @Resource(name = DataRedisBean.MAIN_CLUSTER)
    private StringRedisTemplate clusterRedis;

    public void addBetData(String dateStr, int loop, String uid, List<RedisUserBetData.BetBean> betBeanList) {
        try {
            String json = JSON.toJSONString(betBeanList);
            String key = getLoopKey(dateStr, loop);
            clusterRedis.opsForHash().put(key, uid, json);
            if (!key.equals(expireDate)) {
                clusterRedis.expire(key, 3, TimeUnit.DAYS);
                expireDate = key;
            }
        } catch (Exception e) {
            logger.error("addBetData error dateStr={} loop={} uid={} msg={} ", dateStr, loop, uid, e.getMessage(), e);
        }
    }


    public Map<String, List<RedisUserBetData.BetBean>> getAllBetData(String dateStr, int loop) {
        try {
            Map<String, List<RedisUserBetData.BetBean>> resultMap = new HashMap<>();
            Map<Object, Object> map = clusterRedis.opsForHash().entries(getLoopKey(dateStr, loop));
            for (Map.Entry<Object, Object> entry : map.entrySet()) {
                try {
                    String value = String.valueOf(entry.getValue());
                    List<RedisUserBetData.BetBean> betBeanList = JSON.parseArray(value, RedisUserBetData.BetBean.class);
                    resultMap.put(String.valueOf(entry.getKey()), betBeanList);
                } catch (Exception e) {
                    logger.error("getAllBetData error key={} value={}", entry.getKey(), entry.getValue(), e);
                }
            }
            return resultMap;
        } catch (Exception e) {
            logger.error("getAllBetData error {}", e.getMessage(), e);
            return Collections.emptyMap();
        }
    }


    public List<RedisUserBetData.BetBean> getBetData(String dateStr, int loop, String uid) {
        try {
            String json = (String) clusterRedis.opsForHash().get(getLoopKey(dateStr, loop), uid);
            List<RedisUserBetData.BetBean> toList = JSON.parseArray(json, RedisUserBetData.BetBean.class);
            if (null == toList) {
                return new ArrayList<>();
            } else {
                return toList;
            }
        } catch (Exception e) {
            logger.error("getBetData error dateStr={} loop={} uid={}  msg={} ", dateStr, loop, uid, e.getMessage(), e);
            return new ArrayList<>();
        }
    }


    public void addExpire(String dateStr, int loop) {
        try {
            String key = getLoopKey(dateStr, loop);
            clusterRedis.expire(key, 3, TimeUnit.DAYS);
        } catch (Exception e) {
            logger.error("addBetData error dateStr={} loop={}  msg={} ", dateStr, loop, e.getMessage(), e);
        }
    }

    private String getLoopKey(String dateStr, int loop) {
        return "hash:fruit:loop:" + dateStr + "-" + loop;
    }
}
