package com.quhong.chain;

import com.quhong.constant.GiftConstant;
import com.quhong.constant.GiftHttpCode;
import com.quhong.data.ActorData;
import com.quhong.data.GiftContext;
import com.quhong.data.dto.MoneyDetailReq;
import com.quhong.data.dto.SendGiftDTO;
import com.quhong.data.vo.SendGiftVO;
import com.quhong.enums.ApiResult;
import com.quhong.enums.HttpCode;
import com.quhong.exception.CommonException;
import com.quhong.mysql.data.GiftData;
import com.quhong.redis.MysteryRedis;
import jakarta.annotation.Resource;
import org.springframework.stereotype.Component;
import org.springframework.util.ObjectUtils;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.Iterator;

/**
 * 钻石礼物处理
 */
@Component
public class BeansGiftSend extends AbstractGiftSend {

    public static final String SVIP_GIFT_INCOME = "0.8";

    @Resource
    private MysteryRedis mysteryRedis;

    @Override
    protected void send(SendGiftDTO req, SendGiftVO resp, GiftData giftData, GiftContext context) {
        // 花费的钻石或金币
        int costBeans = giftData.getPrice() * req.getNumber() * context.getAidSet().size();
        if (costBeans <= 0) {
            logger.error("cost beans less then zero! giftPrice={} number={} aidSize={}",
                    giftData.getPrice(), req.getNumber(), context.getAidSet().size());
            throw new CommonException(HttpCode.PARAM_ERROR);
        }
        // 发送金币礼物所消耗的金币平台全部回收，接收者无法获得任何结算币或活跃币
        if (!context.isBackpackGift() && context.isSilverCoinGift()) {
            reduceCoins(req, costBeans, giftData, context);
            logger.info("send coins gift, uid={} giftId={}", req.getUid(), req.getGiftId());
            return;
        }
        // 背包礼物和vip礼物单独处理，不需要扣钻 && !context.isVipGift()
        if (!context.isBackpackGift()) {
            if (context.getSendActorData().getVirtualDiamonds() >= costBeans && !context.isLuckyGift()) {
                // 如果账号有虚拟钻石，在虚拟钻石可使用的功能或场景下优先使用虚拟钻石
                reduceVirtualBeans(req, costBeans, giftData, context);
                context.setUseVirtualBeans(true);
            } else {
                reduceBeans(req, costBeans, giftData, context);
            }
        }
        // 每个用户获得的钻石
        int price = giftData.getPrice();
        int number = req.getNumber();
        if (resp.getGiftInfo() != null && resp.getGiftInfo().getGiftRandom() == 1) {
            // 盲盒礼物接收方获取的是开出来的礼物
            price = context.getGiftInfo().getGiftPrice();
            context.getRandomGiftMap().forEach((k, v) -> {
                BigDecimal earnCharm = getEarnCharm(v.getGiftPrice() * number, req, context);
                v.setEarnCharm(context.isUseVirtualBeans() ? BigDecimal.ZERO : earnCharm);
                v.setEarnCharmWithVirtual(earnCharm);
            });
        }
        context.setEarnBeans(0);
        BigDecimal earnCharm = getEarnCharm(price * number, req, context);
        context.setEarnCharm(context.isUseVirtualBeans() ? BigDecimal.ZERO : earnCharm);
        context.setEarnCharmWithVirtual(earnCharm);
        context.setCostBeans(costBeans);
        context.setCostBeansFixed(getEarnCharm(costBeans, req, context).intValue());
    }

    /**
     * 计算主播，可以获得的魅力值
     *
     * @param diamonds 钻石数 （礼物价格 * 礼物数量）
     */
    public BigDecimal getEarnCharm(int diamonds, SendGiftDTO req, GiftContext context) {
        // 5月1号后走1:1结算
        BigDecimal earnCharm = BigDecimal.valueOf(diamonds);
        if (context.isLuckyGift() && context.getZipInfo().isJackpotGift(1)) {
            // 钻石版幸运礼物
            return BigDecimal.ZERO;
        } else if (context.isLuckyGift() && context.getZipInfo().isJackpotGift(2)) {
            // 收礼用户抽10%魅力值
            return earnCharm.multiply(new BigDecimal("0.1"));
        } else if (context.isHotGift()) {
            // 热门礼物2:1
            return earnCharm.multiply(BigDecimal.valueOf(0.5).setScale(2, RoundingMode.HALF_UP));
        } else if (context.isActivityGift() || context.isCelebrityGift() || context.isCountryGift() || context.isFusionGift()) {
            return earnCharm.multiply(BigDecimal.valueOf(0.8).setScale(2, RoundingMode.HALF_UP));
        } else if (context.isSvipGift()) {
            return earnCharm.multiply(new BigDecimal(SVIP_GIFT_INCOME).setScale(2, RoundingMode.HALF_UP));
        } else if (req.getSendType() == GiftConstant.ST_SEND_ONE && req.getSingleType() == GiftConstant.SINGLE_TYPE_MSG) {
            // 私信礼物
            return earnCharm.multiply(BigDecimal.valueOf(0.7).setScale(2, RoundingMode.HALF_UP));
        }
        return earnCharm;
    }

    private String[] getTitleAndDesc(SendGiftDTO req, GiftData giftData, GiftContext context) {
        String title = "";
        String desc = "";
        if (req.getSendType() == GiftConstant.ST_SEND_ONE) {
            title = GiftConstant.SEND_ONE_GIFTS_TITLE;
            ActorData actorData = actorDao.getActorDataFromCache(req.getAid());
            desc = String.format(GiftConstant.SEND_ONE_GIFTS_DESC, giftData.getGname(), actorData.getStrRid());
        } else if (req.getSendType() == GiftConstant.ST_SEND_MANY) {
            title = GiftConstant.SEND_GIFTS_TITLE;
            StringBuilder ridStr = new StringBuilder();
            Iterator<String> iterator = context.getAidSet().iterator();
            while (iterator.hasNext()) {
                String aid = iterator.next();
                String mysteryId = mysteryRedis.getMysteryIdFromCache(aid);
                if (!ObjectUtils.isEmpty(mysteryId)) {
                    ridStr.append(mysteryId);
                } else {
                    ActorData actorData = actorDao.getActorDataFromCache(aid);
                    if (null == actorData) {
                        continue;
                    }
                    ridStr.append(actorData.getStrRid());
                }
                if (iterator.hasNext()) {
                    ridStr.append(",");
                }
            }
            desc = String.format(GiftConstant.SEND_GIFTS_DESC, giftData.getGname(), ridStr);
        } else if (req.getSendType() == GiftConstant.ST_SEND_EVERYONE) {
            title = GiftConstant.SEND_EVERYONE_GIFTS_TITLE;
            desc = String.format(GiftConstant.SEND_EVERYONE_GIFTS_DESC, giftData.getGname(), context.getOwnerData().getStrRid());
        }
        return new String[]{title, desc};
    }

    private void reduceBeans(SendGiftDTO req, int changed, GiftData giftData, GiftContext context) {
        String[] titleAndDescArr = getTitleAndDesc(req, giftData, context);
        MoneyDetailReq moneyDetailReq = new MoneyDetailReq();
        moneyDetailReq.setRandomId();
        moneyDetailReq.setUid(req.getUid());
        moneyDetailReq.setAtype(GiftConstant.AT_SEND_GIFTS);
        moneyDetailReq.setChanged(-changed);
        moneyDetailReq.setTitle(titleAndDescArr[0]);
        moneyDetailReq.setDesc(titleAndDescArr[1]);
        moneyDetailReq.setRoomId(req.getRoomId());
        ApiResult<String> result = dataCenterService.reduceBeans(moneyDetailReq);
        if (!result.isOk()) {
            if (1 == result.getCode().getCode()) {
                throw new CommonException(GiftHttpCode.DIAMONDS_NOT_ENOUGH);
            }
            logger.error("reduce beans error, uid={} msg={}", req.getUid(), result.getCode().getMsg());
            throw new CommonException(HttpCode.SERVER_ERROR);
        }
    }

    private void reduceCoins(SendGiftDTO req, int changed, GiftData giftData, GiftContext context) {
        String[] titleAndDescArr = getTitleAndDesc(req, giftData, context);
        if (!heartRecordDao.changeHeart(req.getUid(), -changed, titleAndDescArr[0], titleAndDescArr[1])) {
            logger.info("user not enough coin. uid={} change={}", req.getUid(), -changed);
            throw new CommonException(GiftHttpCode.COIN_NOT_ENOUGH);
        }
    }

    private void reduceVirtualBeans(SendGiftDTO req, int changed, GiftData giftData, GiftContext context) {
        String[] titleAndDescArr = getTitleAndDesc(req, giftData, context);
        ApiResult<String> result = dataCenterService.changeVirtualDiamond(req.getUid(), -changed, titleAndDescArr[0], titleAndDescArr[1]);
        if (!result.isOk()) {
            if (1 == result.getCode().getCode()) {
                logger.info("reduceVirtualBeans insufficient balance. uid={} change={}", req.getUid(), -changed);
                throw new CommonException(GiftHttpCode.COIN_NOT_ENOUGH);
            }
            logger.error("reduce beans error, uid={} msg={}", req.getUid(), result.getCode().getMsg());
            throw new CommonException(HttpCode.SERVER_ERROR);
        }
    }
}
