package com.quhong.sud.service;

import cn.hutool.crypto.digest.HMac;
import cn.hutool.crypto.digest.HmacAlgorithm;
import com.alibaba.fastjson.JSONObject;
import com.quhong.analysis.CreateGameLogEvent;
import com.quhong.analysis.EventReport;
import com.quhong.api.SudGameApi;
import com.quhong.config.SudGameConfig;
import com.quhong.controllers.SudController;
import com.quhong.core.concurrency.BaseTaskFactory;
import com.quhong.core.concurrency.queues.SlowTaskQueue;
import com.quhong.core.concurrency.tasks.Task;
import com.quhong.core.config.ServerConfig;
import com.quhong.core.distribution.DistributeLock;
import com.quhong.core.timers.LoopTask;
import com.quhong.core.timers.TimerService;
import com.quhong.core.utils.DateHelper;
import com.quhong.dailyTask.CommonTaskService;
import com.quhong.dailyTask.UserLevelTaskService;
import com.quhong.data.ActorData;
import com.quhong.data.CommonMqTopicData;
import com.quhong.data.RoomRoleData;
import com.quhong.data.UserLevelTaskData;
import com.quhong.data.dto.MoneyDetailReq;
import com.quhong.enums.*;
import com.quhong.exception.GameException;
import com.quhong.feign.DataCenterService;
import com.quhong.handler.HttpEnvData;
import com.quhong.image.ImageUrlGenerator;
import com.quhong.mongo.dao.ActorDao;
import com.quhong.mongo.dao.MongoRoomDao;
import com.quhong.mongo.dao.RoomConfigDao;
import com.quhong.mongo.dao.RoomMemberDao;
import com.quhong.mongo.data.MongoRoomData;
import com.quhong.monitor.MonitorSender;
import com.quhong.mq.MqSenderService;
import com.quhong.msg.MarsServerMsg;
import com.quhong.msg.obj.HighlightTextObject;
import com.quhong.msg.obj.UNameObject;
import com.quhong.msg.room.RoomLudoKitOutMsg;
import com.quhong.msg.room.RoomNotificationMsg;
import com.quhong.msg.room.RoomSudGameOperateMsg;
import com.quhong.mysql.dao.HeartRecordDao;
import com.quhong.mysql.dao.RoomBlacklistDao;
import com.quhong.mysql.mapper.waho.RoomMicInfoMapper;
import com.quhong.room.RoomWebSender;
import com.quhong.room.cache.RoomActorCache;
import com.quhong.room.data.RoomActorDetailData;
import com.quhong.room.redis.RoomKickRedis;
import com.quhong.service.MysteryService;
import com.quhong.sud.data.SudGameConfigInfo;
import com.quhong.sud.data.SudGameInfo;
import com.quhong.sud.dto.CreateSudGameDTO;
import com.quhong.sud.dto.EventDTO;
import com.quhong.sud.dto.GamePermissionsDTO;
import com.quhong.sud.dto.SudGameDTO;
import com.quhong.sud.mongo.dao.SudGameDao;
import com.quhong.sud.mongo.data.SudGameData;
import com.quhong.sud.mongo.data.SudGamePlayerData;
import com.quhong.sud.redis.SudGameRedis;
import com.quhong.sud.vo.BaseVO;
import com.quhong.sud.vo.CreateSudGameVO;
import com.quhong.sud.vo.GetUserInfoVO;
import com.quhong.sud.vo.MatchingVO;
import com.quhong.utils.AppVersionUtils;
import com.quhong.utils.K8sUtils;
import com.quhong.utils.RoomUtils;
import com.quhong.utils.WalletUtils;
import jakarta.annotation.PostConstruct;
import jakarta.annotation.Resource;
import org.bson.types.ObjectId;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import org.springframework.util.ObjectUtils;
import org.springframework.util.StringUtils;
import tech.sud.mgp.auth.api.SudCode;
import tech.sud.mgp.auth.api.SudMGPAuth;

import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2022/7/1
 */
@Service
public class SudService extends SlowTaskQueue {

    private static final Logger logger = LoggerFactory.getLogger(SudService.class);

    private static final String CREATE_GAME_MSG = "Created the %s game."; // 创建游戏消息
    private static final String CREATE_GAME_MSG_AR = "إنشاء لعبة %s"; // 创建游戏消息阿语
    private static final String CLOSED_GAME_MSG = "%s game is closed."; // 关闭游戏消息
    private static final String CLOSED_GAME_MSG_AR = "تم إغلاق اللعبة %s"; // 关闭游戏消息阿语
    private static final String OTHER_GAME_IN_PROGRESS = "The %s game is playing in the room, please wait for the game to end and try again."; // 有其他游戏正在进行提示
    private static final String OTHER_GAME_IN_PROGRESS_AR = "هناك لعبة %s تلعب في الغرفة ، يرجى الانتظار حتى تنتهي اللعبة والمحاولة مرة أخرى."; // 有其他游戏正在进行提示阿语
    private static final String GAME_TIMEOUT_NOT_START = "%s game timeout does not start, it has ended automatically."; // 游戏超时未开始
    private static final String GAME_TIMEOUT_NOT_START_AR = "لا تبدأ مهلة لعبة %s وتنتهي تلقائيا."; // 游戏超时未开始阿语

    private static final int COIN = 1;
    private static final int DIAMOND = 2;

    private static final int LOOP_TIME = 60 * 1000; // 每分钟扫描一下是否有超时的游戏
    private static final int CHECK_TIME = 5 * 60; // 5分钟未开始游戏系统自动关闭游戏
    private static final int CHECK_OVER_TIME = 2 * 60 * 60; // 存在2个小时的游戏系统自动关闭游戏

    private static final String JOIN_GAME_MSG = "%s has joined %s game.";
    private static final String JOIN_GAME_MSG_AR = "انضم %s إلى لعبة %s.";

    private static final int PLATFORM_WARNING_LINE = 110000; // 单款游戏平台亏损1万钻以上时触发告警

    @Resource
    private RoomMemberDao roomMemberDao;
    @Resource
    private MongoRoomDao mongoRoomDao;
    @Resource
    private ActorDao actorDao;
    @Resource
    private SudGameConfig sudGameConfig;
    @Resource
    private SudGameDao sudGameDao;
    @Resource
    private SudGameRedis sudGameRedis;
    @Resource
    private SudMGPAuth sudMGPAuth;
    @Resource
    private RoomWebSender roomWebSender;
    @Resource
    private RoomActorCache roomActorCache;
    @Resource
    private RoomMicInfoMapper roomMicInfoMapper;
    @Resource
    private HeartRecordDao heartRecordDao;
    @Resource
    private SudGameApi sudGameApi;
    @Resource
    private DataCenterService dataCenterService;
    @Resource
    private RoomConfigDao roomConfigDao;
    @Resource
    private EventReport eventReport;
    @Resource
    private RoomBlacklistDao roomBlacklistDao;
    @Resource
    private RoomKickRedis roomKickRedis;
    @Resource
    private UserLevelTaskService userLevelTaskService;
    @Resource
    private MonitorSender monitorSender;
    @Resource
    private MqSenderService mqSenderService;
    @Resource
    private MysteryService mysteryService;
    @Resource
    private CommonTaskService commonTaskService;
    @Resource
    private K8sUtils k8sUtils;


    @PostConstruct
    public void postInit() {
        TimerService.getService().addDelay(new LoopTask(this, LOOP_TIME) {
            @Override
            protected void execute() {
                if (k8sUtils.isMasterFromCache()) {
                    onTick();
                }
            }
        });
    }

    private void onTick() {
        List<SudGameInfo> allSudGameInfo = sudGameRedis.getAllSudGameInfo();
        logger.info("dismiss sud game. allSudGameInfo.size={}", allSudGameInfo.size());
        for (SudGameInfo game : allSudGameInfo) {
            try (DistributeLock lock = new DistributeLock(SudController.SUD_LOCK_KEY + game.getRoomId(), 20)) {
                lock.lock();
                dismissGame(game);
            }
        }
    }

    /**
     * 关闭五分钟未开始的游戏
     */
    private void dismissGame(SudGameInfo game) {
        int gameExistsTime = DateHelper.getNowSeconds() - game.getCreateTime();
        if ((game.getStatus() == SudGameConstant.GAME_MATCHING && gameExistsTime > CHECK_TIME) || gameExistsTime > CHECK_OVER_TIME) {
            try {
                logger.info("dismiss sud game. status={} createTime={} roomId={} gameId={}",
                        game.getStatus(), game.getCreateTime(), game.getRoomId(), game.getGameId());
                String roomId = game.getRoomId();
                // 删除redis里的游戏信息
                sudGameRedis.removeGameInfo(game.getGameId(), roomId);
                // 更新游戏信息
                SudGameData data = sudGameDao.findData(game.getGameId());
                if (data == null) {
                    return;
                }
                data.setStatus(SudGameConstant.GAME_CLOSED);
                data.setEndTime(DateHelper.getNowSeconds());
                sudGameDao.save(data);
                data.getPlayerList().forEach(k -> {
                    // 移除玩家在游戏的redis
                    sudGameRedis.removePlayerData(k.getUid());
                    if (game.getStatus() == SudGameConstant.GAME_MATCHING) {
                        // 游戏超时未开始，返还入场费
                        returnGameCurrency(data, k.getUid());
                    }
                });
                logger.info("dismiss not started sud game. gameId={}", game.getGameId());
                // 发送游戏超时自动关闭消息
                sendRoomSudGameOperateMsg(null, game, SudGameConstant.GAME_TIME_OUT_MSG);
                gameChange(data);
            } catch (Exception e) {
                logger.error("dismiss finished sud game. gameId={} {}", game.getGameId(), e.getMessage(), e);
            }
        }
    }

    /**
     * 发送游戏操作消息
     * opt 1创建游戏 2关闭游戏 3超时关闭 4开始游戏
     */
    private void sendRoomSudGameOperateMsg(RoomActorDetailData actorData, SudGameInfo game, int opt) {
        BaseTaskFactory.getFactory().addSlow(new Task() {
            @Override
            protected void execute() {
                RoomSudGameOperateMsg msg = new RoomSudGameOperateMsg();
                // gameType 1碰碰我最强 2 Ludo 3 UMO
                int gameType = game.getGameType();
                msg.setOpt_user(buildUNameObject(actorData));
                msg.setGame_icon(sudGameConfig.getIconUrlByGameType(gameType));
                msg.setGame_type(gameType);
                msg.setOpt(opt);
                if (opt == SudGameConstant.GAME_CREATE_MSG) {
                    // 创建游戏
                    msg.setMsg(String.format(CREATE_GAME_MSG, game.getGameName()));
                    msg.setMsg_ar(String.format(CREATE_GAME_MSG_AR, game.getGameNameAr()));
                } else if (opt == SudGameConstant.GAME_CLOSED_MSG) {
                    // 结束游戏
                    msg.setMsg(String.format(CLOSED_GAME_MSG, game.getGameName()));
                    msg.setMsg_ar(String.format(CLOSED_GAME_MSG_AR, game.getGameNameAr()));
                } else {
                    // 超时结束游戏
                    msg.setOpt(SudGameConstant.GAME_CLOSED_MSG);
                    msg.setMsg(String.format(GAME_TIMEOUT_NOT_START, game.getGameName()));
                    msg.setMsg_ar(String.format(GAME_TIMEOUT_NOT_START_AR, game.getGameNameAr()));
                }
                int onlineVersion = sudGameConfig.getSudGameInfoMap().get(game.getGameType()).getOnlineVersion();
                roomWebSender.sendRoomWebMsg(game.getRoomId(), "", msg, true, onlineVersion);
            }
        });
    }

    /**
     * 设置房间内创建游戏权限
     */
    public void setPermissions(GamePermissionsDTO reqDTO) {
        String uid = reqDTO.getUid();
        String roomId = reqDTO.getRoomId();
        // 房主才能设置房间内成员创建游戏的权限
        if (!RoomUtils.isHomeowner(uid, roomId)) {
            throw new GameException(GameHttpCode.NOT_ROOM_OWNER);
        }
        int gameType = reqDTO.getGameType() != null ? reqDTO.getGameType() : SudGameConstant.LUDO_GAME;
        if (gameType == SudGameConstant.LUDO_GAME) {
            mongoRoomDao.updateCreateGamePermissions(roomId, reqDTO.getCreateGame());
        } else if (gameType == SudGameConstant.UMO_GAME) {
            roomConfigDao.updateRoomConfig(roomId, RoomConfigDao.CREATE_UMO, reqDTO.getCreateGame());
        } else if (gameType == SudGameConstant.MONSTER_CRUSH_GAME) {
            roomConfigDao.updateRoomConfig(roomId, RoomConfigDao.CREATE_MONSTER_CRUSH, reqDTO.getCreateGame());
        } else if (gameType == SudGameConstant.DOMINO_GAME) {
            roomConfigDao.updateRoomConfig(roomId, RoomConfigDao.CREATE_DOMINO, reqDTO.getCreateGame());
        }
    }

    /**
     * 校验房间内创建和开始游戏的权限
     */
    public void checkPermissions(CreateSudGameDTO reqDTO) {
        MongoRoomData roomData = mongoRoomDao.getDataFromCache(reqDTO.getRoomId());
        if (roomData == null) {
            logger.error("check permissions.roomData = null roomId={}", reqDTO.getRoomId());
            throw new GameException(HttpCode.PARAM_ERROR);
        }
        int createPermissions;
        if (reqDTO.getGameType() == SudGameConstant.LUDO_GAME) {
            createPermissions = roomData.getCreate_game();
        } else if (reqDTO.getGameType() == SudGameConstant.UMO_GAME) {
            createPermissions = roomConfigDao.getIntRoomConfig(reqDTO.getRoomId(), RoomConfigDao.CREATE_UMO, 0);
        } else if (reqDTO.getGameType() == SudGameConstant.MONSTER_CRUSH_GAME) {
            createPermissions = roomConfigDao.getIntRoomConfig(reqDTO.getRoomId(), RoomConfigDao.CREATE_MONSTER_CRUSH, 0);
        } else {
            createPermissions = roomConfigDao.getIntRoomConfig(reqDTO.getRoomId(), RoomConfigDao.CREATE_DOMINO, 0);
        }
        RoomRoleData roleData = roomMemberDao.getRoleData(reqDTO.getRoomId(), reqDTO.getUid());
        switch (createPermissions) {
            case SudGameConstant.MEMBER:
                if (roleData.getRole() == RoomRoleType.AUDIENCE) {
                    throw new GameException(GameHttpCode.NOT_THE_ROOM_MEMBER);
                }
                break;
            case SudGameConstant.ADMIN:
                if (roleData.getRole() == RoomRoleType.AUDIENCE || roleData.getRole() == RoomRoleType.MEMBER) {
                    throw new GameException(GameHttpCode.NOT_THE_ROOM_ADMIN);
                }
                break;
            case SudGameConstant.ONLY_ROOM_OWNER:
                if (roleData.getRole() != RoomRoleType.HOST) {
                    throw new GameException(GameHttpCode.NOT_THE_ROOM_OWNER);
                }
                break;
            default:
                break;
        }
    }

    /**
     * 创建游戏
     */
    public CreateSudGameVO createGame(CreateSudGameDTO reqDTO) {
        String uid = reqDTO.getUid();
        String roomId = reqDTO.getRoomId();
        RoomActorDetailData actorData = roomActorCache.getData(roomId, uid, false);
        if (actorData == null) {
            logger.error("can not find actor data, uid ={}", uid);
            throw new GameException(HttpCode.PARAM_ERROR);
        }
        // 即构小游戏只能同时存在一种
        checkExistOtherSudGame(reqDTO, roomId, reqDTO.getGameType());
        SudGameData sudGameData = checkGame(roomId);
        if (null != sudGameData) {
            logger.info("There are other games in progress. roomId={} ,gameType={}", sudGameData.getGameId(), sudGameData.getGameType());
            throw new GameException(new HttpCode(3021, String.format(OTHER_GAME_IN_PROGRESS, sudGameData.getGameName()), String.format(OTHER_GAME_IN_PROGRESS_AR, sudGameData.getGameNameAr())), gameDataTOVO(sudGameData));
        }
        // 校验创建游戏的权限
        checkPermissions(reqDTO);
        ActorData actorDataFromCache = actorDao.getActorDataFromCache(uid);
        SudGameData gameData = buildSudGameData(reqDTO, actorData, actorDataFromCache.getRobot() == 1);
        if (actorDataFromCache.getRobot() != 1) {
            // Ludo,UMO,Monster Crush游戏创建时需要校验玩家是否已上麦
            List<String> roomMicUidDataList = roomMicInfoMapper.getUidDataList(roomId);
            if (!roomMicUidDataList.contains(uid)) {
                logger.info("player not in mic. uid={} roomId={}", uid, roomId);
                throw new GameException(GameHttpCode.NOT_IN_MIC);
            }
        }
        gameData.setGameId(new ObjectId());
        joinSudGame(uid, roomId, gameData, reqDTO, true);
        // 创建游戏
        SudGameData data = sudGameDao.save(gameData);
        // 往redis里存入游戏相关信息
        SudGameInfo gameInfo = new SudGameInfo();
        BeanUtils.copyProperties(data, gameInfo);
        gameInfo.setGameId(data.getGameId().toString());
        sudGameRedis.saveSudGameInfo(gameInfo);
        sudGameRedis.savePlayerData(uid, data.getGameId().toString());
        // 发送创建游戏消息
        sendRoomSudGameOperateMsg(actorData, gameInfo, SudGameConstant.GAME_CREATE_MSG);
        logger.info("create sud game.creator={} gameId={} gameType={}", gameInfo.getSelfUid(), gameInfo.getGameId(), gameInfo.getGameType());
        // 用户创建游戏时上报数数
        doReportEvent(gameData);
        return gameChange(data);
    }

    public void joinSudGame(String uid, String roomId, SudGameData gameData, HttpEnvData reqDTO, boolean isCreate) {
        // 加入游戏的校验
        if (gameData.getPlayerList().size() >= gameData.getPlayerNumber()) {
            // 游戏人数已满无法加入
            logger.info("game play is full. roomId={} uid={}", roomId, uid);
            throw new GameException(GameHttpCode.PLAYERS_NUM_IS_FULL);
        }
        // 校验玩家是否有未退出的游戏
        String inGameId = sudGameRedis.getPlayerData(uid);
        if (null != inGameId) {
            SudGameInfo sudGameInfo = sudGameRedis.getSudGameInfo(inGameId);
            if (null == sudGameInfo) {
                // 游戏不存在，是脏数据，需要删除
                sudGameRedis.removePlayerData(uid);
                logger.error("clear dirty data uid={} inGameId={}", uid, inGameId);
            } else {
                logger.info("player has in game uid={} isCreate={}", uid, null == gameData.getGameId());
                String gameName = sudGameConfig.getGameNameByGameType(sudGameInfo.getGameType(), reqDTO.getSlang());
                if (isCreate) {
                    throw new GameException(GameHttpCode.PLAYER_IN_GAME_C, new CreateSudGameVO(), new Object[]{gameName});
                } else {
                    throw new GameException(GameHttpCode.PLAYER_IN_GAME_J, gameDataTOVO(gameData), new Object[]{gameName});
                }
            }
        }
        ActorData actorData = actorDao.getActorDataFromCache(uid);
        if (actorData.getRobot() == 1) {
            logger.info("join sud game. actor is robot, skip deduct cost. uid={} gameId={}", uid, gameData.getGameId());
            return;
        }
        SudGameConfigInfo gameConfigInfo = sudGameConfig.getSudGameInfoMap().get(gameData.getGameType());
        if (COIN == gameData.getCurrencyType()) {
            int currency = gameData.getCurrency();
            if (currency > 0) {
                boolean heartFlag = heartRecordDao.changeHeart(uid, -currency, gameConfigInfo.getFeeTitle(), gameConfigInfo.getFeeDesc());
                if (!heartFlag) {
                    logger.info("player not enough heart. uid={} roomId={} change={}", uid, roomId, currency);
                    throw new GameException(GameHttpCode.NOT_ENOUGH_COIN, isCreate ? new CreateSudGameVO() : gameDataTOVO(gameData));
                }
            }
        } else if (DIAMOND == gameData.getCurrencyType()) {
            int currency = gameData.getCurrency();
            if (currency > 0) {
                MoneyDetailReq moneyDetailReq = new MoneyDetailReq();
                moneyDetailReq.setRandomId();
                moneyDetailReq.setUid(uid);
                moneyDetailReq.setAtype(gameConfigInfo.getFeeActType());
                moneyDetailReq.setChanged(-currency);
                moneyDetailReq.setTitle(gameConfigInfo.getFeeTitle());
                moneyDetailReq.setDesc(gameConfigInfo.getFeeDesc());
                moneyDetailReq.setRoomId(roomId);
                ApiResult<String> reduceBeansResult = dataCenterService.reduceBeans(moneyDetailReq);
                if (!reduceBeansResult.isOk()) {
                    if (1 == reduceBeansResult.getCode().getCode()) {
                        logger.info(JSONObject.toJSONString(gameData));
                        throw new GameException(GameHttpCode.DIAMOND_NOT_ENOUGH, isCreate ? new CreateSudGameVO() : gameDataTOVO(gameData));
                    }
                    logger.error("join sud game reduce beans error, uid={} diamond={} msg={}", uid, -currency, reduceBeansResult.getCode().getMsg());
                    throw new GameException(HttpCode.SERVER_ERROR, isCreate ? new CreateSudGameVO() : gameDataTOVO(gameData));
                }
                sudGameRedis.incPrizePoolBeans(gameData.getGameId().toString(), currency);
                gameDiamondAlert(gameData.getGameId().toString(), gameData.getGameType(), -currency);
            }
        } else {
            logger.info("not support currency type. uid={} roomId={} currency type={}", uid, roomId, gameData.getCurrencyType());
            throw new GameException(GameHttpCode.SERVER_ERROR, isCreate ? new CreateSudGameVO() : gameDataTOVO(gameData));
        }
    }

    /**
     * 检测是否存在其他sud游戏
     */
    public void checkExistOtherSudGame(HttpEnvData envData, String roomId, int gameType) {
        int runningGameType = sudGameRedis.getGameTypeByRoomId(roomId);
        if (runningGameType == 0 || runningGameType == gameType) {
            return;
        }
        int onlineVersion = sudGameConfig.getSudGameInfoMap().get(gameType).getOnlineVersion();
        if (!AppVersionUtils.versionCheck(onlineVersion, envData)) {
            logger.info("Please upgrade to the latest version to use it. roomId={} uid={} gameType={} version={}", roomId, envData.getUid(), gameType, envData.getVersioncode());
            throw new GameException(GameHttpCode.NEED_UPGRADE);
        }
        if (runningGameType == SudGameConstant.LUDO_GAME) {
            logger.info("There is Ludo game playing in the room. roomId={} ", roomId);
            throw new GameException(GameHttpCode.EXIST_LUDO_GAME);
        } else if (runningGameType == SudGameConstant.UMO_GAME) {
            logger.info("There is UMO game playing in the room. roomId={} ", roomId);
            throw new GameException(GameHttpCode.EXIST_UMO_GAME);
        } else if (runningGameType == SudGameConstant.MONSTER_CRUSH_GAME) {
            logger.info("There is Monster Crush game playing in the room. roomId={} ", roomId);
            throw new GameException(GameHttpCode.EXIST_MONSTER_CRUSH_GAME);
        } else if (runningGameType == SudGameConstant.DOMINO_GAME) {
            logger.info("There is Domino game playing in the room. roomId={} ", roomId);
            throw new GameException(GameHttpCode.EXIST_DOMINO_GAME);
        }
    }

    /**
     * 构建UNameObject
     */
    private UNameObject buildUNameObject(RoomActorDetailData actorData) {
        if (actorData == null) {
            return null;
        }
        return actorData.toUNameObject();
    }

    /**
     * 加入游戏
     */
    public CreateSudGameVO joinGame(SudGameDTO reqDTO) {
        String uid = reqDTO.getUid();
        SudGameData data = sudGameDao.findData(reqDTO.getGameId());
        if (data == null || data.getStatus() == SudGameConstant.GAME_CLOSED || data.getStatus() == SudGameConstant.GAME_FINISH) {
            throw new GameException(GameHttpCode.GAME_OVER);
        }
        ActorData actorData = mysteryService.getMysteryActor(uid);
        if (actorData == null) {
            logger.error("can not find actor data, uid ={}", uid);
            throw new GameException(HttpCode.PARAM_ERROR);
        }
        checkJoinGame(data, reqDTO, uid);
        return playerJoinGame(reqDTO, data, actorData);
    }

    /**
     * 加入游戏校验
     */
    private void checkJoinGame(SudGameData gameData, SudGameDTO reqDTO, String uid) {
        String roomId = gameData.getRoomId();
        if (gameData.getStatus() == SudGameConstant.GAME_PROCESSING) {
            // 游戏正在进行无法加入
            logger.info("game is running. roomId={} uid={}", roomId, uid);
            throw new GameException(GameHttpCode.GAME_RUNNING, gameDataTOVO(gameData));
        }
        if (gameData.getStatus() == SudGameConstant.GAME_CLOSED) {
            // 游戏已结束无法加入
            logger.info("game is closed. roomId={} uid={}", roomId, uid);
            throw new GameException(GameHttpCode.GAME_CLOSED, gameDataTOVO(gameData));
        }
        // 校验是否已经加入游戏
        List<SudGamePlayerData> playerList = gameData.getPlayerList();
        if (!CollectionUtils.isEmpty(playerList) && !gameData.getSelfUid().equals(uid)) {
            List<String> playerUidList = playerList.stream().map(SudGamePlayerData::getUid).collect(Collectors.toList());
            if (playerUidList.contains(uid)) {
                logger.info("has joined the game. uid={} roomId={}", uid, roomId);
                throw new GameException(GameHttpCode.YOU_HAVE_JOINED_THE_GAME, gameDataTOVO(gameData));
            }
        }
        // 加入游戏的校验
        if (gameData.getPlayerList().size() >= gameData.getPlayerNumber()) {
            // 游戏人数已满无法加入
            logger.info("game play is full. roomId={} uid={}", roomId, uid);
            throw new GameException(GameHttpCode.PLAYERS_NUM_IS_FULL, gameDataTOVO(gameData));
        }
    }

    /**
     * 加入游戏
     */
    public CreateSudGameVO playerJoinGame(SudGameDTO reqDTO, SudGameData data, ActorData actorData) {
        joinSudGame(reqDTO.getUid(), reqDTO.getRoomId(), data, reqDTO, false);
        // 更新游戏信息
        SudGamePlayerData playerData = buildSudGamePlayerData(actorData);
        data.setStatus(SudGameConstant.GAME_MATCHING);
        List<SudGamePlayerData> playerList = data.getPlayerList();
        if (CollectionUtils.isEmpty(playerList)) {
            playerList = new ArrayList<>();
        }
        playerList.add(playerData);
        data.setPlayerList(playerList);
        sudGameDao.save(data);
        // 更新redis里的游戏信息
        SudGameInfo gameInfo = new SudGameInfo();
        BeanUtils.copyProperties(data, gameInfo);
        gameInfo.setGameId(data.getGameId().toString());
        sudGameRedis.updateSudGameInfo(gameInfo);
        sudGameRedis.savePlayerData(reqDTO.getUid(), data.getGameId().toString());
        logger.info("join sud game. uid={} gameId={} gameType={}", reqDTO.getUid(), gameInfo.getGameId(), gameInfo.getGameType());
        sendJoinGameMsg(data, actorData);
        return gameChange(data);
    }

    /**
     * 开始游戏
     */
    public CreateSudGameVO startGame(SudGameDTO reqDTO) {
        String uid = reqDTO.getUid();
        String roomId = reqDTO.getRoomId();
        SudGameData data = sudGameDao.findData(reqDTO.getGameId());
        if (data == null || data.getStatus() == SudGameConstant.GAME_CLOSED || data.getStatus() == SudGameConstant.GAME_FINISH) {
            throw new GameException(GameHttpCode.GAME_CLOSED);
        }
        int playerNum = data.getPlayerList().size();
        if (SudGameConstant.LUDO_GAME == data.getGameType()) {
            if (playerNum < 2 || playerNum > 4) {
                // ludo游戏大于4或小于2人无法开始
                logger.error("wrong number of players.uid={} roomId={} gameType={} playerNumber={}", uid, roomId, data.getGameType(), playerNum);
                throw new GameException(GameHttpCode.WRONG_NUMBER_OF_PLAYERS);
            }
        } else if (SudGameConstant.UMO_GAME == data.getGameType()) {
            if (playerNum < 2 || playerNum > 6) {
                // UMO游戏大于6或小于2人无法开始
                logger.error("wrong number of players.uid={} roomId={} gameType={} playerNumber={}", uid, roomId, data.getGameType(), playerNum);
                throw new GameException(GameHttpCode.WRONG_NUMBER_OF_PLAYERS);
            }
        }
        startSudGame(data);
        // 补充玩家基本信息
        setPlayersBasicInfo(data);
        data.setStartTime(DateHelper.getNowSeconds());
        data.setStatus(SudGameConstant.GAME_PROCESSING);
        data.setTotalCurrency(playerNum * data.getCurrency());
        // 更新游戏信息
        sudGameDao.save(data);
        // 更新redis里的游戏相关信息
        SudGameInfo gameInfo = new SudGameInfo();
        BeanUtils.copyProperties(data, gameInfo);
        gameInfo.setGameId(data.getGameId().toString());
        sudGameRedis.updateSudGameInfo(gameInfo);
        logger.info("start sud game. uid={} gameId={} gameType={}", uid, gameInfo.getGameId(), gameInfo.getGameType());
        // 用户等级任务：玩LUDO或UMO
        userLevelTask(data);
        return gameChange(data);
    }

    private void userLevelTask(SudGameData data) {
        BaseTaskFactory.getFactory().addSlow(new Task() {
            @Override
            protected void execute() {
                for (SudGamePlayerData player : data.getPlayerList()) {
                    if (data.getGameType() == SudGameConstant.LUDO_GAME) {
                        userLevelTaskService.sendTaskDataToMq(new UserLevelTaskData(player.getUid(), UserLevelConstant.PLAY_LUDO, data.getGameId().toString()));
                        if (DateHelper.getNowSeconds() < 1741986000) {
                            // 斋月活动2025年3月12日结束，3月15日停止发送
                            commonTaskService.sendCommonTaskMq(new CommonMqTopicData(player.getUid(), data.getRoomId(), "", data.getGameId().toString(), CommonMqTaskConstant.PLAY_LUDO, 1));
                        }
                    } else if (data.getGameType() == SudGameConstant.UMO_GAME) {
                        userLevelTaskService.sendTaskDataToMq(new UserLevelTaskData(player.getUid(), UserLevelConstant.PLAY_UMO, data.getGameId().toString()));
                    }
                    if (data.getCurrencyType() == DIAMOND) {
                        userLevelTaskService.sendTaskDataToMq(new UserLevelTaskData(player.getUid(), UserLevelConstant.PLAY_ROOM_GAME, data.getCurrency()));
                    }
                }
            }
        });
    }

    private GetUserInfoVO getUserInfoVO(String uid) {
        ActorData actorData = actorDao.getMysteryActorFromCache(uid);
        return GetUserInfoVO.builder()
                .uid(actorData.getUid())
                .isAi(actorData.getRobot() == 1 ? 1 : 0)
                .aiLevel(3)
                .nickName(actorData.getName())
                .gender(actorData.getFb_gender() == 1 ? "male" : "female,")
                .avatarUrl(ImageUrlGenerator.generateSudGameUserUrl(actorData.getHead(), actorData.getSvipLevel()))
                .build();
    }

    private void startSudGame(SudGameData game) {
        String gameId = game.getGameId().toString();
        List<GetUserInfoVO> userInfos = new ArrayList<>();
        for (SudGamePlayerData playerData : game.getPlayerList()) {
            userInfos.add(getUserInfoVO(playerData.getUid()));
        }
        // 不要把机器人放在第一个位置
        userInfos.sort(Comparator.comparing(GetUserInfoVO::getIsAi));
        ApiResult<BaseVO<?>> apiResult = sudGameApi.pushEvent(EventDTO.QuickStartEvent(sudGameConfig.getGameIdByGameType(game.getGameType()), gameId, userInfos, game.getRule()));
        if (apiResult.isError()) {
            logger.error("start game error, roomId={} gameId={} msg={}", game.getRoomId(), gameId, apiResult.getData());
            throw new GameException(GameHttpCode.FAILURE, gameDataTOVO(game));
        }
    }

    public void sendGameResultMsg(String roomId, MarsServerMsg msg, boolean ack, int gameType) {
        int onlineVersion = sudGameConfig.getSudGameInfoMap().get(gameType).getOnlineVersion();
        BaseTaskFactory.getFactory().addSlow(new Task() {
            @Override
            protected void execute() {
                roomWebSender.sendRoomWebMsg(roomId, null, msg, ack, onlineVersion);
            }
        });
    }

    private CreateSudGameVO gameDataTOVO(SudGameData gameData) {
        CreateSudGameVO vo = new CreateSudGameVO();
        BeanUtils.copyProperties(gameData, vo);
        vo.setGameId(gameData.getGameId().toString());
        vo.setCurrency(WalletUtils.diamondsForDisplay(vo.getCurrency()));
        return vo;
    }

    public CreateSudGameVO gameChange(SudGameData gameData) {
        CreateSudGameVO vo = gameDataTOVO(gameData);
        int onlineVersion = sudGameConfig.getSudGameInfoMap().get(gameData.getGameType()).getOnlineVersion();
        BaseTaskFactory.getFactory().addSlow(new Task() {
            @Override
            protected void execute() {
                roomWebSender.sendRoomWebMsg(gameData.getRoomId(), null, vo.toMarsMsg(), true, onlineVersion);
            }
        });
        return vo;
    }

    /**
     * 创建游戏埋点
     */
    private void doReportEvent(SudGameData gameData) {
        CreateGameLogEvent event = new CreateGameLogEvent();
        event.setUid(gameData.getSelfUid());
        event.setIs_robot(0);
        event.setRoom_id(gameData.getRoomId());
        event.setGame_id(gameData.getGameId().toString());
        event.setGame_type(getEventGameType(gameData.getGameType()));
        event.setCost_type(gameData.getCurrency() == 0 ? 0 : (gameData.getCurrencyType() == COIN ? 1 : 2));
        event.setCost_number(gameData.getCurrency());
        event.setCtime(DateHelper.getNowSeconds());
        eventReport.track(new com.quhong.analysis.EventDTO(event));
    }

    public void asyncPushSudEvent(EventDTO dto) {
        BaseTaskFactory.getFactory().addSlow(new Task() {
            @Override
            protected void execute() {
                sudGameApi.pushEvent(dto);
            }
        });
    }

    /**
     * 退出游戏或踢出游戏
     * 玩家手动退出时aid和uid相同
     */
    public CreateSudGameVO quitGame(SudGameDTO reqDTO) {
        String uid = reqDTO.getUid();
        String aid = reqDTO.getAid();
        SudGameData gameData = sudGameDao.findData(reqDTO.getGameId());
        if (null == gameData) {
            logger.info("cannot fin gameData gameId={}", reqDTO.getGameId());
            throw new GameException(GameHttpCode.FAILURE);
        }
        if (gameData.getStatus() == SudGameConstant.GAME_PROCESSING) {
            escapeGame(gameData, uid, 1);
        } else if (gameData.getStatus() == SudGameConstant.GAME_MATCHING) {
            // 有入场费的游戏需要返回入场费
            quitAndReturnCoin(uid, aid, gameData);
        }
        return gameChange(gameData);
    }

    /**
     * 游戏中逃跑
     *
     * @param quitType 1主动逃跑 2被房间管理员踢出房间逃跑
     */
    public void escapeGame(SudGameData gameData, String uid, int quitType) {
        for (SudGamePlayerData playerData : gameData.getPlayerList()) {
            if (playerData.getUid().equals(uid)) {
                asyncPushSudEvent(EventDTO.GameEndEvent(sudGameConfig.getGameIdByGameType(gameData.getGameType()), gameData.getGameId().toString(), uid));
                playerData.setIsQuit(quitType);
                // 逃跑后可以去别的房间玩游戏
                sudGameRedis.removePlayerData(uid);
                sudGameDao.save(gameData);
                break;
            }
        }
    }

    private void quitAndReturnCoin(String uid, String aid, SudGameData gameData) {
        if (uid.equals(aid)) {
            // 主动退出
            if (gameData.getSelfUid().equals(uid)) {
                // 游戏创建者退出游戏，解散游戏，并返还游戏币
                removeGame(gameData);
            } else {
                if (gameData.getPlayerList().stream().anyMatch(a -> a.getUid().equals(aid))) {
                    // 非游戏创建者退出游戏
                    quitOrKickOut(uid, gameData);
                    // 返回入场费
                    returnGameCurrency(gameData, uid);
                } else {
                    logger.error("repeat return game fee!!! uid={}", aid);
                }
            }
        } else {
            // 被踢
            if (!gameData.getSelfUid().equals(uid)) {
                logger.info("cannot kick out player. no right to operate. uid={} aid={}", uid, aid);
                throw new GameException(HttpCode.AUTH_ERROR);
            }
            if (gameData.getPlayerList().stream().anyMatch(a -> a.getUid().equals(aid))) {
                quitOrKickOut(aid, gameData);
                // 返回入场费
                returnGameCurrency(gameData, aid);
                // 发送玩家被踢消息
                sendKickOutMsg(gameData.getRoomId(), uid, aid);
            } else {
                logger.error("repeat return game fee!!! uid={}", aid);
            }
        }
    }

    public SudGameData checkGame(String roomId) {
        String gameId = sudGameRedis.getGameIdByRoomId(roomId);
        if (StringUtils.isEmpty(gameId)) {
            return null;
        }
        return sudGameDao.findData(gameId);
    }

    /**
     * 正常结束游戏
     */
    public void finishGame(SudGameData data) {
        String gameId = data.getGameId().toString();
        // 删除redis里的游戏信息
        sudGameRedis.removeGameInfo(gameId, data.getRoomId());
        data.setStatus(SudGameConstant.GAME_FINISH);
        data.setEndTime(DateHelper.getNowSeconds());
        sudGameDao.save(data);
        data.getPlayerList().forEach(k -> {
            // 移除玩家在游戏的redis
            sudGameRedis.removePlayerData(k.getUid());
        });
        gameChange(data);
        logger.info("finish sud game. gameId={} gameType={}", data.getGameId(), data.getGameType());
    }

    /**
     * 设置玩家基本信息
     */
    private void setPlayersBasicInfo(SudGameData data) {
        if (CollectionUtils.isEmpty(data.getPlayerList())) {
            return;
        }
        for (SudGamePlayerData playerData : data.getPlayerList()) {
            ActorData actorData = mysteryService.getMysteryActor(playerData.getUid());
            if (actorData == null) {
                logger.error("Not find player info. uid={}", playerData.getUid());
                continue;
            }
            playerData.setName(actorData.getName());
            playerData.setHead(ImageUrlGenerator.generateRoomUserUrl(actorData));
            playerData.setRid(actorData.getRid());
            playerData.setGender(actorData.getFb_gender());
            playerData.setMystery(actorData.getMystery());
        }
    }

    /**
     * 构建游戏信息
     */
    private SudGameData buildSudGameData(CreateSudGameDTO reqDTO, RoomActorDetailData actorData, boolean isRobot) {
        SudGameData data = new SudGameData();
        data.setGameType(reqDTO.getGameType());
        data.setGameName(sudGameConfig.getGameNameByGameType(reqDTO.getGameType(), SLangType.ENGLISH));
        data.setGameNameAr(sudGameConfig.getGameNameByGameType(reqDTO.getGameType(), SLangType.ARABIC));
        // UMO和消消乐游戏不支持设置玩家数量(2-6)
        data.setPlayerNumber(reqDTO.getGameType() == SudGameConstant.UMO_GAME ||
                reqDTO.getGameType() == SudGameConstant.MONSTER_CRUSH_GAME ? 6 : reqDTO.getPlayerNumber());
        data.setRule(reqDTO.getRule());
        data.setStatus(SudGameConstant.GAME_MATCHING);
        data.setRoomId(reqDTO.getRoomId());
        data.setSelfUid(reqDTO.getUid());
        data.setCurrencyType(reqDTO.getCurrencyType());
        if (reqDTO.getOs() == ClientOS.IOS || AppVersionUtils.versionCheck(111, reqDTO)) {
            List<Integer> newCurrencyList = sudGameConfig.getNewCurrencyList(reqDTO.getGameType(), reqDTO.getCurrencyType());
            if (isRobot || reqDTO.getCurrency() == 0) {
                data.setCurrency(WalletUtils.diamondsToRaw(newCurrencyList.get(0)));
            } else {
                if (!newCurrencyList.contains(reqDTO.getCurrency()) || reqDTO.getCurrency() < 0) {
                    logger.error("currency param error. currencyType={} currency={}", reqDTO.getCurrencyType(), reqDTO.getCurrency());
                    throw new GameException(GameHttpCode.PARAM_ERROR);
                }
                data.setCurrency(reqDTO.getCurrency());
            }
        } else {
            data.setCurrency(WalletUtils.diamondsToRaw(sudGameConfig.getCurrencyByGameTypeAndId(reqDTO.getGameType(), reqDTO.getCurrencyId())));
        }
        data.setCreateTime(DateHelper.getNowSeconds());
        List<SudGamePlayerData> playerList = new ArrayList<>();
        playerList.add(buildSudGamePlayerData(actorData));
        data.setPlayerList(playerList);
        return data;
    }

    /**
     * 构建玩家信息
     */
    private SudGamePlayerData buildSudGamePlayerData(ActorData actorData) {
        SudGamePlayerData playerData = new SudGamePlayerData();
        playerData.setUid(actorData.getUid());
        playerData.setName(actorData.getName());
        playerData.setGender(actorData.getFb_gender());
        playerData.setHead(ImageUrlGenerator.generateRoomUserUrl(actorData));
        playerData.setRid(actorData.getRid());
        playerData.setRobot(0);
        playerData.setMystery(actorData.getMystery());
        return playerData;
    }

    private SudGamePlayerData buildSudGamePlayerData(RoomActorDetailData actorData) {
        SudGamePlayerData playerData = new SudGamePlayerData();
        playerData.setUid(actorData.getAid());
        playerData.setName(actorData.getName());
        playerData.setGender(actorData.getGender());
        playerData.setHead(actorData.getHead());
        playerData.setRid(actorData.getRid());
        playerData.setRobot(0);
        playerData.setMystery(actorData.getMystery());
        return playerData;
    }

    /**
     * 一局游戏结束，更新游戏状态
     */
    public void updateGameStatus(String gameId) {
        // 更新游戏信息
        SudGameData data = sudGameDao.findData(gameId);
        data.setStatus(SudGameConstant.GAME_MATCHING);
        sudGameDao.save(data);
        // 更新redis里的游戏信息
        SudGameInfo gameInfo = new SudGameInfo();
        BeanUtils.copyProperties(data, gameInfo);
        gameInfo.setGameId(data.getGameId().toString());
        sudGameRedis.updateSudGameInfo(gameInfo);
    }

    /**
     * 退出游戏或踢出游戏，并退还游戏币
     */
    private void quitOrKickOut(String uid, SudGameData gameData) {
        gameData.getPlayerList().removeIf(playerData -> playerData.getUid().equals(uid));
        sudGameDao.save(gameData);
        // 更新redis里的游戏信息
        SudGameInfo gameInfo = new SudGameInfo();
        BeanUtils.copyProperties(gameData, gameInfo);
        gameInfo.setGameId(gameData.getGameId().toString());
        sudGameRedis.updateSudGameInfo(gameInfo);
        sudGameRedis.removePlayerData(uid);
        logger.info("quit sud game. uid={} gameId={} gameType={}", uid, gameInfo.getGameId(), gameInfo.getGameType());
    }

    /**
     * 发送玩家被踢消息
     */
    private void sendKickOutMsg(String roomId, String uid, String aid) {
        BaseTaskFactory.getFactory().addSlow(new Task() {
            @Override
            protected void execute() {
                RoomLudoKitOutMsg msg = new RoomLudoKitOutMsg();
                msg.setAid(uid);
                roomWebSender.sendPlayerWebMsg(roomId, uid, aid, msg, true);
            }
        });
    }

    /**
     * Ludo创建游戏者解散游戏
     */
    private void removeGame(SudGameData gameData) {
        String uid = gameData.getSelfUid();
        String gameId = gameData.getGameId().toString();
        String roomId = gameData.getRoomId();
        for (SudGamePlayerData playerData : gameData.getPlayerList()) {
            // 返回入场费
            returnGameCurrency(gameData, playerData.getUid());
            sudGameRedis.removePlayerData(playerData.getUid());
        }
        gameData.setStatus(SudGameConstant.GAME_CLOSED);
        sudGameDao.save(gameData);
        SudGameInfo gameInfo = new SudGameInfo();
        BeanUtils.copyProperties(gameData, gameInfo);
        gameInfo.setGameId(gameId);
        // 删除redis里的游戏信息
        sudGameRedis.removeGameInfo(gameId, roomId);
        // 结束游戏消息
        RoomActorDetailData actorData = roomActorCache.getData(roomId, uid, false);
        sendRoomSudGameOperateMsg(actorData, gameInfo, SudGameConstant.GAME_CLOSED_MSG);
        logger.info("end sud game. uid={} gameId={} gameType={}", uid, gameId, gameData.getGameType());
    }

    /**
     * 返回入场费
     */
    public void returnGameCurrency(SudGameData gameInfo, String uid) {
        // 返还游戏币
        int currency = gameInfo.getCurrency();
        if (currency == 0) {
            return;
        }
        SudGameConfigInfo gameConfigInfo = sudGameConfig.getSudGameInfoMap().get(gameInfo.getGameType());
        if (COIN == gameInfo.getCurrencyType()) {
            boolean success = heartRecordDao.changeHeart(uid, currency, gameConfigInfo.getFeeReturnTitle(), gameConfigInfo.getFeeReturnDesc());
            if (success) {
                logger.info("sud game reward coin return uid={} coin={} gameId={}", uid, currency, gameInfo.getGameId());
            } else {
                logger.error("sud game reward coin return error uid={} coin={} gameId={}", uid, currency, gameInfo.getGameId());
            }
        } else if (DIAMOND == gameInfo.getCurrencyType()) {
            if (sudGameRedis.incPrizePoolBeans(gameInfo.getGameId().toString(), -currency) < 0) {
                sudGameRedis.setAbnormalUser(gameInfo.getGameType(), uid);
                logger.error("Not enough diamonds in the prize pool. gameId={} uid={} change={}", gameInfo.getGameId(), uid, currency);
                return;
            }
            MoneyDetailReq moneyDetailReq = new MoneyDetailReq();
            moneyDetailReq.setRandomId();
            moneyDetailReq.setUid(uid);
            moneyDetailReq.setAtype(gameConfigInfo.getFeeReturnActType());
            moneyDetailReq.setChanged(currency);
            moneyDetailReq.setTitle(gameConfigInfo.getFeeReturnTitle());
            moneyDetailReq.setDesc(gameConfigInfo.getFeeReturnDesc());
            moneyDetailReq.setRoomId(gameInfo.getRoomId());
            mqSenderService.asyncChargeDiamonds(moneyDetailReq);
            gameDiamondAlert(gameInfo.getGameId().toString(), gameInfo.getGameType(), currency);
            logger.info("sud game reward diamond return uid={} diamond={} gameId={}", uid, currency, gameInfo.getGameId());
        }
    }

    public int getEventGameType(int gameType) {
        if (gameType == SudGameConstant.UMO_GAME) {
            return EventGameTypeConstant.UMO;
        }
        if (gameType == SudGameConstant.MONSTER_CRUSH_GAME) {
            return EventGameTypeConstant.MONSTER_CRUSH;
        }
        if (gameType == SudGameConstant.DOMINO_GAME) {
            return EventGameTypeConstant.DOMINO;
        }
        return EventGameTypeConstant.LUDO;
    }

    public SudCode getGameCode(SudGameDTO reqDTO) {
        // 获取短期令牌Code，默认时长2小时
        return sudMGPAuth.getCode(reqDTO.getUid());
    }

    public String getAppServiceSignature() {
        String data = sudGameConfig.getAppId();
        byte[] key = sudGameConfig.getAppSecret().getBytes();
        HMac mac = new HMac(HmacAlgorithm.HmacMD5, key);
        return mac.digestHex(data);
    }

    public String getAuthorization(String body) {
        // 应用ID
        String appId = sudGameConfig.getAppId();
        // 请求时间戳（发送请求的时间戳）
        String timestamp = String.valueOf(System.currentTimeMillis());
        // 随机字符串 (自定义随机字符串)
        String nonce = "waho";
        // 签名串
        String signContent = String.format("%s\n%s\n%s\n%s\n", appId, timestamp, nonce, body);
        // 签名值
        HMac hMac = new HMac(HmacAlgorithm.HmacSHA1, sudGameConfig.getAppSecret().getBytes());
        String signature = hMac.digestHex(signContent);
        return String.format("Sud-Auth app_id=\"%s\",timestamp=\"%s\",nonce=\"%s\",signature=\"%s\"",
                appId, timestamp, nonce, signature);
    }

    public String addAi(SudGameDTO req) {
        SudGameData game = sudGameDao.findData(req.getGameId());
        ActorData actorData = actorDao.getMysteryActorFromCache(req.getUid());
        if (null == game || null == actorData || actorData.getRobot() != 1 || SudGameConstant.GAME_MATCHING != game.getStatus()) {
            return "false";
        }
        ApiResult<BaseVO<?>> apiResult = sudGameApi.pushEvent(EventDTO.addAi(sudGameConfig.getGameIdByGameType(game.getGameType()), req.getGameId(), actorData));
        if (apiResult.isError()) {
            logger.error("add ai error, roomId={} gameId={} msg={}", game.getRoomId(), req.getGameId(), apiResult.getData());
            return "false";
        }
        sendJoinGameMsg(game, actorData);
        return "true";
    }

    private void sendJoinGameMsg(SudGameData game, ActorData actorData) {
        RoomNotificationMsg msg = new RoomNotificationMsg();
        msg.setUid(actorData.getUid());
        msg.setUser_name(actorData.getName());
        msg.setUser_head(ImageUrlGenerator.generateRoomUserUrl(actorData));
        msg.setText(String.format(JOIN_GAME_MSG, actorData.getName(), game.getGameName()));
        msg.setText_ar(String.format(JOIN_GAME_MSG_AR, actorData.getName(), game.getGameNameAr()));
        List<HighlightTextObject> list = new ArrayList<>();
        HighlightTextObject object = new HighlightTextObject();
        object.setText(actorData.getName());
        object.setHighlightColor("#FFE200");
        list.add(object);
        msg.setHighlight_text(list);
        msg.setHighlight_text_ar(list);
        msg.setHide_head(1);
        msg.setFromRoomId(game.getRoomId());
        msg.setMystery(actorData.getMystery());
        int onlineVersion = sudGameConfig.getSudGameInfoMap().get(game.getGameType()).getOnlineVersion();
        roomWebSender.sendRoomWebMsg(game.getRoomId(), "", msg, false, onlineVersion);
    }

    public MatchingVO matching(SudGameDTO req) {
        // 校验玩家是否有未退出的游戏
        String inGameId = sudGameRedis.getPlayerData(req.getUid());
        if (null != inGameId) {
            SudGameInfo sudGameInfo = sudGameRedis.getSudGameInfo(inGameId);
            logger.info("player has in game uid={}", req.getUid());
            String gameName = sudGameConfig.getGameNameByGameType(sudGameInfo.getGameType(), req.getSlang());
            throw new GameException(GameHttpCode.PLAYER_IN_GAME_J, new Object[]{gameName});
        }
        MatchingVO vo = new MatchingVO();
        List<SudGameData> matchingByGameType = sudGameDao.findMatchingByGameType(req.getGameType());
        Collections.shuffle(matchingByGameType);
        for (SudGameData sudGameData : matchingByGameType) {
            MongoRoomData roomData = mongoRoomDao.findData(sudGameData.getRoomId());
            if (null != roomData && ObjectUtils.isEmpty(roomData.getPwd())
                    && !roomBlacklistDao.isBlock(sudGameData.getRoomId(), req.getUid())
                    && !roomKickRedis.isKick(sudGameData.getRoomId(), req.getUid())) {
                vo.setMatchedPlayers(sudGameData.getPlayerList().stream().map(SudGamePlayerData::getHead).collect(Collectors.toSet()));
                vo.setRoomId(sudGameData.getRoomId());
                vo.setGameId(sudGameData.getGameId().toString());
                break;
            }
        }
        vo.setRecentlyPlayers(sudGameDao.findRecentlyPlayer(req.getUid(), req.getGameType()));
        return vo;
    }

    public void gameDiamondAlert(String gameId, int gameType, int changed) {
        try {
            // 单款游戏平台亏损10000钻时触发告警，每增加1000触发1次。
            int platformBeansChange = sudGameRedis.incSudGameBeansChangeSum(gameType, changed);
            if (platformBeansChange >= WalletUtils.diamondsToRaw(PLATFORM_WARNING_LINE)) {
                int warnLevel = (platformBeansChange - WalletUtils.diamondsToRaw(PLATFORM_WARNING_LINE)) / WalletUtils.diamondsToRaw(11000) + 1;
                int oldWarnLevel = sudGameRedis.getSudGameWarnLevel(gameType);
                if (warnLevel > oldWarnLevel) {
                    StringBuilder desc = new StringBuilder(String.format("平台亏损钻石达%s钻\n", platformBeansChange));
                    String gameName = sudGameConfig.getSudGameInfoMap().get(gameType).getName();
                    Set<String> abnormalUserSet = sudGameRedis.getAbnormalUser(gameType);
                    for (String aid : abnormalUserSet) {
                        ActorData actorData = actorDao.getActorDataFromCache(aid);
                        desc.append(String.format("ID：%s用户%s游戏结算异常;\n", actorData != null ? actorData.getRid() : aid, gameName));
                    }
                    noticeWarn(gameName, desc.toString());
                    sudGameRedis.setSudGameWarnLevel(gameType, warnLevel);
                }
            }
        } catch (Exception e) {
            logger.error("gameDiamondAlert error. gameId={} gameType={} changed={} {}", gameId, gameType, changed, e.getMessage(), e);
        }
    }

    public void noticeWarn(String gameName, String desc) {
        String content = gameName + "游戏结算异常告警 \n"
                + ">告警名: 平台游戏钻石亏损 \n"
                + ">所属项目: Waho \n"
                + ">游戏项目: " + gameName + " \n"
                + ">预警详情: " + desc + " \n"
                + ">处理人: @赖勇奇 @谢建良";
        monitorSender.customMarkdown(ServerConfig.isProduct() ? "sud_game_play" : "waho_java_exception", content);
    }
}
