package com.quhong.sud.vo;

import com.alibaba.fastjson.annotation.JSONField;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 更新长期令牌响应
 * 注意：响应体中的字段命名格式为SNAKE_CASE，需配置spring.jackson.property-naming-strategy=SNAKE_CASE
 *
 * <AUTHOR>
 */
@NoArgsConstructor
@AllArgsConstructor
@Builder
@Data
public class UpdateSSTokenVO {

    /**
     * 重新生成的长期令牌，字段名：ss_token
     */
    @JSONField(name = "ss_token")
    private String ssToken;

    /**
     * 长期令牌SSToken的过期时间（毫秒时间戳），字段名：expire_date
     */
    @JSONField(name = "expire_date")
    private long expireDate;

}


