package com.quhong.room.mic;

import com.alibaba.fastjson.annotation.JSONField;

import java.util.List;
import java.util.concurrent.locks.ReentrantReadWriteLock;


public class RoomMic {

    private String roomId; // 房间id
    private int version; // 麦位版本号
    private List<MicInfo> micList;

    @JSONField(serialize = false)
    private long expireTime; // 对象过期时间戳，用于jvm内存释放
    @JSONField(serialize = false)
    private long lastUpdate; // 用户基础数据的最后刷新时间
    @JSONField(serialize = false)
    private ReentrantReadWriteLock lock = new ReentrantReadWriteLock();

    public void incrVersion() {
        this.version = version + 1;
    }

    public static class MicInfo {
        private int position; // 麦位位置，从0开始
        private int mute; // 麦位禁音 0未静音 1静音
        private int micLock; // 0空闲 1上锁
        // 用户信息
        private String aid; // 用户id
        private int rid; // 用户rid
        private String head; // 头像
        private String name; // 名字
        private int role; // 权限 0-普通用户 1-房主 2-管理员 3-房间会员
        private int gender; // 性别 1-用户 2-女
        // 内部字段
        @JSONField(serialize = false)
        private int upMicTime; // 上麦时间

        public void resetMic() {
            this.mute = MicConstant.MIC_MUTE_OFF;
            this.micLock = 0;
            this.upMicTime = 0;
            this.aid = null;
            this.rid = 0;
            this.head = null;
            this.name = null;
            this.role = 0;
            this.gender = 0;
        }

        public int getPosition() {
            return position;
        }

        public void setPosition(int position) {
            this.position = position;
        }

        public int getMute() {
            return mute;
        }

        public void setMute(int mute) {
            this.mute = mute;
        }

        public int getMicLock() {
            return micLock;
        }

        public void setMicLock(int micLock) {
            this.micLock = micLock;
        }

        public String getAid() {
            return aid;
        }

        public void setAid(String aid) {
            this.aid = aid;
        }

        public int getRid() {
            return rid;
        }

        public void setRid(int rid) {
            this.rid = rid;
        }

        public String getHead() {
            return head;
        }

        public void setHead(String head) {
            this.head = head;
        }

        public String getName() {
            return name;
        }

        public void setName(String name) {
            this.name = name;
        }

        public int getRole() {
            return role;
        }

        public void setRole(int role) {
            this.role = role;
        }

        public int getGender() {
            return gender;
        }

        public void setGender(int gender) {
            this.gender = gender;
        }

        public int getUpMicTime() {
            return upMicTime;
        }

        public void setUpMicTime(int upMicTime) {
            this.upMicTime = upMicTime;
        }
    }

    public String getRoomId() {
        return roomId;
    }

    public void setRoomId(String roomId) {
        this.roomId = roomId;
    }

    public int getVersion() {
        return version;
    }

    public void setVersion(int version) {
        this.version = version;
    }

    public List<MicInfo> getMicList() {
        return micList;
    }

    public void setMicList(List<MicInfo> micList) {
        this.micList = micList;
    }

    public long getExpireTime() {
        return expireTime;
    }

    public void setExpireTime(long expireTime) {
        this.expireTime = expireTime;
    }

    public long getLastUpdate() {
        return lastUpdate;
    }

    public void setLastUpdate(long lastUpdate) {
        this.lastUpdate = lastUpdate;
    }

    public ReentrantReadWriteLock getLock() {
        return lock;
    }

    public void setLock(ReentrantReadWriteLock lock) {
        this.lock = lock;
    }
}
