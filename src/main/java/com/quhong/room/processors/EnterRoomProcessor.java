package com.quhong.room.processors;

import com.alibaba.fastjson.JSON;
import com.quhong.cache.CacheMap;
import com.quhong.core.concurrency.BaseTaskFactory;
import com.quhong.core.concurrency.tasks.Task;
import com.quhong.core.config.ServerConfig;
import com.quhong.core.timers.DelayTask;
import com.quhong.core.timers.TimerService;
import com.quhong.data.ActorData;
import com.quhong.image.ImageUrlGenerator;
import com.quhong.mongo.dao.ActorDao;
import com.quhong.mongo.dao.LiveRoomDevoteDao;
import com.quhong.msg.ProtoHeader;
import com.quhong.msg.room.*;
import com.quhong.mysql.dao.RoomBlacklistDao;
import com.quhong.mysql.dao.VipInfoDao;
import com.quhong.net.sender.PlayerMsgSender;
import com.quhong.redis.WhitelistRedis;
import com.quhong.room.EnterRoomCheckService;
import com.quhong.room.EnterRoomContent;
import com.quhong.room.RoomService;
import com.quhong.room.cache.RoomActorCache;
import com.quhong.room.data.EnterUserData;
import com.quhong.room.data.RoomActorData;
import com.quhong.room.data.RoomActorDetailData;
import com.quhong.room.redis.RoomKickRedis;
import com.quhong.room.redis.RoomPlayerRedis;
import com.quhong.room.redis.RoomRedis;
import com.quhong.room.rooms.NormalRoom;
import com.quhong.room.rooms.Room;
import com.quhong.utils.RoomUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.util.ObjectUtils;
import org.springframework.util.StringUtils;


@Component
public class EnterRoomProcessor extends AbstractRoomProcessor {
    private static final Logger logger = LoggerFactory.getLogger(EnterRoomProcessor.class);

    @Autowired
    protected RoomService roomService;
    @Autowired
    private RoomRedis roomRedis;
    @Autowired
    private RoomPlayerRedis roomPlayerRedis;
    @Autowired
    private ActorDao actorDao;
    @Autowired
    private RoomMsgProcessor roomMsgProcessor;
    @Autowired
    private EnterRoomContent enterRoomContent;
    @Autowired
    private VipInfoDao vipInfoDao;
    @Autowired
    private LeaveRoomProcessor leaveRoomProcessor;
    private final CacheMap<String, Boolean> kickCacheMap;
    @Autowired
    private PlayerMsgSender playerMsgSender;
    @Autowired
    private RoomKickRedis roomKickRedis;
    @Autowired
    private RoomActorCache roomActorCache;
    @Autowired
    private EnterRoomCheckService checkService;
    @Autowired
    private LiveRoomDevoteDao liveRoomDevoteDao;
    @Autowired
    private WhitelistRedis whitelistRedis;
    @Autowired
    private RoomBlacklistDao roomBlacklistDao;

    public EnterRoomProcessor() {
        kickCacheMap = new CacheMap<>(60 * 1000);
    }


    public void addKickCache(String roomId, String uid) {
        kickCacheMap.cacheData(getKickKey(roomId, uid), true);
    }

    public boolean isKick(String roomId, String uid) {
        String key = getKickKey(roomId, uid);
        Boolean ret = kickCacheMap.getData(key);
        if (ret != null) {
            return ret;
        }
        ret = roomKickRedis.isKick(roomId, uid);
        kickCacheMap.cacheData(key, ret);
        return ret;
    }

    private String getKickKey(String roomId, String uid) {
        return roomId + "_" + uid;
    }

    public boolean sendKickMsg(Room room, String uid, boolean official) {
        String fromUid = roomKickRedis.getFromKickUid(room.getRoomId(), uid);
        if (StringUtils.isEmpty(fromUid)) {
            logger.error("send kick msg error. roomId={} uid={}", room.getRoomId(), uid);
            return false;
        }
        ActorData actorData = actorDao.getActorDataFromCache(fromUid);
        KickFromRoomPushMsg msg = new KickFromRoomPushMsg();
        msg.setFrom_uid(actorData.getUid());
        msg.setFrom_name(actorData.getName());
        msg.setFrom_head(actorData.getHead());
        msg.setOfficial(official ? 1 : 0);
        msg.setType(roomBlacklistDao.isBlock(room.getRoomId(), uid) ? 1 : 0);
        if (msg.getType() == 1) {
            msg.setTipsEn("You have been blacklisted from the room and cannot enter.");
            msg.setTipsAr("تم حظرك من الغرفة ولا يمكنك الدخول إليها.");
        } else {
            msg.setTipsEn("You have been kicked out of the room and cannot enter for 24 hours.");
            msg.setTipsAr("تم طردك من الغرفة ولا يمكنك الدخول خلال 24 ساعة.");
        }
        msg.getProtoHeader().setRoomID(room.getRoomId());
        msg.getProtoHeader().setFromUid(fromUid);
        if (ServerConfig.isNotProduct()) {
            logger.info("KickFromRoomPushMsg={}", JSON.toJSONString(msg));
        }
        playerMsgSender.sendMsg(uid, msg);
        TimerService.getService().addDelay(new DelayTask(3000) {
            @Override
            protected void execute() {
                // 保障性发送
                zegoApi.kickOut(room.getRoomId(), uid);
            }
        });
        return true;
    }

    public void enterRoom(Room room, EnterRoomMsg msg) {
        String uid = msg.getUid();
        logger.info("actor enter room. content={} cmd={} roomId={} uid={}", msg.getContent(), msg.getCmd(), room.getRoomId(), uid);
        boolean containMe;
        containMe = StringUtils.isEmpty(msg.getContent());
        doEnterRoom(room, uid, true, containMe, false);
    }

    public void enterRoomFromWeb(Room room, String uid, boolean force) {
        logger.info("actor enter room from web. roomId={} uid={} isForce={}", room.getRoomId(), uid, force);
        doEnterRoom(room, uid, true, !force, force);
    }

    /**
     * 心跳进入房间
     *
     * @param force 是否强制进入房间（不进行被踢等判断）
     */
    public void enterRoomFromHeart(Room room, String uid, boolean force) {
        logger.info("actor enter room from heart or login. roomId={} uid={} force={}", room.getRoomId(), uid, force);
        doEnterRoom(room, uid, false, false, force);
    }

    public void enterRoomFromMsg(Room room, String uid, int cmd) {
        logger.info("actor enter room from cmd. cmd={} roomId={} uid={}", cmd, room.getRoomId(), uid);
        doEnterRoom(room, uid, false, false, false);
    }

    public void enterRoomByRecover(Room room, String uid) {
        logger.info("actor enter room by recover. roomId={} uid={}", room.getRoomId(), uid);
        doEnterRoom(room, uid, false, false, false, false);
    }

    protected void doEnterRoom(Room room, String uid, boolean sendMsg, boolean containMe, boolean force) {
        doEnterRoom(room, uid, sendMsg, containMe, force, true);
    }

    /**
     * 进入房间
     *
     * @param sendMsg    是否需要2002进房间消息
     * @param containMe  消息包含自己
     * @param force      忽略进房间权限校验，直接进入房间
     * @param checkLeave 重启room时enterRoomByRecover可能会造成死循环
     */
    protected void doEnterRoom(Room room, String uid, boolean sendMsg, boolean containMe, boolean force, boolean checkLeave) {
        try {
            // 检查是否有其他房间，如果有，则离开老房间
            if (checkLeave) {
                checkLeaveRoom(room, uid);
            }
            // 通过roomService进房间的接口已经校验过权限
            if (!force) {
                if (isKick(room.getRoomId(), uid)) {
                    // 已经被踢掉
                    logger.error("actor has been kicked. roomId={} uid={}", room.getRoomId(), uid);
                    sendKickMsg(room, uid, false);
                    return;
                }
                if (!checkService.checkCanEnter(room.getRoomId(), uid)) {
                    RoomChangePwdPushMsg msg = new RoomChangePwdPushMsg();
                    msg.setFromUid(RoomUtils.getRoomHostId(room.getRoomId()));
                    msg.setRoomId(room.getRoomId());
                    playerMsgSender.sendMsg(uid, msg);
                    logger.error("enter room check failed. roomId={} uid={}", room.getRoomId(), uid);
                    return;
                }
            }
            ActorData actorData = actorDao.getMysteryActorFromCache(uid);
            RoomActorData roomActorData = new RoomActorData();
            roomActorData.setUid(uid);
            roomActorData.setRoomId(room.getRoomId());
            roomActorData.setActorData(actorData);
            roomActorData.updateHeart();
            boolean robot = actorData.getRobot() == 1;
            roomActorData.setRobot(robot);
            boolean invisibilityEnterRoom = whitelistRedis.isInvisibilityEnterRoom(room.getRoomId(), uid);
            roomActorData.setInvisibleUser(invisibilityEnterRoom);
            room.addActor(roomActorData);
            // 更新到redis
            roomPlayerRedis.enterRoom(room.getRoomId(), uid, robot);
            BaseTaskFactory.getFactory().addSlow(new Task() {
                @Override
                protected void execute() {
                    if (invisibilityEnterRoom) {
                        // 隐身进房，不触发消息
                        return;
                    }
                    if (RoomUtils.isLiveRoom(room.getRoomId())) {
                        if (sendMsg) {
                            // 直播房新版进房间消息
                            sendEnterRoomMsg(room, actorData, containMe);
                        }
                    } else {
                        if (sendMsg) {
                            sendPushMsg(room, actorData, containMe);
                        }
                        // 发送用户房间内改变推送
                        doSendRoomChange(room, actorData);
                    }
                }
            });
            logger.info("actor finish enter room from web. roomId={} uid={}", room.getRoomId(), uid);
        } catch (Exception e) {
            logger.error("enter room error. {}", e.getMessage(), e);
        }
    }

    private void sendEnterRoomMsg(Room room, ActorData fromActor, boolean containMe) {
        RoomActorDetailData detailData = roomActorCache.getData(room.getRoomId(), fromActor.getUid(), false);
        EnterRoomPushMsg pushMsg = generateEnterRoomPushMsg(room, fromActor, detailData);
        int onlineNumber = (int) room.getActorMap().values().stream().filter(k -> !k.isInvisibleUser()).count();
        pushMsg.setOnlineNumber(room.getActorMap().containsKey(RoomUtils.getRoomHostId(room.getRoomId())) ? onlineNumber - 1 : onlineNumber);
        pushMsg.setRoomDevote((int) liveRoomDevoteDao.getActorDevote(room.getRoomId(), fromActor.getUid()));
        roomMsgProcessor.sendMsgToAll(room, fromActor.getUid(), pushMsg, containMe, true, null);
    }

    /**
     * 检查并移除老的房间
     */
    private void checkLeaveRoom(Room room, String uid) {
        String roomId = room.getRoomId();
        String oldRoomId = roomPlayerRedis.getActorRoomStatus(uid);
        if ((!ObjectUtils.isEmpty(oldRoomId)) && (!oldRoomId.equals(roomId))) {
            // 如果老房间存在，且不等于新的房间，老房间退出
            // 旧的房间存在，从房间退出
            Room oldRoom = roomService.getRoom(oldRoomId);
            logger.info("actor leave from old room by heat beat. oldRoomId={} newRoomId={} uid={}", oldRoomId, roomId, uid);
            if (oldRoom != null) {
                leaveRoomProcessor.leaveFromEnterOtherRoom(oldRoom, uid);
            }
        }
    }

    private void sendPushMsg(Room room, ActorData fromActor, boolean containMe) {
        EnterRoomPushMsg pushMsg = generateEnterRoomPushMsg(room, fromActor);
        int onlineNumber = (int) room.getActorMap().values().stream().filter(k -> !k.isInvisibleUser()).count();
        pushMsg.setOnlineNumber(onlineNumber);
        // 进入房间消息做重复性发送
        roomMsgProcessor.sendRepeatedMsgToAll(room, fromActor.getUid(), pushMsg, containMe, null);
    }

    private EnterRoomPushMsg generateEnterRoomPushMsg(Room room, ActorData actorData) {
        return generateEnterRoomPushMsg(room, actorData, roomActorCache.getData(room.getRoomId(), actorData.getUid(), false));
    }

    private EnterRoomPushMsg generateEnterRoomPushMsg(Room room, ActorData actorData, RoomActorDetailData detailData) {
        String uid = actorData.getUid();
        EnterRoomPushMsg pushMsg = new EnterRoomPushMsg();
        ProtoHeader protoHeader = pushMsg.getProtoHeader();
        protoHeader.setRoomID(room.getRoomId());
        protoHeader.setFromUid(uid);
        protoHeader.setMsgId(pushMsg.getMsgId());
        EnterUserData userData = enterRoomContent.generateUserData(detailData, room.getRoomId(), actorData);
        String content = JSON.toJSONString(userData);
        pushMsg.setContent(content);
        pushMsg.setUname(detailData.toUNameObject());
        return pushMsg;
    }

    public Room createRoom(String roomId) {
        Room room = roomService.recoverRoom(roomId);
        if (room == null) {
            room = new NormalRoom(roomId);
        }
        // 添加到service进行管理
        roomService.addRoom(room);
        // 保存到redis
        roomRedis.addRoom(room.getRoomData());
        logger.info("create room. roomId={}", roomId);
        return room;
    }

    private void doSendRoomChange(Room room, ActorData actorData) {
        RoomUserChangePUshMsg msg = new RoomUserChangePUshMsg();
        msg.setType(RoomUserChangePUshMsg.ENTER);
        msg.setVip_level(vipInfoDao.getIntVipLevel(actorData.getUid()));
        msg.setHead(ImageUrlGenerator.generateRoomUserUrl(actorData));
        int onlineNumber = (int) room.getActorMap().values().stream().filter(k -> !k.isInvisibleUser()).count();
        msg.setOnlineNumber(onlineNumber);
        msg.setIdentify(msg.getVip_level() > 0 ? 1 : 0);
        msg.setName(actorData.getName());
        msg.setMystery(actorData.getMystery());
        roomMsgProcessor.sendMsgToAll(room, actorData.getUid(), msg, true, null);
    }
}
