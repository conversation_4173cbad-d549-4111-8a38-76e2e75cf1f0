package com.quhong.room.processors;

import com.quhong.msg.obj.MsgInfoObject;
import com.quhong.redis.LivePkRedis;
import com.quhong.room.cache.RoomActorCache;
import com.quhong.room.data.RoomActorDetailData;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

@Component
public class RoomTextContent {

    @Autowired
    private RoomActorCache actorCache;
    @Autowired
    private LivePkRedis livePkRedis;

    public void fillTextMsg(String roomId, String uid, MsgInfoObject infoObject) {
        RoomActorDetailData detailData = actorCache.getData(roomId, uid, false);
        infoObject.setUanme2(detailData.toUNameObject());
        infoObject.setFromLabel(livePkRedis.getActorMsgLabel(uid));
    }
}
