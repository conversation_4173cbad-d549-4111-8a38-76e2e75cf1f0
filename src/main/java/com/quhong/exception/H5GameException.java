package com.quhong.exception;

import com.quhong.enums.HttpCode;

public class H5GameException extends GameException {
    private static final long serialVersionUID = -5731379974482062032L;

    private HttpCode httpCode;
    // 国际化额外参数
    private Object[] args;
    private Object data;

    public H5GameException() {
    }

    public H5GameException(HttpCode httpCode) {
        this.httpCode = httpCode;
    }

    public H5GameException(HttpCode httpCode, Object data) {
        this.httpCode = httpCode;
        this.data = data;
    }

    public H5GameException(HttpCode httpCode, Object data, Object[] args) {
        this.httpCode = httpCode;
        this.data = data;
        this.args = args;
    }

    public H5GameException(HttpCode httpCode, Object[] args) {
        this.httpCode = httpCode;
        this.args = args;
    }

    public H5GameException(int code, String msg) {
        httpCode = new HttpCode();
        this.httpCode.setCode(code);
        this.httpCode.setMsg(msg);
    }

    public HttpCode getHttpCode() {
        return httpCode;
    }

    public void setHttpCode(HttpCode httpCode) {
        this.httpCode = httpCode;
    }

    public Object[] getArgs() {
        return args;
    }

    public void setArgs(Object[] args) {
        this.args = args;
    }

    public Object getData() {
        return data;
    }

    public void setData(Object data) {
        this.data = data;
    }
}
