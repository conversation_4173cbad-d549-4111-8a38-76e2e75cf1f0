package com.quhong.elasticsearch.data;


import org.springframework.data.annotation.Id;
import org.springframework.data.elasticsearch.annotations.Document;

import java.math.BigDecimal;

/**
 * 主播魅力值流水记录
 * 1接收房间礼物
 * 2接收私信礼物
 * 3美金提现失败返还
 * 4接收公会成员转账
 * 5运营系统操作魅力值
 * 6主播佣金
 * 7子代理佣金
 * 8主播任务奖励 (被拆分成了11和12)
 * 9活动奖励
 * 10邀请用户奖励
 * 11 主播语聊房任务奖励
 * 12 主播直播房任务奖励
 * -1转换为美金
 * -2每月清零
 * -3退出公会清零
 * -4转账给公会长
 * -5转换钻石
 * -6拒绝成员退出公会扣除费用
 * -7美金提现
 * -8主播给用户或币商充值
 * -9退款扣除
 */
@Document(indexName = "charm_log_es")
public class CharmLogDataEs {
    @Id
    private String id;
    private Integer familyId; // 家族id
    private String uid; // 主播uid
    private String aid; // 打赏方uid
    private String roomId; // 房间id
    private BigDecimal change; // 余额变动
    private BigDecimal balance; // 当前余额
    /**
     * @see com.quhong.enums.CharmLogTypeEnum
     */
    private Integer logType; // 记录类型
    private Integer reward; // 打赏时的打赏数额（接受礼物才有值）
    private String title; // 交易标题
    private String titleAr; // 交易标题阿语
    private String desc; // 交易详情
    private String descAr; // 交易详情阿语
    private Integer ctime; // 交易时间


    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public String getUid() {
        return uid;
    }

    public void setUid(String uid) {
        this.uid = uid;
    }

    public String getAid() {
        return aid;
    }

    public void setAid(String aid) {
        this.aid = aid;
    }

    public String getRoomId() {
        return roomId;
    }

    public void setRoomId(String roomId) {
        this.roomId = roomId;
    }

    public BigDecimal getChange() {
        return change;
    }

    public void setChange(BigDecimal change) {
        this.change = change;
    }

    public BigDecimal getBalance() {
        return balance;
    }

    public void setBalance(BigDecimal balance) {
        this.balance = balance;
    }

    public Integer getLogType() {
        return logType;
    }

    public void setLogType(Integer logType) {
        this.logType = logType;
    }

    public Integer getReward() {
        return reward;
    }

    public void setReward(Integer reward) {
        this.reward = reward;
    }

    public String getTitle() {
        return title;
    }

    public void setTitle(String title) {
        this.title = title;
    }

    public String getDesc() {
        return desc;
    }

    public void setDesc(String desc) {
        this.desc = desc;
    }

    public Integer getCtime() {
        return ctime;
    }

    public void setCtime(Integer ctime) {
        this.ctime = ctime;
    }

    public Integer getFamilyId() {
        return familyId;
    }

    public void setFamilyId(Integer familyId) {
        this.familyId = familyId;
    }

    public String getTitleAr() {
        return titleAr;
    }

    public void setTitleAr(String titleAr) {
        this.titleAr = titleAr;
    }

    public String getDescAr() {
        return descAr;
    }

    public void setDescAr(String descAr) {
        this.descAr = descAr;
    }
}
