package com.quhong.mysql.mapper.waho;

import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

public interface UserNoticeInfoMapper {

    @Select({"SELECT count(id) FROM t_user_notice_info where status=1 and aid=#{aid}"})
    int getNoticeCount(@Param("aid") String aid);

    @Select({"update t_user_notice_info set status=0 where aid=#{aid}"})
    void updateCleanNoticeCount(@Param("aid") String aid);
}
