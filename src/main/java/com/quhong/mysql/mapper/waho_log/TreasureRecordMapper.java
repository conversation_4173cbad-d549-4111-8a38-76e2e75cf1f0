package com.quhong.mysql.mapper.waho_log;

import com.quhong.mysql.data.TreasureRecordData;
import com.quhong.mysql.mapper.ShardingMapper;
import org.apache.ibatis.annotations.Insert;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.util.List;

public interface TreasureRecordMapper extends ShardingMapper {

    @Insert("insert into t_treasure_record_${tableSuffix} (`uid`,`pool_key`, `ctime`) values (#{item.uid}, #{item.poolKey}, #{item.ctime})")
    void insert(@Param("tableSuffix") String tableSuffix, @Param("item") TreasureRecordData treasureRecordData);

    @Select({
            "<script>",
            "SELECT uid, pool_key, ctime FROM ( ",
            "<foreach collection='suffixList' item='tableSuffix' separator='UNION ALL'>",
            "SELECT uid, pool_key, ctime FROM t_treasure_record_${tableSuffix} ",
            "WHERE uid=#{uid}",
            "</foreach>",
            " ) AS stat",
            "ORDER BY ctime DESC ",
            "LIMIT #{size} ",
            "OFFSET #{start}",
            "</script>"
    })
    List<TreasureRecordData> getRecords(@Param("uid") String uid, @Param("start") int start,@Param("size") int size,
                                   @Param("suffixList") List<String> suffixList);


    @Select({
            "<script>",
            "SELECT uid, pool_key, ctime FROM ( ",
            "<foreach collection='suffixList' item='tableSuffix' separator='UNION ALL'>",
            "SELECT uid, pool_key, ctime FROM t_treasure_record_${tableSuffix}",
            "</foreach>",
            " ) AS stat",
            "ORDER BY ctime DESC ",
            "LIMIT #{size} ",
            "</script>"
    })
    List<TreasureRecordData> getLastRecords(@Param("size") int size, @Param("suffixList") List<String> suffixList);

    @Select({
            "<script>",
            "SELECT uid, pool_key, ctime FROM ( ",
            "<foreach collection='suffixList' item='tableSuffix' separator='UNION ALL'>",
            "SELECT uid, pool_key, ctime FROM t_treasure_record_${tableSuffix}",
            "WHERE uid=#{uid}",
            "</foreach>",
            " ) AS stat",
            "LIMIT 1 ",
            "</script>"
    })
    TreasureRecordData getUserRecordOne(@Param("uid") String uid, @Param("suffixList") List<String> suffixList);


}
