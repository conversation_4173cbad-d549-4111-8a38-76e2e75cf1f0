package com.quhong.mysql.mapper.waho_log;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.quhong.mysql.data.FamilyDailyDetailData;
import com.quhong.mysql.data.FamilyDailyDetailTotalData;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2023/12/21
 */
public interface FamilyDailyDetailMapper extends BaseMapper<FamilyDailyDetailData> {

    @Select({
            "<script>",
            "select IFNULL(sum(new_anchor_num),0) as new_anchor_num_total, ",
            "IFNULL(sum(working_anchor_num),0) as working_anchor_num_total, ",
            "IFNULL(sum(paying_anchor_num),0) as paying_anchor_num_total, ",
            "IFNULL(sum(room_charm_income),0) as room_charm_income_total, ",
            "IFNULL(sum(msg_charm_income),0) as msg_charm_income_total, ",
            "IFNULL(sum(game_cost_diamonds),0) as game_cost_diamonds_total ",
            "from family_daily_detail where <![CDATA[ ctime >= #{startTime} AND ctime < #{endTime} ]]>",
            "and family_id in ",
            "<foreach item='item' collection='familyIdList' open='(' separator=',' close=')'>",
            "#{item}",
            "</foreach>",
            "</script>"})
    FamilyDailyDetailTotalData getTotalData(@Param("familyIdList") List<Integer> familyIdList, @Param("startTime") int startTime, @Param("endTime") int endTime);

    @Select({
            "<script>",
            "select count(1) from ( select family_id ",
            "from family_daily_detail where <![CDATA[ ctime >= #{startTime} AND ctime < #{endTime} ]]>",
            "and family_id in ",
            "<foreach item='item' collection='familyIdList' open='(' separator=',' close=')'>",
            "#{item}",
            "</foreach>",
            " group by family_id having IFNULL(sum(active_anchor_num),0) >= 15 ) as stat",
            "</script>"})
    Integer getActiveFamilyNumTotal(@Param("familyIdList") List<Integer> familyIdList, @Param("startTime") int startTime, @Param("endTime") int endTime);
}
