package com.quhong.mysql.mapper.waho_log;

import com.quhong.mysql.data.DailyTaskManager;
import com.quhong.mysql.mapper.ShardingMapper;
import org.apache.ibatis.annotations.Insert;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;
import org.apache.ibatis.annotations.Update;

import java.util.List;

public interface DailyTaskManagerMapper extends ShardingMapper {

    @Select("SELECT * FROM t_daily_task_record_${tableSuffix} where task_id=99 and uid=#{uid} and date_key=#{dateKey}")
    DailyTaskManager getMissionTaskByUid(@Param("tableSuffix") String tableSuffix, @Param("uid") String uid, @Param("dateKey") String dateKey);

    @Select("<script>" +
            "SELECT * FROM t_daily_task_record_${tableSuffix} where uid=#{uid} and date_key=#{dateKey} and task_id in " +
            "<foreach collection = 'taskList' open = '(' item = 'item' separator = ',' close = ')'>" +
            "#{item}" +
            "</foreach>" +
            "</script>")
    List<DailyTaskManager> getTaskByIds(@Param("tableSuffix") String tableSuffix, @Param("uid") String uid, @Param("dateKey") String dateKey, @Param("taskList") List<Integer> taskList);

    @Select("<script>" +
            "SELECT * FROM t_daily_task_record_${tableSuffix} where uid=#{uid} and date_key=#{dateKey} and task_id=#{taskId} " +
            "</script>")
    DailyTaskManager getTaskById(@Param("tableSuffix") String tableSuffix, @Param("uid") String uid, @Param("dateKey") String dateKey, @Param("taskId") Integer taskId);

    @Insert("INSERT INTO t_daily_task_record_${tableSuffix} (uid, date_key, task_id, task_value, award_status, c_time, m_time) VALUES (#{item.uid}, #{item.dateKey}, #{item.taskId}, #{item.taskValue}, #{item.awardStatus}, #{item.cTime}, #{item.mTime})")
    void insert(@Param("tableSuffix") String tableSuffix, @Param("item") DailyTaskManager data);

    @Update("UPDATE t_daily_task_record_${tableSuffix} SET uid = #{item.uid}, date_key = #{item.dateKey}, task_id = #{item.taskId}, task_value = #{item.taskValue}, award_status = #{item.awardStatus}, c_time = #{item.cTime}, m_time = #{item.mTime} WHERE id = #{item.id}")
    void update(@Param("tableSuffix") String tableSuffix, @Param("item") DailyTaskManager data);
}
