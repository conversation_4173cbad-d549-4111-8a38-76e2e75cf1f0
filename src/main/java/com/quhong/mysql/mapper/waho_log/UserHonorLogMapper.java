package com.quhong.mysql.mapper.waho_log;

import com.quhong.mysql.data.UserHonorLogData;
import com.quhong.mysql.mapper.ShardingMapper;
import org.apache.ibatis.annotations.Insert;
import org.apache.ibatis.annotations.Param;

public interface UserHonorLogMapper extends ShardingMapper {

    @Insert("insert into t_user_honor_log_${tableSuffix} (`uid`,`recharge_type`, `before_honor_level`, `after_honor_level`, `before_bean`, `change_bean`, `after_bean`, `ctime`) values" +
            "(#{item.uid}, #{item.rechargeType}, #{item.beforeHonorLevel}, #{item.afterHonorLevel}, #{item.beforeBean}, #{item.changeBean}, #{item.afterBean} ,#{item.ctime})")
    void insert(@Param("tableSuffix") String tableSuffix, @Param("item") UserHonorLogData userHonorLogData);

}
