package com.quhong.mysql.mapper.waho_log;

import com.quhong.mysql.data.MysqlMsgRecordData;
import com.quhong.mysql.mapper.ShardingMapper;
import org.apache.ibatis.annotations.Delete;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.util.List;

public interface MsgRecordMapper extends ShardingMapper {
    List<MysqlMsgRecordData> getList(@Param("tableSuffix") String tableSuffix, @Param("msgIndex") String msgIndex, @Param("uid") String uid, @Param("startIndex") int startIndex, @Param("pageSize") int pageSize);

    MysqlMsgRecordData getData(@Param("tableSuffix") String tableSuffix, @Param("msgId") String msgId);

    void insert(@Param("tableSuffix") String tableSuffix, @Param("item") MysqlMsgRecordData recordData);

    void update(@Param("tableSuffix") String tableSuffix, @Param("item") MysqlMsgRecordData recordData);

    MysqlMsgRecordData getLastData(@Param("tableSuffix") String tableSuffix, @Param("msgIndex") String msgIndex, @Param("uid") String uid);

    void updateDeleteAll(@Param("tableSuffix") String tableSuffix, @Param("msgIndex") String msgIndex, @Param("uid") String uid);

    @Select("select count(1) from t_msg_record_${tableSuffix} where `timestamp`<#{timestampToDelete}")
    long selectCountForClean(@Param("tableSuffix") String tableSuffix, @Param("timestampToDelete") long timestampToDelete);

    @Delete("delete from t_msg_record_${tableSuffix} where `timestamp`<#{timestampToDelete} limit #{limit}")
    long clean(@Param("tableSuffix") String tableSuffix, @Param("timestampToDelete") long timestampToDelete, @Param("limit") int limit);

    @Select("select count(1) from t_msg_record_${tableSuffix} where msgType in (21,27) and `timestamp`<#{timestampToDelete}")
    long selectShareMsgCountForClean(@Param("tableSuffix") String tableSuffix, @Param("timestampToDelete") long timestampToDelete);

    @Delete("delete from t_msg_record_${tableSuffix} where msgType in (21,27) and `timestamp`<#{timestampToDelete} limit #{limit}")
    long cleanShareMsg(@Param("tableSuffix") String tableSuffix, @Param("timestampToDelete") long timestampToDelete, @Param("limit") int limit);

    @Select("select count(1) from t_msg_record_${tableSuffix} where from_delete=1 and to_delete=1")
    long selectInvalidCount(@Param("tableSuffix") String tableSuffix);

    @Delete("delete from t_msg_record_${tableSuffix} where from_delete=1 and to_delete=1 limit #{limit}")
    long cleanInvalid(@Param("tableSuffix") String tableSuffix, @Param("limit") int limit);
}
