package com.quhong.mysql.dao;

import com.quhong.mongo.dao.MongoRoomDao;
import com.quhong.mongo.data.Comment;
import com.quhong.mongo.data.MomentData;
import com.quhong.mongo.data.MongoRoomData;
import com.quhong.mysql.data.MomentOpLogData;
import com.quhong.mysql.mapper.waho_log.MomentOpLogMapper;
import jakarta.annotation.Resource;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;
import org.springframework.util.ObjectUtils;

@Component
public class MomentOpDao {

    private static final Logger logger = LoggerFactory.getLogger(MomentOpDao.class);

    @Resource
    private MomentOpLogMapper momentOpLogMapper;
    @Resource
    private MongoRoomDao mongoRoomDao;


    /**
     * @param opType 操作类型，1置顶，2取消置顶，3拉黑朋友圈，4取消拉黑朋友圈，5拉黑评论，6取消拉黑评论
     */
    public void addMomentOpLog(String opName, MomentData moment, Comment comment, int opType) {
        if (null == moment && null == comment) {
            logger.info("addMomentOpLog param error");
            return;
        }
        MomentOpLogData logData = new MomentOpLogData();
        logData.setName(opName);
        logData.setOpType(opType);
        logData.setCtime((int) (System.currentTimeMillis() / 1000));
        if (null != moment) {
            logData.setAid(moment.getUid());
            logData.setMid(moment.get_id().toString());
            logData.setPublishTime(moment.getC_time());
            logData.setContent(moment.getText());
            if (!ObjectUtils.isEmpty(moment.getImgs())) {
                logData.setImg(moment.getImgs().get(0).getOrigin());
            }
            if (null != moment.getQuote() && 5 == moment.getQuote().getType()) {
                // 分享房间处理
                MongoRoomData roomData = mongoRoomDao.findData(moment.getQuote().getAction());
                if (null != roomData) {
                    logData.setContent(roomData.getName());
                    logData.setImg(roomData.getHead());
                }
            }
        }
        if (null != comment) {
            logData.setAid(comment.getCommentator());
            logData.setMid(comment.getMoment_id());
            logData.setCid(comment.get_id().toString());
            logData.setPublishTime(comment.getC_time());
            logData.setContent(comment.getContent());
        }
        momentOpLogMapper.insert(logData);
    }
}
