package com.quhong.mysql.dao;

import com.quhong.config.AsyncConfig;
import com.quhong.core.utils.DateHelper;
import com.quhong.data.MsgPageData;
import com.quhong.enums.MsgType;
import com.quhong.mysql.data.MysqlMsgRecordData;
import com.quhong.mysql.mapper.waho_log.ArchiveMsgRecordMapper;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.Collections;
import java.util.Date;
import java.util.List;

@Component
public class ArchiveMsgRecordDao extends MonthShardingDao<ArchiveMsgRecordMapper> {
    private static final Logger logger = LoggerFactory.getLogger(ArchiveMsgRecordDao.class);

    private static final List<Integer> ARCHIVE_MSG_TYPE = List.of(MsgType.TEXT, MsgType.AUDIO, MsgType.IMAGE, MsgType.VIDEO);

    private static final int MONTH_OFFSET = -2;

    public ArchiveMsgRecordDao() {
        super("t_archive_msg_record");
    }

    @Async(AsyncConfig.ASYNC_TASK)
    public void insert(MysqlMsgRecordData data) {
        try {
            if (!ARCHIVE_MSG_TYPE.contains(data.getMsgType())) {
                return;
            }
            String suffix = DateHelper.ARABIAN.getTableSuffix(new Date(data.getTimestamp()));
            createTable(suffix);
            tableMapper.insert(suffix, data);
        } catch (Exception e) {
            logger.error("insert msg record error. msgId={} {}", data.getMsgId(), e.getMessage(), e);
        }
    }

    public void updateStatus(String msgId, int status, long ctime) {
        String suffix = DateHelper.ARABIAN.getTableSuffix(new Date(ctime));
        tableMapper.updateStatus(suffix, msgId, status);
    }

    private List<MysqlMsgRecordData> findList(String msgIndex, String uid, MsgPageData pageData) {
        try {
            List<String> suffixList = getTableSuffixList(MONTH_OFFSET);
            List<String> retList = new ArrayList<>();
            for (String suffix : suffixList) {
                if (checkExist(suffix)) {
                    retList.add(suffix);
                }
            }
            return tableMapper.getList(retList, msgIndex, uid, pageData.getStart(), pageData.getPageSize());
        } catch (Exception e) {
            logger.error("get msg record list error. msgIndex={} {}", msgIndex, e.getMessage(), e);
        }
        return Collections.emptyList();
    }
}
