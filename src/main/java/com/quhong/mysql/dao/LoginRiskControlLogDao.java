package com.quhong.mysql.dao;

import com.quhong.mysql.data.LoginRiskControlLogData;
import com.quhong.mysql.mapper.waho_log.LoginRiskControlLogMapper;
import jakarta.annotation.Resource;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * @date 2024/11/13
 */
@Component
public class LoginRiskControlLogDao {

    public final Logger logger = LoggerFactory.getLogger(getClass());

    @Resource
    private LoginRiskControlLogMapper baseMapper;

    public void insert(LoginRiskControlLogData data) {
        try {
            baseMapper.insert(data);
        } catch (Exception e) {
            logger.error("insertLoginRiskControlLogData. {}", e.getMessage());
        }
    }
}
