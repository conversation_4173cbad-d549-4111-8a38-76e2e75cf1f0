package com.quhong.mysql.dao;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.quhong.mysql.data.ShootDrawData;
import com.quhong.mysql.mapper.waho_log.ShootDrawMapper;
import jakarta.annotation.Resource;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * @date 2024/10/18
 */
@Component
public class ShootDrawDao {

    @Resource
    private ShootDrawMapper shootDrawMapper;

    public void insert(ShootDrawData data) {
        shootDrawMapper.insert(data);
    }

    public IPage<ShootDrawData> selectPage(String uid, int page, int pageSize) {
        IPage<ShootDrawData> iPage = new Page<>(page, pageSize);
        QueryWrapper<ShootDrawData> query = Wrappers.query();
        query.lambda().eq(ShootDrawData::getUid, uid).orderByDesc(ShootDrawData::getCtime);
        return shootDrawMapper.selectPage(iPage, query);
    }
}
