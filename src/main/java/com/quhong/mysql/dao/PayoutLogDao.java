
package com.quhong.mysql.dao;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.quhong.core.utils.DateHelper;
import com.quhong.mysql.data.PayoutLogData;
import com.quhong.mysql.mapper.waho_log.PayoutLogMapper;
import com.quhong.operation.constant.PayoutStatus;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.concurrent.TimeUnit;

@Component
public class PayoutLogDao extends ServiceImpl<PayoutLogMapper, PayoutLogData> {

    private static final Logger logger = LoggerFactory.getLogger(PayoutLogDao.class);

    public List<PayoutLogData> selectListForAlert() {
        QueryWrapper<PayoutLogData> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("payout_status", PayoutStatus.PENDING);
        queryWrapper.lt("ctime", (int) (DateHelper.getNowSeconds() - TimeUnit.HOURS.toSeconds(64)));
        return super.list(queryWrapper);
    }
}
