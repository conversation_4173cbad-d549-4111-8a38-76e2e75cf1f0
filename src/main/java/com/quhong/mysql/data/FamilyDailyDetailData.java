package com.quhong.mysql.data;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;

/**
 * <AUTHOR>
 * @date 2023/12/21
 */
@TableName("family_daily_detail")
public class FamilyDailyDetailData {

    @TableId(type = IdType.AUTO)
    private Integer id;

    private int familyId; // 公会id

    private int date; // 日期

    private int newAnchorNum; // 新签约主播数

    private int activeAnchorNum; // 活跃主播数量

    private int workingAnchorNum; // 开播主播数

    private int payingAnchorNum; // 付费主播数

    private long roomCharmIncome; // 房间总魅力值收入

    private long msgCharmIncome; // 私信总魅力值收入

    private long gameCostDiamonds; // 总游戏投入钻石数

    private int ctime; // 创建时间

    public Integer getId() {
        return id;
    }

    public void setId(Integer id) {
        this.id = id;
    }

    public int getFamilyId() {
        return familyId;
    }

    public void setFamilyId(int familyId) {
        this.familyId = familyId;
    }

    public int getDate() {
        return date;
    }

    public void setDate(int date) {
        this.date = date;
    }

    public int getNewAnchorNum() {
        return newAnchorNum;
    }

    public void setNewAnchorNum(int newAnchorNum) {
        this.newAnchorNum = newAnchorNum;
    }

    public int getActiveAnchorNum() {
        return activeAnchorNum;
    }

    public void setActiveAnchorNum(int activeAnchorNum) {
        this.activeAnchorNum = activeAnchorNum;
    }

    public int getWorkingAnchorNum() {
        return workingAnchorNum;
    }

    public void setWorkingAnchorNum(int workingAnchorNum) {
        this.workingAnchorNum = workingAnchorNum;
    }

    public int getPayingAnchorNum() {
        return payingAnchorNum;
    }

    public void setPayingAnchorNum(int payingAnchorNum) {
        this.payingAnchorNum = payingAnchorNum;
    }

    public long getRoomCharmIncome() {
        return roomCharmIncome;
    }

    public void setRoomCharmIncome(long roomCharmIncome) {
        this.roomCharmIncome = roomCharmIncome;
    }

    public long getMsgCharmIncome() {
        return msgCharmIncome;
    }

    public void setMsgCharmIncome(long msgCharmIncome) {
        this.msgCharmIncome = msgCharmIncome;
    }

    public long getGameCostDiamonds() {
        return gameCostDiamonds;
    }

    public void setGameCostDiamonds(long gameCostDiamonds) {
        this.gameCostDiamonds = gameCostDiamonds;
    }

    public int getCtime() {
        return ctime;
    }

    public void setCtime(int ctime) {
        this.ctime = ctime;
    }
}
