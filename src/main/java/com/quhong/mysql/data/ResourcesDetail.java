package com.quhong.mysql.data;


import java.util.Set;

/**
 * MySQL流水对象
 */
public class ResourcesDetail {

    private String uid;
    private String resId;
    private Integer resType;  // 资源类型 1 勋章 2 麦位框 3 坐骑 4 背包礼物  5 房间锁 6 聊天气泡 7 声波纹 8 浮屏
    private Integer days;    // 资源下发天数，-1为永久
    private Integer actionType; //  1 仅下发资源 2 下发且佩戴 3 仅佩戴 4 删除资源 5 过期删除 6 过期时，重新获得
    private String desc = ""; // 描述 比如 “某某活动第一名”
    private Integer mTime; // 触发时间戳，单位秒
    private Integer handleTime; //处理的时间戳，单位秒
    private Integer endTime; // 资源到期的时间点，单位秒
    private Integer gainType = 0; // 0 自然天流失 1 使用次数减少
    private Integer seconds; // 测试环境可能用 资源下秒数，-1为永久
    private Integer getWay; //  获得途径  0:平台抽奖、心心兑换、购买获得  1: 运营活动获得
    private Integer num; // 获得的资源数量，目前只有背包礼物有
    private Set<String> removeList; // 批量删除勋章列表 预留字段
    private String roomId;
    private String itemsSourceDetail; //数数埋点游戏title字段，具体业务由产品定
    private String name; // 资源的名称
    private Integer beans; //资源的售价
    private int emptyWearType; //是否无佩戴，则佩戴
    private Integer canChangeBeautifulRid;  // 是否无佩戴，则佩戴
    private int setEndTime = 1;
    private int resLevel; // 资源等级，如vip等级
    /**
     * 靓号的等级
     * 仅资源类型为靓号资源有效(TYPE_BEAUTIFUL_RID)
     */
    private int alphaLevel;

    /**
     * getWay 为他人购买赠予时，赠予用户的uid
     */
    private String fromUid;

    /**
     * 0 非加锁资源正常流程处理 1 增加加锁资源 2 解锁资源，自己使用 3 解锁资源，他人使用
     * 是vip奖励是， 1加锁可赠送， 2加锁不可赠送
     */
    private Integer lockType;  // 非数据库字段

    private int officialMsg;   // 是否官方消息推送 ,非数据库字段
    private long rechargeBeans;

    public String getResId() {
        return resId;
    }

    public void setResId(String resId) {
        this.resId = resId;
    }

    public Integer getResType() {
        return resType;
    }

    public void setResType(Integer resType) {
        this.resType = resType;
    }

    public Integer getDays() {
        return days == null ? 0 : days;
    }

    public void setDays(Integer days) {
        this.days = days;
    }


    public String getDesc() {
        return desc;
    }

    public void setDesc(String desc) {
        this.desc = desc;
    }

    public String getUid() {
        return uid;
    }

    public void setUid(String uid) {
        this.uid = uid;
    }

    public Integer getmTime() {
        return mTime;
    }

    public void setmTime(Integer mTime) {
        this.mTime = mTime;
    }

    public Integer getActionType() {
        return actionType;
    }

    public void setActionType(Integer actionType) {
        this.actionType = actionType;
    }

    public Integer getHandleTime() {
        return handleTime;
    }

    public void setHandleTime(Integer handleTime) {
        this.handleTime = handleTime;
    }

    public Integer getEndTime() {
        return endTime;
    }

    public void setEndTime(Integer endTime) {
        this.endTime = endTime;
    }

    public Integer getGainType() {
        return gainType == null ? 0 : gainType;
    }

    public void setGainType(Integer gainType) {
        this.gainType = gainType;
    }

    public Integer getSeconds() {
        return seconds;
    }

    public void setSeconds(Integer seconds) {
        this.seconds = seconds;
    }

    public Integer getGetWay() {
        return getWay != null ? getWay : 1;
    }

    public void setGetWay(Integer getWay) {
        this.getWay = getWay;
    }

    public Integer getNum() {
        return num;
    }

    public void setNum(Integer num) {
        this.num = num;
    }

    public Set<String> getRemoveList() {
        return removeList;
    }

    public void setRemoveList(Set<String> removeList) {
        this.removeList = removeList;
    }

    public String getRoomId() {
        return roomId;
    }

    public void setRoomId(String roomId) {
        this.roomId = roomId;
    }

    public String getItemsSourceDetail() {
        return itemsSourceDetail;
    }

    public void setItemsSourceDetail(String itemsSourceDetail) {
        this.itemsSourceDetail = itemsSourceDetail;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public Integer getBeans() {
        return beans;
    }

    public void setBeans(Integer beans) {
        this.beans = beans;
    }

    public int getEmptyWearType() {
        return emptyWearType;
    }

    public void setEmptyWearType(int emptyWearType) {
        this.emptyWearType = emptyWearType;
    }

    public int getResLevel() {
        return resLevel;
    }

    public void setResLevel(int resLevel) {
        this.resLevel = resLevel;
    }

    public int getAlphaLevel() {
        return alphaLevel;
    }

    public void setAlphaLevel(int alphaLevel) {
        this.alphaLevel = alphaLevel;
    }

    public Integer getCanChangeBeautifulRid() {
        return canChangeBeautifulRid;
    }

    public void setCanChangeBeautifulRid(Integer canChangeBeautifulRid) {
        this.canChangeBeautifulRid = canChangeBeautifulRid;
    }

    public int getSetEndTime() {
        return setEndTime;
    }

    public void setSetEndTime(int setEndTime) {
        this.setEndTime = setEndTime;
    }

    public String getFromUid() {
        return fromUid;
    }

    public void setFromUid(String fromUid) {
        this.fromUid = fromUid;
    }

    public Integer getLockType() {
        return lockType;
    }

    public void setLockType(Integer lockType) {
        this.lockType = lockType;
    }

    public int getOfficialMsg() {
        return officialMsg;
    }

    public void setOfficialMsg(int officialMsg) {
        this.officialMsg = officialMsg;
    }

    public long getRechargeBeans() {
        return rechargeBeans;
    }

    public void setRechargeBeans(long rechargeBeans) {
        this.rechargeBeans = rechargeBeans;
    }

    @Override
    public String toString() {
        return "ResourcesDetail{" +
                "uid='" + uid + '\'' +
                ", resId='" + resId + '\'' +
                ", resType=" + resType +
                ", days=" + days +
                ", actionType=" + actionType +
                ", desc='" + desc + '\'' +
                ", mTime=" + mTime +
                ", handleTime=" + handleTime +
                ", endTime=" + endTime +
                ", gainType=" + gainType +
                ", seconds=" + seconds +
                ", getWay=" + getWay +
                ", num=" + num +
                ", removeList=" + removeList +
                ", roomId='" + roomId + '\'' +
                ", itemsSourceDetail='" + itemsSourceDetail + '\'' +
                ", name='" + name + '\'' +
                ", beans=" + beans +
                ", emptyWearType=" + emptyWearType +
                ", alphaLevel=" + alphaLevel +
                ", canChangeBeautifulRid=" + canChangeBeautifulRid +
                ", setEndTime=" + setEndTime +
                ", fromUid='" + fromUid + '\'' +
                ", lockType=" + lockType +
                ", officialMsg=" + officialMsg +
                '}';
    }
}
