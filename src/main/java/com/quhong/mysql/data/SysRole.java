package com.quhong.mysql.data;

import com.baomidou.mybatisplus.annotation.*;

/**
 * 角色
 */
@TableName("sys_role")
public class SysRole {

    /**
     * 主键id
     */
    @TableId(type = IdType.AUTO)
    private Integer id;

    /**
     * 角色名
     */
    @TableField(condition = SqlCondition.LIKE)
    private String name;

    /**
     * 备注
     */
    private String remark;

    public Integer getId() {
        return id;
    }

    public void setId(Integer id) {
        this.id = id;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getRemark() {
        return remark;
    }

    public void setRemark(String remark) {
        this.remark = remark;
    }
}
