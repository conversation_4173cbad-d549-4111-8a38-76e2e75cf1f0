package com.quhong.mysql.data;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;

/**
 * <AUTHOR>
 * @date 2022/8/23
 */
@TableName("t_tn_user_uploadlog")
public class TnUserUploadlogData {

    @TableId(type = IdType.AUTO)
    private Integer rid;

    /**
     * 用户uid
     */
    private String uid;

    /**
     * 申述原因
     */
    private String msg;

    /**
     * 创建时间
     */
    private Integer ctime;

    /**
     * 设备id
     */
    private String tn_id;

    /**
     * 0 安卓 1 ios
     */
    private Integer os;

    /**
     * 注册时间
     */
    private Integer rtime;

    public Integer getRid() {
        return rid;
    }

    public void setRid(Integer rid) {
        this.rid = rid;
    }

    public String getUid() {
        return uid;
    }

    public void setUid(String uid) {
        this.uid = uid;
    }

    public String getMsg() {
        return msg;
    }

    public void setMsg(String msg) {
        this.msg = msg;
    }

    public Integer getCtime() {
        return ctime;
    }

    public void setCtime(Integer ctime) {
        this.ctime = ctime;
    }

    public String getTn_id() {
        return tn_id;
    }

    public void setTn_id(String tn_id) {
        this.tn_id = tn_id;
    }

    public Integer getOs() {
        return os;
    }

    public void setOs(Integer os) {
        this.os = os;
    }

    public Integer getRtime() {
        return rtime;
    }

    public void setRtime(Integer rtime) {
        this.rtime = rtime;
    }
}
