package com.quhong.mysql.slave_mapper.data;

public class UserRelationBehavior {
    private int id;
    private int date_num;
    private String uid;
    private String aid;
    /**
     * 关注follow、取消关注unfollow、相互关注followed、加好友add_friend、删除好友delfriend、拉黑block_user、取消拉黑unblock_user
     */
    private String action;
    private int times; // 次数，以天为单位
    private int uid_is_new; // 0 老用户  1:新用户
    private int ctime;
    private int os;
    private String package_name;
    private String channel;

    public int getId() {
        return id;
    }

    public void setId(int id) {
        this.id = id;
    }

    public int getDate_num() {
        return date_num;
    }

    public void setDate_num(int date_num) {
        this.date_num = date_num;
    }

    public String getUid() {
        return uid;
    }

    public void setUid(String uid) {
        this.uid = uid;
    }

    public String getAid() {
        return aid;
    }

    public void setAid(String aid) {
        this.aid = aid;
    }

    public String getAction() {
        return action;
    }

    public void setAction(String action) {
        this.action = action;
    }

    public int getTimes() {
        return times;
    }

    public void setTimes(int times) {
        this.times = times;
    }

    public int getUid_is_new() {
        return uid_is_new;
    }

    public void setUid_is_new(int uid_is_new) {
        this.uid_is_new = uid_is_new;
    }

    public int getCtime() {
        return ctime;
    }

    public void setCtime(int ctime) {
        this.ctime = ctime;
    }

    public int getOs() {
        return os;
    }

    public void setOs(int os) {
        this.os = os;
    }

    public String getPackage_name() {
        return package_name;
    }

    public void setPackage_name(String package_name) {
        this.package_name = package_name;
    }

    public String getChannel() {
        return channel;
    }

    public void setChannel(String channel) {
        this.channel = channel;
    }
}
