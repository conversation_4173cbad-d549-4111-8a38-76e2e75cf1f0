package com.quhong.mysql.slave_mapper.waho_log;

import com.quhong.mysql.mapper.ShardingMapper;
import com.quhong.operation.share.mysql.MysqlMsgRecordData;
import org.apache.ibatis.annotations.Param;

import java.util.List;

public interface ArchiveMsgRecordMapper extends ShardingMapper {
    List<MysqlMsgRecordData> getList(@Param("suffixList") List<String> suffixList, @Param("msgIndex") String msgIndex, @Param("uid") String uid, @Param("startIndex") int startIndex, @Param("pageSize") int pageSize);

    MysqlMsgRecordData getData(@Param("tableSuffix") String tableSuffix, @Param("msgId") String msgId);

    void insert(@Param("tableSuffix") String tableSuffix, @Param("item") MysqlMsgRecordData recordData);

    MysqlMsgRecordData getLastData(@Param("tableSuffix") String tableSuffix, @Param("msgIndex") String msgIndex, @Param("uid") String uid);

    List<MysqlMsgRecordData> getHistoryMsgList(@Param("tableSuffix") String tableSuffix, @Param("uid") String uid, @Param("msgIndex") String msgIndex, @Param("endTime") long endTime, @Param("length") int length);

    List<MysqlMsgRecordData> getMsgRecordList(@Param("tableSuffixList") List<String> tableSuffixList, @Param("msgIndex") String msgIndex, @Param("startTime") long startTime, @Param("endTime") long endTime, @Param("offset") int offset, @Param("pageSize") int pageSize);

    int getMsgRecordCount(@Param("tableSuffixList") List<String> tableSuffixList, @Param("msgIndex") String msgIndex, @Param("startTime") long startTime, @Param("endTime") long endTime);

}