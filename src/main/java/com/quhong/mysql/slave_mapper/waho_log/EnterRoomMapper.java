package com.quhong.mysql.slave_mapper.waho_log;

import com.quhong.mysql.mapper.ShardingMapper;
import com.quhong.operation.share.data.AggStatData;
import com.quhong.operation.share.mysql.EnterRoom;
import com.quhong.operation.share.tool.TotalVO;
import com.quhong.operation.share.vo.EnterRoomStatVO;
import org.apache.ibatis.annotations.Param;

import java.util.List;
import java.util.Set;

/**
 * <AUTHOR>
 * @date 2020/8/4
 */
public interface EnterRoomMapper extends ShardingMapper {

    TotalVO roomActorOnlineTime(@Param("tableSuffix") String tableSuffix,
                                @Param("startTime") Integer startTime,
                                @Param("endTime") Integer endTime,
                                @Param("roomId") String roomId);

    List<String> newActorInRoomPerson(@Param("tableSuffix") String tableSuffix,
                                      @Param("startTime") Integer startTime,
                                      @Param("endTime") Integer endTime,
                                      @Param("roomId") String roomId);

    Integer newActorInRoomCount(@Param("tableSuffix") String tableSuffix,
                                @Param("startTime") Integer startTime,
                                @Param("endTime") Integer endTime,
                                @Param("roomId") String roomId);

    Integer actorJoinRoomCount(@Param("tableSuffix") String tableSuffix,
                               @Param("startTime") Integer startTime,
                               @Param("endTime") Integer endTime,
                               @Param("userId") String userId);

    List<String> actorJoinRoomNum(@Param("tableSuffix") String tableSuffix,
                                  @Param("startTime") Integer startTime,
                                  @Param("endTime") Integer endTime,
                                  @Param("userId") String userId);

    List<String> getRoomActorList(@Param("tableSuffix") String tableSuffix,
                                  @Param("startTime") Integer startTime,
                                  @Param("endTime") Integer endTime,
                                  @Param("roomId") String roomId);

    List<EnterRoom> getEnterRoomUserId(@Param("tableSuffix") String tableSuffix,
                                       @Param("startUid") String startUid,
                                       @Param("endUid") String endUid,
                                       @Param("startTime") Integer startTime,
                                       @Param("endTime") Integer endTime,
                                       @Param("rookieStatus") Integer rookieStatus);

    List<Integer> personEnterRoom(@Param("tableSuffix") String tableSuffix,
                                  @Param("uidList") List<String> uidList,
                                  @Param("startTime") Integer startTime,
                                  @Param("endTime") Integer endTime);

    Integer isRegisterInRoom(@Param("tableSuffix") String tableSuffix,
                             @Param("userId") String userId,
                             @Param("rookieStatus") Integer rookieStatus,
                             @Param("startTime") Integer startTime,
                             @Param("endTime") Integer endTime);

    /**
     * 获取进入迎新房用户
     *
     * @param tableSuffix 表名后缀
     * @param startTime   开始时间
     * @param endTime     结尾时间
     * @return 用户uid和os信息
     */
    List<EnterRoom> inWelcomeRoomActor(@Param("tableSuffix") String tableSuffix,
                                       @Param("startTime") Integer startTime,
                                       @Param("endTime") Integer endTime);

    /**
     * 获取进入迎新放用户数
     *
     * @param os
     * @param tableSuffix
     * @param startTime
     * @param endTime
     * @return
     */
    List<EnterRoom> selectRookieUserCount(@Param("tableSuffix") String tableSuffix,
                                          @Param("os") int os,
                                          @Param("startTime") Integer startTime,
                                          @Param("endTime") Integer endTime);

    /**
     * 获取进入房间用户数
     *
     * @param tableSuffix
     * @param startTime
     * @param endTime
     * @return 进入房间用户数
     */
    List<EnterRoom> selectUserCount(@Param("tableSuffix") String tableSuffix,
                                    @Param("startTime") Integer startTime,
                                    @Param("endTime") Integer endTime,
                                    @Param("newUserUidSet") Set<String> newUserUidSet);


    /**
     * 获取升序的进房记录：ctime排序
     *
     * @param tableSuffix
     * @param startTime
     * @param endTime
     * @return
     */
    List<EnterRoom> selectRooms(
            @Param("tableSuffix") String tableSuffix,
            @Param("startTime") Integer startTime,
            @Param("endTime") Integer endTime,
            @Param("roomId") String roomId
    );

    EnterRoomStatVO enterRoomStat(@Param("tableSuffixList") List<String> tableSuffixList,
                                  @Param("startTime") int startTime,
                                  @Param("endTime") int endTime,
                                  @Param("os") Integer os);

    List<String> enterRoomUid(@Param("tableSuffixList") List<String> tableSuffixList,
                              @Param("startTime") int startTime,
                              @Param("endTime") int endTime);

    List<String> enterRoomUidByRoomId(@Param("tableSuffixList") List<String> tableSuffixList,
                              @Param("roomId") String roomId,
                              @Param("startTime") int startTime,
                              @Param("endTime") int endTime);

    List<AggStatData> userDayRoomStats(
            @Param("tableSuffix") String tableSuffix,
            @Param("uidSet") Set<String> uidSet,
            @Param("startTime") int startTime,
            @Param("endTime") int endTime,
            @Param("os") int os,
            @Param("roomType") int roomType
    );

    List<AggStatData> dayRoomStats(
            @Param("tableSuffix") String tableSuffix,
            @Param("uidSet") Set<String> uidSet,
            @Param("startTime") int startTime,
            @Param("endTime") int endTime,
            @Param("os") int os,
            @Param("roomType") int roomType
    );

    List<String> enterRoomUser(@Param("tableSuffix") String tableSuffix,
                              @Param("startTime") int startTime,
                              @Param("endTime") int endTime);

    List<String> enterRookieRoomNewUsers(
            @Param("tableSuffix") String tableSuffix,
            @Param("ridSet") Set<String> ridSet,
            @Param("startTime") int startTime,
            @Param("endTime") int endTime
    );

    List<AggStatData> enterRookieRoomNewUsersTime(
            @Param("tableSuffixList") List<String> tableSuffixList,
            @Param("startTime") int startTime,
            @Param("endTime") int endTime,
            @Param("ridSet") Set<String> ridSet,
            @Param("low") int low,
            @Param("hight") int hight
    );

}
