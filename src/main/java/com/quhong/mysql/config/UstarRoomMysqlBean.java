package com.quhong.mysql.config;

import com.baomidou.mybatisplus.extension.spring.MybatisSqlSessionFactoryBean;
import org.mybatis.spring.annotation.MapperScan;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.jdbc.datasource.DataSourceTransactionManager;

@MapperScan(value = "com.quhong.mysql.mapper.waho_room", sqlSessionFactoryRef = "waho_room_sqlSession")
@Configuration
public class UstarRoomMysqlBean extends BaseMysqlBean {
    public static final String JDBC_PRE = "jdbc";
    public static final String DATABASE = "waho_room";

    public static final String WAHO_TRANSACTION = "waho_room_transaction";

    @Bean(name = "waho_room_sqlSession")
    public MybatisSqlSessionFactoryBean sqlSessionFactory() throws Exception {
        return createSqlSessionFactoryBean(JDBC_PRE, DATABASE, DATABASE);
    }

    @Bean(name = WAHO_TRANSACTION)
    public DataSourceTransactionManager transactionManager() {
        return createTransactionManager(JDBC_PRE, DATABASE, DATABASE);
    }
}
