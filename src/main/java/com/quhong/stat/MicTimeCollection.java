package com.quhong.stat;

import com.quhong.core.utils.DateHelper;
import com.quhong.mysql.dao.MicTimeDao;
import com.quhong.mysql.data.MicTimeData;
import com.quhong.redis.RoomMicRedis;
import com.quhong.service.EventService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

import jakarta.annotation.Resource;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Map;

@Component
public class MicTimeCollection {
    private static final Logger logger = LoggerFactory.getLogger(MicTimeCollection.class);

    @Resource
    private RoomMicRedis roomMicRedis;
    @Resource
    private MicTimeDao micTimeDao;
    @Resource
    private EventService eventService;

    /**
     * 北京时间：08:05
     */
    @Scheduled(cron = "0 5 0 * * ?")
    private void logData() {
        logMicTime();
    }

    private void logMicTime() {
        long ctime = DateHelper.ARABIAN.getDayOffset(-1);
        String statDate = DateHelper.ARABIAN.formatDateInDay(new Date(ctime));
        Map<String, Integer> micTimeMap = roomMicRedis.getMicTimeMap(statDate);
        logger.info("start log mic time, statDate={}, size={}", statDate, micTimeMap.size());
        List<MicTimeData> list = new ArrayList<>();
        for (Map.Entry<String, Integer> entry : micTimeMap.entrySet()) {
            try {
                String[] ids = entry.getKey().split("_");
                String uid = ids[0];
                String roomId = ids[1];
                list.add(new MicTimeData(uid, roomId, entry.getValue(), statDate, Integer.parseInt(statDate.replace("-", ""))));
                if (list.size() >= 100) {
                    micTimeDao.insertList(list, (int) (ctime / 1000));
                    list = new ArrayList<>();
                }
                // 增加数数事件
                // eventService.roomMicTimeEventTrack(roomId, uid, statDate, entry.getValue());
            } catch (Exception e) {
                logger.error("log mic time error. key={} {}", entry.getKey(), e.getMessage(), e);
            }
        }
        try {
            if (list.size() > 0) {
                micTimeDao.insertList(list, (int) (ctime / 1000));
            }
        } catch (Exception e) {
            logger.error("log mic time error. list size={} {}", list.size(), e.getMessage(), e);
        }
    }
}
