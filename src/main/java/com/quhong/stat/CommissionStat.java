package com.quhong.stat;

import com.quhong.core.date.DateSupport;
import com.quhong.core.utils.DateHelper;
import com.quhong.mysql.dao.CommissionStatDao;
import com.quhong.mysql.data.CommissionStatData;
import jakarta.annotation.Resource;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;

@Component
public class CommissionStat {

    private static final Logger logger = LoggerFactory.getLogger(CommissionStat.class);

    private static final List<Integer> LOG_TYPE_LIST = List.of(6, 7);

    @Resource
    private CommissionStatDao commissionStatDao;

    @Scheduled(cron = "5 0 21 * * ?")
    public void stat() {
        // 昨日数据
        String yesterday = DateSupport.ARABIAN.getStrYesterday();
        int nowSeconds = DateHelper.getNowSeconds();
        try {
            logger.info("start stat commission yesterday={}", yesterday);
            for (int logType : LOG_TYPE_LIST) {
                commissionStatDao.getHasCommissionUser(yesterday, logType).forEach(uid -> {
                    logger.info("stat commission uid={} logType={}", uid, logType);
                    // 1. 从redis中获取数据
                    Map<String, Double> commissionMap = commissionStatDao.getCommissionMapFromRedis(uid, yesterday, logType);
                    // 2. 写入mysql
                    List<CommissionStatData> dataList = new ArrayList<>();
                    for (Map.Entry<String, Double> entry : commissionMap.entrySet()) {
                        CommissionStatData data = new CommissionStatData();
                        data.setUid(entry.getKey());
                        data.setLogType(logType);
                        data.setAid(entry.getKey());
                        data.setStatDate(Integer.parseInt(yesterday.replace("-", "")));
                        data.setCommission(BigDecimal.valueOf(entry.getValue()));
                        dataList.add(data);
                    }
                    commissionStatDao.batchInsert(dataList);
                });
            }
        } catch (Exception e) {
            logger.error("stat commission error yesterday={}", yesterday, e);
        }
        logger.info("finish stat commission data yesterday={} seconds={}", yesterday, DateHelper.getNowSeconds() - nowSeconds);
    }
}
