package com.quhong.stat;

import com.quhong.analysis.EventDTO;
import com.quhong.analysis.EventReport;
import com.quhong.analysis.RoomActiveEvent;
import com.quhong.core.utils.DateHelper;
import com.quhong.room.redis.RoomActiveRedis;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

import jakarta.annotation.Resource;
import java.util.Date;
import java.util.Map;

@Component
public class RoomActiveCollection {
    private static final Logger logger = LoggerFactory.getLogger(RoomActiveCollection.class);

    @Resource
    private RoomActiveRedis roomActiveRedis;
    @Resource
    private EventReport eventReport;

    /**
     * 每天 00:15执行入库昨日数据
     */
    @Scheduled(cron = "0 15 0 * * ?")
    private void roomActiveStat() {
        // 昨日数据
        String yesterday = DateHelper.DEFAULT.getYesterdayStr(new Date());
        int nowSeconds = DateHelper.getNowSeconds();
        try {
            logger.info("start stat room active yesterday={}", yesterday);
            Map<Object, Object> roomActiveTimeMap = roomActiveRedis.getActiveTimeMap(yesterday);
            for (Map.Entry<Object, Object> entry : roomActiveTimeMap.entrySet()) {
                try {
                    String roomId = String.valueOf(entry.getKey());
                    int activeTime = Integer.parseInt(String.valueOf(entry.getValue()));
                    eventReport.track(new EventDTO(new RoomActiveEvent(roomId, activeTime, yesterday), yesterday));
                } catch (Exception e) {
                    logger.error("log data by day error. key={} value={}", entry.getKey(), entry.getValue(), e);
                }
            }
        } catch (Exception e) {
            logger.error("stat room active error yesterday={}", yesterday, e);
        }
        logger.info("finish stat room active data yesterday={} seconds={}", yesterday, DateHelper.getNowSeconds() - nowSeconds);
    }
}
