package com.quhong.consumer;

import com.alibaba.fastjson.JSON;
import com.quhong.constant.AnchorTaskConstant;
import com.quhong.core.concurrency.tasks.Task;
import com.quhong.mq.CommonData;
import com.quhong.service.AnchorTaskService;
import com.quhong.service.InviteAnchorTaskService;
import com.quhong.task.TaskFactory;
import jakarta.annotation.Resource;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.amqp.core.Message;
import org.springframework.amqp.rabbit.annotation.RabbitListener;
import org.springframework.stereotype.Component;
import org.springframework.util.StringUtils;

/**
 * <AUTHOR>
 * @date 2024/4/15
 */
@Component
public class AnchorTaskMqConsumer {

    private final Logger logger = LoggerFactory.getLogger(getClass());

    @Resource
    private AnchorTaskService anchorTaskService;
    @Resource
    private InviteAnchorTaskService inviteAnchorTaskService;

    @RabbitListener(queues = AnchorTaskConstant.QUEUE)
    public void handleMessage(Message message) {
        try {
            TaskFactory.getFactory().addPush(new Task() {
                @Override
                protected void execute() {
                    String json = new String(message.getBody());
                    CommonData commonData = JSON.parseObject(json, CommonData.class);
                    if (!StringUtils.hasLength(commonData.getUid()) || !StringUtils.hasLength(commonData.getItem())) {
                        logger.error("mq msg param error. uid={} key={}", commonData.getUid(), commonData.getItem());
                        return;
                    }
                    if (AnchorTaskConstant.UP_MIC.equals(commonData.getItem())) {
                        anchorTaskService.anchorUpMicTask(commonData.getRoomId(), commonData.getUid(), commonData.getValue());
                        inviteAnchorTaskService.inviteAnchorUpMicTask(commonData.getRoomId(), commonData.getUid(), commonData.getValue());
                    }
                }
            });
        } catch (Exception e) {
            logger.error("handle message error. message body={}", new String(message.getBody()), e);
        }
    }
}
