package com.quhong.robot;

import com.quhong.chains.room.CreateRoomStrategy;
import com.quhong.chains.room.TurnTableCreateStrategy;
import com.quhong.chains.room.TurnTableJoinStrategy;
import com.quhong.core.utils.SpringUtils;
import com.quhong.data.CreateRoomData;
import com.quhong.data.RobotActorData;
import com.quhong.data.RobotConfig;
import com.quhong.services.RobotTaskService;
import com.quhong.services.task.TurnTableCreateTask;
import com.quhong.services.task.TurnTableJoinTask;

public class TurnTableCreateRobot extends AbstractRobot<TurnTableCreateTask> {
    private CreateRoomData createRoomData;

    public TurnTableCreateRobot(RobotConfig robotConfig, TurnTableCreateTask robotTask,  RobotActorData robotData) {
        super(robotConfig, robotTask, robotData);
    }

    @Override
    protected void doStart() {
        // 创建房间策略
        this.addStrategy(new CreateRoomStrategy(this));
        // 创建转盘
        this.addStrategy(new TurnTableCreateStrategy(this));
    }

    @Override
    protected void doAfterEnterRoom(){
        TurnTableCreateStrategy strategy = getStrategy(TurnTableCreateStrategy.class);
        if(strategy != null){
            strategy.afterEnterRoom();
        }
        RobotTaskService taskService = SpringUtils.getBean(RobotTaskService.class);
        // 添加到加入任务的task中
        TurnTableJoinTask joinTask = (TurnTableJoinTask)taskService.getRobotTask(RobotTaskService.TURN_TABLE_JOIN_TASK);
        if(joinTask != null){
            joinTask.addRoomId(this.roomData.getRoomId());
        }
    }

    public void setCreateRoomData(CreateRoomData createRoomData) {
        this.createRoomData = createRoomData;
    }

    public CreateRoomData getCreateRoomData() {
        return createRoomData;
    }
}
