package com.quhong.services;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.quhong.core.timers.LoopTask;
import com.quhong.core.timers.TimerService;
import com.quhong.executors.http.SystemHandler;
import com.quhong.http.HttpResult;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import jakarta.annotation.PostConstruct;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.concurrent.ThreadLocalRandom;

@Component
public class RoomListService {
    private static final Logger logger = LoggerFactory.getLogger(RoomListService.class);

    private static final int TICK_INTERVAL = 5 * 60 * 1000;
    private static final int HIGH_ROOM_COUNT = 20;

    @Autowired
    private SystemHandler systemHandler;

    private List<RoomDataRsp> roomList;
    private List<RoomDataRsp> highCountRoomList;
    private Map<String, RoomDataRsp> roomDataMap;

    @PostConstruct
    public void postInit(){
        roomList = new ArrayList<>();
        TimerService.getService().addDelay(new LoopTask(TICK_INTERVAL) {
            @Override
            protected void execute() {
                reqRoomList();
            }
        });
        reqRoomList();
    }

    private void reqRoomList(){
        systemHandler.requestRoomList(0, data -> {
            if(data.isError()){
                logger.error("request room List error. status={}", data.getStatus());
                return;
            }
            HttpResult httpResult = data.getBody();
            if(httpResult.isError()){
                logger.error("request room List error. code={}", httpResult.getCode());
                return;
            }
            Map<String, RoomDataRsp> map = new HashMap<>();
            List<RoomDataRsp> list = new ArrayList<>();
            List<RoomDataRsp> highCountList = new ArrayList<>();
            JSONArray jsonArray = (JSONArray)httpResult.getJSONData();
            for(Object jsonObject : jsonArray){
                RoomDataRsp roomDataRsp = ((JSONObject)jsonObject).toJavaObject(RoomDataRsp.class);
                list.add(roomDataRsp);
                map.put(roomDataRsp.getRoomId(), roomDataRsp);
                if(roomDataRsp.getUserCount() >= HIGH_ROOM_COUNT){
                    highCountList.add(roomDataRsp);
                }
            }
            roomList = list;
            roomDataMap = map;
            highCountRoomList = highCountList;
        });
    }

    public String randomRoomId(){
        int max = roomList.size();
        if(max == 0){
            return null;
        }
        int index = ThreadLocalRandom.current().nextInt(max);
        return roomList.get(index).getRoomId();
    }

    public List<RoomDataRsp> getRoomList() {
        return roomList;
    }

    public List<RoomDataRsp> getHighCountRoomList(){
        return highCountRoomList;
    }

    public RoomDataRsp getRoomData(String uid){
        return roomDataMap.get(uid);
    }

    public static class RoomDataRsp{
        private String roomId;
        private int userCount;
        private int robotCount;

        public RoomDataRsp(){

        }

        public String getRoomId() {
            return roomId;
        }

        public void setRoomId(String roomId) {
            this.roomId = roomId;
        }

        public int getUserCount() {
            return userCount;
        }

        public void setUserCount(int userCount) {
            this.userCount = userCount;
        }

        public int getRobotCount() {
            return robotCount;
        }

        public void setRobotCount(int robotCount) {
            this.robotCount = robotCount;
        }
    }
}
