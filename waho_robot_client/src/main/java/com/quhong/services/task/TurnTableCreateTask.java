package com.quhong.services.task;

import com.alibaba.fastjson.JSON;
import com.quhong.data.CreateRoomData;
import com.quhong.data.RobotActorData;
import com.quhong.data.RobotConfigData;
import com.quhong.robot.Robot;
import com.quhong.robot.TurnTableCreateRobot;
import com.quhong.services.RobotTaskService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;

@Component
public class TurnTableCreateTask extends AbstractRobotTask {
    private static final Logger logger = LoggerFactory.getLogger(TurnTableCreateTask.class);

    private Map<String, CreateRoomData> createRoomDataMap = new ConcurrentHashMap<>();

    public TurnTableCreateTask() {
        super(RobotTaskService.TURN_TABLE_CREATE_TASK);
    }

    @Override
    protected void doStart() {
        doRefresh(this.configData);
    }

    @Override
    protected void doRefresh(RobotConfigData configData) {
        ConfigParamData paramData = JSON.parseObject(configData.getParams(), ConfigParamData.class);
        if (paramData.createList == null) {
            logger.error("do refresh error. paramdata.createList is empty.");
            return;
        }
        for (CreateRoomData createRoomData : paramData.createList) {
            createRoomDataMap.put(createRoomData.getUid(), createRoomData);
            doCreateRobot(createRoomData);
        }
    }

    protected void doCreateRobot(CreateRoomData createRoomData){
        RobotActorData robotActorData = new RobotActorData();
        robotActorData.setUid(createRoomData.getUid());
        Robot robot = createRobot(robotActorData, 1);
        if(robot != null){
            ((TurnTableCreateRobot)robot).setCreateRoomData(createRoomData);
        }
    }

    @Override
    protected Class<? extends Robot> getRobotClass() {
        return TurnTableCreateRobot.class;
    }

    @Override
    public String getEnterRoomId(String uid) {
        return null;
    }

    @Override
    public void createRobotList(CreateRobotData createRobotData) {
        // null
    }

    public static class ConfigParamData {
        private List<CreateRoomData> createList;

        public ConfigParamData() {

        }

        public List<CreateRoomData> getCreateList() {
            return createList;
        }

        public void setCreateList(List<CreateRoomData> createList) {
            this.createList = createList;
        }
    }
}
