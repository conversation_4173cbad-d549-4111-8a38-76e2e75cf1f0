package com.quhong.services.task;

import com.quhong.data.RobotActorData;
import com.quhong.data.RobotConfigData;

import java.util.List;

public interface RobotTask {
    void start();
    void stop();
    boolean isRunning();

    RobotConfigData getConfigData();
    void updateConfig(RobotConfigData configData);

    void onTick();

    String getEnterRoomId(String uid);

    String getTaskName();
    void createRobotList(CreateRobotData createRobotData);

    class CreateRobotData{
        private int delay;
        private List<RobotActorData> robotList;
        private int listIndex;
        private int delayInterval;

        public CreateRobotData(int delayInterval){
            this.delayInterval = delayInterval;
        }

        public int getDelay() {
            return delay;
        }

        public void setDelay(int delay) {
            this.delay = delay;
        }

        public RobotActorData next(){
            if(this.listIndex >= this.robotList.size()){
                return null;
            }
            RobotActorData actorData = this.robotList.get(listIndex);
            this.listIndex ++;
            this.delay += this.delayInterval;
            return actorData;
        }

        public List<RobotActorData> getRobotList() {
            return robotList;
        }

        public void setRobotList(List<RobotActorData> robotList) {
            this.robotList = robotList;
        }

        public int getListIndex() {
            return listIndex;
        }
    }
}
