package com.quhong.chains.room;

import com.quhong.enums.TurnTableStatus;
import com.quhong.robot.Robot;
import org.springframework.util.StringUtils;

public class TurnTableJoinStrategy extends TurnTableStrategy {
    public TurnTableJoinStrategy(Robot robot) {
        super(robot);
    }

    @Override
    protected void doAfterUpdateTurnTable() {
        checkAndJoinTurnTable();
    }

    @Override
    protected void doOnTick() {

    }

    private void checkAndJoinTurnTable(){
        if(checkJoinTurnTable()){
            turnTableHandler.join(robot, turnTableData.getGameId());
        }
    }


    private boolean checkJoinTurnTable() {
        if (StringUtils.isEmpty(robot.getRoomId())) {
            return false;
        }
        if (turnTableData == null) {
            return false;
        }
        if (turnTableData.getStatus() == TurnTableStatus.STOP || turnTableData.getStatus() == TurnTableStatus.START) {
            return false;
        }
        return !turnTableData.isJoin();
    }
}
