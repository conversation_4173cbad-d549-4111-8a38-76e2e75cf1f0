package com.quhong.executors.http;

import com.quhong.data.HttpReqData;
import com.quhong.http.AppHttpCallBack;
import com.quhong.http.AppHttpClient;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

@Component
public class SystemHandler {
    private static final Logger logger = LoggerFactory.getLogger(SystemHandler.class);

    @Autowired
    private AppHttpClient httpClient;
    @Value("${robot.robotDomain}")
    private String robotDomain;

    public void requestRobotList(int robotCount, AppHttpCallBack callBack) {
        RobotListReq robotListReq = new RobotListReq();
        robotListReq.setSize(robotCount);
        robotListReq.setInit(1);
        request("robot_server/robot_list", robotListReq, callBack);
    }

    public void requestRoomList(int minUserCount, AppHttpCallBack callBack) {
        RoomListReq roomListReq = new RoomListReq();
        roomListReq.setMinUserCount(minUserCount);
        request("robot_server/room_list", roomListReq, callBack);
    }

    public void requestRobotConfig(AppHttpCallBack callBack) {
        request("robot_server/robot_config", new HttpReqData(), callBack);
    }

    public void request(String path, HttpReqData data, AppHttpCallBack callBack) {
        httpClient.request(robotDomain, path, data, callBack);
    }

    public static class RoomListReq extends HttpReqData {
        private int minUserCount; //房间最小人数

        public RoomListReq() {

        }

        public int getMinUserCount() {
            return minUserCount;
        }

        public void setMinUserCount(int minUserCount) {
            this.minUserCount = minUserCount;
        }
    }

    public static class RobotListReq extends HttpReqData {
        private int size;
        private int init;

        public int getInit() {
            return init;
        }

        public void setInit(int init) {
            this.init = init;
        }

        public int getSize() {
            return size;
        }

        public void setSize(int size) {
            this.size = size;
        }
    }

}
