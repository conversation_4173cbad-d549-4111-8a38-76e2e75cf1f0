package com.quhong.executors.http;

import com.quhong.data.HttpReqData;
import com.quhong.data.StressTestParam;
import com.quhong.http.HttpResult;
import com.quhong.robot.Robot;
import com.quhong.services.task.StressTestTask;
import com.quhong.utils.RoomUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import jakarta.annotation.Resource;
import java.util.*;
import java.util.concurrent.ThreadLocalRandom;

/**
 * svga小于1299礼物： [230, 358, 435, 447, 452, 453, 463, 470, 489, 490, 506, 507, 532, 533, 534, 535, 543, 544, 547, 548, 566, 571, 573, 576, 606, 608, 615, 616, 617, 618, 619, 620, 628]
 * svga大于等于1299礼物： [320, 441, 515, 560, 612]
 * mp4 礼物：[478, 613, 614, 622, 625, 632]
 * 非svga小于1299礼物： [7, 9, 14, 16, 17, 20, 21, 25, 26, 28, 30, 31, 32, 33, 41, 42, 44, 49, 51, 53, 56, 58, 60, 62, 65, 66, 67, 68, 69, 70, 72, 73, 74, 77, 80, 81, 82, 83, 84, 85, 86, 90, 91, 95, 108, 110, 111, 113, 115, 142, 286, 303, 306, 314, 352, 354, 398, 400, 401, 403, 404, 405, 431, 472, 473, 474, 475, 476, 477, 550, 551, 552, 553, 554, 587, 588, 589, 590, 591, 592, 623, 624, 626, 629]
 */
@Component
public class SendGiftHandler extends AbstractEnterRoomHandler {
    private static final Logger logger = LoggerFactory.getLogger(SendGiftHandler.class);
    private static final List<Integer> NUMBER_LIST = Arrays.asList(1, 7, 17, 77, 99, 666, 999, 9999);
    // 0随机 1发送给麦位单人 2发送给麦位多人 3发送给所有麦位用户 4发送给全房间用户
    private static final List<Integer> SEND_TO_LIST = Arrays.asList(1, 2, 3, 4);
    private static final List<Integer> GIFT_LIST = Arrays.asList(7, 9, 14, 16, 17, 20, 21, 25, 26, 28, 30, 31, 32, 33, 41, 42, 44, 49, 51, 53, 56, 58, 60, 62, 65, 66, 67, 68, 69, 70, 72, 73, 74, 77, 80, 81, 82, 83, 84, 85, 86, 90, 91, 95, 108, 110, 111, 113, 115, 142, 286, 303, 306, 314, 352, 354, 398, 400, 401, 403, 404, 405, 431, 472, 473, 474, 475, 476, 477, 550, 551, 552, 553, 554, 587, 588, 589, 590, 591, 592, 623, 624, 626, 629);
    private static final List<Integer> BIG_GIFT_LIST = Arrays.asList(40, 64, 114);
    @Resource
    private StressTestTask stressTestTask;


    public void reqSendRoomMsg(Robot robot) {
        SendRoomGiftReq reqData = new SendRoomGiftReq();
        reqData.copyFrom(robot);
        reqData.setRoomId(robot.getRoomId());
        buildReq(reqData, stressTestTask.getStressTestParam());
        if (0 == reqData.getSendType()
                || (reqData.getSendType() == 1 && CollectionUtils.isEmpty(reqData.getAidSet()))
                || (null != reqData.getAidSet() && reqData.getAidSet().size() > 8)) {
            return;
        }
        requestV2(robot, "gift/send", reqData, data -> {
            HttpResult result = data.getBody();
            if (result == null) {
                logger.error("send gift error. result is null. body={} status={} uid={}", data.getBody(), data.getStatus(), robot.getUid());
                return;
            }
            if (result.isError()) {
                logger.error("send gift error. code={}. uid={}", result.getCode(), robot.getUid());
            }
        });
    }

    public void buildReq(SendRoomGiftReq reqData, StressTestParam param) {
        int apiSendGiftId = stressTestTask.getStressTestParam().getApiParam().getSendGiftId();
        if (apiSendGiftId > 0) {
            buildSendTo(reqData, param);
            reqData.setNumber(buildSendNumber(param));
            reqData.setGiftId(apiSendGiftId);
            return;
        }
        if (ThreadLocalRandom.current().nextBoolean()) {
            buildSendTo(reqData, param);
            reqData.setNumber(buildSendNumber(param));
        } else {
            reqData.setNumber(1);
            buildBigSendTo(reqData);
        }
    }

    public int buildSendNumber(StressTestParam param) {
        int sendGiftNum = param.getSendGiftNum();
        int apiSendGiftNum = stressTestTask.getStressTestParam().getApiParam().getSendGiftNum();
        if (-1 != apiSendGiftNum) {
            sendGiftNum = apiSendGiftNum;
        }
        if (sendGiftNum == 0) {
            return NUMBER_LIST.get(ThreadLocalRandom.current().nextInt(NUMBER_LIST.size()));
        }
        return sendGiftNum;
    }

    public void buildBigSendTo(SendRoomGiftReq reqData) {
        reqData.setGiftId(BIG_GIFT_LIST.get(ThreadLocalRandom.current().nextInt(BIG_GIFT_LIST.size())));
        reqData.setSendType(1);
        reqData.setIsAllMic(2);

        Set<String> roomRobotSet = stressTestTask.getRoomRobotMap().get(reqData.getRoomId());
        if (CollectionUtils.isEmpty(roomRobotSet)) {
            reqData.setSendType(1);
            reqData.setIsAllMic(2);
            reqData.setAidSet(Collections.singleton(RoomUtils.getRoomHostId(reqData.getRoomId())));
            return;
        }
        for (String aid : roomRobotSet) {
            reqData.setAidSet(Collections.singleton(aid));
            break;
        }
    }

    public void buildSendTo(SendRoomGiftReq reqData, StressTestParam param) {
        List<String> list = stressTestTask.getRoomMicRobotMap().get(reqData.getRoomId());
        reqData.setGiftId(GIFT_LIST.get(ThreadLocalRandom.current().nextInt(GIFT_LIST.size())));
        if (CollectionUtils.isEmpty(list)) {
            reqData.setSendType(1);
            reqData.setIsAllMic(2);
            for (String aid : stressTestTask.getRoomRobotMap().get(reqData.getRoomId())) {
                reqData.setAidSet(Collections.singleton(aid));
                break;
            }
            return;
        }
        int sendTo = param.getSendTo();
        int apiSendTo = stressTestTask.getStressTestParam().getApiParam().getSendTo();
        if (-1 != apiSendTo) {
            sendTo = apiSendTo;
        }
        if (param.getSendTo() == 0) {
            sendTo = SEND_TO_LIST.get(ThreadLocalRandom.current().nextInt(SEND_TO_LIST.size()));
        }
        if (sendTo == 1) {
            reqData.setSendType(1);
            reqData.setIsAllMic(2);
            reqData.setAidSet(Collections.singleton(list.get(ThreadLocalRandom.current().nextInt(list.size()))));
        } else if (sendTo == 2) {
            reqData.setSendType(1);
            reqData.setIsAllMic(4);
            if (list.size() <= 1) {
                reqData.setAidSet(Collections.singleton(list.get(ThreadLocalRandom.current().nextInt(list.size()))));
            } else {
                Collections.shuffle(list);
                int nextInt = ThreadLocalRandom.current().nextInt(list.size());
                if (nextInt == 0) {
                    nextInt = list.size() - 1;
                }
                reqData.setAidSet(new HashSet<>(list.subList(0, nextInt)));
            }
        } else if (sendTo == 3) {
            reqData.setSendType(1);
            reqData.setIsAllMic(1);
            reqData.setAidSet(new HashSet<>(list));
        } else if (sendTo == 4) {
            reqData.setSendType(3);
        } else {
            logger.error("build send to error, not support sendTo. sendTo={}", sendTo);
            reqData.setSendType(1);
            reqData.setIsAllMic(2);
            reqData.setAidSet(Collections.singleton(RoomUtils.getRoomHostId(reqData.getRoomId())));
        }
    }

    public static class SendRoomGiftReq extends HttpReqData {
        private String roomId; // 房间ID
        private int giftId; // 礼物id
        private int number; // 礼物数量
        private int sendType; // 1指定用户发送，2一对一礼物发送（私信礼物），3全房间发送
        private int isAllMic; // (sendType=1时区分)是否发送给全麦位用户 1是 2单个用户 4多个用户
        private int backpack; // 是否背包礼物 1是
        private Set<String> aidSet; // 麦位上面的人 可以为空(sendType=1时不能为空)

        public String getRoomId() {
            return roomId;
        }

        public void setRoomId(String roomId) {
            this.roomId = roomId;
        }

        public int getGiftId() {
            return giftId;
        }

        public void setGiftId(int giftId) {
            this.giftId = giftId;
        }

        public int getNumber() {
            return number;
        }

        public void setNumber(int number) {
            this.number = number;
        }

        public int getSendType() {
            return sendType;
        }

        public void setSendType(int sendType) {
            this.sendType = sendType;
        }

        public int getIsAllMic() {
            return isAllMic;
        }

        public void setIsAllMic(int isAllMic) {
            this.isAllMic = isAllMic;
        }

        public int getBackpack() {
            return backpack;
        }

        public void setBackpack(int backpack) {
            this.backpack = backpack;
        }

        public Set<String> getAidSet() {
            return aidSet;
        }

        public void setAidSet(Set<String> aidSet) {
            this.aidSet = aidSet;
        }
    }
}
