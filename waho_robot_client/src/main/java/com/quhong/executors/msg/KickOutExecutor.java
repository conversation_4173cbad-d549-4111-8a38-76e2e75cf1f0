package com.quhong.executors.msg;

import com.quhong.core.annotation.MsgExecutor;
import com.quhong.enums.Cmd;
import com.quhong.msg.room.KickFromRoomPushMsg;
import com.quhong.robot.Robot;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

@MsgExecutor
public class KickOutExecutor extends RobotMsgExecutor<KickFromRoomPushMsg> {
    private static final Logger logger = LoggerFactory.getLogger(KickOutExecutor.class);

    public KickOutExecutor() {
        super(Cmd.KICK_FROM_ROOM);
    }

    @Override
    public void doExecute(Robot connector, KickFromRoomPushMsg msg) {
        logger.info("robot has been kicked out. fromUid={} roomId={} uid={}", msg.getFrom_uid(), msg.getRoomId(), connector.getUid());
        connector.afterLeaveRoom();
    }
}
