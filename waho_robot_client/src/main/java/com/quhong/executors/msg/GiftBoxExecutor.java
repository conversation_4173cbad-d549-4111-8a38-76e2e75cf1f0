package com.quhong.executors.msg;

import com.quhong.chains.room.LuckyBoxStrategy;
import com.quhong.core.annotation.MsgExecutor;
import com.quhong.enums.Cmd;
import com.quhong.msg.room.GiftBoxPushMsg;
import com.quhong.robot.Robot;
import com.quhong.services.RoomService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;

@MsgExecutor
public class GiftBoxExecutor extends RobotMsgExecutor<GiftBoxPushMsg> {
    private static final Logger logger = LoggerFactory.getLogger(GiftBoxExecutor.class);
    private static final double DELAY_RATIO = 0.3;
    private static final int MAX_DELAY_TIME = 5 * 1000;
    @Autowired
    private RoomService roomService;

    public GiftBoxExecutor() {
        super(Cmd.GIFT_BOX_PUSH);
    }

    @Override
    protected void doExecute(Robot robot, GiftBoxPushMsg msg) {
        String roomId = msg.getRoomId();
        roomService.callIdleRobotsEnterRoom(roomId);
        LuckyBoxStrategy strategy = (LuckyBoxStrategy) robot.getStrategy(LuckyBoxStrategy.class);
        if(strategy != null) {
            strategy.requestGiftBox(msg);
        }
    }
}
