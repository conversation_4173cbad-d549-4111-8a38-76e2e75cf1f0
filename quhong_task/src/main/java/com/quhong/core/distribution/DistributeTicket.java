package com.quhong.core.distribution;

import com.quhong.core.concurrency.queues.TaskQueue;
import com.quhong.core.concurrency.tasks.ITask;
import com.quhong.core.config.ServerConfig;
import com.quhong.core.dispose.Disposer;
import com.quhong.core.timers.LoopTask;
import com.quhong.core.timers.TimerService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

/**
 * 凭证
 */
public class DistributeTicket extends Disposer {
    private static final Logger logger = LoggerFactory.getLogger(DistributeTicket.class);
    private static final long DEFAULT_INTERVAL = 10; //默认为10秒处理一次

    private ConstantLock lock;
    private boolean own;
    private TaskQueue taskQueue;
    private LoopTask loopTask;
    private String key;

    @Override
    protected void doDispose() {
        if(loopTask != null){
            loopTask.cancel();
        }
    }

    public DistributeTicket(TaskQueue queue, String key, String lockTailValue){
        this(queue, key, DEFAULT_INTERVAL, lockTailValue);
    }

    public DistributeTicket(TaskQueue queue, String key, long interval){
        this(queue, key, interval, ServerConfig.getServerID() + "");
    }

    /**
     *
     * @param key
     * @param interval 单位 秒
     * @param lockTailValue
     */
    public DistributeTicket(TaskQueue queue, String key, long interval, String lockTailValue){
        if(interval < DEFAULT_INTERVAL){
            interval = DEFAULT_INTERVAL;
        }
        if(queue == null){
            throw new RuntimeException("queue is null, key :" + key);
        }
        this.key = key;
        taskQueue = queue;
        lock = new ConstantLock(key, interval * 2, lockTailValue);
        // 获取凭证
        tryGetTicket();
        loopTask = new LoopTask(taskQueue, (int)(interval * 1000)) {
            @Override
            protected void execute() {
                tryGetTicket();
            }
        };
        TimerService.getService().addDelay(loopTask);
    }

    private void tryGetTicket(){
        boolean tempOwn = lock.keepLocking();
        if(tempOwn == own){
            return;
        }
        own = lock.keepLocking();
        if(own){
            logger.info("start get ticket. key={} lock_value={}", key, lock.getLockValue());
        }else{
            logger.info("lost ticket. key={} lock_value={}", key, lock.getLockValue());
        }
    }

    /**
     * 是否拥有该凭证
     * @return
     */
    public boolean isOwn() {
        return own;
    }

    public void addTask(ITask task){
        this.taskQueue.add(task);
    }

    public TaskQueue getTaskQueue() {
        return taskQueue;
    }

    public String getKey() {
        return key;
    }
}
