package com.quhong.core.config;

public interface IConfig {
    /**
     *
     * @param key
     * @return
     */
    String getStr(String key);

    /**
     * 获取str,如果获取不到，就用默认的
     * @param key
     * @param defaultKey
     * @return
     */
    String getStr(String key, String defaultKey);

    /**
     *
     * @param key
     * @param value
     */
    void setStr(String key, String value);

    /**
     * 如果value不为空，则赋值
     * @param key
     * @param value
     */
    void setNotEmptyStr(String key, String value);
}
