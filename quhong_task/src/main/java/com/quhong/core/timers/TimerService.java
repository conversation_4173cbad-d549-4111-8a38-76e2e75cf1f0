package com.quhong.core.timers;

import com.quhong.core.concurrency.BaseTaskFactory;
import com.quhong.core.concurrency.pools.MyThreadFactory;
import com.quhong.core.concurrency.pools.TaskPool;
import com.quhong.core.concurrency.tasks.CancelableTask;
import com.quhong.core.dispose.Disposer;
import com.quhong.core.utils.DateHelper;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.HashSet;
import java.util.List;
import java.util.Set;
import java.util.concurrent.*;

/**
 * create by maoyule on 2019/1/9
 */
public class TimerService extends Disposer {
    private static final Logger logger = LoggerFactory.getLogger(TimerService.class);

    private static TimerService service = null;

    public static TimerService getService() {
        return service;
    }

    private ScheduledExecutorService scheduledService;

    private ScheduledFuture secondFuture;

    private ScheduledFuture delayedFuture;

    private ScheduledFuture dayStartFuture;

    private Set<SecondTask> secondTickerSet;

    private DelayQueue<LoopTask> delayQueue;

    private DelayedRunnable delayedRunnable;

    private List<CancelableTask> dayStartList;

    public TimerService() {
        if (service == null) {
            service = this;
        } else {
            throw new RuntimeException("timer service has init");
        }
        init();
    }

    @Override
    protected void doDispose() {
        if (delayedRunnable != null) {
            delayedRunnable.dispose();
        }
        if (scheduledService != null) {
            scheduledService.shutdown();
        }
    }

    private void init() {
        delayQueue = new DelayQueue<>();
        secondTickerSet = ConcurrentHashMap.newKeySet();

        scheduledService = Executors.newScheduledThreadPool(1, new MyThreadFactory(
                "timer"));
        secondFuture = scheduledService.scheduleAtFixedRate(new SecondRunner(), 0, 1,
                TimeUnit.SECONDS);

        delayedRunnable = new DelayedRunnable();

        delayedFuture = scheduledService.scheduleAtFixedRate(delayedRunnable, 0, 50,
                TimeUnit.MILLISECONDS);

        // 每日定时执行
        long time = DateHelper.ARABIAN.getDayOffset(1);
        int delayTime = (int) (time - System.currentTimeMillis());
        delayTime += 5 * 60 * 1000; //延迟五分钟执行
        int oneDay = 86400 * 1000;
        dayStartList = new CopyOnWriteArrayList<>();
        dayStartFuture = scheduledService.scheduleAtFixedRate(new DayStartRunner(), delayTime, oneDay, TimeUnit.MILLISECONDS);
    }

    public ScheduledExecutorService getScheduledService() {
        return this.scheduledService;
    }

    /**
     * 添加每秒调用的task
     *
     * @param ticker
     */
    public void addTicker(SecondTask ticker) {
        secondTickerSet.add(ticker);
    }

    public void removeTicker(SecondTask ticker) {
        secondTickerSet.remove(ticker);
    }

    public void addDelay(LoopTask loopTask) {
        this.delayQueue.add(loopTask);
    }

    /**
     * 每天0点，延迟五分钟执行
     *
     * @param task
     */
    public void addDayStart(CancelableTask task) {
        dayStartList.add(task);
    }

    private class DelayedRunnable extends Disposer implements Runnable {
        private boolean isRunning = true;
        private Thread currentThread;

        public DelayedRunnable() {
        }

        @Override
        protected void doDispose() {
            isRunning = false;
            if (currentThread != null) {
                currentThread.interrupt();
            }
        }

        @Override
        public void run() {
            currentThread = Thread.currentThread();
            while (isRunning) {
                try {
                    LoopTask delay = delayQueue.poll();
                    if (delay == null) {
                        break;
                    }
                    try {
                        delay.attach();
                    } catch (Exception e) {
                        logger.error(e.getMessage(), e);
                    }
                    if (delay.next()) {
                        delayQueue.add(delay);
                    }
                } catch (Exception e) {
                    logger.error(e.getMessage(), e);
                }
            }
        }
    }

    private class SecondRunner implements Runnable {
        @Override
        public void run() {
            try {
                // second tick
                for (SecondTask ticker : secondTickerSet) {
                    ticker.attach();
                }
                BaseTaskFactory.getFactory().taskPoolTick();
            } catch (Exception e) {
                logger.error(e.getMessage(), e);
            }
        }
    }

    private class DayStartRunner implements Runnable {
        @Override
        public void run() {
            try {
                // second tick
                for (CancelableTask task : dayStartList) {
                    if (task.isCancel()) {
                        dayStartList.remove(task);
                        continue;
                    }
                    BaseTaskFactory.getFactory().addSlow(task);
                }
            } catch (Exception e) {
                logger.error(e.getMessage(), e);
            }
        }
    }
}
