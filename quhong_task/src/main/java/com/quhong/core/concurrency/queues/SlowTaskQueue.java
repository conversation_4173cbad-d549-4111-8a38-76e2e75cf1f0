package com.quhong.core.concurrency.queues;

import com.quhong.core.concurrency.BaseTaskFactory;

/**
 * create by ma<PERSON><PERSON> on 2019/1/9
 */
public class SlowTaskQueue extends MonitorTaskQueue {
    public SlowTaskQueue() {
        super(BaseTaskFactory.getFactory().getSlowPool());
    }

    public SlowTaskQueue(long runOnceLimit) {
        super(BaseTaskFactory.getFactory().getSlowPool(), runOnceLimit);
    }
}
