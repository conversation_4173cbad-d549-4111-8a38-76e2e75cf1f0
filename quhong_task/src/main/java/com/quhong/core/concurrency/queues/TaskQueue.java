package com.quhong.core.concurrency.queues;


import com.quhong.core.concurrency.BaseTaskFactory;

/**
 * create by <PERSON><PERSON><PERSON> on 2019/1/9
 */
public class TaskQueue extends MonitorTaskQueue {
    public TaskQueue() {
        super(BaseTaskFactory.getFactory().getMainPool());
    }

    public TaskQueue(long runOnceLimit) {
        super(BaseTaskFactory.getFactory().getMainPool(), runOnceLimit);
    }
}
