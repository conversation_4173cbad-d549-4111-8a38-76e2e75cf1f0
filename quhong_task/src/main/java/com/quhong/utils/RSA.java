package com.quhong.utils;

import org.apache.commons.codec.binary.Base64;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import javax.crypto.Cipher;
import java.security.*;
import java.security.interfaces.RSAPrivateKey;
import java.security.interfaces.RSAPublicKey;
import java.security.spec.PKCS8EncodedKeySpec;
import java.security.spec.X509EncodedKeySpec;
import java.util.ArrayList;
import java.util.List;

public class RSA {
    private static final Logger logger = LoggerFactory.getLogger(RSA.class);

    private static String private_key;
    private static Cipher defaultCipher;

    static {
        private_key = "MIICdgIBADANBgkqhkiG9w0BAQEFAASCAmAwggJcAgEAAoGBANnvt64L7Q8ZHtQaL4GwH9iEwN2lbuU017zWNt9dR5HgHh80Q2/fjOcNh6Mx/EsDzOqDu5ugO7fPra90Y3KhUVClIZkQTwBZ2mtHRNPZ1DoIpGjJ0vPrxViv/yFrV6L+ZEICsIEtCbtz9jyPJ4j/wyZZL/jPLbRWgAQydSrLMBypAgMBAAECgYAB/B7NQx7LN8h5+EyVNupNVaUvh5ePVxg9aCqLBX2WwKaplFdmZA/zlUIWXIjOkpfpzV4WrGKK2aV8bvgOZdV4nR3+GCMZBJbCf1XeDmdDTVkPAGbONka+0P0JLTAaOyVtG6ryivVaEdI/6Zi2dj8manqPJ58GbP2lIx1pSAJ50QJBAP4zIvYl9vEDYWMDE60xfEFuy24mjgLMfOGB21x2kmGZ/1dBjzR/2b4qbvjWbT8KufqDPgIVWRX0T642cRB225ECQQDbetXn5YaRhSKerA6nfG1rRR6ilBAYRtM8TDN3QBnjVJQ6SXynQOcocZSAjM7IVsdZRUD4Os8n2oOr2AxhWTOZAkEA2xrmsrZskLdyNO7NAgbabGf8jvhKhd0Ocy4ED0Hg0KH1dpd6wSzGWWSn/HKO/bzYKzb+trsB66uHFsm0URWyEQJAAplJvx0worh42HghFEqc4RBIdJRcPG1aQvSEB31y0QZHWwlTuIdENW5i28LPEAA3DA+N3WKU4VvtAzPjsaNkwQJAUseM1Sqd6teKdzttwBQDcJsT9iYIOZUGF4VlNavtMMV0+pStYedWPZX17ALkizTxp/XLLIvxLaH9REwpzDXA9g==";
        try {
            defaultCipher = Cipher.getInstance("RSA");
            //base64编码的私钥
            byte[] decoded = Base64.decodeBase64(private_key);
            RSAPrivateKey rsaKey = (RSAPrivateKey) KeyFactory.getInstance("RSA").generatePrivate(new PKCS8EncodedKeySpec(decoded));
            defaultCipher.init(Cipher.DECRYPT_MODE, rsaKey);
        }catch (Exception e){
            logger.error("init rsa error.", e.getMessage(), e);
        }
    }

    /**
     * 随机生成密钥对
     * @throws NoSuchAlgorithmException
     */
    public static List<String> genKeyPair() throws NoSuchAlgorithmException {
        // KeyPairGenerator类用于生成公钥和私钥对，基于RSA算法生成对象
        KeyPairGenerator keyPairGen = KeyPairGenerator.getInstance("RSA");
        // 初始化密钥对生成器，密钥大小为96-1024位
        keyPairGen.initialize(1024,new SecureRandom());
        // 生成一个密钥对，保存在keyPair中
        KeyPair keyPair = keyPairGen.generateKeyPair();
        RSAPrivateKey privateKey = (RSAPrivateKey) keyPair.getPrivate();   // 得到私钥
        RSAPublicKey publicKey = (RSAPublicKey) keyPair.getPublic();  // 得到公钥
        String publicKeyString = new String(Base64.encodeBase64(publicKey.getEncoded()));
        // 得到私钥字符串
        String privateKeyString = new String(Base64.encodeBase64((privateKey.getEncoded())));
        List<String> keyList = new ArrayList<>();  //用于封装随机产生的公钥与私钥
        // 将公钥和私钥保存到Map
        keyList.add(publicKeyString);  //0表示公钥
        keyList.add(privateKeyString);  //1表示私钥
        return keyList;
    }
    /**
     * RSA公钥加密
     *
     * @param str
     *            加密字符串
     * @param publicKey
     *            公钥
     * @return 密文
     * @throws Exception
     *             加密过程中的异常信息
     */
    public static String encrypt( String str, String publicKey ) throws Exception{
        //base64编码的公钥
        byte[] decoded = Base64.decodeBase64(publicKey);
        RSAPublicKey pubKey = (RSAPublicKey) KeyFactory.getInstance("RSA").generatePublic(new X509EncodedKeySpec(decoded));
        //RSA加密
        Cipher cipher = Cipher.getInstance("RSA");
        cipher.init(Cipher.ENCRYPT_MODE, pubKey);
        String outStr = Base64.encodeBase64String(cipher.doFinal(str.getBytes("UTF-8")));
        return outStr;
    }

    public static String decrypt(String str){
        try {
            //64位解码加密后的字符串
            byte[] inputBytes = Base64.decodeBase64(str.getBytes());
            String outStr = new String(defaultCipher.doFinal(inputBytes));
            return outStr;
        }catch (Exception e){
            logger.error("rsa decrypt error. {}", e.getMessage(), e);
        }
        return null;
    }

    /**
     * RSA私钥解密
     *
     * @param str
     *            加密字符串
     * @param privateKey
     *            私钥
     * @return 铭文
     * @throws Exception
     *             解密过程中的异常信息
     */
    public static String decrypt(String str, String privateKey) throws Exception{
        //64位解码加密后的字符串
        byte[] inputByte = Base64.decodeBase64(str.getBytes("UTF-8"));
        //base64编码的私钥
        byte[] decoded = Base64.decodeBase64(privateKey);
        RSAPrivateKey priKey = (RSAPrivateKey) KeyFactory.getInstance("RSA").generatePrivate(new PKCS8EncodedKeySpec(decoded));
        //RSA解密
        Cipher cipher = Cipher.getInstance("RSA");
        cipher.init(Cipher.DECRYPT_MODE, priKey);
        String outStr = new String(cipher.doFinal(inputByte));
        return outStr;
    }
}