package com.quhong.core.redis;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.quhong.data.ActorData;
import com.quhong.data.BaseActorData;
import com.quhong.datas.PlayerData;
import com.quhong.enums.BaseRedisKey;
import jakarta.annotation.Resource;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.context.annotation.Lazy;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.stereotype.Component;
import org.springframework.util.ObjectUtils;
import org.springframework.util.StringUtils;

import java.util.concurrent.TimeUnit;

@Component
@Lazy
public class BasePlayerRedis {
    private static final Logger logger = LoggerFactory.getLogger(BasePlayerRedis.class);

    protected static final int PLAYER_DATA_EXPIRE_TIME = 30; //单位 分钟

    private static final int EXPIRE_SECOND = 7 * 24 * 60 * 60;

    @Resource
    protected StringRedisTemplate mainRedisTemplate;

    public PlayerData getPlayerDataFromRedis(String uid) {
        String key = BaseRedisKey.PLAYER_PRE + uid;
        String json = mainRedisTemplate.opsForValue().get(key);
        if (StringUtils.isEmpty(json)) {
            return null;
        }
        PlayerData playerData = null;
        try {
            playerData = JSON.parseObject(json, PlayerData.class);
        } catch (Exception e) {
            logger.error("parse player error. uid={} json={}", uid, json, e);
        }
        return playerData;
    }

    public void savePlayerToRedis(PlayerData playerData) {
        try {
            String key = BaseRedisKey.PLAYER_PRE + playerData.getUid();
            String json = JSON.toJSONString(playerData);
            mainRedisTemplate.opsForValue().set(key, json, PLAYER_DATA_EXPIRE_TIME, TimeUnit.MINUTES);
        } catch (Exception e) {
            logger.error("save playerData error. {}", playerData != null ? playerData.getUid() : null);
        }
    }

    /**
     * 更新玩家过期时间，如果更新失败，就插入
     *
     * @param playerData
     */
    public void updatePlayerTime(PlayerData playerData) {
        try {
            String key = BaseRedisKey.PLAYER_PRE + playerData.getUid();
            if (mainRedisTemplate.expire(key, PLAYER_DATA_EXPIRE_TIME, TimeUnit.MINUTES)) {
                return;
            }
            savePlayerToRedis(playerData);
        } catch (Exception e) {
            logger.error("save playerData error. {}", playerData != null ? playerData.getUid() : null);
        }
    }

    /**
     * 这里线程不安全，后期优化
     *
     * @param uid
     * @param sessionId
     */
    protected boolean deletePlayerData(String uid, long sessionId) {
        PlayerData playerData = getPlayerDataFromRedis(uid);
        if (playerData == null) {
            return true;
        }
        if (playerData.getSessionId() == -1) {
            return true;
        }
        if (playerData.getSessionId() == sessionId) {
            playerData.setSessionId(-1);
            savePlayerToRedis(playerData);
            return true;
        }
        return false;
    }

    public ActorData getActorFromRedis(String uid) {
        try {
            String json = getActorJsonFromRedis(uid);
            if (StringUtils.isEmpty(json) || "null".equals(json)) {
                return null;
            }
            ActorData actorData = null;
            try {
                actorData = JSON.parseObject(json, ActorData.class);
                actorData.setUid(uid);
            } catch (Exception e) {
                logger.error("parse actor error. uid={} json={}", uid, json, e);
            }
            return actorData;
        } catch (Exception e) {
            logger.error("get actor data from redis. uid={} {}", uid, e.getMessage(), e);
        }
        return null;
    }

    public String getActorJsonFromRedis(String uid) {
        try {
            String json = mainRedisTemplate.opsForValue().get(getActorKey(uid));
            if (StringUtils.isEmpty(json) || "null".equals(json)) {
                return null;
            }
            return json;
        } catch (Exception e) {
            logger.error("get actor data from redis. uid={} {}", uid, e.getMessage(), e);
        }
        return null;
    }

    public <T extends BaseActorData> T getActorFromRedis(String uid, Class<T> entityClass) {
        try {
            String json = getActorJsonFromRedis(uid);
            if (StringUtils.isEmpty(json) || "null".equals(json)) {
                return null;
            }
            T t = null;
            try {
                t = JSON.parseObject(json, entityClass);
                t.setUid(uid);
            } catch (Exception e) {
                logger.error("parse actor error. uid={} json={}", uid, json, e);
            }
            return t;
        } catch (Exception e) {
            logger.error("get actor data from redis. uid={} {}", uid, e.getMessage(), e);
        }
        return null;
    }

    /**
     * 保存actor数据到redis
     *
     * @param actorObj
     */
    public ActorData saveActor(String uid, JSONObject actorObj) {
        // 防止存储null字符串
        if (null == actorObj) {
            return null;
        }
        String key = getActorKey(uid);
        String json = JSON.toJSONString(actorObj);
        try {
            mainRedisTemplate.opsForValue().set(key, json, 2, TimeUnit.DAYS);
            ActorData actorData = JSON.parseObject(json, ActorData.class);
            actorData.setUid(uid);
            return actorData;
        } catch (Exception e) {
            logger.error("save actor to redis error. uid={} {}", uid, e.getMessage(), e);
        }
        return null;
    }

    public boolean expireActor(String uid) {
        try {
            String key = getActorKey(uid);
            if (mainRedisTemplate.expire(key, 2, TimeUnit.DAYS)) {
                return true;
            }
            return false;
        } catch (Exception e) {
            logger.error("expire actor error. uid={} {}", uid, e.getMessage(), e);
        }
        return false;
    }

    /**
     * 获取token，用于验证用户是否登录
     *
     * @param uid
     * @return
     */
    public String getToken(String uid) {
        return mainRedisTemplate.opsForValue().get(getTokenKey(uid));
    }

    public String getUidByToken(String token) {
        return mainRedisTemplate.opsForValue().get(getUserTokenKey(token));
    }

    public void removeToken(String uid) {
        String token = getToken(uid);
        mainRedisTemplate.delete(getTokenKey(uid));
        mainRedisTemplate.delete(getUserTokenKey(token));
    }

    public void addToken(String uid, String token) {
        try {
            mainRedisTemplate.opsForValue().set(getTokenKey(uid), token, EXPIRE_SECOND, TimeUnit.SECONDS);
            mainRedisTemplate.opsForValue().set(getUserTokenKey(token), uid, EXPIRE_SECOND, TimeUnit.SECONDS);
        } catch (Exception e) {
            logger.error("add token uid={} {}", uid, e.getMessage(), e);
        }
    }

    public long getTokenExpireTime(String uid) {
        try {
            Long expireTime = mainRedisTemplate.getExpire(getTokenKey(uid), TimeUnit.SECONDS);
            return expireTime != null ? expireTime.intValue() : 0;
        } catch (Exception e) {
            logger.error("get token expire time error. uid={} {}", uid, e.getMessage(), e);
            return 0L;
        }
    }

    public void incrTokenExpireTime(String uid) {
        try {
            if (ObjectUtils.isEmpty(uid)) {
                return;
            }
            String token = getToken(uid);
            mainRedisTemplate.expire(getTokenKey(uid), EXPIRE_SECOND, TimeUnit.SECONDS);
            mainRedisTemplate.expire(getUserTokenKey(token), EXPIRE_SECOND, TimeUnit.SECONDS);
        } catch (Exception e) {
            logger.error("incr token expire time error uid={} {}", uid, e.getMessage(), e);
        }
    }

    /**
     * 外部访问的临时token，5分钟有效期，以ex开头
     */
    public void addExternalToken(String uid, String token) {
        mainRedisTemplate.opsForValue().set(getExternalTokenKey(uid), token, 5, TimeUnit.MINUTES);
    }

    /**
     * 外部访问的临时token，5分钟有效期，访问后刷新
     */
    public String getAndExpireExternalToken(String uid) {
        String key = getExternalTokenKey(uid);
        String externalToken = mainRedisTemplate.opsForValue().get(key);
        if (!ObjectUtils.isEmpty(externalToken)) {
            mainRedisTemplate.expire(key, 5, TimeUnit.MINUTES);
        }
        return externalToken;
    }

    private String getUserTokenKey(String token) {
        return "str:userToken:" + token;
    }

    public String getTokenKey(String uid) {
        return "str:loginToken:" + uid;
    }

    private String getActorKey(String uid) {
        return "str:actorDataJson:" + uid;
    }

    public String getExternalTokenKey(String uid) {
        return "str:ExternalLoginToken:" + uid;
    }
}
