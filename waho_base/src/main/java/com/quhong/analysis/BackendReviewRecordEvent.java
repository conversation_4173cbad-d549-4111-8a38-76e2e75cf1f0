package com.quhong.analysis;

public class BackendReviewRecordEvent extends UserEvent {

    private int create_ctime; // 记录创建该条审核记录的时间
    private int modify_ctime; // 记录审核状态发生变更时的状态
    private String scene;      // 场景
    private String scene_desc; // 场景描述
    private String sub_scene;      // 子场景
    private String sub_scene_desc; // 子场景描述
    private String operater;   // 审核人
    private int review_status; // 0 待审核(已创建待审核) 1 审核成功(审核通过) 2 审核失败(审核拒绝)

    @Override
    public String getEventName() {
        return "backend_review_record";
    }

    @Override
    public int getEventTime() {
        return modify_ctime;
    }

    public int getCreate_ctime() {
        return create_ctime;
    }

    public void setCreate_ctime(int create_ctime) {
        this.create_ctime = create_ctime;
    }

    public int getModify_ctime() {
        return modify_ctime;
    }

    public void setModify_ctime(int modify_ctime) {
        this.modify_ctime = modify_ctime;
    }

    public String getScene() {
        return scene;
    }

    public void setScene(String scene) {
        this.scene = scene;
    }

    public String getScene_desc() {
        return scene_desc;
    }

    public void setScene_desc(String scene_desc) {
        this.scene_desc = scene_desc;
    }

    public String getSub_scene() {
        return sub_scene;
    }

    public void setSub_scene(String sub_scene) {
        this.sub_scene = sub_scene;
    }

    public String getSub_scene_desc() {
        return sub_scene_desc;
    }

    public void setSub_scene_desc(String sub_scene_desc) {
        this.sub_scene_desc = sub_scene_desc;
    }

    public String getOperater() {
        return operater;
    }

    public void setOperater(String operater) {
        this.operater = operater;
    }

    public int getReview_status() {
        return review_status;
    }

    public void setReview_status(int review_status) {
        this.review_status = review_status;
    }
}
