package com.quhong.analysis;

/**
 * <AUTHOR>
 * @date 2025/2/5
 */
public class StarGiftGrantRecordEvent extends UserEvent {

    private int rid;
    private int gift_id;
    private String gift_name;
    private int gift_class;
    private int ctime;

    @Override
    public String getEventName() {
        return "star_gift_grant_record";
    }

    @Override
    public int getEventTime() {
        return ctime;
    }

    public int getRid() {
        return rid;
    }

    public void setRid(int rid) {
        this.rid = rid;
    }

    public int getGift_id() {
        return gift_id;
    }

    public void setGift_id(int gift_id) {
        this.gift_id = gift_id;
    }

    public String getGift_name() {
        return gift_name;
    }

    public void setGift_name(String gift_name) {
        this.gift_name = gift_name;
    }

    public int getGift_class() {
        return gift_class;
    }

    public void setGift_class(int gift_class) {
        this.gift_class = gift_class;
    }

    public int getCtime() {
        return ctime;
    }

    public void setCtime(int ctime) {
        this.ctime = ctime;
    }
}
