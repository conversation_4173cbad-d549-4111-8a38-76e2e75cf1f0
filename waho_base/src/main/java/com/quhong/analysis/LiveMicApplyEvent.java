package com.quhong.analysis;


public class LiveMicApplyEvent extends UserEvent {

    private String room_id; // 直播房id
    private String live_id; // 直播场次id
    private int mic_apply_end_reason; // 退出连麦申请原因 1为用户自己退出列表 2为房主拒绝连麦 3为用户离开房间 4为房主离开房间
    private long ctime;

    public LiveMicApplyEvent() {
    }

    public LiveMicApplyEvent(String uid, String room_id, String live_id, int mic_apply_end_reason, long ctime) {
        this.uid = uid;
        this.room_id = room_id;
        this.live_id = live_id;
        this.mic_apply_end_reason = mic_apply_end_reason;
        this.ctime = ctime;
    }

    @Override
    public String getEventName() {
        return "live_mic_apply";
    }

    @Override
    public int getEventTime() {
        return 0;
    }

    public String getRoom_id() {
        return room_id;
    }

    public void setRoom_id(String room_id) {
        this.room_id = room_id;
    }

    public String getLive_id() {
        return live_id;
    }

    public void setLive_id(String live_id) {
        this.live_id = live_id;
    }

    public int getMic_apply_end_reason() {
        return mic_apply_end_reason;
    }

    public void setMic_apply_end_reason(int mic_apply_end_reason) {
        this.mic_apply_end_reason = mic_apply_end_reason;
    }

    public long getCtime() {
        return ctime;
    }

    public void setCtime(long ctime) {
        this.ctime = ctime;
    }
}
