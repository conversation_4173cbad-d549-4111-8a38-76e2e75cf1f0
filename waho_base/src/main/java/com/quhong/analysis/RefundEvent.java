package com.quhong.analysis;


public class RefundEvent extends UserEvent {
    private String pay_title;
    private int pay_type; // 1: GP 支付 2:Apple 支付 8中台支付
    private String recharge_money;
    private int recharge_time;
    private int refund_type; // 1申请退款 2退款成功
    private int should_deduct; // 应当扣除的钻石数
    private int actual_deduct; // 实际扣除的钻石数
    private int ctime;


    @Override
    public String getEventName() {
        return "log_refund_record";
    }

    @Override
    public int getEventTime() {
        return 0;
    }

    public String getPay_title() {
        return pay_title;
    }

    public void setPay_title(String pay_title) {
        this.pay_title = pay_title;
    }

    public int getPay_type() {
        return pay_type;
    }

    public void setPay_type(int pay_type) {
        this.pay_type = pay_type;
    }

    public String getRecharge_money() {
        return recharge_money;
    }

    public void setRecharge_money(String recharge_money) {
        this.recharge_money = recharge_money;
    }

    public int getRecharge_time() {
        return recharge_time;
    }

    public void setRecharge_time(int recharge_time) {
        this.recharge_time = recharge_time;
    }

    public int getRefund_type() {
        return refund_type;
    }

    public void setRefund_type(int refund_type) {
        this.refund_type = refund_type;
    }

    public int getShould_deduct() {
        return should_deduct;
    }

    public void setShould_deduct(int should_deduct) {
        this.should_deduct = should_deduct;
    }

    public int getActual_deduct() {
        return actual_deduct;
    }

    public void setActual_deduct(int actual_deduct) {
        this.actual_deduct = actual_deduct;
    }

    public int getCtime() {
        return ctime;
    }

    public void setCtime(int ctime) {
        this.ctime = ctime;
    }
}
