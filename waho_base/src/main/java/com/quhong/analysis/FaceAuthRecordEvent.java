package com.quhong.analysis;

/**
 * <AUTHOR>
 * @date 2024/7/12
 */
public class FaceAuthRecordEvent extends UserEvent {

    private int source; // 来源
    private int verification_state; // 验证状态
    private int ctime;

    @Override
    public String getEventName() {
        return "face_authentication_record";
    }

    @Override
    public int getEventTime() {
        return ctime;
    }

    public int getSource() {
        return source;
    }

    public void setSource(int source) {
        this.source = source;
    }

    public int getVerification_state() {
        return verification_state;
    }

    public void setVerification_state(int verification_state) {
        this.verification_state = verification_state;
    }

    public int getCtime() {
        return ctime;
    }

    public void setCtime(int ctime) {
        this.ctime = ctime;
    }
}
