package com.quhong.analysis;

/**
 * <AUTHOR>
 * @date 2025/3/24
 */
public class MakeFriendRecordEvent extends UserEvent{

    private String from_uid;
    private int from_time;
    private String to_uid;
    private int make_friend_type; // 类型 1 成为好友 -1删除好友
    private int ctime;

    @Override
    public String getEventName() {
        return "make_friend_record";
    }

    @Override
    public int getEventTime() {
        return ctime;
    }

    public String getFrom_uid() {
        return from_uid;
    }

    public void setFrom_uid(String from_uid) {
        this.from_uid = from_uid;
    }

    public int getFrom_time() {
        return from_time;
    }

    public void setFrom_time(int from_time) {
        this.from_time = from_time;
    }

    public String getTo_uid() {
        return to_uid;
    }

    public void setTo_uid(String to_uid) {
        this.to_uid = to_uid;
    }

    public int getMake_friend_type() {
        return make_friend_type;
    }

    public void setMake_friend_type(int make_friend_type) {
        this.make_friend_type = make_friend_type;
    }

    public int getCtime() {
        return ctime;
    }

    public void setCtime(int ctime) {
        this.ctime = ctime;
    }
}
