package com.quhong.analysis;

/**
 * moment_interact_log
 * 点赞动态后上报：uid，moment_id（动态id），moment_uid（发动态的用户uid），moment_interact_type=1，ctime
 * 评论动态后上报：uid，moment_id（动态id），moment_uid（发动态的用户uid），moment_interact_type=2，ctime
 * 点赞动态里的评论后上报：uid，moment_id（动态id），moment_uid（发动态的用户uid），moment_interact_type=3，ctime
 * 回复动态里的评论后上报：uid，moment_id（动态id），moment_uid（发动态的用户uid），moment_interact_type=4，ctime
 */
public class MomentInteractLogEvent extends UserEvent {
    private String moment_id; // 动态id
    private String moment_uid; // 发动态的用户uid
    private int moment_interact_type; // 1点赞动态后上报 2评论动态后上报 3点赞动态里的评论后上报 4回复动态里的评论后上报 5发送礼物打赏后上报 6取消动态的点赞 7 取消动态里的评论点赞
    private int ctime;

    @Override
    public String getEventName() {
        return "moment_interact_log";
    }

    @Override
    public int getEventTime() {
        return 0;
    }

    public String getMoment_id() {
        return moment_id;
    }

    public void setMoment_id(String moment_id) {
        this.moment_id = moment_id;
    }

    public String getMoment_uid() {
        return moment_uid;
    }

    public void setMoment_uid(String moment_uid) {
        this.moment_uid = moment_uid;
    }

    public int getMoment_interact_type() {
        return moment_interact_type;
    }

    public void setMoment_interact_type(int moment_interact_type) {
        this.moment_interact_type = moment_interact_type;
    }

    public int getCtime() {
        return ctime;
    }

    public void setCtime(int ctime) {
        this.ctime = ctime;
    }
}
