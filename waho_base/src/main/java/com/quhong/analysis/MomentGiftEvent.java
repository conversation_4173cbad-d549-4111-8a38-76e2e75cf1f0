package com.quhong.analysis;

/**
 * moment_gift
 */
public class MomentGiftEvent extends UserEvent {

    private String moment_id;
    private String from_uid; // 送礼方
    private String to_uid; // 收礼方
    private int gift_id; // 礼物ID
    private int gift_number;
    private int gift_price;
    private int gift_price_type; // 1: 钻石  2: 心心
    private int earn_beans;
    private int ctime;

    @Override
    public String getEventName() {
        return "moment_gift";
    }

    @Override
    public int getEventTime() {
        return 0;
    }

    public String getMoment_id() {
        return moment_id;
    }

    public void setMoment_id(String moment_id) {
        this.moment_id = moment_id;
    }

    public String getFrom_uid() {
        return from_uid;
    }

    public void setFrom_uid(String from_uid) {
        this.from_uid = from_uid;
    }

    public String getTo_uid() {
        return to_uid;
    }

    public void setTo_uid(String to_uid) {
        this.to_uid = to_uid;
    }

    public int getGift_id() {
        return gift_id;
    }

    public void setGift_id(int gift_id) {
        this.gift_id = gift_id;
    }

    public int getGift_number() {
        return gift_number;
    }

    public void setGift_number(int gift_number) {
        this.gift_number = gift_number;
    }

    public int getGift_price() {
        return gift_price;
    }

    public void setGift_price(int gift_price) {
        this.gift_price = gift_price;
    }

    public int getGift_price_type() {
        return gift_price_type;
    }

    public void setGift_price_type(int gift_price_type) {
        this.gift_price_type = gift_price_type;
    }

    public int getEarn_beans() {
        return earn_beans;
    }

    public void setEarn_beans(int earn_beans) {
        this.earn_beans = earn_beans;
    }

    public int getCtime() {
        return ctime;
    }

    public void setCtime(int ctime) {
        this.ctime = ctime;
    }
}
