package com.quhong.analysis;

/**
 * <AUTHOR>
 * @date 2025/2/18
 */
public class PkActivityRecordEvent extends UserEvent {

    private String activity_pk_pid;    // 活动PK场次id
    private String from_uid;           // 发起方uid
    private String to_uid;             // 接收方uid
    private int from_uid_score;    // 发起方分值
    private int to_uid_score;      // 接收方分值
    private int confront_start_at;    // 对抗开始时间 (时间戳)
    private int confront_end_at;      // 对抗结束时间 (时间戳)
    private int result;            // pk结果 1发起者赢 2受邀者赢 3平局
    private String win_uid;           // 获胜者uid
    private String from_no1_uid;      // 发起方榜1大哥uid
    private String to_no1_uid;        // 接收方榜1大哥uid
    private int ctime;               // 数据创建时间 (时间戳)

    @Override
    public String getEventName() {
        return "pk_activity_record";
    }

    @Override
    public int getEventTime() {
        return ctime;
    }

    public String getActivity_pk_pid() {
        return activity_pk_pid;
    }

    public void setActivity_pk_pid(String activity_pk_pid) {
        this.activity_pk_pid = activity_pk_pid;
    }

    public String getFrom_uid() {
        return from_uid;
    }

    public void setFrom_uid(String from_uid) {
        this.from_uid = from_uid;
    }

    public String getTo_uid() {
        return to_uid;
    }

    public void setTo_uid(String to_uid) {
        this.to_uid = to_uid;
    }

    public int getFrom_uid_score() {
        return from_uid_score;
    }

    public void setFrom_uid_score(int from_uid_score) {
        this.from_uid_score = from_uid_score;
    }

    public int getTo_uid_score() {
        return to_uid_score;
    }

    public void setTo_uid_score(int to_uid_score) {
        this.to_uid_score = to_uid_score;
    }

    public int getConfront_start_at() {
        return confront_start_at;
    }

    public void setConfront_start_at(int confront_start_at) {
        this.confront_start_at = confront_start_at;
    }

    public int getConfront_end_at() {
        return confront_end_at;
    }

    public void setConfront_end_at(int confront_end_at) {
        this.confront_end_at = confront_end_at;
    }

    public int getResult() {
        return result;
    }

    public void setResult(int result) {
        this.result = result;
    }

    public String getWin_uid() {
        return win_uid;
    }

    public void setWin_uid(String win_uid) {
        this.win_uid = win_uid;
    }

    public String getFrom_no1_uid() {
        return from_no1_uid;
    }

    public void setFrom_no1_uid(String from_no1_uid) {
        this.from_no1_uid = from_no1_uid;
    }

    public String getTo_no1_uid() {
        return to_no1_uid;
    }

    public void setTo_no1_uid(String to_no1_uid) {
        this.to_no1_uid = to_no1_uid;
    }

    public int getCtime() {
        return ctime;
    }

    public void setCtime(int ctime) {
        this.ctime = ctime;
    }
}
