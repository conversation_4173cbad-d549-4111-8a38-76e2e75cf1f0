package com.quhong.analysis;

/**
 * 抽奖记录
 *
 */
public class DrawPrizeRecordEvent extends UserEvent {

    public String sence; // 游戏id
    public int sence_detail; //
    public String room_id; // 房间id
    public int cost_diamonds; // 消耗钻石数
    public int ticket_type; // 抽奖卷类型
    public int cost_ticket; // 消耗抽奖券数
    public int cost_score; // 消耗积分
    public int draw_nums; // 抽奖次数
    public int draw_success_nums; // 中奖次数
    public String desc; // 描述备注  lucky_hit
    public String draw_detail; // 抽奖细节描述
    public String draw_result; // 抽奖结果
    public int ctime; // 数据创建时间

    @Override
    public String getEventName() {
        return "draw_prizes_record";
    }

    @Override
    public int getEventTime() {
        return ctime;
    }

    public String getRoom_id() {
        return room_id;
    }

    public void setRoom_id(String room_id) {
        this.room_id = room_id;
    }

    public String getSence() {
        return sence;
    }

    public void setSence(String sence) {
        this.sence = sence;
    }

    public int getSence_detail() {
        return sence_detail;
    }

    public void setSence_detail(int sence_detail) {
        this.sence_detail = sence_detail;
    }

    public int getCost_diamonds() {
        return cost_diamonds;
    }

    public void setCost_diamonds(int cost_diamonds) {
        this.cost_diamonds = cost_diamonds;
    }

    public int getCost_ticket() {
        return cost_ticket;
    }

    public void setCost_ticket(int cost_ticket) {
        this.cost_ticket = cost_ticket;
    }

    public int getDraw_nums() {
        return draw_nums;
    }

    public void setDraw_nums(int draw_nums) {
        this.draw_nums = draw_nums;
    }

    public String getDesc() {
        return desc;
    }

    public void setDesc(String desc) {
        this.desc = desc;
    }

    public String getDraw_detail() {
        return draw_detail;
    }

    public void setDraw_detail(String draw_detail) {
        this.draw_detail = draw_detail;
    }

    public String getDraw_result() {
        return draw_result;
    }

    public void setDraw_result(String draw_result) {
        this.draw_result = draw_result;
    }

    public int getDraw_success_nums() {
        return draw_success_nums;
    }

    public void setDraw_success_nums(int draw_success_nums) {
        this.draw_success_nums = draw_success_nums;
    }

    public int getCtime() {
        return ctime;
    }

    public void setCtime(int ctime) {
        this.ctime = ctime;
    }

    public int getCost_score() {
        return cost_score;
    }

    public void setCost_score(int cost_score) {
        this.cost_score = cost_score;
    }

    public int getTicket_type() {
        return ticket_type;
    }

    public void setTicket_type(int ticket_type) {
        this.ticket_type = ticket_type;
    }
}
