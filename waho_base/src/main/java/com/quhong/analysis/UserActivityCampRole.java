package com.quhong.analysis;

public class UserActivityCampRole extends UserEvent {

    private int ctime;
    private String activity_name;
    private String active_id;
    private String camp;

    @Override
    public String getEventName() {
        return "user_activity_camp_role";
    }

    @Override
    public int getEventTime() {
        return ctime;
    }

    public int getCtime() {
        return ctime;
    }

    public void setCtime(int ctime) {
        this.ctime = ctime;
    }

    public String getActivity_name() {
        return activity_name;
    }

    public void setActivity_name(String activity_name) {
        this.activity_name = activity_name;
    }

    public String getActive_id() {
        return active_id;
    }

    public void setActive_id(String active_id) {
        this.active_id = active_id;
    }

    public String getCamp() {
        return camp;
    }

    public void setCamp(String camp) {
        this.camp = camp;
    }
}
