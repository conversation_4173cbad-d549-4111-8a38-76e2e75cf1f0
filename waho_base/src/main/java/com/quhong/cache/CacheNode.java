package com.quhong.cache;

public class CacheNode<K, D> {
    protected D data;
    protected K key;
    protected long expireTimeMillisecond;
    protected long expireIntervalMillisecond;
    protected long closeToExpireMillisecond = 1000L; //接近过期时间为1s

    public CacheNode(long expireIntervalMillisecond) {
        this.expireIntervalMillisecond = expireIntervalMillisecond;
        if (this.expireIntervalMillisecond <= 1000) {
            this.closeToExpireMillisecond = expireIntervalMillisecond / 10;
        }
        this.extendExpireTime();
    }

    public void reset() {
        this.clear();
        this.extendExpireTime();
    }

    public void clear() {
        this.data = null;
        this.key = null;
    }

    public void extendExpireTime() {
        extendExpireTime(expireIntervalMillisecond);
    }

    public void extendExpireTime(long time) {
        this.expireTimeMillisecond = System.currentTimeMillis() + time;
    }

    public void addExpireTime(long time) {
        this.expireTimeMillisecond += time;
    }

    public boolean isExpire() {
        return System.currentTimeMillis() >= this.expireTimeMillisecond;
    }

    /**
     * 是否接近过期
     *
     * @return
     */
    public boolean isNearExpire() {
        return System.currentTimeMillis() >= this.expireTimeMillisecond - closeToExpireMillisecond;
    }

    /**
     * 获取缓存写入之间的时间，单位毫秒
     */
    public long getWriteTime() {
        return expireIntervalMillisecond - (expireTimeMillisecond - System.currentTimeMillis());
    }

    public D getData() {
        return data;
    }

    public K getKey() {
        return key;
    }

    public void setData(K key, D data) {
        this.key = key;
        this.data = data;
    }
}
