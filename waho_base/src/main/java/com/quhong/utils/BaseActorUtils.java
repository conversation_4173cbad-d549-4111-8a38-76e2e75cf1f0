package com.quhong.utils;

import com.quhong.core.utils.DateHelper;
import org.bson.types.ObjectId;

public class BaseActorUtils {

    /**
     * 是否最近n天注册的用户
     *
     * @param day 最近n天
     */
    public static boolean isNewRegisterActor(String uid, int day) {
        long registerTime = new ObjectId(uid).getTimestamp();
        return (registerTime + day * 24 * 60 * 60L) > DateHelper.getNowSeconds();
    }
}
