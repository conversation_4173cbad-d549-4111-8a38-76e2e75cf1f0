package com.quhong.msg.obj;

import com.quhong.core.msg.IProto;
import com.quhong.proto.WahoProtoRoom;

public class MsgInfoObject implements IProto<WahoProtoRoom.MsgInfo> {
    private String fromUid = ""; //发送者uid
    private String uname = "";
    private String content = "";  //消息内容
    private int msgType; //消息类型
    private int msgCategory;  //消息分类
    private int sendTime; //发送时间，单位/秒
    private UNameObject uanme2;
    private int isRepeatMsg; //是否是重复消息 0不是 1是 2是重复消息的第一条 3重复消息的最后一条
    private String fromLabel; // 发送者公屏发言标签，例如：d-1,w-2,m-3
    private int pkTeam; // pk队伍 1黄队 2蓝队
    private String ownerAid; // 房主aid

    private WahoProtoRoom.MsgInfo.Builder cacheBuilder;

    @Override
    public void doFromBody(WahoProtoRoom.MsgInfo proto) {
        this.fromUid = proto.getFromUid();
        this.uname = proto.getUname();
        this.content = proto.getContent();
        this.msgType = proto.getMsgType();
        this.msgCategory = proto.getMsgCategory();
        this.sendTime = proto.getSendTime();
        if (proto.getUname2() != null) {
            this.uanme2 = new UNameObject();
            this.uanme2.doFromBody(proto.getUname2());
        }
        this.isRepeatMsg = proto.getIsRepeatMsg();
        this.fromLabel = proto.getFromLabel();
        this.pkTeam = proto.getPkTeam();
        this.ownerAid = proto.getOwnerAid();
    }

    @Override
    public WahoProtoRoom.MsgInfo.Builder doToBody() {
        if (cacheBuilder != null) {
            cacheBuilder.build();
        }
        WahoProtoRoom.MsgInfo.Builder builder = WahoProtoRoom.MsgInfo.newBuilder();
        builder.setFromUid(fromUid == null ? "" : fromUid);
        builder.setUname(uname == null ? "" : uname);
        builder.setContent(content == null ? "" : content);
        builder.setFromLabel(fromLabel == null ? "" : fromLabel);
        builder.setOwnerAid(ownerAid == null ? "" : ownerAid);
        builder.setMsgType(msgType);
        builder.setMsgCategory(msgCategory);
        builder.setSendTime(sendTime);
        if (this.uanme2 != null) {
            builder.setUname2(this.uanme2.doToBody());
        }
        builder.setIsRepeatMsg(isRepeatMsg);
        builder.setPkTeam(pkTeam);
        this.cacheBuilder = builder;
        return builder;
    }

    public String getFromUid() {
        return fromUid;
    }

    public void setFromUid(String fromUid) {
        this.fromUid = fromUid;
    }

    public String getUname() {
        return uname;
    }

    public void setUname(String uname) {
        this.uname = uname;
    }

    public String getContent() {
        return content;
    }

    public void setContent(String content) {
        this.content = content;
    }

    public int getMsgType() {
        return msgType;
    }

    public void setMsgType(int msgType) {
        this.msgType = msgType;
    }

    public int getMsgCategory() {
        return msgCategory;
    }

    public void setMsgCategory(int msgCategory) {
        this.msgCategory = msgCategory;
    }

    public int getSendTime() {
        return sendTime;
    }

    public void setSendTime(int sendTime) {
        this.sendTime = sendTime;
    }

    public UNameObject getUanme2() {
        return uanme2;
    }

    public void setUanme2(UNameObject uanme2) {
        this.uanme2 = uanme2;
    }

    public int getIsRepeatMsg() {
        return isRepeatMsg;
    }

    public void setIsRepeatMsg(int isRepeatMsg) {
        this.isRepeatMsg = isRepeatMsg;
    }

    public String getFromLabel() {
        return fromLabel;
    }

    public void setFromLabel(String fromLabel) {
        this.fromLabel = fromLabel;
    }

    public WahoProtoRoom.MsgInfo.Builder getCacheBuilder() {
        return cacheBuilder;
    }

    public void setCacheBuilder(WahoProtoRoom.MsgInfo.Builder cacheBuilder) {
        this.cacheBuilder = cacheBuilder;
    }

    public int getPkTeam() {
        return pkTeam;
    }

    public void setPkTeam(int pkTeam) {
        this.pkTeam = pkTeam;
    }

    public String getOwnerAid() {
        return ownerAid;
    }

    public void setOwnerAid(String ownerAid) {
        this.ownerAid = ownerAid;
    }
}
