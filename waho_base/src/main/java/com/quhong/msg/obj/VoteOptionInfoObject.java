package com.quhong.msg.obj;

import com.quhong.core.msg.IProto;
import com.quhong.proto.WahoProtoRoom;

/**
 * <AUTHOR>
 * @date 2023/3/6
 */
public class VoteOptionInfoObject implements IProto<WahoProtoRoom.VoteOptionInfo> {

    /**
     * 投票选项id
     */
    private int id;

    /**
     * 选项内容
     */
    private String optionContent;

    /**
     * 用户uid （礼物投票才有）
     */
    private String aid;

    /**
     * 用户昵称 （礼物投票才有）
     */
    private String userName;

    /**
     * 用户头像（礼物投票才有）
     */
    private String userHead;

    /**
     * 礼物id（礼物投票才有）
     */
    private int giftId;

    /**
     * 礼物icon（礼物投票才有）
     */
    private String giftIcon;

    /**
     * 票数
     */
    private int votesNum;

    @Override
    public void doFromBody(WahoProtoRoom.VoteOptionInfo proto) {
        this.id = proto.getId();
        this.optionContent = proto.getOptionContent();
        this.aid = proto.getAid();
        this.userName = proto.getUserName();
        this.userHead = proto.getUserHead();
        this.giftIcon = proto.getGiftIcon();
        this.votesNum = proto.getVotesNum();
        this.giftId = proto.getGiftId();
    }

    @Override
    public WahoProtoRoom.VoteOptionInfo.Builder doToBody() {
        WahoProtoRoom.VoteOptionInfo.Builder builder = WahoProtoRoom.VoteOptionInfo.newBuilder();
        builder.setId(this.id);
        builder.setOptionContent(this.optionContent == null ? "" : this.optionContent);
        builder.setAid(this.aid == null ? "" : this.aid);
        builder.setUserName(this.userName == null ? "" : this.userName);
        builder.setUserHead(this.userHead == null ? "" : this.userHead);
        builder.setGiftIcon(this.giftIcon == null ? "" : this.giftIcon);
        builder.setVotesNum(this.votesNum);
        builder.setGiftId(this.giftId);
        return builder;
    }

    public int getId() {
        return id;
    }

    public void setId(int id) {
        this.id = id;
    }

    public String getOptionContent() {
        return optionContent;
    }

    public void setOptionContent(String optionContent) {
        this.optionContent = optionContent;
    }

    public String getAid() {
        return aid;
    }

    public void setAid(String aid) {
        this.aid = aid;
    }

    public String getUserName() {
        return userName;
    }

    public void setUserName(String userName) {
        this.userName = userName;
    }

    public String getUserHead() {
        return userHead;
    }

    public void setUserHead(String userHead) {
        this.userHead = userHead;
    }

    public String getGiftIcon() {
        return giftIcon;
    }

    public void setGiftIcon(String giftIcon) {
        this.giftIcon = giftIcon;
    }

    public int getVotesNum() {
        return votesNum;
    }

    public void setVotesNum(int votesNum) {
        this.votesNum = votesNum;
    }

    public int getGiftId() {
        return giftId;
    }

    public void setGiftId(int giftId) {
        this.giftId = giftId;
    }
}
