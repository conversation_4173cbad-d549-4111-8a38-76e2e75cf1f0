package com.quhong.msg.obj;

import com.quhong.core.msg.IProto;
import com.quhong.proto.WahoProtoRoomMicInfo;

public class RoomMicInfoObject implements IProto<WahoProtoRoomMicInfo.RoomMicInfo> {
    private int index; // 麦位位置
    private int status; // 麦位状态 麦位状态 0空闲 1有人 2上锁
    private int mute; // 麦位禁音 0未静音 1静音
    private RoomMicUserObject user; // 麦位用户信息 status = 1返回
    private int pkMute; // 1LivePk静音主播

    @Override
    public void doFromBody(WahoProtoRoomMicInfo.RoomMicInfo proto) {
        this.index = proto.getIndex();
        this.status = proto.getStatus();
        this.mute = proto.getMute();
        this.user = new RoomMicUserObject();
        this.user.doFromBody(proto.getUser());
        this.pkMute = proto.getPkMute();
    }

    @Override
    public WahoProtoRoomMicInfo.RoomMicInfo.Builder doToBody() {
        WahoProtoRoomMicInfo.RoomMicInfo.Builder builder = WahoProtoRoomMicInfo.RoomMicInfo.newBuilder();
        builder.setIndex(index);
        builder.setStatus(status);
        builder.setMute(mute);
        if (this.user != null) {
            builder.setUser(this.user.doToBody());
        }
        builder.setPkMute(pkMute);
        return builder;
    }

    public int getIndex() {
        return index;
    }

    public void setIndex(int index) {
        this.index = index;
    }

    public int getStatus() {
        return status;
    }

    public void setStatus(int status) {
        this.status = status;
    }

    public int getMute() {
        return mute;
    }

    public void setMute(int mute) {
        this.mute = mute;
    }

    public RoomMicUserObject getUser() {
        return user;
    }

    public void setUser(RoomMicUserObject user) {
        this.user = user;
    }

    public int getPkMute() {
        return pkMute;
    }

    public void setPkMute(int pkMute) {
        this.pkMute = pkMute;
    }
}
