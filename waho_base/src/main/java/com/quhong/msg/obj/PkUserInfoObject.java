package com.quhong.msg.obj;

import com.quhong.core.msg.IProto;
import com.quhong.proto.WahoProtoUname;

public class PkUserInfoObject implements IProto<WahoProtoUname.PkUserInfo> {
    private String uid;
    private String name;
    private String head;
    private String score;
    private int mvpLevel;
    private int team;
    private int top; // mvpLevel和top都不为0时优先显示mvp
    private int intScore; // 内部排序字段
    private int mystery; // 1神秘人


    @Override
    public void doFromBody(WahoProtoUname.PkUserInfo proto) {
        this.uid = proto.getUid();
        this.name = proto.getName();
        this.head = proto.getHead();
        this.score = proto.getScore();
        this.mvpLevel = proto.getMvpLevel();
        this.team = proto.getTeam();
        this.top = proto.getTop();
        this.mystery = proto.getMystery();
    }

    @Override
    public WahoProtoUname.PkUserInfo.Builder doToBody() {
        WahoProtoUname.PkUserInfo.Builder builder = WahoProtoUname.PkUserInfo.newBuilder();
        builder.setUid(uid == null ? "" : uid);
        builder.setName(name == null ? "" : name);
        builder.setHead(head == null ? "" : head);
        builder.setScore(score == null ? "" : score);
        builder.setMvpLevel(mvpLevel);
        builder.setTeam(team);
        builder.setTop(top);
        builder.setMystery(mystery);
        return builder;
    }

    public String getUid() {
        return uid;
    }

    public void setUid(String uid) {
        this.uid = uid;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getHead() {
        return head;
    }

    public void setHead(String head) {
        this.head = head;
    }

    public String getScore() {
        return score;
    }

    public void setScore(String score) {
        this.score = score;
    }

    public int getMvpLevel() {
        return mvpLevel;
    }

    public void setMvpLevel(int mvpLevel) {
        this.mvpLevel = mvpLevel;
    }

    public int getTeam() {
        return team;
    }

    public void setTeam(int team) {
        this.team = team;
    }

    public int getTop() {
        return top;
    }

    public void setTop(int top) {
        this.top = top;
    }

    public int getIntScore() {
        return intScore;
    }

    public void setIntScore(int intScore) {
        this.intScore = intScore;
    }

    public int getMystery() {
        return mystery;
    }

    public void setMystery(int mystery) {
        this.mystery = mystery;
    }
}
