package com.quhong.msg.obj;

import com.alibaba.fastjson.JSONObject;
import com.quhong.core.msg.IProto;
import com.quhong.proto.WahoProtoLuckyWheel;

public class LuckyWheelPlayer implements IProto<WahoProtoLuckyWheel.Player> {
    private String _id;
    private String name;
    private int fb_gender;
    private String head;
    private int rid;
    private int mystery;

    public void fillFrom(JSONObject object){
        this._id = object.getString("_id");
        this.name = object.getString("name");
        this.fb_gender = object.getIntValue("fb_gender");
        this.head = object.getString("head");
        this.rid = object.getIntValue("rid");
        this.mystery = object.getIntValue("mystery");
    }

    @Override
    public void doFromBody(WahoProtoLuckyWheel.Player proto) {
        this._id = proto.getId();
        this.name = proto.getName();
        this.fb_gender = proto.getFbGender();
        this.head = proto.getHead();
        this.rid = proto.getRid();
        this.mystery = proto.getMystery();
    }

    @Override
    public WahoProtoLuckyWheel.Player.Builder doToBody() {
        WahoProtoLuckyWheel.Player.Builder builder = WahoProtoLuckyWheel.Player.newBuilder();
        builder.setId(this._id == null ? "" : this._id);
        builder.setName(this.name == null ? "" : this.name);
        builder.setFbGender(this.fb_gender);
        builder.setHead(this.head == null ? "" : this.head);
        builder.setRid(this.rid);
        builder.setMystery(this.mystery);
        return builder;
    }

    public String get_id() {
        return _id;
    }

    public void set_id(String _id) {
        this._id = _id;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public int getFb_gender() {
        return fb_gender;
    }

    public void setFb_gender(int fb_gender) {
        this.fb_gender = fb_gender;
    }

    public String getHead() {
        return head;
    }

    public void setHead(String head) {
        this.head = head;
    }

    public int getRid() {
        return rid;
    }

    public void setRid(int rid) {
        this.rid = rid;
    }

    public int getMystery() {
        return mystery;
    }

    public void setMystery(int mystery) {
        this.mystery = mystery;
    }
}
