package com.quhong.msg.room;

import com.quhong.core.annotation.Message;
import com.quhong.enums.Cmd;
import com.quhong.msg.MarsCacheMsg;
import com.quhong.msg.MarsServerMsg;
import com.quhong.msg.obj.MsgInfoObject;
import com.quhong.proto.WahoProtoRoom;

import java.util.ArrayList;
import java.util.List;

@Message(cmd = Cmd.SEND_TEXT_MSG_PUSH)
public class SendRoomPushMsg extends MarsCacheMsg {
    private List<MsgInfoObject> msgList;

    @Override
    protected void doFromBody(byte[] bytes) throws Exception {
        WahoProtoRoom.SendRoomPushMessage msg = WahoProtoRoom.SendRoomPushMessage.parseFrom(bytes);
        this.protoHeader.doFromBody(msg.getHeader());
        this.msgList = new ArrayList<>();
        for (WahoProtoRoom.MsgInfo msgInfo : msg.getMsgListList()) {
            MsgInfoObject object = new MsgInfoObject();
            object.doFromBody(msgInfo);
            this.msgList.add(object);
        }
    }

    @Override
    protected byte[] doToBody() throws Exception {
        if (this.cacheBody != null) {
            return this.cacheBody;
        }
        WahoProtoRoom.SendRoomPushMessage.Builder builder = WahoProtoRoom.SendRoomPushMessage.newBuilder();
        builder.setHeader(this.protoHeader.doToBody());
        if (this.msgList != null) {
            for (MsgInfoObject object : this.msgList) {
                builder.addMsgList(object.doToBody());
            }
        }
        byte[] body = builder.build().toByteArray();
        this.cacheBody(body);
        return body;
    }

    public void add(MsgInfoObject object) {
        if (this.msgList == null) {
            this.msgList = new ArrayList<>();
        }
        this.msgList.add(object);
    }

    public List<MsgInfoObject> getMsgList() {
        return msgList;
    }

    public void setMsgList(List<MsgInfoObject> msgList) {
        this.msgList = msgList;
    }
}
