package com.quhong.msg.room;

import com.alibaba.fastjson.JSONObject;
import com.quhong.core.annotation.Message;
import com.quhong.enums.Cmd;
import com.quhong.msg.MarsServerMsg;
import com.quhong.msg.obj.UserInfoObject;
import com.quhong.proto.WahoProtoRoom;

@Message(cmd = Cmd.OPT_BAN_TXT_PUSH)
public class OptBanTxtPushMsg extends MarsServerMsg {
    private String from_name;
    private String from_Img;
    private int opt; // 1 禁言  0取消禁言
    private UserInfoObject opt_user;
    private int official; // 1官方管理员

    public void fillFrom(JSONObject object, boolean ban) {
        this.from_name = object.getString("from_name");
        this.from_Img = object.getString("from_img");
        this.opt = ban ? 1 : 0;
        JSONObject userObj = object.getJSONObject("opt_user");
        if (userObj != null) {
            opt_user = new UserInfoObject();
            opt_user.fillFrom(userObj);
        }
    }

    @Override
    protected void doFromBody(byte[] bytes) throws Exception {
        WahoProtoRoom.OptBandTxtMessage msg = WahoProtoRoom.OptBandTxtMessage.parseFrom(bytes);
        this.protoHeader.doFromBody(msg.getHeader());
        this.from_name = msg.getFromName();
        this.from_Img = msg.getFromImg();
        this.official = msg.getOfficial();
        this.opt = msg.getOpt();
        if (msg.getOptUser() != null) {
            this.opt_user = new UserInfoObject();
            this.opt_user.doFromBody(msg.getOptUser());
        }
    }

    @Override
    protected byte[] doToBody() throws Exception {
        WahoProtoRoom.OptBandTxtMessage.Builder builder = WahoProtoRoom.OptBandTxtMessage.newBuilder();
        builder.setHeader(this.protoHeader.doToBody());
        builder.setFromName(this.from_name == null ? "" : this.from_name);
        builder.setFromImg(this.from_Img == null ? "" : this.from_Img);
        builder.setOpt(this.opt);
        builder.setOfficial(this.official);
        if (this.opt_user != null) {
            builder.setOptUser(this.opt_user.doToBody());
        }
        return builder.build().toByteArray();
    }

    public String getFrom_name() {
        return from_name;
    }

    public void setFrom_name(String from_name) {
        this.from_name = from_name;
    }

    public String getFrom_Img() {
        return from_Img;
    }

    public void setFrom_Img(String from_Img) {
        this.from_Img = from_Img;
    }

    public int getOpt() {
        return opt;
    }

    public void setOpt(int opt) {
        this.opt = opt;
    }

    public UserInfoObject getOpt_user() {
        return opt_user;
    }

    public void setOpt_user(UserInfoObject opt_user) {
        this.opt_user = opt_user;
    }

    public int getOfficial() {
        return official;
    }

    public void setOfficial(int official) {
        this.official = official;
    }
}
