package com.quhong.msg.room;

import com.quhong.core.annotation.Message;
import com.quhong.enums.Cmd;
import com.quhong.msg.MarsServerMsg;
import com.quhong.proto.WahoProtoRoom;

@Message(cmd = Cmd.SEND_TEXT_MSG)
public class SendRoomMsg extends MarsServerMsg {
    private String uname = "";
    private String content = "";  //消息内容
    private int msgType; //消息类型
    private int msgCategory;  //消息分类
    private int slang; // 1 英语 2 阿语 3 土耳其语
    private int isRepeatMsg; // 是否是重复消息 0不是 1是 2是重复消息的第一条 3重复消息的最后一条
    private int pkTeam; // pk队伍 1黄队 2蓝队
    private String ownerAid; // 房主aid

    @Override
    protected void doFromBody(byte[] bytes) throws Exception {
        WahoProtoRoom.SendRoomMessage msg = WahoProtoRoom.SendRoomMessage.parseFrom(bytes);
        this.protoHeader.doFromBody(msg.getHeader());
        this.uname = msg.getUname();
        this.content = msg.getContent();
        this.msgType = msg.getMsgType();
        this.msgCategory = msg.getMsgCategory();
        this.slang = msg.getSlang();
        this.isRepeatMsg = msg.getIsRepeatMsg();
        this.pkTeam = msg.getPkTeam();
        this.ownerAid = msg.getOwnerAid();
    }

    @Override
    protected byte[] doToBody() throws Exception {
        WahoProtoRoom.SendRoomMessage.Builder builder = WahoProtoRoom.SendRoomMessage.newBuilder();
        builder.setHeader(this.protoHeader.doToBody());
        builder.setUname(uname);
        builder.setContent(content);
        builder.setMsgType(msgType);
        builder.setMsgCategory(msgCategory);
        builder.setSlang(slang);
        builder.setIsRepeatMsg(isRepeatMsg);
        builder.setPkTeam(pkTeam);
        builder.setOwnerAid(null == ownerAid ? "" : ownerAid);
        return builder.build().toByteArray();
    }

    public String getUname() {
        return uname;
    }

    public void setUname(String uname) {
        this.uname = uname;
    }

    public String getContent() {
        return content;
    }

    public void setContent(String content) {
        this.content = content;
    }

    public int getMsgType() {
        return msgType;
    }

    public void setMsgType(int msgType) {
        this.msgType = msgType;
    }

    public int getMsgCategory() {
        return msgCategory;
    }

    public void setMsgCategory(int msgCategory) {
        this.msgCategory = msgCategory;
    }

    public int getSlang() {
        return slang;
    }

    public void setSlang(int slang) {
        this.slang = slang;
    }

    public int getIsRepeatMsg() {
        return isRepeatMsg;
    }

    public void setIsRepeatMsg(int isRepeatMsg) {
        this.isRepeatMsg = isRepeatMsg;
    }

    public int getPkTeam() {
        return pkTeam;
    }

    public void setPkTeam(int pkTeam) {
        this.pkTeam = pkTeam;
    }

    public String getOwnerAid() {
        return ownerAid;
    }

    public void setOwnerAid(String ownerAid) {
        this.ownerAid = ownerAid;
    }
}
