package com.quhong.msg.room;

import com.alibaba.fastjson.JSONObject;
import com.quhong.core.annotation.Message;
import com.quhong.enums.Cmd;
import com.quhong.msg.MarsServerMsg;
import com.quhong.proto.WahoProtoRoom;

@Message(cmd = Cmd.ROOM_FOLLOW_ROOM_TIPS_MSG)
public class RoomFollowRoomTipsMsg extends MarsServerMsg {
    private String room_head; //房间头像
    private String room_id; //房间id
    private String msg; //关注消息英语
    private String msg_ar; //关注消息阿语


    @Override
    public void fillFrom(JSONObject data) {
        this.room_head = data.getString("room_head");
        this.room_id = data.getString("room_id");
        this.msg = data.getString("msg");
        this.msg_ar = data.getString("msg_ar");
    }

    @Override
    protected void doFromBody(byte[] bytes) throws Exception {
        WahoProtoRoom.RoomFollowRoomTipsMessage msg = WahoProtoRoom.RoomFollowRoomTipsMessage.parseFrom(bytes);
        this.protoHeader.doFromBody(msg.getHeader());
        this.room_head = msg.getRoomHead();
        this.room_id = msg.getRoomId();
        this.msg = msg.getMsg();
        this.msg_ar = msg.getMsgAr();
    }

    @Override
    protected byte[] doToBody() throws Exception {
        WahoProtoRoom.RoomFollowRoomTipsMessage.Builder builder = WahoProtoRoom.RoomFollowRoomTipsMessage.newBuilder();
        builder.setHeader(this.protoHeader.doToBody());
        builder.setRoomHead(room_head == null ? "" : room_head);
        builder.setRoomId(room_id == null ? "" : room_id);
        builder.setMsg(msg == null ? "" : msg);
        builder.setMsgAr(msg_ar == null ? "" : msg_ar);
        return builder.build().toByteArray();
    }

    public String getRoom_head() {
        return room_head;
    }

    public void setRoom_head(String room_head) {
        this.room_head = room_head;
    }

    public String getRoom_id() {
        return room_id;
    }

    public void setRoom_id(String room_id) {
        this.room_id = room_id;
    }

    public String getMsg() {
        return msg;
    }

    public void setMsg(String msg) {
        this.msg = msg;
    }

    public String getMsg_ar() {
        return msg_ar;
    }

    public void setMsg_ar(String msg_ar) {
        this.msg_ar = msg_ar;
    }
}
