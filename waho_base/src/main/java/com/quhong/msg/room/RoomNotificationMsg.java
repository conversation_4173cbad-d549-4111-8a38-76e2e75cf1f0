package com.quhong.msg.room;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.quhong.core.annotation.Message;
import com.quhong.enums.Cmd;
import com.quhong.msg.MarsServerMsg;
import com.quhong.msg.obj.HighlightTextObject;
import com.quhong.proto.WahoProtoHighlightText;
import com.quhong.proto.WahoProtoRoom;

import java.util.ArrayList;
import java.util.List;

@Message(cmd = Cmd.ROOM_NOTIFICATION_PUSH)
public class RoomNotificationMsg extends MarsServerMsg {
    private String uid; //用户uid
    private String user_name; //用户名字
    private String user_head; //用户头像
    private String text; //活动文案
    private String text_ar; //活动文案
    private List<HighlightTextObject> highlight_text; //通知样式，例如：[{text : string, highlightColor : string (例如#FFFFFF)}]
    private List<HighlightTextObject> highlight_text_ar; //通知样式，例如：[{text : string, highlightColor : string (例如#FFFFFF)}]
    private String web_url; //跳转url地址
    private int web_type; //网页打开方式 0 跳转 1 房间内全屏 2房间内半屏
    private String game_type; //smash_egg 为砸蛋
    private int width; //半宽宽度，web_type=1时需要设置
    private int height; //半屏高度，web_type=1时需要设置
    private int hide_head; // 是否隐藏头像，1是（过度处理，以后如果要隐藏头像，直接不下发user_head字段）
    private int floating; // 是否展示飘屏广播
    private String fromRoomId; // 来源房间id
    private int mystery; // 1神秘人

    @Override
    public void fillFrom(JSONObject object) {
        this.uid = object.getString("uid");
        this.user_name = object.getString("user_name");
        this.user_head = object.getString("user_head");
        this.text = object.getString("text");
        this.text_ar = object.getString("text_ar");
        this.web_url = object.getString("web_url");
        this.web_type = object.getIntValue("web_type");
        this.game_type = object.getString("game_type");
        this.width = object.getIntValue("width");
        this.height = object.getIntValue("height");
        JSONArray jsonArray = object.getJSONArray("highlight_text");
        if (jsonArray != null) {
            highlight_text = new ArrayList<>();
            for (Object o : jsonArray) {
                JSONObject jsonObject = (JSONObject) o;
                HighlightTextObject highlightTextObject = new HighlightTextObject();
                highlightTextObject.fillFrom(jsonObject);
                highlight_text.add(highlightTextObject);
            }
        }
        JSONArray jsonArrayAr = object.getJSONArray("highlight_text_ar");
        if (jsonArrayAr != null) {
            highlight_text_ar = new ArrayList<>();
            for (Object o : jsonArrayAr) {
                JSONObject jsonObject = (JSONObject) o;
                HighlightTextObject highlightTextObject = new HighlightTextObject();
                highlightTextObject.fillFrom(jsonObject);
                highlight_text_ar.add(highlightTextObject);
            }
        }
    }

    @Override
    protected void doFromBody(byte[] bytes) throws Exception {
        WahoProtoRoom.RoomNotificationMessage msg = WahoProtoRoom.RoomNotificationMessage.parseFrom(bytes);
        this.protoHeader.doFromBody(msg.getHeader());
        this.uid = msg.getUid();
        this.user_name = msg.getUserName();
        this.user_head = msg.getUserHead();
        this.text = msg.getText();
        this.text_ar = msg.getTextAr();
        this.web_url = msg.getWebUrl();
        this.web_type = msg.getWebType();
        this.game_type = msg.getGameType();
        this.width = msg.getWidth();
        this.height = msg.getHeight();
        this.hide_head = msg.getHideHead();
        this.highlight_text = new ArrayList<>();
        this.floating = msg.getFloating();
        this.fromRoomId = msg.getFromRoomId();
        this.mystery = msg.getMystery();
        for (WahoProtoHighlightText.HighlightText highlightText : msg.getHighlightTextList()) {
            HighlightTextObject highlightTextObject = new HighlightTextObject();
            highlightTextObject.doFromBody(highlightText);
            this.highlight_text.add(highlightTextObject);
        }
        this.highlight_text_ar = new ArrayList<>();
        for (WahoProtoHighlightText.HighlightText highlightText : msg.getHighlightTextArList()) {
            HighlightTextObject highlightTextObject = new HighlightTextObject();
            highlightTextObject.doFromBody(highlightText);
            this.highlight_text_ar.add(highlightTextObject);
        }
    }

    @Override
    protected byte[] doToBody() throws Exception {
        WahoProtoRoom.RoomNotificationMessage.Builder builder = WahoProtoRoom.RoomNotificationMessage.newBuilder();
        builder.setHeader(this.protoHeader.doToBody());
        builder.setUid(this.uid == null ? "" : this.uid);
        builder.setUserName(this.user_name == null ? "" : this.user_name);
        builder.setUserHead(this.user_head == null ? "" : this.user_head);
        builder.setText(this.text == null ? "" : this.text);
        builder.setTextAr(this.text_ar == null ? "" : this.text_ar);
        builder.setWebUrl(this.web_url == null ? "" : this.web_url);
        builder.setWebType(web_type);
        builder.setGameType(this.game_type == null ? "" : this.game_type);
        builder.setFromRoomId(this.fromRoomId == null ? "" : this.fromRoomId);
        builder.setWidth(width);
        builder.setHeight(height);
        builder.setHideHead(hide_head);
        builder.setFloating(floating);
        builder.setMystery(mystery);
        if (this.highlight_text != null) {
            for (HighlightTextObject highlightTextObject : highlight_text) {
                builder.addHighlightText(highlightTextObject.doToBody());
            }
        }
        if (this.highlight_text_ar != null) {
            for (HighlightTextObject highlightTextObject : highlight_text_ar) {
                builder.addHighlightTextAr(highlightTextObject.doToBody());
            }
        }
        return builder.build().toByteArray();
    }

    @Override
    public String getUid() {
        return uid;
    }

    public void setUid(String uid) {
        this.uid = uid;
    }

    public String getUser_name() {
        return user_name;
    }

    public void setUser_name(String user_name) {
        this.user_name = user_name;
    }

    public String getUser_head() {
        return user_head;
    }

    public void setUser_head(String user_head) {
        this.user_head = user_head;
    }

    public String getText() {
        return text;
    }

    public void setText(String text) {
        this.text = text;
    }

    public List<HighlightTextObject> getHighlight_text() {
        return highlight_text;
    }

    public void setHighlight_text(List<HighlightTextObject> highlight_text) {
        this.highlight_text = highlight_text;
    }

    public String getWeb_url() {
        return web_url;
    }

    public void setWeb_url(String web_url) {
        this.web_url = web_url;
    }

    public int getWeb_type() {
        return web_type;
    }

    public void setWeb_type(int web_type) {
        this.web_type = web_type;
    }

    public String getText_ar() {
        return text_ar;
    }

    public void setText_ar(String text_ar) {
        this.text_ar = text_ar;
    }

    public List<HighlightTextObject> getHighlight_text_ar() {
        return highlight_text_ar;
    }

    public void setHighlight_text_ar(List<HighlightTextObject> highlight_text_ar) {
        this.highlight_text_ar = highlight_text_ar;
    }

    public String getGame_type() {
        return game_type;
    }

    public void setGame_type(String game_type) {
        this.game_type = game_type;
    }

    public int getWidth() {
        return width;
    }

    public void setWidth(int width) {
        this.width = width;
    }

    public int getHeight() {
        return height;
    }

    public void setHeight(int height) {
        this.height = height;
    }

    public int getHide_head() {
        return hide_head;
    }

    public void setHide_head(int hide_head) {
        this.hide_head = hide_head;
    }

    public void setFloating(int floating) {
        this.floating = floating;
    }

    public int getFloating() {
        return floating;
    }

    public String getFromRoomId() {
        return fromRoomId;
    }

    public void setFromRoomId(String fromRoomId) {
        this.fromRoomId = fromRoomId;
    }

    public int getMystery() {
        return mystery;
    }

    public void setMystery(int mystery) {
        this.mystery = mystery;
    }
}
