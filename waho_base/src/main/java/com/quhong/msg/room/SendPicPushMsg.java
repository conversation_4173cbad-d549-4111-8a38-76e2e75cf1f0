package com.quhong.msg.room;

import com.alibaba.fastjson.JSONObject;
import com.quhong.core.annotation.Message;
import com.quhong.enums.Cmd;
import com.quhong.msg.MarsServerMsg;
import com.quhong.msg.obj.UNameObject;
import com.quhong.proto.WahoProtoRoom;

@Message(cmd = Cmd.SEND_PIC_PUSH)
public class SendPicPushMsg extends MarsServerMsg {
    private UNameObject name;
    private int width;
    private int height;
    private String url;
    private String thumbnailUrl; // 缩略图
    private int imageType;
    private String thumbnailStaticUrl;
    private int pkTeam; // pk队伍 1黄队 2蓝队

    @Override
    public void fillFrom(JSONObject object) {
        this.name = new UNameObject();
        this.width = object.getIntValue("width");
        this.height = object.getIntValue("height");
        this.url = object.getString("url");
        this.thumbnailUrl = object.getString("thumbnailUrl");
        this.imageType = object.getIntValue("imageType");
        this.thumbnailStaticUrl = object.getString("thumbnailStaticUrl");

    }

    @Override
    protected void doFromBody(byte[] bytes) throws Exception {
        WahoProtoRoom.SendPicMessage msg = WahoProtoRoom.SendPicMessage.parseFrom(bytes);
        this.protoHeader.doFromBody(msg.getHeader());
        this.name = new UNameObject();
        this.name.doFromBody(msg.getName());
        this.width = msg.getWidth();
        this.height = msg.getHeight();
        this.url = msg.getUrl();
        this.thumbnailUrl = msg.getThumbnailUrl();
        this.imageType = msg.getImageType();
        this.thumbnailStaticUrl = msg.getThumbnailStaticUrl();
        this.pkTeam = msg.getPkTeam();
    }

    @Override
    protected byte[] doToBody() throws Exception {
        WahoProtoRoom.SendPicMessage.Builder builder = WahoProtoRoom.SendPicMessage.newBuilder();
        builder.setHeader(this.protoHeader.doToBody());
        if (name != null) {
            builder.setName(this.name.doToBody());
        }
        builder.setWidth(width);
        builder.setHeight(height);
        builder.setUrl(url == null ? "" : url);
        builder.setThumbnailUrl(thumbnailUrl == null ? "" : thumbnailUrl);
        builder.setImageType(imageType);
        builder.setThumbnailStaticUrl(thumbnailStaticUrl == null ? "" : thumbnailStaticUrl);
        builder.setPkTeam(pkTeam);
        return builder.build().toByteArray();
    }

    public UNameObject getName() {
        return name;
    }

    public void setName(UNameObject name) {
        this.name = name;
    }

    public int getWidth() {
        return width;
    }

    public void setWidth(int width) {
        this.width = width;
    }

    public int getHeight() {
        return height;
    }

    public void setHeight(int height) {
        this.height = height;
    }

    public String getUrl() {
        return url;
    }

    public void setUrl(String url) {
        this.url = url;
    }

    public String getThumbnailUrl() {
        return thumbnailUrl;
    }

    public void setThumbnailUrl(String thumbnailUrl) {
        this.thumbnailUrl = thumbnailUrl;
    }

    public int getImageType() {
        return imageType;
    }

    public void setImageType(int imageType) {
        this.imageType = imageType;
    }

    public String getThumbnailStaticUrl() {
        return thumbnailStaticUrl;
    }

    public void setThumbnailStaticUrl(String thumbnailStaticUrl) {
        this.thumbnailStaticUrl = thumbnailStaticUrl;
    }

    public int getPkTeam() {
        return pkTeam;
    }

    public void setPkTeam(int pkTeam) {
        this.pkTeam = pkTeam;
    }
}
