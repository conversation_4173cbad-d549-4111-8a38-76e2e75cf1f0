package com.quhong.msg.room;

import com.alibaba.fastjson.JSONObject;
import com.quhong.core.annotation.Message;
import com.quhong.enums.Cmd;
import com.quhong.msg.MarsServerMsg;
import com.quhong.msg.obj.UserInfoObject;
import com.quhong.proto.WahoProtoRoom;

@Message(cmd = Cmd.ROOM_ANNOUNCE_PUSH)
public class RoomAnnouncePushMsg extends MarsServerMsg {
    private String announce;
    private UserInfoObject opt_user;

    @Override
    public void fillFrom(JSONObject object){
        this.announce = object.getString("announce");
        JSONObject userObj = object.getJSONObject("opt_user");
        if(userObj != null){
            opt_user = new UserInfoObject();
            opt_user.fillFrom(userObj);
        }
    }

    @Override
    protected void doFromBody(byte[] bytes) throws Exception {
        WahoProtoRoom.AnnounceMessage msg = WahoProtoRoom.AnnounceMessage.parseFrom(bytes);
        this.protoHeader.doFromBody(msg.getHeader());
        this.announce = msg.getAnnounce();
        if(msg.getOptUser() != null){
            this.opt_user = new UserInfoObject();
            this.opt_user.doFromBody(msg.getOptUser());
        }
    }

    @Override
    protected byte[] doToBody() throws Exception {
        WahoProtoRoom.AnnounceMessage.Builder builder = WahoProtoRoom.AnnounceMessage.newBuilder();
        builder.setHeader(this.protoHeader.doToBody());
        builder.setAnnounce(this.announce == null ? "" : this.announce);
        if(this.opt_user != null){
            builder.setOptUser(this.opt_user.doToBody());
        }
        return builder.build().toByteArray();
    }
}
