package com.quhong.msg.room;

import com.quhong.core.annotation.Message;
import com.quhong.enums.Cmd;
import com.quhong.msg.MarsServerMsg;
import com.quhong.msg.obj.HighlightTextObject;
import com.quhong.proto.WahoProtoHighlightText;
import com.quhong.proto.WahoProtoRoom;

import java.util.ArrayList;
import java.util.List;

/**
 * actionType
 * 0:跳转 h5, 1: 统一格式，保留不使用[弹窗], 2: 跳转等级页面, 3: 商店Ride坐骑列表, 4: 商店mic列表, 5: 商店bubble列表, 6: 商店voice声波, 7: Unique ID,
 * 8: 女王  9: tycoons  10: userPage个人主页  11: 商店飘屏列表  12: 勋章列表  13: 我的坐骑列表 14: 我的麦位框列表  15: 我的气泡列表  16: 我的声波列表
 * 17: 我的飘屏列表 18: 点击游戏全屏 19: 点击游戏半屏 20: 充值页面  21: 商城主页  22: 商店入场通知列表  23: 我的入场通知列表 24: 跳转家族主页 25: 跳转日常任务
 * 99: 指定房间  998 不跳转
 */
@Message(cmd = Cmd.ROOM_COMMON_SCROLL_MSG)
public class RoomCommonScrollMsg extends MarsServerMsg {
    private String uid;
    private String prizeIcon;
    private String prizeTextEn;
    private String prizeTextAr;
    private List<HighlightTextObject> highlightTextEn;
    private List<HighlightTextObject> highlightTextAr;
    private int actionType;
    private String actionValue;

    @Override
    protected void doFromBody(byte[] bytes) throws Exception {
        WahoProtoRoom.RoomCommonScrollMessage msg = WahoProtoRoom.RoomCommonScrollMessage.parseFrom(bytes);
        this.protoHeader.doFromBody(msg.getHeader());
        this.uid = msg.getUid();
        this.prizeIcon = msg.getPrizeIcon();
        this.prizeTextEn = msg.getPrizeTextEn();
        this.prizeTextAr = msg.getPrizeTextAr();
        this.highlightTextEn = new ArrayList<>();
        for (WahoProtoHighlightText.HighlightText highlightText : msg.getHighlightTextEnList()) {
            HighlightTextObject highlightTextObject = new HighlightTextObject();
            highlightTextObject.doFromBody(highlightText);
            this.highlightTextEn.add(highlightTextObject);
        }
        this.highlightTextAr = new ArrayList<>();
        for (WahoProtoHighlightText.HighlightText highlightText : msg.getHighlightTextArList()) {
            HighlightTextObject highlightTextObject = new HighlightTextObject();
            highlightTextObject.doFromBody(highlightText);
            this.highlightTextAr.add(highlightTextObject);
        }
        this.actionType = msg.getActionType();
        this.actionValue = msg.getActionValue();
    }

    @Override
    protected byte[] doToBody() throws Exception {
        WahoProtoRoom.RoomCommonScrollMessage.Builder builder = WahoProtoRoom.RoomCommonScrollMessage.newBuilder();
        builder.setHeader(this.protoHeader.doToBody());
        builder.setPrizeIcon(this.prizeIcon == null ? "" : this.prizeIcon);
        builder.setPrizeTextEn(this.prizeTextEn == null ? "" : this.prizeTextEn);
        builder.setPrizeTextAr(this.prizeTextAr == null ? "" : this.prizeTextAr);
        if(this.highlightTextEn != null){
            for (HighlightTextObject highlightTextObject : highlightTextEn) {
                builder.addHighlightTextEn(highlightTextObject.doToBody());
            }
        }
        if(this.highlightTextAr != null){
            for (HighlightTextObject highlightTextObject : highlightTextAr) {
                builder.addHighlightTextAr(highlightTextObject.doToBody());
            }
        }
        builder.setActionType(actionType);
        builder.setActionValue(this.actionValue == null ? "" : this.actionValue);
        return builder.build().toByteArray();
    }

    @Override
    public String getUid() {
        return uid;
    }

    public void setUid(String uid) {
        this.uid = uid;
    }

    public String getPrizeIcon() {
        return prizeIcon;
    }

    public void setPrizeIcon(String prizeIcon) {
        this.prizeIcon = prizeIcon;
    }

    public String getPrizeTextEn() {
        return prizeTextEn;
    }

    public void setPrizeTextEn(String prizeTextEn) {
        this.prizeTextEn = prizeTextEn;
    }

    public String getPrizeTextAr() {
        return prizeTextAr;
    }

    public void setPrizeTextAr(String prizeTextAr) {
        this.prizeTextAr = prizeTextAr;
    }

    public List<HighlightTextObject> getHighlightTextEn() {
        return highlightTextEn;
    }

    public void setHighlightTextEn(List<HighlightTextObject> highlightTextEn) {
        this.highlightTextEn = highlightTextEn;
    }

    public List<HighlightTextObject> getHighlightTextAr() {
        return highlightTextAr;
    }

    public void setHighlightTextAr(List<HighlightTextObject> highlightTextAr) {
        this.highlightTextAr = highlightTextAr;
    }

    public int getActionType() {
        return actionType;
    }

    public void setActionType(int actionType) {
        this.actionType = actionType;
    }

    public String getActionValue() {
        return actionValue;
    }

    public void setActionValue(String actionValue) {
        this.actionValue = actionValue;
    }
}
