package com.quhong.msg.room;

import com.quhong.core.annotation.Message;
import com.quhong.enums.Cmd;
import com.quhong.msg.MarsServerMsg;
import com.quhong.proto.WahoProtoRoom;


@Message(cmd = Cmd.MIC_APPLY_CHANGE_MSG)
public class MicApplyChangeMsg extends MarsServerMsg {

    private int applyCount; // 申请数量
    private String firstHead; // 最新一个申请用户头像，房主、副房主和管理员显示

    @Override
    protected void doFromBody(byte[] bytes) throws Exception {
        WahoProtoRoom.MicApplyChangeMessage msg = WahoProtoRoom.MicApplyChangeMessage.parseFrom(bytes);
        this.protoHeader.doFromBody(msg.getHeader());
        this.applyCount = msg.getApplyCount();
        this.firstHead = msg.getFirstHead();
    }

    @Override
    protected byte[] doToBody() throws Exception {
        WahoProtoRoom.MicApplyChangeMessage.Builder builder = WahoProtoRoom.MicApplyChangeMessage.newBuilder();
        builder.setHeader(this.protoHeader.doToBody());
        builder.setApplyCount(this.applyCount);
        builder.setFirstHead(this.firstHead == null ? "" : this.firstHead);
        return builder.build().toByteArray();
    }

    public int getApplyCount() {
        return applyCount;
    }

    public void setApplyCount(int applyCount) {
        this.applyCount = applyCount;
    }

    public String getFirstHead() {
        return firstHead;
    }

    public void setFirstHead(String firstHead) {
        this.firstHead = firstHead;
    }
}
