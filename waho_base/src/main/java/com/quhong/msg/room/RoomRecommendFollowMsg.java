package com.quhong.msg.room;

import com.quhong.core.annotation.Message;
import com.quhong.enums.Cmd;
import com.quhong.msg.MarsServerMsg;
import com.quhong.proto.WahoProtoRoom;

@Message(cmd = Cmd.ROOM_RECOMMEND_FOLLOW_MSG)
public class RoomRecommendFollowMsg extends MarsServerMsg {
    private String head; //用户头像
    private String aid; //用户id
    private String name; //用户名
    private String msg; //关注消息英语
    private String msg_ar; //关注消息阿语


    @Override
    protected void doFromBody(byte[] bytes) throws Exception {
        WahoProtoRoom.RoomRecommendFollowMessage msg = WahoProtoRoom.RoomRecommendFollowMessage.parseFrom(bytes);
        this.protoHeader.doFromBody(msg.getHeader());
        this.head = msg.getHead();
        this.aid = msg.getAid();
        this.name = msg.getName();
        this.msg = msg.getMsg();
        this.msg_ar = msg.getMsgAr();
    }

    @Override
    protected byte[] doToBody() throws Exception {
        WahoProtoRoom.RoomRecommendFollowMessage.Builder builder = WahoProtoRoom.RoomRecommendFollowMessage.newBuilder();
        builder.setHeader(this.protoHeader.doToBody());
        builder.setHead(head == null ? "" : head);
        builder.setAid(aid == null ? "" : aid);
        builder.setName(name == null ? "" : name);
        builder.setMsg(msg == null ? "" : msg);
        builder.setMsgAr(msg_ar == null ? "" : msg_ar);
        return builder.build().toByteArray();
    }

    public String getHead() {
        return head;
    }

    public void setHead(String head) {
        this.head = head;
    }

    public String getAid() {
        return aid;
    }

    public void setAid(String aid) {
        this.aid = aid;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getMsg() {
        return msg;
    }

    public void setMsg(String msg) {
        this.msg = msg;
    }

    public String getMsg_ar() {
        return msg_ar;
    }

    public void setMsg_ar(String msg_ar) {
        this.msg_ar = msg_ar;
    }
}
