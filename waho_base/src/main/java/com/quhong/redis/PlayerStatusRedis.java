package com.quhong.redis;

import jakarta.annotation.Resource;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * @date 2020/9/11
 */
@Component
public class PlayerStatusRedis {

    @Resource
    private UserOnlineRedis userOnlineRedis;


    /**
     * 获取用户在线状态
     *
     * @return 大于0为在线
     */
    public int getPlayerStatus(String uid) {
        return userOnlineRedis.isOnline(uid) ? 1 : 0;
    }
}
