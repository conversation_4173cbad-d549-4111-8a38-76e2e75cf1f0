package com.quhong.data.dto;

import com.quhong.core.utils.DateHelper;

import java.util.UUID;

public class MoneyDetailReq {

    // 记录流水号
    private String id;
    // 用户uid
    private String uid;
    // 记录钻石
    private Integer changed;
    // 记录标题
    private String title;
    // 记录描述
    private String desc;
    // 记录类型
    private Integer atype;
    // 更新时间
    private Integer mtime;

    // 非数据库字段
    private boolean credit; // 是否赊账(账户扣成负数)
    private String activityId; // 活动id（活动下发钻石奖励要传）
    // 埋点用
    private String roomId;
    private String fromUid;

    public MoneyDetailReq() {
        this.setMtime(DateHelper.getNowSeconds());
    }

    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public void setRandomId() {
        this.id = UUID.randomUUID().toString().replace("-", "");
    }

    public String getUid() {
        return uid;
    }

    public void setUid(String uid) {
        this.uid = uid;
    }

    public Integer getChanged() {
        return changed;
    }

    public void setChanged(Integer changed) {
        this.changed = changed;
    }

    public String getTitle() {
        return title;
    }

    public void setTitle(String title) {
        this.title = title;
    }

    public String getDesc() {
        return desc;
    }

    public void setDesc(String desc) {
        this.desc = desc;
    }

    public Integer getAtype() {
        return atype;
    }

    public void setAtype(Integer atype) {
        this.atype = atype;
    }

    public Integer getMtime() {
        return mtime;
    }

    public void setMtime(Integer mtime) {
        this.mtime = mtime;
    }

    public boolean isCredit() {
        return credit;
    }

    public void setCredit(boolean credit) {
        this.credit = credit;
    }

    public String getRoomId() {
        return roomId;
    }

    public void setRoomId(String roomId) {
        this.roomId = roomId;
    }

    public String getFromUid() {
        return fromUid;
    }

    public void setFromUid(String fromUid) {
        this.fromUid = fromUid;
    }

    public String getActivityId() {
        return activityId;
    }

    public void setActivityId(String activityId) {
        this.activityId = activityId;
    }
}
