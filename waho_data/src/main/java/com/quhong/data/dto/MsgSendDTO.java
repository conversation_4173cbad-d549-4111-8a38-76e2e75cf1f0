package com.quhong.data.dto;

import com.alibaba.fastjson.JSONObject;
import com.quhong.handler.HttpEnvData;

import java.util.Set;

public class MsgSendDTO extends HttpEnvData {
    public String aid;
    public int msg_type;
    public String msg_body;
    public JSONObject msg_info;
    // 以下三个字段必须三选一，优先级：aidSet->allFriend->allFriendExSet
    public Set<String> aidSet; // 发送的目标用户
    public Set<String> allFriendExSet; // 发送给除此集合外的所有好友
    public int allFriend; // 1发送给所有好友
    public String quoteMsgId; // 引用的消息id
    private String msgId; // 消息id
    private boolean payMsg; // 付费消息

    // 内部字段
    public boolean batchSend = false;
    private Integer sendStrangerRemain; // 本月还可以给x个陌生人发消息
    private Integer strangerMsgRemain; // 本月还可以这个陌生人发x条消息

    public void copyFrom(SendChatMsgDTO dto) {
        this.uid = dto.getUid();
        this.aid = dto.getAid();
        this.msg_type = dto.getMsgType() == null ? 0 : dto.getMsgType();
        this.msg_body = dto.getMsgBody();
        this.msg_info = dto.getMsgInfo();
    }

    public String getAid() {
        return aid;
    }

    public void setAid(String aid) {
        this.aid = aid;
    }

    public int getMsg_type() {
        return msg_type;
    }

    public void setMsg_type(int msg_type) {
        this.msg_type = msg_type;
    }

    public String getMsg_body() {
        return msg_body;
    }

    public void setMsg_body(String msg_body) {
        this.msg_body = msg_body;
    }

    public JSONObject getMsg_info() {
        return msg_info;
    }

    public void setMsg_info(JSONObject msg_info) {
        this.msg_info = msg_info;
    }

    public Set<String> getAidSet() {
        return aidSet;
    }

    public void setAidSet(Set<String> aidSet) {
        this.aidSet = aidSet;
    }

    public Set<String> getAllFriendExSet() {
        return allFriendExSet;
    }

    public void setAllFriendExSet(Set<String> allFriendExSet) {
        this.allFriendExSet = allFriendExSet;
    }

    public int getAllFriend() {
        return allFriend;
    }

    public void setAllFriend(int allFriend) {
        this.allFriend = allFriend;
    }

    public String getQuoteMsgId() {
        return quoteMsgId;
    }

    public void setQuoteMsgId(String quoteMsgId) {
        this.quoteMsgId = quoteMsgId;
    }

    public boolean isBatchSend() {
        return batchSend;
    }

    public void setBatchSend(boolean batchSend) {
        this.batchSend = batchSend;
    }

    public Integer getSendStrangerRemain() {
        return sendStrangerRemain;
    }

    public void setSendStrangerRemain(Integer sendStrangerRemain) {
        this.sendStrangerRemain = sendStrangerRemain;
    }

    public Integer getStrangerMsgRemain() {
        return strangerMsgRemain;
    }

    public void setStrangerMsgRemain(Integer strangerMsgRemain) {
        this.strangerMsgRemain = strangerMsgRemain;
    }

    public String getMsgId() {
        return msgId;
    }

    public void setMsgId(String msgId) {
        this.msgId = msgId;
    }

    public boolean isPayMsg() {
        return payMsg;
    }

    public void setPayMsg(boolean payMsg) {
        this.payMsg = payMsg;
    }
}