package com.quhong.data;

import java.util.Collections;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2024/5/6
 */
public class PointControlConfigData {

    /**
     * 普通用户亏损点控值触发比例 （百分比）
     */
    private int propLower;

    /**
     * 普通用户亏损点控值触发比例 （百分比）
     */
    private int propUpper;

    /**
     * 白名单用户亏损点控值触发比例 （百分比）
     */
    private int whitelistPropLower;

    /**
     * 白名单用户亏损点控值触发比例 （百分比）
     */
    private int whitelistPropUpper;

    /**
     * 黑名单用户亏损点控值触发比例 （百分比）
     */
    private int blacklistPropLower;

    /**
     * 黑名单用户亏损点控值触发比例 （百分比）
     */
    private int blacklistPropUpper;

    /**
     * 针对新用户的配置
     */
    @Deprecated
    private Map<String, Integer> newUserConfigMap;

    /**
     * 针对新用户的配置，第0个为刷新周期，1~7为第n次
     */
    private Map<String, List<Integer>> countryControlMap = Collections.emptyMap();

    /**
     * 长期配置
     */
    private Map<String, MonthlyConfig> monthlyControlMap = Collections.emptyMap();


    public static class MonthlyConfig {
        private int frequency; // 30天频率
        private int addRangeStart; // 加值范围
        private int addRangeEnd; // 加值范围
        private int losses2; // 连续2天为负
        private int losses3; // 连续3天为负
        private int losses4; // 连续4天为负
        private int losses5; // 连续5天为负
        private int losses6; // 连续6天为负
        private int losses7; // 连续7天为负

        public int getFrequency() {
            return frequency;
        }

        public void setFrequency(int frequency) {
            this.frequency = frequency;
        }

        public int getAddRangeStart() {
            return addRangeStart;
        }

        public void setAddRangeStart(int addRangeStart) {
            this.addRangeStart = addRangeStart;
        }

        public int getAddRangeEnd() {
            return addRangeEnd;
        }

        public void setAddRangeEnd(int addRangeEnd) {
            this.addRangeEnd = addRangeEnd;
        }

        public int getLosses2() {
            return losses2;
        }

        public void setLosses2(int losses2) {
            this.losses2 = losses2;
        }

        public int getLosses3() {
            return losses3;
        }

        public void setLosses3(int losses3) {
            this.losses3 = losses3;
        }

        public int getLosses4() {
            return losses4;
        }

        public void setLosses4(int losses4) {
            this.losses4 = losses4;
        }

        public int getLosses5() {
            return losses5;
        }

        public void setLosses5(int losses5) {
            this.losses5 = losses5;
        }

        public int getLosses6() {
            return losses6;
        }

        public void setLosses6(int losses6) {
            this.losses6 = losses6;
        }

        public int getLosses7() {
            return losses7;
        }

        public void setLosses7(int losses7) {
            this.losses7 = losses7;
        }
    }

    public int getPropLower() {
        return propLower;
    }

    public void setPropLower(int propLower) {
        this.propLower = propLower;
    }

    public int getPropUpper() {
        return propUpper;
    }

    public void setPropUpper(int propUpper) {
        this.propUpper = propUpper;
    }

    public int getWhitelistPropLower() {
        return whitelistPropLower;
    }

    public void setWhitelistPropLower(int whitelistPropLower) {
        this.whitelistPropLower = whitelistPropLower;
    }

    public int getWhitelistPropUpper() {
        return whitelistPropUpper;
    }

    public void setWhitelistPropUpper(int whitelistPropUpper) {
        this.whitelistPropUpper = whitelistPropUpper;
    }

    public int getBlacklistPropLower() {
        return blacklistPropLower;
    }

    public void setBlacklistPropLower(int blacklistPropLower) {
        this.blacklistPropLower = blacklistPropLower;
    }

    public int getBlacklistPropUpper() {
        return blacklistPropUpper;
    }

    public void setBlacklistPropUpper(int blacklistPropUpper) {
        this.blacklistPropUpper = blacklistPropUpper;
    }

    public Map<String, Integer> getNewUserConfigMap() {
        return newUserConfigMap;
    }

    public void setNewUserConfigMap(Map<String, Integer> newUserConfigMap) {
        this.newUserConfigMap = newUserConfigMap;
    }

    public Map<String, List<Integer>> getCountryControlMap() {
        return countryControlMap;
    }

    public void setCountryControlMap(Map<String, List<Integer>> countryControlMap) {
        this.countryControlMap = countryControlMap;
    }

    public Map<String, MonthlyConfig> getMonthlyControlMap() {
        return monthlyControlMap;
    }

    public void setMonthlyControlMap(Map<String, MonthlyConfig> monthlyControlMap) {
        this.monthlyControlMap = monthlyControlMap;
    }
}
