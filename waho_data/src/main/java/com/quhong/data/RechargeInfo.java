package com.quhong.data;

/**
 * <AUTHOR>
 * @date 2022/11/14
 */
public class RechargeInfo {

    /**
     * uid
     */
    private String uid;

    /**
     * 充值订单id
     */
    private String orderId;

    /**
     * 充值金额
     */
    private Double rechargeMoney;

    /**
     * 充值钻石数
     */
    private Integer rechargeDiamond;

    /**
     * 充值时间
     */
    private Integer rechargeTime;

    /**
     * 充值类型
     */
    private Integer rechargeType;

    /**
     * 充值子分类 [中台支付]
     */
    private String subType;

    /**
     *  充值渠道 "google", "apple", "admin",  "MGP",
     *  "payCenter": 中台支付
     */
    private String rechargeItem;

    /**
     * 使用的中台项目
     */
    private String project;

    public RechargeInfo() {
    }

    public RechargeInfo(String uid, String orderId) {
        this.uid = uid;
        this.orderId = orderId;
    }

    public RechargeInfo(String uid, String orderId, Double rechargeMoney, Integer rechargeDiamond, Integer rechargeTime) {
        this.uid = uid;
        this.orderId = orderId;
        this.rechargeMoney = rechargeMoney;
        this.rechargeDiamond = rechargeDiamond;
        this.rechargeTime = rechargeTime;
    }

    public String getUid() {
        return uid;
    }

    public void setUid(String uid) {
        this.uid = uid;
    }

    public String getOrderId() {
        return orderId;
    }

    public void setOrderId(String orderId) {
        this.orderId = orderId;
    }

    public Double getRechargeMoney() {
        return rechargeMoney;
    }

    public void setRechargeMoney(Double rechargeMoney) {
        this.rechargeMoney = rechargeMoney;
    }

    public Integer getRechargeDiamond() {
        return rechargeDiamond;
    }

    public void setRechargeDiamond(Integer rechargeDiamond) {
        this.rechargeDiamond = rechargeDiamond;
    }

    public Integer getRechargeTime() {
        return rechargeTime;
    }

    public void setRechargeTime(Integer rechargeTime) {
        this.rechargeTime = rechargeTime;
    }

    public Integer getRechargeType() {
        return rechargeType;
    }

    public void setRechargeType(Integer rechargeType) {
        this.rechargeType = rechargeType;
    }

    public String getSubType() {
        return subType;
    }

    public void setSubType(String subType) {
        this.subType = subType;
    }

    public String getRechargeItem() {
        return rechargeItem;
    }

    public void setRechargeItem(String rechargeItem) {
        this.rechargeItem = rechargeItem;
    }

    public String getProject() {
        return project;
    }

    public void setProject(String project) {
        this.project = project;
    }

    @Override
    public String toString() {
        return "RechargeInfo{" +
                "uid='" + uid + '\'' +
                ", orderId='" + orderId + '\'' +
                ", rechargeMoney=" + rechargeMoney +
                ", rechargeDiamond=" + rechargeDiamond +
                ", rechargeTime=" + rechargeTime +
                ", rechargeType=" + rechargeType +
                ", subType='" + subType + '\'' +
                ", rechargeItem='" + rechargeItem + '\'' +
                ", project='" + project + '\'' +
                '}';
    }
}
