package com.quhong.enums;

public class NoviceTaskIdConstant {

    public static final int TASK_ENTER_ROOM = 1; // 进入任意房间
    public static final int TASK_SEND_MSG = 2; // 房间内发送一条消息
    public static final int TASK_SEND_GIFT = 3; // 发送任意礼物
    public static final int TASK_UP_MIC = 4; // 任意房上麦

    public static final int TASK_CRUSH_SLIDE = 5; // 匹配卡片滑动一次
    public static final int TASK_COMPACT_PAGE = 6; //晚上个人资料页
    public static final int TASK_CRUSH_SLIDE_LIKE = 7; // 匹配卡片右滑三次，右滑代表喜欢
    public static final int TASK_CRUSH_OK = 8; // 完成一次匹配

}
