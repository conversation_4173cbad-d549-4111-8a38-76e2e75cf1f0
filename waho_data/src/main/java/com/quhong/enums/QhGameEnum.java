package com.quhong.enums;

import java.util.Arrays;
import java.util.List;

public enum QhGameEnum {

    FRUIT_PARTY(10000, "Fruit Party", "حفلة فواكه", "https://cloudcdn.waho.live/common/op_1690355647_icon_en.png"),
    NEW_GREEDY(10001, "Greedy", "جشعة", "https://cloudcdn.waho.live/common/op_1722932731_room-banner(1).png"),
    ANIMAL_PARTY(10002, "Animal Party", "حفلة الحيوانات", "https://cloudcdn.waho.live/common/op_1721790181_op_1718175095_icon.png"),
    BIG_BATTLE(10003, "Big Battle", "المعركة الكبيرة", "https://cloudcdn.waho.live/common/op_1722501092_1.png"),
    SLOT(10004, "Slots", "سلوتس", "https://cloudcdn.waho.live/common/op_1721790037_logo.png"),
    NEW_CRASH(10006, "Crash", "كراش", "https://cloudcdn.waho.live/common/op_1724655143_op_1723630281_logo.png"),
    ALADDIN_SLOT(10007, "Aladin", "علاء الدين", "https://cloudcdn.waho.live/common/op_1726823673_icon.png"),
    ROULETTE(10010, "Roulette", "الروليت", "https://cloudcdn.waho.live/common/op_1729663160_icon.png"),
    PENALTY_SHOOTOUT(10012, "Penalty Shootout", "ركلات الترجيح", "https://cloudcdn.waho.live/common/op_1733127909_icon.png"),
    LUCKY_FISH(10015, "Lucky Fish", "سمكة محظوظة", "https://cloudcdn.waho.live/resource/op_sys_1748930511_lucky_fish.png"),
    FOOTBALL_LEAGUE(10016, "Football League", "دوري كرة القدم", "https://cloudcdn.waho.live/resource/op_sys_1748930511_football_league.png")
    ;

    private int gameId; // 游戏id
    private String gameName; // 游戏名
    private String gameNameAr; // 游戏名阿语
    private String gameIcon; // 游戏图标

    QhGameEnum(int gameId, String gameName, String gameNameAr, String gameIcon) {
        this.gameId = gameId;
        this.gameName = gameName;
        this.gameNameAr = gameNameAr;
        this.gameIcon = gameIcon;
    }

    public static QhGameEnum getGameEnumByGameId(int gameId) {
        for (QhGameEnum gameEnum : QhGameEnum.values()) {
            if (gameEnum.getGameId() == gameId) {
                return gameEnum;
            }
        }
        return null;
    }

    public static List<QhGameEnum> getGameEnumList() {
        return Arrays.asList(QhGameEnum.values());
    }

    public int getGameId() {
        return gameId;
    }

    public String getGameName() {
        return gameName;
    }

    public String getGameNameAr() {
        return gameNameAr;
    }

    public String getGameIcon() {
        return gameIcon;
    }
}
