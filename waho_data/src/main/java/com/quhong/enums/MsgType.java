package com.quhong.enums;

public interface MsgType {
    int TEXT = 1; // 文本
    int AUDIO = 2; // 音频
    int IMAGE = 3; // 图像
    int VIDEO = 5; // 视频
    int GIFT = 7; //发送礼物
    int HEART_GIFT = 8; //心心礼物
    int REPOST_MOMENT = 20; // 朋友圈转发的消息
    int SHARE_ROOM = 21; // 分享房间
    int SEND_RESOURCES = 22; // 赠送道具资源
    int SHARE_ROOM_EVENT = 23; // 分享房间活动
    int SHARE_ACTIVITY = 24; // 分享H5活动
    int SHARE_FAMILY = 25; // 分享公会
    int AGENT_INVITATION = 26; // 代理邀请
    int SHARE_LIVE_ROOM = 27; // 分享直播房间
    int MOMENT_NOTICE = 28; // 朋友圈通知
    int DEDUCT_TIP_MSG = 30; // 扣费提示消息
    int AT_MSG = 222; // @消息

    String FCM_JUMP_MSG = "1"; // 跳转到该用户的聊天页面
    String FCM_JUMP_FRIEND_REQUEST = "2"; // 跳转到好友请求页面
    String FCM_JUMP_FOLLOW = "3"; // 跳转到被关注页面
    String FCM_JUMP_ROOM = "4"; // 进入该用户的房间
}
