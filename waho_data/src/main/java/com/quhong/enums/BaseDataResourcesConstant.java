package com.quhong.enums;

import java.util.Arrays;
import java.util.List;

public class BaseDataResourcesConstant {
    public static final int FOREVER_DAY = -1;

    public static final int ACTION_GET = 1;
    public static final int ACTION_GET_WEAR = 2;
    public static final int ACTION_WEAR = 3;
    public static final int ACTION_DELETE = 4;
    public static final int ACTION_ALL_EXPIRE = 5; // 到达过期时间，系统删除资源
    public static final int ACTION_EXPIRE_GET = 6; // 过期时，重新获得
    public static final int ACTION_CANCLE_WEAR = 7; // 取消佩戴
    public static final int ACTION_REDUCE = 8; // 使用次数资源，递减
    public static final int ACTION_ALL_EXPIRE_NOTE = 9; // 快过期通知
    public static final int ACTION_TEST = 99; // 测试调用

    // 资源类型
    public static final int TYPE_BADGE = 1; // 勋章
    public static final int TYPE_MIC = 2; // 麦位框
    public static final int TYPE_RIDE = 3; // 入场动画
    public static final int TYPE_BAG_GIFT = 4; // 背包礼物
    public static final int TYPE_ROOM_LOCK = 5; // 房间锁
    public static final int TYPE_BUDDLE = 6; // 气泡
    public static final int TYPE_RIPPLE = 7; // 声波
    public static final int TYPE_FLOAT_SCREEN = 8; // 浮屏
    public static final int TYPE_MINE_BACKGROUND = 9; // 个人背景资源
    public static final int TYPE_BEAUTIFUL_RID = 10; // 靓号资源
    public static final int TYPE_ENTRY_EFFECT = 11; // 进房通知
    public static final int TYPE_HONOR_TITLE = 12; // 荣誉称号
    public static final int TYPE_RECHARGE_COUPON = 13; // 充值优惠卷
    public static final int TYPE_TICKET = 14;      // 游戏抽奖卷
    public static final int TYPE_PROFILE_CARD_FRAME = 15; // 资料卡边框
    public static final int TYPE_BACKGROUND_UPLOAD_CARD = 50; // 房间背景上传卡
    public static final int TYPE_WEALTH_LEVEL = 101; // 财富等级(未实现)
    public static final int TYPE_VIP_LEVEL = 102; // vip等级


    // 资源获取方式
    public static final int TYPE_OTHERS_GET = 0; // 未知
    public static final int TYPE_ACTIVITY_GET = 1; // 运营活动获得，默认值
    public static final int TYPE_SIGIN_GET = 2; // 签到获得
    public static final int TYPE_ADMIN_GET = 3; // admin发放
    public static final int TYPE_GAME_GET = 4; // 游戏奖励
    public static final int TYPE_CHARGE_GET = 5; // 充值奖励
    public static final int TYPE_BEHAVIOR_GET = 6; // 行为奖励
    public static final int TYPE_BUY_GET = 7; // 购买获得
    public static final int TYPE_ACHIEVED_GET = 8; // 成就奖励
    public static final int TYPE_OTHER_BUY_GET = 9; // 他人购买赠与获得
    public static final int TYPE_OTHER_LOCK_GET = 10; // 他人解锁道具赠与获得
    public static final int TYPE_MY_LOCK_GET = 11; // 自己解锁道具获得
    public static final int TYPE_WELCOME_PACK_GET = 12; // 欢迎礼包获得
    public static final int TYPE_OPERATION_SET_GET = 13; //运营后台-资源下发

    public static final int DEFAULT_BUDDLE_ID = 1;
    public static final int DEFAULT_RIPPLE_ID = 1;
    public static final int DEFAULT_ROOM_LOCK_ID = 999;

    public static final int GAIN_TYPE_DAY = 0; // 按自然天过期资源
    public static final int GAIN_TYPE_USE = 1; // 按使用次数过期资源

    public static final int EMPTY_WEAR_AUTO = 1; // 没有任何佩戴则佩戴

    public static final int DELETE_ALL_NUM = -1; //  不指定天数或者数量,删除所有资源

    /**
     * 资源有新增需要通知的type,支持上新的type
     */
    public static final List<Integer> VALID_TYPE_LIST = Arrays.asList(BaseDataResourcesConstant.TYPE_MIC
            , BaseDataResourcesConstant.TYPE_RIDE, BaseDataResourcesConstant.TYPE_BUDDLE, BaseDataResourcesConstant.TYPE_RIPPLE
            , BaseDataResourcesConstant.TYPE_FLOAT_SCREEN, BaseDataResourcesConstant.TYPE_ENTRY_EFFECT, BaseDataResourcesConstant.TYPE_TICKET
            , BaseDataResourcesConstant.TYPE_PROFILE_CARD_FRAME);

    /**
     * 资源有新增需要OfficialMsg
     */
    public static final List<Integer> OFFICIAL_MSG_TYPE_LIST = Arrays.asList(
            BaseDataResourcesConstant.TYPE_BADGE,
            BaseDataResourcesConstant.TYPE_MIC,
            BaseDataResourcesConstant.TYPE_RIDE,
            BaseDataResourcesConstant.TYPE_BUDDLE,
            BaseDataResourcesConstant.TYPE_RIPPLE,
            BaseDataResourcesConstant.TYPE_FLOAT_SCREEN,
            BaseDataResourcesConstant.TYPE_TICKET);
    /**
     * 新版本增加，资源商店购买类型
     */
    public static final int STORE_BUY_ITEM_TYPE = 5;

    /**
     * 正常资源，不处理加锁逻辑
     */
    public static final int NONE_LOCK_TYPE = 0;

    /**
     * 道具资源加锁
     */
    public static final int ADD_LOCK_TYPE = 1;

    /**
     * 2 解锁道具资源，自己使用
     */
    public static final int MY_UNLOCK_TYPE = 2;

    /**
     * 2 解锁道具资源，他人使用
     */
    public static final int OTHER_UNLOCK_TYPE = 3;

    /**
     * 道具资源加锁资源撤回
     */
    public static final int REMOVE_LOCK_TYPE = 4;

    /**
     * 资源有新增需要OfficialMsg
     */
    public static final List<Integer> LOCK_GOODS_TYPE_LIST = Arrays.asList(
            BaseDataResourcesConstant.TYPE_MIC,
            BaseDataResourcesConstant.TYPE_RIDE,
            BaseDataResourcesConstant.TYPE_BUDDLE,
            BaseDataResourcesConstant.TYPE_RIPPLE,
            BaseDataResourcesConstant.TYPE_FLOAT_SCREEN,
            BaseDataResourcesConstant.TYPE_ENTRY_EFFECT);
}
