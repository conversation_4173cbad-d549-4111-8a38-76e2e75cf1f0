package com.quhong.enums;

public enum RewardTypeEnum {
    REWARD_TYPE_HEART(1, "heart"), // 心心
    REWARD_TYPE_GIFT(2, "gift"), // 礼物
    REWARD_TYPE_BUDDLE(3, "buddle"), // buddle
    REWARD_TYPE_DIAMOND(4, "diamond"), // 钻石
    REWARD_TYPE_MIC(5, "mic") // 上麦
    ;


    private int code;

    private String msg;

    RewardTypeEnum(int code, String msg) {
        this.code = code;
        this.msg = msg;
    }

    public int getCode() {
        return code;
    }

    public String getMsg() {
        return msg;
    }
}
