package com.quhong.mysql.mapper.waho_log;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.quhong.mysql.data.CommissionStatData;
import org.apache.ibatis.annotations.Insert;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.util.List;

public interface CommissionStatMapper extends BaseMapper<CommissionStatData> {

    @Select({
            "<script>",
            "SELECT IFNULL(sum(commission), 0) FROM t_commission_stat WHERE <![CDATA[ stat_date >= #{startDate} AND stat_date <= #{endDate} ]]>",
            "<if test='uid != null and uid.length > 0'> AND uid = #{uid} </if>" ,
            "<if test='aid != null and aid.length > 0'> AND aid = #{aid} </if>" ,
            "<if test='logType != null and logType != 0'> AND log_type = #{logType} </if>" ,
            "</script>"
    })
    int getCommissionStat(@Param("uid") String uid, @Param("aid") String aid, @Param("logType") int logType, @Param("startDate") int startDate, @Param("endDate") int endDate);

    @Insert({
            "<script>",
            "insert into t_commission_stat (uid,aid,log_type,stat_date,commission) values ",
            "<foreach collection='itemList' item='item' index='index' separator=','>",
            "(#{item.uid},#{item.aid},#{item.logType},#{item.statDate},#{item.commission})",
            "</foreach>",
            "</script>"
    })
    void batchInsert(@Param("itemList") List<CommissionStatData> itemList);
}
