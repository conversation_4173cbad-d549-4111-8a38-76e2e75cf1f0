package com.quhong.mysql.mapper.waho;

import com.quhong.mysql.data.RoomMicData;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Set;

@Repository
public interface RoomMicInfoMapper {
    RoomMicData getData(@Param("roomId") String roomId, @Param("position") int position);

    void update(@Param("item") RoomMicData data);

    void insert(@Param("item") RoomMicData data);

    List<String> getUidDataList(@Param("roomId") String roomId);

    List<RoomMicData> getDataList(@Param("roomId") String roomId);

    List<RoomMicData> getRoomMicDataList(@Param("roomId") String roomId);

    List<RoomMicData> getRecommendDataList(@Param("uid") String uid, @Param("gender") int gender, @Param("country") String country);

    List<RoomMicData> getRecommendUserDataList(@Param("gender") int gender);

    List<String> getSameHobbyUidList(@Param("uid") String uid, @Param("aidSet") Set<String> aidSet);

    List<String> getFcmRoomList();

    List<RoomMicData> getAllOnMicUserList();

    @Select("select room_id from t_room_mic_info where uid=#{uid}")
    List<String> getRoomMicUserList(@Param("uid") String uid);
}
