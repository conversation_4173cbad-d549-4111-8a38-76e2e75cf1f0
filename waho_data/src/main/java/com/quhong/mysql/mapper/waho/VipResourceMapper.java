package com.quhong.mysql.mapper.waho;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.quhong.mysql.data.VipResourceData;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.util.List;


public interface VipResourceMapper extends BaseMapper<VipResourceData> {

    @Select("select * from t_vip_resource WHERE uid=#{uid} order by mtime desc limit #{size} offset #{start}")
    List<VipResourceData> selectPageList(@Param("uid") String uid, @Param("start") int start, @Param("size") int size);

    @Select("select * from t_vip_resource WHERE uid=#{uid} and status in (1,2) order by mtime desc limit #{size} offset #{start}")
    List<VipResourceData> selectUnlockPage(@Param("uid") String uid, @Param("start") int start, @Param("size") int size);
}
