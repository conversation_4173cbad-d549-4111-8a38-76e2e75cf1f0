package com.quhong.mysql.slave_mapper.waho_log;

import com.quhong.data.MoneyDetailsData;
import com.quhong.mongo.data.GameStatData;
import com.quhong.mysql.data.MoneyDetail;
import com.quhong.mysql.mapper.ShardingMapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;
import org.springframework.stereotype.Repository;

import java.util.Collection;
import java.util.List;
import java.util.Set;

@Repository
public interface SlaveMoneyDetailMapper extends ShardingMapper {


    /**
     * 统计游戏消耗钻石数字
     * 水果机：920，砸蛋910，ludo：923(返还925)，umo：926(返还928)，创建猜拳：67，加入猜拳，68(返还69)，转盘游戏：40(包括返还)，幸运数字:210
     * LuckyChest: 947(返还948) LavaSlot: 949(返还950) Lottery: 951(返还952) Crash: 953(返还954) FishingStar: 955(返还956) Greedy: 957(返还958)
     */
    @Select({
            "<script>",
            "SELECT IFNULL(sum(changed),0) as count from ( ",
            "<foreach collection='suffixList' item='tableSuffix' separator='UNION ALL'>",
            "SELECT IFNULL(sum(changed),0) as changed from t_money_detail_${tableSuffix} ",
            "where uid in <foreach collection = 'uidSet' open = '(' item = 'item' separator = ',' close = ') '> #{item} </foreach>",
            "AND <![CDATA[ ctime >= #{start} AND ctime < #{end} ]]> ",
            "and atype in (920,910,923,925,926,928,67,68,40,210,947,949,951,953,955,957,959,961) ",
            "and title !='win turntable game' ",
            "and title !='take a percentage in turntable game' ",
            "</foreach>",
            " ) as stat ",
            "</script>"
    })
    long calculateGameCostBeans(@Param("suffixList") List<String> suffixList, @Param("uidSet") Collection<String> uidSet,
                                @Param("start") long start, @Param("end") long end);


    @Select({
            "<script>",
            "SELECT IFNULL(sum(changed),0) as sumChanged FROM ( ",
            "<foreach collection='suffixList' item='tableSuffix' separator='UNION ALL'>",
            "SELECT IFNULL(sum(changed),0) as changed FROM t_money_detail_${tableSuffix} ",
            "WHERE uid=#{uid} and atype in <foreach collection = 'typeList' open = '(' item = 'item' separator = ',' close = ') '> #{item} </foreach> ",
            "and <![CDATA[ ctime >= #{startTime} and ctime <= #{endTime}]]>",
            "</foreach>",
            ") as stat",
            "</script>"
    })
    long getTotalBeanByType(@Param("uid") String uid, @Param("typeList") List<Integer> typeList, @Param("startTime") int startTime, @Param("endTime") int endTime, @Param("suffixList") List<String> suffixList);

    @Select({
            "<script>",
            "SELECT IFNULL(sum(changed),0) as count from ( ",
            "<foreach collection='suffixList' item='tableSuffix' separator='UNION ALL'>",
            "SELECT IFNULL(sum(changed),0) as changed from t_money_detail_${tableSuffix} ",
            "where uid = #{uid} ",
            "AND atype = #{aType} ",
            "AND <![CDATA[ ctime >= #{startTime} AND ctime < #{endTime} ]]> ",
            "</foreach>",
            " ) as stat ",
            "</script>"
    })
    long calculateTotalBeanSum(@Param("suffixList") List<String> retList, @Param("uid") String uid, @Param("aType") int aType, @Param("startTime") int startTime, @Param("endTime") int endTime);

    @Select({
            "<script>",
            "SELECT atype, IFNULL(sum(changed),0) as changed FROM t_money_detail_${tableSuffix} ",
            "WHERE uid=#{uid} and atype in <foreach collection = 'typeList' open = '(' item = 'item' separator = ',' close = ') '> #{item} </foreach> ",
            "and <![CDATA[ ctime >= #{startTime} and ctime <= #{endTime}]]> group by atype",
            "</script>"
    })
    List<MoneyDetailsData> getUserGameActTypeSum(@Param("tableSuffix") String tableSuffix, @Param("uid") String uid, @Param("typeList") List<Integer> typeList, @Param("startTime") int startTime, @Param("endTime") int endTime);

    @Select({
            "<script>",
            "SELECT DISTINCT uid FROM t_money_detail_${tableSuffix} WHERE ",
            "atype in <foreach collection = 'typeList' open = '(' item = 'item' separator = ',' close = ') '> #{item} </foreach> ",
            "and <![CDATA[ mtime >= #{startTime} and mtime <= #{endTime}]]>",
            "</script>"
    })
    Set<String> getUidSet(@Param("tableSuffix") String tableSuffix, @Param("typeList") List<Integer> typeList, @Param("startTime") int startTime, @Param("endTime") int endTime);

    @Select({
            "<script>",
            "SELECT DISTINCT uid FROM t_money_detail_${tableSuffix} WHERE ",
            "atype = #{aType} ",
            // "and <![CDATA[ mtime >= #{startTime} and mtime <= #{endTime}]]>",
            "</script>"
    })
    Set<String> getUidSetByType(@Param("tableSuffix") String tableSuffix, @Param("aType") int aType, @Param("startTime") int startTime, @Param("endTime") int endTime);

    @Select({
            "<script>",
            "SELECT uid, atype as actType, title, IFNULL(sum(changed),0) as diamonds FROM t_money_detail_${tableSuffix} WHERE uid=#{uid} AND",
            "atype in <foreach collection = 'typeList' open = '(' item = 'item' separator = ',' close = ') '> #{item} </foreach> ",
            "AND <![CDATA[ ctime >= #{startTime} and ctime <= #{endTime}]]> GROUP BY atype, title",
            "</script>"
    })
    List<GameStatData> getUserGameStatData(@Param("tableSuffix") String tableSuffix, @Param("uid") String uid, @Param("typeList") List<Integer> typeList, @Param("startTime") int startTime, @Param("endTime") int endTime);

    @Select("select * from t_money_detail_${tableSuffix} order by mtime asc limit #{size} offset #{start}")
    List<MoneyDetail> selectList(@Param("tableSuffix") String tableSuffix, @Param("start") int start, @Param("size") int size);

    @Select("select * from t_money_detail_${tableSuffix} where mtime>=#{startTime} and mtime<#{endTime}")
    List<MoneyDetail> selectListForFix(@Param("tableSuffix") String tableSuffix, @Param("startTime") int startTime, @Param("endTime") int endTime);

    @Select("SELECT count(*) FROM t_money_detail_${tableSuffix} WHERE atype = #{aType} and mtime >= #{startTime} and mtime <= #{endTime} limit 1")
    int getRecordCount(@Param("tableSuffix") String tableSuffix, @Param("aType") int aType, @Param("startTime") int startTime, @Param("endTime") int endTime);

    @Select("SELECT DISTINCT uid FROM `t_money_detail_2024_10` WHERE atype = 966 AND mtime >= 1730304000")
    Set<String> getPlayGreedyUserSet();
}
