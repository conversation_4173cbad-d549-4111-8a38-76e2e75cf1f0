package com.quhong.mysql.interceptor;

import com.quhong.monitor.MonitorSender;
import org.apache.ibatis.cache.CacheKey;
import org.apache.ibatis.executor.Executor;
import org.apache.ibatis.mapping.BoundSql;
import org.apache.ibatis.mapping.MappedStatement;
import org.apache.ibatis.mapping.ParameterMapping;
import org.apache.ibatis.mapping.SqlCommandType;
import org.apache.ibatis.plugin.Interceptor;
import org.apache.ibatis.plugin.Intercepts;
import org.apache.ibatis.plugin.Invocation;
import org.apache.ibatis.plugin.Signature;
import org.apache.ibatis.reflection.MetaObject;
import org.apache.ibatis.session.Configuration;
import org.apache.ibatis.session.ResultHandler;
import org.apache.ibatis.session.RowBounds;
import org.apache.ibatis.type.TypeHandlerRegistry;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.text.DateFormat;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Locale;


@Intercepts({
        @Signature(type = Executor.class, method = "query", args = {MappedStatement.class, Object.class, RowBounds.class, ResultHandler.class}),
        @Signature(type = Executor.class, method = "query", args = {MappedStatement.class, Object.class, RowBounds.class, ResultHandler.class, CacheKey.class, BoundSql.class}),
})
public class MyBatisMonitorInterceptor implements Interceptor {
    private static final Logger logger = LoggerFactory.getLogger(MyBatisMonitorInterceptor.class);
    private MonitorSender monitorSender;
    private static final int minSize = 40000;
    private long minQueryTime = 5 * 1000L;

    public MyBatisMonitorInterceptor() {
    }

    public MyBatisMonitorInterceptor(MonitorSender monitorSender, boolean isOperation) {
        this.monitorSender = monitorSender;
        if (isOperation) {
            minQueryTime = 10 * 1000L;
        }
    }

    @Override
    public Object intercept(Invocation invocation) throws Throwable {
        long startTime = System.currentTimeMillis();
        Object result = invocation.proceed();
        long queryTime = System.currentTimeMillis() - startTime;
        if (result instanceof ArrayList<?> resultList) {
            boolean slaveMapper = false;
            if (invocation.getArgs().length > 0 && invocation.getArgs()[0] instanceof MappedStatement mappedStatement) {
                // 从库放宽告警条件
                slaveMapper = mappedStatement.getId().contains("slave_mapper");
            }
            if (resultList.size() > (slaveMapper ? minSize * 10 : minSize)) {
                logger.error("sql query result list size={}. queryTime={} sql={}", resultList.size(), queryTime, getAndGenSql(invocation));
                monitorSender.info("waho_java_exception", "MySQL查询结果集: " + resultList.size() + ", 查询时间: " + queryTime, getAndGenSql(invocation));
            }
            if (queryTime > (slaveMapper ? minQueryTime * 10 : minQueryTime)) {
                logger.error("sql query time gt 10s. queryTime={} sql={}", queryTime, getAndGenSql(invocation));
                monitorSender.info("waho_java_exception", "MySQL查询时间: " + queryTime + ", 结果集: " + resultList.size(), getAndGenSql(invocation));
            }
        }
        return result;
    }

    private String getSql(Invocation invocation) {
        Object target = invocation.getTarget();
        Object[] args = invocation.getArgs();
        String sql = null;
        if (target instanceof Executor) {
            boolean isUpdate = args.length == 2;
            MappedStatement ms = (MappedStatement) args[0];
            if (!isUpdate && ms.getSqlCommandType() == SqlCommandType.SELECT) {
                BoundSql boundSql;
                if (args.length == 4) {
                    boundSql = ms.getBoundSql(args[1]);
                } else {
                    boundSql = (BoundSql) args[5];
                }
                sql = boundSql.getSql();
            }
        }
        return sql;
    }

    private String getAndGenSql(Invocation invocation) {
        try {
            Object target = invocation.getTarget();
            Object[] args = invocation.getArgs();
            String sql = "";
            if (target instanceof Executor) {
                boolean isUpdate = args.length == 2;
                MappedStatement ms = (MappedStatement) args[0];
                if (!isUpdate && ms.getSqlCommandType() == SqlCommandType.SELECT) {
                    BoundSql boundSql;
                    if (args.length == 4) {
                        boundSql = ms.getBoundSql(args[1]);
                    } else {
                        boundSql = (BoundSql) args[5];
                    }
                    Configuration configuration = ms.getConfiguration();
                    sql = geneSql(configuration, boundSql);
                    sql = ms.getId() + " " + sql;
                }
            }
            return sql;
        } catch (Exception e) {
            monitorSender.info("waho_java_exception", "拦截器生成sql错误", "error" + e.getMessage());
        }
        return "error sql";
    }

    /**
     * 生成对应的带有值得sql语句，超过500个字符，取不完整字符
     */
    private String geneSql(Configuration configuration, BoundSql boundSql) {
        Object parameterObject = boundSql.getParameterObject(); // 获得参数对象，如{id:1,name:"user1",param1:1,param2:"user1"}
        List<ParameterMapping> parameterMappings = boundSql.getParameterMappings(); // 获得映射的对象参数
        String sql = boundSql.getSql().replaceAll("\\s+", " "); // 获得带问号的sql语句
        if (!parameterMappings.isEmpty() && parameterObject != null) { // 如果参数个数大于0且参数对象不为空，说明该sql语句是带有条件的
            TypeHandlerRegistry typeHandlerRegistry = configuration.getTypeHandlerRegistry();
            if (typeHandlerRegistry.hasTypeHandler(parameterObject.getClass())) { // 检查该参数是否是一个参数
                //getParameterValue用于返回是否带有单引号的字符串，如果是字符串则加上单引号
                sql = sql.replaceFirst("\\?", getParameterValue(parameterObject)); // 如果是一个参数则只替换一次，将问号直接替换成值
            } else {
                MetaObject metaObject = configuration.newMetaObject(parameterObject); // 将映射文件的参数和对应的值返回，比如：id，name以及对应的值。
                int limit = 10;
                int idx = 0;
                for (ParameterMapping parameterMapping : parameterMappings) { // 遍历参数，如:id,name等
                    idx++;
                    if (idx > limit) {
                        break;
                    }
                    String propertyName = parameterMapping.getProperty(); // 获得属性名，如id,name等字符串
                    if (metaObject.hasGetter(propertyName)) { // 检查该属性是否在metaObject中
                        Object obj = metaObject.getValue(propertyName); // 如果在metaObject中，那么直接获取对应的值
                        sql = sql.replaceFirst("\\?", getParameterValue(obj)); // 然后将问号?替换成对应的值。
                    } else if (boundSql.hasAdditionalParameter(propertyName)) {
                        Object obj = boundSql.getAdditionalParameter(propertyName);
                        sql = sql.replaceFirst("\\?", getParameterValue(obj));
                    }
                }
            }
        }
        if (sql.length() > 500) {
            sql = sql.substring(0, 500);
        }
        return sql;//最后将sql语句返回
    }

    /**
     * 如果是字符串对象则加上单引号返回，如果是日期则也需要转换成字符串形式，如果是其他则直接转换成字符串返回。
     */
    private String getParameterValue(Object obj) {
        String value;
        if (obj instanceof String) {
            value = "'" + obj + "'";
        } else if (obj instanceof Date) {
            DateFormat formatter = DateFormat.getDateTimeInstance(DateFormat.DEFAULT, DateFormat.DEFAULT, Locale.CHINA);
            value = "'" + formatter.format(obj) + "'";
        } else {
            if (obj != null) {
                value = obj.toString();
            } else {
                value = "";
            }

        }
        return value;
    }
}
