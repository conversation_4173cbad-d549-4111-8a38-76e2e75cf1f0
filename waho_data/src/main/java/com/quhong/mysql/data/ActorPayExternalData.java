package com.quhong.mysql.data;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;

import java.math.BigDecimal;

@TableName("actor_pay_external")
public class ActorPayExternalData {

    @TableId(type = IdType.AUTO)
    private Integer id;
    /**
     * 用户uid
     */
    private String uid;

    /**
     * 总充值金额
     */
    private BigDecimal rechargeMoney;

    /**
     * 用户类型标识  1: 小R   2: 中R  3:大R
     */
    private Integer userIdentify;

    /**
     * 最后一笔充值渠道 1: GP 支付、 2:Apple 支付 3: Tap 支付、 4:Admin 支付、 5:JollychicCharge、6：huawei_pay 7:OPayCharge  8: 中台支付
     */
    private Integer rechargeChannel;

    /**
     * 最后一笔充值时间
     */
    private Integer lastTime;

    /**
     * 创建时间
     */
    private Integer ctime;

    /**
     * 修改时间
     */
    private Integer mtime;

    public Integer getId() {
        return id;
    }

    public void setId(Integer id) {
        this.id = id;
    }

    public String getUid() {
        return uid;
    }

    public void setUid(String uid) {
        this.uid = uid;
    }

    public BigDecimal getRechargeMoney() {
        return rechargeMoney;
    }

    public void setRechargeMoney(BigDecimal rechargeMoney) {
        this.rechargeMoney = rechargeMoney;
    }

    public Integer getUserIdentify() {
        return userIdentify;
    }

    public void setUserIdentify(Integer userIdentify) {
        this.userIdentify = userIdentify;
    }

    public Integer getRechargeChannel() {
        return rechargeChannel;
    }

    public void setRechargeChannel(Integer rechargeChannel) {
        this.rechargeChannel = rechargeChannel;
    }

    public Integer getLastTime() {
        return lastTime;
    }

    public void setLastTime(Integer lastTime) {
        this.lastTime = lastTime;
    }

    public Integer getCtime() {
        return ctime;
    }

    public void setCtime(Integer ctime) {
        this.ctime = ctime;
    }

    public Integer getMtime() {
        return mtime;
    }

    public void setMtime(Integer mtime) {
        this.mtime = mtime;
    }
}
