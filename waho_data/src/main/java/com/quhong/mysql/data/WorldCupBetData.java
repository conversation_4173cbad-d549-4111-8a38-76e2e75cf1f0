package com.quhong.mysql.data;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;

@TableName("t_world_cup_bet")
public class WorldCupBetData {

    @TableId(type = IdType.AUTO)
    private Integer id;

    private String uid;
    private String matchType;
    private Integer teamKey;  // -1打平 或者 球队id
    private Integer betNum;
    private Integer winType;  // -1 未开奖  0 未中奖  1中奖
    private Integer winNum;
    private Integer mtime;
    private Integer ctime;

    public Integer getId() {
        return id;
    }

    public void setId(Integer id) {
        this.id = id;
    }

    public String getUid() {
        return uid;
    }

    public void setUid(String uid) {
        this.uid = uid;
    }

    public String getMatchType() {
        return matchType;
    }

    public void setMatchType(String matchType) {
        this.matchType = matchType;
    }

    public Integer getTeamKey() {
        return teamKey;
    }

    public void setTeamKey(Integer teamKey) {
        this.teamKey = teamKey;
    }

    public Integer getBetNum() {
        return betNum;
    }

    public void setBetNum(Integer betNum) {
        this.betNum = betNum;
    }

    public Integer getWinType() {
        return winType;
    }

    public void setWinType(Integer winType) {
        this.winType = winType;
    }

    public Integer getWinNum() {
        return winNum;
    }

    public void setWinNum(Integer winNum) {
        this.winNum = winNum;
    }

    public Integer getMtime() {
        return mtime;
    }

    public void setMtime(Integer mtime) {
        this.mtime = mtime;
    }

    public Integer getCtime() {
        return ctime;
    }

    public void setCtime(Integer ctime) {
        this.ctime = ctime;
    }
}
