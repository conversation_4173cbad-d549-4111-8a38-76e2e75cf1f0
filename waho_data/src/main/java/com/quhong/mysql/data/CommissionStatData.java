package com.quhong.mysql.data;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;

import java.math.BigDecimal;


/**
 * 佣金收益统计数据，按天统计
 */
@TableName("t_commission_stat")
public class CommissionStatData {

    @TableId(type = IdType.AUTO)
    private Integer id;
    private String uid; // 用户uid
    private String aid; // 收益来源用户
    private int logType; // 佣金类型 6主播佣金 7子代理佣金
    private int statDate; // 记录日期 yyyyMMdd
    private BigDecimal commission; // 佣金金额

    public CommissionStatData() {
    }

    public CommissionStatData(String uid, String aid, int logType, int statDate, BigDecimal commission) {
        this.uid = uid;
        this.aid = aid;
        this.logType = logType;
        this.statDate = statDate;
        this.commission = commission;
    }

    public Integer getId() {
        return id;
    }

    public void setId(Integer id) {
        this.id = id;
    }

    public String getUid() {
        return uid;
    }

    public void setUid(String uid) {
        this.uid = uid;
    }

    public String getAid() {
        return aid;
    }

    public void setAid(String aid) {
        this.aid = aid;
    }

    public int getLogType() {
        return logType;
    }

    public void setLogType(int logType) {
        this.logType = logType;
    }

    public int getStatDate() {
        return statDate;
    }

    public void setStatDate(int statDate) {
        this.statDate = statDate;
    }

    public BigDecimal getCommission() {
        return commission;
    }

    public void setCommission(BigDecimal commission) {
        this.commission = commission;
    }
}
