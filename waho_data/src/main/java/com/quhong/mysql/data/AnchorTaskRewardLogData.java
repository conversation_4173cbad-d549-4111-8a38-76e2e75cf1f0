package com.quhong.mysql.data;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;

/**
 * <AUTHOR>
 * @date 2024/4/15
 */
@TableName("t_anchor_task_reward_log")
public class AnchorTaskRewardLogData {

    @TableId(type = IdType.AUTO)
    private Integer id;

    /**
     * 用户uid
     */
    private String uid;

    /**
     * 类型： 0语聊上麦任务 1直播任务
     */
    private Integer type;

    /**
     * 日期
     */
    private String strDate;

    /**
     * 上麦时长
     */
    private Integer upMicTime;

    /**
     * 近7天总收入
     */
    private Integer totalIncome;

    /**
     * 奖励魅力值
     */
    private Integer rewardCharm;

    /**
     * 奖励目标
     */
    private Integer rewardTarget;

    /**
     * 奖励等级
     */
    private String rewardLevel;

    /**
     * 创建时间
     */
    private Integer ctime;

    public Integer getId() {
        return id;
    }

    public void setId(Integer id) {
        this.id = id;
    }

    public String getUid() {
        return uid;
    }

    public void setUid(String uid) {
        this.uid = uid;
    }

    public Integer getType() {
        return type;
    }

    public void setType(Integer type) {
        this.type = type;
    }

    public String getStrDate() {
        return strDate;
    }

    public void setStrDate(String strDate) {
        this.strDate = strDate;
    }

    public Integer getUpMicTime() {
        return upMicTime;
    }

    public void setUpMicTime(Integer upMicTime) {
        this.upMicTime = upMicTime;
    }

    public Integer getTotalIncome() {
        return totalIncome;
    }

    public void setTotalIncome(Integer totalIncome) {
        this.totalIncome = totalIncome;
    }

    public Integer getRewardTarget() {
        return rewardTarget;
    }

    public void setRewardTarget(Integer rewardTarget) {
        this.rewardTarget = rewardTarget;
    }

    public Integer getRewardCharm() {
        return rewardCharm;
    }

    public void setRewardCharm(Integer rewardCharm) {
        this.rewardCharm = rewardCharm;
    }

    public String getRewardLevel() {
        return rewardLevel;
    }

    public void setRewardLevel(String rewardLevel) {
        this.rewardLevel = rewardLevel;
    }

    public Integer getCtime() {
        return ctime;
    }

    public void setCtime(Integer ctime) {
        this.ctime = ctime;
    }
}

