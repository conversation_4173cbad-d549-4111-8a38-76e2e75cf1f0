package com.quhong.mysql.data;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;

@TableName("t_room_hot_card")
public class RoomHotCardData {

    @TableId(type = IdType.AUTO)
    private Integer id;
    /**
     * 用户uid
     */
    private String uid;
    /**
     * 数量
     */
    private Integer num;
    /**
     * 热度时间 (单位：分钟)
     */
    private Integer hotTime;
    /**
     * 有效期结束时间
     */
    private Integer endTime;
    /**
     * 生效的房间id
     */
    private String roomId;
    /**
     * 状态 0未使用 1已使用 2已过期 3已移除
     */
    private Integer status;
    /**
     * 使用时间
     */
    private Integer mtime;
    /**
     * 获得时间
     */
    private Integer ctime;

    public Integer getId() {
        return id;
    }

    public void setId(Integer id) {
        this.id = id;
    }

    public String getUid() {
        return uid;
    }

    public void setUid(String uid) {
        this.uid = uid;
    }

    public Integer getNum() {
        return num;
    }

    public void setNum(Integer num) {
        this.num = num;
    }

    public Integer getHotTime() {
        return hotTime;
    }

    public void setHotTime(Integer hotTime) {
        this.hotTime = hotTime;
    }

    public Integer getEndTime() {
        return endTime;
    }

    public void setEndTime(Integer endTime) {
        this.endTime = endTime;
    }

    public String getRoomId() {
        return roomId;
    }

    public void setRoomId(String roomId) {
        this.roomId = roomId;
    }

    public Integer getStatus() {
        return status;
    }

    public void setStatus(Integer status) {
        this.status = status;
    }

    public Integer getMtime() {
        return mtime;
    }

    public void setMtime(Integer mtime) {
        this.mtime = mtime;
    }

    public Integer getCtime() {
        return ctime;
    }

    public void setCtime(Integer ctime) {
        this.ctime = ctime;
    }
}
