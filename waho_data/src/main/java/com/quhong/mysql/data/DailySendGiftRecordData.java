package com.quhong.mysql.data;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;

/**
 * <AUTHOR>
 * @date 2024/9/27
 */
@TableName("t_daily_send_gift_record")
public class DailySendGiftRecordData {

    @TableId(type = IdType.AUTO)
    private Integer id;
    /**
     * 发送者
     */
    private String senderUid;
    /**
     * 接收者
     */
    private String receiverUid;
    /**
     * 日期 yyyyMMdd
     */
    private int intDate;

    public DailySendGiftRecordData() {
    }

    public DailySendGiftRecordData(String senderUid, String receiverUid, int intDate) {
        this.senderUid = senderUid;
        this.receiverUid = receiverUid;
        this.intDate = intDate;
    }

    public Integer getId() {
        return id;
    }

    public void setId(Integer id) {
        this.id = id;
    }

    public String getSenderUid() {
        return senderUid;
    }

    public void setSenderUid(String senderUid) {
        this.senderUid = senderUid;
    }

    public String getReceiverUid() {
        return receiverUid;
    }

    public void setReceiverUid(String receiverUid) {
        this.receiverUid = receiverUid;
    }

    public int getIntDate() {
        return intDate;
    }

    public void setIntDate(int intDate) {
        this.intDate = intDate;
    }
}
