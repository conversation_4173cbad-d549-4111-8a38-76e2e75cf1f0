package com.quhong.mysql.data;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;

@TableName("t_account_status")
public class AccountStatusData {
    @TableId(type = IdType.AUTO)
    private Integer id;
    private String uid;
    private Integer status; // 用户账号状态 1：已标记删除，30天内删除  2：账号已删除，无法登录
    private Integer applyTime; // 申请注销时间
    private Integer ctime;
    private Integer mtime;
    private int py_status ; // py清空账号数据 0:未清空  1:已清空

    public Integer getId() {
        return id;
    }

    public void setId(Integer id) {
        this.id = id;
    }

    public String getUid() {
        return uid;
    }

    public void setUid(String uid) {
        this.uid = uid;
    }

    public Integer getStatus() {
        return status;
    }

    public void setStatus(Integer status) {
        this.status = status;
    }

    public Integer getApplyTime() {
        return applyTime;
    }

    public void setApplyTime(Integer applyTime) {
        this.applyTime = applyTime;
    }

    public Integer getCtime() {
        return ctime;
    }

    public void setCtime(Integer ctime) {
        this.ctime = ctime;
    }

    public Integer getMtime() {
        return mtime;
    }

    public void setMtime(Integer mtime) {
        this.mtime = mtime;
    }

    public int getPy_status() {
        return py_status;
    }

    public void setPy_status(int py_status) {
        this.py_status = py_status;
    }
}
