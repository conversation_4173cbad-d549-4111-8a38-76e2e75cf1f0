package com.quhong.mysql.data;

public class MsgStatData {
    private int id;
    private int cmd; //协议号
    private long msgId; //消息id
    private String toUid; //接受者
    private String roomId; //房间号
    private int sendTestCount; //重发次数
    private int costTimeMillis; //发送消息耗时
    private int status; // 发送失败 1 发送成功
    private int ctime;
    private int os;
    private String suffix;

    public MsgStatData(){

    }

    public int getId() {
        return id;
    }

    public void setId(int id) {
        this.id = id;
    }

    public int getCmd() {
        return cmd;
    }

    public void setCmd(int cmd) {
        this.cmd = cmd;
    }

    public long getMsgId() {
        return msgId;
    }

    public void setMsgId(long msgId) {
        this.msgId = msgId;
    }

    public String getToUid() {
        return toUid;
    }

    public void setToUid(String toUid) {
        this.toUid = toUid;
    }

    public String getRoomId() {
        return roomId;
    }

    public void setRoomId(String roomId) {
        this.roomId = roomId;
    }

    public int getSendTestCount() {
        return sendTestCount;
    }

    public void setSendTestCount(int sendTestCount) {
        this.sendTestCount = sendTestCount;
    }

    public int getCostTimeMillis() {
        return costTimeMillis;
    }

    public void setCostTimeMillis(int costTimeMillis) {
        this.costTimeMillis = costTimeMillis;
    }

    public int getStatus() {
        return status;
    }

    public void setStatus(int status) {
        this.status = status;
    }

    public int getCtime() {
        return ctime;
    }

    public void setCtime(int ctime) {
        this.ctime = ctime;
    }

    public int getOs() {
        return os;
    }

    public void setOs(int os) {
        this.os = os;
    }

    public String getSuffix() {
        return suffix;
    }

    public void setSuffix(String suffix) {
        this.suffix = suffix;
    }
}
