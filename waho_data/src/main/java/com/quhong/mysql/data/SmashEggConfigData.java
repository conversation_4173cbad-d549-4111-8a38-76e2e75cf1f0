package com.quhong.mysql.data;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;

@TableName("t_smash_egg_config")
public class SmashEggConfigData {

    @TableId(type = IdType.AUTO)
    private Integer id;
    private String winType;         // 中奖key
    private Integer status;         // 状态
    private Integer showPage;       // 是否在页面展示
    private Integer joinPool;       // 是否加入奖池
    private String icon;            // 弹窗图标
    private String nameEn;          // 弹窗英语
    private String nameAr;          // 弹窗阿语
    private String pageAwardUrl;    // 页面图标
    private String pageAwardEn;     // 页面英语
    private String pageAwardAr;     // 页面阿语
    private Integer pageOrder;      // 页面奖排序
    private Integer floatingScreen; // 是否有飘屏通知
    private Integer screen;         // 是否有公屏消息
    private int msgBlockAnchor; // 消息屏蔽主播
    private Integer luckyGood;      // 是否幸运值奖品
    private Integer clearValue;     // 是否中奖清除幸运值
    private Integer roll;           // 是否跑马灯通知
    private String prize;           // 奖池大小
    private String rewardType;      // 奖品type
    private Integer sourceId;       // 资源id
    private Integer rewardTime;     // 资源时长
    private Integer rewardNum;      // 资源数量
    private Integer lockType;       // 资源下发方式
    private Integer ctime;          // 创建时间

    public Integer getId() {
        return id;
    }

    public void setId(Integer id) {
        this.id = id;
    }

    public String getWinType() {
        return winType;
    }

    public void setWinType(String winType) {
        this.winType = winType;
    }

    public Integer getStatus() {
        return status;
    }

    public void setStatus(Integer status) {
        this.status = status;
    }

    public Integer getShowPage() {
        return showPage;
    }

    public void setShowPage(Integer showPage) {
        this.showPage = showPage;
    }

    public Integer getJoinPool() {
        return joinPool;
    }

    public void setJoinPool(Integer joinPool) {
        this.joinPool = joinPool;
    }

    public String getIcon() {
        return icon;
    }

    public void setIcon(String icon) {
        this.icon = icon;
    }

    public String getNameEn() {
        return nameEn;
    }

    public void setNameEn(String nameEn) {
        this.nameEn = nameEn;
    }

    public String getNameAr() {
        return nameAr;
    }

    public void setNameAr(String nameAr) {
        this.nameAr = nameAr;
    }

    public String getPageAwardUrl() {
        return pageAwardUrl;
    }

    public void setPageAwardUrl(String pageAwardUrl) {
        this.pageAwardUrl = pageAwardUrl;
    }

    public String getPageAwardEn() {
        return pageAwardEn;
    }

    public void setPageAwardEn(String pageAwardEn) {
        this.pageAwardEn = pageAwardEn;
    }

    public String getPageAwardAr() {
        return pageAwardAr;
    }

    public void setPageAwardAr(String pageAwardAr) {
        this.pageAwardAr = pageAwardAr;
    }

    public Integer getPageOrder() {
        return pageOrder;
    }

    public void setPageOrder(Integer pageOrder) {
        this.pageOrder = pageOrder;
    }

    public Integer getScreen() {
        return screen;
    }

    public void setScreen(Integer screen) {
        this.screen = screen;
    }

    public Integer getLuckyGood() {
        return luckyGood;
    }

    public void setLuckyGood(Integer luckyGood) {
        this.luckyGood = luckyGood;
    }

    public Integer getClearValue() {
        return clearValue;
    }

    public void setClearValue(Integer clearValue) {
        this.clearValue = clearValue;
    }

    public Integer getRoll() {
        return roll;
    }

    public void setRoll(Integer roll) {
        this.roll = roll;
    }

    public String getPrize() {
        return prize;
    }

    public void setPrize(String prize) {
        this.prize = prize;
    }

    public String getRewardType() {
        return rewardType;
    }

    public void setRewardType(String rewardType) {
        this.rewardType = rewardType;
    }

    public Integer getSourceId() {
        return sourceId;
    }

    public void setSourceId(Integer sourceId) {
        this.sourceId = sourceId;
    }

    public Integer getRewardTime() {
        return rewardTime;
    }

    public void setRewardTime(Integer rewardTime) {
        this.rewardTime = rewardTime;
    }

    public Integer getRewardNum() {
        return rewardNum;
    }

    public void setRewardNum(Integer rewardNum) {
        this.rewardNum = rewardNum;
    }

    public Integer getLockType() {
        return lockType;
    }

    public void setLockType(Integer lockType) {
        this.lockType = lockType;
    }

    public Integer getCtime() {
        return ctime;
    }

    public void setCtime(Integer ctime) {
        this.ctime = ctime;
    }

    public Integer getFloatingScreen() {
        return floatingScreen;
    }

    public void setFloatingScreen(Integer floatingScreen) {
        this.floatingScreen = floatingScreen;
    }

    public int getMsgBlockAnchor() {
        return msgBlockAnchor;
    }

    public void setMsgBlockAnchor(int msgBlockAnchor) {
        this.msgBlockAnchor = msgBlockAnchor;
    }
}
