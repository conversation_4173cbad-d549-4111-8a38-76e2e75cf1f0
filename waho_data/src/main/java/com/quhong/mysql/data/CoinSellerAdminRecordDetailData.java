package com.quhong.mysql.data;

/**
 * <AUTHOR>
 * @date 2023/6/8
 */
public class CoinSellerAdminRecordDetailData {

    private Integer id;
    private String uid; // 币商uid
    private Integer merchantType; // 1一级币商 2二级币商
    private String aid; // 运营系统管理员的uid
    private String rechargeAmount; // 充值金额，运营人员输入，带小数点
    private Integer rechargeCoins; // 充值金币数
    private Integer rechargeType; // 充值类型 1:运营平台充值 2:奖励及薪资 3:测试 4:第三方充值
    private String payMethod; // 支付方式
    private String remark; // 备注
    private Integer ctime; // 充值时间

    public Integer getId() {
        return id;
    }

    public void setId(Integer id) {
        this.id = id;
    }

    public String getUid() {
        return uid;
    }

    public void setUid(String uid) {
        this.uid = uid;
    }

    public Integer getMerchantType() {
        return merchantType;
    }

    public void setMerchantType(Integer merchantType) {
        this.merchantType = merchantType;
    }

    public String getAid() {
        return aid;
    }

    public void setAid(String aid) {
        this.aid = aid;
    }

    public String getRechargeAmount() {
        return rechargeAmount;
    }

    public void setRechargeAmount(String rechargeAmount) {
        this.rechargeAmount = rechargeAmount;
    }

    public Integer getRechargeCoins() {
        return rechargeCoins;
    }

    public void setRechargeCoins(Integer rechargeCoins) {
        this.rechargeCoins = rechargeCoins;
    }

    public Integer getRechargeType() {
        return rechargeType;
    }

    public void setRechargeType(Integer rechargeType) {
        this.rechargeType = rechargeType;
    }

    public String getPayMethod() {
        return payMethod;
    }

    public void setPayMethod(String payMethod) {
        this.payMethod = payMethod;
    }

    public String getRemark() {
        return remark;
    }

    public void setRemark(String remark) {
        this.remark = remark;
    }

    public Integer getCtime() {
        return ctime;
    }

    public void setCtime(Integer ctime) {
        this.ctime = ctime;
    }
}
