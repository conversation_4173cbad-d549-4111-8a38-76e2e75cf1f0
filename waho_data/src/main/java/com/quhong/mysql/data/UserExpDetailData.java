package com.quhong.mysql.data;

/**
 * 用户每日获得的经验明细
 */
public class UserExpDetailData {
    private int id;
    private String uid;
    private String date_str;//年-月-日 格式的字符串
    private int stay_room;//房间停留
    private int up_mic;//上麦
    private int post_moment;//发布动态
    private int send_msg;//给不同好友发送消息
    private String send_msg_ids = "";
    private int like_moment;//点赞动态
    private String like_moment_ids = "";
    private int comment_moment;//评论动态
    private String comment_moment_ids = "";
    private int play_ludo;//玩ludo游戏
    private String play_ludo_ids = "";
    private int play_umo;//玩umo游戏
    private String play_umo_ids = "";
    private int followed;//被用户关注
    private String followed_ids = "";
    private int become_friends;//同意加好友请求
    private String become_friends_ids = "";
    private int reply_new_friend;//回复新好友消息
    private String reply_new_friend_ids = "";
    private int homepage_viewed;//个人主页每被一人查看
    private String homepage_viewed_ids = "";
    private int moment_liked;//动态收到点赞
    private String moment_liked_ids = "";
    private int moment_comment;//帖子每获得一人的评论
    private String moment_comment_ids = "";
    private int today_exp;//当日获得积分
    private int ctime;
    private int mtime;

    public int getId() {
        return id;
    }

    public void setId(int id) {
        this.id = id;
    }

    public String getUid() {
        return uid;
    }

    public void setUid(String uid) {
        this.uid = uid;
    }

    public String getDate_str() {
        return date_str;
    }

    public void setDate_str(String date_str) {
        this.date_str = date_str;
    }

    public int getStay_room() {
        return stay_room;
    }

    public void setStay_room(int stay_room) {
        this.stay_room = stay_room;
    }

    public int getUp_mic() {
        return up_mic;
    }

    public void setUp_mic(int up_mic) {
        this.up_mic = up_mic;
    }

    public int getPost_moment() {
        return post_moment;
    }

    public void setPost_moment(int post_moment) {
        this.post_moment = post_moment;
    }

    public int getSend_msg() {
        return send_msg;
    }

    public void setSend_msg(int send_msg) {
        this.send_msg = send_msg;
    }

    public String getSend_msg_ids() {
        return send_msg_ids;
    }

    public void setSend_msg_ids(String send_msg_ids) {
        this.send_msg_ids = send_msg_ids;
    }

    public int getLike_moment() {
        return like_moment;
    }

    public void setLike_moment(int like_moment) {
        this.like_moment = like_moment;
    }

    public String getLike_moment_ids() {
        return like_moment_ids;
    }

    public void setLike_moment_ids(String like_moment_ids) {
        this.like_moment_ids = like_moment_ids;
    }

    public int getComment_moment() {
        return comment_moment;
    }

    public void setComment_moment(int comment_moment) {
        this.comment_moment = comment_moment;
    }

    public String getComment_moment_ids() {
        return comment_moment_ids;
    }

    public void setComment_moment_ids(String comment_moment_ids) {
        this.comment_moment_ids = comment_moment_ids;
    }

    public int getPlay_ludo() {
        return play_ludo;
    }

    public void setPlay_ludo(int play_ludo) {
        this.play_ludo = play_ludo;
    }

    public int getPlay_umo() {
        return play_umo;
    }

    public void setPlay_umo(int play_umo) {
        this.play_umo = play_umo;
    }

    public int getFollowed() {
        return followed;
    }

    public void setFollowed(int followed) {
        this.followed = followed;
    }

    public String getFollowed_ids() {
        return followed_ids;
    }

    public void setFollowed_ids(String followed_ids) {
        this.followed_ids = followed_ids;
    }

    public int getBecome_friends() {
        return become_friends;
    }

    public void setBecome_friends(int become_friends) {
        this.become_friends = become_friends;
    }

    public String getBecome_friends_ids() {
        return become_friends_ids;
    }

    public void setBecome_friends_ids(String become_friends_ids) {
        this.become_friends_ids = become_friends_ids;
    }

    public int getReply_new_friend() {
        return reply_new_friend;
    }

    public void setReply_new_friend(int reply_new_friend) {
        this.reply_new_friend = reply_new_friend;
    }

    public String getReply_new_friend_ids() {
        return reply_new_friend_ids;
    }

    public void setReply_new_friend_ids(String reply_new_friend_ids) {
        this.reply_new_friend_ids = reply_new_friend_ids;
    }

    public int getHomepage_viewed() {
        return homepage_viewed;
    }

    public void setHomepage_viewed(int homepage_viewed) {
        this.homepage_viewed = homepage_viewed;
    }

    public String getHomepage_viewed_ids() {
        return homepage_viewed_ids;
    }

    public void setHomepage_viewed_ids(String homepage_viewed_ids) {
        this.homepage_viewed_ids = homepage_viewed_ids;
    }

    public int getMoment_liked() {
        return moment_liked;
    }

    public void setMoment_liked(int moment_liked) {
        this.moment_liked = moment_liked;
    }

    public String getMoment_liked_ids() {
        return moment_liked_ids;
    }

    public void setMoment_liked_ids(String moment_liked_ids) {
        this.moment_liked_ids = moment_liked_ids;
    }

    public int getMoment_comment() {
        return moment_comment;
    }

    public void setMoment_comment(int moment_comment) {
        this.moment_comment = moment_comment;
    }

    public String getMoment_comment_ids() {
        return moment_comment_ids;
    }

    public void setMoment_comment_ids(String moment_comment_ids) {
        this.moment_comment_ids = moment_comment_ids;
    }

    public int getToday_exp() {
        return today_exp;
    }

    public void setToday_exp(int today_exp) {
        this.today_exp = today_exp;
    }

    public int getCtime() {
        return ctime;
    }

    public void setCtime(int ctime) {
        this.ctime = ctime;
    }

    public int getMtime() {
        return mtime;
    }

    public void setMtime(int mtime) {
        this.mtime = mtime;
    }

    public String getPlay_ludo_ids() {
        return play_ludo_ids;
    }

    public void setPlay_ludo_ids(String play_ludo_ids) {
        this.play_ludo_ids = play_ludo_ids;
    }

    public String getPlay_umo_ids() {
        return play_umo_ids;
    }

    public void setPlay_umo_ids(String play_umo_ids) {
        this.play_umo_ids = play_umo_ids;
    }
}
