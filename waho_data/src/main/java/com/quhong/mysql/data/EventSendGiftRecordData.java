package com.quhong.mysql.data;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;

/**
 * <AUTHOR>
 * @date 2022/12/9
 */
@TableName("t_event_send_gift_record")
public class EventSendGiftRecordData {

    /**
     * id
     */
    @TableId(type = IdType.AUTO)
    private Integer id;

    /**
     * 活动房间id
     */
    private String roomId;

    /**
     * 发送礼物的用户id
     */
    private String uid;

    /**
     * 活动id
     */
    private Integer eventId;

    /**
     * 礼物总数量
     */
    private Integer giftTotalNum;

    /**
     * 礼物总价值
     */
    private Long giftTotalPrice;

    public Integer getId() {
        return id;
    }

    public void setId(Integer id) {
        this.id = id;
    }

    public String getRoomId() {
        return roomId;
    }

    public void setRoomId(String roomId) {
        this.roomId = roomId;
    }

    public String getUid() {
        return uid;
    }

    public void setUid(String uid) {
        this.uid = uid;
    }

    public Integer getEventId() {
        return eventId;
    }

    public void setEventId(Integer eventId) {
        this.eventId = eventId;
    }

    public Integer getGiftTotalNum() {
        return giftTotalNum;
    }

    public void setGiftTotalNum(Integer giftTotalNum) {
        this.giftTotalNum = giftTotalNum;
    }

    public Long getGiftTotalPrice() {
        return giftTotalPrice;
    }

    public void setGiftTotalPrice(Long giftTotalPrice) {
        this.giftTotalPrice = giftTotalPrice;
    }
}
