package com.quhong.mysql.dao;

import com.quhong.core.utils.DateHelper;
import com.quhong.data.MoneyDetailsData;
import com.quhong.mongo.data.GameStatData;
import com.quhong.mysql.slave_mapper.waho_log.SlaveMoneyDetailMapper;
import com.quhong.utils.WalletUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.util.*;
import java.util.stream.Collectors;


@Lazy
@Service
public class SlaveMoneyDetailDao extends MonthShardingDao<SlaveMoneyDetailMapper> {
    private static final Logger logger = LoggerFactory.getLogger(SlaveMoneyDetailDao.class);

    public SlaveMoneyDetailDao() {
        super("t_money_detail");
    }


    public long calculateGameCostBeans(Collection<String> uidSet, int startTime, int endTime) {
        try {
            List<String> suffixList = DateHelper.ARABIAN.getTableSuffixList(startTime, endTime);
            List<String> retList = getExistTableSuffixList(suffixList);
            if (retList.isEmpty()) {
                return 0;
            }
            long costBeans;
            if (DateHelper.getNowSeconds() > WalletUtils.ACTIVE_TIME) {
                if (startTime >= WalletUtils.ACTIVE_TIME) {
                    costBeans = tableMapper.calculateGameCostBeans(retList, uidSet, startTime, endTime);
                } else if (endTime <= WalletUtils.ACTIVE_TIME) {
                    costBeans = tableMapper.calculateGameCostBeans(retList, uidSet, startTime, endTime) * WalletUtils.DIAMONDS_TIMES;
                } else {
                    costBeans = tableMapper.calculateGameCostBeans(retList, uidSet, startTime, WalletUtils.ACTIVE_TIME) * WalletUtils.DIAMONDS_TIMES + tableMapper.calculateGameCostBeans(retList, uidSet, WalletUtils.ACTIVE_TIME, endTime);
                }
            } else {
                costBeans = tableMapper.calculateGameCostBeans(retList, uidSet, startTime, endTime);
            }
            return Math.abs(costBeans);
        } catch (Exception e) {
            logger.error("calculateGameCostBeans error startTime={} endTime={}", startTime, endTime, e);
            return 0;
        }
    }


    public long getTotalBeanByTypeList(String uid, List<Integer> typeList, int startTime, int endTime) {
        try{
            List<String> suffixList = getTableSuffixList(-2);
            suffixList = suffixList.stream().filter(this::checkExist).collect(Collectors.toList());
            if (suffixList.isEmpty()) {
                return 0;
            }
            if (DateHelper.getNowSeconds() > WalletUtils.ACTIVE_TIME) {
                if (startTime >= WalletUtils.ACTIVE_TIME) {
                    return tableMapper.getTotalBeanByType(uid, typeList, startTime, endTime, suffixList);
                } else if (endTime <= WalletUtils.ACTIVE_TIME) {
                    return tableMapper.getTotalBeanByType(uid, typeList, startTime, endTime, suffixList) * WalletUtils.DIAMONDS_TIMES;
                } else {
                    return tableMapper.getTotalBeanByType(uid, typeList, startTime, WalletUtils.ACTIVE_TIME, suffixList) * WalletUtils.DIAMONDS_TIMES + tableMapper.getTotalBeanByType(uid, typeList, WalletUtils.ACTIVE_TIME, endTime, suffixList);
                }
            } else {
                return tableMapper.getTotalBeanByType(uid, typeList, startTime, endTime, suffixList);
            }
        } catch (Exception e) {
            logger.error("getTotalBeanByTypeList error uid={} error={}", uid, e.getMessage(), e);
            return 0;
        }
    }

    public long calculateTotalBeanSum(String uid, int aType, int startTime, int endTime) {
        try{
            List<String> suffixList = DateHelper.ARABIAN.getTableSuffixList(startTime, endTime);
            List<String> retList = getExistTableSuffixList(suffixList);
            if (retList.isEmpty()) {
                return 0;
            }
            if (DateHelper.getNowSeconds() > WalletUtils.ACTIVE_TIME) {
                if (startTime >= WalletUtils.ACTIVE_TIME) {
                    return tableMapper.calculateTotalBeanSum(retList, uid, aType, startTime, endTime);
                } else if (endTime <= WalletUtils.ACTIVE_TIME) {
                    return tableMapper.calculateTotalBeanSum(retList, uid, aType, startTime, endTime) * WalletUtils.DIAMONDS_TIMES;
                } else {
                    return tableMapper.calculateTotalBeanSum(retList, uid, aType, startTime, WalletUtils.ACTIVE_TIME) * WalletUtils.DIAMONDS_TIMES + tableMapper.calculateTotalBeanSum(retList, uid, aType, WalletUtils.ACTIVE_TIME, endTime);
                }
            } else {
                return tableMapper.calculateTotalBeanSum(retList, uid, aType, startTime, endTime);
            }
        } catch (Exception e) {
            logger.error("calculateTotalBeanSum error uid={} aType={} startTime={} endTime={} error={}", uid, aType, startTime, endTime, e.getMessage(), e);
            return 0;
        }
    }

    public Map<Integer, Long> getUserGameActTypeMap(String uid, List<Integer> typeList, int startTime, int endTime) {
        Map<Integer, Long> resultMap = new HashMap<>(typeList.size());
        try{
            List<String> suffixList = DateHelper.ARABIAN.getTableSuffixList(startTime, endTime);
            List<String> retList = getExistTableSuffixList(suffixList);
            if (!retList.isEmpty()) {
                for (String suffix : retList) {
                    List<MoneyDetailsData> list = tableMapper.getUserGameActTypeSum(suffix, uid, typeList, startTime, endTime);
                    if (CollectionUtils.isEmpty(list)) {
                        continue;
                    }
                    list.forEach(k -> {
                        resultMap.put(k.getAtype(), resultMap.getOrDefault(k.getAtype(), 0L) + k.getChanged());
                    });
                }
            }
        } catch (Exception e) {
            logger.error("getUserGameActTypeMap error uid={} startTime={} endTime={} error={}", uid, startTime, endTime, e.getMessage(), e);
        }
        return resultMap;
    }

    public Set<String> getUidSet(List<Integer> actTypeList, int startTime, int endTime) {
        try {
            String tableSuffix = DateHelper.ARABIAN.getTableSuffix(new Date(startTime * 1000L));
            return tableMapper.getUidSet(tableSuffix, actTypeList, startTime, endTime);
        } catch (Exception e) {
            logger.error("getUidSet error. startTime={} endTime={} {}", startTime, endTime, e.getMessage(), e);
            return Collections.emptySet();
        }
    }

    public List<GameStatData> getUserGameStatData(String uid, List<Integer> actTypeList, int startTime, int endTime) {
        try {
            String tableSuffix = DateHelper.ARABIAN.getTableSuffix(new Date(startTime * 1000L));
            return tableMapper.getUserGameStatData(tableSuffix, uid, actTypeList, startTime, endTime);
        } catch (Exception e) {
            logger.error("getUserGameStatData error.uid={} startTime={} endTime={} {}", uid, startTime, endTime, e.getMessage(), e);
            return Collections.emptyList();
        }
    }

    public Set<String> getUidSetByType(int actType, int startTime, int endTime) {
        try {
            String tableSuffix = DateHelper.ARABIAN.getTableSuffix(new Date(startTime * 1000L));
            return tableMapper.getUidSetByType(tableSuffix, actType, startTime, endTime);
        } catch (Exception e) {
            logger.error("getUidSetByType error. startTime={} endTime={} {}", startTime, endTime, e.getMessage(), e);
            return Collections.emptySet();
        }
    }

    public int getRecordCount(int actType, int startTime, int endTime) {
        try {
            List<String> tableSuffixList = DateHelper.ARABIAN.getTableSuffixList(startTime, endTime);
            int count = 0;
            for (String tableSuffix : tableSuffixList) {
                count += tableMapper.getRecordCount(tableSuffix, actType, startTime, endTime);
            }
            return count;
        } catch (Exception e) {
            logger.error("getRecordCount error. actType={} startTime={} endTime={} {}", actType, startTime, endTime, e.getMessage(), e);
            return 0;
        }
    }

    public Set<String> getPlayGreedyUserSet() {
        return tableMapper.getPlayGreedyUserSet();
    }
}
