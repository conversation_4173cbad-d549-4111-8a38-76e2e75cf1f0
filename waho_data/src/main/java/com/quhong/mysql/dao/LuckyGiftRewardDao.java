package com.quhong.mysql.dao;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.quhong.config.CaffeineCacheConfig;
import com.quhong.core.utils.DateHelper;
import com.quhong.data.ActorData;
import com.quhong.image.ImageUrlGenerator;
import com.quhong.mongo.dao.ActorDao;
import com.quhong.mysql.data.LuckyGiftRewardData;
import com.quhong.mysql.mapper.waho_log.LuckyGiftRewardMapper;
import com.quhong.redis.DataRedisBean;
import com.quhong.utils.CacheUtils;
import jakarta.annotation.Resource;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.context.annotation.Lazy;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.data.redis.core.ZSetOperations;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.Set;
import java.util.concurrent.TimeUnit;


@Lazy
@Component
public class LuckyGiftRewardDao extends ServiceImpl<LuckyGiftRewardMapper, LuckyGiftRewardData> {
    private static final Logger logger = LoggerFactory.getLogger(LuckyGiftRewardDao.class);


    @Resource(name = DataRedisBean.MAIN_CLUSTER)
    private StringRedisTemplate redisTemplate;
    @Resource
    private ActorDao actorDao;

    public void saveData(LuckyGiftRewardData data) {
        super.save(data);
        incrRankingScore(data.getUid(), data.getJackpotType(), data.getReward());
    }

    private String getRankKey(String dateStr, int jackpotType) {
        return "zset:luckyGiftRank:" + dateStr + ":" + jackpotType;
    }

    public void incrRankingScore(String uid, int jackpotType, int score) {
        try {
            String dateStr = DateHelper.ARABIAN.formatDateInDay();
            String key = getRankKey(dateStr, jackpotType);
            redisTemplate.opsForZSet().incrementScore(key, uid, score);
            if (!CacheUtils.hasKey(key)) {
                redisTemplate.expire(key, 2, TimeUnit.DAYS);
                CacheUtils.put(key, key);
            }
        } catch (Exception e) {
            logger.error("incrRankingScore error uid={} jackpotType={} score={}", uid, jackpotType, score, e);
        }
    }

    @Cacheable(value = "getRankingList", key = "#p0", cacheManager = CaffeineCacheConfig.EXPIRE_10M_AFTER_WRITE)
    public List<RankInfo> getRankingList(int jackpotType) {
        String dateStr = DateHelper.ARABIAN.formatDateInDay();
        String key = getRankKey(dateStr, jackpotType);
        Set<ZSetOperations.TypedTuple<String>> rangeWithScores = redisTemplate.opsForZSet().reverseRangeWithScores(key, 0, 50 - 1);
        if (null == rangeWithScores) {
            return Collections.emptyList();
        }
        List<RankInfo> result = new ArrayList<>();
        for (ZSetOperations.TypedTuple<String> rangeWithScore : rangeWithScores) {
            if (null == rangeWithScore.getValue() || null == rangeWithScore.getScore()) {
                continue;
            }
            RankInfo rankInfo = new RankInfo();
            rankInfo.setUid(rangeWithScore.getValue());
            rankInfo.setReward(rangeWithScore.getScore().intValue());
            ActorData actorData = actorDao.getActorDataFromCache(rankInfo.getUid());
            rankInfo.setName(actorData.getName());
            rankInfo.setHead(ImageUrlGenerator.generateRoomUrl(actorData.getHead()));
            result.add(rankInfo);
        }
        return result;
    }

    @Cacheable(value = "luckyGiftWinningUser", key = "#p0", cacheManager = CaffeineCacheConfig.EXPIRE_1M_AFTER_WRITE)
    public List<LuckyGiftRewardData> findWinningUser(int jackpotType) {
        return lambdaQuery()
                .eq(LuckyGiftRewardData::getJackpotType, jackpotType)
                .orderByDesc(LuckyGiftRewardData::getCtime)
                .last("limit 40").list();
    }

    public List<LuckyGiftRewardData> findByUid(String uid, int jackpotType) {
        return lambdaQuery()
                .eq(LuckyGiftRewardData::getUid, uid)
                .eq(LuckyGiftRewardData::getJackpotType, jackpotType)
                .orderByDesc(LuckyGiftRewardData::getCtime).
                last("limit 30").list();
    }

    public void cleanUp() {
        int cleanupTime = DateHelper.getNowSeconds() - 15 * 24 * 60 * 60;
        QueryWrapper<LuckyGiftRewardData> queryWrapper = new QueryWrapper<>();
        queryWrapper.lt("ctime", cleanupTime);
        Long count = baseMapper.selectCount(queryWrapper);
        if (null != count) {
            while (count > 0) {
                logger.info("cleanUp luckyGiftRewardData. count={}", count);
                count = count - 1000;
                QueryWrapper<LuckyGiftRewardData> deleteWrapper = new QueryWrapper<>();
                deleteWrapper.lt("ctime", cleanupTime);
                deleteWrapper.last("limit 1000");
                baseMapper.delete(deleteWrapper);
            }
        }
    }

    public static class RankInfo {
        private String uid; // 中奖者uid
        private String name; // 中奖名字
        private String head; // 中奖头像
        private int reward; // 中奖信息

        public String getUid() {
            return uid;
        }

        public void setUid(String uid) {
            this.uid = uid;
        }

        public String getName() {
            return name;
        }

        public void setName(String name) {
            this.name = name;
        }

        public String getHead() {
            return head;
        }

        public void setHead(String head) {
            this.head = head;
        }

        public int getReward() {
            return reward;
        }

        public void setReward(int reward) {
            this.reward = reward;
        }
    }

}
