package com.quhong.mysql.dao;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.UpdateWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.quhong.mysql.data.EventGiftStatistics;
import com.quhong.mysql.data.EventSendGiftRecordData;
import com.quhong.mysql.mapper.waho_log.EventSendGiftRecordMapper;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;

import jakarta.annotation.Resource;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2022/12/9
 */
@Component
public class EventSendGiftRecordDao {

    private static final Logger logger = LoggerFactory.getLogger(EventSendGiftRecordDao.class);

    @Resource
    private EventSendGiftRecordMapper sendGiftRecordMapper;

    public void insert(EventSendGiftRecordData data) {
        sendGiftRecordMapper.insert(data);
    }

    public void update(EventSendGiftRecordData data) {
        UpdateWrapper<EventSendGiftRecordData> updateWrapper = new  UpdateWrapper<>();
        updateWrapper.eq("id", data.getId());
        sendGiftRecordMapper.update(data, updateWrapper);
    }

    public EventSendGiftRecordData selectOne(String roomId, Integer eventId, String uid) {
        QueryWrapper<EventSendGiftRecordData> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("room_id", roomId);
        queryWrapper.eq("event_id", eventId);
        queryWrapper.eq("uid", uid);
        return sendGiftRecordMapper.selectOne(queryWrapper);
    }

    public List<EventSendGiftRecordData> selectPage(String roomId, Integer eventId, int page, int pageSize) {
        IPage<EventSendGiftRecordData> dataPage = new Page<>(page, pageSize);
        QueryWrapper<EventSendGiftRecordData> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("room_id", roomId);
        queryWrapper.eq("event_id", eventId);
        queryWrapper.orderByDesc("gift_total_price");
        dataPage = sendGiftRecordMapper.selectPage(dataPage, queryWrapper);
        return dataPage.getRecords();
    }

    public EventGiftStatistics getEventGiftStatistics(String roomId, Integer eventId) {
        return sendGiftRecordMapper.getEventGiftStatistics(roomId, eventId);
    }
}
