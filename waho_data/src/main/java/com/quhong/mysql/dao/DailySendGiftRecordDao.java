package com.quhong.mysql.dao;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.quhong.core.date.DateSupport;
import com.quhong.mysql.data.DailySendGiftRecordData;
import com.quhong.mysql.mapper.waho_log.DailySendGiftRecordMapper;
import com.quhong.utils.CacheUtils;
import jakarta.annotation.Resource;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2024/9/27
 */
@Lazy
@Component
public class DailySendGiftRecordDao {

    @Resource
    private DailySendGiftRecordMapper baseMapper;

    public void insert(String senderUid, String receiverUid) {
        int intDate = DateSupport.ARABIAN.getTodayIntDate();
        String cacheKey = senderUid + "_" + receiverUid + "_" + intDate;
        if (CacheUtils.hasKey(cacheKey)) {
            return;
        }
        baseMapper.insertOne(new DailySendGiftRecordData(senderUid, receiverUid, intDate));
        CacheUtils.put(cacheKey, true);
    }

    public int getReceiveGiftUserNum(String uid, int startDate, int endDate) {
        QueryWrapper<DailySendGiftRecordData> query = Wrappers.query();
        query.lambda().eq(DailySendGiftRecordData::getReceiverUid, uid)
                .ge(DailySendGiftRecordData::getIntDate, startDate)
                .le(DailySendGiftRecordData::getIntDate, endDate);
        return baseMapper.selectCount(query).intValue();
    }

    public int getHasReceivedGiftUserNum(List<String> uidList, int startDate, int endDate) {
        return baseMapper.getHasReceivedGiftUserNum(uidList, startDate, endDate);
    }
}
