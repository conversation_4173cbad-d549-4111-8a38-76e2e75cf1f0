package com.quhong.mysql.dao;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.quhong.mysql.data.UserFeedbackNumData;
import com.quhong.mysql.mapper.waho.UserFeedbackNumMapper;
import com.quhong.utils.CollectionUtil;
import jakarta.annotation.Resource;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Component;

import java.util.Set;


@Component
@Lazy
public class UserFeedbackNumDao {

    private static final Logger logger = LoggerFactory.getLogger(UserFeedbackNumDao.class);
    @Resource
    private UserFeedbackNumMapper userFeedbackNumMapper;


    public int updateOne(UserFeedbackNumData userFeedbackNumData) {
        return userFeedbackNumMapper.updateById(userFeedbackNumData);
    }


    public int insertOne(UserFeedbackNumData data) {
        return userFeedbackNumMapper.insert(data);
    }

    public UserFeedbackNumData selectOne(String feedbackId){
        QueryWrapper<UserFeedbackNumData> query = new QueryWrapper<>();
        query.eq("feedback_id", feedbackId);
        return userFeedbackNumMapper.selectOne(query);
    }

    public Set<String> getFeedbackNumFromDB(int feedbackNum) {
        QueryWrapper<UserFeedbackNumData> queryWrapper = new QueryWrapper<>();
        if(feedbackNum == 0){
            queryWrapper.ge("total", 4);
        }else {
            queryWrapper.eq("total", feedbackNum);
        }

        queryWrapper.select("feedback_id");
        return CollectionUtil.listToPropertySet(userFeedbackNumMapper.selectList(queryWrapper), UserFeedbackNumData::getFeedbackId);
    }


}
