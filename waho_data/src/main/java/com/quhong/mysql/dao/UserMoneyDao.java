package com.quhong.mysql.dao;

import com.quhong.mysql.data.UserMoneyData;
import com.quhong.mysql.mapper.waho.UserMoneyMapper;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Component;

@Lazy
@Component
public class UserMoneyDao {
    private static final Logger logger = LoggerFactory.getLogger(UserMoneyDao.class);

    @Autowired
    private UserMoneyMapper mapper;

    public boolean insertMoney(UserMoneyData userMoneyData) {
        try {
            mapper.insert(userMoneyData);
            return true;
        } catch (Exception e) {
            logger.error("insert money error. userMoneyData={}", userMoneyData);
            return false;
        }
    }

    public int getMaxGenRid() {
        try {
            return mapper.getMaxRid();
        } catch (Exception e) {
            logger.error("getMaxGenRid error. e={}", e.getMessage(), e);
            return 0;
        }

    }

    public long getBalance(String uid) {
        try {
            Long balance = mapper.getBalance(uid);
            if (null == balance) {
                logger.error("cannot find actor balance. money is null. uid={}", uid);
                return 0;
            }
            return balance;
        } catch (Exception e) {
            logger.error("getBalance error. uid={} {}", uid, e.getMessage(), e);
            return 0;
        }
    }
}
