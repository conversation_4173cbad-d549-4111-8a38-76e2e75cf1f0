package com.quhong.mysql.dao;

import com.quhong.config.CaffeineCacheConfig;
import com.quhong.core.date.DateSupport;
import com.quhong.core.date.DayTimeSupport;
import com.quhong.core.utils.DateHelper;
import com.quhong.mongo.dao.CharmStatDao;
import com.quhong.mongo.dao.GameStatDao;
import com.quhong.mysql.data.AnchorDailyDetailData;
import com.quhong.mysql.mapper.waho_log.AnchorDailyDetailMapper;
import jakarta.annotation.Resource;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import java.time.LocalDate;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2023/10/27
 */
@Component
public class AnchorDailyDetailDao {

    public final Logger logger = LoggerFactory.getLogger(getClass());

    private static final String TABLE_PRE = "t_anchor_daily_detail";

    @Resource
    private RoomMicLogDao roomMicLogDao;
    @Resource
    private AnchorDailyDetailMapper anchorDailyDetailMapper;
    @Resource
    private CharmStatDao charmStatDao;
    @Resource
    private GameStatDao gameStatDao;
    @Resource
    private DailySendGiftRecordDao dailySendGiftRecordDao;

    public void insert(AnchorDailyDetailData data) {
        anchorDailyDetailMapper.insertOne(getTableName(data.getUid()), data);
    }

    @Cacheable(value = "cache", cacheManager = CaffeineCacheConfig.EXPIRE_1M_AFTER_WRITE,
            key = "#root.method.name+T(org.springframework.util.StringUtils).arrayToDelimitedString(args,'-')")
    public List<AnchorDailyDetailData> selectList(String uid, int familyId, int startTime, int endTime) {
        LocalDate yesterday = DateSupport.ARABIAN.getYesterday();
        LocalDate startDate = DateSupport.ARABIAN.getLocalDate(startTime * 1000L);
        LocalDate endDate = DateSupport.ARABIAN.getLocalDate(endTime * 1000L);
        List<AnchorDailyDetailData> list = new ArrayList<>();
        List<Integer> dateList = new ArrayList<>();
        while (!startDate.isAfter(endDate)) {
            String strDate = DateSupport.format(startDate);
            if (startDate.isBefore(yesterday)) {
                dateList.add(Integer.parseInt(strDate.replace("-", "")));
            }
            startDate = startDate.plusDays(1);
        }
        List<AnchorDailyDetailData> listFromDb = selectListFromDb(uid, familyId, dateList);
        if (!CollectionUtils.isEmpty(listFromDb)) {
            list.addAll(listFromDb);
        }
        int yesterdayTime = (int) DayTimeSupport.ARABIAN.getTimeSeconds(yesterday.atTime(0, 0, 0));
        if (endTime > yesterdayTime) {
            List<AnchorDailyDetailData> listFromOther = selectListFromOther(uid, familyId, Math.max(yesterdayTime, startTime), DateHelper.getNowSeconds());
            if (!CollectionUtils.isEmpty(listFromOther)) {
                list.addAll(listFromOther);
            }
        }
        return list;
    }

    private List<AnchorDailyDetailData> selectListFromDb(String uid, int familyId, List<Integer> dateList) {
        if (CollectionUtils.isEmpty(dateList)) {
            return Collections.emptyList();
        }
        return anchorDailyDetailMapper.selectList(getTableName(uid), uid, familyId, dateList);
    }

    public List<AnchorDailyDetailData> selectListFromOther(String uid, int familyId, int startTime, int endTime) {
        LocalDate startDate = DateSupport.ARABIAN.getLocalDate(startTime * 1000L);
        LocalDate endDate = DateSupport.ARABIAN.getLocalDate(endTime * 1000L);
        List<AnchorDailyDetailData> list = new ArrayList<>();
        while (!startDate.isAfter(endDate)) {
            list.add(selectOneFromOther(uid, familyId, DateSupport.format(startDate)));
            startDate = startDate.plusDays(1);
        }
        return list;
    }

    public AnchorDailyDetailData selectOneFromOther(String uid, int familyId, String strDate) {
        int date = Integer.parseInt(strDate.replace("-", ""));
        Integer[] timeArr = DateHelper.ARABIAN.getStartOrEndSeconds(strDate, strDate);
        int startTime = timeArr[0];
        int endTime = --timeArr[1];
        AnchorDailyDetailData data = new AnchorDailyDetailData();
        data.setUid(uid);
        data.setFamilyId(familyId);
        data.setDate(date);
        data.setUpMicTime(roomMicLogDao.actorAddUpMicTime(startTime, endTime, uid));
        data.setRoomTotalGifters(dailySendGiftRecordDao.getReceiveGiftUserNum(uid, DateSupport.ARABIAN.getIntDate(startTime), DateSupport.ARABIAN.getIntDate(endTime)));
        data.setMsgCharmIncome((int)charmStatDao.getUserTotalCharm(uid, familyId, 2, startTime, endTime));
        data.setRoomCharmIncome((int)charmStatDao.getUserTotalCharm(uid, familyId, 1, startTime, endTime));
        data.setRoomGameSpending(gameStatDao.getUserGameTotalCost(uid, startTime, endTime));
        data.setTaskCharmIncome((int)charmStatDao.getUserTotalCharm(uid, null, Arrays.asList(8, 11, 12), startTime, endTime));
        data.setCtime(DateHelper.getNowSeconds());
        return data;
    }

    /**
     * 获取分表名
     */
    private String getTableName(String uid) {
        return TABLE_PRE + "_" + uid.substring(uid.length() - 1);
    }
}
