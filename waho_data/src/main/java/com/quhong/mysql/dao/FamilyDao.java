package com.quhong.mysql.dao;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.UpdateWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.quhong.config.CaffeineCacheConfig;
import com.quhong.core.utils.DateHelper;
import com.quhong.data.condition.FamilyCondition;
import com.quhong.mysql.data.FamilyData;
import com.quhong.mysql.mapper.waho.FamilyMapper;
import com.quhong.redis.DataRedisBean;
import com.quhong.utils.CollectionUtil;
import jakarta.annotation.Resource;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.context.annotation.Lazy;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import java.util.*;
import java.util.concurrent.TimeUnit;


/**
 * <AUTHOR>
 * @date 2023/5/29
 */
@Lazy
@Component
public class FamilyDao {

    private final Logger logger = LoggerFactory.getLogger(getClass());

    public static String FAMILY_DETAIL_HEAD = "https://cloudcdn.waho.live/resource/op_sys_1693394734_default_family_icon.png";

    @Resource(name = DataRedisBean.MAIN_CLUSTER)
    private StringRedisTemplate redisTemplate;
    @Resource
    private FamilyMapper familyMapper;

    public void insert(FamilyData data) {
        familyMapper.insert(data);
    }

    public void update(FamilyData data) {
        familyMapper.updateById(data);
    }

    public FamilyData selectById(Integer id) {
        return familyMapper.selectById(id);
    }

    @Cacheable(value = "cache", cacheManager = CaffeineCacheConfig.EXPIRE_10M_AFTER_WRITE,
            condition = "T(com.quhong.core.config.ServerConfig).isProduct()",
            key = "#root.method.name+T(org.springframework.util.StringUtils).arrayToDelimitedString(args,'-')")
    public FamilyData selectByIdFromCache(Integer id) {
        if (null == id || 0 == id) {
            return null;
        }
        return selectById(id);
    }

    /**
     * 获取所有的子代理，包括自己
     */
    @Cacheable(value = "cache", cacheManager = CaffeineCacheConfig.EXPIRE_10M_AFTER_WRITE,
            condition = "T(com.quhong.core.config.ServerConfig).isProduct()",
            key = "#root.method.name+T(org.springframework.util.StringUtils).arrayToDelimitedString(args,'-')")
    public Set<Integer> findAllAgent(int id) {
        Set<Integer> allAgentSet = new HashSet<>();
        allAgentSet.add(id);
        fillAllChildFamily(id, allAgentSet);
        return allAgentSet;
    }

    public void fillAllChildFamily(int pid, Set<Integer> allAgentSet) {
        QueryWrapper<FamilyData> query = Wrappers.query();
        query.lambda().eq(FamilyData::getPid, pid).select(FamilyData::getId);
        List<FamilyData> familyDataList = familyMapper.selectList(query);
        Set<Integer> childFamily = CollectionUtil.listToPropertySet(familyDataList, FamilyData::getId);
        if (!childFamily.isEmpty()) {
            allAgentSet.addAll(childFamily);
            for (Integer id : childFamily) {
                fillAllChildFamily(id, allAgentSet);
            }
        }
    }

    public List<FamilyData> getSubAgentList(int pid) {
        QueryWrapper<FamilyData> query = Wrappers.query();
        query.lambda().eq(FamilyData::getPid, pid).eq(FamilyData::getStatus, 1);
        return familyMapper.selectList(query);
    }

    public List<FamilyData> getSubAgentList(int pid, Integer status) {
        QueryWrapper<FamilyData> query = Wrappers.query();
        query.lambda().eq(FamilyData::getPid, pid);
        if (status != null) {
            query.lambda().eq(FamilyData::getStatus, status);
        }
        return familyMapper.selectList(query);
    }

    public FamilyData selectByFamilyRid(Integer familyRid) {
        QueryWrapper<FamilyData> query = Wrappers.query();
        query.lambda().eq(FamilyData::getRid, familyRid);
        return familyMapper.selectOne(query);
    }

    public void updateById(FamilyData familyData) {
        familyMapper.updateById(familyData);
    }

    public IPage<FamilyData> selectList(FamilyCondition condition, int page, int pageSize) {
        IPage<FamilyData> dataPage = new Page<>(page, pageSize);
        QueryWrapper<FamilyData> queryWrapper = new QueryWrapper<>();
        if (condition.getRid() != null) {
            queryWrapper.eq("rid", condition.getRid());
        }
        if (StringUtils.hasLength(condition.getOwnerUid())) {
            queryWrapper.eq("owner_uid", condition.getOwnerUid());
        }
        if (StringUtils.hasLength(condition.getRegion())) {
            queryWrapper.eq("region", condition.getRegion());
        }
        if (StringUtils.hasLength(condition.getCountry())) {
            queryWrapper.eq("country", condition.getCountry());
        }
        if (StringUtils.hasLength(condition.getManager())) {
            if ("无运营负责人".equals(condition.getManager())) {
                queryWrapper.eq("manager", "");
            } else {
                queryWrapper.eq("manager", condition.getManager());
            }
        }
        if (StringUtils.hasLength(condition.getSuperManager())) {
            if ("无超管".equals(condition.getSuperManager())) {
                queryWrapper.eq("super_manager", "");
            } else {
                queryWrapper.eq("super_manager", condition.getSuperManager());
            }
        }
        // if (!CollectionUtils.isEmpty(condition.getManagerList())) {
        //
        //     queryWrapper.in("manager", condition.getManagerList());
        // }
        if (condition.getStatus() != null && condition.getStatus() != 0) {
            queryWrapper.eq("status", condition.getStatus());
        }
        if (condition.getForcedControl() != null && condition.getForcedControl() != -1) {
            queryWrapper.eq("forced_control", condition.getForcedControl());
        }
        if (condition.getCascadeLevel() != null && condition.getCascadeLevel() > 0) {
            queryWrapper.in("cascade_level", condition.getCascadeLevel());
        }
        queryWrapper.orderByDesc("ctime");
        return familyMapper.selectPage(dataPage, queryWrapper);
    }

    public int calcCascadeLevel(int familyId, int cascadeLevel) {
        if (0 == familyId) {
            return cascadeLevel;
        }
        cascadeLevel++;
        FamilyData parentFamily = selectByIdFromCache(familyId);
        if (null == parentFamily.getPid()) {
            return cascadeLevel;
        }
        return calcCascadeLevel(parentFamily.getPid(), cascadeLevel);
    }

    public List<FamilyData> selectAll() {
        return familyMapper.selectList(new QueryWrapper<>());
    }

    @Cacheable(value = "cache", cacheManager = CaffeineCacheConfig.EXPIRE_10M_AFTER_WRITE,
            condition = "T(com.quhong.core.config.ServerConfig).isProduct()",
            key = "#root.method.name+T(org.springframework.util.StringUtils).arrayToDelimitedString(args,'-')")
    public Collection<Integer> getAllDisabledFamily() {
        QueryWrapper<FamilyData> query = Wrappers.query();
        query.lambda().eq(FamilyData::getStatus, 2).select(FamilyData::getId);
        List<FamilyData> familyDataList = familyMapper.selectList(query);
        return CollectionUtil.listToPropertySet(familyDataList, FamilyData::getId);
    }

    public List<FamilyData> getAllForcedControlFamily() {
        QueryWrapper<FamilyData> query = Wrappers.query();
        query.lambda().eq(FamilyData::getForcedControl, 1).eq(FamilyData::getStatus, 1);
        return familyMapper.selectList(query);
    }

    @Cacheable(value = "cache", cacheManager = CaffeineCacheConfig.EXPIRE_10M_AFTER_WRITE,
            condition = "T(com.quhong.core.config.ServerConfig).isProduct()",
            key = "#root.method.name+T(org.springframework.util.StringUtils).arrayToDelimitedString(args,'-')")
    public List<FamilyData> getAllFamilyFromCache() {
        return getAllFamily();
    }

    public List<FamilyData> getAllFamily() {
        QueryWrapper<FamilyData> query = Wrappers.query();
        query.lambda().eq(FamilyData::getStatus, 1);
        return familyMapper.selectList(query);
    }

    public List<FamilyData> selectByManagerList(Collection<String> managerList) {
        if (CollectionUtils.isEmpty(managerList)) {
            return Collections.emptyList();
        }
        QueryWrapper<FamilyData> query = Wrappers.query();
        query.lambda().eq(FamilyData::getStatus, 1).in(FamilyData::getManager, managerList);
        return familyMapper.selectList(query);
    }

    public void updateDetailHead(int familyId) {
        UpdateWrapper<FamilyData> updateWrapper = Wrappers.update();
        updateWrapper.lambda()
                .eq(FamilyData::getId, familyId)
                .set(FamilyData::getHead, FAMILY_DETAIL_HEAD);
        familyMapper.update(null, updateWrapper);
    }

    public void updateManager(int familyId, String manager) {
        UpdateWrapper<FamilyData> updateWrapper = Wrappers.update();
        updateWrapper.lambda()
                .eq(FamilyData::getId, familyId)
                .set(FamilyData::getManager, manager);
        familyMapper.update(null, updateWrapper);
    }

    public void updateManager(String oldManager, String newManager) {
        UpdateWrapper<FamilyData> updateWrapper = Wrappers.update();
        updateWrapper.lambda()
                .eq(FamilyData::getManager, oldManager)
                .set(FamilyData::getManager, newManager);
        familyMapper.update(null, updateWrapper);
    }

    public void updateSuperManager(int familyId, String superManager) {
        UpdateWrapper<FamilyData> updateWrapper = Wrappers.update();
        updateWrapper.lambda()
                .eq(FamilyData::getId, familyId)
                .set(FamilyData::getSuperManager, superManager);
        familyMapper.update(null, updateWrapper);
    }

    public void updateSuperManager(String oldSuperManager, String newSuperManager) {
        UpdateWrapper<FamilyData> updateWrapper = Wrappers.update();
        updateWrapper.lambda()
                .eq(FamilyData::getSuperManager, oldSuperManager)
                .set(FamilyData::getSuperManager, newSuperManager);
        familyMapper.update(null, updateWrapper);
    }

    public void updatePid(int familyId, int pid, int cascadeLevel) {
        UpdateWrapper<FamilyData> updateWrapper = Wrappers.update();
        updateWrapper.lambda()
                .eq(FamilyData::getId, familyId)
                .set(FamilyData::getPid, pid)
                .set(FamilyData::getCascadeLevel, cascadeLevel)
                .set(FamilyData::getBecomeAgentTime, pid == 0 ? 0 : DateHelper.getNowSeconds());
        familyMapper.update(null, updateWrapper);
    }

    public List<FamilyData> selectByManager(String manager) {
        QueryWrapper<FamilyData> query = Wrappers.query();
        query.lambda().eq(FamilyData::getStatus, 1).eq(FamilyData::getManager, manager);
        return familyMapper.selectList(query);
    }

    public List<FamilyData> selectByFamilyIds(Collection<Integer> familyIds) {
        if (CollectionUtils.isEmpty(familyIds)) {
            return Collections.emptyList();
        }
        QueryWrapper<FamilyData> query = Wrappers.query();
        query.lambda().eq(FamilyData::getStatus, 1).in(FamilyData::getId, familyIds);
        return familyMapper.selectList(query);
    }

    public List<String> getOwnerUidsByFamilyIdSet(Set<Integer> agentFamilyIdSet) {
        QueryWrapper<FamilyData> query = Wrappers.query();
        query.lambda().select(FamilyData::getOwnerUid).eq(FamilyData::getStatus, 1).in(FamilyData::getId, agentFamilyIdSet);
        return CollectionUtil.getPropertyList(familyMapper.selectList(query), FamilyData::getOwnerUid, "");
    }

    public List<FamilyData> selectPageByFamilyIds(Set<Integer> agentFamilyIdSet, int page, int pageSize) {
        if (CollectionUtils.isEmpty(agentFamilyIdSet)) {
            return Collections.emptyList();
        }
        IPage<FamilyData> dataPage = new Page<>(page, pageSize);
        QueryWrapper<FamilyData> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("status", 1);
        queryWrapper.in("id", agentFamilyIdSet);
        queryWrapper.orderByDesc("ctime");
        return familyMapper.selectPage(dataPage, queryWrapper).getRecords();
    }

    public List<FamilyData> selectSubAgentPage(int pid, int page, int pageSize, boolean hasBannedData) {
        IPage<FamilyData> dataPage = new Page<>(page, pageSize);
        QueryWrapper<FamilyData> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("pid", pid);
        if (!hasBannedData) {
            queryWrapper.eq("status", 1);
        }
        queryWrapper.orderByAsc("status");
        queryWrapper.orderByDesc("ctime");
        return familyMapper.selectPage(dataPage, queryWrapper).getRecords();
    }

    public int selectSubAgentCount(int pid, boolean hasBannedData) {
        QueryWrapper<FamilyData> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("pid", pid);
        if (!hasBannedData) {
            queryWrapper.eq("status", 1);
        }
        return familyMapper.selectCount(queryWrapper).intValue();
    }

    public FamilyData selectByOwnerUid(String ownerUid) {
        QueryWrapper<FamilyData> query = Wrappers.query();
        query.lambda().eq(FamilyData::getStatus, 1).eq(FamilyData::getOwnerUid, ownerUid);
        return familyMapper.selectOne(query);
    }

    public void addBanCreateFamilyUser(String aid) {
        String key = getBanCreateFamilyUserKey();
        try {
            redisTemplate.opsForSet().add(key, aid);
            redisTemplate.expire(key, 180, TimeUnit.DAYS);
        } catch (Exception e) {
            logger.info("addBanCreateFamilyUser error. aid={} msg={}", aid, e.getMessage());
        }
    }

    public boolean isBannedCreateFamily(String aid) {
        try {
            return Boolean.TRUE.equals(redisTemplate.opsForSet().isMember(getBanCreateFamilyUserKey(), aid));
        } catch (Exception e) {
            logger.info("isBannedCreateFamily error. aid={} msg={}", aid, e.getMessage());
            return false;
        }
    }

    public void removeBanCreateFamilyUser(String aid) {
        try {
            redisTemplate.opsForSet().remove(getBanCreateFamilyUserKey(), aid);
        } catch (Exception e) {
            logger.info("removeBanCreateFamilyUser error. aid={} msg={}", aid, e.getMessage());
        }
    }

    private String getBanCreateFamilyUserKey() {
        return "set:banCreateFamilyUser";
    }
    /**
     * 根据公会长id查询所有公会信息
     * @param ownerUid
     * @return java.util.List<com.quhong.mysql.data.FamilyData>
     * @description TODO
     */

    public List<FamilyData> selectListByOwnerUid(String ownerUid) {
        QueryWrapper<FamilyData> query = Wrappers.query();
        query.lambda().eq(FamilyData::getOwnerUid, ownerUid);
        return familyMapper.selectList(query);
    }

}
