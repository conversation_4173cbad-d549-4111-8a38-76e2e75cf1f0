package com.quhong.mysql.dao;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.UpdateWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.quhong.config.CaffeineCacheConfig;
import com.quhong.core.utils.DateHelper;
import com.quhong.mysql.data.RoomEventData;
import com.quhong.mysql.mapper.waho_log.RoomEventMapper;
import jakarta.annotation.Resource;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import java.util.Arrays;
import java.util.List;
import java.util.Set;
import java.util.concurrent.TimeUnit;

/**
 * <AUTHOR>
 * @date 2022/12/6
 */
@Component
public class RoomEventDao {

    public static final String ROOM_EVENT_LOCK_KEY = "room_event_";
    public static final int COST_BEAN = 1000;

    @Resource
    private RoomEventMapper roomEventMapper;

    public void insert(RoomEventData data) {
        roomEventMapper.insert(data);
    }

    public List<RoomEventData> getRoomEventList(int nowTime, Set<String> testUserList) {
        QueryWrapper<RoomEventData> queryWrapper = new QueryWrapper<>();
        if (!CollectionUtils.isEmpty(testUserList)) {
            queryWrapper.notIn("room_id", testUserList);
        }
        queryWrapper.eq("status", 1);
        queryWrapper.gt("end_time", nowTime);
        queryWrapper.orderByAsc("start_time");
        return roomEventMapper.selectList(queryWrapper);
    }

    public RoomEventData selectById(Integer eventId) {
        return roomEventMapper.selectById(eventId);
    }

    public List<RoomEventData> getPastRoomEvent(String roomId, int nowTime, int page, int pageSize) {
        IPage<RoomEventData> dataPage = new Page<>(page, pageSize);
        QueryWrapper<RoomEventData> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("room_id", roomId);
        queryWrapper.lt("end_time", nowTime);
        queryWrapper.orderByDesc("start_time");
        dataPage = roomEventMapper.selectPage(dataPage, queryWrapper);
        return dataPage.getRecords();
    }

    public List<RoomEventData> getRoomEventRecord(String roomId, int tabType, int nowTime, int page, int pageSize) {
        IPage<RoomEventData> dataPage = new Page<>(page, pageSize);
        QueryWrapper<RoomEventData> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("room_id", roomId);
        switch (tabType) {
            case 1 -> {
                // 审核通过（审核通过且未开始）
                queryWrapper.gt("start_time", nowTime);
                queryWrapper.eq("status", 1);
            }
            case 2 -> {
                // 审核中
                queryWrapper.eq("status", 0);
            }
            case 3 -> {
                // 已拒绝
                queryWrapper.eq("status", 2);
            }
            case 4 -> {
                // 已结束（审核通过且已结束）
                queryWrapper.lt("end_time", nowTime);
                queryWrapper.eq("status", 1);
            }
            default -> {
            }
        }
        queryWrapper.orderByDesc("start_time");
        dataPage = roomEventMapper.selectPage(dataPage, queryWrapper);
        return dataPage.getRecords();
    }

    public List<RoomEventData> getNotEndRoomEvent(String roomId, int nowTime, Integer status, int page, int pageSize) {
        IPage<RoomEventData> dataPage = new Page<>(page, pageSize);
        QueryWrapper<RoomEventData> queryWrapper = new QueryWrapper<>();
        if (status != null) {
            queryWrapper.eq("status", status);
        }
        queryWrapper.eq("room_id", roomId);
        queryWrapper.gt("end_time", nowTime);
        queryWrapper.orderByAsc("start_time");
        dataPage = roomEventMapper.selectPage(dataPage, queryWrapper);
        return dataPage.getRecords();
    }

    public RoomEventData getOneNotEndRoomEvent(String roomId, int nowTime) {
        QueryWrapper<RoomEventData> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("room_id", roomId);
        queryWrapper.eq("status", 1);
        queryWrapper.gt("end_time", nowTime);
        queryWrapper.orderByAsc("start_time");
        queryWrapper.last("limit 1");
        return roomEventMapper.selectOne(queryWrapper);
    }

    public int getNotEndRoomEventNum(String roomId, int nowTime) {
        QueryWrapper<RoomEventData> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("room_id", roomId);
        queryWrapper.gt("end_time", nowTime);
        queryWrapper.eq("status", 1);
        Long value = roomEventMapper.selectCount(queryWrapper);
        return null == value ? 0 : value.intValue();
    }

    public void updateSubNum(RoomEventData data) {
        UpdateWrapper<RoomEventData> updateWrapper = new UpdateWrapper<>();
        updateWrapper.eq("id", data.getId());
        updateWrapper.set("sub_num", data.getSubNum());
        roomEventMapper.update(data, updateWrapper);
    }


    public List<RoomEventData> selectList(List<Integer> eventIds) {
        QueryWrapper<RoomEventData> queryWrapper = new QueryWrapper<>();
        queryWrapper.in("id", eventIds);
        queryWrapper.eq("status", 1);
        queryWrapper.orderByAsc("start_time");
        return roomEventMapper.selectList(queryWrapper);
    }

    public List<RoomEventData> getMyCreatedList(String uid, Integer status, int page, int pageSize) {
        IPage<RoomEventData> dataPage = new Page<>(page, pageSize);
        QueryWrapper<RoomEventData> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("creator", uid);
        if (status != null) {
            queryWrapper.eq("status", status);
        }
        queryWrapper.orderByDesc("start_time");
        dataPage = roomEventMapper.selectPage(dataPage, queryWrapper);
        return dataPage.getRecords();
    }

    public List<RoomEventData> getRoomNotEndEvent(String roomId, int nowTime) {
        QueryWrapper<RoomEventData> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("room_id", roomId);
        queryWrapper.gt("end_time", nowTime);
        queryWrapper.orderByDesc("start_time");
        return roomEventMapper.selectList(queryWrapper);
    }

    @Cacheable(value = "cache", cacheManager = CaffeineCacheConfig.EXPIRE_15S_AFTER_WRITE,
            key = "#root.method.name+T(org.springframework.util.StringUtils).arrayToDelimitedString(args,'-')")
    public RoomEventData getOngoingRoomEvent(String roomId) {
        int nowTime = DateHelper.getNowSeconds();
        QueryWrapper<RoomEventData> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("room_id", roomId);
        queryWrapper.le("start_time", nowTime + 15);
        queryWrapper.ge("end_time", nowTime);
        queryWrapper.eq("status", 1);
        queryWrapper.last("limit 1");
        return roomEventMapper.selectOne(queryWrapper);
    }

    public List<RoomEventData> getNotStartRoomEvent(int nowTime) {
        QueryWrapper<RoomEventData> queryWrapper = new QueryWrapper<>();
        queryWrapper.gt("start_time", nowTime);
        queryWrapper.eq("status", 1);
        return roomEventMapper.selectList(queryWrapper);
    }

    public void delete(int eventId) {
        roomEventMapper.deleteById(eventId);
    }

    public List<RoomEventData> getHasEndedEventList(int startTime, int endTime) {
        QueryWrapper<RoomEventData> queryWrapper = new QueryWrapper<>();
        queryWrapper.ge("end_time", startTime);
        queryWrapper.lt("end_time", endTime);
        return roomEventMapper.selectList(queryWrapper);
    }

    public List<RoomEventData> getNotEndEventList(int page, int pageSize) {
        IPage<RoomEventData> dataPage = new Page<>(page, pageSize);
        QueryWrapper<RoomEventData> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("status", 1);
        queryWrapper.gt("end_time", DateHelper.getNowSeconds());
        queryWrapper.orderByAsc("start_time");
        dataPage = roomEventMapper.selectPage(dataPage, queryWrapper);
        return dataPage.getRecords();
    }

    public void update(RoomEventData data) {
        UpdateWrapper<RoomEventData> updateWrapper = new UpdateWrapper<>();
        updateWrapper.eq("id", data.getId());
        updateWrapper.set("name", data.getName());
        updateWrapper.set("description", data.getDescription());
        updateWrapper.set("type", data.getType());
        updateWrapper.set("duration", data.getDuration());
        updateWrapper.set("start_time", data.getStartTime());
        updateWrapper.set("end_time", data.getEndTime());
        updateWrapper.set("event_cover_url", data.getEventCoverUrl());
        updateWrapper.set("status", data.getStatus());
        roomEventMapper.update(data, updateWrapper);
    }

    public void updateStatus(RoomEventData data) {
        UpdateWrapper<RoomEventData> updateWrapper = new UpdateWrapper<>();
        updateWrapper.eq("id", data.getId());
        updateWrapper.set("status", data.getStatus());
        updateWrapper.set("review_reason", data.getReviewReason());
        updateWrapper.set("reviewer_uid", data.getReviewerUid());
        updateWrapper.set("mtime", DateHelper.getNowSeconds());
        roomEventMapper.update(data, updateWrapper);
    }

    public IPage<RoomEventData> selectPage(Integer status, int startTime, int endTime, String creator, String roomId, String reviewer, int start, int pageSize) {
        IPage<RoomEventData> dataPage = new Page<>(start, pageSize);
        QueryWrapper<RoomEventData> queryWrapper = new QueryWrapper<>();
        if (startTime != 0) {
            queryWrapper.ge("start_time", startTime);
        }
        if (endTime != 0) {
            queryWrapper.le("end_time", endTime);
        }
        if (StringUtils.hasLength(creator)) {
            queryWrapper.eq("creator", creator);
        }
        if (StringUtils.hasLength(roomId)) {
            queryWrapper.eq("room_id", roomId);
        }
        if (StringUtils.hasLength(reviewer)) {
            queryWrapper.eq("reviewer_uid", reviewer);
        }
        if (status != null) {
            if (status == -1) {
                queryWrapper.lt("end_time", DateHelper.getNowSeconds());
            } else {
                queryWrapper.eq("status", status);
            }
        }
        queryWrapper.orderByDesc("mtime");
        return roomEventMapper.selectPage(dataPage, queryWrapper);
    }

    public List<RoomEventData> getNeedRefundFeeList() {
        QueryWrapper<RoomEventData> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("status", 2);
        queryWrapper.le("start_time", DateHelper.getNowSeconds());
        queryWrapper.eq("refund_beans", 0);
        return roomEventMapper.selectList(queryWrapper);
    }

    public void updateRefundBeans(RoomEventData data) {
        UpdateWrapper<RoomEventData> updateWrapper = new UpdateWrapper<>();
        updateWrapper.eq("id", data.getId());
        updateWrapper.set("refund_beans", data.getRefundBeans());
        roomEventMapper.update(data, updateWrapper);
    }

    public List<RoomEventData> getNeedReviewList() {
        QueryWrapper<RoomEventData> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("status", 0);
        queryWrapper.le("start_time", DateHelper.getNowSeconds() - (int) TimeUnit.MINUTES.toSeconds(10));
        return roomEventMapper.selectList(queryWrapper);
    }
}
