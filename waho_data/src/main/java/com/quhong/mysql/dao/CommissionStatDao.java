package com.quhong.mysql.dao;

import com.baomidou.mybatisplus.core.toolkit.CollectionUtils;
import com.quhong.core.date.DateSupport;
import com.quhong.mysql.data.CommissionStatData;
import com.quhong.mysql.mapper.waho_log.CommissionStatMapper;
import com.quhong.redis.DataRedisBean;
import jakarta.annotation.Resource;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.context.annotation.Lazy;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.stereotype.Component;

import java.math.BigDecimal;
import java.util.*;
import java.util.concurrent.TimeUnit;

@Component
@Lazy
public class CommissionStatDao {

    private static final Logger logger = LoggerFactory.getLogger(CommissionStatDao.class);

    private static final Set<String> HAS_EXPIRE_TIME_KEY_SET = new HashSet<>();

    @Resource
    private CommissionStatMapper commissionStatMapper;
    @Resource(name = DataRedisBean.MAIN_CLUSTER)
    private StringRedisTemplate clusterRedis;

    public void batchInsert(List<CommissionStatData> dataList) {
        try {
            if (CollectionUtils.isEmpty(dataList)) {
                return;
            }
            commissionStatMapper.batchInsert(dataList);
        } catch (Exception e) {
            logger.error("batchInsert error. {}", e.getMessage(), e);
        }
    }

    public int getCommissionStat(String uid, String aid, int logType, int startTime, int endTime) {
        return commissionStatMapper.getCommissionStat(uid, aid, logType, startTime, endTime);
    }

    public void addCommissionInRedis(String uid, String aid, int logType, BigDecimal value) {
        String strDate = DateSupport.ARABIAN.getStrToday();
        String key = getCommissionStatKey(uid, logType, strDate);
        try {
            clusterRedis.opsForHash().increment(key, aid, value.doubleValue());
            if (!HAS_EXPIRE_TIME_KEY_SET.contains(key)) {
                String hasCommissionUserKey = getHasCommissionUserKey(logType, strDate);
                clusterRedis.expire(key, 3, TimeUnit.DAYS);
                clusterRedis.opsForSet().add(hasCommissionUserKey, uid);
                clusterRedis.expire(hasCommissionUserKey, 3, TimeUnit.DAYS);
                HAS_EXPIRE_TIME_KEY_SET.add(key);
            }
        } catch (Exception e) {
            logger.error("addCommissionInRedis error. logType={} uid={} aid={} value={} {}", logType, uid, aid, value, e.getMessage(), e);
        }
    }

    public long getCommissionFromRedis(String uid, String strDate, int logType) {
        return (long) getCommissionMapFromRedis(uid, strDate, logType).values().stream().mapToDouble(Double::doubleValue).sum();
    }

    public Map<String, Double> getCommissionMapFromRedis(String uid, String strDate, int logType) {
        String key = getCommissionStatKey(uid, logType, strDate);
        try {
            Map<Object, Object> entries = clusterRedis.opsForHash().entries(key);
            Map<String, Double> resultMap = new HashMap<>();
            for (Map.Entry<Object, Object> entry : entries.entrySet()) {
                resultMap.put(entry.getKey() + "", Double.parseDouble(String.valueOf(entry.getValue())));
            }
            return resultMap;
        } catch (Exception e) {
            logger.error("getCommissionMapFromRedis error. logType={} uid={} {}", logType, uid, e.getMessage(), e);
            return Collections.emptyMap();
        }
    }

    private String getCommissionStatKey(String uid, int logType, String strDate) {
        return "hash:commissionStat:" + strDate + ":" + logType + ":" + uid;
    }

    public Set<String> getHasCommissionUser(String strDate, int logType) {
        String key = getHasCommissionUserKey(logType, strDate);
        try {
            return clusterRedis.opsForSet().members(key);
        } catch (Exception e) {
            logger.error("getHasCommissionUser error. logType={} strDate={} {}", logType, strDate, e.getMessage(), e);
            return Collections.emptySet();
        }
    }

    private String getHasCommissionUserKey(int logType, String strDate) {
        return "set:hasCommissionUser:" + strDate + ":" + logType;
    }
}
