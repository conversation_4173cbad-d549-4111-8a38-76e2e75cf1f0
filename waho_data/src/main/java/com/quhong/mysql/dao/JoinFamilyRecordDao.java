package com.quhong.mysql.dao;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.quhong.analysis.EventDTO;
import com.quhong.analysis.EventReport;
import com.quhong.analysis.JoinFamilyRecordEvent;
import com.quhong.config.CaffeineCacheConfig;
import com.quhong.core.utils.DateHelper;
import com.quhong.mysql.data.JoinFamilyRecordData;
import com.quhong.mysql.mapper.waho.JoinFamilyRecordMapper;
import com.quhong.utils.CollectionUtil;
import jakarta.annotation.Resource;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Set;
import java.util.concurrent.TimeUnit;

/**
 * <AUTHOR>
 * @date 2023/7/24
 */
@Component
public class JoinFamilyRecordDao {

    @Resource
    private JoinFamilyRecordMapper joinFamilyRecordMapper;
    @Autowired(required = false)
    private EventReport eventReport;

    public void insert(JoinFamilyRecordData data) {
        joinFamilyRecordMapper.insert(data);
        JoinFamilyRecordEvent event = new JoinFamilyRecordEvent();
        event.setUid(data.getUid());
        event.setDevice_id(data.getDeviceId());
        event.setFamily_id(data.getFamilyId());
        event.setJoin_family_action(data.getAction());
        event.setCtime(data.getCtime());
        eventReport.track(new EventDTO(event));
    }

    @Cacheable(value = "cache", cacheManager = CaffeineCacheConfig.EXPIRE_10M_AFTER_WRITE,
            key = "#root.method.name+T(org.springframework.util.StringUtils).arrayToDelimitedString(args,'-')")
    public int getFirstJoinFamilyTime(String deviceId) {
        QueryWrapper<JoinFamilyRecordData> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("device_id", deviceId);
        queryWrapper.eq("action", 0);
        queryWrapper.orderByAsc("ctime");
        queryWrapper.last("limit 1");
        JoinFamilyRecordData data = joinFamilyRecordMapper.selectOne(queryWrapper);
        return data != null ? data.getCtime() : 0;
    }

    public List<JoinFamilyRecordData> selectByFamilyIdAndAction(Integer familyId, int action, int ctime) {
        QueryWrapper<JoinFamilyRecordData> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("family_id", familyId);
        queryWrapper.eq("action", action);
        queryWrapper.ge("ctime", ctime);
        return joinFamilyRecordMapper.selectList(queryWrapper);
    }

    @Cacheable(value = "cache", cacheManager = CaffeineCacheConfig.EXPIRE_10M_AFTER_WRITE,
            key = "#root.method.name+T(org.springframework.util.StringUtils).arrayToDelimitedString(args,'-')")
    public int getJoinFamilyTime(int familyId, String uid) {
        QueryWrapper<JoinFamilyRecordData> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("family_id", familyId);
        queryWrapper.eq("uid", uid);
        queryWrapper.eq("action", 0);
        queryWrapper.orderByDesc("ctime");
        queryWrapper.last("limit 1");
        JoinFamilyRecordData data = joinFamilyRecordMapper.selectOne(queryWrapper);
        return data != null ? data.getCtime() : 0;
    }

    /**
     * @param familyId 公会id
     * @param uid 用户id
     * @param ctime 退出公会时间
     * @return int
     */
    @Cacheable(value = "cache", cacheManager = CaffeineCacheConfig.EXPIRE_10M_AFTER_WRITE,
            key = "#root.method.name+T(org.springframework.util.StringUtils).arrayToDelimitedString(args,'-')")
    public int getJoinFamilyTime(int familyId, String uid, int ctime) {
        QueryWrapper<JoinFamilyRecordData> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("family_id", familyId);
        queryWrapper.eq("uid", uid);
        queryWrapper.eq("action", 0);
        queryWrapper.lt("ctime", ctime);
        queryWrapper.orderByDesc("ctime");
        queryWrapper.last("limit 1");
        JoinFamilyRecordData data = joinFamilyRecordMapper.selectOne(queryWrapper);
        return data != null ? data.getCtime() : 0;
    }

    @Cacheable(value = "cache", cacheManager = CaffeineCacheConfig.EXPIRE_10M_AFTER_WRITE,
            key = "#root.method.name+T(org.springframework.util.StringUtils).arrayToDelimitedString(args,'-')")
    public int getQuitFamilyTime(int familyId, String uid) {
        QueryWrapper<JoinFamilyRecordData> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("family_id", familyId);
        queryWrapper.eq("uid", uid);
        queryWrapper.eq("action", 1);
        queryWrapper.orderByDesc("ctime");
        queryWrapper.last("limit 1");
        JoinFamilyRecordData data = joinFamilyRecordMapper.selectOne(queryWrapper);
        return data != null ? data.getCtime() : 0;
    }

    public Set<String> selectQuitFamilySet(int startTime) {
        QueryWrapper<JoinFamilyRecordData> queryWrapper = new QueryWrapper<>();
        queryWrapper.select("uid");
        queryWrapper.eq("action", 1);
        queryWrapper.ge("ctime", Math.max(startTime, DateHelper.getNowSeconds() - TimeUnit.DAYS.toSeconds(365)));
        List<JoinFamilyRecordData> dataList = joinFamilyRecordMapper.selectList(queryWrapper);
        return CollectionUtil.listToPropertySet(dataList, JoinFamilyRecordData::getUid);
    }

    public int getQuitFamilyCount(String uid, int startTime) {
        QueryWrapper<JoinFamilyRecordData> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("uid", uid);
        queryWrapper.eq("action", 1);
        queryWrapper.ge("ctime", Math.max(startTime, DateHelper.getNowSeconds() - TimeUnit.DAYS.toSeconds(365)));
        return joinFamilyRecordMapper.selectCount(queryWrapper).intValue();
    }

    /**
     * 分页查询公会记录数据
     *
     * @param page    分页参数，包含当前页码和每页大小
     * @param wrapper 查询条件包装器，用于构建复杂的查询条件
     * @return 返回符合查询条件的家族记录数据分页结果
     */
    public IPage<JoinFamilyRecordData> selectPage(Page<JoinFamilyRecordData> page, LambdaQueryWrapper<JoinFamilyRecordData> wrapper) {
        return joinFamilyRecordMapper.selectPage(page, wrapper);
    }
}
