package com.quhong.mysql.dao;

import com.quhong.core.distribution.DistributeLock;
import com.quhong.mysql.data.UserResourceData;
import com.quhong.mysql.mapper.waho_log.UserResourceMapper;
import jakarta.annotation.Resource;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import java.util.*;

/**
 * <AUTHOR>
 * @date 2023/4/23
 */
@Component
public class UserResourceDao {

    private final Logger logger = LoggerFactory.getLogger(getClass());

    private static final String TABLE_NAME = "t_user_resource";
    private static final int TABLE_NUM = 8;

    @Resource
    private UserResourceMapper userResourceMapper;

    public void insert(UserResourceData data) {
        userResourceMapper.insert(getTableName(data.getUid()), data);
    }

    public void insertMany(String uid, List<UserResourceData> dataList) {
        userResourceMapper.insertMany(getTableName(uid), dataList);
    }

    public UserResourceData selectUserUsingResource(String uid, int resourceType) {
        return userResourceMapper.selectUserUsingResource(getTableName(uid), uid, resourceType);
    }

    public List<UserResourceData> selectUserUsingResourceList(String uid, int resourceType) {
        return userResourceMapper.selectUserUsingResourceList(getTableName(uid), uid, resourceType);
    }

    public List<UserResourceData> selectUserUsingAllResourceList(String uid) {
        return userResourceMapper.selectUserUsingAllResourceList(getTableName(uid), uid);
    }

    public UserResourceData selectUserResource(String uid, int resourceId) {
        return userResourceMapper.selectUserResource(getTableName(uid), uid, resourceId);
    }

    public List<UserResourceData> selectPage(String uid, int resourceType, int start, int pageSize) {
        return userResourceMapper.selectPage(getTableName(uid), uid, resourceType, start, pageSize);
    }

    public void updateStatus(String uid, int resourceId, int status) {
        userResourceMapper.updateStatus(getTableName(uid), uid, resourceId, status);
    }

    public void updateEndTime(String uid, int resourceId, int status, long endTime) {
        userResourceMapper.updateEndTime(getTableName(uid), uid, resourceId, status, endTime);
    }

    public void deleteUserResource(String uid, int resourceId) {
        userResourceMapper.deleteUserResource(getTableName(uid), uid, resourceId);
    }

    public List<UserResourceData> getExpiredResourceList(int resourceType, int startTime, int endTime) {
        List<UserResourceData> list = new ArrayList<>();
        for (int i = 0; i < TABLE_NUM; i++) {
            String tableName = TABLE_NAME + "_" + i;
            List<UserResourceData> resourceList = userResourceMapper.getExpiredResourceList(tableName, resourceType, startTime, endTime);
            if (!CollectionUtils.isEmpty(resourceList)) {
                list.addAll(resourceList);
            }
        }
        return list;
    }

    public List<UserResourceData> selectList(String uid, Integer resType, Integer resId, int start, int pageSize) {
        if (StringUtils.hasLength(uid)) {
            return userResourceMapper.selectListByUid(getTableName(uid), uid, resType, resId, start, pageSize);
        } else {
            return userResourceMapper.selectList(resType, resId, start, pageSize);
        }
    }

    public long selectCount(String uid, Integer resType, Integer resId) {
        if (StringUtils.hasLength(uid)) {
            return userResourceMapper.selectCountByUid(getTableName(uid), uid, resType, resId);
        } else {
            return userResourceMapper.selectCount(resType, resId);
        }
    }

    public void updateNumberById(String uid, int id, int num) {
        userResourceMapper.updateNumberById(getTableName(uid), id, num);
    }

    public int selectTotalByUidEndTime(String uid, Integer resType, Integer resId, Integer endTime) {
        Integer ret = userResourceMapper.selectTotalByUidEndTime(getTableName(uid), uid, resType, resId, endTime);
        return ret == null ? 0 : ret;
    }

    public List<UserResourceData> selectListByUidEndTime(String uid, Integer resType, Integer resId, Integer endTime) {
        return userResourceMapper.selectListByUidEndTime(getTableName(uid), uid, resType, resId, endTime);
    }

    public void deleteUserResourceByIdList(String uid, Collection<Integer> idList) {
        userResourceMapper.deleteUserResourceByIdList(getTableName(uid), idList);
    }

    public void deleteUserResourceById(String uid, int id) {
        userResourceMapper.deleteUserResourceById(getTableName(uid), id);
    }

    public int reduceResourceChange(String uid, Integer resType, Integer resId, int change, int endTime) {
        try (DistributeLock lock = new DistributeLock(getResourceChangeKey(uid, resType, resId))) {
            lock.lock();
            List<UserResourceData> resDataList = selectListByUidEndTime(uid, resType, resId, endTime);
            if (CollectionUtils.isEmpty(resDataList)) {
                logger.info("user didn't get any number of the res, uid={} resType={} resId={}", uid, resType, resId);
                return -1;
            }

            int totalNum = resDataList.stream().mapToInt(UserResourceData::getResourceNumber).sum();
            int leftNum = totalNum - Math.abs(change);
            if (leftNum < 0) {
                logger.info("user doesn't have enough res number, uid={} resType={} resId={}", uid, resType, resId);
                return -1;
            }

            Set<Integer> removeIdList = new HashSet<>();
            int leftCostNum = Math.abs(change);  // 代扣数量
            for (UserResourceData resourceData : resDataList) {
                int giftNum = resourceData.getResourceNumber();
                if (leftCostNum >= giftNum) {
                    leftCostNum = leftCostNum - giftNum;
                    removeIdList.add(resourceData.getId());
                } else {
                    updateNumberById(uid, resourceData.getId(), giftNum - leftCostNum);
                    leftCostNum = 0;
                }
                if (leftCostNum <= 0) {
                    break;
                }
            }
            if (!CollectionUtils.isEmpty(removeIdList)) {
                deleteUserResourceByIdList(uid, removeIdList);
            }
            return leftNum;
        } catch (Exception e) {
            logger.error("resource reduce error. uid={} resType={} resId={}  change={} error msg={}",
                    uid, resType, resId, change, e.getMessage(), e);
            return -2;
        }
    }

    /**
     * 获取分表名
     */
    private String getTableName(String uid) {
        int index = Integer.parseInt(uid.substring(uid.length() - 1), 16);
        if (index >= TABLE_NUM) {
            return TABLE_NAME + "_" + index % TABLE_NUM;
        }
        return TABLE_NAME + "_" + index;
    }

    private String getResourceChangeKey(String uid, int resType, int resId) {
        return String.format("reduce_change_res_%s_%d_%d", uid, resType, resId);
    }
}
