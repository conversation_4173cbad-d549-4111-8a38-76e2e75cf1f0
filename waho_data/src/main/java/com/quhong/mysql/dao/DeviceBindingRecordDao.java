package com.quhong.mysql.dao;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.quhong.mysql.data.DeviceBindingRecordData;
import com.quhong.mysql.mapper.waho_log.DeviceBindingRecordMapper;
import jakarta.annotation.Resource;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2024/3/1
 */
@Component
public class DeviceBindingRecordDao {

    @Resource
    private DeviceBindingRecordMapper deviceBindingRecordMapper;

    public void insert(DeviceBindingRecordData data) {
        deviceBindingRecordMapper.insert(data);
    }

    public List<DeviceBindingRecordData> selectList(String uid, int status) {
        QueryWrapper<DeviceBindingRecordData> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("uid", uid);
        queryWrapper.eq("status", status);
        return deviceBindingRecordMapper.selectList(queryWrapper);
    }

    public DeviceBindingRecordData selectOne(String uid, String tnId, int status) {
        QueryWrapper<DeviceBindingRecordData> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("uid", uid);
        queryWrapper.eq("tn_id", tnId);
        queryWrapper.eq("status", status);
        queryWrapper.last("limit 1");
        return deviceBindingRecordMapper.selectOne(queryWrapper);
    }

    public void update(DeviceBindingRecordData data) {
        deviceBindingRecordMapper.updateById(data);
    }
}
