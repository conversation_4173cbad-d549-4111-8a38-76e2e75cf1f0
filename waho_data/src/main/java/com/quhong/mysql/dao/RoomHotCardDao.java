package com.quhong.mysql.dao;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.UpdateWrapper;
import com.quhong.core.utils.DateHelper;
import com.quhong.mysql.data.RoomHotCardData;
import com.quhong.mysql.mapper.waho.RoomHotCardMapper;
import jakarta.annotation.Resource;
import org.springframework.stereotype.Component;

import java.util.List;

@Component
public class RoomHotCardDao {

    @Resource
    private RoomHotCardMapper roomHotCardMapper;

    public void insert(RoomHotCardData data) {
        roomHotCardMapper.insert(data);
    }

    public RoomHotCardData selectById(int cardId) {
        return roomHotCardMapper.selectById(cardId);
    }

    public void updateNum(int cardId, int num) {
        UpdateWrapper<RoomHotCardData> updateWrapper = new UpdateWrapper<>();
        updateWrapper.eq("id", cardId);
        updateWrapper.set("num", num);
        roomHotCardMapper.update(null, updateWrapper);
    }

    public void updateById(RoomHotCardData cardData) {
        roomHotCardMapper.updateById(cardData);
    }

    public List<RoomHotCardData> getExpiredNotupdateList() {
        QueryWrapper<RoomHotCardData> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("status", 0);
        queryWrapper.lt("end_time", DateHelper.getNowSeconds());
        return roomHotCardMapper.selectList(queryWrapper);
    }
}
