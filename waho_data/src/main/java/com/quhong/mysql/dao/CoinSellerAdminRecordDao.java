package com.quhong.mysql.dao;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.quhong.mysql.data.CoinSellerAdminRecordData;
import com.quhong.mysql.data.CoinSellerAdminRecordDetailData;
import com.quhong.mysql.mapper.waho.CoinSellerAdminRecordMapper;
import jakarta.annotation.Resource;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;

import java.util.List;

@Lazy
@Service
public class CoinSellerAdminRecordDao extends ServiceImpl<CoinSellerAdminRecordMapper, CoinSellerAdminRecordData> {

    private final Logger logger = LoggerFactory.getLogger(getClass());

    @Resource
    private CoinSellerAdminRecordMapper coinSellerAdminRecordMapper;

    public List<CoinSellerAdminRecordDetailData> selectDetailList(String uid, Integer merchantType, Integer rechargeType, String payMethod, Integer startTime, Integer endTime, int offset, int pageSize) {
        return coinSellerAdminRecordMapper.selectDetailList(uid, merchantType, rechargeType, payMethod, startTime, endTime, offset, pageSize);
    }

    public int selectDetailCount(String uid, Integer merchantType, Integer rechargeType, String payMethod, Integer startTime, Integer endTime) {
        return coinSellerAdminRecordMapper.selectDetailCount(uid, merchantType, rechargeType, payMethod, startTime, endTime);
    }
}
