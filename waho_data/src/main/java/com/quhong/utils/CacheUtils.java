package com.quhong.utils;

import com.github.benmanes.caffeine.cache.Cache;
import com.github.benmanes.caffeine.cache.Caffeine;
import com.quhong.core.utils.SpringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.cache.caffeine.CaffeineCacheManager;

import java.util.concurrent.TimeUnit;

public class CacheUtils {
    private static final String CACHE_NAME = "CacheUtils";

    private static final Logger logger = LoggerFactory.getLogger(CacheUtils.class);

    private static final Cache<String, Object> caffeineCache = Caffeine.newBuilder()
            // 设置最后一次写入或访问后经过固定时间过期
            .expireAfterAccess(30, TimeUnit.MINUTES)
            // 初始的缓存空间大小
            .initialCapacity(100)
            // 缓存的最大条数
            .maximumSize(50000)
            .build();

    public static <T> T get(String key) {
        try {
            // noinspection unchecked
            return (T) caffeineCache.getIfPresent(key);
        } catch (Exception e) {
            logger.error("get from cache error. key={} {}", key, e.getMessage());
            return null;
        }
    }

    public static boolean hasKey(String key) {
        return get(key) != null;
    }

    public static void put(String key, Object value) {
        if (null == value) {
            return;
        }
        caffeineCache.put(key, value);
    }

    public static void remove(String key) {
        caffeineCache.invalidate(key);
    }

    private static Cache<Object, Object> getCacheByName(String cacheManagerName) {
        try {
            CaffeineCacheManager cacheManager = (CaffeineCacheManager) SpringUtils.getBean(cacheManagerName);
            org.springframework.cache.Cache cache = cacheManager.getCache(CACHE_NAME);
            if (null == cache) {
                return null;
            }
            // noinspection unchecked
            return (Cache<Object, Object>) cache.getNativeCache();
        } catch (Exception e) {
            logger.error("cannot find bean. name={} {}", cacheManagerName, e.getMessage());
            return null;
        }
    }

    public static <T> T get(String key, String cacheManagerName) {
        try {
            Cache<Object, Object> cache = getCacheByName(cacheManagerName);
            if (null == cache) {
                return null;
            }
            // noinspection unchecked
            return (T) cache.getIfPresent(key);
        } catch (Exception e) {
            logger.error("get from cache error. key={} cacheManagerName{} {}", key, cacheManagerName, e.getMessage());
            return null;
        }
    }

    public static <T> T getOrDefault(String key, T defaultValue) {
        try {
            T value = get(key);
            if (null == value) {
                put(key, defaultValue);
                return defaultValue;
            }
            return value;
        } catch (Exception e) {
            logger.error("get or default from cache error. key={} {}", key, e.getMessage());
            return defaultValue;
        }
    }

    public static boolean hasKey(String key, String cacheManagerName) {
        Cache<Object, Object> cache = getCacheByName(cacheManagerName);
        if (null == cache) {
            return false;
        }
        return cache.asMap().containsKey(key);
    }

    public static void put(String key, Object value, String cacheManagerName) {
        if (null == value) {
            return;
        }
        Cache<Object, Object> cache = getCacheByName(cacheManagerName);
        if (null == cache) {
            return;
        }
        cache.put(key, value);
    }

    public static void remove(String key, String cacheManagerName) {
        Cache<Object, Object> cache = getCacheByName(cacheManagerName);
        if (null == cache) {
            return;
        }
        cache.invalidate(key);
    }
}
