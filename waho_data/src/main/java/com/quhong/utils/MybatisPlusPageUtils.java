package com.quhong.utils;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.quhong.vo.H5PageVO;

import java.util.function.Function;
import java.util.stream.Collectors;

public class MybatisPlusPageUtils {

    /**
     * 通用分页方法，page为零时返回所有(1w条记录)
     */
    public static <T> H5PageVO<T> getPage(BaseMapper<T> baseMapper, QueryWrapper<T> queryWrapper, int page, int pageSize) {
        H5PageVO<T> vo = new H5PageVO<>();
        IPage<T> iPage = new Page<>(page <= 0 ? 1 : page, page <= 0 ? 10000 : pageSize);
        iPage = baseMapper.selectPage(iPage, queryWrapper);
        vo.setList(iPage.getRecords());
        vo.setPages(iPage.getPages());
        vo.setTotal(iPage.getTotal());
        return vo;
    }

    /**
     * 通用分页方法，page为零时返回所有(1w条记录)
     */
    public static <T, V> H5PageVO<V> getVOPage(BaseMapper<T> baseMapper, QueryWrapper<T> queryWrapper, int page, int pageSize, Function<T, V> converter) {
        H5PageVO<V> vo = new H5PageVO<>();
        IPage<T> iPage = new Page<>(page <= 0 ? 1 : page, page <= 0 ? 10000 : pageSize);
        iPage = baseMapper.selectPage(iPage, queryWrapper);
        // 转换vo
        vo.setList(iPage.getRecords().stream().map(converter).collect(Collectors.toList()));
        vo.setPages(iPage.getPages());
        vo.setTotal(iPage.getTotal());
        return vo;
    }
}
