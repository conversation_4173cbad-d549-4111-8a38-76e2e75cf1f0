package com.quhong.utils;

import com.quhong.core.utils.DateHelper;

import java.math.BigDecimal;
import java.math.RoundingMode;

public class MatchUtils {
    public static String generateLikeIndex(String id1, String id2) {
        if (id1.compareTo(id2) > 0) {
            return id2 + "#" + id1;
        } else {
            return id1 + "#" + id2;
        }
    }

    /**
     * 格式化贡献数值
     */
    public static String formatDevotes(long devotes) {
        return formatDevotes(devotes, RoundingMode.HALF_UP);
    }

    /**
     * 格式化贡献数值
     */
    public static String formatDevotes(long devotes, RoundingMode mode) {
        if (devotes >= 1000000) {
            return new BigDecimal(devotes / 1000000f + "").setScale(1, mode) + "M";
        } else if (devotes >= 1000) {
            return new BigDecimal(devotes / 1000f + "").setScale(1, mode) + "K";
        } else {
            return devotes + "";
        }
    }

    public static String formatK(long devotes) {
        return formatK(devotes, 1);
    }

    public static String formatK(long devotes, int scale) {
        if (devotes >= 1000) {
            return new BigDecimal(devotes / 1000f + "").setScale(scale, RoundingMode.HALF_UP) + "K";
        } else {
            return devotes + "";
        }
    }

    public static String formatDevotes(long devotes, int scale, RoundingMode mode) {
        if (devotes >= 1000000) {
            return new BigDecimal(devotes / 1000000f + "").setScale(scale, mode) + "M";
        } else if (devotes >= 1000) {
            return new BigDecimal(devotes / 1000f + "").setScale(scale, mode) + "K";
        } else {
            return devotes + "";
        }
    }


    public static int getStepFromDayStart(int stepLength) {
        if (stepLength != 0) {
            long startTimeSec = DateHelper.DEFAULT.getTodayStartTime() / 1000;
            int nowSec = (int) (DateHelper.getNowSeconds() - startTimeSec);
            return nowSec / stepLength + 1;
        }
        return 0;
    }


    public static int getPreStepByStep(int step, int stepLength) {
        if (stepLength == 0) {
            return --step;
        } else {
            int maxStep = 86400 / stepLength;
            if (step == 1) {
                return maxStep;
            } else {
                return --step;
            }
        }
    }
}
