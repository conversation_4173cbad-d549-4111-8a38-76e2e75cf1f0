package com.quhong.net.sender;

import com.quhong.core.msg.ServerMsg;
import com.quhong.msg.MarsCacheMsg;
import com.quhong.msg.MarsServerMsg;

public interface MsgSender {

    default boolean sendMsg(String toUid, MarsServerMsg msg){
        return sendMsg(toUid, msg, false);
    }

    /**
     * 发送消息，消息无回报，继续重发
     * @param toUid
     * @param msg
     */
    default boolean sendMsgRepeated(String toUid, MarsServerMsg msg){
        return sendMsg(toUid, msg, true);
    }

    boolean sendMsg(String toUid, MarsServerMsg msg, boolean repeated);

    boolean reSendMsg(String toUid, MarsCacheMsg msg);
}
