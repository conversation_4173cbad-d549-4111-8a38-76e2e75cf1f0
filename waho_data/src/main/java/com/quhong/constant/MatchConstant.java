package com.quhong.constant;

public class MatchConstant {

    public static final int FRIEND = 0; // 已成为好友
    public static final int LIKE = 1; // 喜欢
    public static final int DISLIKE = 2; // 不喜欢
    public static final int SUPER_LIKE = 3; // 超级喜欢

    public static final int MATCH_OPEN = 1; // 开启匹配
    public static final int MATCH_CLOSE = 2; // 关闭匹配

    public static final int NONE = 0; // 参数为空
    public static final int MALE = 1; // 男
    public static final int FEMALE = 2; // 女
    public static final int ALL = 3; // 全部

    public static final int LEVEL_ONLINE = 4; // 推荐等级：在线、在房间
    public static final int LEVEL_SAME_COUNTRY = 3; // 推荐等级：相同国家地区
    public static final int LEVEL_SAME_HOBBY = 2; // 推荐等级：有一个或多个相同兴趣爱好
    public static final int LEVEL_SAME_LANGUAGE = 1; // 推荐等级：相同语言的用户

    public static final int RECOMMEND_LIMIT = 10; // 一次推荐用户数数量
    public static final int RECOMMEND_SIZE = 15; // 一次推荐用户迭代数据的阈值，防止数据量过大造成反应过慢，数值越大越准确
    public static final int LIKE_RECOMMEND = 5; // 往推荐列表中增加对方喜欢我的用户数，和RECOMMEND_SIZE搭配

    public static final int BALANCE_TYPE_HEART = 0; //余额类型：心心
    public static final int BALANCE_TYPE_DIAMOND = 1; //余额类型：钻石

    public static final int SWIPE_COUNT_SWITCH_CLOSE = 0; //滑动次数限制开关：关闭
    public static final int SWIPE_COUNT_SWITCH_OPEN = 1; //滑动次数限制开关：开启

    public static final int SWIPE_FROM_LIKE_LIST_FALSE = 0; //不是来自like列表的滑动
    public static final int SWIPE_FROM_LIKE_LIST_TRUE = 1; //来自like列表的滑动

    public static final String GOT_LIKE_HIM = "Someone likes you, come find him!";
    public static final String GOT_LIKE_HIM_AR = "شخص ما أعجب بك، تعال وتعرف عليه!";

    public static final String GOT_LIKE_HER = "Someone likes you, come find her!";
    public static final String GOT_LIKE_HER_AR = "شخص ما أعجب بك، تعال وتعرف عليه!";

    public static final String MATCH_HIM = "Match successfully, come and see who he is!";
    public static final String MATCH_HIM_AR = "تم تطابق بنجاح ، تعال وانظر من هو!";

    public static final String MATCH_HER = "Matching successful!";
    public static final String MATCH_HER_AR = "مطابقة ناجحة!";

    public static final int STEP_LENGTH = 5 * 60; //步长时间间隔为5分钟

}
