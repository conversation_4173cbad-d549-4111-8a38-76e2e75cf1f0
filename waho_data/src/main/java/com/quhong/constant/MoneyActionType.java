package com.quhong.constant;

import java.util.Arrays;
import java.util.List;

public enum MoneyActionType {

    ONLINE_RECHARGE(1, "Online Recharge", "Recharge %s diamonds via %s.", "中台支付充值"),
    CALL_CHARGE(10, "", "", "Hotchat通话扣费"),
    CHARGE_VIP(100, "Charge Vip", "buy vip", "购买vip"),
    COIN_SELLER_RECHARGE(500, "Diamond Seller Recharge", "Recharge from diamond seller %s(%s)", "币商给用户充值"),
    USD_TO_DIAMONDS(501, "Convert to diamonds", "Converting to %s diamonds costs %s USD.", "美金账户兑换钻石"),
    LUCKY_GIFT(502, "lucky gift", "win %s times reward", "幸运礼物获得钻石"),
    GAME_KING_REWARDS(503, "game king rewards", "Game King rewards", "游戏王活动奖励"),
    REJECT_QUIT_FAMILY(504, "Reject quitting", "Reject quitting family request deduct", "拒绝主播退出公会"),
    USD_SELLER_RECHARGE(505, "Host Seller Recharge", "Recharged %s diamonds from user %s", "主播美金给用户或币商充值"),
    BUY_BG_UPLOAD_CARD(52, "buy background upload card", "buy background upload card", "购买资源"),
    ROOM_GATHERING(514, "Room Gathering", "", "召集广播"),

    WEEKLY_START(901, "Weekly Gifts Star Competition Win", "Top %d in the weekly gifts star competition win", "周星榜奖励"),
    REACHING_REWARD(902, "Reaching Reward", "In the Ranking Activity Reward", "冲榜活动奖励"),
    REACHING_REWARD_V2(902, "%s Reaching Reward", "In the Ranking Activity Reward", "冲榜活动奖励"),
    ACTIVITY_COMPETITION(905, "%s Competition Win", "Top %d in the %s Competition Win", "活动奖励"),
    ACTIVITY_SEND_RANK(905, "%s-send rank", "Top %d in the %s-send rank", "活动送礼榜奖励"),
    ACTIVITY_RECEIVE_RANK(905, "%s-receive rank", "Top %d in %s-receive rank", "活动收礼榜奖励"),

    LUCKY_CHEST_GAME_FEE(947, "Lucky Chest game fee", "Lucky Chest game fee", "Lucky Chest 游戏费用"),
    LUCKY_CHEST_GAME_REWARDS(948, "Lucky Chest game rewards", "Lucky Chest game rewards", "Lucky Chest 游戏奖励"),
    LAVA_SLOT_GAME_FEE(949, "Lava Slot game fee", "Lava Slot game fee", "Lava Slot 游戏费用"),
    LAVA_SLOT_GAME_REWARDS(950, "Lava Slot game rewards", "Lava Slot game rewards", "Lava Slot 游戏奖励"),
    LOTTERY_GAME_FEE(951, "Lottery game fee", "Lottery game fee", "Lottery 游戏费用"),
    LOTTERY_GAME_REWARDS(952, "Lottery game rewards", "Lottery game rewards", "Lottery 游戏奖励"),
    CRASH_GAME_FEE(953, "Crash game fee", "Crash game fee", "Crash 游戏费用"),
    CRASH_GAME_REWARDS(954, "Crash game rewards", "Crash game rewards", "Crash 游戏奖励"),
    FISHING_STAR_GAME_FEE(955, "Fishing Star game fee", "Fishing Star game fee", "Fishing Star 游戏费用"),
    FISHING_STAR_GAME_REWARDS(956, "Fishing Star game rewards", "Fishing Star game rewards", "Fishing Star 游戏奖励"),
    GREEDY_GAME_FEE(957, "Greedy game fee", "Greedy game fee", "Greedy 游戏费用"),
    GREEDY_GAME_REWARDS(958, "Greedy game rewards", "Greedy game rewards", "Greedy 游戏奖励"),
    LUCKY77_GAME_FEE(959, "Lucky77 game fee", "Lucky77 game fee", "Lucky77 游戏费用"),
    LUCKY77_GAME_REWARDS(960, "Lucky77 game rewards", "Lucky77 game rewards", "Lucky77 游戏奖励"),
    BOUNTY_RACING_GAME_FEE(961, "Bounty Racing game fee", "Bounty Racing game fee", "Bounty Reacing 游戏费用"),
    BOUNTY_RACING_GAME_REWARDS(962, "Bounty Racing game rewards", "Bounty Racing game rewards", "Bounty Reacing 游戏奖励"),
    CREATE_ROOM_EVENT(1000, "Create Room Event", "Create Room Event", "创建房间活动"),
    REFUND_CREATE_ROOM_EVENT_FEE(1001, "Refund Create Room Event Fee", "Refund Create Room Event Fee", "退还创建房间费用"),
    SEND_MSG_FEE(1002, "Send message", "Send message to %s", "发送私信消息费用"),

    ADMIN_REDUEC(2, "", "", "admin减钻"),
    REFUND(4, "refund", "refund from %s", "内购退款"),
    PLAY_TURNTABLE_GAME(40, "", "", "转盘游戏"),
    BUY_RIDE(51, "", "", "购买坐骑"),
    BUY_ROOM_LOCK(53, "", "", "购买房间锁"),
    REDUEC(101, "", "", "减钻"),
    ROOM_MEMBER_FEE(103, "", "", "房间会员费"),
    BUY_COINS(180, "", "", "钻石购买金币"),
    LUCKY_NUMBER(210, "", "", "发送幸运数字"),
    SEND_GIFT(301, "", "", "发礼物"),
    RECEIVE_GIFT(302, "", "", "收礼物"),
    SYSTEM_RECOVER(600, "", "", "操作失误恢复钻"),
    SEND_LUCKY_BOX(800, "", "", "发钻石红包"),
    GET_LUCKY_BOX(801, "", "", "收钻石红包"),
    RETURN_LUCKY_BOX(802, "", "", "钻石红包退回"),
    EGG_SMASHING_GAME_FEE(910 , "", "","砸蛋游戏费"),
    EGG_SMASHING_GAME_REWARDS(911 , "", "","砸蛋游戏奖励"),
    PLAY_FRUIT_GAME(920 , "", "","水果机游戏扣钻"),
    FRUIT_PRIZE(921 , "", "","水果机游戏钻石奖励"),
    PLAY_LUDO_GAME_FEE(923 , "", "","Ludo游戏入场费"),
    PLAY_LUDO_GAME_PRIZE(924 , "", "","Ludo游戏奖励"),
    RETURN_LUDO_GAME_FEE(925 , "", "","Ludo返还入场费"),
    PLAY_UMO_GAME_FEE(926 , "", "","UMO游戏入场费"),
    PLAY_UMO_GAME_PRIZE(927 , "", "","UMO游戏奖励"),
    RETURN_UMO_GAME_FEE(928 , "", "","UMO返还入场费"),
    PLAY_MONSTER_CRUSH_GAME_FEE(929 ,"", "","Play Monster Crush game fee"),
    PLAY_MONSTER_CRUSH_GAME_PRIZE(930 ,"", "","Play Monster Crush game prize"),
    RETURN_MONSTER_CRUSH_GAME_FEE(931 ,"", "","Return Monster Crush game fee"),
    PLAY_DOMINO_GAME_FEE(932 , "", "","Domino游戏入场费"),
    PLAY_DOMINO_GAME_PRIZE(933 , "", "","Domino游戏奖励"),
    RETURN_DOMINO_GAME_FEE(934 , "", "","Domino返还入场费"),
    NEW_GREEDY_GAME_FEE(966, "", "", "New Greedy游戏费用"),
    NEW_GREEDY_GAME_REWARDS(967, "", "", "New Greedy游戏奖励"),
    PLAY_LUCKY_DRAW(970, "", "", "Lucky Draw费用"),
    LUCKY_DRAW(971, "", "", "Lucky Draw奖励"),
    ANIMAL_PARTY_GAME_FEE(972, "", "", "Animal Party游戏费用"),
    ANIMAL_PARTY_GAME_REWARDS(973, "", "", "Animal Party游戏奖励"),
    BIG_BATTLE_GAME_FEE(974, "", "", "Big Battle游戏费用"),
    BIG_BATTLE_GAME_REWARDS(975, "", "", "Big Battle游戏奖励"),
    YUMMY_SLOT_GAME_FEE(976, "", "", "Yummy Slot游戏费用"),
    YUMMY_SLOT_GAME_REWARDS(977, "", "", "Yummy Slot游戏奖励"),
    NEW_CRASH_GAME_FEE(2060, "", "", "Crash游戏费用"),
    NEW_CRASH_GAME_REWARDS(2061, "", "", "Crash游戏奖励"),
    ALADDIN_SLOT_GAME_FEE(2070, "", "", "Aladdin Slot游戏费用"),
    ALADDIN_SLOT_GAME_REWARDS(2071, "", "", "Aladdin Slot游戏奖励"),
    ;

    public int actionType;
    public String title;
    public String desc;
    public String remark;

    MoneyActionType(int actionType, String title, String desc, String remark) {
        this.actionType = actionType;
        this.title = title;
        this.desc = desc;
        this.remark = remark;
    }

    public static String getRemarkByActType(int actionType) {
        for (MoneyActionType type : MoneyActionType.values()) {
            if (type.getActionType() == actionType) {
                return type.getRemark();
            }
        }
        return "";
    }

    public static List<MoneyActionType> getALl() {
        return Arrays.asList(MoneyActionType.values());
    }

    public int getActionType() {
        return actionType;
    }

    public void setActionType(int actionType) {
        this.actionType = actionType;
    }

    public String getTitle() {
        return title;
    }

    public void setTitle(String title) {
        this.title = title;
    }

    public String getDesc() {
        return desc;
    }

    public void setDesc(String desc) {
        this.desc = desc;
    }

    public String getRemark() {
        return remark;
    }

    public void setRemark(String remark) {
        this.remark = remark;
    }
}
