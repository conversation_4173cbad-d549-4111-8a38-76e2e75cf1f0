package com.quhong.constant;

import java.util.Map;

/**
 * 活动相关常量
 */
public class ActivityConstant {

    public static final int STATUS_INIT = 0; // 初始化
    public static final int STATUS_DONE = 1; // 已结束

    public static final int SEND_RANK = 1; // 发送榜
    public static final int RECEIVE_RANK = 2; // 接收榜
    public static final int ROOM_RANK = 3; // 房间榜
    public static final int CONQUER_RANK = 4; // 征服房间榜
    public static final int SEND_RECEIVE_RANK = 5; // 统计发送和接收榜
    public static final int FAMILY_SEND_RANK = 6; // 家族榜发送榜
    public static final int FAMILY_RECEIVE_RANK = 7; // 家族榜接收榜
    public static final int GAME_COST_RANK = 9; // 游戏消耗钻石榜
    public static final int GAME_WIN_RANK = 10; // 游戏胜场榜


    public static final int GIFT_NUM = 1; // 计算礼物数
    public static final int GIFT_DIAMONDS = 2; // 2计算钻石数

    public static final Map<Integer, String> RANKING_TYPE_MAP = Map.of(
            SEND_RANK, "发送榜",
            RECEIVE_RANK, "接收榜",
            ROOM_RANK, "房间榜",
            CONQUER_RANK, "征服房间榜",
            SEND_RECEIVE_RANK, "统计发送和接收榜",
            FAMILY_SEND_RANK, "家族榜发送榜",
            FAMILY_RECEIVE_RANK, "家族榜接收榜"
    );
}
