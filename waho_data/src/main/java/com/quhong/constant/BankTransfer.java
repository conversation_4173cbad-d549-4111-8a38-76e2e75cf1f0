package com.quhong.constant;

import java.util.HashMap;
import java.util.Map;

public class BankTransfer {

    public static final Map<String, String> MAP = new HashMap<>() {
        {
            put("AHLI_UNITED",  "Ahli United Bank");
            put("CITI_NA",  "Citi Bank N.A. Egypt");
            put("MID",  "MIDBANK");
            put("BANQUE_DU_CAIRE",  "Banque Du Caire");
            put("HSBC", "HSBC Bank Egypt S.A.E");
            put("AGRICOLE_CREDIT_EGYPT",    "Credit Agricole Egypt S.A.E");
            put("GULF", "Egyptian Gulf Bank");
            put("UNITED_EGYPT", "The United Bank");
            put("ALAHLI_QNB",   "Qatar National Bank Alahli");
            put("ARAB_PLC", "Arab Bank PLC");
            put("EMIRATES_NATIONAL_DUBAI",  "Emirates National Bank of Dubai");
            put("AL_AHLI_KUWAIT",   "Al Ahli Bank of Kuwait – Egypt");
            put("NATIONAL_KUWAIT",  "National Bank of Kuwait – Egypt");
            put("ARAB_CORPORATION", "Arab Banking Corporation - Egypt S.A.E");
            put("FIRST_ABU_DHABI",  "First Abu Dhabi Bank");
            put("ABU_DHABI_ISLAMIC",    "Abu Dhabi Islamic Bank – Egypt");
            put("COMMERCIAL_INTERNATIONAL", "Commercial International Bank - Egypt S.A.E");
            put("HOUSING_DEVELOPMENT",  "Housing And Development Bank");
            put("BANQUE_MISR",  "Banque Misr");
            put("ARAB_AFRICAN_INTERNATIONAL",   "Arab African International Bank");
            put("ARAB_LAND",    "Egyptian Arab Land Bank");
            put("EXPORT_DEVELOPMENT",   "Export Development Bank of Egypt");
            put("FAISAL_ISLAMIC",   "Faisal Islamic Bank of Egypt");
            put("BLOM", "Blom Bank");
            put("ABU_DHABI_COMMERCIAL", "Abu Dhabi Commercial Bank – Egypt");
            put("ALEX_BANK",    "Alex Bank Egypt");
            put("SOCIETE_ARABE_INTERNATIONALE", "Societe Arabe Internationale De Banque");
            put("NATIONAL_EGYPT",   "National Bank of Egypt");
            put("AL_BARAKA",    "Al Baraka Bank Egypt B.S.C.");
            put("POST",   "Egypt Post");
            put("NASSER_SOCIAL",    "Nasser Social Bank");
            put("INDUSTRIAL_DEVELOPMENT",   "Industrial Development Bank");
            put("SUEZ_CANAL",   "Suez Canal Bank");
            put("MASHREQ",  "Mashreq Bank");
            put("ARAB_INVESTMENT",  "Arab Investment Bank");
            put("AUDI", "Audi Bank");
            put("GASC", "General Authority For Supply Commodities");
            put("ARAB_INTERNATIONAL",   "Arab International Bank");
            put("AGRICULTURAL_EGYPT",   "Agricultural Bank of Egypt");
            put("NATIONAL_GREECE",  "National Bank of Greece");
            put("CENTRAL_EGYPT",    "Central Bank Of Egypt");
            put("ATTIJARIWAFA", "ATTIJARIWAFA BANK Egypt");
        }
    };

}
