package com.quhong.constant;


import java.util.Arrays;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

public class AchieveBadgeConstant {

    /**
     * 1: 签到勋章
     * 2: 火箭成就勋章
     * 3: 猜拳成就勋章
     * 4: 发礼物成就勋章
     * 5: 交友成就勋章
     * 6: ludo成就勋章
     * 7: umo成就勋章
     * 8: 动态之星成就勋章
     */

    public static final Integer TYPE_SING = 1;
    public static final Integer TYPE_ROCKET = 2;
    public static final Integer TYPE_GUESS = 3;
    public static final Integer TYPE_SEND_GIFT = 4;
    public static final Integer TYPE_FRIEND = 5;
    public static final Integer TYPE_LUDO = 6;
    public static final Integer TYPE_UMO = 7;
    public static final Integer TYPE_MOMENT = 8;

    public static final List<Integer> TYPE_ACTOR_LIST = Arrays.asList(TYPE_ROCKET, TYPE_GUESS, TYPE_SEND_GIFT, TYPE_LUDO, TYPE_UMO, TYPE_MOMENT);

    public static Map<Integer, String> TYPE_COUNT_MAP = new HashMap<Integer, String>(){
        {
            put(TYPE_ROCKET, "rocketCount");
            put(TYPE_GUESS, "fingerGuessCount");
            put(TYPE_SEND_GIFT, "giftCount");
            put(TYPE_LUDO, "ludoCount");
            put(TYPE_UMO, "umoCount");
            put(TYPE_MOMENT, "momentCount");
        }
    };

}
