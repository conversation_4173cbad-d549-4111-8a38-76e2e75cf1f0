package com.quhong.service;

import com.quhong.analysis.BlockedListLogEvent;
import com.quhong.analysis.EventDTO;
import com.quhong.analysis.EventReport;
import com.quhong.core.utils.DateHelper;
import com.quhong.data.ActorData;
import com.quhong.image.ImageUrlGenerator;
import com.quhong.mongo.dao.ActorDao;
import com.quhong.mongo.dao.FollowRoomDao;
import com.quhong.mongo.dao.RoomMemberDao;
import com.quhong.mysql.dao.RoomBlacklistDao;
import com.quhong.mysql.dao.VipInfoDao;
import com.quhong.mysql.data.RoomBlacklistData;
import com.quhong.redis.RoomAdminRedis;
import com.quhong.room.redis.MicFrameRedis;
import com.quhong.utils.RoomUtils;
import com.quhong.vo.PageVO;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;

import jakarta.annotation.Resource;
import java.util.ArrayList;
import java.util.List;

@Lazy
@Service
public class RoomBlacklistService {

    private static final Logger logger = LoggerFactory.getLogger(RoomBlacklistService.class);

    @Resource
    private RoomBlacklistDao roomBlacklistDao;
    @Resource
    private FollowRoomDao followRoomDao;
    @Resource
    private RoomAdminRedis roomAdminRedis;
    @Resource
    private ActorDao actorDao;
    @Resource
    private VipInfoDao vipInfoDao;
    @Resource
    private RoomMemberDao memberDao;
    @Resource
    private EventReport eventReport;
    @Resource
    private MicFrameRedis micFrameRedis;

    /**
     * 被房间拉黑的用户：取消关注房间、退出加入的房间、取消管理员身份
     *
     * @param roomId 房间id
     * @param aid    被拉黑的用户
     * @param optUid 操作用户
     */
    public void addRoomBlackList(String roomId, String aid, String optUid) {
        // 加入房间黑名单
        roomBlacklistDao.addBlock(RoomUtils.getRoomId(roomId), aid, optUid);
        // 退出加入的房间
        followRoomDao.delete(roomId, aid);
        // 取消语聊房管理员身份
        roomAdminRedis.removeRoomAdmin(RoomUtils.getRoomId(roomId), aid);
        // 取消直播房管理员身份
        roomAdminRedis.removeRoomAdmin(RoomUtils.getLiveRoomId(roomId), aid);
        // 退出加入的房间
        memberDao.delRoomMember(RoomUtils.getRoomId(roomId), aid);
        memberDao.delRoomMember(RoomUtils.getLiveRoomId(roomId), aid);
        // 埋点
        eventReport(roomId, aid, optUid, 3);
    }

    public PageVO roomBlockedList(String roomId, int page) {
        List<RoomBlacklistData> pageList = roomBlacklistDao.findByPage(roomId, page);
        List<BlockActorVO> list = new ArrayList<>(pageList.size());
        for (RoomBlacklistData data : pageList) {
            ActorData actorData = actorDao.getActorDataFromCache(data.getUid());
            if (null != actorData) {
                list.add(copyFromActor(data, actorData));
            }
        }
        PageVO vo = new PageVO();
        vo.setList(list);
        vo.setNextUrl(list.size() < RoomBlacklistDao.PAGE_SIZE ? "" : (page + 1) + "");
        return vo;
    }

    private BlockActorVO copyFromActor(RoomBlacklistData blacklistData, ActorData actorData) {
        BlockActorVO blockActorVO = new BlockActorVO();
        blockActorVO.setAid(actorData.getUid());
        blockActorVO.setName(actorData.getName());
        blockActorVO.setHead(ImageUrlGenerator.generateRoomUserUrl(actorData));
        blockActorVO.setVipLevel(vipInfoDao.getIntVipLevel(actorData.getUid()));
        blockActorVO.setOptName(actorDao.getActorDataFromCache(blacklistData.getOptUid()).getName());
        blockActorVO.setMicFrame(micFrameRedis.getMicSourceFromCache(actorData.getUid()));
        blockActorVO.setCtime(blacklistData.getCtime());
        return blockActorVO;
    }

    public PageVO searchRoomBlackList(String roomId, String key) {
        PageVO vo = new PageVO();
        List<BlockActorVO> list = new ArrayList<>();
        List<ActorData> actorDataList = actorDao.searchByKey(key);
        for (ActorData actorData : actorDataList) {
            RoomBlacklistData blacklistData = roomBlacklistDao.findData(roomId, actorData.getUid());
            if (null == blacklistData) {
                continue;
            }
            list.add(copyFromActor(blacklistData, actorData));
        }
        vo.setList(list);
        vo.setNextUrl("");
        return vo;
    }

    public Object removeRoomBlackList(String roomId, String aid, String uid) {
        roomBlacklistDao.delBlock(roomId, aid);
        // 埋点
        eventReport(roomId, aid, uid, 4);
        return null;
    }

    private void eventReport(String roomId, String aid, String uid, int opType) {
        BlockedListLogEvent event = new BlockedListLogEvent();
        event.setRoom_id(roomId);
        event.setBlocked_list_action(opType);
        event.setTo_uid(aid);
        event.setUid(uid);
        event.setCtime(DateHelper.getNowSeconds());
        eventReport.track(new EventDTO(event));
    }

    /**
     * 列表展示字段：用户头像、昵称、VIP勋章、被加入者用户昵称、拉黑时间，点击用户头像访问用户主页、点击用户头像访问用户主页
     */
    public static class BlockActorVO {
        private String aid;
        private String name;
        private String optName;
        private String head;
        private int vipLevel;
        private int ctime;
        private String micFrame;

        public String getAid() {
            return aid;
        }

        public void setAid(String aid) {
            this.aid = aid;
        }

        public String getName() {
            return name;
        }

        public void setName(String name) {
            this.name = name;
        }

        public String getOptName() {
            return optName;
        }

        public void setOptName(String optName) {
            this.optName = optName;
        }

        public String getHead() {
            return head;
        }

        public void setHead(String head) {
            this.head = head;
        }

        public int getVipLevel() {
            return vipLevel;
        }

        public void setVipLevel(int vipLevel) {
            this.vipLevel = vipLevel;
        }

        public int getCtime() {
            return ctime;
        }

        public void setCtime(int ctime) {
            this.ctime = ctime;
        }

        public void setMicFrame(String micFrame) {
            this.micFrame = micFrame;
        }

        public String getMicFrame() {
            return micFrame;
        }
    }
}
