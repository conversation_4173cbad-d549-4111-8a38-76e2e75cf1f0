package com.quhong.service;

import com.quhong.core.config.ServerConfig;
import com.quhong.data.ActorData;
import com.quhong.mongo.dao.ActorDao;
import com.quhong.monitor.MonitorSender;
import com.quhong.mysql.dao.FamilyDao;
import com.quhong.mysql.data.FamilyData;
import jakarta.annotation.Resource;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;

import java.util.Map;

@Lazy
@Service
public class ActivityMonitorService {

    private final Logger logger = LoggerFactory.getLogger(getClass());

    private static final String WARN_NAME = ServerConfig.isProduct() ? "waho_activity_warn" : "waho_java_exception";

    @Resource
    private MonitorSender monitorSender;
    @Resource
    private ActorDao actorDao;
    @Resource
    private FamilyDao familyDao;

    /**
     * 钻石奖励下发门槛修改告警
     * @param activityName  活动名
     * @param rankingType   榜单类型
     * @param beanLimit     钻石奖励门槛
     * @param optName       修改人
     */
    public void rewardBeanLimitUpdateMonitor(String activityName, String rankingType, long beanLimit, String optName) {
        String content = ">告警名: 活动钻石奖励下发门槛修改 \n"
                + ">告警平台: Waho \n"
                + ">活动名称: " + activityName + "\n"
                + ">榜单类型: " + rankingType + "\n"
                + ">钻石门槛: " + beanLimit + "\n"
                + ">修改人: " + optName;
        monitorSender.customMarkdown(WARN_NAME, content);
    }

    /**
     * 榜单奖励修改告警
     * @param activityName 活动名
     * @param rankingType  榜单类型
     * @param rewardMap    奖励map
     * @param optName      修改人
     */
    public void rewardBeanUpdateMonitor(String activityName, String rankingType, Map<Integer, Integer> rewardMap, String optName) {
        if (rewardMap == null || rewardMap.isEmpty()) {
            return;
        }
        try {
            StringBuilder detail = new StringBuilder();
            long sumDiamonds = 0;
            for (Map.Entry<Integer, Integer> entry : rewardMap.entrySet()) {
                int rank = entry.getKey();
                int diamonds = entry.getValue();
                sumDiamonds += diamonds;
                detail.append("名次：").append(rank).append(", 奖励：").append(diamonds).append("钻石; \n");
            }
            String content = ">告警名: 活动榜单奖励修改 \n"
                    + ">告警平台: Waho \n"
                    + ">活动名称: " + activityName + "\n"
                    + ">榜单类型: " + rankingType + "\n"
                    + ">榜单奖励总额: " + sumDiamonds + "钻石 (约" + sumDiamonds / 10000 + "万钻石)" + "\n"
                    + ">奖励钻石详情: \n" + detail + "\n"
                    + ">修改人: " + optName;
            monitorSender.customMarkdown(WARN_NAME, content);
        } catch (Exception e) {
            logger.error("rewardBeanUpdate error. {}", e.getMessage(), e);
        }
    }

    /**
     * 奖励下发告警
     * @param activityName 活动名
     * @param rankingType  榜单类型
     * @param rewardMap    奖励map
     * @param scoreMap     榜单分数map
     */
    public void rewardBeanMonitor(String activityName, String rankingType, Map<String, Integer> rewardMap, Map<String, Long> scoreMap) {
        if (rewardMap == null || rewardMap.isEmpty()) {
            return;
        }
        try {
            StringBuilder detail = new StringBuilder();
            int rank = 1;
            for (Map.Entry<String, Long> entry : scoreMap.entrySet()) {
                String aid = entry.getKey();
                boolean isUser = false;
                String showId = "";
                try {
                    int familyId = Integer.parseInt(aid);
                    FamilyData familyData = familyDao.selectByIdFromCache(familyId);
                    showId = familyData.getRid() + "";
                } catch (Exception e) {
                    ActorData actorData = actorDao.getActorDataFromCache(aid);
                    showId = actorData != null ? actorData.getShowRid() : aid;
                    isUser = true;
                }

                long score = entry.getValue();
                long diamonds = rewardMap.getOrDefault(aid, 0);
                detail.append("名次：").append(rank)
                        .append(isUser ? ", 用户ID：" : ", 公会ID：").append(showId)
                        .append(", 榜单分数：").append(score)
                        .append(", 奖励：").append(diamonds).append("钻石; \n");
                rank ++;
            }
            int sumDiamonds = rewardMap.values().stream().mapToInt(Integer::intValue).sum();
            String content = ">告警名: 活动奖励下发 \n"
                    + ">告警平台: Waho \n"
                    + ">活动名称: " + activityName + "\n"
                    + ">榜单类型: " + rankingType + "\n"
                    + ">奖励钻石总计: " + sumDiamonds + "钻石 (约" + sumDiamonds / 10000 + "万钻石)" + "\n"
                    + ">奖励钻石详情: \n" + detail + "\n";
            monitorSender.customMarkdown(WARN_NAME, content);
        } catch (Exception e) {
            logger.error("rewardBeanMonitor error. {}", e.getMessage(), e);
        }
    }
}
