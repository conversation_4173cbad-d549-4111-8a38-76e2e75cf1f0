package com.quhong.service.mysql;

import com.quhong.cache.CacheMap;
import com.quhong.mysql.dao.BadWordDao;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import jakarta.annotation.PostConstruct;
import jakarta.annotation.Resource;
import java.util.*;

/**
 * 脏词词库
 *
 * <AUTHOR>
 */
@Lazy
@Service
public class BadWordService {

    private static final Logger logger = LoggerFactory.getLogger(BadWordService.class);

    /**
     * 最小匹配规则
     */
    public static final int MIN_MATCH_TYPE = 1;

    /**
     * 最大匹配规则
     */
    public static final int MAX_MATCH_TYPE = 2;

    private final CacheMap<String, Map<String, Object>> cacheMap;

    public BadWordService() {
        cacheMap = new CacheMap<>(3 * 60 * 1000L);
    }

    @PostConstruct
    public void postInit() {
        cacheMap.start();
    }

    @Resource
    private BadWordDao badWordDao;

    /**
     * 检查文字中是否包含敏感字符，检查规则如下：
     *
     * @param txt 文字
     * @param beginIndex 开始检测的下标
     * @param matchType 匹配类型
     * @return 如果存在，则返回敏感词字符的长度，不存在返回0
     */
    @SuppressWarnings({ "rawtypes"})
    public int checkBadWord(String txt,int beginIndex,int matchType) {
        //敏感词结束标识位：用于敏感词只有1位的情况
        boolean flag = false;
        //匹配标识数默认为0
        int matchFlag = 0;
        Map nowMap = getBadWordMap();
        for(int i = beginIndex; i < txt.length() ; i++){
            char word = txt.charAt(i);
            //获取指定key
            nowMap = (Map) nowMap.get(String.valueOf(word));
            //存在，则判断是否为最后一个
            if(nowMap != null){
                //找到相应key，匹配标识+1
                matchFlag++;
                //如果为最后一个匹配规则,结束循环，返回匹配标识数
                if("1".equals(nowMap.get("isEnd"))){
                    flag = true;
                    //最小规则，直接返回,最大规则还需继续查找
                    if(MIN_MATCH_TYPE == matchType){
                        break;
                    }
                }
            }
            else{
                break;
            }
        }
        if(!flag){
            matchFlag = 0;
        }
        return matchFlag;
    }

    /**
     * 判断文字是否包含敏感字符
     * @param txt  文字
     * @param matchType  匹配规则 1：最小匹配规则，2：最大匹配规则
     * @return 若包含返回true，否则返回false
     */
    public boolean isContainBadWord(String txt,int matchType){
        txt = handleWord(txt);
        boolean flag = false;
        for(int i = 0 ; i < txt.length() ; i++){
            //判断是否包含敏感字符
            int matchFlag = checkBadWord(txt, i, matchType);
            if(matchFlag > 0){
                //大于0存在，返回true
                flag = true;
            }
        }
        return flag;
    }

    /**
     * 替换敏感字字符
     *
     * @param txt 文本
     * @param matchType 匹配类型
     * @param replaceChar 替换字符，默认*
     * @return 替换后的文本
     */
    public String replaceBadWord(String txt,int matchType,String replaceChar){
        txt = handleWord(txt);
        String resultTxt = txt;
        //获取所有的敏感词
        Set<String> set = getBadWord(txt, matchType);
        for (String word : set) {
            String replaceString = getReplaceChars(replaceChar, word.trim().length());
            resultTxt = resultTxt.replaceAll(word, replaceString);
        }
        return resultTxt;
    }
    /**
     * 获取文字中的敏感词
     */
    private Set<String> getBadWord(String txt , int matchType){
        Set<String> sensitiveWordList = new HashSet<>();
        for(int i = 0 ; i < txt.length() ; i++){
            //判断是否包含敏感字符
            int length = checkBadWord(txt, i, matchType);
            //存在,加入list中
            if(length > 0){
                sensitiveWordList.add(txt.substring(i, i+length));
                //减1的原因，是因为for会自增
                i = i + length - 1;
            }
        }
        return sensitiveWordList;
    }

    /**
     * 获取替换字符串
     */
    private String getReplaceChars(String replaceChar, int length){
        StringBuilder resultReplace = new StringBuilder(replaceChar);
        for(int i = 1 ; i < length ; i++){
            resultReplace.append(replaceChar);
        }
        return resultReplace.toString();
    }

    private Map<String, Object> getBadWordMap(){
        Map<String, Object> wordMap = cacheMap.getData(getBadWordKey());
        if (!CollectionUtils.isEmpty(wordMap)) {
            return wordMap;
        }
        Set<String> keyWordSet = badWordDao.getBadWordSet();
        return addBadWordToHashMap(keyWordSet);
    }

    /**
     * 将敏感词库构建成了一个类似与一颗一颗的树，这样我们判断一个词是否为敏感词时就大大减少了检索的匹配范围。
     * @param keyWordSet 敏感词库
     */
   @SuppressWarnings({ "unchecked", "rawtypes" })
    private Map<String, Object> addBadWordToHashMap(Set<String> keyWordSet) {
        // 初始化敏感词容器，减少扩容操作
        HashMap sensitiveWordMap = new HashMap(keyWordSet.size());
        Map<String, Object> nowMap;
        Map<String, Object> newWordMap;
        //迭代keyWordSet
        for (String s : keyWordSet) {
            //关键字
            String key = s.toLowerCase();
            nowMap = sensitiveWordMap;
            for (int i = 0; i < key.length(); i++) {
                //转换成char型
                char keyChar = key.charAt(i);
                Object wordMap = nowMap.get(String.valueOf(keyChar));
                if (wordMap != null) {
                    //如果存在该key，直接赋值
                    nowMap = (Map) wordMap;
                } else {
                    //不存在则，则构建一个map，同时将isEnd设置为0，因为他不是最后一个
                    newWordMap = new HashMap(16);
                    //不是最后一个
                    newWordMap.put("isEnd", "0");
                    nowMap.put(String.valueOf(keyChar), newWordMap);
                    nowMap = newWordMap;
                }

                if (i == key.length() - 1) {
                    //最后一个
                    nowMap.put("isEnd", "1");
                }
            }
        }
        // 缓存脏词库
        cacheMap.cacheData(getBadWordKey(), sensitiveWordMap);
        logger.info("Dirty word thesaurus built successfully.");
        return sensitiveWordMap;
    }

    private String handleWord(String txt) { return " " + txt.toLowerCase() + " "; }

    private String getBadWordKey() { return "str:badWordMap"; }

}
