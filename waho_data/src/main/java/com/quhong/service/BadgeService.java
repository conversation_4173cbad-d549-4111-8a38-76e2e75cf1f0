package com.quhong.service;

import com.quhong.core.utils.DateHelper;
import com.quhong.data.dto.ResourcesDTO;
import com.quhong.enums.BaseDataResourcesConstant;
import com.quhong.mongo.dao.BadgeDao;
import com.quhong.mongo.dao.BadgeListDao;
import com.quhong.mongo.data.BadgeData;
import com.quhong.mongo.data.BadgeListData;
import com.quhong.mq.MqSenderService;
import com.quhong.mysql.dao.AchievementBadgeDao;
import com.quhong.mysql.dao.CoinSellerUserRecordDao;
import com.quhong.mysql.dao.RechargeDailyInfoDao;
import com.quhong.mysql.data.AchievementBadgeData;
import com.quhong.utils.WalletUtils;
import jakarta.annotation.Resource;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.concurrent.TimeUnit;
import java.util.function.Function;
import java.util.stream.Collectors;


@Lazy
@Service
public class BadgeService {
    private static final Logger logger = LoggerFactory.getLogger(BadgeService.class);
    private static final String ACHIEVE_DESC = "AchieveBadge";

    private static final Long M = 1000000L; // 单位：百万
    private static final List<Long> BADGE_RECHARGE_THRESHOLD = List.of(11 * M, 110 * M, 550 * M, 1100 * M, 5500 * M);
    private static final List<Integer> RECHARGE_BADGE_ID= List.of(71, 72, 73, 74, 75);
    public static final int RECHARGE_AGENT_BADGE_ID = 76;
    private static final long RECHARGE_AGENT_BADGE_LIMIT = 10000 * 10000; // 币商勋章门槛：销售8千万钻石

    @Resource
    private RechargeDailyInfoDao rechargeDailyInfoDao;
    @Resource
    private MqSenderService mqSenderService;
    @Resource
    private AchievementBadgeDao achievementBadgeDao;
    @Resource
    private BadgeListDao badgeListDao;
    @Resource
    private BadgeDao badgeDao;
    @Resource
    private CoinSellerUserRecordDao coinSellerUserRecordDao;


    /**
     * 根据数量获取等级，返回值范围[0,list.Size()]
     */
    public int getLevelFromSoredList(List<Integer> list, int num) {
        if (CollectionUtils.isEmpty(list) || num <= 0) {
            return 0;
        }
        for (int i = 0; i < list.size(); i++) {
            int listValue = list.get(i);
            if (listValue > num) {
                return i;
            }
            if (listValue == num) {
                return i + 1;
            }
        }
        return list.size();
    }

    /**
     * 根据数量获取等级，返回值范围[0,list.Size()]
     */
    public int getLevelFromSoredList(List<Long> list, long num) {
        if (CollectionUtils.isEmpty(list) || num <= 0) {
            return 0;
        }
        for (int i = 0; i < list.size(); i++) {
            long listValue = list.get(i);
            if (listValue > num) {
                return i;
            }
            if (listValue == num) {
                return i + 1;
            }
        }
        return list.size();
    }

    public void doGiftBadge(String aid, int giftId, int num, int oldNum,
                            Map<Integer, List<Integer>> giftNumMap, Map<Integer, List<Integer>> badgeIdMap) {
        if (!giftNumMap.containsKey(giftId) || !badgeIdMap.containsKey(giftId)) {
            return;
        }
        doGiftBadge(aid, num, oldNum, badgeIdMap.get(giftId), giftNumMap.get(giftId));
    }

    public void doGiftBadge(String aid, int num, int oldNum, List<Integer> badgeIdList, List<Integer> giftNumList) {
        int levelNow = getLevelFromSoredList(giftNumList, oldNum);
        if (levelNow >= giftNumList.size()) {
            return;
        }
        int levelUpNum = giftNumList.get(levelNow);
        if (levelUpNum > 0 && num >= levelUpNum) {
            int levelNew = getLevelFromSoredList(giftNumList, num);
            logger.info("badge level up, uid={} levelNow={} levelNew={} num={} oldNum={} levelUpNum={}", aid, levelNow, levelNew, num, oldNum, levelUpNum);
            if (levelNow > 0) {
                BadgeData badgeData = badgeDao.getBadgeData(aid, badgeIdList.get(levelNow - 1));
                if (null != badgeData) {
                    badgeDao.deleteBadge(badgeData);
                }
            }
            // 下标从0开始
            giveBadgeToUser(aid, badgeIdList.get(levelNew - 1), num);
        }
    }

    /**
     * 下发等级勋章，每次判断勋章是否存在，不存在即为升级
     */
    public void doGiftBadge(String aid, int num, List<Integer> badgeIdList, List<Integer> giftNumList) {
        int levelNew = getLevelFromSoredList(giftNumList, num);
        BadgeData oldBadgeData = badgeDao.getBadgeData(aid, badgeIdList.get(levelNew - 1));
        if (null == oldBadgeData) {
            logger.info("badge level up, uid={} levelNew={} num={}", aid, levelNew, num);
            badgeDao.removeBadges(aid, badgeIdList);
            // 下标从0开始
            giveBadgeToUser(aid, badgeIdList.get(levelNew - 1), num);
        }
    }

    public void giveBadgeToUser(String aid, int badgeId, int num) {
        logger.info("giveBadgeToUser uid={} badgeId={} num={}", aid, badgeId, num);
        BadgeListData badgeListData = badgeListDao.findData(badgeId);
        if (null == badgeListData) {
            logger.error("badge not exists, uid={} badgeId={}", aid, badgeId);
            return;
        }
        BadgeData oldBadgeData = badgeDao.getBadgeData(aid, badgeId);
        boolean newBadge = false;
        if (null == oldBadgeData) {
            List<BadgeData> wearBadgeList = badgeDao.getWearBadgeList(aid);
            List<Integer> badgeStatus = new ArrayList<>();
            for (BadgeData badgeData : wearBadgeList) {
                badgeStatus.add(badgeData.getStatus());
            }
            int st = 0;
            if (badgeStatus.size() != 3) {
                for (int status = 1; status <= 3; status++) {
                    if (!badgeStatus.contains(status)) {
                        st = status;
                        break;
                    }
                }
            }
            BadgeData badgeData = new BadgeData();
            badgeData.setUid(aid);
            badgeData.setBadge_id(badgeId);
            badgeData.setStatus(st);
            badgeData.setGet_time(DateHelper.getNowSeconds());
            // 1767196800表示永久
            badgeData.setEnd_time(1767196800);
            badgeDao.save(badgeData);
            newBadge = true;
        }
        logger.info("change badge success, uid={} badgeId={} newBadge={}", aid, badgeId, newBadge);
    }

    /**
     * 下发成就勋章
     * @param uid 用户uid
     * @param badgeType 下发勋章类型
     * @param getNum 获得的数量
     */
    public void doAchieveBadge(String uid, int badgeType, long countNum, long getNum){
        try{

            List<AchievementBadgeData> achieveBadgeDataList = achievementBadgeDao.getAchievementFromCache(badgeType);

            List<Long> countNumList = achieveBadgeDataList.stream().map(AchievementBadgeData::getCountNum).distinct().collect(Collectors.toList());
            Map<Long, AchievementBadgeData> achieveBadgeMap = achieveBadgeDataList.stream().collect(Collectors.toMap(AchievementBadgeData::getCountNum, Function.identity()));

            // 发送礼物前的等级

            int levelNow = getLevelFromSoredList(countNumList, countNum - getNum);

            logger.info("doAchieveBadge1 countNumList: {}, achieveBadgeMap: {}, levelNow:{}", countNumList, achieveBadgeMap, levelNow);
            if (levelNow >= countNumList.size()) {
                return;
            }

            // 下一等级所需数量
            long levelUpNum = countNumList.get(levelNow);

            logger.info("doAchieveBadge2 levelUpNum: {}", levelUpNum);
            if (levelUpNum > 0 && countNum >= levelUpNum) {
                int levelNew = getLevelFromSoredList(countNumList, countNum);
                AchievementBadgeData achievementBadgeData =  achieveBadgeDataList.get(levelNew - 1);
                if (achievementBadgeData != null) {
                    long lastCountNum = achievementBadgeData.getLastCountNum();
                    AchievementBadgeData lastBadgeInfo = achieveBadgeMap.get(lastCountNum);
                    if(lastBadgeInfo != null){
                        int lastBadgeId = lastBadgeInfo.getBadgeId();
                        BadgeData lastBadgeData = badgeDao.getBadgeData(uid, lastBadgeId);
                        if(lastBadgeData != null && lastBadgeData.getStatus() > 0){
                            lastBadgeData.setStatus(0);
                            badgeDao.update(lastBadgeData);
                        }
                    }
                    int currentBadgeId = achievementBadgeData.getBadgeId();
                    sendReward(uid, currentBadgeId, BaseDataResourcesConstant.ACTION_GET_WEAR);
                }
                logger.info("doAchieveBadge3 levelNew: {}, achievementBadgeData: {}", levelNew, achievementBadgeData);
            }
        }catch (Exception e){
            logger.error("doAchieveBadge error uid: {}, badgeType:{}, countNum:{}, getNum:{}, message:{}", uid, badgeType, countNum, getNum, e.getMessage(), e);
        }
    }

    /**
     * 补发成就勋章
     * @param uid 用户uid
     * @param badgeType 下发勋章类型
     * @param countNum 当前数量
     */
    public void supplyAchieveBadge(String uid, int badgeType, long countNum){
        try{
            List<AchievementBadgeData> achieveBadgeDataList = achievementBadgeDao.getAchievementFromCache(badgeType);
            for (AchievementBadgeData badgeData : achieveBadgeDataList) {
                if(countNum >= badgeData.getCountNum()){
                    sendReward(uid, badgeData.getBadgeId(), BaseDataResourcesConstant.ACTION_GET);
                }
            }
        }catch (Exception e){
            logger.error("supplyAchieveBadge error uid: {}, badgeType:{}, countNum:{}, getNum:{}, message:{}", uid, badgeType, countNum, e.getMessage(), e);
        }
    }

    /**
     *  降级删除成就勋章
     * @param uid 用户uid
     * @param badgeType 移除勋章类型
     * @param countNum 已减数量之后的统计数量
     * @param reduceNum 减少的数量
     */
    public void deleteAchieveBadge(String uid, int badgeType, int countNum, int reduceNum){
        try{
            List<AchievementBadgeData> achieveBadgeDataList = achievementBadgeDao.getAchievementFromCache(badgeType);
            List<Long> countNumList = achieveBadgeDataList.stream().map(AchievementBadgeData::getCountNum).distinct().collect(Collectors.toList());
            Map<Long, AchievementBadgeData> achieveBadgeMap = achieveBadgeDataList.stream().collect(Collectors.toMap(AchievementBadgeData::getCountNum, Function.identity()));

            // 删除好友前的等级
            int levelNow = getLevelFromSoredList(countNumList, countNum + reduceNum);
            if (levelNow <= 0) {
                return;
            }

            // 上一等级所需数量
            long levelDownNum = countNumList.get(levelNow - 1);
            if (levelDownNum > 0 && countNum < levelDownNum) {
                int levelNew = getLevelFromSoredList(countNumList, countNum);
                AchievementBadgeData achievementBadgeData =  achieveBadgeDataList.get(levelNew);
                if (achievementBadgeData != null) {
                    int currentBadgeId = achievementBadgeData.getBadgeId();
                    BadgeData badgeData = badgeDao.getBadgeData(uid, currentBadgeId);
                    if(badgeData != null){
                        sendReward(uid, currentBadgeId, BaseDataResourcesConstant.ACTION_DELETE);
                    }
                }
            }
        }catch (Exception e){
            logger.error("deleteAchieveBadge error uid: {}, badgeType:{}, countNum:{}, getNum:{}, message:{}", uid, badgeType, countNum, reduceNum, e.getMessage(), e);
        }
    }


    public void sendReward(String uid, int badgeId, int badgeAction){
        logger.info("send msg to mq. toUid={}, badgeId={}", uid, badgeId);
        mqSenderService.asyncHandleResources(getBadgeResourcesDTO(uid, badgeId, ACHIEVE_DESC, badgeAction, -1));
    }

    /**
     * 处理充值勋章
     */
    public void doRechargeBadge(String uid) {
        long rechargeTotalDiamond = rechargeDailyInfoDao.getUserTotalRechargeDiamonds(uid);
        List<Long> rechargeThresholds = BADGE_RECHARGE_THRESHOLD;
        for (Long threshold : rechargeThresholds) {
            if (rechargeTotalDiamond >= WalletUtils.diamondsToRaw(threshold.intValue())) {
                sendRechargeBadge(uid, RECHARGE_BADGE_ID.get(rechargeThresholds.indexOf(threshold)));
            }
        }
    }

    public void sendRechargeBadge(String uid, int badgeId) {
        BadgeData badgeData = badgeDao.getBadgeData(uid, badgeId);
        if (badgeData != null) {
            logger.info("The user already has the badge. toUid={}, badgeId={}", uid, badgeId);
            return;
        }
        List<BadgeData> wearBadgeList = badgeDao.getWearBadgeList(uid);
        if (!CollectionUtils.isEmpty(wearBadgeList)) {
            // 佩戴了同一等级的勋章要下掉
            wearBadgeList.forEach(k -> {
                if (RECHARGE_BADGE_ID.contains(k.getBadge_id())) {
                    k.setStatus(0);
                    badgeDao.update(k);
                }
            });
        }
        logger.info("send  msg to mq. toUid={}, badgeId={}", uid, badgeId);
        ResourcesDTO resourcesDTO = new ResourcesDTO();
        resourcesDTO.setUid(uid);
        resourcesDTO.setResId(String.valueOf(badgeId));
        resourcesDTO.setResType(BaseDataResourcesConstant.TYPE_BADGE);
        resourcesDTO.setItemsSourceDetail("RechargeBadge");
        resourcesDTO.setDesc("RechargeBadge");
        resourcesDTO.setActionType(2);
        resourcesDTO.setOfficialMsg(1);
        resourcesDTO.setmTime(DateHelper.getNowSeconds());
        resourcesDTO.setNum(1);
        resourcesDTO.setDays(-1);
        mqSenderService.asyncHandleResources(resourcesDTO);
    }

    /**
     * 回收代理勋章
     */
    public void recycleRechargeAgentBadge(String uid) {
        int badgeId = RECHARGE_AGENT_BADGE_ID;
        BadgeData badgeData = badgeDao.getBadgeData(uid, badgeId);
        if (badgeData == null) {
            logger.info("The user already not has the badge. toUid={}, badgeId={}", uid, badgeId);
            return;
        }
        badgeDao.setCoinSellerBadgeEndTime(uid, (int) badgeData.getEnd_time());
        logger.info("send msg to mq. toUid={}, badgeId={}", uid, badgeId);
        mqSenderService.asyncHandleResources(getBadgeResourcesDTO(uid, badgeId, "RechargeAgentBadge", 4, 0));
    }

    public void sendBadge(String uid, int badgeId, String desc, int days) {
        mqSenderService.asyncHandleResources(getBadgeResourcesDTO(uid, badgeId, desc, 2, days));
    }

    private ResourcesDTO getBadgeResourcesDTO(String uid, int badgeId, String desc, int actionType, int days) {
        ResourcesDTO resourcesDTO = new ResourcesDTO();
        resourcesDTO.setUid(uid);
        resourcesDTO.setResId(String.valueOf(badgeId));
        resourcesDTO.setResType(BaseDataResourcesConstant.TYPE_BADGE);
        resourcesDTO.setItemsSourceDetail(desc);
        resourcesDTO.setDesc(desc);
        resourcesDTO.setActionType(actionType);
        resourcesDTO.setOfficialMsg(1);
        resourcesDTO.setmTime(DateHelper.getNowSeconds());
        resourcesDTO.setNum(1);
        resourcesDTO.setDays(days);
        return resourcesDTO;
    }

    public void sendCoinSellerBadge(String uid) {
        try {
            BadgeData badgeData = badgeDao.getBadgeData(uid, RECHARGE_AGENT_BADGE_ID);
            if (badgeData != null) {
                return;
            }
            int nowSeconds = DateHelper.getNowSeconds();
            int startTime = (int)(nowSeconds - TimeUnit.DAYS.toSeconds(60));
            long soldDiamondsTotal = coinSellerUserRecordDao.getSoldDiamondsTotal(uid, startTime, nowSeconds);
            if (soldDiamondsTotal >= RECHARGE_AGENT_BADGE_LIMIT) {
                logger.info("send msg to mq. toUid={}, badgeId={}", uid, RECHARGE_AGENT_BADGE_ID);
                mqSenderService.asyncHandleResources(getBadgeResourcesDTO(uid, RECHARGE_AGENT_BADGE_ID, "RechargeAgentBadge", 2, 60));
            }
        } catch (Exception e) {
            logger.error("sendCoinSellerBadge error. uid={} {}", uid, e.getMessage(), e);
        }
    }

    public void extendCoinSellerBadgeEndTime(BadgeData badgeData) {
        int nowSeconds = DateHelper.getNowSeconds();
        int startTime = (int)(nowSeconds - TimeUnit.DAYS.toSeconds(60));
        long soldDiamondsTotal = coinSellerUserRecordDao.getSoldDiamondsTotal(badgeData.getUid(), startTime, nowSeconds);
        if (soldDiamondsTotal >= RECHARGE_AGENT_BADGE_LIMIT) {
            badgeData.setEnd_time(DateHelper.getNowSeconds() + TimeUnit.DAYS.toSeconds(60));
            badgeDao.update(badgeData);
        }
    }

    public void insertBadge(String uid, int badgeId, int endTime) {
        BadgeData badgeData = badgeDao.getBadgeData(uid, badgeId);
        if (null != badgeData) {
            badgeDao.deleteBadge(badgeData);
        }
        badgeData = new BadgeData();
        badgeData.setUid(uid);
        badgeData.setBadge_id(badgeId);
        badgeData.setStatus(0);
        badgeData.setGet_time(DateHelper.getNowSeconds());
        badgeData.setEnd_time(endTime);
        badgeDao.save(badgeData);
    }

    public void returnRechargeAgentBadge(String uid) {
        int endTime = badgeDao.getCoinSellerBadgeEndTime(uid);
        if (endTime == 0 || endTime <= DateHelper.getNowSeconds()) {
            return;
        }
        insertBadge(uid, RECHARGE_AGENT_BADGE_ID, endTime);
    }
}
