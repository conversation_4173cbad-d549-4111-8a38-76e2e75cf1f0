package com.quhong.service;

import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.quhong.analysis.AnchorCharmLogEvent;
import com.quhong.analysis.EventDTO;
import com.quhong.analysis.EventReport;
import com.quhong.config.CaffeineCacheConfig;
import com.quhong.constant.MoneyActionType;
import com.quhong.core.config.ServerConfig;
import com.quhong.core.date.DateSupport;
import com.quhong.core.distribution.DistributeLock;
import com.quhong.core.utils.DateHelper;
import com.quhong.data.ActorData;
import com.quhong.data.RechargeInfo;
import com.quhong.data.dto.AsyncCharmDTO;
import com.quhong.data.dto.MoneyDetailReq;
import com.quhong.enums.CharmLogTypeEnum;
import com.quhong.enums.HttpCode;
import com.quhong.enums.SLangType;
import com.quhong.exception.CommonException;
import com.quhong.exception.CommonH5Exception;
import com.quhong.mongo.dao.ActorDao;
import com.quhong.mongo.dao.FamilyDailyIncomeDao;
import com.quhong.mongo.data.OfficialData;
import com.quhong.monitor.MonitorSender;
import com.quhong.mq.MqSenderService;
import com.quhong.mysql.config.WahoMySQLBean;
import com.quhong.mysql.dao.*;
import com.quhong.mysql.data.*;
import com.quhong.redis.BlockAgentUserRedis;
import com.quhong.utils.AsyncUtils;
import com.quhong.utils.WalletUtils;
import jakarta.annotation.Resource;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.ObjectUtils;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.HashMap;
import java.util.Map;
import java.util.Set;

/**
 * 主播钱包，包括魅力值和美金
 */
@Lazy
@Service
public class AnchorWalletService {

    private final Logger logger = LoggerFactory.getLogger(getClass());
    public static final int USD_TO_DIAMONDS = 8800; // 1美金可转换xx钻石
    public static final int USD_BUY_DIAMONDS = 9000; // 1美金可向币商购买xx钻石
    public static final int USD_COIN_SELLER_BUY_DIAMONDS = 9000; // 币商1美金可向主播币商购买xx钻石
    public static final long USD_TO_CHARM = 14000; // 1美金可转换xxx魅力值

    public static final long[] AGENT_INCOME = new long[]{0, 70000000, 140000000};
    public static final BigDecimal[] AGENT_RATE = new BigDecimal[]{new BigDecimal("0.15"), new BigDecimal("0.17"), new BigDecimal("0.20")};
    public static final String[] AGENT_LEVEL = new String[]{"B", "A", "S"};

    // 特殊分成公会，key=主键id,value=分成
    public static final Map<Integer, BigDecimal> SPECIAL_FAMILY_ID = new HashMap<>() {{
        put(5171, new BigDecimal("0.21")); // 17967
    }};

    private static final String BUY_OFFICIAL_TITLE = "Notification";
    private static final String BUY_OFFICIAL_TITLE_AR = "إشعار";
    private static final String BUY_OFFICIAL_BODY = "You have successfully recharged %s diamonds from user %s!";
    private static final String BUY_OFFICIAL_BODY_AR = "لقد قمت بشحن %s الماس بنجاح من المستخدم %s!";
    public static final String ACTION = "View";
    public static final String ACTION_AR = "شاهد";

    private @Resource ActorDao actorDao;
    private @Resource AnchorWalletDao anchorWalletDao;
    private @Resource CoinExchangeService coinExchangeService;
    private @Resource CoinSellerDao coinSellerDao;
    private @Resource AnchorCharmLogDao anchorCharmLogDao;
    private @Resource MqSenderService mqSenderService;
    private @Resource WithdrawReviewDao withdrawReviewDao;
    private @Resource OfficialMsgService officialMsgService;
    private @Resource FamilyDao familyDao;
    private @Resource FamilyDailyIncomeDao familyDailyIncomeDao;
    private @Resource BlockAgentUserRedis blockAgentUserRedis;
    private @Resource FreezeCharmDao freezeCharmDao;
    private @Resource MonitorSender monitorSender;
    private @Resource WithdrawalOrderDao withdrawalOrderDao;
    private @Resource AnchorWalletService anchorWalletService;
    private @Resource CharmMonitorService charmMonitorService;
    private @Autowired(required = false) EventReport eventReport;
    private @Resource TaskCharmLogDao taskCharmLogDao;
    private @Resource CommissionStatDao commissionStatDao;

    private String getLockKey(String uid) {
        return "anchorWallet:" + uid;
    }

    @Deprecated
    public static int getUsdToDiamonds() {
        return USD_TO_DIAMONDS;
    }

    @Deprecated
    public static int getUsdBuyDiamonds() {
        return USD_BUY_DIAMONDS;
    }

    /**
     * 获取钱包，返回值可能为空
     */
    public AnchorWalletData getWallet(String uid) {
        return anchorWalletDao.getById(uid);
    }

    /**
     * 获取扣除冻结魅力值后的魅力值余额
     */
    public BigDecimal getShowCharmBalance(String uid, AnchorWalletData walletData) {
        if (walletData == null) {
            walletData = getWallet(uid);
        }
        if (walletData == null) {
            return BigDecimal.ZERO;
        }
        BigDecimal freezeCharm = freezeCharmDao.getTotalFreezeCharm(uid);
        if (freezeCharm.compareTo(walletData.getCharmBalance()) > 0) {
            return BigDecimal.ZERO;
        }
        return walletData.getCharmBalance().subtract(freezeCharm);
    }

    /**
     * admin魅力值操作，【该方法余额不足时也会扣除成功，并且为负数】
     *
     * @param uid    主播uid
     * @param change 魅力值变动数额，大于0为增加，小于0为减少
     */
    public AnchorWalletData adminChangeCharm(String uid, BigDecimal change, AnchorCharmLogData logData) {
        if (change.compareTo(BigDecimal.ZERO) == 0 || null == logData) {
            throw new CommonException(HttpCode.PARAM_ERROR);
        }
        try (DistributeLock lock = new DistributeLock(getLockKey(uid))) {
            lock.lock();
            AnchorWalletData walletData = anchorWalletDao.getWallet(uid);
            walletData.setCharmBalance(walletData.getCharmBalance().add(change));
            walletData.setMtime(DateHelper.getNowSeconds());
            anchorWalletDao.updateById(walletData);
            logger.info("admin change charm balance. uid={} aid={} change={} balance={}",
                    uid, logData.getAid(), change, walletData.getCharmBalance());
            logData.setUid(uid);
            logData.setChange(change);
            logData.setBalance(walletData.getCharmBalance());
            saveCharmLog(logData);
            AsyncUtils.execute(() -> {
                if (change.compareTo(BigDecimal.ZERO) > 0) {
                    sendAdminAddCharmNotice(uid, logData.getDesc());
                }
            });
            return walletData;
        }
    }

    /**
     * 魅力值操作，并初始化钱包数据
     *
     * @param uid    主播uid
     * @param change 魅力值变动数额，大于0为增加，小于0为减少
     */
    public AnchorWalletData changeCharm(String uid, BigDecimal change, AnchorCharmLogData logData) {
        if (change.compareTo(BigDecimal.ZERO) == 0 || null == logData) {
            throw new CommonException(HttpCode.PARAM_ERROR);
        }
        AnchorWalletData walletData;
        long startTime = System.currentTimeMillis();
        try (DistributeLock lock = new DistributeLock(getLockKey(uid))) {
            lock.lock();
            long lockTime = System.currentTimeMillis();
            walletData = anchorWalletDao.getWallet(uid);
            if (change.compareTo(BigDecimal.ZERO) < 0) {
                BigDecimal freezeCharm = freezeCharmDao.getTotalFreezeCharm(uid);
                BigDecimal charmBalance = logData.getLogType() != -9 ? walletData.getCharmBalance().subtract(freezeCharm) : walletData.getCharmBalance();
                if (charmBalance.compareTo(change.abs()) < 0) {
                    throw new CommonException(HttpCode.INSUFFICIENT_BALANCE);
                }
            }
            walletData.setCharmBalance(walletData.getCharmBalance().add(change));
            walletData.setMtime(DateHelper.getNowSeconds());
            anchorWalletDao.updateById(walletData);
            long endTime = System.currentTimeMillis();
            logger.info("change charm balance. uid={} aid={} change={} logType={} updateCost={} totalCost={}",
                    uid, logData.getAid(), change, logData.getLogType(), endTime - lockTime, endTime - startTime);
        }
        logData.setUid(uid);
        logData.setChange(change);
        logData.setBalance(walletData.getCharmBalance());
        saveCharmLog(logData);
        return walletData;
    }

    /**
     * 分布式锁操作魅力值余额，【过渡时需同一时间切换】
     */
    private AnchorWalletData distributeLockChangeCharm(String uid, BigDecimal change, AnchorCharmLogData logData) {
        long startTime = System.currentTimeMillis();
        try (DistributeLock lock = new DistributeLock(getLockKey(uid))) {
            lock.lock();
            long lockTime = System.currentTimeMillis();
            AnchorWalletData walletData = anchorWalletDao.getWallet(uid);
            if (change.compareTo(BigDecimal.ZERO) < 0) {
                BigDecimal freezeCharm = freezeCharmDao.getTotalFreezeCharm(uid);
                BigDecimal charmBalance = logData.getLogType() != -9 ? walletData.getCharmBalance().subtract(freezeCharm) : walletData.getCharmBalance();
                if (charmBalance.compareTo(change.abs()) < 0) {
                    throw new CommonException(HttpCode.INSUFFICIENT_BALANCE);
                }
            }
            walletData.setCharmBalance(walletData.getCharmBalance().add(change));
            walletData.setMtime(DateHelper.getNowSeconds());
            anchorWalletDao.updateById(walletData);
            long endTime = System.currentTimeMillis();
            logger.info("change charm balance. uid={} aid={} change={} logType={} updateCost={} totalCost={}",
                    uid, logData.getAid(), change, logData.getLogType(), endTime - lockTime, endTime - startTime);
            return walletData;
        }
    }

    /**
     * 乐观锁锁操作魅力值余额，【过渡时需同一时间切换】
     */
    private AnchorWalletData optimisticChangeCharm(String uid, BigDecimal change, AnchorCharmLogData logData) {
        int retryCount = 0;
        while (retryCount < 1000) {
            AnchorWalletData walletData = anchorWalletDao.getWallet(uid);
            if (change.compareTo(BigDecimal.ZERO) < 0) {
                BigDecimal freezeCharm = freezeCharmDao.getTotalFreezeCharm(uid);
                BigDecimal charmBalance = logData.getLogType() != -9 ? walletData.getCharmBalance().subtract(freezeCharm) : walletData.getCharmBalance();
                if (charmBalance.compareTo(change.abs()) < 0) {
                    throw new CommonException(HttpCode.INSUFFICIENT_BALANCE);
                }
            }
            BigDecimal charmBalance = walletData.getCharmBalance().add(change);
            LambdaUpdateWrapper<AnchorWalletData> updateWrapper = new LambdaUpdateWrapper<>();
            updateWrapper.eq(AnchorWalletData::getUid, uid)
                    .eq(AnchorWalletData::getVersion, walletData.getVersion())
                    .set(AnchorWalletData::getCharmBalance, charmBalance)
                    .set(AnchorWalletData::getVersion, walletData.getVersion() + 1)
                    .set(AnchorWalletData::getMtime, DateHelper.getNowSeconds());
            if (anchorWalletDao.update(updateWrapper)) {
                walletData.setCharmBalance(charmBalance);
                return walletData;
            } else {
                retryCount++;
                logger.info("optimistic change charm failed. uid={} retryCount={}", uid, retryCount);
            }
        }
        logger.error("optimistic change charm failed. uid={} retryCount={}", uid, retryCount);
        throw new CommonException(HttpCode.SERVER_ERROR);
    }

    public void saveCharmLog(AnchorCharmLogData logData) {
        anchorCharmLogDao.insert(logData);
        // 魅力值监控
        charmMonitorService.charmChange(logData);
        // 上报数数
        AnchorCharmLogEvent event = new AnchorCharmLogEvent();
        FamilyData familyData = familyDao.selectByIdFromCache(logData.getFamilyId());
        event.setFamily_id(familyData != null ? familyData.getRid() : 0);
        event.setUid(logData.getUid());
        event.setAid(logData.getAid());
        event.setRoom_id(logData.getRoomId());
        event.setCharm_change(logData.getChange().doubleValue());
        event.setCharm_balance(logData.getBalance().doubleValue());
        event.setCharm_reward(logData.getReward() == null ? 0 : logData.getReward());
        event.setCharm_log_type(logData.getLogType());
        event.setTitle(logData.getTitle());
        event.setDesc(logData.getDesc());
        event.setCtime(logData.getCtime());
        eventReport.track(new EventDTO(event));
        if (logData.getLogType() == CharmLogTypeEnum.LIVE_HOST_BASIC_SALARY.getLogType()) {
            saveTaskCharmLogData(logData);
        }
        if (ServerConfig.isNotProduct()) {
            if (logData.getLogType() == CharmLogTypeEnum.HOST_EARNINGS.getLogType() || logData.getLogType() == CharmLogTypeEnum.INVITE_AGENT_EARNINGS.getLogType()) {
                commissionStatDao.addCommissionInRedis(logData.getAid(), logData.getUid(), logData.getLogType(), logData.getChange());
            }
        }
    }

    private void saveTaskCharmLogData(AnchorCharmLogData logData) {
        try {
            TaskCharmLogData data = new TaskCharmLogData();
            BeanUtils.copyProperties(logData, data, "id");
            taskCharmLogDao.insert(data);
        } catch (Exception e) {
            logger.error("saveTaskCharmLogData error. {}", e.getMessage(), e);
        }
    }

    /**
     * 计算代理佣金收入
     *
     * @param familyId      家族id
     * @param aid           礼物接收者
     * @param change        主播收入
     * @param processedRate 已处理的佣金比率
     * @param firstAgent    是否是一级代理
     * @param uid           礼物发送者
     */
    public void agentBrokerageIncome(int familyId, String aid, BigDecimal change, BigDecimal processedRate, boolean firstAgent, String uid) {
        if (0 == familyId) {
            return;
        }
        if (firstAgent) {
            // 公会收入
            familyDailyIncomeDao.incrFamilyIncome(familyId, change.intValue());
        }
        FamilyData familyData = familyDao.selectByIdFromCache(familyId);
        BigDecimal agentRate;
        if (SPECIAL_FAMILY_ID.containsKey(familyId)) {
            agentRate = SPECIAL_FAMILY_ID.get(familyId);
        } else if (!ObjectUtils.isEmpty(familyData.getRewardLevel())) {
            agentRate = findAgentRate(familyData.getRewardLevel());
        } else {
            // 代理近30日收益比率
            agentRate = calcAgentRate(anchorWalletService.getAllAgentIncome(familyId));
        }
        // 代理实际收益比率
        BigDecimal actualAgentRate = agentRate.subtract(processedRate);
        logger.info("agentBrokerageIncome familyId={} aid={} change={} actualAgentRate={} processedRate={}", familyId, aid, change, actualAgentRate, processedRate);
        if (actualAgentRate.compareTo(BigDecimal.ZERO) <= 0) {
            // 该代理无法获得分成，寻找上一级代理
            if (familyData.getPid() > 0 && processedRate.compareTo(AGENT_RATE[AGENT_RATE.length - 1]) < 0) {
                agentBrokerageIncome(familyData.getPid(), familyData.getOwnerUid(), change, processedRate, false, uid);
            }
            return;
        }
        // 执行代理收入
        changeCharm(familyData.getOwnerUid(), change.multiply(actualAgentRate), CharmLogTypeEnum.getLogData(familyId, aid, firstAgent ? CharmLogTypeEnum.HOST_EARNINGS : CharmLogTypeEnum.INVITE_AGENT_EARNINGS));
        // 代理挖猎防刷优化
        // 1. 代理打赏给该代理的下级主播，给该主播的代理分成，不给代理的上级代理分成。
        // 2. 绑定关系建立时，如果判断主播和该主播的代理IP地址相同，给该主播的代理分成，不给代理的上级代理分成。
        if (firstAgent && (familyData.getOwnerUid().equals(uid) || blockAgentUserRedis.getAllBlackAgentUser().contains(aid))) {
            logger.info("skip agentBrokerageIncome. uid={} aid={} change={}", uid, aid, change);
            return;
        }
        // 迭代上级代理收益
        processedRate = processedRate.add(actualAgentRate);
        if (familyData.getPid() > 0 && processedRate.compareTo(AGENT_RATE[AGENT_RATE.length - 1]) < 0) {
            agentBrokerageIncome(familyData.getPid(), familyData.getOwnerUid(), change, processedRate, false, uid);
        }
    }

    /**
     * 获取所有子代理包括代理主播近30日收益
     */
    @Cacheable(value = "cache", cacheManager = CaffeineCacheConfig.EXPIRE_10M_AFTER_WRITE,
            key = "#root.method.name+T(org.springframework.util.StringUtils).arrayToDelimitedString(args,'-')")
    public long getAllAgentIncome(int familyId) {
        int startDate = Integer.parseInt(DateSupport.yyyyMMdd(DateSupport.ARABIAN.getDayOffset(-29)));
        int endDate = Integer.parseInt(DateSupport.yyyyMMdd(DateSupport.ARABIAN.getDayOffset(0)));
        Set<Integer> allAgent = familyDao.findAllAgent(familyId);
        long familyIncome = familyDailyIncomeDao.getFamilyIncome(allAgent, startDate, endDate);
        if (ServerConfig.isNotProduct()) {
            logger.info("getAllAgentIncome. familyId={} familyIncome={} allAgent={}", familyId, familyIncome, allAgent);
        }
        return familyIncome;
    }

    public BigDecimal findAgentRate(String rewardLevel) {
        for (int i = 0; i < AGENT_LEVEL.length; i++) {
            if (AGENT_LEVEL[i].equals(rewardLevel)) {
                return AGENT_RATE[i];
            }
        }
        return BigDecimal.ZERO;
    }


    public String getNextRewardLevel(String rewardLevel) {
        for (int i = 0; i < AGENT_LEVEL.length; i++) {
            if (AGENT_LEVEL[i].equals(rewardLevel)) {
                return AGENT_LEVEL[Math.min(i + 1, AGENT_LEVEL.length - 1)];
            }
        }
        return "";
    }

    /**
     * 计算佣金比率档位
     */
    public BigDecimal calcAgentRate(long totalIncome) {
        for (int i = AGENT_INCOME.length - 1; i >= 0; i--) {
            if (totalIncome >= AGENT_INCOME[i]) {
                return AGENT_RATE[i];
            }
        }
        return BigDecimal.ZERO;
    }

    /**
     * 获取代理等级
     */
    public String getAgentLevel(long totalIncome) {
        for (int i = AGENT_INCOME.length - 1; i >= 0; i--) {
            if (totalIncome >= AGENT_INCOME[i]) {
                return AGENT_LEVEL[i];
            }
        }
        return AGENT_LEVEL[0];
    }

    /**
     * 扣除魅力值
     *
     * @param uid    主播uid
     * @param change 扣除的魅力值
     */
    public AnchorWalletData reduceCharm(String uid, int change, AnchorCharmLogData logData) {
        if (change >= 0) {
            throw new CommonException(HttpCode.PARAM_ERROR);
        }
        return changeCharm(uid, new BigDecimal(change), logData);
    }

    /**
     * 美金转账
     *
     * @param uid    发送方uid
     * @param aid    接收方uid
     * @param amount 需要转账的美金数额
     */
    public AnchorWalletData transferUsd(String uid, String aid, int amount, Integer familyId) {
        if (amount <= 0) {
            throw new CommonException(HttpCode.PARAM_ERROR);
        }
        BigDecimal costCharm = new BigDecimal(amount * USD_TO_CHARM);
        ActorData fromActor = actorDao.getActorDataFromCache(uid);
        ActorData toActor = actorDao.getActorDataFromCache(aid);
        // 转账方扣除魅力值
        CharmLogTypeEnum transferToEnum = CharmLogTypeEnum.TRANSFER_TO_FAMILY_OWNER;
        AnchorCharmLogData logData = new AnchorCharmLogData();
        logData.setFamilyId(familyId);
        logData.setUid(uid);
        logData.setAid(aid);
        logData.setLogType(transferToEnum.logType);
        logData.setChange(costCharm.negate());
        logData.setReward(0);
        logData.setTitle(transferToEnum.title);
        logData.setTitleAr(transferToEnum.titleAr);
        logData.setDesc(transferToEnum.desc.formatted(toActor.getName(), amount));
        logData.setDescAr(transferToEnum.descAr.formatted(toActor.getName(), amount));
        logData.setCtime(DateHelper.getNowSeconds());
        AnchorWalletData walletData = changeCharm(uid, costCharm.negate(), logData);
        // 接收方增加魅力值
        CharmLogTypeEnum transferFromEnum = CharmLogTypeEnum.TRANSFER_FROM_FAMILY_MEMBER;
        AnchorCharmLogData toLog = new AnchorCharmLogData();
        toLog.setFamilyId(familyId);
        toLog.setUid(aid);
        toLog.setAid(uid);
        toLog.setLogType(transferFromEnum.logType);
        toLog.setChange(costCharm);
        toLog.setReward(0);
        toLog.setTitle(transferFromEnum.title);
        toLog.setTitleAr(transferFromEnum.titleAr);
        toLog.setDesc(transferFromEnum.desc.formatted(fromActor.getName(), amount));
        toLog.setDescAr(transferFromEnum.descAr.formatted(fromActor.getName(), amount));
        toLog.setCtime(DateHelper.getNowSeconds());
        mqSenderService.asyncCharm(new AsyncCharmDTO(aid, costCharm, toLog));
        return walletData;
    }

    /**
     * 美金转钻石
     *
     * @param uid    主播uid
     * @param amount 需要转换为钻石的美金数额
     */
    public AnchorWalletData usdToDiamonds(String uid, int amount, Integer familyId) {
        if (amount <= 0) {
            throw new CommonException(HttpCode.PARAM_ERROR);
        }
        BigDecimal costCharm = new BigDecimal(amount * USD_TO_CHARM);
        int convertDiamonds = amount * getUsdToDiamonds();
        CharmLogTypeEnum charmTypeEnum = CharmLogTypeEnum.CONVERT_TO_DIAMONDS;
        AnchorCharmLogData logData = new AnchorCharmLogData(familyId, uid, charmTypeEnum.logType, charmTypeEnum.title, charmTypeEnum.titleAr, charmTypeEnum.desc.formatted(convertDiamonds, amount), charmTypeEnum.descAr.formatted(convertDiamonds, amount));
        AnchorWalletData walletData = changeCharm(uid, costCharm.negate(), logData);
        // 异步打钻
        MoneyActionType actionType = MoneyActionType.USD_TO_DIAMONDS;
        MoneyDetailReq moneyDetailReq = new MoneyDetailReq();
        moneyDetailReq.setRandomId();
        moneyDetailReq.setUid(uid);
        moneyDetailReq.setAtype(actionType.getActionType());
        moneyDetailReq.setChanged(convertDiamonds);
        moneyDetailReq.setTitle(logData.getTitle());
        moneyDetailReq.setDesc(logData.getDesc());
        moneyDetailReq.setMtime(DateHelper.getNowSeconds());
        mqSenderService.asyncChargeDiamonds(moneyDetailReq);
        // 用户充值mq
        sendUserRechargeToMq(uid, "usdToDiamonds:" + logData.getId(), actionType.getActionType(), "usdConvertToDiamonds", amount, convertDiamonds);
        logger.info("usd to diamonds. uid={} amount={} convertDiamonds={}", uid, amount, convertDiamonds);
        return walletData;
    }

    /**
     * 扣除美金
     *
     * @param uid    主播uid
     * @param amount 需要扣除的美金数额
     */
    public AnchorWalletData reduceUsd(String uid, int amount, AnchorCharmLogData logData) {
        if (amount <= 0) {
            throw new CommonException(HttpCode.PARAM_ERROR);
        }
        BigDecimal costCharm = new BigDecimal(amount * USD_TO_CHARM);
        return changeCharm(uid, costCharm.negate(), logData);
    }

    /**
     * 美金提现，运营系统
     *
     * @param reviewData 提现审核信息
     */
    public AnchorWalletData usdWithdrawal(WithdrawReviewData reviewData) {
        if (reviewData.getAmount() <= 0) {
            throw new CommonH5Exception(HttpCode.PARAM_ERROR);
        }
        long startTime = System.currentTimeMillis();
        AnchorWalletData walletData;
        AnchorCharmLogData logData = CharmLogTypeEnum.getLogData(reviewData.getFamilyId(), reviewData.getUid(), CharmLogTypeEnum.WITHDRAWAL);
        BigDecimal costCharm = new BigDecimal(reviewData.getAmount() * USD_TO_CHARM).negate();
        try (DistributeLock lock = new DistributeLock(getLockKey(reviewData.getUid()))) {
            lock.lock();
            long lockTime = System.currentTimeMillis();
            walletData = anchorWalletDao.getWallet(reviewData.getUid());
            BigDecimal freezeCharm = freezeCharmDao.getTotalFreezeCharm(reviewData.getUid());
            // 可提现魅力值 = 魅力值余额 - 冻结魅力值
            BigDecimal charmBalance = walletData.getCharmBalance().subtract(freezeCharm);
            if (charmBalance.compareTo(costCharm.abs()) < 0) {
                throw new CommonException(HttpCode.INSUFFICIENT_BALANCE);
            }
            anchorWalletService.usdWithdrawal(costCharm, reviewData, logData, walletData);
            long endTime = System.currentTimeMillis();
            logger.info("change charm balance. uid={} aid={} change={} logType={} updateCost={} totalCost={}", reviewData.getUid(), logData.getAid(), costCharm, logData.getLogType(), endTime - lockTime, endTime - startTime);
        }
        saveCharmLog(logData);
        return walletData;
    }

    @Transactional(value = WahoMySQLBean.WAHO_TRANSACTION)
    public void usdWithdrawal(BigDecimal costCharm, WithdrawReviewData reviewData, AnchorCharmLogData logData, AnchorWalletData walletData) {
        String uid = reviewData.getUid();
        // 提现审核
        withdrawReviewDao.insert(reviewData);
        walletData.setCharmBalance(walletData.getCharmBalance().add(costCharm));
        walletData.setMtime(DateHelper.getNowSeconds());
        anchorWalletDao.updateById(walletData);
        logData.setUid(uid);
        logData.setChange(costCharm);
        logData.setBalance(walletData.getCharmBalance());
        logData.setDesc(logData.getDesc().formatted(reviewData.getId()));
        logData.setDescAr(logData.getDescAr().formatted(reviewData.getId()));
    }

    /**
     * C2C提现
     *
     * @param orderData 提现订单
     */
    public AnchorWalletData withdrawal(WithdrawalOrderData orderData) {
        int costCharm = orderData.getCharm() + orderData.getHandlingFee() + orderData.getTraderHandlingFee();
        if (costCharm <= 0) {
            throw new CommonH5Exception(HttpCode.PARAM_ERROR);
        }
        ActorData trader = actorDao.getActorDataFromCache(orderData.getAid());
        String traderName = trader != null ? trader.getName() : "";
        String traderRid = trader != null ? trader.getRid() + "" : "";
        CharmLogTypeEnum typeEnum = CharmLogTypeEnum.C2C_WITHDRAWAL_DEDUCT;
        AnchorCharmLogData logData = new AnchorCharmLogData(0,
                orderData.getUid(),
                typeEnum.logType,
                typeEnum.title,
                typeEnum.titleAr,
                String.format(typeEnum.desc, costCharm, traderName, traderRid),
                String.format(typeEnum.descAr, costCharm, traderName, traderRid));
        AnchorWalletData walletData = changeCharm(orderData.getUid(), new BigDecimal(-costCharm), logData);
        // 生成提现订单
        withdrawalOrderDao.insert(orderData);
        return walletData;
    }

    /**
     * 美金提现失败，返还美金到余额，运营系统
     *
     * @param uid    用户uid
     * @param amount 退还金额 单位：美元
     */
    public void refundUsd(String uid, int amount, int familyId) {
        if (amount <= 0) {
            throw new CommonH5Exception(HttpCode.PARAM_ERROR);
        }
        BigDecimal addCharm = new BigDecimal(amount * USD_TO_CHARM);
        AnchorCharmLogData logData = CharmLogTypeEnum.getLogData(familyId, uid, CharmLogTypeEnum.WITHDRAWAL_FAILED);
        // 异步加魅力值
        mqSenderService.asyncCharm(new AsyncCharmDTO(uid, addCharm, logData));
    }

    /**
     * 运营系统操作魅力值
     *
     * @param aid    接收方uid
     * @param amount 需要增加的魅力值数额
     */
    public void operateCharm(String aid, long amount, String desc, int familyId) {
        if (amount == 0 || amount > 20000000) {
            throw new CommonH5Exception(1, "参数错误，金额不能为0或大于20000000");
        }
        CharmLogTypeEnum typeEnum = CharmLogTypeEnum.TRANSFER_FROM_ADMIN;
        changeCharm(aid, new BigDecimal(amount), new AnchorCharmLogData(familyId, aid, typeEnum.logType, typeEnum.title, typeEnum.titleAr, desc, desc));
        AsyncUtils.execute(() -> {
            if (amount > 0) {
                sendAdminAddCharmNotice(aid, desc);
            }
        });
    }

    /**
     * 主播美金给用户或币商充值
     * 被充值的用户，根据用户身份是否为币商判断：
     * a. 如果是非币商身份，则充值到用户钻石余额内
     * b. 如果为币商身份，则充值到币商钻石余额内
     *
     * @param uid    主播用户
     * @param aid    被充值用户
     * @param amount 魅力值数额
     * @return 主播钱包
     */
    public AnchorWalletData newUsdSellDiamonds(String uid, String aid, int amount, int familyId) {
        if (amount <= 0) {
            throw new CommonH5Exception(HttpCode.PARAM_ERROR);
        }
        float usdAmount = (float) amount / USD_TO_CHARM;
        int sellDiamonds = (int) Math.ceil(usdAmount * getUsdBuyDiamonds());
        int sellerDiamonds = (int) Math.ceil(usdAmount * USD_COIN_SELLER_BUY_DIAMONDS);
        BigDecimal costCharm = new BigDecimal(amount);
        CoinSellerData coinSellerData = coinSellerDao.getById(aid);
        String desc;
        String descAr;
        if (null != coinSellerData && 1 == coinSellerData.getStatus()) {
            if (coinSellerData.getMerchantType() == 2) {
                throw new CommonH5Exception(HttpCode.SECOND_LEVEL_RECHARGE_AGENT_LIMIT);
            }
            desc = "Selling %s diamonds to %s seller costs %s Charm.".formatted(sellerDiamonds, actorDao.getActorDataFromCache(aid).getRid(), costCharm);
            descAr = "بيع %s ألماسة للبائع %s يكلف %s نقطة جاذبية.".formatted(sellerDiamonds, actorDao.getActorDataFromCache(aid).getRid(), costCharm);
        } else {
            desc = "Selling %s diamonds to %s costs %s Charm.".formatted(sellDiamonds, actorDao.getActorDataFromCache(aid).getRid(), costCharm);
            descAr = "بيع %s ألماسة إلى %s يكلف %s نقطة جاذبية".formatted(sellDiamonds, actorDao.getActorDataFromCache(aid).getRid(), costCharm);
        }
        CharmLogTypeEnum typeEnum = CharmLogTypeEnum.SELLING_DIAMONDS;
        AnchorCharmLogData logData = new AnchorCharmLogData(familyId, aid, typeEnum.logType, typeEnum.title, typeEnum.titleAr, desc, descAr);
        // 1. 主播减魅力值
        AnchorWalletData walletData = changeCharm(uid, costCharm.negate(), logData);
        // 2. 用户或币商充值
        if (null != coinSellerData && 1 == coinSellerData.getStatus()) {
            try {
                coinExchangeService.usdRechargeToCoinSeller(aid, uid, new BigDecimal(usdAmount).setScale(2, RoundingMode.FLOOR).floatValue(), sellerDiamonds);
            } catch (CommonException ignored) {
            } catch (Exception e) {
                // 退还魅力值
                AnchorCharmLogData rollbackLogData = new AnchorCharmLogData(familyId, aid, CharmLogTypeEnum.TRANSFER_FROM_ADMIN.logType, "[Refund]Selling diamonds", desc);
                mqSenderService.asyncCharm(new AsyncCharmDTO(uid, costCharm, rollbackLogData));
                JSONObject jsonObject = new JSONObject();
                jsonObject.put("uid", aid);
                jsonObject.put("aid", uid);
                jsonObject.put("usdAmount", usdAmount);
                jsonObject.put("sellerDiamonds", sellerDiamonds);
                jsonObject.put("e", e.getMessage());
                monitorSender.info("waho_pay", "主播用户给币商充值异常", jsonObject.toJSONString());
            }
        } else {
            ActorData hostActor = actorDao.getActorDataFromCache(uid);
            MoneyActionType actionType = MoneyActionType.USD_SELLER_RECHARGE;
            MoneyDetailReq moneyDetailReq = new MoneyDetailReq();
            moneyDetailReq.setRandomId();
            moneyDetailReq.setUid(aid);
            moneyDetailReq.setFromUid(uid);
            moneyDetailReq.setAtype(actionType.getActionType());
            moneyDetailReq.setChanged(sellDiamonds);
            moneyDetailReq.setTitle(actionType.getTitle());
            moneyDetailReq.setDesc(actionType.getDesc().formatted(sellDiamonds, hostActor.getStrRid()));
            moneyDetailReq.setMtime(DateHelper.getNowSeconds());
            mqSenderService.asyncChargeDiamonds(moneyDetailReq);
            // 发送官方通知给用户
            sendBuyDiamondsOfficialData(aid, sellDiamonds, hostActor.getStrRid());
            // 用户充值mq
            sendUserRechargeToMq(aid, "usdSellDiamonds:" + logData.getId(), actionType.getActionType(), "diamondsSoldByOtherUsers", usdAmount, sellDiamonds);
        }
        logger.info("usd sell diamonds. uid={} aid={} amount={} buyDiamonds={}", uid, aid, amount, sellDiamonds);
        return walletData;
    }

    private void sendBuyDiamondsOfficialData(String toUid, int amount, String fromRid) {
        int slang = actorDao.getActorDataFromCache(toUid).getSlang();
        OfficialData officialData = new OfficialData();
        officialData.setTo_uid(toUid);
        officialData.setTitle(SLangType.ENGLISH == slang ? BUY_OFFICIAL_TITLE : BUY_OFFICIAL_TITLE_AR);
        officialData.setBody((SLangType.ENGLISH == slang ? BUY_OFFICIAL_BODY : BUY_OFFICIAL_BODY_AR).formatted(WalletUtils.diamondsForDisplay(amount), fromRid));
        officialData.setValid(1);
        officialData.setAtype(18);
        officialData.setNews_type(0);
        officialData.setCtime(DateHelper.getNowSeconds());
        officialData.setAct(SLangType.ENGLISH == slang ? ACTION : ACTION_AR);
        officialMsgService.officialMsgPush(officialData);
    }

    private void sendUserRechargeToMq(String uid, String orderId, int rechargeType, String channel, double rechargeMoney, int changed) {
        RechargeInfo rechargeInfo = new RechargeInfo();
        rechargeInfo.setUid(uid);
        rechargeInfo.setOrderId(orderId);
        rechargeInfo.setRechargeMoney(rechargeMoney);
        rechargeInfo.setRechargeDiamond(changed);
        rechargeInfo.setRechargeTime(DateHelper.getNowSeconds());
        rechargeInfo.setRechargeType(rechargeType);
        rechargeInfo.setSubType("");
        rechargeInfo.setRechargeItem(channel);
        mqSenderService.sendUserRechargeToMq(channel, rechargeInfo);
    }

    private void sendAdminAddCharmNotice(String toUid, String desc) {
        int slang = actorDao.getActorDataFromCache(toUid).getSlang();
        OfficialData officialData = new OfficialData();
        officialData.setTo_uid(toUid);
        officialData.setTitle(SLangType.ENGLISH == slang ? BUY_OFFICIAL_TITLE : BUY_OFFICIAL_TITLE_AR);
        officialData.setBody(desc);
        officialData.setValid(1);
        officialData.setAtype(0);
        officialData.setUrl(ServerConfig.isProduct() ? "https://statics.waho.live/income_v3/" : "https://api.opswaho.com/income_v3/");
        officialData.setNews_type(0);
        officialData.setCtime(DateHelper.getNowSeconds());
        officialData.setAct(SLangType.ENGLISH == slang ? ACTION : ACTION_AR);
        officialMsgService.officialMsgPush(officialData);
    }
}
