package com.quhong.room.mic;

import com.quhong.analysis.EventDTO;
import com.quhong.analysis.EventReport;
import com.quhong.analysis.NewMicOperateEvent;
import com.quhong.analysis.RoomVideoOperateEvent;
import com.quhong.constant.AchievementConstant;
import com.quhong.constant.AnchorTaskConstant;
import com.quhong.core.concurrency.BaseTaskFactory;
import com.quhong.core.concurrency.tasks.Task;
import com.quhong.core.distribution.DistributeLock;
import com.quhong.core.utils.DateHelper;
import com.quhong.dailyTask.DailyTaskService;
import com.quhong.dailyTask.UserLevelTaskService;
import com.quhong.data.ActorData;
import com.quhong.data.UserLevelTaskData;
import com.quhong.enums.ApiResult;
import com.quhong.enums.HttpCode;
import com.quhong.enums.RoomMicHttpCode;
import com.quhong.enums.UserLevelConstant;
import com.quhong.exception.CommonException;
import com.quhong.image.ImageUrlGenerator;
import com.quhong.mongo.dao.*;
import com.quhong.mongo.data.LivePkData;
import com.quhong.mongo.data.MongoRoomData;
import com.quhong.mq.CommonData;
import com.quhong.mq.MqSenderService;
import com.quhong.msg.obj.RoomMicInfoObject;
import com.quhong.msg.obj.RoomMicUserObject;
import com.quhong.msg.room.InviteMicPushMsg;
import com.quhong.msg.room.RemoveSeatPushMsg;
import com.quhong.msg.room.RoomNotificationMsg;
import com.quhong.mysql.dao.RoomMicDao;
import com.quhong.mysql.dao.RoomMicLogDao;
import com.quhong.mysql.data.RoomMicData;
import com.quhong.mysql.data.RoomMicListData;
import com.quhong.mysql.data.RoomMicLogData;
import com.quhong.redis.CharmCounterRedis;
import com.quhong.redis.CheatGiftRedis;
import com.quhong.redis.RoomMicRedis;
import com.quhong.room.RoomWebSender;
import com.quhong.room.api.ThirdPartApiRedis;
import com.quhong.room.api.zego.ZegoApi;
import com.quhong.room.cache.RoomActorCache;
import com.quhong.room.data.RoomActorDetailData;
import com.quhong.room.redis.RoomPlayerRedis;
import com.quhong.utils.ActorUtils;
import com.quhong.utils.RoomUtils;
import com.quhong.vo.RoomMicListVo;
import jakarta.annotation.Resource;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Component;
import org.springframework.util.ObjectUtils;
import org.springframework.util.StringUtils;

import java.util.*;
import java.util.concurrent.TimeUnit;

/**
 * 麦位service，使用此类需要引入analysis
 */
@Lazy
@Component
public class RoomMicService {
    private static final Logger logger = LoggerFactory.getLogger(RoomMicService.class);
    private static final long MUTE_MAX_SECONDS = TimeUnit.DAYS.toSeconds(3);
    public static final int LIVE_MIC_SIZE = 3;
    private static final Set<Integer> SUPPORTED_MIC_SIZE = new HashSet<>(Arrays.asList(2, 5, 8, 9, 12, 15));

    @Autowired
    private RoomMicDao roomMicDao;
    @Autowired
    private RoomMicRedis roomMicRedis;
    @Autowired
    private MongoRoomDao roomDao;
    @Autowired
    private RoomMicLogDao roomMicLogDao;
    @Autowired
    private ActorDao actorDao;
    @Autowired
    private RoomWebSender roomWebSender;
    @Autowired
    private DailyTaskService dailyTaskService;
    @Autowired
    private UserLevelTaskService userLevelTaskService;
    @Autowired
    private RoomPlayerRedis roomPlayerRedis;
    @Resource
    private ZegoApi zegoApi;
    @Resource
    private ThirdPartApiRedis thirdPartApiRedis;
    @Autowired(required = false)
    private EventReport eventReport;
    @Resource
    private CharmCounterRedis charmCounterRedis;
    @Resource
    private MqSenderService mqSenderService;
    @Resource
    private RoomActorCache roomActorCache;
    @Resource
    private TeamPkDao teamPkDao;
    @Resource
    private LivePkDao livePkDao;


    public RoomMicListData getRoomMicList(String roomId) {
        return getRoomMicList(roomId, roomDao.getDataFromCache(roomId).getMicSize());
    }

    public RoomMicListData getRoomMicList(String roomId, int micSize) {
        List<RoomMicData> list = roomMicDao.getRoomMicList(roomId, micSize == 0 ? 8 : micSize);
        // 添加麦位禁止
        fillForbidAndMute(list);
        int version = roomMicDao.getVersion(roomId);
        // 麦位版本从1开始
        if (0 == version) {
            incVersion(roomId);
            version = 1;
        }
        RoomMicListData listData = new RoomMicListData();
        listData.setList(list);
        listData.setVersion(version);
        listData.setRoomId(roomId);
        return listData;
    }

    /**
     * 变更麦位大小
     *
     * @param roomData   房间对象
     * @param fromActor  操作用户
     * @param micSize    新设置的麦位大小
     * @param beforeSize 之前的麦位大小
     * @return 麦位信息
     */
    public RoomMicListData getAndResizeRoomMicList(MongoRoomData roomData, ActorData fromActor, int micSize, int beforeSize, int kickReason) {
        if (micSize < beforeSize) {
            // 麦位数量由多切换到少的时候，末尾减少的麦位用户将被移除下麦。移下麦位的用户端弹出踢下麦提示弹窗。
            RoomMicListData roomMicList = getRoomMicList(roomData.getRid(), beforeSize);
            for (int i = micSize; i < roomMicList.getList().size(); i++) {
                RoomMicData micData = roomMicList.getList().get(i);
                if (null == micData) {
                    continue;
                }
                if (!ObjectUtils.isEmpty(micData.getUid())) {
                    // 麦位移除消息
                    RemoveSeatPushMsg msg = new RemoveSeatPushMsg();
                    msg.setFrom_name(fromActor.getName());
                    msg.setFrom_Img(ImageUrlGenerator.generateRoomUserUrl(fromActor));
                    msg.setReason(kickReason);
                    roomWebSender.sendPlayerWebMsg(roomData.getRid(), fromActor.getUid(), micData.getUid(), msg, true);
                    // 空间不足下麦处理
                    downRoomMic(roomMicList, micData.getUid(), false, true, 6, roomData);
                }
            }
        }
        RoomMicListData roomMicList = getRoomMicList(roomData.getRid(), micSize);
        // 增加版本号
        roomMicList.setVersion(roomMicDao.incVersion(roomData.getRid()));
        return roomMicList;
    }

    private void fillForbidAndMute(List<RoomMicData> list) {
        for (RoomMicData micData : list) {
            fillForbidAndMute(micData);
        }
    }

    private void fillForbidAndMute(RoomMicData micData) {
        if (DateHelper.getNowSeconds() - micData.getMuteTime() > MUTE_MAX_SECONDS) {
            micData.setMute(0);
        }
    }

    /**
     * 房主直播房自动上麦
     */
    public RoomMicListVo autoUpRoomMic(MongoRoomData roomData, String uid, int micType) {
        if (com.quhong.enums.RoomConstant.LIVE_ROOM_MODE != roomData.getRoomMode()) {
            return null;
        }
        String roomId = roomData.getRid();
        if (!RoomMemberDao.isRoomHost(roomId, uid)) {
            return null;
        }
        try (DistributeLock lock = new DistributeLock(createLockKey(roomId))) {
            lock.lock();
            // 处理麦位信息
            checkAndResizeGuestLive(roomData, uid);
            RoomMicListData listData = getRoomMicList(roomId, roomData.getMicSize());
            long upMicCount = listData.getList().stream()
                    .filter(roomMicData -> !ObjectUtils.isEmpty(roomMicData.getUid()))
                    .count();
            if (upMicCount == 0) {
                listData = doUpRoomMic(listData, uid, 0, "", roomData, true, true, 0, micType).getData();
                roomMicRedis.setUpMicType(uid, 1);
            }
            return getRoomMicListVo(roomData, listData);
        } catch (Exception e) {
            logger.error("op up room mic error. roomId={} uid={} {}", roomId, uid, e.getMessage(), e);
        }
        return null;
    }

    /**
     * 直播房访客自动上麦
     */
    public void visitorAutoUpRoomMic(MongoRoomData roomData, String uid, int micType) {
        if (com.quhong.enums.RoomConstant.LIVE_ROOM_MODE != roomData.getRoomMode()) {
            return;
        }
        String roomId = roomData.getRid();
        try (DistributeLock lock = new DistributeLock(createLockKey(roomId))) {
            lock.lock();
            // 连麦等功能麦位自动扩容
            checkAndResizeGuestLive(roomData, uid);
            RoomMicListData listData = getRoomMicList(roomId, roomData.getMicSize());
            int position = searchPosition(listData.getList(), -1, uid, true);
            if (position == -1 || roomMicRedis.getMicActorCount(roomId) == 3) {
                throw new CommonException(RoomMicHttpCode.LIVE_NO_IDLE_MICS);
            }
            ApiResult<RoomMicListData> apiResult = doUpRoomMic(listData, uid, position, "", roomData, true, true, 0, micType);
            if (apiResult.isError()) {
                throw new CommonException(apiResult.getCode());
            }
            roomMicRedis.setUpMicType(uid, 1);
        } catch (CommonException e) {
            throw e;
        } catch (Exception e) {
            logger.error("visitorAutoUpRoomMic error. roomId={} uid={} {}", roomId, uid, e.getMessage(), e);
        }
    }

    /**
     * 新版live房间，保证房主自动上麦时在第一个麦位，如果1号麦被占用，则移动其他用户麦位
     *
     * @param micDataList 麦位列表
     */
    public void migrateMicList(List<RoomMicData> micDataList) {
        RoomMicData firstRoomMic = micDataList.get(0);
        if (StringUtils.isEmpty(firstRoomMic.getUid())) {
            return;
        }
        // 移动麦位
        for (int i = 0; i < LIVE_MIC_SIZE; i++) {
            RoomMicData roomMicData = micDataList.get(i);
            if (StringUtils.isEmpty(roomMicData.getUid())) {
                roomMicData.setUid(firstRoomMic.getUid());
                roomMicData.setUpMicTime(firstRoomMic.getUpMicTime());
                roomMicData.setThirdPartId(firstRoomMic.getThirdPartId());
                roomMicData.setMuteTime(firstRoomMic.getMuteTime());
                roomMicData.setMute(firstRoomMic.getMute());
                roomMicData.setMicLock(0);
                roomMicData.setMtime(DateHelper.getNowSeconds());
                roomMicData.setVoiceType(0);
                roomMicDao.doUpdateRoomMic(roomMicData);
                break;
            }
        }
        firstRoomMic.setUid(null);
    }

    /**
     * 上麦查找空余位置
     */
    public int searchPosition(List<RoomMicData> list, int position, String uid, boolean invited) {
        if (position > -1 && position < list.size()) {
            RoomMicData micData = list.get(position);
            if (ObjectUtils.isEmpty(micData.getUid())) {
                return micData.getPosition();
            }
            if (micData.getUid().equals(uid)) {
                return micData.getPosition();
            }
        }
        for (RoomMicData micData : list) {
            if (micData.getMicLock() > 0 && !invited) {
                continue;
            }
            if (ObjectUtils.isEmpty(micData.getUid())) {
                return micData.getPosition();
            }
            if (micData.getUid().equals(uid)) {
                return micData.getPosition();
            }
        }
        return -1;
    }

    /**
     * @param micType 0audio 1video
     */
    public ApiResult<RoomMicListData> doUpRoomMic(RoomMicListData listData, String uid, int position,
                                                  String thirdPartId, MongoRoomData roomData, boolean pushMsg,
                                                  boolean autoUpMic, int opType, int micType) {
        logger.info("up room mic. position={} roomId={} uid={} thirdPartId={} opType={}", position, listData.getRoomId(), uid, thirdPartId, opType);
        downRoomMic(listData, uid, pushMsg, 3 != opType, 3 == opType ? 7 : 1, roomData);
        if (!autoUpMic) {
            if (!roomPlayerRedis.isActorInRoom(listData.getRoomId(), uid)) {
                logger.error("up room mic. actor not in any room. position={} roomId={} uid={}", position, listData.getRoomId(), uid);
                return ApiResult.getError(HttpCode.NOT_IN_ROOM);
            }
        }
        List<RoomMicData> list = listData.getList();
        RoomMicData micData = getMicData(list, position);
        if (micData == null) {
            logger.error("up room mic error. can not find. micCData. . position={} roomId={} uid={}", position, listData.getRoomId(), uid);
            return ApiResult.getError(HttpCode.PARAM_ERROR);
        }
        // 上麦后清除串流记录，防止误判
        thirdPartApiRedis.clearMicCrossTimes(listData.getRoomId(), uid);
        // 解决房间串流问题，判断用户在别的房间有没有上麦，如果已经上麦则强制下麦
        for (String roomId : roomMicDao.getRoomMicUserList(uid)) {
            if (listData.getRoomId().equals(roomId)) {
                if (downRoomMic(listData, uid, true, true, 1, roomData)) {
                    logger.error("mic mess! success down room mic, roomId={} uid={}", roomId, uid);
                }
                continue;
            }
            MongoRoomData otherRoomData = roomDao.findData(roomId);
            try (DistributeLock lock = new DistributeLock(createLockKey(roomId))) {
                lock.lock();
                if (downRoomMic(getRoomMicList(roomId, otherRoomData.getMicSize()), uid, true, true, 1, otherRoomData)) {
                    logger.error("mic mess! success down room mic, roomId={} otherRoomId={} uid={}", listData.getRoomId(), roomId, uid);
                }
            } catch (Exception e) {
                logger.error("force down room mic error, roomId={} otherRoomId={} uid={}", listData.getRoomId(), roomId, uid, e);
            }
        }
        micData.setUid(uid);
        micData.setUpMicTime(DateHelper.getNowSeconds());
        micData.setMicType(micType);
        // 重新上麦后默认为原声
        micData.setVoiceType(0);
        micData.setMicLock(0);
        micData.setVideoStatus(0);
        micData.setVideoOpTime(0);
        micData.setThirdPartId(thirdPartId == null ? "" : thirdPartId);
        if (opType == 1 || RoomUtils.isLiveRoom(listData.getRoomId())) {
            // 邀请上麦或连麦时取消麦位静音
            micData.setMute(0);
            roomMicDao.setMuteStatus(micData.getRoomId(), false);
        }
        int version = roomMicDao.updateRoomMic(micData);
        listData.setVersion(version);
        fillForbidAndMute(micData);
        if (pushMsg) {
            sendMicChange(listData.getRoomId(), autoUpMic ? null : uid);
        }
        // 上麦成功后记录上麦的房间
        roomMicDao.addRoomMicUser(listData.getRoomId(), uid);
        // 用户成就：演说家
        mqSenderService.sendUserAchievementToMq(new CommonData(uid, AchievementConstant.SPEECHER, 1));
        // 刷新麦位魅力值计数器清零时间
        if (roomData.getCharmCounter() == 1) {
            charmCounterRedis.expireCharmCounter(roomData, uid);
        }
        return new ApiResult<RoomMicListData>().ok(listData);
    }

    /**
     * 下麦处理
     *
     * @param listData     麦位列表
     * @param uid          需要下麦的用户uid
     * @param pushMsg      是否推送麦位变化消息
     * @param deleteStream 是否删除推流
     * @param downType     下麦操作类型：1为主动下麦，2为房主踢掉下麦，3为管理员踢掉下麦，4为离开房间下麦，5切换live模式时被踢下麦 6切换麦位类型时踢下麦 7切换麦位下麦
     * @param roomData     room数据
     * @return 下麦结果
     */
    public boolean downRoomMic(RoomMicListData listData, String uid, boolean pushMsg, boolean deleteStream, int downType, MongoRoomData roomData) {
        boolean doDown = false;
        for (RoomMicData micData : listData.getList()) {
            if (uid.equals(micData.getUid())) {
                int now = DateHelper.getNowSeconds();
                // 这两个值会在下麦时清零，所以要提前拿出来
                long upTime = micData.getUpMicTime();
                if (micData.getVideoStatus() == 1 && 0 != micData.getVideoOpTime()) {
                    RoomVideoOperateEvent operateEvent = new RoomVideoOperateEvent();
                    operateEvent.setRoom_id(micData.getRoomId());
                    operateEvent.setUid(uid);
                    operateEvent.setRoom_video_close_type(downType);
                    operateEvent.setRoom_video_open_time(micData.getVideoOpTime());
                    operateEvent.setRoom_video_close_type(now);
                    operateEvent.setRoom_video_time(now - micData.getVideoOpTime());
                    eventReport.track(new EventDTO(operateEvent));
                }
                doDownRoomMic(micData);
                // 写入日志
                roomMicLog(uid, micData.getRoomId(), upTime, micData.getPosition());
                if (upTime == 0) {
                    logger.error("upTime is zero. roomId={} uid={}", micData.getRoomId(), uid);
                } else {
                    int micTime = (int) (now - upTime);
                    // 下麦时，记录时间
                    dailyTaskService.roomMicTime(uid, micTime, true);
                    userLevelTaskService.upMicTask(new UserLevelTaskData(uid, UserLevelConstant.UP_MIC, micTime / 60));
                    mqSenderService.sendAnchorTaskToMq(new CommonData(uid, AnchorTaskConstant.UP_MIC, micTime / 60, micData.getRoomId()));
                    doDown = true;
                    if (deleteStream) {
                        zegoApi.deleteStream(micData.getRoomId(), uid, micData.getThirdPartId());
                    }
                    // 数数麦位互动事件
                    NewMicOperateEvent event = new NewMicOperateEvent();
                    event.setUid(uid);
                    event.setRoom_id(micData.getRoomId());
                    event.setMic_up_time((int) upTime);
                    event.setMic_down_time(now);
                    event.setMic_time(micTime);
                    int upMicType = roomMicRedis.getUpMicType(uid);
                    event.setMic_up_type(upMicType == 0 ? 1 : upMicType);
                    event.setMic_down_type(downType);
                    event.setRoom_type(roomData.getRoomMode() == 0 ? 1 : roomData.getRoomMode());
                    eventReport.track(new EventDTO(event));
                }
            }
        }
        listData.setVersion(roomMicDao.getVersion(listData.getRoomId()));
        // 切麦操作不进行麦位变化消息下发
        if (7 != downType && doDown) {
            if (pushMsg) {
                sendMicChange(listData.getRoomId(), uid);
            }
            // 下麦后删除上麦的标记
            roomMicDao.removeRoomMicUser(uid);
        }
        return doDown;
    }

    public void downAllRoomMics(String roomId, boolean pushMsg) {
        RoomMicListData listData = getRoomMicList(roomId);
        for (RoomMicData micData : listData.getList()) {
            if (StringUtils.isEmpty(micData.getUid())) {
                continue;
            }
            // 这两个值会在下麦时清零，所以要提前拿出来
            String uid = micData.getUid();
            long upTime = micData.getUpMicTime();
            doDownRoomMic(micData);
            // 写入日志
            roomMicLog(uid, micData.getRoomId(), upTime, micData.getPosition());
            if (upTime == 0) {
                logger.error("upTime is zero. roomId={} uid={}", micData.getRoomId(), uid);
            } else {
                int now = DateHelper.getNowSeconds();
                int micTime = (int) (now - upTime);
                // 下麦时，记录时间
                dailyTaskService.roomMicTime(uid, micTime, true);
                userLevelTaskService.upMicTask(new UserLevelTaskData(uid, UserLevelConstant.UP_MIC, micTime / 60));
                mqSenderService.sendAnchorTaskToMq(new CommonData(uid, AnchorTaskConstant.UP_MIC, micTime / 60, micData.getRoomId()));
            }
        }
        listData.setVersion(roomMicDao.getVersion(listData.getRoomId()));
        if (pushMsg) {
            sendMicChange(listData.getRoomId(), null);
        }
    }

    private void doDownRoomMic(RoomMicData micData) {
        try {
            logger.info("do down room mic. position={} roomId={} uid={}", micData.getPosition(), micData.getRoomId(), micData.getUid());
            micData.setUid("");
            micData.setUpMicTime(0);
            micData.setThirdPartId("");
            micData.setMtime(DateHelper.getNowSeconds());
            micData.setVideoStatus(0);
            micData.setVideoOpTime(0);
            micData.setMicType(0);
            roomMicDao.updateRoomMic(micData);
            fillForbidAndMute(micData);
        } catch (Exception e) {
            logger.error("do down room mic error. position={} roomId={} {}", micData.getPosition(), micData.getRoomId(), e.getMessage(), e);
        }
    }

    private void roomMicLog(String uid, String roomId, long upTime, int position) {
        try {
            ActorData actorData = actorDao.getActorDataFromCache(uid);
            if (actorData == null) {
                logger.error("room mic error. position={} roomId={} uid={}", position, roomId, uid);
                return;
            }
            int now = DateHelper.getNowSeconds();
            int micTime = (int) (now - upTime);
            RoomMicLogData micLogData = new RoomMicLogData();
            micLogData.setUserId(uid);
            micLogData.setRoomId(roomId);
            micLogData.setMicPosition(position);
            micLogData.setMicTime(micTime);
            micLogData.setRookieStatus(ActorUtils.isNewRegisterActor(actorData.getUid()) ? 1 : 0);
            micLogData.setVersionCode(actorData.getVersion_code());
            micLogData.setOs(actorData.getIntOs());
            micLogData.setCtime(now);
            micLogData.setMtime(now);
            roomMicLogDao.insert(micLogData);
        } catch (Exception e) {
            logger.error("room mic log error. roomId={} uid={} {}", roomId, uid, e.getMessage(), e);
        }
    }

    public void lockRoomMic(RoomMicData micData, boolean isLock) {
        try {
            logger.info("do lock room mic. position={} isLock={} roomId={}", micData.getPosition(), isLock, micData.getRoomId());
            micData.setMicLock(isLock ? 1 : 0);
            micData.setMtime(DateHelper.getNowSeconds());
            roomMicDao.updateRoomMic(micData);
            fillForbidAndMute(micData);
        } catch (Exception e) {
            logger.error("do lock room mic error. position={} roomId={} {}", micData.getPosition(), micData.getRoomId(), e.getMessage(), e);
        }
    }

    public int changeMicType(RoomMicData micData, int micType) {
        try {
            micData.setMicType(micType);
            micData.setMtime(DateHelper.getNowSeconds());
            int version = roomMicDao.updateRoomMic(micData);
            fillForbidAndMute(micData);
            return version;
        } catch (Exception e) {
            logger.error("changeMicType error. position={} roomId={} {}", micData.getPosition(), micData.getRoomId(), e.getMessage(), e);
            return 0;
        }
    }

    public int muteRoomMic(RoomMicData micData, boolean isMute) {
        try {
            logger.info("do mute room mic. roomId={} position={} isMute={} ", micData.getRoomId(), micData.getPosition(), isMute);
            micData.setMute(isMute ? 1 : 0);
            int nowSeconds = DateHelper.getNowSeconds();
            micData.setMuteTime(nowSeconds);
            micData.setMtime(nowSeconds);
            int version = roomMicDao.updateRoomMic(micData);
            fillForbidAndMute(micData);
            roomMicDao.setMuteStatus(micData.getRoomId(), isMute);
            return version;
        } catch (Exception e) {
            logger.error("do mute room mic error. position={} roomId={} {}", micData.getPosition(), micData.getRoomId(), e.getMessage(), e);
            return 0;
        }
    }

    public void incVersion(String roomId) {
        roomMicDao.incVersion(roomId);
    }

    public int incAndGetVersion(String roomId) {
        return roomMicDao.incVersion(roomId);
    }

    private RoomMicData getMicData(List<RoomMicData> list, int position) {
        for (RoomMicData micData : list) {
            if (micData.getPosition() == position) {
                return micData;
            }
        }
        return null;
    }

    private RoomMicData getMicData(List<RoomMicData> list, String uid) {
        for (RoomMicData micData : list) {
            if (uid.equals(micData.getUid())) {
                return micData;
            }
        }
        return null;
    }

    public String createLockKey(String roomId) {
        return "mic_lock_" + roomId;
    }

    public void incrVersionAndSendMicChange(String roomId, String fromUid) {
        incVersion(roomId);
        sendMicChange(roomId, fromUid);
    }

    public void sendMicChange(String roomId, String fromUid) {
        BaseTaskFactory.getFactory().addSlow(new Task() {
            @Override
            protected void execute() {
                String path = "mic_change";
                Map<String, String> params = new HashMap<>();
                params.put("room_id", roomId);
                params.put("from_uid", fromUid);
                roomWebSender.sendPost(path, roomId, params);
            }
        });
    }

    public void sendInviteUpMicMsg(String roomId, String fromUid, String toUid, int position) {
        sendInviteUpMicMsg(roomId, fromUid, toUid, position, false);
    }

    public void sendInviteUpMicMsg(String roomId, String fromUid, String toUid, int position, boolean autoUpMic) {
        ActorData actorData = actorDao.getActorData(fromUid);
        InviteMicPushMsg msg = new InviteMicPushMsg();
        msg.setFrom_name(actorData.getName());
        msg.setFromHead(ImageUrlGenerator.generateRoomUserUrl(actorData));
        msg.setMicIndex(position);
        msg.setAutoUpMic(autoUpMic ? 1 : 0);
        roomWebSender.sendPlayerWebMsg(roomId, fromUid, toUid, msg, true);
        logger.info("send invite up mic msg. roomId={} uid={} aid={}", roomId, fromUid, toUid);
    }

    public RoomMicListData switchRoomMode(MongoRoomData roomData, String uid, int roomMode) {
        ActorData actorData = actorDao.getActorDataFromCache(uid);
        try (DistributeLock lock = new DistributeLock(createLockKey(roomData.getRid()))) {
            lock.lock();
            RoomMicListData roomMicList = getRoomMicList(roomData.getRid(), roomData.getMicSize());
            // 去除麦位上多出部分的用户
            if (com.quhong.enums.RoomConstant.LIVE_ROOM_MODE == roomMode) {
                roomMicList.getList().sort((d1, d2) -> {
                    String roomId1 = RoomUtils.formatRoomId(d1.getUid());
                    String roomId2 = RoomUtils.formatRoomId(d2.getUid());
                    if (roomData.getRid().equals(roomId1) && !roomData.getRid().equals(roomId2)) {
                        return -1;
                    } else if (!roomData.getRid().equals(roomId1) && roomData.getRid().equals(roomId2)) {
                        return 1;
                    } else if (!StringUtils.isEmpty(d1.getUid()) && StringUtils.isEmpty(d2.getUid())) {
                        return -1;
                    } else if (StringUtils.isEmpty(d1.getUid()) && !StringUtils.isEmpty(d2.getUid())) {
                        return 1;
                    } else {
                        return 0;
                    }
                });
                for (int i = 0; i < roomMicList.getList().size(); i++) {
                    RoomMicData micData = roomMicList.getList().get(i);
                    micData.setPosition(i);
                    if (i >= LIVE_MIC_SIZE && !StringUtils.isEmpty(micData.getUid())) {
                        // 麦位移除消息
                        RemoveSeatPushMsg msg = new RemoveSeatPushMsg();
                        msg.setFrom_name(actorData.getName());
                        msg.setFrom_Img(ImageUrlGenerator.generateRoomUserUrl(actorData));
                        roomWebSender.sendPlayerWebMsg(roomData.getRid(), uid, micData.getUid(), msg, true);
                        // 空间不足下麦处理
                        downRoomMic(roomMicList, micData.getUid(), false, true, 5, roomData);
                    } else {
                        micData.setMicLock(0);
                        roomMicDao.doUpdateRoomMic(micData);
                    }
                }
            }
            roomDao.updateField(roomData.getRid(), "roomMode", roomMode);
            roomMicList.setVersion(incAndGetVersion(roomData.getRid()));
            sendMicChange(roomData.getRid(), uid);
            return roomMicList;
        } catch (Exception e) {
            logger.error("switch room mode error. roomId={} uid={} {}", roomData.getRid(), uid, e.getMessage(), e);
            throw new CommonException(HttpCode.SERVER_ERROR);
        }
    }

    /**
     * 设置麦位大小
     *
     * @param kickReason 切换麦位提出下麦原因 1开启pk模式切换
     */
    public RoomMicListData setMicMode(String roomId, String uid, int micSize, int kickReason) {
        ActorData fromActor = actorDao.getActorDataFromCache(uid);
        MongoRoomData roomData;
        RoomMicListData listData;
        try (DistributeLock lock = new DistributeLock(createLockKey(roomId))) {
            lock.lock();
            roomData = roomDao.findData(roomId);
            if (null == roomData || !SUPPORTED_MIC_SIZE.contains(micSize)) {
                throw new CommonException(HttpCode.PARAM_ERROR);
            }
            roomDao.updateField(roomId, "micSize", micSize);
            listData = getAndResizeRoomMicList(roomData, fromActor, micSize, roomData.getMicSize() == 0 ? 8 : roomData.getMicSize(), kickReason);
        } catch (Exception e) {
            logger.error("op set mic mode error. roomId={} uid={} {}", roomId, uid, e.getMessage(), e);
            throw new CommonException(HttpCode.SERVER_ERROR);
        }
        // 发送改变通知
        sendMicChange(roomId, uid);
        if (RoomUtils.isVoiceRoom(roomId)) {
            RoomNotificationMsg msg = new RoomNotificationMsg();
            msg.setUid(fromActor.getUid());
            msg.setUser_name(fromActor.getName());
            msg.setText(("The host has switched to a new seat mode, and the current microphone positions will change."));
            msg.setText_ar("قام المضيف بالتبديل إلى وضع مقاعد جديد، وستتغير مواقع الميكروفون الحالية.");
            msg.setHide_head(1);
            msg.setWeb_type(1);
            msg.setFromRoomId(roomData.getRid());
            roomWebSender.sendRoomWebMsg(roomData.getRid(), null, msg, false);
        }
        return listData;
    }

    public void checkAndResizeGuestLive(MongoRoomData roomData, String uid) {
        if (RoomUtils.isLiveRoom(roomData.getRid())) {
            if (roomData.getMicSize() != LIVE_MIC_SIZE) {
                // 首次使用时重置一下麦位
                ActorData fromActor = actorDao.getActorDataFromCache(uid);
                roomDao.updateField(roomData.getRid(), "micSize", LIVE_MIC_SIZE);
                roomData.setMicSize(LIVE_MIC_SIZE);
                getAndResizeRoomMicList(roomData, fromActor, LIVE_MIC_SIZE, roomData.getMicSize(), 0);
            }
        }
    }

    public RoomMicInfoObject createMicInfo(RoomMicData micData, MongoRoomData roomData, boolean forceUpdate,
                                           String teamPkId, Map<String, CheatGiftRedis.CheatGiftData> map) {
        RoomMicInfoObject micObj = new RoomMicInfoObject();
        micObj.setIndex(micData.getPosition());
        micObj.setMute(micData.getMute());
        micObj.setStatus(micData.getStatus());
        if (!ObjectUtils.isEmpty(micData.getUid())) {
            RoomMicUserObject userObject = createUser(roomData, micData.getUid(), forceUpdate, teamPkId, micData.getPosition());
            // 以下仅作业务处理
            CheatGiftRedis.CheatGiftData cheatGiftData = map.get(micData.getUid());
            if (null != cheatGiftData) {
                userObject.setVoice_type(cheatGiftData.getVoiceType());
                if (!ObjectUtils.isEmpty(cheatGiftData.getPrankMicFrame())) {
                    userObject.setMic_frame(cheatGiftData.getPrankMicFrame());
                }
            }
            userObject.setUpMicTime(micData.getUpMicTime());
            userObject.setThirdPartId(micData.getThirdPartId());
            userObject.setMicType(micData.getMicType());
            micObj.setUser(userObject);
        }
        return micObj;
    }

    private RoomMicUserObject createUser(MongoRoomData roomData, String uid, boolean forceUpdate, String teamPkId, int micIndex) {
        RoomActorDetailData detailData = roomActorCache.getData(roomData.getRid(), uid, forceUpdate);
        RoomMicUserObject userObject = new RoomMicUserObject();
        userObject.setAid(detailData.getAid());
        userObject.setName(detailData.getName());
        userObject.setHead(detailData.getHead());
        userObject.setGender(detailData.getGender());
        userObject.setMic_frame(detailData.getMicFrame());
        userObject.setRipple_url(detailData.getRippleUrl());
        userObject.setVip_level(detailData.getVipLevel());
        userObject.setRole(detailData.getRole());
        userObject.setCharmLevel(detailData.getCharmLevel());
        userObject.setWealthLevel(detailData.getWealthLevel());
        userObject.setMystery(detailData.getMystery());
        userObject.setIdentify(detailData.getIdentify());
        userObject.setSvipLevel(detailData.getSvipLevel());
        userObject.setStreamId(zegoApi.generateActorStreamId(roomData.getOwnerRid(), detailData.getRid(), detailData.getAid()));
        userObject.setCharm(charmCounterRedis.getCharmCounter(roomData, uid));
        if (!ObjectUtils.isEmpty(teamPkId)) {
            userObject.setTeamPkDevote(String.valueOf(teamPkDao.getContestantScore(teamPkId, uid, micIndex)));
        }
        return userObject;
    }

    public RoomMicInfoObject createLivePkMicInfo(String pkId, String hostId, String aid, int pkTeam, int index, int score) {
        String toRoomId = RoomUtils.formatLiveRoomId(aid);
        // 添加pk用户
        RoomMicData micData = new RoomMicData();
        micData.setUid(aid);
        micData.setPosition(index);
        RoomMicData roomMicData = roomMicDao.getDataFromRedis(RoomUtils.formatLiveRoomId(aid), 0);
        micData.setMicType(roomMicData != null ? roomMicData.getMicType() : 1);
        // 检查对方是否自我禁麦
        micData.setMute(roomMicDao.getMuteStatus(toRoomId));
        RoomMicInfoObject micInfo = createMicInfo(micData, roomDao.getDataFromCache(toRoomId), false, null, Collections.emptyMap());
        micInfo.setPkMute(livePkDao.getMuteStatus(pkId, hostId, aid));
        micInfo.getUser().setTeamPkDevote(String.valueOf(score));
        micInfo.getUser().setPkTeam(pkTeam);
        return micInfo;
    }

    public List<RoomMicInfoObject> convertMicInfoList(MongoRoomData roomData, List<RoomMicData> micList, LivePkData livePkData, String teamPkId, boolean forceUpdate) {
        List<RoomMicInfoObject> infoList = new ArrayList<>();
        // Map<String, CheatGiftRedis.CheatGiftData> map = cheatGiftRedis.fillCheatGift(micList);
        for (RoomMicData micData : micList) {
            infoList.add(createMicInfo(micData, roomData, forceUpdate, teamPkId, Collections.emptyMap()));
        }
        if (null != livePkData) {
            String pkId = livePkData.get_id().toString();
            long micCount = micList.stream().filter(micData -> !ObjectUtils.isEmpty(micData.getUid())).count();
            if (micCount != 1) {
                // should never happen
                logger.error("business error. live pk mic count error. pkId={} roomId={} micCount={}", pkId, roomData.getRid(), micCount);
                return infoList;
            }
            int fromScore = livePkDao.getLivePkScore(pkId, roomData.getRid());
            int toScore = 0;
            // 第一个麦为房主，其次为pk的本队队长、队友、对方队长、队友
            infoList.get(0).getUser().setPkTeam(1);
            infoList.get(0).getUser().setTeamPkDevote(String.valueOf(fromScore));
            if (infoList.size() > 1) {
                infoList.subList(1, infoList.size()).clear();
            }
            String hostId = RoomUtils.getRoomHostId(roomData.getRid());
            // 自己的队伍
            int team = livePkData.getPlayerList().stream().filter(player -> player.getAid().equals(hostId)).findFirst().orElse(new LivePkData.Player()).getTeam();
            List<LivePkData.Player> yellowTeamList = new ArrayList<>();
            List<LivePkData.Player> blueTeamList = new ArrayList<>();
            for (LivePkData.Player player : livePkData.getPlayerList()) {
                if (player.getAid().equals(hostId)) {
                    continue;
                }
                if (player.getTeam() == team) {
                    if (player.getStatus() == 1) {
                        // 提前离开的队友分数
                        fromScore = fromScore + livePkDao.getLivePkScore(pkId, RoomUtils.formatLiveRoomId(player.getAid()));
                        continue;
                    }
                    yellowTeamList.add(player);
                } else {
                    if (player.getStatus() == 1) {
                        // 提前离开的队友分数
                        toScore = toScore + livePkDao.getLivePkScore(pkId, RoomUtils.formatLiveRoomId(player.getAid()));
                        continue;
                    }
                    blueTeamList.add(player);
                }
            }
            int index = 0;
            for (LivePkData.Player player : yellowTeamList) {
                index++;
                String toRoomId = RoomUtils.formatLiveRoomId(player.getAid());
                int livePkScore = livePkDao.getLivePkScore(pkId, toRoomId);
                fromScore = fromScore + livePkScore;
                infoList.add(index, createLivePkMicInfo(pkId, hostId, player.getAid(), 1, index, livePkScore));
            }
            for (LivePkData.Player player : blueTeamList) {
                index++;
                String toRoomId = RoomUtils.formatLiveRoomId(player.getAid());
                int livePkScore = livePkDao.getLivePkScore(pkId, toRoomId);
                toScore = toScore + livePkScore;
                infoList.add(index, createLivePkMicInfo(pkId, hostId, player.getAid(), 2, index, livePkScore));
            }
            livePkData.setFromScore(fromScore);
            livePkData.setToScore(toScore);
        }
        return infoList;
    }

    /**
     * 获取最新的麦位信息
     */
    public RoomMicListVo getRoomMicListVo(String roomId) {
        if (ObjectUtils.isEmpty(roomId)) {
            return null;
        }
        MongoRoomData roomData = roomDao.findData(roomId);
        RoomMicListData listData = getRoomMicList(roomId, roomData.getMicSize());
        return getRoomMicListVo(roomData, listData);
    }

    public RoomMicListVo getRoomMicListVo(MongoRoomData roomData, RoomMicListData listData) {
        RoomMicListVo vo = new RoomMicListVo();
        LivePkData livePkData = livePkDao.findActivePkByRoomId(roomData.getRid());
        String teamPkId = teamPkDao.getTeamPkId(roomData.getRid());
        vo.setVersion(listData.getVersion());
        vo.setRoomId(listData.getRoomId());
        vo.setList(convertMicInfoList(roomData, listData.getList(), livePkData, teamPkId, false));
        vo.setLivePk(livePkDao.createLivePkInfo(roomData.getRid(), livePkData));
        vo.setTeamPk(teamPkDao.createTeamPkInfo(teamPkId));
        return vo;
    }

    public RoomMicListVo getRoomMicListFromRedis(String roomId) {
        RoomMicListVo roomMicListVo = roomMicRedis.getRoomMicFromRedis(roomId);
        if (null != roomMicListVo) {
            if (null != roomMicListVo.getTeamPk() && ObjectUtils.isEmpty(teamPkDao.getTeamPkId(roomId))) {
                roomMicListVo.setTeamPk(null);
            }
            if (null != roomMicListVo.getLivePk() && ObjectUtils.isEmpty(livePkDao.getLivePkId(roomId))) {
                roomMicListVo.setLivePk(null);
            }
            return roomMicListVo;
        }
        logger.info("skip getRoomMicList from redis. roomId={}", roomId);
        return getRoomMicListVo(roomId);
    }
}
