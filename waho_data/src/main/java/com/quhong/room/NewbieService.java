package com.quhong.room;

import com.quhong.data.ActorData;
import com.quhong.image.ImageUrlGenerator;
import com.quhong.mongo.dao.ActorDao;
import com.quhong.mongo.data.MongoActorData;
import com.quhong.msg.room.NewbieJoinRoomMessage;
import com.quhong.redis.WhitelistRedis;
import com.quhong.room.data.RoomActorDetailData;
import com.quhong.service.redis.RechargeRedis;
import com.quhong.utils.ActorUtils;
import jakarta.annotation.Resource;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Component;
import org.springframework.util.ObjectUtils;
import org.springframework.util.StringUtils;

import java.util.*;
import java.util.stream.Collectors;

@Component
@Lazy
public class NewbieService {
    private static final Logger logger = LoggerFactory.getLogger(NewbieService.class);

    // 富庶之地，注册国家为海湾国家：沙特阿拉伯，科威特，阿拉伯联合酋长国，卡塔尔，阿曼，巴林
    public static final Set<String> RICH_PLACE_COUNTRY = new HashSet<>(Arrays.asList("SA", "KW", "AE", "QA", "OM", "BH"));

    @Resource
    private ActorDao actorDao;
    @Resource
    private RechargeRedis rechargeRedis;
    @Resource
    private WhitelistRedis whitelistRedis;

    public NewbieJoinRoomMessage getJoinRoomMsg(String aid) {
        ActorData actorData = actorDao.getActorDataFromCache(aid);
        if (null == actorData) {
            return null;
        }
        return getJoinRoomMsg(actorData);
    }

    public NewbieJoinRoomMessage getJoinRoomMsg(ActorData actorData) {
        NewbieJoinRoomMessage msg = new NewbieJoinRoomMessage();
        msg.setAid(actorData.getUid());
        msg.setName(actorData.getName());
        msg.setGender(actorData.getFb_gender());
        msg.setAge(actorData.getAge());
        msg.setHead(ImageUrlGenerator.generateRoomUserUrl(actorData));
        msg.setLocation(getCountry(actorData.getCountry()));
        msg.setPlatform(actorData.getIntOs());
        msg.setRichPlace(RICH_PLACE_COUNTRY.contains(msg.getLocation()) ? 1 : 0);
        msg.setTopUp(rechargeRedis.isRechargeUser(actorData.getUid()) ? 1 : 0);
        msg.setNewbie(1);
        msg.setSvipLevel(actorData.getSvipLevel());
        msg.setMystery(actorData.getMystery());
        return msg;
    }

    public NewbieJoinRoomMessage getJoinRoomMsg(RoomActorDetailData actorData) {
        NewbieJoinRoomMessage msg = new NewbieJoinRoomMessage();
        msg.setAid(actorData.getAid());
        msg.setName(actorData.getName());
        msg.setGender(actorData.getGender());
        msg.setAge(actorData.getAge());
        msg.setHead(ImageUrlGenerator.generateRoomUserUrl(actorData.getHead()));
        msg.setLocation(getCountry(actorData.getCountry()));
        msg.setPlatform(actorData.getOs());
        msg.setRichPlace(RICH_PLACE_COUNTRY.contains(msg.getLocation()) ? 1 : 0);
        msg.setTopUp(rechargeRedis.isRechargeUser(actorData.getAid()) ? 1 : 0);
        msg.setNewbie(1);
        msg.setSvipLevel(actorData.getSvipLevel());
        msg.setMystery(actorData.getMystery());
        return msg;
    }

    private String getCountry(String country) {
        if (!StringUtils.hasLength(country)) {
            return "";
        }
        if (country.length() >= 3) {
            return country.substring(3);
        }
        return country;
    }

    public Set<String> getNewbieHostSet(Set<String> inRoomUserSet) {
        if (ObjectUtils.isEmpty(inRoomUserSet)) {
            return Collections.emptySet();
        }
        Set<String> limitSet = inRoomUserSet.stream()
                .limit(300)
                .collect(Collectors.toCollection(LinkedHashSet::new));
        List<MongoActorData> actors = actorDao.getActors(limitSet);
        Set<String> result = new HashSet<>(whitelistRedis.getWhitelistSetFromCache(WhitelistRedis.NEWBIE_HOST_LIST));
        for (MongoActorData actor : actors) {
            if (actor.getFb_gender() == 2 && actor.getFamilyId() > 0) {
                result.add(actor.get_id().toString());
            }
        }
        return result;
    }
}
