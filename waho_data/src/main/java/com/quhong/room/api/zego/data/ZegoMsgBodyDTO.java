package com.quhong.room.api.zego.data;

import com.alibaba.fastjson.annotation.JSONField;

public class ZegoMsgBodyDTO {

    @JSONField(name = "Message")
    private Object message; // Command 消息内容，默认为 2 KB；最大为 32 KB

    public ZegoMsgBodyDTO() {
    }

    public ZegoMsgBodyDTO(Object message) {
        this.message = message;
    }

    public Object getMessage() {
        return message;
    }

    public void setMessage(Object message) {
        this.message = message;
    }
}
