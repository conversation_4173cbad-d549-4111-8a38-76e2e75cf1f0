package com.quhong.room.api.zego.data;

public class ZegoStreamDataVO {
    private String StreamId;    // 流ID
    private String UserId;    // 用户名
    private String UserName;    // 用户昵称
    private Long CreateTime;    // 创建时间
    private Integer StreamNumberId;    // 房间唯一流 ID

    public String getStreamId() {
        return StreamId;
    }

    public void setStreamId(String streamId) {
        StreamId = streamId;
    }

    public String getUserId() {
        return UserId;
    }

    public void setUserId(String userId) {
        UserId = userId;
    }

    public String getUserName() {
        return UserName;
    }

    public void setUserName(String userName) {
        UserName = userName;
    }

    public Long getCreateTime() {
        return CreateTime;
    }

    public void setCreateTime(Long createTime) {
        CreateTime = createTime;
    }

    public Integer getStreamNumberId() {
        return StreamNumberId;
    }

    public void setStreamNumberId(Integer streamNumberId) {
        StreamNumberId = streamNumberId;
    }
}
