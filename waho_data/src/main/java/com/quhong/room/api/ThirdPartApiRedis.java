package com.quhong.room.api;

import com.quhong.core.utils.DateHelper;
import com.quhong.redis.DataRedisBean;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.context.annotation.Lazy;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.stereotype.Service;

import jakarta.annotation.Resource;
import java.util.concurrent.TimeUnit;

@Lazy
@Service
public class ThirdPartApiRedis {
    private static final Logger logger = LoggerFactory.getLogger(ThirdPartApiRedis.class);
    private static final long RECORD_EXPIRE_SECOND = 30 * 60;

    @Resource(name = DataRedisBean.MAIN_CLUSTER)
    private StringRedisTemplate mainRedis;

    /**
     * 记录第三方id（串流时已不再麦上）
     */
    public void setUid(String roomId, String uid, String thirdPartId) {
        try {
            mainRedis.opsForValue().set(getThirdPartIdKey(roomId, thirdPartId), uid, 2, TimeUnit.DAYS);
        } catch (Exception e) {
            logger.error("set uid error roomId={} uid={} thirdPartId={} {}", roomId, uid, thirdPartId, e.getMessage());
        }
    }

    /**
     * 获取第三方id
     */
    public String getUid(String roomId, String thirdPartId) {
        try {
            return mainRedis.opsForValue().get(getThirdPartIdKey(roomId, thirdPartId));
        } catch (Exception e) {
            logger.error("get uid error roomId={} thirdPartId={} {}", roomId, thirdPartId, e.getMessage());
            return null;
        }
    }

    /**
     * 记录删除流事件，防止报警误判
     */
    public void setDelStreamRecord(String roomId, String uid) {
        try {
            mainRedis.opsForValue().set(getDelStreamRecordKey(roomId, uid), DateHelper.getNowSeconds() + "", RECORD_EXPIRE_SECOND, TimeUnit.SECONDS);
        } catch (Exception e) {
            logger.error("set del stream record error roomId={} uid={} {}", roomId, uid, e.getMessage());
        }
    }

    /**
     * 记录串流次数，第一次不告警(并发情况)
     */
    public int incrMicCrossTimes(String roomId, String uid) {
        try {
            String key = getMicCrossTimesKey(roomId, uid);
            Long value = mainRedis.opsForValue().increment(key);
            mainRedis.expire(key, 15, TimeUnit.MINUTES);
            return null == value ? 0 : value.intValue();
        } catch (Exception e) {
            logger.error("incr mic cross times error roomId={} uid={} {}", roomId, uid, e.getMessage());
            return 0;
        }
    }

    /**
     * 清除串流记录
     */
    public void clearMicCrossTimes(String roomId, String uid) {
        try {
            mainRedis.delete(getMicCrossTimesKey(roomId, uid));
        } catch (Exception e) {
            logger.error("clear mic cross times error roomId={} uid={} {}", roomId, uid, e.getMessage());
        }
    }

    /**
     * 判断删除流记录是否最近N秒写入
     */
    public boolean delStreamRecordIsNew(String roomId, String uid, int second) {
        try {
            Long expire = mainRedis.getExpire(getDelStreamRecordKey(roomId, uid), TimeUnit.SECONDS);
            if (null == expire) {
                return false;
            }
            return expire >= RECORD_EXPIRE_SECOND - second;
        } catch (Exception e) {
            logger.error("get del stream record ttl error. roomId={} uid={} {}", roomId, uid, e.getMessage(), e);
        }
        return false;
    }

    private String getDelStreamRecordKey(String roomId, String uid) {
        return String.format("str:delStreamRecord:%s:%s", roomId, uid);
    }

    private String getThirdPartIdKey(String roomId, String thirdPartId) {
        return String.format("str:thirdPartId:%s:%s", roomId, thirdPartId);
    }

    private String getMicCrossTimesKey(String roomId, String uid) {
        return String.format("str:micCrossTimes:%s:%s", roomId, uid);
    }
}
