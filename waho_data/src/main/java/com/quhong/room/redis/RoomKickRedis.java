package com.quhong.room.redis;

import com.quhong.constant.ExpireTimeConstant;
import com.quhong.core.config.ServerConfig;
import com.quhong.core.utils.DateHelper;
import jakarta.annotation.Resource;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.context.annotation.Lazy;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.stereotype.Component;
import org.springframework.util.StringUtils;

import java.util.concurrent.TimeUnit;

@Lazy
@Component
public class RoomKickRedis {
    private static final Logger logger = LoggerFactory.getLogger(RoomKickRedis.class);

    @Resource
    private StringRedisTemplate redisTemplate;

    public boolean isKick(String roomId, String uid) {
        try {
            String key = getKickKey(roomId, uid);
            String value = redisTemplate.opsForValue().get(key);
            return !StringUtils.isEmpty(value);
        } catch (Exception e) {
            logger.error("is kick error. roomId={} uid={} {}", roomId, uid, e.getMessage(), e);
        }
        return false;
    }

    public int getKickTime(String roomId, String uid) {
        try {
            String key = getKickKey(roomId, uid);
            String value = redisTemplate.opsForValue().get(key);
            return !StringUtils.isEmpty(value) ? Integer.parseInt(value) : 0;
        } catch (Exception e) {
            logger.error("get kick time error. roomId={} uid={} {}", roomId, uid, e.getMessage(), e);
        }
        return 0;
    }

    public void roomKick(String roomId, String uid) {
        try {
            String key = getKickKey(roomId, uid);
            String value = String.valueOf(DateHelper.getNowSeconds());
            redisTemplate.opsForValue().set(key, value);
            if (ServerConfig.isProduct()) {
                redisTemplate.expire(key, ExpireTimeConstant.EXPIRE_DAYS_ONE, TimeUnit.DAYS);
            } else {
                // 测试服踢2秒=分钟
                redisTemplate.expire(key, ExpireTimeConstant.EXPIRE_MINUTES_TWO, TimeUnit.MINUTES);
            }
        } catch (Exception e) {
            logger.error("set room kick data error. uid={} roomId={} {}", uid, roomId, e.getMessage(), e);
        }
    }

    public int getReportLevel(String uid) {
        try {
            return 0;
//            String value = redisTemplate.opsForValue().get(getReportKey(uid));
//            return !StringUtils.isEmpty(value) ? Integer.parseInt(value) : 0;
        } catch (Exception e) {
            logger.error("get report level error. uid={} {}", uid, e.getMessage(), e);
        }
        return 0;
    }

    public String getFromKickUid(String roomId, String uid) {
        try {
            String key = getKickFromUidKey(roomId, uid);
            return redisTemplate.opsForValue().get(key);
        } catch (Exception e) {
            logger.error("get from kick uid error. roomId={} uid={} {}", roomId, uid, e.getMessage(), e);
        }
        return null;
    }

    public String getFromKickRoomId(String uid) {
        try {
            return redisTemplate.opsForValue().get(getRoomKickRecordKey(uid));
        } catch (Exception e) {
            logger.error("get from kick roomId error. uid={} {}", uid, e.getMessage(), e);
        }
        return null;
    }

    public void setKickRecord(String roomId, String uid, String aid) {
        try {
            String key = getKickFromUidKey(roomId, aid);
            // 写入一条校验记录
            redisTemplate.opsForValue().set(getRoomKickRecordKey(aid), roomId, ExpireTimeConstant.EXPIRE_MINUTES_TWO, TimeUnit.MINUTES);
            // 记录被谁踢的
            redisTemplate.opsForValue().set(key, uid, ExpireTimeConstant.EXPIRE_HOUES_TEN, TimeUnit.HOURS);
        } catch (Exception e) {
            logger.error("set room kick record error. roomId={} uid={} {}", roomId, uid, e.getMessage(), e);
        }
    }

    private String getRoomKickRecordKey(String uid) {
        return "ser:roomKickRecord:" + uid;
    }

    private String getKickKey(String roomId, String uid) {
        return "rmkick_" + uid + "_" + roomId;
    }

    private String getKickFromUidKey(String roomId, String uid) {
        return "kick_uid_" + uid + "_" + roomId;
    }

    private String getReportKey(String uid) {
        return "rep_" + uid;
    }
}
