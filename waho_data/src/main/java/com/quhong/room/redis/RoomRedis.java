package com.quhong.room.redis;

import com.alibaba.fastjson.JSON;
import com.quhong.core.utils.DateHelper;
import com.quhong.data.EnterRoomCheckData;
import com.quhong.redis.LiveRoomRedis;
import com.quhong.room.data.RoomData;
import com.quhong.utils.RoomUtils;
import jakarta.annotation.Resource;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.stereotype.Component;
import org.springframework.util.StringUtils;

import java.util.*;
import java.util.concurrent.TimeUnit;

@Component
public class RoomRedis {
    private static final Logger logger = LoggerFactory.getLogger(RoomRedis.class);

    // 房间5分钟过期
    public static final long ROOM_EXPIRE_TIME_SEC = 10 * 60;

    @Resource
    private StringRedisTemplate redisTemplate;
    @Resource
    private LiveRoomRedis liveRoomRedis;
    @Resource
    private MicApplyRedis micApplyRedis;

    private String expireDate = "";

    /**
     * 添加房间数据
     *
     * @param roomData
     */
    public void addRoom(RoomData roomData) {
        // 将room添加到hash中，以便重启时恢复
        addToRoomHash(roomData.getRoomId(), roomData);
        // 更新房间状态
        updateRoomZSet(roomData.getRoomId(), DateHelper.getNowSeconds());
    }

    /**
     * 移除房间
     *
     * @param roomId
     */
    public void removeRoom(String roomId) {
        removeFromRoomHash(roomId);
        removeRoomFromZSet(roomId);
        if (RoomUtils.isLiveRoom(roomId)) {
            liveRoomRedis.removeLiveRoom(roomId);
        }
        micApplyRedis.clearMicApply(roomId);
    }

    /**
     * 更新房间数据
     *
     * @param roomData
     */
    public void updateRoom(RoomData roomData) {
        if (!hasInRoomHash(roomData.getRoomId())) {
            // 如果没在roomHash中，则加入
            logger.error("roomData has not in room hash. roomId={}", roomData.getRoomId());
            addRoom(roomData);
        } else {
            updateRoomZSet(roomData.getRoomId(), DateHelper.getNowSeconds());
        }
    }

    public RoomData getRoomData(String roomId) {
        return getRoomDataFromHash(roomId);
    }

    public List<RoomData> getAllRoomData() {
        String key = getRoomHashKey();
        List<RoomData> roomDataList = new ArrayList<>();
        try {
            List<Object> list = redisTemplate.opsForHash().values(key);
            if (list == null) {
                return roomDataList;
            }
            for (Object value : list) {
                String json = (String) value;
                if (StringUtils.isEmpty(json)) {
                    continue;
                }
                RoomData roomData = JSON.parseObject(json, RoomData.class);
                roomDataList.add(roomData);
            }
        } catch (Exception e) {
            logger.error("redis. get all room data from hash error. {}", e.getMessage(), e);
        }
        return roomDataList;
    }

    protected void addToRoomHash(String roomId, RoomData roomData) {
        logger.info("redis. add to room hash. roomId={}", roomId);
        String key = getRoomHashKey();
        try {
            String json = JSON.toJSONString(roomData);
            redisTemplate.opsForHash().put(key, roomId, json);
        } catch (Exception e) {
            logger.error("add to room hash error. roomId={} {}", roomId, e.getMessage(), e);
        }
    }

    protected void removeFromRoomHash(String roomId) {
        logger.info("redis. remove from room hash. roomId={}", roomId);
        String key = getRoomHashKey();
        try {
            redisTemplate.opsForHash().delete(key, roomId);
        } catch (Exception e) {
            logger.error("remove from room hash error. roomId={} {}", roomId, e.getMessage(), e);
        }
    }

    protected RoomData getRoomDataFromHash(String roomId) {
        logger.info("redis. get room data from hash. roomId={}", roomId);
        String key = getRoomHashKey();
        try {
            String json = (String) redisTemplate.opsForHash().get(key, roomId);
            if (StringUtils.isEmpty(json)) {
                return null;
            } else {
                return JSON.parseObject(json, RoomData.class);
            }
        } catch (Exception e) {
            logger.error("redis. get room data from hash error. roomId={} {}", roomId, e.getMessage(), e);
        }
        return null;
    }

    protected boolean hasInRoomHash(String roomId) {
        String key = getRoomHashKey();
        try {
            return redisTemplate.opsForHash().hasKey(key, roomId);
        } catch (Exception e) {
            logger.error("has in room hash error. roomId={} {}", roomId, e.getMessage(), e);
        }
        return false;
    }

    protected void updateRoomZSet(String roomId, long timeSec) {
        logger.info("redis. update room zset time. roomId={}", roomId);
        String key = getRoomZSetKey();
        try {
            redisTemplate.opsForZSet().add(key, roomId, timeSec);
        } catch (Exception e) {
            logger.error("update room zset error. roomId={} {}", roomId, e.getMessage(), e);
        }
    }

    protected void removeRoomFromZSet(String roomId) {
        logger.info("redis. delete room zset time. roomId={}", roomId);
        String key = getRoomZSetKey();
        try {
            redisTemplate.opsForZSet().remove(key, roomId);
        } catch (Exception e) {
            logger.error("update room zset error. roomId={} {}", roomId, e.getMessage(), e);
        }
    }

    public Set<String> removeExpireRoomsFromZSet() {
        String key = getRoomZSetKey();
        try {
            long expireTime = DateHelper.getNowSeconds() - ROOM_EXPIRE_TIME_SEC;
            Set<String> roomSet = redisTemplate.opsForZSet().rangeByScore(key, 0, expireTime);
            redisTemplate.opsForZSet().removeRangeByScore(key, 0, expireTime);
            return roomSet;
        } catch (Exception e) {
            logger.error("remove expire rooms from zset error. {}", e.getMessage(), e);
        }
        return null;
    }

    public Set<String> getRoomSet() {
        String key = getRoomZSetKey();
        try {
            int nowSeconds = DateHelper.getNowSeconds();
            return redisTemplate.opsForZSet().rangeByScore(key, nowSeconds - ROOM_EXPIRE_TIME_SEC, nowSeconds);
        } catch (Exception e) {
            logger.error("get rooms from zset error. {}", e.getMessage(), e);
        }
        return null;
    }

    /**
     * 随机返回所有类型的房间
     */
    public List<String> randomRooms(int count) {
        return redisTemplate.opsForZSet().randomMembers(getRoomZSetKey(), count);
    }

    public Set<String> getAllRoomSet() {
        String key = getRoomZSetKey();
        try {
            return redisTemplate.opsForZSet().range(key, 0, -1);
        } catch (Exception e) {
            logger.error("get all rooms from zset error. {}", e.getMessage(), e);
        }
        return null;
    }

    public int getActivityRoomCount() {
        String key = getRoomZSetKey();
        try {
            Long count = redisTemplate.opsForZSet().zCard(key);
            return count == null ? 0 : count.intValue();
        } catch (Exception e) {
            logger.error("get activity room count error. {}", e.getMessage(), e);
        }
        return 0;
    }

    public EnterRoomCheckData getEnterRoomValue(String uid) {
        String key = getEnterRoomKey(uid);
        try {
            String value = redisTemplate.opsForValue().get(key);
            logger.info("get enter room check data. data={} uid={}", value, uid);
            if (StringUtils.isEmpty(value)) {
                return null;
            }
            String[] arr = value.split("_");
            EnterRoomCheckData checkData = new EnterRoomCheckData();
            checkData.setRoomId(arr[0]);
            if (arr.length > 1 && (!StringUtils.isEmpty(arr[1]))) {
                checkData.setPwd(arr[1]);
            }
            return checkData;
        } catch (Exception e) {
            logger.error("get enter room value error. uid={} {}", uid, e.getMessage(), e);
        }
        return null;
    }


    private String getRoomHashKey() {
        return "mars_room_hash";
    }

    private String getRoomZSetKey() {
        return "mars_room_zset";
    }

    private String getEnterRoomKey(String uid) {
        return "room_join_" + uid;
    }

    public Map<Object, Object> getAllRoomsUserOnlineTime(String date) {
        try {
            return redisTemplate.opsForHash().entries("room_stat_" + date);
        } catch (Exception e) {
            logger.error("get all rooms user online time error date{} {}", date, e.getMessage(), e);
        }
        return null;
    }

    public Map<Object, Object> getRoomUserOnlineData(String roomId, int deltaDay) {
        return redisTemplate.opsForHash().entries(getUserEnterRoomKey(roomId, deltaDay));
    }

    private String getUserEnterRoomKey(String roomId, int deltaDay) {
        long startTime = DateHelper.DEFAULT.getDayOffset(deltaDay);
        String date = DateHelper.DEFAULT.formatDateInDay(new Date(startTime));
        return "enter_room_" + date + "_" + roomId;
    }

    public void updateRoomUser(String roomId, String uid, int todayStartTimeSec) {
        try {
            // 删除昨天的数据
            removeRoomUser(roomId, uid);
            // 设置新一天的数据
            String key = getUserEnterRoomKey(roomId, 0);
            redisTemplate.opsForHash().put(key, uid, String.valueOf(todayStartTimeSec));
            redisTemplate.expire(key, 2, TimeUnit.DAYS);
        } catch (Exception e) {
            logger.error("update room user error roomId={} uid={} {}", roomId, uid, e.getMessage(), e);
        }
    }

    public void removeRoomUser(String roomId, String uid) {
        try {
            String key = getUserEnterRoomKey(roomId, -1);
            redisTemplate.opsForHash().delete(key, uid);
        } catch (Exception e) {
            logger.error("remove room user error roomId={} uid={} {}", roomId, uid, e.getMessage(), e);

        }
    }

    public void removeRoomUserStat(String statDate, String key) {
        try {
            redisTemplate.opsForHash().delete("room_stat_" + statDate, key);
        } catch (Exception e) {
            logger.error("remove room user stat error statDate={} key={} {}", statDate, key, e.getMessage(), e);
        }
    }

    public void addRoomJoin(String uid, String roomId) {
        try {
            redisTemplate.opsForValue().set(getEnterRoomKey(uid), roomId, 7, TimeUnit.DAYS);
        } catch (Exception e) {
            logger.error("add room join error. roomId={} uid={} msg={}", roomId, uid, e.getMessage());
        }
    }

    public void deleteRoomJoin(String uid) {
        try {
            redisTemplate.delete(getEnterRoomKey(uid));
        } catch (Exception e) {
            logger.error("delete room join error. uid={} msg={}", uid, e.getMessage());
        }
    }

    /**
     * int voiceOnlineActor;
     * int liveOnlineActor;
     * int liveRoomCount;
     * int voiceRoomCount;
     * int ludoRoomCount;
     * int videoRoomCount;
     * int turntableRoomCount;
     */
    public void saveRoomStatData(Map<String, String> map) {
        try {
            redisTemplate.opsForHash().putAll(getRoomStatKey(), map);
        } catch (Exception e) {
            logger.error("save room stat data error. {}", e.getMessage(), e);
        }
    }

    public int getRoomStatValue(String itemKey) {
        try {
            String value = (String) redisTemplate.opsForHash().get(getRoomStatKey(), itemKey);
            if (StringUtils.isEmpty(value)) {
                return 0;
            }
            return Integer.parseInt(value);
        } catch (Exception e) {
            logger.error("getRoomStatValue error. itemKey={}", itemKey, e);
            return 0;
        }
    }

    public Map<String, Integer> getRoomStatData() {
        try {
            Map<Object, Object> entries = redisTemplate.opsForHash().entries(getRoomStatKey());
            Map<String, Integer> resultMap = new HashMap<>();
            for (Map.Entry<Object, Object> entry : entries.entrySet()) {
                try {
                    String itemKey = String.valueOf(entry.getKey());
                    int value = Integer.parseInt(String.valueOf(entry.getValue()));
                    resultMap.put(itemKey, value);
                } catch (Exception e) {
                    logger.error("parse roomStatData error. key={} value={}", entry.getKey(), entry.getValue(), e);
                }
            }
            return resultMap;
        } catch (Exception e) {
            logger.error("get room stat data error. {}", e.getMessage(), e);
            return Collections.emptyMap();
        }
    }

    public void addRoomSendMsgUser(String roomId, String uid) {
        try {
            String key = getRoomSendMsgUserKey(roomId, DateHelper.ARABIAN.formatDateInDay());
            Long add = redisTemplate.opsForSet().add(key, uid);
            if (add != null && add == 1) {
                redisTemplate.expire(key, 3, TimeUnit.DAYS);
            }
        } catch (Exception e) {
            logger.error("addRoomSendMsgUser error. roomId={} uid={} {}", roomId, uid, e.getMessage(), e);
        }
    }

    public int getRoomSendMsgUserNum(String roomId, String strDate) {
        try {
            String key = getRoomSendMsgUserKey(roomId, strDate);
            Long size = redisTemplate.opsForSet().size(key);
            return size != null ? size.intValue() : 0;
        } catch (Exception e) {
            logger.error("addRoomSendMsgUser error. roomId={} strDate={} {}", roomId, strDate, e.getMessage(), e);
            return 0;
        }
    }

    public void setLastCreateRoom(String uid, int roomType) {
        // roomType 0直播房 1语聊房
        String key = getLastCreateRoomKey();
        try {
            redisTemplate.opsForHash().put(key, uid, roomType + "");
            String strDate = DateHelper.ARABIAN.formatDateInDay();
            if (!expireDate.equals(strDate)) {
                redisTemplate.expire(key, 1, TimeUnit.DAYS);
                expireDate = strDate;
            }
        } catch (Exception e) {
            logger.error("setLastCreateRoom error. uid={} roomType={} {}", uid, roomType, e.getMessage(), e);
        }
    }

    /**
     * 获取用户当天最后一次创建发房间类型
     *
     * @return 0直播房 1语聊房
     */
    public int getLastCreateRoom(String uid) {
        String key = getLastCreateRoomKey();
        try {
            String strValue = (String) redisTemplate.opsForHash().get(key, uid);
            if (StringUtils.hasLength(strValue)) {
                return Integer.parseInt(strValue);
            }
        } catch (Exception e) {
            logger.error("getLastCreateRoom error. uid={} {}", uid, e.getMessage(), e);
        }
        return 1;
    }

    private String getRoomSendMsgUserKey(String roomId, String strDate) {
        return "set:roomSendMsgUser_" + strDate;
    }

    private String getRoomStatKey() {
        return "hash:roomStat";
    }

    private String getLastCreateRoomKey() {
        return "hash:lastCreateRoom_" + DateHelper.ARABIAN.formatDateInDay();
    }
}
