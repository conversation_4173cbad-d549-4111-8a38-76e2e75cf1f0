package com.quhong.mongo.dao;

import com.alibaba.fastjson.JSONObject;
import com.mongodb.client.MongoCollection;
import com.mongodb.client.MongoCursor;
import com.mongodb.client.model.UpdateOptions;
import com.mongodb.client.result.DeleteResult;
import com.quhong.cache.CacheMap;
import com.quhong.core.utils.DateHelper;
import com.quhong.mongo.config.MongoBean;
import com.quhong.mongo.data.PhoneAccountData;
import jakarta.annotation.PostConstruct;
import jakarta.annotation.Resource;
import org.bson.BsonDocument;
import org.bson.BsonString;
import org.bson.Document;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.context.annotation.Lazy;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.data.mongodb.core.query.Criteria;
import org.springframework.data.mongodb.core.query.Query;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.List;
import java.util.Set;

/**
 * <AUTHOR>
 * @date 2022/6/11
 */
@Lazy
@Component
public class PhoneAccountDao {

    private static final Logger logger = LoggerFactory.getLogger(PhoneAccountDao.class);

    private static final long CACHE_TIME_MILLIS = 10 * 60 * 1000L;
    private final CacheMap<String, PhoneAccountData> cacheMap;
    public static final String TABLE_NAME = "phone_account";

    @Resource(name = MongoBean.MOVIES)
    private MongoTemplate mongoTemplate;

    @PostConstruct
    public void postInit() {
        cacheMap.start();
    }

    public PhoneAccountDao() {
        this.cacheMap = new CacheMap<>(CACHE_TIME_MILLIS);
    }

    public String getUserPhone(String uid) {
        PhoneAccountData data = findPhoneAccountData(uid);
        if (data == null) {
            return "";
        }
        return data.get_id();
    }

    public PhoneAccountData findPhoneAccountData(String uid) {
        PhoneAccountData data = cacheMap.getData(getCacheKey(uid));
        if (data != null) {
            return data;
        }
        data = findData(uid);
        if (data != null) {
            cacheMap.cacheData(getCacheKey(uid), data);
        }
        return data;
    }

    public PhoneAccountData findData(String uid) {
        try {
            Criteria criteria = Criteria.where("uid").is(uid);
            Query query = new Query(criteria);
            query.fields().include("_id");
            query.fields().include("uid");
            query.fields().include("pwd");
            query.fields().include("c_time");
            return mongoTemplate.findOne(query, PhoneAccountData.class);
        } catch (Exception e) {
            logger.error("find phone account data error. uid={} {}", uid, e.getMessage(), e);
        }
        return null;
    }


    public PhoneAccountData findDataByAccount(String account) {
        try {
            MongoCollection<Document> collection = mongoTemplate.getDb().getCollection(TABLE_NAME);
            Document find = new Document("_id", account);
            MongoCursor<Document> iter = collection.find(find).limit(1).iterator();
            if (iter.hasNext()) {
                Document doc = iter.next();
//                PhoneAccountData phoneAccountData = new PhoneAccountData();
//                phoneAccountData.set_id(doc.getString("_id"));
//                phoneAccountData.setUid(doc.getString("uid"));
//                phoneAccountData.setPwd(doc.getString("pwd"));
//                phoneAccountData.setC_time(doc.getInteger("c_time")); //
                String docStr = doc.toJson();
                PhoneAccountData phoneAccountData = JSONObject.parseObject(docStr, PhoneAccountData.class);
//                logger.info(" docStr={} phoneAccountData={}", docStr, phoneAccountData);
                return phoneAccountData;
            }
        } catch (Exception e) {
            logger.error(" account={} {}", account, e.getMessage(), e);
        }
        return null;
    }

    /**
     * 获取账号列表
     *
     * @param accountSet
     * @return
     */
    public List<PhoneAccountData> findPhoneByAccount(Set<String> accountSet) {
        try {
            MongoCollection<Document> collection = mongoTemplate.getDb().getCollection(TABLE_NAME);
            Document find = new Document();
            find.append("_id", new Document("$in", accountSet));
            MongoCursor<Document> iter = collection.find(find).iterator();
            List<PhoneAccountData> target = new ArrayList<>();
            while (iter.hasNext()) {
                Document doc = iter.next();
                String docStr = doc.toJson();
                PhoneAccountData phoneAccountData = JSONObject.parseObject(docStr, PhoneAccountData.class);
                target.add(phoneAccountData);
            }
            return target;
        } catch (Exception e) {
            logger.error("msg={}", e.getMessage(), e);
        }
        return new ArrayList<>();
    }


    public void insertData(String account, String uid, String pwd,String phoneNum,String thirdUid) {
        try {
            MongoCollection<Document> collection = mongoTemplate.getDb().getCollection(TABLE_NAME);
            Document data = new Document("_id", account);
            data.put("uid", uid);
            data.put("pwd", pwd);
            data.put("c_time", DateHelper.getNowSeconds());
            data.put("register_uid", uid);
            data.put("register_third_uid", thirdUid);
            data.put("register_num", phoneNum);
            collection.insertOne(data);
        } catch (Exception e) {
            logger.error("insertData account={} uid={}", account, uid, e);
        }
    }


    public void updatePwd(String account, String newUid, String newPwd) {
        try {
            MongoCollection<Document> beansTotalDevote = mongoTemplate.getDb().getCollection(TABLE_NAME);
            BsonDocument filter = new BsonDocument("_id", new BsonString(account));
            BsonDocument update = new BsonDocument();
            BsonDocument doc = new BsonDocument();
            doc.append("uid", new BsonString(newUid));
            doc.append("pwd", new BsonString(newPwd));
//            doc.append("c_time", new BsonInt32(DateHelper.getNowSeconds()));
            update.append("$set", doc);
            beansTotalDevote.updateOne(filter, update, new UpdateOptions().upsert(false));
        } catch (Exception e) {
            logger.error("updatePwd account={} newUid={}", account, newUid, e);
        }
    }

    public long deleteByUid(String uid) {
        try {
            Criteria criteria = Criteria.where("uid").is(uid);
            Query dQuery = new Query(criteria);
            DeleteResult deleteResult = mongoTemplate.remove(dQuery, TABLE_NAME);
            return deleteResult.getDeletedCount();
        } catch (Exception e) {
            logger.error("find phone account data error. uid={} {}", uid, e.getMessage(), e);
        }
        return 0;
    }

    private String getCacheKey(String uid) {
        return "phoneAccount:" + uid;
    }

    public PhoneAccountData findDataByThirdUid(String registerThirdUid) {
        try {
            Criteria criteria = Criteria.where("register_third_uid").is(registerThirdUid);
            return mongoTemplate.findOne(new Query(criteria), PhoneAccountData.class);
        } catch (Exception e) {
            logger.error("findDataByThirdUid error. registerThirdUid={} {}", registerThirdUid, e.getMessage(), e);
            return null;
        }
    }
}
