package com.quhong.mongo.dao;

import com.quhong.config.CaffeineCacheConfig;
import com.quhong.mongo.config.MongoBean;
import com.quhong.mongo.data.GameEntryConfigData;
import jakarta.annotation.Resource;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.context.annotation.Lazy;
import org.springframework.data.domain.Sort;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.data.mongodb.core.query.Criteria;
import org.springframework.data.mongodb.core.query.Query;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2023/8/24
 */
@Lazy
@Component
public class GameEntryConfigDao {

    private final Logger logger = LoggerFactory.getLogger(getClass());

    public static final String TABLE_NAME = "game_entry_config";

    @Resource(name = MongoBean.MOVIES)
    private MongoTemplate mongoTemplate;

    @Cacheable(value = "findAllGameEntryConfigData", cacheManager = CaffeineCacheConfig.EXPIRE_1M_AFTER_WRITE)
    public List<GameEntryConfigData> findAll() {
        try {
            Query query = new Query(Criteria.where("status").is(1));
            Sort sort = Sort.by(Sort.Direction.ASC, "order");
            return mongoTemplate.find(query.with(sort), GameEntryConfigData.class);
        } catch (Exception e) {
            return null;
        }
    }

    @Cacheable(value = "findAllGameEntryConfigDataNotStatus", cacheManager = CaffeineCacheConfig.EXPIRE_1M_AFTER_WRITE)
    public List<GameEntryConfigData> findAllNotStatus() {
        try {
            return mongoTemplate.findAll(GameEntryConfigData.class);
        } catch (Exception e) {
            return null;
        }
    }
}
