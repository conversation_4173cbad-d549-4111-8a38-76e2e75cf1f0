package com.quhong.mongo.dao;

import com.quhong.cache.CacheMap;
import com.quhong.mongo.config.MongoBean;
import com.quhong.mongo.data.BuddleSourceData;
import com.quhong.mongo.data.FloatScreenSourceData;
import com.quhong.mongo.data.MReportData;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.context.annotation.Lazy;
import org.springframework.data.domain.Sort;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.data.mongodb.core.query.Criteria;
import org.springframework.data.mongodb.core.query.Query;
import org.springframework.data.mongodb.core.query.Update;
import org.springframework.stereotype.Component;
import org.springframework.util.StringUtils;

import jakarta.annotation.PostConstruct;
import jakarta.annotation.Resource;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;

@Component
@Lazy
public class MReportDao {
    private static final Logger logger = LoggerFactory.getLogger(MReportDao.class);

    public static final String TABLE_NAME = "report";

    @Resource(name = MongoBean.MOVIES)
    private MongoTemplate mongoTemplate;

    private final CacheMap<String, Boolean> cacheMap;

    public MReportDao() {
        this.cacheMap = new CacheMap<>();
    }

    @PostConstruct
    public void postInit() {
//        this.cacheMap.start();
    }

    public int reportCountFromDb(String aid,int startTime) {
        try {
            Criteria criteria = Criteria.where("aid").is(aid).and("mtime").gte(startTime);
            return (int)mongoTemplate.count(new Query(criteria), TABLE_NAME);
        } catch (Exception e) {
            logger.error("blockCountFromDb error. aid={} uid={} {}", aid, e.getMessage(), e);
        }
        return 0;
    }

    public MReportData findData(String uid, String targetId) {
        try {
            Criteria criteria = Criteria.where("uid").is(uid).and("target_id").is(targetId);
            Query query = new Query(criteria);
            Sort sort = Sort.by(Sort.Direction.DESC, "mtime");
            query.with(sort).limit(1);
            return mongoTemplate.findOne(query, MReportData.class);
        } catch (Exception e) {
            logger.error("find report data error. uid={} targetId={} {}", uid, targetId, e.getMessage(), e);
        }
        return null;
    }

    public void save(MReportData data) {
        try {
            mongoTemplate.save(data);
        } catch (Exception e) {
            logger.error("save report data error. uid={} targetId={} {}", data.getUid(), data.getTarget_id(), e.getMessage(), e);
        }
    }



    // 运营系统相关
    public List<MReportData> selectMReportDataPage(int newType, int status, String search, int start, int size) {
        try {
            // 查询条件
            Criteria criteria = new Criteria();
            criteria.and("mtime").gt(1676950953);

            if(newType != -1){
                criteria.and("new_type").is(newType);
            }

            if(status != -1){
                criteria.and("status").is(status);
            }

            if (!StringUtils.isEmpty(search)){
                criteria.and("uid").is(search);
            }

            Sort sort = Sort.by(Sort.Direction.DESC, "_id");
            Query query = new Query(criteria);
            query.with(sort).skip(start).limit(size);
            return mongoTemplate.find(query, MReportData.class);
        } catch (Exception e) {
            logger.error("selectMReportDataPage selectPage error.{}", e.getMessage(), e);
        }
        return Collections.emptyList();
    }


    public long selectCount(int status, String search) {
        // 查询条件
        Criteria criteria = new Criteria();
        criteria.and("mtime").gt(1676950953);
        if(status != -1){
            criteria.and("status").is(status);
        }

        if (!StringUtils.isEmpty(search)){
            criteria.and("uid").is(search);
        }

        return mongoTemplate.count(new Query(criteria), TABLE_NAME);
    }


    public MReportData getMReportDataByID(String docId){
        try{
            return mongoTemplate.findById(docId, MReportData.class);
        }catch (Exception e){
            logger.error("MReportData query error. msg = {}", e.getMessage(), e);

        }
        return null;
    }


    public void updateData(String docId, Update update) {
        try {
            mongoTemplate.updateFirst(new Query(Criteria.where("_id").is(docId)), update, TABLE_NAME);
        } catch (Exception e) {
            logger.error("report updateData error.  id={} {}", docId, e.getMessage(), e);
        }
    }




}
