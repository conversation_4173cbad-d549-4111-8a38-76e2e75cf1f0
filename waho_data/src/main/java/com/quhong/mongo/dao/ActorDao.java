package com.quhong.mongo.dao;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.mongodb.BasicDBObject;
import com.quhong.config.CaffeineCacheConfig;
import com.quhong.constant.AccountConstant;
import com.quhong.core.redis.BasePlayerRedis;
import com.quhong.core.utils.DateHelper;
import com.quhong.data.ActorData;
import com.quhong.data.ActorPushData;
import com.quhong.data.BaseActorData;
import com.quhong.data.UpdateUserInfoData;
import com.quhong.mongo.config.MongoBean;
import com.quhong.mongo.data.MongoActorData;
import com.quhong.mysql.dao.UserMoneyDao;
import com.quhong.redis.DataRedisBean;
import com.quhong.service.MysteryService;
import com.quhong.service.mysql.MySQLActorService;
import jakarta.annotation.Resource;
import org.bson.BsonRegularExpression;
import org.bson.types.ObjectId;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.data.mongodb.core.BulkOperations;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.data.mongodb.core.query.BasicQuery;
import org.springframework.data.mongodb.core.query.Criteria;
import org.springframework.data.mongodb.core.query.Query;
import org.springframework.data.mongodb.core.query.Update;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;
import org.springframework.util.ObjectUtils;
import org.springframework.util.StringUtils;

import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.regex.Pattern;

/**
 * 用户信息
 */
@Component
public class ActorDao {
    public static final Logger logger = LoggerFactory.getLogger(ActorDao.class);

    public static final String TABLE_NAME = "actor";

    public static final int LOGIN_STATUS = 1; // 在线状态
    public static final int LOGOUT_STATUS = 0; // 离线状态

    private static final long EXPIRE_DAY = 2;

    @Autowired
    private BasePlayerRedis playerRedis;
    @Resource(name = DataRedisBean.MAIN_CLUSTER)
    private StringRedisTemplate mainRedisTemplate;
    @Resource(name = MongoBean.MOVIES)
    private MongoTemplate mongoTemplate;
    private long updateLogoutRedisSTime;
    @Resource
    private MySQLActorService mySQLActorService;
    @Resource
    private UserMoneyDao userMoneyDao;
    @Resource
    private MysteryService mysteryService;

    /**
     * 获取用户余额
     */
    public long getBalance(String uid) {
        return userMoneyDao.getBalance(uid);
    }

    /**
     * 获取用户余额
     */
    public int getIntBalance(String uid) {
        return (int) getBalance(uid);
    }

    /**
     * 开启了隐身用户的actor
     */
    @Cacheable(value = "cache", cacheManager = CaffeineCacheConfig.EXPIRE_15S_AFTER_WRITE,
            key = "#root.method.name+T(org.springframework.util.StringUtils).arrayToDelimitedString(args,'-')")
    public ActorData getMysteryActorFromCache(String uid) {
        return mysteryService.fillMysteryData(getActorData(uid));
    }

    @Cacheable(value = "cache", cacheManager = CaffeineCacheConfig.EXPIRE_1M_AFTER_WRITE,
            key = "#root.method.name+T(org.springframework.util.StringUtils).arrayToDelimitedString(args,'-')")
    public ActorData getActorDataFromCache(String uid) {
        return getActorData(uid);
    }

    @Cacheable(value = "cache", cacheManager = CaffeineCacheConfig.EXPIRE_10M_AFTER_WRITE,
            key = "#root.method.name+T(org.springframework.util.StringUtils).arrayToDelimitedString(args,'-')")
    public ActorData getActorFromPersistentCache(String uid) {
        return getActorData(uid);
    }

    @Cacheable(value = "cache", cacheManager = CaffeineCacheConfig.EXPIRE_15S_AFTER_WRITE,
            key = "#root.method.name+T(org.springframework.util.StringUtils).arrayToDelimitedString(args,'-')")
    public ActorData getActorByRid(int rid) {
        Criteria criteria = Criteria.where("rid").is(rid);
        MongoActorData mongoActorData = mongoTemplate.findOne(new Query(criteria), MongoActorData.class, TABLE_NAME);
        ActorData actorData = null;
        if (mongoActorData != null) {
            actorData = new ActorData();
            mongoActorData.copyTo(actorData);
        }
        return actorData;
    }

    @Cacheable(value = "cache", cacheManager = CaffeineCacheConfig.EXPIRE_15S_AFTER_WRITE,
            condition = "T(com.quhong.core.config.ServerConfig).isProduct()",
            key = "#root.method.name+T(org.springframework.util.StringUtils).arrayToDelimitedString(args,'-')")
    public List<ActorData> searchActorByRid(String rid, int page, int pageSize) {
        if (ObjectUtils.isEmpty(rid)) {
            return Collections.emptyList();
        }
        int intRid = 0;
        try {
            intRid = Integer.parseInt(rid);
        } catch (NumberFormatException ignored) {
        }
        Criteria criteria = new Criteria();
        criteria.orOperator(Criteria.where("alphaRid").regex(Pattern.compile("^" + Pattern.quote(rid) + ".*")), Criteria.where("rid").is(intRid));
        Query query = new Query(criteria);
        query.skip((long) pageSize * ((page <= 0 ? 1 : page) - 1)).limit(pageSize);
        List<MongoActorData> mongoActorDataList = mongoTemplate.find(query, MongoActorData.class, TABLE_NAME);
        List<ActorData> result = new ArrayList<>();
        for (MongoActorData mongoActorData : mongoActorDataList) {
            ActorData actorData = new ActorData();
            mongoActorData.copyTo(actorData);
            result.add(actorData);
        }
        return result;
    }

    public ActorData getActorByRidNoCache(int rid) {
        Criteria criteria = Criteria.where("rid").is(rid);
        MongoActorData mongoActorData = mongoTemplate.findOne(new Query(criteria), MongoActorData.class, TABLE_NAME);
        ActorData actorData = null;
        if (mongoActorData != null) {
            actorData = new ActorData();
            mongoActorData.copyTo(actorData);
        }
        return actorData;
    }

    @Cacheable(value = "cache", cacheManager = CaffeineCacheConfig.EXPIRE_15S_AFTER_WRITE,
            key = "#root.method.name+T(org.springframework.util.StringUtils).arrayToDelimitedString(args,'-')")
    public ActorData getActorByRidOrAlphaRid(String strRid) {
        return getActorByRidOrAlphaRidFromDb(strRid);
    }

    public ActorData getActorByRidOrAlphaRidFromDb(String strRid) {
        if (ObjectUtils.isEmpty(strRid)) {
            return null;
        }
        int intRid = 0;
        try {
            intRid = Integer.parseInt(strRid);
        } catch (NumberFormatException ignored) {
        }
        Criteria criteria = new Criteria();
        criteria.orOperator(Criteria.where("alphaRid").is(strRid), Criteria.where("rid").is(intRid));
        MongoActorData mongoActorData = mongoTemplate.findOne(new Query(criteria), MongoActorData.class, TABLE_NAME);
        ActorData actorData = null;
        if (mongoActorData != null) {
            actorData = new ActorData();
            mongoActorData.copyTo(actorData);
        }
        return actorData;
    }

    @Cacheable(value = "cache", cacheManager = CaffeineCacheConfig.EXPIRE_1M_AFTER_WRITE,
            key = "#root.method.name+T(org.springframework.util.StringUtils).arrayToDelimitedString(args,'-')")
    public List<MongoActorData> searchByRegularName(String key) {
        if (key.length() >= 30) {
            return Collections.emptyList();
        }
        try {
            key = key.trim();
            Criteria criteria = Criteria.where("name").is(key);
            Query query = new Query(criteria);
            query.limit(10);
            return mongoTemplate.find(query, MongoActorData.class, TABLE_NAME);

        } catch (Exception e) {
            logger.error("searchByRegularKey error. key={} error={}", key, e.getMessage(), e);
        }
        return Collections.emptyList();
    }

    @Cacheable(value = "cache", cacheManager = CaffeineCacheConfig.EXPIRE_1M_AFTER_WRITE,
            key = "#root.method.name+T(org.springframework.util.StringUtils).arrayToDelimitedString(args,'-')")
    public List<ActorData> searchByKey(String key) {
        key = key.trim();
        if (key.length() >= 30) {
            return Collections.emptyList();
        }
        try {
            ActorData actorData = getActorByRidOrAlphaRid(key);
            if (null != actorData && 1 == actorData.getValid() && AccountConstant.DELETED != actorData.getAccountStatus()) {
                return Collections.singletonList(actorData);
            }
        } catch (NumberFormatException e) {
            // 字符串搜索
        }
        logger.info("search actor by key, cannot not find actor. key={}", key);
        return Collections.emptyList();
    }

    @Cacheable(value = "cache", cacheManager = CaffeineCacheConfig.EXPIRE_1M_AFTER_WRITE,
            key = "#root.method.name+T(org.springframework.util.StringUtils).arrayToDelimitedString(args,'-')")
    public ActorData getActorByName(String name) {
        Criteria criteria = Criteria.where("name").is(name);
        MongoActorData mongoActorData = mongoTemplate.findOne(new Query(criteria), MongoActorData.class, TABLE_NAME);
        ActorData actorData = null;
        if (mongoActorData != null) {
            actorData = new ActorData();
            mongoActorData.copyTo(actorData);
        }
        return actorData;
    }

    public List<MongoActorData> getActorsByName(String name, Set<String> uidSet) {
        Criteria criteria = Criteria.where("_id").in(uidSet).and("name").regex(new BsonRegularExpression(".*?" + name + ".*", "i"));
        Query query = new Query(criteria);
        query.limit(10);
        return mongoTemplate.find(query, MongoActorData.class, TABLE_NAME);
    }

    public List<MongoActorData> getActors(Set<String> uidSet) {
        Criteria criteria = Criteria.where("_id").in(uidSet);
        Query query = new Query(criteria);
        query.limit(3000);
        return mongoTemplate.find(query, MongoActorData.class, TABLE_NAME);
    }

    /**
     * 获取actorData
     *
     * @param uid
     * @return
     */
    public ActorData getActorData(String uid) {
        if (ObjectUtils.isEmpty(uid) || uid.length() < 24) {
            return null;
        }
        ActorData actorData = playerRedis.getActorFromRedis(uid);
        if (actorData == null) {
            JSONObject jsonObject = findActorJSONFromDB(uid);
            actorData = playerRedis.saveActor(uid, jsonObject);
            if (actorData == null) {
                MongoActorData mongoActorData = findActorDataFromDB(uid);
                if (mongoActorData != null) {
                    actorData = new ActorData();
                    mongoActorData.copyTo(actorData);
                }
            }
        }
        return actorData;
    }

    /**
     * 获得actor的json字符串
     *
     * @param uid
     * @return
     */
    public JSONObject findActorJSONFromDB(String uid) {
        try {
            BasicDBObject obj = new BasicDBObject();
            obj.put("_id", new ObjectId(uid));
            String jsonStr = mongoTemplate.findOne(new BasicQuery(obj.toString()), String.class, TABLE_NAME);
            if (StringUtils.isEmpty(jsonStr)) {
                return null;
            }
            return JSON.parseObject(jsonStr);
        } catch (Exception e) {
            logger.error("get actor from mongodb. uid={} {}", uid, e.getMessage(), e);
        }
        return null;
    }

    /**
     * 返回自定义actor对象
     */
    public <T extends BaseActorData> T getActorFromRedis(String uid, Class<T> entityClass) {
        try {
            String actorJson = playerRedis.getActorJsonFromRedis(uid);
            if (null == actorJson) {
                JSONObject actorJSONFromDB = findActorJSONFromDB(uid);
                if (actorJSONFromDB == null) {
                    return null;
                }
                actorJson = actorJSONFromDB.toJSONString();
            }
            T t = null;
            try {
                t = JSON.parseObject(actorJson, entityClass);
                t.setUid(uid);
            } catch (Exception e) {
                logger.error("parse actor error. uid={} json={}", uid, actorJson, e);
            }
            return t;
        } catch (Exception e) {
            logger.error("get actor data from redis. uid={} {}", uid, e.getMessage(), e);
            return null;
        }
    }

    public MongoActorData findActorDataFromDB(String uid) {
        try {
            Criteria criteria = Criteria.where("_id").is(new ObjectId(uid));
            return mongoTemplate.findOne(new Query(criteria), MongoActorData.class);
        } catch (Exception e) {
            logger.error("get actor from db error. uid={} {}", uid, e.getMessage(), e);
        }
        return null;
    }

    public void updateRedis(String uid) {
        JSONObject actorObj = findActorJSONFromDB(uid);
        playerRedis.saveActor(uid, actorObj);
    }

    /**
     * 更新用户最后登录信息
     *
     * @param uid 用户uid
     */
    public void updateLastLoginInfo(String uid) {
        try {
            JSONObject jsonObject = mongoTemplate.findById(new ObjectId(uid), JSONObject.class, TABLE_NAME);
            if (null == jsonObject || jsonObject.isEmpty()) {
                logger.error("find actor error. json object is empty. uid={}", uid);
                return;
            }
            JSONObject lastLogin = jsonObject.getJSONObject("last_login");
            Map<String, Object> map;
            if (lastLogin == null) {
                map = new HashMap<>();
            } else {
                map = lastLogin.getInnerMap();
            }
            map.put("status", LOGIN_STATUS);
            map.put("login_time", DateHelper.getNowSeconds());
            // 更新mongodb数据
            Query query = new Query(Criteria.where("_id").is(uid));
            Update update = new Update();
            update.set("last_login", map);
            mongoTemplate.updateFirst(query, update, TABLE_NAME);
            // 删除离线状态
            removeLogoutTime(uid);
        } catch (Exception e) {
            logger.error("update last login time. uid={} {}", uid, e.getMessage(), e);
        }
    }


    /**
     * 更新用户最后退出信息
     *
     * @param uid 用户uid
     */
    public void updateLastLogoutInfo(String uid) {
        try {
            JSONObject jsonObject = mongoTemplate.findById(new ObjectId(uid), JSONObject.class, TABLE_NAME);
            if (null == jsonObject || jsonObject.isEmpty()) {
                logger.error("find actor error. json object is empty. uid={}", uid);
                return;
            }
            JSONObject lastLogin = jsonObject.getJSONObject("last_login");
            Map<String, Object> map;
            if (lastLogin == null) {
                map = new HashMap<>();
            } else {
                map = lastLogin.getInnerMap();
            }
            map.put("status", LOGOUT_STATUS);
            map.put("logout_time", DateHelper.getNowSeconds());
            // 更新mongodb数据
            Query query = new Query(Criteria.where("_id").is(uid));
            Update update = new Update();
            update.set("last_login", map);
            mongoTemplate.updateFirst(query, update, TABLE_NAME);
            // 更新离线时间到zset，用于评分
            setLogoutTime(uid, DateHelper.getNowSeconds());
        } catch (Exception e) {
            logger.error("update last logout time. uid={} {}", uid, e.getMessage(), e);
        }
    }

    public void invalidActor(String uid) {
        try {
            // 更新mongodb数据
            Query query = new Query(Criteria.where("_id").is(new ObjectId(uid)));
            Update update = new Update();
            update.set("valid", 0);
            update.set("beans", 0);
            update.set("gold", 0);
            mongoTemplate.updateFirst(query, update, TABLE_NAME);
            updateRedis(uid);
        } catch (Exception e) {
            logger.error("invalid actor error. uid={} {}", uid, e.getMessage(), e);
        }
    }

    public void incrMomentUnread(Set<String> uidSet) {
        try {
            if (CollectionUtils.isEmpty(uidSet)) {
                return;
            }
            BulkOperations bulkOps = mongoTemplate.bulkOps(BulkOperations.BulkMode.UNORDERED, TABLE_NAME);
            for (String uid : uidSet) {
                Query query = new Query(Criteria.where("_id").is(uid));
                Update update = new Update().inc("moment_unread", 1);
                bulkOps.updateOne(query, update);
            }
            bulkOps.execute();
        } catch (Exception e) {
            logger.error("incrMomentUnread error. uidSize={} {}", uidSet.size(), e.getMessage(), e);
        }
    }

    public void clearMomentUnread(String uid) {
        try {
            Query query = new Query(Criteria.where("_id").is(uid));
            Update update = new Update();
            update.set("moment_unread", 0);
            mongoTemplate.updateFirst(query, update, TABLE_NAME);
        } catch (Exception e) {
            logger.error("clearMomentUnread error. uid={} {}", uid, e.getMessage(), e);
        }
    }

    public int getMomentUnread(String uid) {
        try {
            JSONObject actorJSONFromDB = findActorJSONFromDB(uid);
            if (actorJSONFromDB == null) {
                return 0;
            }
            return actorJSONFromDB.getIntValue("moment_unread");
        } catch (Exception e) {
            logger.error("getMomentUnread error. uid={} {}", uid, e.getMessage(), e);
            return 0;
        }
    }

    public void updateHeart(String uid, long heart) {
        Query query = new Query(Criteria.where("_id").is(new ObjectId(uid)));
        Update update = new Update();
        update.set("heart_got", heart);
        mongoTemplate.updateFirst(query, update, TABLE_NAME);
        updateRedis(uid);
    }

    public void updateRoom(String uid, String room) {
        try {
            // 更新mongodb数据
            Query query = new Query(Criteria.where("_id").is(new ObjectId(uid)));
            Update update = new Update();
            update.set("room", room);
            mongoTemplate.updateFirst(query, update, TABLE_NAME);
            updateRedis(uid);
        } catch (Exception e) {
            logger.error("update room error. uid={} {}", uid, e.getMessage(), e);
        }
    }

    public boolean updateAccountStatus(String uid, int value) {
        try {
            Query query = new Query(Criteria.where("_id").is(new ObjectId(uid)));
            Update update = new Update();
            update.set("account_status", value);
            mongoTemplate.updateFirst(query, update, TABLE_NAME);
            JSONObject actorObj = findActorJSONFromDB(uid);
            playerRedis.saveActor(uid, actorObj);
            mySQLActorService.updateActor(uid, value);
            logger.info("update account status value={}", value);
            return value == actorObj.getIntValue("account_status");
        } catch (Exception e) {
            logger.error("delete account status error. uid={} {}", uid, e.getMessage(), e);
            return false;
        }
    }

    /**
     * 设置语音首选项
     */
    public void updateVideoOption(String uid, String videoOption) {
        try {
            Query query = new Query(Criteria.where("_id").is(new ObjectId(uid)));
            Update update = new Update();
            update.set("video_option", videoOption);
            mongoTemplate.updateFirst(query, update, TABLE_NAME);
            updateRedis(uid);
        } catch (Exception e) {
            logger.error("update video option error. uid={} {}", uid, e.getMessage(), e);
        }
    }

    /**
     * 设置用户所属区域
     */
    public void updateActorArea(String uid, int nlang, int area) {
        try {
            Query query = new Query(Criteria.where("_id").is(new ObjectId(uid)));
            Update update = new Update();
            update.set("nlang", nlang);
            update.set("area", area);
            mongoTemplate.updateFirst(query, update, TABLE_NAME);
            updateRedis(uid);
        } catch (Exception e) {
            logger.error("update area error. uid={} {}", uid, e.getMessage(), e);
        }
    }

    /**
     * 更新某个字段
     */
    public void updateField(String uid, String key, Object value) {
        Query query = new Query(Criteria.where("_id").is(new ObjectId(uid)));
        Update update = new Update();
        update.set(key, value);
        mongoTemplate.updateFirst(query, update, TABLE_NAME);
        updateRedis(uid);
    }

    /**
     * 设置坐骑首选项
     */
    public void updateRideOption(String uid, int rideOption) {
        try {
            Query query = new Query(Criteria.where("_id").is(new ObjectId(uid)));
            Update update = new Update();
            update.set("ride_option", rideOption);
            mongoTemplate.updateFirst(query, update, TABLE_NAME);
            updateRedis(uid);
        } catch (Exception e) {
            logger.error("update ride option error. uid={} {}", uid, e.getMessage(), e);
        }
    }

    /**
     * 设置语音首选项
     */
    public void updateAcceptTalk(String uid, int acceptTalk) {
        try {
            Query query = new Query(Criteria.where("_id").is(new ObjectId(uid)));
            Update update = new Update();
            update.set("accept_talk", acceptTalk);
            mongoTemplate.updateFirst(query, update, TABLE_NAME);
            updateRedis(uid);
        } catch (Exception e) {
            logger.error("update accept_talk error. uid={} {}", uid, e.getMessage(), e);
        }
    }

    /**
     * 获取不活跃用户
     *
     * @param inactiveTime
     * @param page
     * @param pageSize
     * @return
     */
    public List<MongoActorData> pageInactiveActors(int inactiveTime, int page, int pageSize) {
        try {
            Query query = new Query(Criteria.where("last_login.login_time").lt(inactiveTime));
            if (page <= 0) {
                page = 1;
            }
            if (pageSize <= 0) {
                pageSize = 10;
            }
            query.skip(pageSize * (page - 1)).limit(pageSize);
            return mongoTemplate.find(query, MongoActorData.class);
        } catch (Exception e) {
            logger.error("page inactive actors error. {}", e.getMessage(), e);
        }
        return null;
    }

    public void setLogoutTime(String uid, long secondTime) {
        mainRedisTemplate.opsForZSet().add(getLastLogoutZSetKey(), uid, secondTime);
        if (System.currentTimeMillis() - updateLogoutRedisSTime > 86400 * 1000) {
            updateLogoutRedisSTime = System.currentTimeMillis();
            mainRedisTemplate.expire(getLastLogoutZSetKey(), EXPIRE_DAY, TimeUnit.DAYS);
            // 移除两天内过期的
            removeExpireLogouts();
        }
    }

    public void removeLogoutTime(String uid) {
        mainRedisTemplate.opsForZSet().remove(getLastLogoutZSetKey(), uid);
    }

    public void removeExpireLogouts() {
        long time = DateHelper.getNowSeconds();
        mainRedisTemplate.opsForZSet().removeRangeByScore(getLastLogoutZSetKey(), 0, time - 2 * 86400);
    }

    private String getLastLogoutZSetKey() {
        return "zset:actor:logout";
    }

    /**
     * 更新用户设置
     *
     * @param uid 用户uid
     */
    public void updateGeneralConf(String uid, String key, int value) {
        try {
            JSONObject jsonObject = mongoTemplate.findById(new ObjectId(uid), JSONObject.class, TABLE_NAME);
            if (null == jsonObject || jsonObject.isEmpty()) {
                logger.error("find actor error. json object is empty. uid={}", uid);
                return;
            }
            JSONObject lastLogin = jsonObject.getJSONObject("general_conf");
            Map<String, Object> map;
            if (lastLogin == null) {
                map = new HashMap<>();
            } else {
                map = lastLogin.getInnerMap();
            }
            map.put(key, value);
            // 更新mongodb数据
            Query query = new Query(Criteria.where("_id").is(uid));
            Update update = new Update();
            update.set("general_conf", map);
            mongoTemplate.updateFirst(query, update, TABLE_NAME);
            updateRedis(uid);
        } catch (Exception e) {
            logger.error("update general conf. uid={} {}", uid, e.getMessage(), e);
        }
    }

    public void updatePush(String uid, ActorPushData actorPushData) {
        try {
            Query query = new Query(Criteria.where("_id").is(new ObjectId(uid)));
            Update update = new Update();
            update.set("push", actorPushData);
            mongoTemplate.updateFirst(query, update, TABLE_NAME);
            updateRedis(uid);
        } catch (Exception e) {
            logger.error("update push. uid={} {}", uid, e.getMessage(), e);
        }
    }

    /**
     * 获取用户性别，未找到时返回0
     *
     * @return 用户性别 1男 2女
     */
    public int getGender(String uid) {
        ActorData actorData = getActorDataFromCache(uid);
        if (null == actorData) {
            logger.error("get actor data from cache error. can not find actor. uid={}", uid);
            return 0;
        }
        return actorData.getFb_gender() == 1 ? 1 : 2;
    }

    public int findCountByTnId(String tnId) {
        try {
            Criteria criteria = Criteria.where("tn_id").is(tnId);
            return (int) mongoTemplate.count(new Query(criteria), MongoActorData.class);
        } catch (Exception e) {
            logger.error("get device count from db error. tnId={} {}", tnId, e.getMessage(), e);
        }
        return 0;
    }

    public List<MongoActorData> findListByTnId(String tnId) {
        try {
            Criteria criteria = Criteria.where("tn_id").is(tnId);
            return mongoTemplate.find(new Query(criteria), MongoActorData.class);
        } catch (Exception e) {
            logger.error("findListByTnId error. tnId={} {}", tnId, e.getMessage(), e);
            return new ArrayList<>();
        }
    }

    public MongoActorData findOneByTnId(String tnId) {
        try {
            Criteria criteria = Criteria.where("tn_id").is(tnId);
            return mongoTemplate.findOne(new Query(criteria).limit(1), MongoActorData.class);
        } catch (Exception e) {
            logger.error("findOneByTnId error. tnId={} {}", tnId, e.getMessage(), e);
            return null;
        }
    }

    public List<MongoActorData> findListByIp(String ip) {
        try {
            Criteria criteria = Criteria.where("ip").is(ip);
            return mongoTemplate.find(new Query(criteria), MongoActorData.class);
        } catch (Exception e) {
            logger.error("findListByIp error. ip={} {}", ip, e.getMessage(), e);
            return new ArrayList<>();
        }
    }

    public void deleteAlpha(String uid) {
        try {
            // 更新mongodb数据
            Query query = new Query(Criteria.where("_id").is(new ObjectId(uid)));
            Update update = new Update();
            update.set("alphaRid", "");
            update.set("alphaLevel", 0);
            mongoTemplate.updateFirst(query, update, TABLE_NAME);
            updateRedis(uid);
        } catch (Exception e) {
            logger.error("invalid actor error. uid={} {}", uid, e.getMessage(), e);
        }
    }

    public void changeRid(String uid, String alphaRid, int alphaLevel) {
        try {
            // 更新mongodb数据
            Query query = new Query(Criteria.where("_id").is(new ObjectId(uid)));
            Update update = new Update();
            update.set("alphaRid", alphaRid);
            update.set("alphaLevel", alphaLevel);
            mongoTemplate.updateFirst(query, update, TABLE_NAME);
            updateRedis(uid);
        } catch (Exception e) {
            logger.error("invalid actor error. uid={} alphaRid={} alphaLevel={} {}", uid, alphaRid, alphaLevel, e.getMessage(), e);
        }
    }

    /**
     * 更改用户个人资料
     */
    public void updateActorUserInfo(String uid, UpdateUserInfoData userInfoData) {
        try {
            Query query = new Query(Criteria.where("_id").is(new ObjectId(uid)));
            Update update = new Update();
            boolean isUpdate = false;
            if (!StringUtils.isEmpty(userInfoData.getName())) {
                update.set("name", userInfoData.getName());
                isUpdate = true;
            }
            if (!StringUtils.isEmpty(userInfoData.getDesc())) {
                update.set("desc", userInfoData.getDesc());
                isUpdate = true;
            }
            if (!StringUtils.isEmpty(userInfoData.getHead())) {
                update.set("head", userInfoData.getHead());
                update.set("is_face", 2);
                isUpdate = true;
            }
            if (!StringUtils.isEmpty(userInfoData.getCover())) {
                update.set("cover", userInfoData.getCover());
                isUpdate = true;
            }
            if (userInfoData.isUpdateGeneralConf()) {
                Map<String, Object> map;
                if (CollectionUtils.isEmpty(userInfoData.getGeneral_conf())) {
                    map = new HashMap<>();
                } else {
                    map = userInfoData.getGeneral_conf();
                }
                if (userInfoData.getHeadIndex() != null) {
                    map.put("headIndex", userInfoData.getHeadIndex());
                }
                if (userInfoData.getFill_done() != null) {
                    map.put("fill_done", 1);
                }
                if (userInfoData.getFill_gender() != null) {
                    map.put("fill_gender", 1);
                }
                if (userInfoData.getFill_album() != null) {
                    map.put("fill_album", 1);
                }
                if (userInfoData.getFill_interests() != null) {
                    map.put("fill_interests", 1);
                }
                if (userInfoData.getFill_about_me() != null) {
                    map.put("fill_about_me", 1);
                }
                update.set("general_conf", map);
                userInfoData.setGeneral_conf(map);
                isUpdate = true;
            }

            if (userInfoData.getBanner() != null) {
                update.set("banner", userInfoData.getBanner());
                isUpdate = true;
            }
            if (!CollectionUtils.isEmpty(userInfoData.getLabel_list())) {
                update.set("label_list", userInfoData.getLabel_list());
                isUpdate = true;
            }
            if (!StringUtils.isEmpty(userInfoData.getBirthday())) {
                update.set("birthday", userInfoData.getBirthday());
                isUpdate = true;
            }
            if (userInfoData.getAge() != null && userInfoData.getAge() >= 18) {
                update.set("age", userInfoData.getAge());
                isUpdate = true;
            }
            if (userInfoData.getFb_gender() != null) {
                update.set("fb_gender", userInfoData.getFb_gender());
                isUpdate = true;
            }
            if (!StringUtils.isEmpty(userInfoData.getCity())) {
                update.set("city", userInfoData.getCity());
                isUpdate = true;
            }
            if (!StringUtils.isEmpty(userInfoData.getPhone())) {
                update.set("phone", userInfoData.getPhone());
                isUpdate = true;
            }
            if (!StringUtils.isEmpty(userInfoData.getCountry())) {
                update.set("country", userInfoData.getCountry());
                isUpdate = true;
            }
            if (userInfoData.getNlang() != null) {
                update.set("nlang", userInfoData.getNlang());
                update.set("area", userInfoData.getNlang());
                isUpdate = true;
            }
            if (userInfoData.getImprove_data() != null) {
                update.set("improve_data", userInfoData.getImprove_data());
                isUpdate = true;
            }
            if (isUpdate) {
                mongoTemplate.updateFirst(query, update, TABLE_NAME);
                updateRedis(uid);
            } else {
                logger.info("update user info error. nothing change uid={} userInfoData={}", uid, userInfoData);
            }
        } catch (Exception e) {
            logger.error("update user info error. uid={} userInfoData={} msg={}", uid, userInfoData, e.getMessage(), e);
        }
    }


    /**
     * 更改用户图片
     */
    public void updateUnSafePic(String uid, UpdateUserInfoData userInfoData) {
        try {
            Query query = new Query(Criteria.where("_id").is(new ObjectId(uid)));
            Update update = new Update();
            boolean isUpdate = false;
            if (!StringUtils.isEmpty(userInfoData.getHead())) {
                update.set("head", userInfoData.getHead());
                update.set("is_face", 0);
                isUpdate = true;
            }
            if (!StringUtils.isEmpty(userInfoData.getCover()) || userInfoData.isSetCover()) {
                update.set("cover", userInfoData.getCover());
                isUpdate = true;
            }
            if (userInfoData.isUpdateGeneralConf()) {
                Map<String, Object> map;
                if (CollectionUtils.isEmpty(userInfoData.getGeneral_conf())) {
                    map = new HashMap<>();
                } else {
                    map = userInfoData.getGeneral_conf();
                }
                if (userInfoData.getHeadIndex() != null) {
                    map.put("headIndex", userInfoData.getHeadIndex());
                }
                update.set("general_conf", map);
                isUpdate = true;
            }
            if (userInfoData.getBanner() != null) {
                update.set("banner", userInfoData.getBanner());
                isUpdate = true;
            }
            if (isUpdate) {
                mongoTemplate.updateFirst(query, update, TABLE_NAME);
                updateRedis(uid);
            } else {
                logger.info("updateUnSafePic user info error. nothing change uid={} userInfoData={}", uid, userInfoData);
            }
        } catch (Exception e) {
            logger.error("updateUnSafePic user info error. uid={} userInfoData={} msg={}", uid, userInfoData, e.getMessage(), e);
        }
    }


    /**
     * 更改用户版本号，包名
     */
    public void updateVersionCodeSlang(String uid, UpdateUserInfoData userInfoData) {
        try {
            Query query = new Query(Criteria.where("_id").is(new ObjectId(uid)));
            Update update = new Update();
            boolean isUpdate = false;
            if (!StringUtils.isEmpty(userInfoData.getAppPackageName())) {
                update.set("app_package_name", userInfoData.getAppPackageName());
                isUpdate = true;
            }
            if (userInfoData.getVersioncode() > 0) {
                update.set("version_code", userInfoData.getVersioncode());
                isUpdate = true;
            }
            if (userInfoData.getSlang() > 0) {
                update.set("slang", userInfoData.getSlang());
                isUpdate = true;
            }
            if (isUpdate) {
                mongoTemplate.updateFirst(query, update, TABLE_NAME);
                updateRedis(uid);
            } else {
                logger.info("updateVersionCodeSlang user info error. nothing change uid={} userInfoData={}", uid, userInfoData);
            }
        } catch (Exception e) {
            logger.error("updateVersionCodeSlang user info error. uid={} userInfoData={} msg={}", uid, userInfoData, e.getMessage(), e);
        }
    }

    /**
     * 注销账号
     */
    public void deleteActor(String uid, String bornUid) {
        try {
            if (StringUtils.isEmpty(bornUid)) {
                logger.info("bornUid is empty uid:{}", uid);
                return;
            }
            Query query = new Query(Criteria.where("_id").is(new ObjectId(uid)));
            Update update = new Update();
            update.set("uid", bornUid);
            update.set("tn_id", "");
            mongoTemplate.updateFirst(query, update, TABLE_NAME);
            updateRedis(uid);
        } catch (Exception e) {
            logger.error("deleteActor user info error. uid={} msg={}", uid, e.getMessage(), e);
        }
    }

    public List<MongoActorData> getNewRegisterUserList(int day) {
        try {
            Criteria criteria = Criteria.where("_id").gte(new ObjectId(DateHelper.ARABIAN.getBeforeDay(day)));
            return mongoTemplate.find(new Query(criteria), MongoActorData.class);
        } catch (Exception e) {
            logger.error("getNewRegisterUserList error. day={} {}", day, e.getMessage(), e);
            return new ArrayList<>();
        }
    }

    public void updateActorBanner(String uid, List<String> bannerList) {
        try {
            Query query = new Query(Criteria.where("_id").is(new ObjectId(uid)));
            Update update = new Update();
            update.set("banner", bannerList);
            mongoTemplate.updateFirst(query, update, TABLE_NAME);
            updateRedis(uid);
        } catch (Exception e) {
            logger.error("updateActorBanner error. uid={} msg={}", uid, e.getMessage(), e);
        }
    }

    public void updateVirtualDiamond(String uid, long virtualDiamonds) {
        Query query = new Query(Criteria.where("_id").is(new ObjectId(uid)));
        Update update = new Update();
        update.set("virtualDiamonds", virtualDiamonds);
        mongoTemplate.updateFirst(query, update, TABLE_NAME);
        updateRedis(uid);
    }

    /**
     * 设置用户为无效
     */
    public void updateInValid(String uid, String head, List<String> banner) {
        try {
            Query query = new Query(Criteria.where("_id").is(new ObjectId(uid)));
            Update update = new Update();
            update.set("valid", 0);
            update.set("head", head);
            update.set("banner", banner);
            update.set("is_face", 0);
            mongoTemplate.updateFirst(query, update, TABLE_NAME);
            updateRedis(uid);
        } catch (Exception e) {
            logger.error("updateInValid user info error. uid={}  msg={}", uid, e.getMessage(), e);
        }
    }

    public ActorData getActorByDistinctId(String distinctId) {
        Criteria criteria = Criteria.where("distinct_id").is(distinctId);
        MongoActorData mongoActorData = mongoTemplate.findOne(new Query(criteria), MongoActorData.class, TABLE_NAME);
        ActorData actorData = null;
        if (mongoActorData != null) {
            actorData = new ActorData();
            mongoActorData.copyTo(actorData);
        }
        return actorData;
    }
}
