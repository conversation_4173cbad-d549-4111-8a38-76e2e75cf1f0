package com.quhong.mongo.dao;

import com.mongodb.client.result.DeleteResult;
import com.quhong.core.config.ServerConfig;
import com.quhong.core.date.DateSupport;
import com.quhong.mongo.config.MongoBean;
import com.quhong.mongo.data.GameStatData;
import jakarta.annotation.Resource;
import org.bson.types.ObjectId;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.data.mongodb.core.aggregation.Aggregation;
import org.springframework.data.mongodb.core.query.Criteria;
import org.springframework.data.mongodb.core.query.Query;
import org.springframework.data.mongodb.core.query.Update;
import org.springframework.stereotype.Component;

import java.time.LocalDateTime;
import java.time.ZoneId;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2024/7/22
 */
@Component
public class GameStatDao {

    private final Logger logger = LoggerFactory.getLogger(getClass());

    /**
     * 所有游戏相关的aType
     */
    public static final List<Integer> ACT_GAME_TYPE_LIST = List.of(40,210,910,911,920,921,923,924,925,926,927,928,929,930,931,932,933,934,947,948,949,950,951,952,953,954,955,956,957,958,959,960,961,962,966,967,972,973,974,975,976,977,2060,2061,2070,2071,2110,2111,2120,2121,2130,2131);
    /**
     * BC游戏的aType
     */
    public static final List<Integer> BC_GAME_ACT_TYPE_LIST = List.of(920,921,947,948,949,950,951,952,953,954,955,956,957,958,959,960,961,962,966,967,972,973,974,975,976,977,2060,2061,2070,2071,2110,2111,2120,2121,2130,2131);
    /**
     * 计算游戏花费的钻石数相关aType
     */
    public static final List<Integer> GAME_COST_ACT_TYPE_LIST = List.of(40,67,68,210,910,920,923,925,926,928,929,931,932,934,947,949,951,953,955,957,959,961,966,972,974,976,2060,2070,2110,2120,2130);

    @Resource(name = MongoBean.MOVIES)
    private MongoTemplate mongoTemplate;

    /**
     * 增加魅力值统计数据
     *
     * @param uid       uid
     * @param actType  流水类型
     * @param title    流水标题
     * @param diamonds 钻石数
     */
    public void incrGameStat(String uid, int actType, String title, long diamonds) {
        try {
            if (diamonds == 0 || !ACT_GAME_TYPE_LIST.contains(actType)) {
                return;
            }
            int statDate = Integer.parseInt(DateSupport.yyyyMMdd(DateSupport.ARABIAN.getToday()));
            if (ServerConfig.isProduct() && statDate <= 20240723) {
                // 正式服从2024年07月24日开始写入数据
                return;
            }
            Query query = new Query(Criteria.where("uid").is(uid).and("statDate").is(statDate).and("actType").is(actType));
            Update update = new Update();
            update.set("bcGame", BC_GAME_ACT_TYPE_LIST.contains(actType) ? 1 : 0);
            update.set("costBean", GAME_COST_ACT_TYPE_LIST.contains(actType) ? 1 : 0);
            update.set("title", title);
            update.inc("diamonds", diamonds);
            mongoTemplate.upsert(query, update, GameStatData.class);
        } catch (Exception e) {
            logger.error("incrGameStat error uid={} actType={} title={} diamonds={}", uid, actType, title, diamonds, e);
        }
    }

    public void insertAll(List<GameStatData> list) {
        try {
            mongoTemplate.insertAll(list);
        } catch (Exception e) {
            logger.error("insertAll error.", e);
        }
    }

    /**
     * 清理360天前的旧数据
     */
    public long clearOldData() {
        try {
            ObjectId timeToCleanup = new ObjectId(Date.from(LocalDateTime.now().plusDays(-360).atZone(ZoneId.systemDefault()).toInstant()));
            Criteria criteria = Criteria.where("_id").lt(timeToCleanup);
            DeleteResult deleteResult = mongoTemplate.remove(new Query(criteria), GameStatData.class);
            return deleteResult.getDeletedCount();
        } catch (Exception e) {
            logger.error("clearOldData error.", e);
            return 0;
        }
    }

    public int getIntDateByTimestamp(int timestamp) {
        return Integer.parseInt(DateSupport.yyyyMMdd(DateSupport.ARABIAN.getLocalDate(timestamp * 1000L)));
    }

    public long getUserGameTotalCost(String uid, int startTime, int endTime) {
        try {
            int startDate = getIntDateByTimestamp(startTime);
            int endDate = getIntDateByTimestamp(endTime);
            Criteria criteria = Criteria.where("uid").is(uid).and("costBean").is(1).and("statDate").gte(startDate).lte(endDate);
            Aggregation agg = Aggregation.newAggregation(
                    Aggregation.match(criteria),
                    Aggregation.group().sum("diamonds").as("count")
            );
            List<CountData> result = mongoTemplate.aggregate(agg, GameStatData.class, CountData.class).getMappedResults();
            long count = 0;
            for (CountData countData : result) {
                count += countData.getCount();
            }
            return Math.abs(count);
        } catch (Exception e) {
            logger.error("getUserGameTotalCost error. uid={} startTime={} endTime={} {}", uid, startTime, endTime, e.getMessage(), e);
            return 0;
        }
    }

    public long getUsersGameTotalCost(List<String> uidList, int startTime, int endTime) {
        try {
            int startDate = getIntDateByTimestamp(startTime);
            int endDate = getIntDateByTimestamp(endTime);
            Criteria criteria = Criteria.where("uid").in(uidList).and("costBean").is(1).and("statDate").gte(startDate).lte(endDate);
            Aggregation agg = Aggregation.newAggregation(
                    Aggregation.match(criteria),
                    Aggregation.group().sum("diamonds").as("count")
            );
            List<CountData> result = mongoTemplate.aggregate(agg, GameStatData.class, CountData.class).getMappedResults();
            long count = 0;
            for (CountData countData : result) {
                count += countData.getCount();
            }
            return Math.abs(count);
        } catch (Exception e) {
            logger.error("getUserGameTotalCost error. uidList.size={} startTime={} endTime={} {}", uidList.size(), startTime, endTime, e.getMessage(), e);
            return 0;
        }
    }

    public Map<Integer, Long> getUserBcGameActTypeMap(String uid, int startTime, int endTime) {
        Map<Integer, Long> resultMap = new HashMap<>(BC_GAME_ACT_TYPE_LIST.size());
        try{
            int startDate = getIntDateByTimestamp(startTime);
            int endDate = getIntDateByTimestamp(endTime);
            Criteria criteria = Criteria.where("uid").is(uid).and("bcGame").is(1).and("statDate").gte(startDate).lte(endDate);
            Aggregation agg = Aggregation.newAggregation(
                    Aggregation.match(criteria),
                    Aggregation.group("actType").sum("diamonds").as("count")
                            .first("actType").as("key")
            );
            List<CountData> result = mongoTemplate.aggregate(agg, GameStatData.class, CountData.class).getMappedResults();
            for (CountData countData : result) {
                int actType = Integer.parseInt(countData.getKey());
                resultMap.put(actType, resultMap.getOrDefault(actType, 0L) + countData.getCount());
            }
        } catch (Exception e) {
            logger.error("getUserBcGameActTypeMap error uid={} startTime={} endTime={} error={}", uid, startTime, endTime, e.getMessage(), e);
        }
        return resultMap;
    }

    public long getUserBcGameProfitLoss(String uid, int startTime, int endTime) {
        try {
            int startDate = getIntDateByTimestamp(startTime);
            int endDate = getIntDateByTimestamp(endTime);
            Criteria criteria = Criteria.where("uid").is(uid).and("bcGame").is(1).and("statDate").gte(startDate).lte(endDate);
            Aggregation agg = Aggregation.newAggregation(
                    Aggregation.match(criteria),
                    Aggregation.group().sum("diamonds").as("count")
            );
            List<CountData> result = mongoTemplate.aggregate(agg, GameStatData.class, CountData.class).getMappedResults();
            long count = 0;
            for (CountData countData : result) {
                count += countData.getCount();
            }
            return count;
        } catch (Exception e) {
            logger.error("getUserBcGameProfitLoss error. uid={} startTime={} endTime={} {}", uid, startTime, endTime, e.getMessage(), e);
            return 0;
        }
    }

    public static class CountData {
        private long count;
        private String key;

        public long getCount() {
            return count;
        }

        public void setCount(long count) {
            this.count = count;
        }

        public String getKey() {
            return key;
        }

        public void setKey(String key) {
            this.key = key;
        }
    }
}
