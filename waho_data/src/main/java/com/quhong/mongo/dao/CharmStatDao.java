package com.quhong.mongo.dao;

import com.mongodb.client.result.DeleteResult;
import com.quhong.core.config.ServerConfig;
import com.quhong.core.date.DateSupport;
import com.quhong.data.CharmIncomeData;
import com.quhong.enums.CharmLogTypeEnum;
import com.quhong.mongo.config.MongoBean;
import com.quhong.mongo.data.CharmStatData;
import com.quhong.mongo.data.FamilyDevoteDetailData;
import com.quhong.utils.RoomUtils;
import jakarta.annotation.Resource;
import org.bson.types.ObjectId;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.data.domain.Sort;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.data.mongodb.core.aggregation.Aggregation;
import org.springframework.data.mongodb.core.query.Criteria;
import org.springframework.data.mongodb.core.query.Query;
import org.springframework.data.mongodb.core.query.Update;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;
import org.springframework.util.ObjectUtils;
import org.springframework.util.StringUtils;

import java.time.LocalDateTime;
import java.time.ZoneId;
import java.util.*;

@Component
public class CharmStatDao {
    private final Logger logger = LoggerFactory.getLogger(getClass());

    private static final List<Integer> RECEIVES_GIFT = List.of(1, 2); // 接收房间和私信礼物收益

    @Resource(name = MongoBean.MOVIES)
    private MongoTemplate mongoTemplate;

    /**
     * 增加魅力值统计数据
     *
     * @param uid      uid
     * @param familyId 公会主键id
     * @param logType  记录类型
     * @param charm    魅力值
     * @param roomId   房间id
     */
    public void incrCharmStat(String uid, int familyId, int logType, double charm, String roomId) {
        try {
            if (charm == 0) {
                return;
            }
            int statDate = Integer.parseInt(DateSupport.yyyyMMdd(DateSupport.ARABIAN.getToday()));
            Criteria criteria = Criteria.where("familyId").is(familyId).and("uid").is(uid).and("statDate").is(statDate).and("logType").is(logType);
            if (logType == 1) {
                int liveCharmIncome = RoomUtils.isLiveRoom(roomId) ? 1 : 0;
                criteria.and("liveCharmIncome").is(liveCharmIncome);
            }
            Query query = new Query(criteria);
            Update update = new Update();
            update.inc("charm", charm);
            mongoTemplate.upsert(query, update, CharmStatData.class);
        } catch (Exception e) {
            logger.error("incrCharmStat error uid={} logType={} familyId={} charm={}", uid, logType, familyId, charm, e);
        }
    }

    public void insertAll(List<CharmStatData> list) {
        try {
            mongoTemplate.insertAll(list);
        } catch (Exception e) {
            logger.error("insertAll error.", e);
        }
    }

    /**
     * 清理360天前的旧数据
     */
    public long clearOldData() {
        try {
            ObjectId timeToCleanup = new ObjectId(Date.from(LocalDateTime.now().plusDays(-360).atZone(ZoneId.systemDefault()).toInstant()));
            Criteria criteria = Criteria.where("_id").lt(timeToCleanup);
            DeleteResult deleteResult = mongoTemplate.remove(new Query(criteria), CharmStatData.class);
            return deleteResult.getDeletedCount();
        } catch (Exception e) {
            logger.error("clearOldData error.", e);
            return 0;
        }
    }

    public long getUserReceivesGiftCharm(String uid, Integer familyId, int startTime, int endTime) {
        int startDate = getIntDateByTimestamp(startTime);
        int endDate = getIntDateByTimestamp(endTime);
        return (long)getStatCharm(singleton(uid), singleton(familyId), RECEIVES_GIFT, startDate, endDate);
    }

    public long getUserTotalCharm(String uid, Integer familyId, Integer logType, int startTime, int endTime) {
        int startDate = getIntDateByTimestamp(startTime);
        int endDate = getIntDateByTimestamp(endTime);
        if (ServerConfig.isNotProduct()) {
            logger.info("getUserTotalCharm. uid={} startTime={} endTime={} startDate={} endDate={}", uid, startTime, endTime, startDate, endDate);
        }
        return (long)getStatCharm(singleton(uid), singleton(familyId), singleton(logType), startDate, endDate);
    }

    public long getUserTotalCharm(String uid, Integer familyId, List<Integer> logTypeList, int startTime, int endTime) {
        int startDate = getIntDateByTimestamp(startTime);
        int endDate = getIntDateByTimestamp(endTime);
        if (ServerConfig.isNotProduct()) {
            logger.info("getUserTotalCharm. uid={} startTime={} endTime={} startDate={} endDate={}", uid, startTime, endTime, startDate, endDate);
        }
        return (long)getStatCharm(singleton(uid), singleton(familyId), logTypeList, startDate, endDate);
    }

    public long getUsersTotalCharm(Collection<String> uids, Integer familyId, Integer logType, int startTime, int endTime) {
        int startDate = getIntDateByTimestamp(startTime);
        int endDate = getIntDateByTimestamp(endTime);
        return (long)getStatCharm(uids, singleton(familyId), singleton(logType), startDate, endDate);
    }

    public long getFamilyTotalCharm(Integer familyId, Integer logType, int startTime, int endTime) {
        int startDate = getIntDateByTimestamp(startTime);
        int endDate = getIntDateByTimestamp(endTime);
        return (long)getStatCharm(null, singleton(familyId), singleton(logType), startDate, endDate);
    }

    public double getStatCharm(Collection<String> uid, Collection<Integer> familyId, Collection<Integer> logType, int startDate, int endDate) {
        try {
            if (ObjectUtils.isEmpty(startDate) || ObjectUtils.isEmpty(endDate)) {
                return 0;
            }
            Criteria criteria = Criteria.where("statDate").gte(startDate).lte(endDate);
            if (!ObjectUtils.isEmpty(uid)) {
                criteria.and("uid").in(uid);
            }
            if (!ObjectUtils.isEmpty(familyId)) {
                criteria.and("familyId").in(familyId);
            }
            if (!ObjectUtils.isEmpty(logType)) {
                criteria.and("logType").in(logType);
            }
            return getCharmSum(criteria);
        } catch (Exception e) {
            logger.error("getStatCharm error familyId={} {}", familyId, e.getMessage(), e);
        }
        return 0;
    }

    public long getConsumeCharm(String uid, int startTime, int endTime) {
        try {
            int startDate = getIntDateByTimestamp(startTime);
            int endDate = getIntDateByTimestamp(endTime);
            Criteria criteria = Criteria.where("uid").is(uid).and("statDate").gte(startDate).lte(endDate).and("charm").lt(0);
            return (long)getCharmSum(criteria);
        } catch (Exception e) {
            logger.error("getConsumeCharm error uid={} startTime={} endTime={} {}", uid, startTime, endTime, e.getMessage(), e);
        }
        return 0;
    }

    public long getLiveCharmIncome(Integer familyId, String uid, int startTime, int endTime) {
        try {
            int startDate = getIntDateByTimestamp(startTime);
            int endDate = getIntDateByTimestamp(endTime);
            if (ObjectUtils.isEmpty(startDate) || ObjectUtils.isEmpty(endDate)) {
                return 0;
            }
            Criteria criteria = Criteria.where("statDate").gte(startDate).lte(endDate);
            if (StringUtils.hasLength(uid)) {
                criteria.and("uid").in(uid);
            }
            if (familyId != null && familyId != 0) {
                criteria.and("familyId").is(familyId);
            }
            criteria.and("logType").is(1).and("liveCharmIncome").is(1);
            return (long) getCharmSum(criteria);
        } catch (Exception e) {
            logger.error("getLiveCharmIncome error familyId={} {}", familyId, e.getMessage(), e);
        }
        return 0;
    }

    public long getChatCharmIncome(Integer familyId, String uid, int startTime, int endTime) {
        try {
            int startDate = getIntDateByTimestamp(startTime);
            int endDate = getIntDateByTimestamp(endTime);
            if (ObjectUtils.isEmpty(startDate) || ObjectUtils.isEmpty(endDate)) {
                return 0;
            }
            Criteria criteria = Criteria.where("statDate").gte(startDate).lte(endDate);
            if (StringUtils.hasLength(uid)) {
                criteria.and("uid").in(uid);
            }
            if (familyId != null && familyId != 0) {
                criteria.and("familyId").is(familyId);
            }
            criteria.and("logType").is(1).and("liveCharmIncome").is(0);
            return (long) getCharmSum(criteria);
        } catch (Exception e) {
            logger.error("getLiveCharmIncome error familyId={} {}", familyId, e.getMessage(), e);
        }
        return 0;
    }

    public int getHasIncomeAnchor(int familyId, int startTime, int endTime) {
        try {
            int startDate = getIntDateByTimestamp(startTime);
            int endDate = getIntDateByTimestamp(endTime);
            Criteria criteria = Criteria.where("familyId").is(familyId).and("logType").in(RECEIVES_GIFT).and("statDate").gte(startDate).lte(endDate);
            Aggregation agg = Aggregation.newAggregation(
                    Aggregation.match(criteria),
                    Aggregation.group("uid").count().as("count")
            );
            List<CountData> result = mongoTemplate.aggregate(agg, CharmStatData.class, CountData.class).getMappedResults();
            return result.size();
        } catch (Exception e) {
            logger.error("getHasIncomeAnchor error familyId={} startTime={} endTime={} {}", familyId, startTime, endTime, e.getMessage(), e);
        }
        return 0;
    }

    private double getCharmSum(Criteria criteria) {
        Aggregation agg = Aggregation.newAggregation(
                Aggregation.match(criteria),
                Aggregation.group().sum("charm").as("count")
        );
        List<CountData> result = mongoTemplate.aggregate(agg, CharmStatData.class, CountData.class).getMappedResults();
        double count = 0;
        for (CountData countData : result) {
            count += countData.getCount();
        }
        return count;
    }

    public List<CharmIncomeData> getFamilyMembersIncome(int familyId, Set<String> aidSet, int liveCharmIncome, int startTime, int endTime) {
        try {
            int startDate = getIntDateByTimestamp(startTime);
            int endDate = getIntDateByTimestamp(endTime);
            Criteria criteria = Criteria.where("familyId").is(familyId).and("logType").is(CharmLogTypeEnum.RECEIVE_ROOM_GIFTS.logType).and("statDate").gte(startDate).lte(endDate);
            if (liveCharmIncome >= 0) {
                criteria.and("liveCharmIncome").is(liveCharmIncome);
            }
            if (!CollectionUtils.isEmpty(aidSet)) {
                criteria.and("uid").in(aidSet);
            }
            Aggregation agg = Aggregation.newAggregation(
                    Aggregation.match(criteria),
                    Aggregation.group("uid").sum("charm").as("totalCharm").first("uid").as("uid"),
                    Aggregation.sort(Sort.by(Sort.Direction.DESC, "totalCharm")),
                    Aggregation.project("uid", "totalCharm").andExclude("_id")
            );
            return mongoTemplate.aggregate(agg, CharmStatData.class, CharmIncomeData.class).getMappedResults();
        } catch (Exception e) {
            logger.error("getFamilyMembersIncome error. {}", e.getMessage(), e);
            return Collections.emptyList();
        }
    }

    public int getIntDateByTimestamp(int timestamp) {
        return Integer.parseInt(DateSupport.yyyyMMdd(DateSupport.ARABIAN.getLocalDate(timestamp * 1000L)));
    }

    private <T> Collection<T> singleton(T t) {
        if (t == null) {
            return null;
        }
        if (t instanceof String) {
            return !Objects.equals(t, "") ? Collections.singleton(t) : null;
        } else if (t instanceof  Integer) {
            return !Objects.equals(t, 0) ? Collections.singleton(t) : null;
        } else {
            return Collections.singleton(t);
        }
    }

    public static class CountData {
        private double count;
        private String key;

        public double getCount() {
            return count;
        }

        public void setCount(double count) {
            this.count = count;
        }

        public String getKey() {
            return key;
        }

        public void setKey(String key) {
            this.key = key;
        }
    }
}
