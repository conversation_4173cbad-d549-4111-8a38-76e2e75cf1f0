package com.quhong.mongo.dao;

import com.quhong.mongo.config.MongoBean;
import com.quhong.mongo.data.BeautifulRidListData;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.data.mongodb.core.query.Criteria;
import org.springframework.data.mongodb.core.query.Query;
import org.springframework.data.mongodb.core.query.Update;
import org.springframework.stereotype.Component;

import jakarta.annotation.Resource;
import java.util.Collections;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2022/8/26
 */
@Component
public class BeautifulRidListDao {

    private static final Logger logger = LoggerFactory.getLogger(BeautifulRidListDao.class);

    public static final String TABLE_NAME = "beautiful_rid_list";

    @Resource(name = MongoBean.MOVIES)
    private MongoTemplate mongoTemplate;

    public BeautifulRidListData findData(int rid) {
        try {
            Criteria criteria = Criteria.where("rid").is(rid).and("status").is(0);
            return mongoTemplate.findOne(new Query(criteria), BeautifulRidListData.class);
        } catch (Exception e) {
            logger.error("find beautiful rid list data error. rid={} {}", rid, e.getMessage(), e);
        }
        return null;
    }

    public void updateStatus(int rid, int status, String bufferUid) {
        try {
            Criteria criteria = Criteria.where("rid").is(rid);
            Update update = new Update();
            update.set("status", status);
            update.set("buffer_uid", bufferUid);
            mongoTemplate.updateFirst(new Query(criteria), update, TABLE_NAME);
        } catch (Exception e) {
            logger.error("updateStatus BeautifulRidList data error. rid={} status={} bufferUid={} {}", rid, status, bufferUid,
                    e.getMessage(), e);
        }
    }


    public BeautifulRidListData findValidData(int rid) {
        try {
            Criteria criteria = Criteria.where("rid").is(rid);
            return mongoTemplate.findOne(new Query(criteria), BeautifulRidListData.class);
        } catch (Exception e) {
            logger.error("findValidData data error. rid={} {}", rid, e.getMessage(), e);
        }
        return null;
    }


    public List<BeautifulRidListData> findRecommendRid(int honorLevel) {
        try {
            Criteria criteria = Criteria.where("level").gt(0).lt(honorLevel).and("status").is(0);
            Query query = new Query(criteria);
            query.limit(10);
            return mongoTemplate.find(query, BeautifulRidListData.class);
        } catch (Exception e) {
            logger.error("findRecommendRid data error. rid={} {}", honorLevel, e.getMessage(), e);
        }
        return Collections.emptyList();
    }

}
