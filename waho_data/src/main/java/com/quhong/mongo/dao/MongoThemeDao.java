package com.quhong.mongo.dao;

import com.quhong.config.CaffeineCacheConfig;
import com.quhong.mongo.config.MongoBean;
import com.quhong.mongo.data.MongoThemeData;
import jakarta.annotation.Resource;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.context.annotation.Lazy;
import org.springframework.data.domain.Sort;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.data.mongodb.core.query.Criteria;
import org.springframework.data.mongodb.core.query.Query;
import org.springframework.data.mongodb.core.query.Update;
import org.springframework.stereotype.Component;
import org.springframework.util.StringUtils;

import java.util.ArrayList;
import java.util.List;
import java.util.regex.Pattern;

/**
 * <AUTHOR>
 * @date 2022/7/15
 */
@Component
@Lazy
public class MongoThemeDao {

    private static final Logger logger = LoggerFactory.getLogger(MongoThemeDao.class);

    public static final String TABLE_NAME = "theme";

    @Resource(name = MongoBean.MOVIES)
    private MongoTemplate mongoTemplate;

    @Cacheable(value = "cache", cacheManager = CaffeineCacheConfig.EXPIRE_10M_AFTER_WRITE,
            key = "#root.method.name+T(org.springframework.util.StringUtils).arrayToDelimitedString(args,'-')")
    public MongoThemeData findThemeData(int tid) {
        return findData(tid);
    }

    public MongoThemeData findData(Integer tid) {
        try {
            Criteria criteria = Criteria.where("tid").is(tid);
            return mongoTemplate.findOne(new Query(criteria), MongoThemeData.class);
        } catch (Exception e) {
            logger.error("find theme data error. tid={} {}", tid, e.getMessage(), e);
        }
        return null;
    }

    public List<MongoThemeData> findAllData() {
        try {
            Criteria criteria = Criteria.where("status").is(1).and("micThme").ne(1);
            Sort sort = Sort.by(Sort.Direction.ASC, "tid");
            return mongoTemplate.find(new Query(criteria).with(sort), MongoThemeData.class);
        } catch (Exception e) {
            logger.error("find all theme data error. {}", e.getMessage(), e);
            return new ArrayList<>();
        }
    }

    public List<MongoThemeData> findListByType(Integer type) {
        try {
            Criteria criteria = Criteria.where("status").is(1).and("micThme").ne(1).and("newtheme").is(0).and("type").is(type);
            Sort sort = Sort.by(Sort.Direction.DESC, "_id");
            Query query = new Query(criteria);
            query.with(sort);
            return mongoTemplate.find(query, MongoThemeData.class);
        } catch (Exception e) {
            logger.error("find theme data list by type error. {}", e.getMessage(), e);
            return new ArrayList<>();
        }
    }

    public List<MongoThemeData> findListByType(Integer type, int start, int pageSize) {
        try {
            Criteria criteria = Criteria.where("status").is(1).and("micThme").ne(1).and("newtheme").is(0).and("type").is(type);
            Sort sort = Sort.by(Sort.Direction.DESC, "_id");
            Query query = new Query(criteria);
            query.with(sort).skip(start).limit(pageSize);
            return mongoTemplate.find(query, MongoThemeData.class);
        } catch (Exception e) {
            logger.error("find theme data list by type error. {}", e.getMessage(), e);
            return new ArrayList<>();
        }
    }

    public List<MongoThemeData> getNewBackgroundList(int start, int pageSize) {
        try {
            Criteria criteria = Criteria.where("status").is(1).and("micThme").ne(1).and("newtheme").is(1).andOperator(Criteria.where("type").ne(0), Criteria.where("type").ne(1));
            Sort sort = Sort.by(Sort.Direction.DESC, "_id");
            Query query = new Query(criteria);
            query.with(sort).skip(start).limit(pageSize);
            return mongoTemplate.find(query, MongoThemeData.class);
        } catch (Exception e) {
            logger.error("find new theme data list error. {}", e.getMessage(), e);
            return new ArrayList<>();
        }
    }

    /**
     * 获取最新房间背景上架时间
     */
    @Cacheable(value = "cache", cacheManager = CaffeineCacheConfig.EXPIRE_10M_AFTER_WRITE,
            key = "#root.method.name+T(org.springframework.util.StringUtils).arrayToDelimitedString(args,'-')")
    public int getNewBgShelfTime() {
        MongoThemeData data = findNewestOneBg();
        if (data != null) {
            return data.get_id().getTimestamp();
        }
        return 0;
    }

    // 运营系统操作
    public List<MongoThemeData> selectPage(String search, int bgType, int status, int start, int size) {
        try {
            // 查询条件
            Criteria criteria = new Criteria();
            if (StringUtils.hasLength(search)) {
                try {
                    criteria = Criteria.where("tid").is(Integer.parseInt(search));
                } catch (Exception e) {
                    criteria = Criteria.where("title").regex(Pattern.compile(".*?" + search + ".*"));
                }
            }
            if (bgType != -1) {
                criteria.and("type").is(bgType);
            }
            if (status != -1) {
                criteria.and("status").is(status);
            }
            Sort sort = Sort.by(Sort.Direction.DESC, "_id");

            Query query = new Query(criteria);
            query.with(sort).skip(start).limit(size);
            return mongoTemplate.find(query, MongoThemeData.class);
        } catch (Exception e) {
            logger.error("MongoThemeData selectPage error.{}", e.getMessage(), e);
        }
        return new ArrayList<>();
    }

    public long selectCount(String search, int bgType, int status) {
        Criteria criteria = new Criteria();
        if (!StringUtils.isEmpty(search)) {
            criteria = Criteria.where("title").regex(Pattern.compile(".*?" + search + ".*"));
        }
        if (bgType != -1) {
            criteria.and("type").is(bgType);
        }
        if (status != -1) {
            criteria.and("status").is(status);
        }
        return mongoTemplate.count(new Query(criteria), TABLE_NAME);
    }

    private MongoThemeData findNewestOneBg() {
        try {
            Criteria criteria = Criteria.where("status").is(1).and("micThme").ne(1).and("newtheme").is(1);
            Sort sort = Sort.by(Sort.Direction.DESC, "_id");
            Query query = new Query(criteria);
            return mongoTemplate.findOne(query.with(sort), MongoThemeData.class);
        } catch (Exception e) {
            logger.error("find newest one theme data error. {}", e.getMessage(), e);
            return null;
        }
    }

    public MongoThemeData getLastThemeData() {

        Query query = new Query();
        // query.addCriteria(criteria);
        query.with(Sort.by(Sort.Order.desc("tid")));
        return mongoTemplate.findOne(query, MongoThemeData.class);
    }

    public void saveThemeOne(MongoThemeData mongoThemeData) {
        try {
            mongoTemplate.insert(mongoThemeData);
        } catch (Exception e) {
            logger.error("mongoThemeData insert error. msg = {}", e.getMessage(), e);
        }
    }


    public MongoThemeData getThemeDataByID(String docId) {
        try {
            return mongoTemplate.findById(docId, MongoThemeData.class);
        } catch (Exception e) {
            logger.error("MongoThemeData query error. msg = {}", e.getMessage(), e);

        }
        return null;
    }


    public void updateThemeData(String docId, Update update) {
        try {
            mongoTemplate.updateFirst(new Query(Criteria.where("_id").is(docId)), update, TABLE_NAME);
        } catch (Exception e) {
            logger.error("updateData error.  id={} {}", docId, e.getMessage(), e);
        }
    }

}
