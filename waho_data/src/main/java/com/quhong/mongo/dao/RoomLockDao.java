package com.quhong.mongo.dao;

import com.quhong.core.utils.DateHelper;
import com.quhong.mongo.config.MongoBean;
import com.quhong.mongo.data.RoomLockData;
import jakarta.annotation.Resource;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.context.annotation.Lazy;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.data.mongodb.core.query.Criteria;
import org.springframework.data.mongodb.core.query.Query;
import org.springframework.data.mongodb.core.query.Update;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import java.util.ArrayList;
import java.util.List;

@Component
@Lazy
public class RoomLockDao {
    private static final Logger logger = LoggerFactory.getLogger(RoomLockDao.class);

    public static final String TABLE_NAME = "room_lock";

    @Resource(name = MongoBean.MOVIES)
    private MongoTemplate mongoTemplate;

    public RoomLockData findData(String uid) {
        try {
            return mongoTemplate.findOne(new Query(Criteria.where("uid").is(uid)), RoomLockData.class);
        } catch (Exception e) {
            logger.error("find room lock data error uid={} {}", uid, e.getMessage(), e);
            return null;
        }
    }

    public void removeRoomLock(RoomLockData roomLockData) {
        logger.info("remove room lock endTime={} uid={}", roomLockData.getEnd_time(), roomLockData.getUid());
        try {
            removeRoomLockFromDb(roomLockData);
        } catch (Exception e) {
            logger.error("remove room lock cartoon data error.  uid={} {}", roomLockData.getUid(), e.getMessage(), e);
        }
    }

    private void removeRoomLockFromDb(RoomLockData roomLockData) {
        try {
            Criteria criteria = Criteria.where("_id").is(roomLockData.get_id());
            mongoTemplate.remove(new Query(criteria), TABLE_NAME);
        } catch (Exception e) {
            logger.error("remove room lock data from db error. uid={} {}", roomLockData.getUid(), e.getMessage(), e);
        }
    }

    public void insertDB(String uid, int endTime) {
        try {
            logger.info("update room lock. uid={} endTime={} ", uid, endTime);
            RoomLockData roomLockData = new RoomLockData();
            roomLockData.setUid(uid);
            roomLockData.setC_time(DateHelper.getNowSeconds());
            roomLockData.setEnd_time(endTime);
            mongoTemplate.insert(roomLockData);
        } catch (Exception e) {
            logger.error("insert room lock data error. uid={} {}", uid, e.getMessage(), e);
        }
    }

    public void updateRoomLockByTime(String uid, long endTime, RoomLockData oldRoomLockData) {
        try {
            logger.info("update room lock. uid={} endTime={}", uid, endTime);
            Criteria criteria = Criteria.where("uid").is(uid);
            Update update = new Update();
            update.set("end_time", endTime);
            mongoTemplate.updateFirst(new Query(criteria), update, TABLE_NAME);
        } catch (Exception e) {
            logger.error("update room lock data error. uid={} {}", uid, e.getMessage(), e);
        }
    }


    public List<RoomLockData> listByEndTime(int start, int end) {
        Criteria criteria = Criteria.where("end_time").gt(start).lte(end);
        List<RoomLockData> list = mongoTemplate.find(new Query(criteria), RoomLockData.class);
        if (CollectionUtils.isEmpty(list)) {
            logger.info("listByEndTime room lock list is null  end = {}", end);
            return new ArrayList<>();
        }
        return list;
    }
}
