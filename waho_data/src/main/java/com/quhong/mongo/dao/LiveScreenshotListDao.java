package com.quhong.mongo.dao;

import com.quhong.config.CaffeineCacheConfig;
import com.quhong.mongo.config.MongoBean;
import com.quhong.mongo.data.LiveScreenshotListData;
import com.quhong.redis.DataRedisBean;
import jakarta.annotation.Resource;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.context.annotation.Lazy;
import org.springframework.data.domain.Sort;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.data.mongodb.core.aggregation.Aggregation;
import org.springframework.data.mongodb.core.query.Criteria;
import org.springframework.data.mongodb.core.query.Query;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import java.util.Collections;
import java.util.List;
import java.util.Set;
import java.util.concurrent.TimeUnit;

/**
 * <AUTHOR>
 * @date 2024/11/11
 */
@Component
@Lazy
public class LiveScreenshotListDao {

    private static final Logger logger = LoggerFactory.getLogger(LiveScreenshotListDao.class);

    public static final String TABLE_NAME = "live_screenshot_list";

    @Resource(name = MongoBean.MOVIES)
    private MongoTemplate mongoTemplate;
    @Resource(name = DataRedisBean.MAIN_CLUSTER)
    private StringRedisTemplate clusterRedis;

    public void save(LiveScreenshotListData data) {
        try {
            mongoTemplate.save(data);
            if (data.getStatus() == 1) {
                addLiveScreenshotList(data.getAid());
            } else {
                removeLiveScreenshotList(data.getAid());
            }
        } catch (Exception e) {
            logger.error("saveLiveScreenshotListData error. {}", e.getMessage(), e);
        }
    }

    public int selectCount(String aid, int type, int status) {
        try {
            return (int) mongoTemplate.count(new Query(buildCriteria(aid, type, status)), LiveScreenshotListData.class);
        } catch (Exception e) {
            logger.error("selectCount error. aid={} type={} status={} {}", aid, type, status, e.getMessage(), e);
            return 0;
        }
    }

    public LiveScreenshotListData selectByAid(String aid) {
        try {
            return mongoTemplate.findOne(new Query(Criteria.where("aid").is(aid)), LiveScreenshotListData.class);
        } catch (Exception e) {
            logger.error("selectByAid error. aid={} {}", aid, e.getMessage(), e);
            return null;
        }
    }

    public List<LiveScreenshotListData> selectList(String aid, int type, int status, int start, int pageSize) {
        try {
            Aggregation aggregation = Aggregation.newAggregation(
                    Aggregation.match(buildCriteria(aid, type, status)),
                    // 排序
                    Aggregation.sort(Sort.by(Sort.Direction.DESC, "mtime")),
                    // 分页
                    Aggregation.skip(start),
                    Aggregation.limit(pageSize)
            );
            return mongoTemplate.aggregate(aggregation, TABLE_NAME, LiveScreenshotListData.class).getMappedResults();
        } catch (Exception e) {
            logger.error("selectPage error. aid={} type={} status={} {}", aid, type, status, e.getMessage(), e);
            return null;
        }
    }

    private Criteria buildCriteria(String aid, int type, int status) {
        Criteria criteria = new Criteria();
        if (StringUtils.hasLength(aid)) {
            criteria.and("aid").is(aid);
        }
        if (type != -1) {
            criteria.and("type").is(type);
        }
        if (status != -1) {
            criteria.and("status").is(status);
        }
        return criteria;
    }

    public void addLiveScreenshotList(String aid) {
        String key = getLiveScreenshotListKey();
        try {
            clusterRedis.opsForSet().add(key, aid);
            clusterRedis.expire(key, 160, TimeUnit.DAYS);
        } catch (Exception e) {
            logger.error("addLiveScreenshotList error. aid={} {}", aid, e.getMessage(), e);
        }
    }

    @Cacheable(value = "cache", cacheManager = CaffeineCacheConfig.EXPIRE_1M_AFTER_WRITE,
            condition = "T(com.quhong.core.config.ServerConfig).isProduct()",
            key = "#root.method.name+T(org.springframework.util.StringUtils).arrayToDelimitedString(args,'-')")
    public Set<String> getLiveScreenshotList() {
        String key = getLiveScreenshotListKey();
        try {
            return clusterRedis.opsForSet().members(key);
        } catch (Exception e) {
            logger.error("getLiveScreenshotList error. {}", e.getMessage(), e);
            return Collections.emptySet();
        }
    }

    public void removeLiveScreenshotList(String aid) {
        String key = getLiveScreenshotListKey();
        try {
            clusterRedis.opsForSet().remove(key, aid);
        } catch (Exception e) {
            logger.error("removeLiveAuditFailedUser error. aid={} {}", aid, e.getMessage(), e);
        }
    }

    /**
     * 需要直播截屏的用户
     */
    public boolean isNeedScreenshotUser(String uid, int familyId) {
        Set<String> aidSet = getLiveScreenshotList();
        if (CollectionUtils.isEmpty(aidSet)) {
            return false;
        }
        if (aidSet.contains(uid)) {
            return true;
        }
        return familyId != 0 && aidSet.contains(familyId + "");
    }

    private String getLiveScreenshotListKey() {
        return "set:liveScreenshotList";
    }
}
