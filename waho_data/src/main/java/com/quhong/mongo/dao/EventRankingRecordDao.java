package com.quhong.mongo.dao;

import com.quhong.config.CaffeineCacheConfig;
import com.quhong.mongo.config.MongoBean;
import com.quhong.mongo.data.EventRankingRecordData;
import jakarta.annotation.Resource;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.context.annotation.Lazy;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.data.mongodb.core.query.Criteria;
import org.springframework.data.mongodb.core.query.Query;
import org.springframework.stereotype.Component;

import java.util.Collections;
import java.util.Map;

@Lazy
@Component
public class EventRankingRecordDao {

    public static final String TABLE_NAME = "event_ranking_record";

    private static final Logger logger = LoggerFactory.getLogger(EventRankingRecordDao.class);

    @Resource(name = MongoBean.MOVIES)
    private MongoTemplate mongoTemplate;


    public void save(EventRankingRecordData data) {
        try {
            mongoTemplate.save(data);
        } catch (Exception e) {
            logger.error("save event ranking record error. {}", e.getMessage(), e);
        }
    }

    @Cacheable(value = "cache", cacheManager = CaffeineCacheConfig.EXPIRE_10M_AFTER_WRITE,
            key = "#root.method.name+T(org.springframework.util.StringUtils).arrayToDelimitedString(args,'-')")
    public Map<String, Long> getRankingMapFromCache(int eventId, String rankingType) {
        return getRankingMap(eventId, rankingType);
    }

    public Map<String, Long> getRankingMap(int eventId, String rankingType) {
        try {
            Criteria criteria = Criteria.where("eventId").is(eventId).and("rankingType").is(rankingType);
            EventRankingRecordData data = mongoTemplate.findOne(new Query(criteria), EventRankingRecordData.class);
            return data != null ? data.getRankingMap() : Collections.emptyMap();
        } catch (Exception e) {
            logger.error("find event ranking record error. eventId={} rankingType={} {}", eventId, rankingType, e.getMessage(), e);
            return Collections.emptyMap();
        }
    }
}
