package com.quhong.mongo.dao;

import com.alibaba.fastjson.JSONObject;
import com.quhong.core.utils.DateHelper;
import com.quhong.mongo.config.MongoBean;
import com.quhong.mongo.data.AnchorDataStatData;
import com.quhong.mysql.dao.FamilyMemberDao;
import com.quhong.mysql.data.FamilyMemberData;
import com.quhong.redis.DataRedisBean;
import com.quhong.utils.RoomUtils;
import jakarta.annotation.Resource;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.context.annotation.Lazy;
import org.springframework.data.domain.Sort;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.data.mongodb.core.aggregation.Aggregation;
import org.springframework.data.mongodb.core.aggregation.SortOperation;
import org.springframework.data.mongodb.core.query.Criteria;
import org.springframework.data.mongodb.core.query.Query;
import org.springframework.data.mongodb.core.query.Update;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.stereotype.Component;
import org.springframework.util.StringUtils;

import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2024/12/26
 */
@Lazy
@Component
public class AnchorDataStatDao {

    private static final Logger logger = LoggerFactory.getLogger(AnchorDataStatDao.class);

    public static final String TABLE_NAME = "anchor_data_stat";

    public static final String ROOM_RECEIVE_BEAN = "roomReceiveBean";
    public static final String LIVE_ROOM_CHARM_INCOME = "liveRoomCharmIncome";
    public static final String CHAT_ROOM_CHARM_INCOME = "chatRoomCharmIncome";
    public static final String CHAT_TIME = "chatTime";
    public static final String LIVE_TIME = "liveTime";

    @Resource(name = MongoBean.MOVIES)
    private MongoTemplate mongoTemplate;
    @Resource(name = DataRedisBean.MAIN_CLUSTER)
    private StringRedisTemplate clusterTemplate;
    @Resource
    private FamilyMemberDao familyMemberDao;

    public void save(AnchorDataStatData data) {
        try {
            mongoTemplate.save(data);
        } catch (Exception e) {
            logger.error("saveAnchorDataStatData. {}", e.getMessage());
        }
    }

    public void update(AnchorDataStatData data) {
        try {
            // 更新mongodb数据
            Query query = new Query(Criteria.where("_id").is(data.get_id()));
            Update update = new Update();
            update.set("workDays", data.getWorkDays());
            update.set("liveWorkDays", data.getLiveWorkDays());
            update.set("chatWorkDays", data.getChatWorkDays());
            update.set("upMicTime", data.getUpMicTime());
            update.set("liveTime", data.getLiveTime());
            update.set("chatTime", data.getChatTime());
            update.set("roomReceiveBean", data.getRoomReceiveBean());
            update.set("roomCharmIncome", data.getRoomCharmIncome());
            update.set("liveRoomCharmIncome", data.getLiveRoomCharmIncome());
            update.set("chatRoomCharmIncome", data.getChatRoomCharmIncome());
            update.set("sendGiftUserNum", data.getSendGiftUserNum());
            mongoTemplate.updateFirst(query, update, TABLE_NAME);
        } catch (Exception e) {
            logger.error("update. id={} {}", data.get_id().toString(), e.getMessage(), e);
        }
    }

    public AnchorDataStatData findData(int familyId, String uid, int intDate) {
        try {
            Criteria criteria = Criteria.where("uid").is(uid).and("intDate").is(intDate).and("familyId").is(familyId);
            return mongoTemplate.findOne(new Query(criteria), AnchorDataStatData.class);
        } catch (Exception e) {
            logger.error("findData. familyId={} uid={} intDate={} {}", familyId, uid, intDate, e.getMessage());
            return null;
        }
    }

    public List<AnchorDataStatData> selectPage(int familyId, String uid, int sortType, int startDate, int endDate, int start, int pageSize) {
        try {
            logger.info("selectPage. familyId={} uid={} sortType={} startDate={} endDate={} start={} pageSize={}", familyId, uid, sortType, startDate, endDate, start, pageSize);
            // 查询条件
            Criteria criteria = Criteria.where("familyId").is(familyId).and("intDate").gte(startDate).lte(endDate);
            if (StringUtils.hasLength(uid)) {
                criteria.and("uid").is(uid);
            }
            Aggregation aggregation = Aggregation.newAggregation(
                    Aggregation.match(criteria),
                    Aggregation.group("uid")
                            .first("uid").as("uid")
                            .first("familyId").as("familyId")
                            .sum("workDays").as("workDays")
                            .sum("liveWorkDays").as("liveWorkDays")
                            .sum("chatWorkDays").as("chatWorkDays")
                            .sum("upMicTime").as("upMicTime")
                            .sum("liveTime").as("liveTime")
                            .sum("chatTime").as("chatTime")
                            .sum("roomReceiveBean").as("roomReceiveBean")
                            .sum("roomCharmIncome").as("roomCharmIncome")
                            .sum("liveRoomCharmIncome").as("liveRoomCharmIncome")
                            .sum("chatRoomCharmIncome").as("chatRoomCharmIncome")
                            .sum("sendGiftUserNum").as("sendGiftUserNum"),
                    getSort(sortType),
                    // 分页
                    Aggregation.skip(start),
                    Aggregation.limit(pageSize)
            );
            List<AnchorDataStatData> mappedResults = mongoTemplate.aggregate(aggregation, TABLE_NAME, AnchorDataStatData.class).getMappedResults();
            logger.info("selectPage. result={}", JSONObject.toJSONString(mappedResults));
            return mappedResults;
        } catch (Exception e) {
            logger.error("selectPage error. familyId={} uid={} sortType={} startDate={} endDate={} start={} pageSize={} {}"
                    , familyId, uid, sortType, startDate, endDate, start, pageSize, e.getMessage(), e);
        }
        return Collections.emptyList();
    }

    private SortOperation getSort(int sortType) {
        SortOperation sortOperation;
        switch (sortType) {
            case 1 -> sortOperation = Aggregation.sort(Sort.by(Sort.Direction.ASC, "workDays"));
            case 3 -> sortOperation = Aggregation.sort(Sort.by(Sort.Direction.ASC, "roomReceiveBean"));
            case 4 -> sortOperation = Aggregation.sort(Sort.by(Sort.Direction.DESC, "roomReceiveBean"));
            case 5 -> sortOperation = Aggregation.sort(Sort.by(Sort.Direction.ASC, "roomCharmIncome"));
            case 6 -> sortOperation = Aggregation.sort(Sort.by(Sort.Direction.DESC, "roomCharmIncome"));
            case 7 -> sortOperation = Aggregation.sort(Sort.by(Sort.Direction.ASC, "sendGiftUserNum"));
            case 8 -> sortOperation = Aggregation.sort(Sort.by(Sort.Direction.DESC, "sendGiftUserNum"));
            case 9 -> sortOperation = Aggregation.sort(Sort.by(Sort.Direction.ASC, "liveRoomCharmIncome"));
            case 10 -> sortOperation = Aggregation.sort(Sort.by(Sort.Direction.DESC, "liveRoomCharmIncome"));
            case 11 -> sortOperation = Aggregation.sort(Sort.by(Sort.Direction.ASC, "liveTime"));
            case 12 -> sortOperation = Aggregation.sort(Sort.by(Sort.Direction.DESC, "liveTime"));
            case 13 -> sortOperation = Aggregation.sort(Sort.by(Sort.Direction.ASC, "liveWorkDays"));
            case 14 -> sortOperation = Aggregation.sort(Sort.by(Sort.Direction.DESC, "liveWorkDays"));
            case 15 -> sortOperation = Aggregation.sort(Sort.by(Sort.Direction.ASC, "chatRoomCharmIncome"));
            case 16 -> sortOperation = Aggregation.sort(Sort.by(Sort.Direction.DESC, "chatRoomCharmIncome"));
            case 17 -> sortOperation = Aggregation.sort(Sort.by(Sort.Direction.ASC, "chatTime"));
            case 18 -> sortOperation = Aggregation.sort(Sort.by(Sort.Direction.DESC, "chatTime"));
            case 19 -> sortOperation = Aggregation.sort(Sort.by(Sort.Direction.ASC, "chatWorkDays"));
            case 20 -> sortOperation = Aggregation.sort(Sort.by(Sort.Direction.DESC, "chatWorkDays"));
            default -> sortOperation = Aggregation.sort(Sort.by(Sort.Direction.DESC, "workDays"));

        }
        return sortOperation;
    }

    public void incAnchorHourUpMicTime(String hashKey, long upMicTime) {
        try {
            String[] strArr = hashKey.split("_");
            String uid = strArr[0];
            String roomId = strArr[1];
            FamilyMemberData memberData = familyMemberDao.selectByUidFromCache(uid);
            if (memberData == null) {
                return;
            }
            incAnchorHourData(memberData.getFamilyId(), uid, RoomUtils.isLiveRoom(roomId) ? LIVE_TIME : CHAT_TIME , upMicTime);
        } catch (Exception e) {
            logger.error("incAnchorHourUpMicData error. hashKey={} upMicTime={} {}", hashKey, upMicTime, e.getMessage(), e);
        }
    }

    public long incAnchorHourData(int familyId, String uid, String hashKey, long hashValue) {
        String key = getAnchorHourDataKey(familyId);
        hashKey = hashKey + "_" + uid;
        try {
            return clusterTemplate.opsForHash().increment(key, hashKey, hashValue);
        } catch (Exception e) {
            logger.error("incAnchorHourData error. familyId={} uid={} hashKey={} hashValue={} {}", familyId, uid, hashKey, hashValue, e.getMessage(), e);
            return 0;
        }
    }

    public Map<String, Long> getAnchorHourDataMap(int familyId, String strHours) {
        String key = getAnchorHourDataKey(familyId, strHours);
        try {
            Map<Object, Object> entries = clusterTemplate.opsForHash().entries(key);
            Map<String, Long> resultMap = new HashMap<>(entries.size());
            for (Map.Entry<Object, Object> entry : entries.entrySet()) {
                resultMap.put(entry.getKey() + "", Long.parseLong(String.valueOf(entry.getValue())));
            }
            return resultMap;
        } catch (Exception e) {
            logger.error("getAnchorHourDataMap error. familyId={} strHours={} {}", familyId, strHours, e.getMessage(), e);
            return Collections.emptyMap();
        }
    }

    private String getAnchorHourDataKey(int familyId) {
        return getAnchorHourDataKey(familyId, DateHelper.ARABIAN.formatDateInHour());
    }

    private String getAnchorHourDataKey(int familyId, String strHour) {
        return "hash:anchorHourData_" + strHour + "_" + familyId;
    }
}
