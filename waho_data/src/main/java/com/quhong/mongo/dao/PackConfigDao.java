package com.quhong.mongo.dao;

import com.quhong.config.CaffeineCacheConfig;
import com.quhong.mongo.config.MongoBean;
import com.quhong.mongo.data.PackConfigData;
import jakarta.annotation.Resource;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.data.domain.Sort;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.data.mongodb.core.query.Criteria;
import org.springframework.data.mongodb.core.query.Query;
import org.springframework.stereotype.Component;
import org.springframework.util.StringUtils;

import java.util.Collections;
import java.util.List;
import java.util.Set;

@Component
public class PackConfigDao {

    private final Logger logger = LoggerFactory.getLogger(getClass());

    public static final String TABLE_NAME = "pack_config";

    @Resource(name = MongoBean.MOVIES)
    private MongoTemplate mongoTemplate;

    public void save(PackConfigData data) {
        try {
            mongoTemplate.save(data);
        } catch (Exception e) {
            logger.error("save PackConfigData error. {}", e.getMessage(), e);
        }
    }

    @Cacheable(value = "cache", cacheManager = CaffeineCacheConfig.EXPIRE_1M_AFTER_WRITE,
            key = "#root.method.name+T(org.springframework.util.StringUtils).arrayToDelimitedString(args,'-')")
    public PackConfigData findByKey(String key) {
        return mongoTemplate.findOne(new Query(Criteria.where("key").is(key).and("status").is(0)), PackConfigData.class);
    }

    public PackConfigData findById(String id) {
        return mongoTemplate.findById(id, PackConfigData.class);
    }

    public List<PackConfigData> getAllList(String search) {
        try {
            Criteria criteria = Criteria.where("status").is(0);
            if (StringUtils.hasLength(search)) {
                criteria.orOperator(
                        Criteria.where("key").regex(".*?" + search + ".*?"),
                        Criteria.where("name").regex(".*?" + search + ".*?"),
                        Criteria.where("title").regex(".*?" + search + ".*?"),
                        Criteria.where("titleAr").regex(".*?" + search + ".*?"),
                        Criteria.where("desc").regex(".*?" + search + ".*?"),
                        Criteria.where("descAr").regex(".*?" + search + ".*?")
                );
            }
            Sort sort = Sort.by(Sort.Direction.DESC, "_id");
            Query query = new Query(criteria);
            query.with(sort);
            return mongoTemplate.find(query, PackConfigData.class);
        } catch (Exception e) {
            logger.error("getAllList error.{}", e.getMessage(), e);
        }
        return Collections.emptyList();
    }

    @Cacheable(value = "cache", cacheManager = CaffeineCacheConfig.EXPIRE_1M_AFTER_WRITE,
            key = "#root.method.name+T(org.springframework.util.StringUtils).arrayToDelimitedString(args,'-')")
    public List<PackConfigData> findListByKeys(Set<String> keySet) {
        return mongoTemplate.find(new Query(Criteria.where("key").in(keySet).and("status").is(0)), PackConfigData.class);
    }
}
