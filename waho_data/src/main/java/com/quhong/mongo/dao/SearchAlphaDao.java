package com.quhong.mongo.dao;

import com.quhong.mongo.config.MongoBean;
import com.quhong.mongo.data.BeautifulRidData;
import com.quhong.mongo.data.SearchAlphaData;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.context.annotation.Lazy;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.data.mongodb.core.query.Criteria;
import org.springframework.data.mongodb.core.query.Query;
import org.springframework.stereotype.Component;

import jakarta.annotation.Resource;

/**
 * <AUTHOR>
 * @date 2022/8/26
 */
@Lazy
@Component
public class SearchAlphaDao {

    private static final Logger logger = LoggerFactory.getLogger(SearchAlphaDao.class);

    public static final String TABLE_NAME = "search_alpha";

    @Resource(name = MongoBean.MOVIES)
    private MongoTemplate mongoTemplate;

    public SearchAlphaData findData(String uid) {
        try {
            Criteria criteria = Criteria.where("uid").is(uid);
            return mongoTemplate.findOne(new Query(criteria), SearchAlphaData.class);
        } catch (Exception e) {
            logger.error("find search alpha data error. uid={} {}", uid, e.getMessage(), e);
        }
        return null;
    }

    public void removeSearchAlphaFromDb(SearchAlphaData data) {
        try {
            Criteria criteria = Criteria.where("_id").is(data.get_id());
            mongoTemplate.remove(new Query(criteria), TABLE_NAME);
        } catch (Exception e) {
            logger.error("remove SearchAlpha data from db error. uid={} alpha_id={} {}", data.getUid(), data.getAlpha_rid(), e.getMessage(), e);
        }
    }
}
