package com.quhong.mongo.dao;

import com.alibaba.fastjson.JSONObject;
import com.mongodb.client.FindIterable;
import com.mongodb.client.MongoCollection;
import com.mongodb.client.MongoCursor;
import com.mongodb.client.model.UpdateOptions;
import com.quhong.core.utils.DateHelper;
import com.quhong.mongo.config.MongoBean;
import com.quhong.mongo.data.SignTableData;
import jakarta.annotation.Resource;
import org.bson.*;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import java.util.ArrayList;
import java.util.Collections;
import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2022/9/21
 */
@Component
public class SignTableDao {

    private static final Logger logger = LoggerFactory.getLogger(SignTableDao.class);

    public static final String TABLE_NAME = "sign_table";

    @Resource(name = MongoBean.MOVIES)
    private MongoTemplate mongoTemplate;

    public SignTableData findData(String uid) {
        try {
            MongoCollection<Document> collection = mongoTemplate.getDb().getCollection(TABLE_NAME);
            Document find = new Document("_id", uid);
            MongoCursor<Document> iter = collection.find(find).limit(1).iterator();
            if (iter.hasNext()) {
                Document doc = iter.next();
                String docStr = doc.toJson();
                return JSONObject.parseObject(docStr, SignTableData.class);
            }
        } catch (Exception e) {
            logger.error("find sign data error. uid={} {}", uid, e.getMessage(), e);
        }
        return null;
    }

    public List<SignTableData> getYesterdaySign() {
        try {
            long startTime = DateHelper.DEFAULT.getDayOffset(-1);
            String yesterday = DateHelper.DEFAULT.formatDateInDay(new Date(startTime));
            MongoCollection<Document> collection = mongoTemplate.getDb().getCollection(TABLE_NAME);
            Document find = new Document("last_sign", yesterday);
            FindIterable<Document> limit = collection.find(find).limit(10000);
            List<SignTableData> result = new ArrayList<>();
            for (Document document : limit) {
                result.add(JSONObject.parseObject(document.toJson(), SignTableData.class));
            }
            return result;
        } catch (Exception e) {
            logger.error("find sign data error. {}", e.getMessage(), e);
            return Collections.emptyList();
        }
    }

    public void insertData(SignTableData signTableData) {
        try {
            MongoCollection<Document> collection = mongoTemplate.getDb().getCollection(TABLE_NAME);
            Document data = new Document("_id", signTableData.get_id());
            data.put("check_date", signTableData.getCheck_date());
            data.put("last_sign", signTableData.getLast_sign());
            data.put("combo", signTableData.getCombo());
            data.put("sign_record", signTableData.getSign_record());
            data.put("boom_check", signTableData.getBoom_check());
            data.put("continu", signTableData.getContinu());
            data.put("cover", signTableData.getCover());
            data.put("signCount", signTableData.getSignCount());
            collection.insertOne(data);
        } catch (Exception e) {
            logger.error("insert sing data error. uid={} {}", signTableData.get_id(), e.getMessage(), e);
        }
    }


    public void updateData(SignTableData signTableData) {
        try {
            MongoCollection<Document> beansTotalDevote = mongoTemplate.getDb().getCollection(TABLE_NAME);
            BsonDocument filter = new BsonDocument("_id", new BsonString(signTableData.get_id()));
            BsonDocument update = new BsonDocument();
            BsonDocument doc = new BsonDocument();
            doc.append("check_date", new BsonString(signTableData.getCheck_date()));
            doc.append("last_sign", new BsonString(signTableData.getLast_sign()));
            doc.append("combo", new BsonInt32(signTableData.getCombo()));
            BsonArray signRecordArray = new BsonArray();
            if (!CollectionUtils.isEmpty(signTableData.getSign_record())) {
                for (Integer signRecord : signTableData.getSign_record()) {
                    signRecordArray.add(new BsonInt32(signRecord));
                }
            }
            doc.append("sign_record", signRecordArray);
            doc.append("boom_check", new BsonString(signTableData.getBoom_check()));
            doc.append("continu", new BsonInt32(signTableData.getContinu()));
            doc.append("cover", new BsonInt32(signTableData.getCover()));
            doc.append("signCount", new BsonInt32(signTableData.getSignCount()));
            update.append("$set", doc);
            beansTotalDevote.updateOne(filter, update, new UpdateOptions().upsert(false));
        } catch (Exception e) {
            logger.error("update sign data error. uid={} {}", signTableData.get_id(), e.getMessage(), e);
        }
    }
}
