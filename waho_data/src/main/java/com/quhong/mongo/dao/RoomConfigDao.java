package com.quhong.mongo.dao;

import com.quhong.mongo.config.MongoBean;
import com.quhong.mongo.data.RoomConfigData;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.data.mongodb.core.query.Criteria;
import org.springframework.data.mongodb.core.query.Query;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import jakarta.annotation.Resource;
import java.util.HashMap;
import java.util.Map;

/**
 * 房间配置
 *
 * <AUTHOR>
 * @date 2022/7/18
 */
@Component
public class RoomConfigDao {

    public static final Logger logger = LoggerFactory.getLogger(RoomConfigDao.class);
    public static final Map<String, Object> DEF_ROOM_CONFIG = new HashMap<String, Object>() {{
        put(CREATE_UMO, 0);
        put(CREATE_MONSTER_CRUSH, 0);
        put(CREATE_DOMINO, 0);
        put(LUCKY_NUM_COST, 0);
        put(LUCKY_NUM_RANGE, 9);
        put(LUCKY_NUM_SWITCH, 0);
        put(LUCKY_NUM_DATA, 0);
        put(LUCKY_NUM_ADMIN, 0);
    }};

    public static final String TABLE_NAME = "room_config";

    public static final String CREATE_UMO = "createUMO";  //创建UMO游戏权限 0everyone 1member 2admin 3owner
    public static final String CREATE_MONSTER_CRUSH = "createMonsterCrush";  //创建消消乐游戏权限 0everyone 1member 2admin 3owner
    public static final String CREATE_DOMINO = "createDomino"; //创建多米诺游戏权限 0everyone 1member 2admin 3owner
    public static final String LUCKY_NUM_COST = "luckyNumCost";
    public static final String LUCKY_NUM_RANGE = "luckyNumRange";
    public static final String LUCKY_NUM_SWITCH = "luckyNumSwitch";
    public static final String LUCKY_NUM_DATA = "luckyNumData";
    public static final String LUCKY_NUM_ADMIN = "luckyNumAdmin";


    @Resource(name = MongoBean.MOVIES)
    private MongoTemplate mongoTemplate;

    public void save(RoomConfigData data) {
        try {
            mongoTemplate.save(data);
        } catch (Exception e) {
            logger.error("save room config data error. roomId={} {}", data.getRoomId(), e.getMessage(), e);
        }
    }

    public RoomConfigData findData(String roomId) {
        try {
            Criteria criteria = Criteria.where("roomId").is(roomId);
            RoomConfigData configData = mongoTemplate.findOne(new Query(criteria), RoomConfigData.class);
            return configData != null ? configData : initRoomConfigData(roomId);
        } catch (Exception e) {
            logger.error("find room config data error. roomId={} {}", roomId, e.getMessage(), e);
            return initRoomConfigData(roomId);
        }
    }

    public void updateRoomConfig(String roomId, String configName, Object configValue) {
        RoomConfigData configData = findData(roomId);
        Map<String, Object> configMap = configData.getRoom_config();
        configMap.put(configName, configValue);
        configData.setRoom_config(configMap);
        save(configData);
    }

    public void updateRoomConfigByMap(String roomId, Map<String, Object> updateMap) {
        RoomConfigData configData = findData(roomId);
        Map<String, Object> configMap = configData.getRoom_config();
        configMap.putAll(updateMap);
        configData.setRoom_config(configMap);
        save(configData);
    }

    public int getIntRoomConfig(String roomId, String configName, int defaultValue) {
        RoomConfigData data = findData(roomId);
        Map<String, Object> roomConfigMap = data.getRoom_config();
        try {
            return roomConfigMap.get(configName) != null ? (int) roomConfigMap.get(configName) : defaultValue;
        } catch (Exception e) {
            logger.error("{}", e.getMessage(), e);
        }
        return defaultValue;
    }

    public RoomConfigData initRoomConfigData(String roomId) {
        RoomConfigData configData = new RoomConfigData();
        configData.setRoomId(roomId);
        configData.setRoom_config(new HashMap<>(DEF_ROOM_CONFIG));
        return configData;
    }
}
