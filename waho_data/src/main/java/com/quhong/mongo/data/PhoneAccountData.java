package com.quhong.mongo.data;

import com.quhong.mongo.dao.PhoneAccountDao;
import org.springframework.data.annotation.Id;
import org.springframework.data.mongodb.core.mapping.Document;

/**
 * phone_account表
 *
 * <AUTHOR>
 * @date 2022/6/11
 */
@Document(collection = PhoneAccountDao.TABLE_NAME)
public class PhoneAccountData {

    @Id
    private String _id;
    /**
     * 用户id
     */
    private String uid;
    /**
     * 密码
     */
    private String pwd;
    /**
     * 创建时间
     */
    private int c_time;

    /**
     * 注册时关联的uid
     */
    private String register_uid;

    /**
     * 注册时关联的第三方uid
     */
    private String register_third_uid;

    /**
     * 注册时关联第三方返回手机号
     */
    private String register_num;

    public String get_id() {
        return _id;
    }

    public void set_id(String _id) {
        this._id = _id;
    }

    public String getUid() {
        return uid;
    }

    public void setUid(String uid) {
        this.uid = uid;
    }

    public String getPwd() {
        return pwd;
    }

    public void setPwd(String pwd) {
        this.pwd = pwd;
    }

    public int getC_time() {
        return c_time;
    }

    public void setC_time(int c_time) {
        this.c_time = c_time;
    }

    public String getRegister_uid() {
        return register_uid;
    }

    public void setRegister_uid(String register_uid) {
        this.register_uid = register_uid;
    }

    public String getRegister_third_uid() {
        return register_third_uid;
    }

    public void setRegister_third_uid(String register_third_uid) {
        this.register_third_uid = register_third_uid;
    }

    public String getRegister_num() {
        return register_num;
    }

    public void setRegister_num(String register_num) {
        this.register_num = register_num;
    }

    @Override
    public String toString() {
        return "PhoneAccountData{" +
                "_id='" + _id + '\'' +
                ", uid='" + uid + '\'' +
                ", pwd='" + pwd + '\'' +
                ", c_time=" + c_time +
                ", register_uid='" + register_uid + '\'' +
                ", register_third_uid='" + register_third_uid + '\'' +
                ", register_num='" + register_num + '\'' +
                '}';
    }
}
