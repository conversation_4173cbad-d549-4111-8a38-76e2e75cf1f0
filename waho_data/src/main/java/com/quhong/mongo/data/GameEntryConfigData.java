package com.quhong.mongo.data;

import com.quhong.mongo.dao.GameEntryConfigDao;
import org.bson.types.ObjectId;
import org.springframework.data.annotation.Id;
import org.springframework.data.mongodb.core.mapping.Document;

/**
 * <AUTHOR>
 * @date 2023/8/24
 */
@Document(GameEntryConfigDao.TABLE_NAME)
public class GameEntryConfigData {

    @Id
    private ObjectId _id;

    private String name;

    private String nameAr;

    private String gameUrl;

    private String icon;

    private String iconAr;

    private String bgUrl;

    private String banner;

    private String bannerAr;

    private String gameType;

    private int costBeanActType;

    public ObjectId get_id() {
        return _id;
    }

    public void set_id(ObjectId _id) {
        this._id = _id;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getNameAr() {
        return nameAr;
    }

    public void setNameAr(String nameAr) {
        this.nameAr = nameAr;
    }

    public String getGameUrl() {
        return gameUrl;
    }

    public void setGameUrl(String gameUrl) {
        this.gameUrl = gameUrl;
    }

    public String getIcon() {
        return icon;
    }

    public void setIcon(String icon) {
        this.icon = icon;
    }

    public String getIconAr() {
        return iconAr;
    }

    public void setIconAr(String iconAr) {
        this.iconAr = iconAr;
    }

    public String getBgUrl() {
        return bgUrl;
    }

    public void setBgUrl(String bgUrl) {
        this.bgUrl = bgUrl;
    }

    public String getBanner() {
        return banner;
    }

    public void setBanner(String banner) {
        this.banner = banner;
    }

    public String getBannerAr() {
        return bannerAr;
    }

    public void setBannerAr(String bannerAr) {
        this.bannerAr = bannerAr;
    }

    public String getGameType() {
        return gameType;
    }

    public void setGameType(String gameType) {
        this.gameType = gameType;
    }

    public int getCostBeanActType() {
        return costBeanActType;
    }

    public void setCostBeanActType(int costBeanActType) {
        this.costBeanActType = costBeanActType;
    }
}
