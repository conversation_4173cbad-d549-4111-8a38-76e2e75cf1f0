package com.quhong.mongo.data;

import com.quhong.mongo.dao.RechargeLimitUserDao;
import org.bson.types.ObjectId;
import org.springframework.data.annotation.Id;
import org.springframework.data.mongodb.core.mapping.Document;

/**
 * <AUTHOR>
 * @date 2024/12/20
 */
@Document(collection = RechargeLimitUserDao.TABLE_NAME)
public class RechargeLimitUserData {

    @Id
    private ObjectId _id;
    private String uid; // 用户uid
    private int amountLimit; // 充值限额 单位：美金
    private int limitType; // 限额类型 0按天 1按周 2按月 3累计
    private String optUid; // 操作者uid
    private int ctime; // 创建时间

    public ObjectId get_id() {
        return _id;
    }

    public void set_id(ObjectId _id) {
        this._id = _id;
    }

    public String getUid() {
        return uid;
    }

    public void setUid(String uid) {
        this.uid = uid;
    }

    public int getAmountLimit() {
        return amountLimit;
    }

    public void setAmountLimit(int amountLimit) {
        this.amountLimit = amountLimit;
    }

    public int getLimitType() {
        return limitType;
    }

    public void setLimitType(int limitType) {
        this.limitType = limitType;
    }

    public String getOptUid() {
        return optUid;
    }

    public void setOptUid(String optUid) {
        this.optUid = optUid;
    }

    public int getCtime() {
        return ctime;
    }

    public void setCtime(int ctime) {
        this.ctime = ctime;
    }
}
