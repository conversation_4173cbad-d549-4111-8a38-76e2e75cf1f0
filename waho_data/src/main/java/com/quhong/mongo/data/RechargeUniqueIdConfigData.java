package com.quhong.mongo.data;

import com.quhong.mongo.dao.RechargeUniqueIdConfigDao;
import org.bson.types.ObjectId;
import org.springframework.data.annotation.Id;
import org.springframework.data.mongodb.core.mapping.Document;

/**
 * <AUTHOR>
 * @date 2025/4/17
 */
@Document(collection = RechargeUniqueIdConfigDao.TABLE_NAME)
public class RechargeUniqueIdConfigData {

    @Id
    private ObjectId _id;
    private int level; // 等级
    private long rechargeBeans; // 需要充值的钻石数
    private int uniqueIdLevel; // 靓号等级
    private String ruleEn; // 规则文案英语
    private String ruleAr; // 规则文案阿语
    private String levelStyle; // 等级样式

    public ObjectId get_id() {
        return _id;
    }

    public void set_id(ObjectId _id) {
        this._id = _id;
    }

    public int getLevel() {
        return level;
    }

    public void setLevel(int level) {
        this.level = level;
    }

    public long getRechargeBeans() {
        return rechargeBeans;
    }

    public void setRechargeBeans(long rechargeBeans) {
        this.rechargeBeans = rechargeBeans;
    }

    public int getUniqueIdLevel() {
        return uniqueIdLevel;
    }

    public String getRuleEn() {
        return ruleEn;
    }

    public void setRuleEn(String ruleEn) {
        this.ruleEn = ruleEn;
    }

    public String getRuleAr() {
        return ruleAr;
    }

    public void setRuleAr(String ruleAr) {
        this.ruleAr = ruleAr;
    }

    public void setUniqueIdLevel(int uniqueIdLevel) {
        this.uniqueIdLevel = uniqueIdLevel;
    }

    public String getLevelStyle() {
        return levelStyle;
    }

    public void setLevelStyle(String levelStyle) {
        this.levelStyle = levelStyle;
    }
}
