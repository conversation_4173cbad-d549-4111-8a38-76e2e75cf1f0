package com.quhong.mongo.data;

import com.quhong.mongo.dao.RechargePackConfigDao;
import org.bson.types.ObjectId;
import org.springframework.data.annotation.Id;
import org.springframework.data.mongodb.core.mapping.Document;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2023/10/11
 */
@Document(collection = RechargePackConfigDao.TABLE_NAME)
public class RechargePackConfigData {

    @Id
    private ObjectId _id;

    /**
     * 类型： 1天配置 2周配置 3月配置
     */
    private int type;

    /**
     * 档次
     */
    private int level;

    /**
     * 目标充值钻石数
     */
    private int targetDiamondAmount;

    private List<Reward> rewardList;

    public static class Reward {

        private Integer resId; // 资源id

        private Integer resType; // 资源类型 1 勋章 2 麦位框 3 坐骑 4 背包礼物  5 房间锁 6 聊天气泡 7 声波纹 8 浮屏 9 个人背景资源 10靓号 11进房通知 12荣誉称号 100金币 101财富等级

        private String icon; // 奖励图标

        private Integer num; // 奖励数量，天数（除特殊资源都是天数）、金币数（金币resType=100时）、个数(背包礼物resType=4时)、等级(背包礼物resType=101时)、-1永久

        private Integer lockType; // 加锁类型

        public Integer getResId() {
            return resId;
        }

        public void setResId(Integer resId) {
            this.resId = resId;
        }

        public Integer getResType() {
            return resType;
        }

        public void setResType(Integer resType) {
            this.resType = resType;
        }

        public String getIcon() {
            return icon;
        }

        public void setIcon(String icon) {
            this.icon = icon;
        }

        public Integer getNum() {
            return num;
        }

        public void setNum(Integer num) {
            this.num = num;
        }

        public Integer getLockType() {
            return lockType;
        }

        public void setLockType(Integer lockType) {
            this.lockType = lockType;
        }
    }

    public ObjectId get_id() {
        return _id;
    }

    public void set_id(ObjectId _id) {
        this._id = _id;
    }

    public int getType() {
        return type;
    }

    public void setType(int type) {
        this.type = type;
    }

    public int getLevel() {
        return level;
    }

    public void setLevel(int level) {
        this.level = level;
    }

    public int getTargetDiamondAmount() {
        return targetDiamondAmount;
    }

    public void setTargetDiamondAmount(int targetDiamondAmount) {
        this.targetDiamondAmount = targetDiamondAmount;
    }

    public List<Reward> getRewardList() {
        return rewardList;
    }

    public void setRewardList(List<Reward> rewardList) {
        this.rewardList = rewardList;
    }
}
