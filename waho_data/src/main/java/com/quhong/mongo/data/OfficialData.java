package com.quhong.mongo.data;

import com.quhong.core.utils.DateHelper;
import com.quhong.mongo.dao.OfficialDao;
import org.bson.types.ObjectId;
import org.springframework.data.annotation.Id;
import org.springframework.data.mongodb.core.mapping.Document;

import java.util.List;

/**
 * atype: 跳转的功能
 * 0:跳转h5[没有url不跳转], 1: 统一格式，保留不使用[弹窗], 2: 跳转等级页面, 3: 商店Ride坐骑列表, 4: 商店mic列表, 5: 商店bubble列表,
 * 6: 商店voice声波, 7: Unique ID, 8: QUEEN 9:user_page 11:商店飘屏列表  12: 跳勋章佩戴页
 * 13：我的坐骑列表, 14: 我的mic列表 15: 我的bubble列表  16: 我的voice列表  17: 我的飘屏列表
 * 18: 钻石余额页面, 19：币商页面 20：家族主页 21：商城 22：入场通知 23：我的入场通知 27：我的抽奖卷 28:VIP记录页面
 * 99: 指定房间
 * news_type: 消息在app展示状态
 * 0:通用的  1：分享注册的  2：等级的  3: 指定用户ui展示【暂时不用，不要下发】  4：纯图片ui展示  5:各种奖励UI展示 6:多个icon展示
 */

@Document(collection = OfficialDao.TABLE_NAME)
public class OfficialData {
    @Id
    private ObjectId _id;
    private String title;
    private String subTitle;
    private String picture;
    private String body;
    private String act;
    private int valid;
    private String mtime;// 格式"18/01/2018"
    private String url;
    private String to_uid;// 发给谁的，为空时是系统消息， 有uid时为发给某些人
    private int ctime; //时间戳
    private String official_push_id;
    private int atype;
    private int award_type;
    private int award_num;
    private String award_icon;
    private List<AwardInfo> award_list; // 图标列表，配合news_type=6使用
    private String room_id;
    private int width;
    private int height;
    private int news_type;
    private int status;
    private int ntype; // 0官方通知 1活动
    private int delete_start;

    public OfficialData() {

    }

    public OfficialData(String title, String body, String to_uid) {
        this.title = title;
        this.body = body;
        this.to_uid = to_uid;
        this.valid = 1;
        this.ctime = DateHelper.getNowSeconds();
    }

    public static class AwardInfo {
        private String name; // 礼包名称
        private String icon; // 礼包图标
        private String tag; // 礼包角标内容

        public AwardInfo() {
        }

        public AwardInfo(String name, String icon, String tag) {
            this.name = name;
            this.icon = icon;
            this.tag = tag;
        }

        public String getName() {
            return name;
        }

        public void setName(String name) {
            this.name = name;
        }

        public String getIcon() {
            return icon;
        }

        public void setIcon(String icon) {
            this.icon = icon;
        }

        public String getTag() {
            return tag;
        }

        public void setTag(String tag) {
            this.tag = tag;
        }
    }


    public ObjectId get_id() {
        return _id;
    }

    public void set_id(ObjectId _id) {
        this._id = _id;
    }

    public String getTitle() {
        return title;
    }

    public void setTitle(String title) {
        this.title = title;
    }

    public String getSubTitle() {
        return subTitle;
    }

    public void setSubTitle(String subTitle) {
        this.subTitle = subTitle;
    }

    public String getPicture() {
        return picture;
    }

    public void setPicture(String picture) {
        this.picture = picture;
    }

    public String getBody() {
        return body;
    }

    public void setBody(String body) {
        this.body = body;
    }

    public String getAct() {
        return act;
    }

    public void setAct(String act) {
        this.act = act;
    }

    public int getValid() {
        return valid;
    }

    public void setValid(int valid) {
        this.valid = valid;
    }

    public String getMtime() {
        return mtime;
    }

    public void setMtime(String mtime) {
        this.mtime = mtime;
    }

    public String getUrl() {
        return url;
    }

    public void setUrl(String url) {
        this.url = url;
    }

    public String getTo_uid() {
        return to_uid;
    }

    public void setTo_uid(String to_uid) {
        this.to_uid = to_uid;
    }

    public int getCtime() {
        return ctime;
    }

    public void setCtime(int ctime) {
        this.ctime = ctime;
    }

    public String getOfficial_push_id() {
        return official_push_id;
    }

    public void setOfficial_push_id(String official_push_id) {
        this.official_push_id = official_push_id;
    }

    public int getAtype() {
        return atype;
    }

    public void setAtype(int atype) {
        this.atype = atype;
    }

    public int getAward_type() {
        return award_type;
    }

    public void setAward_type(int award_type) {
        this.award_type = award_type;
    }

    public int getAward_num() {
        return award_num;
    }

    public void setAward_num(int award_num) {
        this.award_num = award_num;
    }

    public String getAward_icon() {
        return award_icon;
    }

    public void setAward_icon(String award_icon) {
        this.award_icon = award_icon;
    }

    public List<AwardInfo> getAward_list() {
        return award_list;
    }

    public void setAward_list(List<AwardInfo> award_list) {
        this.award_list = award_list;
    }

    public String getRoom_id() {
        return room_id;
    }

    public void setRoom_id(String room_id) {
        this.room_id = room_id;
    }

    public int getWidth() {
        return width;
    }

    public void setWidth(int width) {
        this.width = width;
    }

    public int getHeight() {
        return height;
    }

    public void setHeight(int height) {
        this.height = height;
    }

    public int getNews_type() {
        return news_type;
    }

    public void setNews_type(int news_type) {
        this.news_type = news_type;
    }

    public int getStatus() {
        return status;
    }

    public void setStatus(int status) {
        this.status = status;
    }

    public int getNtype() {
        return ntype;
    }

    public void setNtype(int ntype) {
        this.ntype = ntype;
    }

    public int getDelete_start() {
        return delete_start;
    }

    public void setDelete_start(int delete_start) {
        this.delete_start = delete_start;
    }
}
