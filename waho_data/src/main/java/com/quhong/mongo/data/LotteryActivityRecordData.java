package com.quhong.mongo.data;

import com.quhong.enums.ResTypeEnum;
import com.quhong.mongo.dao.LotteryActivityRecordDao;
import org.bson.types.ObjectId;
import org.springframework.data.annotation.Id;
import org.springframework.data.mongodb.core.mapping.Document;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2024/8/13
 */
@Document(collection = LotteryActivityRecordDao.TABLE_NAME)
public class LotteryActivityRecordData {

    @Id
    private ObjectId _id;
    private String activityId; // 活动id
    private String uid; // 用户uid
    private int costPoints; // 花费积分数
    private int hasReward; // 是否中奖 0否 1是
    private List<Reward> resultList; // 中奖结果
    private int ctime; // 创建时间

    public static class Reward {
        private String rewardId; // 奖励唯一标识
        /**
         * @see ResTypeEnum
         */
        private Integer resType;// 奖励类型
        private Integer resId;  // 奖励资源id
        private String resIcon; // 奖励资源介绍图片
        private String resName; // 资源名称
        private String resNameAr; // 资源名称阿语
        private Integer days;   //资源时长（天） 0永久
        private Integer num;    // 礼物数量 可能为空
        private int lockType;   // 资源下发方式
        private String tag; // 标签
        private String tagAr; // 标签阿语

        public String getRewardId() {
            return rewardId;
        }

        public void setRewardId(String rewardId) {
            this.rewardId = rewardId;
        }

        public Integer getResType() {
            return resType;
        }

        public void setResType(Integer resType) {
            this.resType = resType;
        }

        public Integer getResId() {
            return resId;
        }

        public void setResId(Integer resId) {
            this.resId = resId;
        }

        public String getResIcon() {
            return resIcon;
        }

        public void setResIcon(String resIcon) {
            this.resIcon = resIcon;
        }

        public Integer getDays() {
            return days;
        }

        public void setDays(Integer days) {
            this.days = days;
        }

        public Integer getNum() {
            return num;
        }

        public void setNum(Integer num) {
            this.num = num;
        }

        public int getLockType() {
            return lockType;
        }

        public void setLockType(int lockType) {
            this.lockType = lockType;
        }

        public String getTag() {
            return tag;
        }

        public void setTag(String tag) {
            this.tag = tag;
        }

        public String getTagAr() {
            return tagAr;
        }

        public void setTagAr(String tagAr) {
            this.tagAr = tagAr;
        }

        public String getResName() {
            return resName;
        }

        public void setResName(String resName) {
            this.resName = resName;
        }

        public String getResNameAr() {
            return resNameAr;
        }

        public void setResNameAr(String resNameAr) {
            this.resNameAr = resNameAr;
        }
    }

    public ObjectId get_id() {
        return _id;
    }

    public void set_id(ObjectId _id) {
        this._id = _id;
    }

    public String getActivityId() {
        return activityId;
    }

    public void setActivityId(String activityId) {
        this.activityId = activityId;
    }

    public String getUid() {
        return uid;
    }

    public void setUid(String uid) {
        this.uid = uid;
    }

    public int getCostPoints() {
        return costPoints;
    }

    public void setCostPoints(int costPoints) {
        this.costPoints = costPoints;
    }

    public List<Reward> getResultList() {
        return resultList;
    }

    public void setResultList(List<Reward> resultList) {
        this.resultList = resultList;
    }

    public int getCtime() {
        return ctime;
    }

    public void setCtime(int ctime) {
        this.ctime = ctime;
    }

    public int getHasReward() {
        return hasReward;
    }

    public void setHasReward(int hasReward) {
        this.hasReward = hasReward;
    }
}
