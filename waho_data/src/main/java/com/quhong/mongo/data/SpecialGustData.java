package com.quhong.mongo.data;

import com.quhong.mongo.dao.SpecialGustDao;
import org.springframework.data.annotation.Id;
import org.springframework.data.mongodb.core.mapping.Document;


@Document(collection = SpecialGustDao.TABLE_NAME)
public class SpecialGustData {

    @Id
    private String _id;  // 账号account
    private String uid;
    private String pwd;

    public String get_id() {
        return _id;
    }

    public void set_id(String _id) {
        this._id = _id;
    }

    public String getUid() {
        return uid;
    }

    public void setUid(String uid) {
        this.uid = uid;
    }

    public String getPwd() {
        return pwd;
    }

    public void setPwd(String pwd) {
        this.pwd = pwd;
    }
}
