package com.quhong.mongo.data;

import com.quhong.mongo.dao.AppPageDao;
import org.bson.types.ObjectId;
import org.springframework.data.mongodb.core.mapping.Document;

@Document(collection = AppPageDao.TABLE_NAME)
public class AppPageData {
    private ObjectId _id;
    private String name;
    private int status;
    private int startTime;
    private int endTime;
    private String url;
    private String urlAr;
    private String iphoneXUrlEn;
    private String iphoneXUrlAr;
    private String link;
    private int actionType; // 10跳转用户
    private String targetId;
    private int skip;
    private int sortNum;
    private int mtime;
    private int ctime;

    public ObjectId get_id() {
        return _id;
    }

    public void set_id(ObjectId _id) {
        this._id = _id;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public int getStatus() {
        return status;
    }

    public void setStatus(int status) {
        this.status = status;
    }

    public int getStartTime() {
        return startTime;
    }

    public void setStartTime(int startTime) {
        this.startTime = startTime;
    }

    public int getEndTime() {
        return endTime;
    }

    public void setEndTime(int endTime) {
        this.endTime = endTime;
    }

    public String getUrl() {
        return url;
    }

    public void setUrl(String url) {
        this.url = url;
    }

    public String getUrlAr() {
        return urlAr;
    }

    public void setUrlAr(String urlAr) {
        this.urlAr = urlAr;
    }

    public String getIphoneXUrlEn() {
        return iphoneXUrlEn;
    }

    public void setIphoneXUrlEn(String iphoneXUrlEn) {
        this.iphoneXUrlEn = iphoneXUrlEn;
    }

    public String getIphoneXUrlAr() {
        return iphoneXUrlAr;
    }

    public void setIphoneXUrlAr(String iphoneXUrlAr) {
        this.iphoneXUrlAr = iphoneXUrlAr;
    }

    public String getLink() {
        return link;
    }

    public void setLink(String link) {
        this.link = link;
    }

    public int getActionType() {
        return actionType;
    }

    public void setActionType(int actionType) {
        this.actionType = actionType;
    }

    public String getTargetId() {
        return targetId;
    }

    public void setTargetId(String targetId) {
        this.targetId = targetId;
    }

    public int getSkip() {
        return skip;
    }

    public void setSkip(int skip) {
        this.skip = skip;
    }

    public int getSortNum() {
        return sortNum;
    }

    public void setSortNum(int sortNum) {
        this.sortNum = sortNum;
    }

    public int getMtime() {
        return mtime;
    }

    public void setMtime(int mtime) {
        this.mtime = mtime;
    }

    public int getCtime() {
        return ctime;
    }

    public void setCtime(int ctime) {
        this.ctime = ctime;
    }
}
