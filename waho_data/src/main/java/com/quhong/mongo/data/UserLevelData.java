package com.quhong.mongo.data;

import com.quhong.mongo.dao.UserLevelDao;
import org.bson.types.ObjectId;
import org.springframework.data.annotation.Id;
import org.springframework.data.mongodb.core.mapping.Document;
import org.springframework.data.mongodb.core.mapping.Field;

import java.util.ArrayList;
import java.util.List;

@Document(collection = UserLevelDao.TABLE_NAME)
public class UserLevelData {
    @Id
    private ObjectId _id;
    private String uid;
    private int active_level; // 活跃等级
    private int charm_level; // 魅力等级
    private int wealth_level; // 财富等级
    private long active_exp_next;// 到下一级所需的经验值(活跃等级)
    private long charm_exp_next;//到下一级所需的经验值(魅力等级)
    private long wealth_exp_next;//到下一级所需的经验值(财富等级)
    private long active_total_exp;// 活跃总经验值
    private long charm_total_exp;//魅力总经验值
    private long wealth_total_exp;//财富总经验值

    @Field("reached")
    private List<String> reached = new ArrayList<>();

    public ObjectId get_id() {
        return _id;
    }

    public void set_id(ObjectId _id) {
        this._id = _id;
    }

    public String getUid() {
        return uid;
    }

    public void setUid(String uid) {
        this.uid = uid;
    }

    public int getActive_level() {
        return active_level;
    }

    public void setActive_level(int active_level) {
        this.active_level = active_level;
    }

    public int getCharm_level() {
        return charm_level;
    }

    public void setCharm_level(int charm_level) {
        this.charm_level = charm_level;
    }

    public int getWealth_level() {
        return wealth_level;
    }

    public void setWealth_level(int wealth_level) {
        this.wealth_level = wealth_level;
    }

    public long getActive_total_exp() {
        return active_total_exp;
    }

    public void setActive_total_exp(long active_total_exp) {
        this.active_total_exp = active_total_exp;
    }

    public long getCharm_total_exp() {
        return charm_total_exp;
    }

    public void setCharm_total_exp(long charm_total_exp) {
        this.charm_total_exp = charm_total_exp;
    }

    public long getWealth_total_exp() {
        return wealth_total_exp;
    }

    public void setWealth_total_exp(long wealth_total_exp) {
        this.wealth_total_exp = wealth_total_exp;
    }

    public List<String> getReached() {
        return reached;
    }

    public void setReached(List<String> reached) {
        this.reached = reached;
    }

    public long getActive_exp_next() {
        return active_exp_next;
    }

    public void setActive_exp_next(long active_exp_next) {
        this.active_exp_next = active_exp_next;
    }

    public long getCharm_exp_next() {
        return charm_exp_next;
    }

    public void setCharm_exp_next(long charm_exp_next) {
        this.charm_exp_next = charm_exp_next;
    }

    public long getWealth_exp_next() {
        return wealth_exp_next;
    }

    public void setWealth_exp_next(long wealth_exp_next) {
        this.wealth_exp_next = wealth_exp_next;
    }
}
