package com.quhong.mongo.data;

import com.quhong.enums.ResTypeEnum;
import com.quhong.enums.SLangType;
import com.quhong.msg.obj.PackInfoObject;
import com.quhong.service.PackService;
import com.quhong.utils.WalletUtils;

/**
 * <AUTHOR>
 * @date 2023/8/30
 */
public class PackData {

    /**
     * @see ResTypeEnum
     */
    private Integer resType; // 资源类型 1 勋章 2 麦位框 3 坐骑 4 背包礼物  5 房间锁 6 聊天气泡 7 声波纹 8 浮屏 9 个人背景资源 10靓号 11进房通知 12荣誉称号 100金币 101财富等级 102vip
    private Integer resId; // 资源id
    private String icon; // 奖励图标
    private String cartoon; // 奖励动画
    private Integer num; // 奖励数量，天数（除特殊资源都是天数）、金币数（金币resType=100时）、个数(背包礼物resType=4时)、等级(背包礼物resType=101时)、-1永久
    private Integer days; // 背包礼物有效天数
    private int lockType;  // 资源下发方式

    private int inRoomScreen;            // 本房间公屏
    private int allRoomScreen;           // 全房间公屏
    private int broadcast;               // 房间横幅广播

    public PackData() {
    }

    public PackData(Integer resType, Integer resId, String icon, Integer num, int lockType) {
        this.resType = resType;
        this.resId = resId;
        this.icon = icon;
        this.num = num;
        this.lockType = lockType;
    }

    public PackData(Integer resType, Integer resId, String icon, Integer num, Integer days, int lockType) {
        this.resType = resType;
        this.resId = resId;
        this.icon = icon;
        this.num = num;
        this.days = days;
        this.lockType = lockType;
    }

    public PackInfoObject toPackInfoObject(int slang) {
        ResTypeEnum resTypeEnum = getResTypeEnum();
        if (null == resTypeEnum) {
            return null;
        }
        PackInfoObject object = new PackInfoObject();
        object.setIcon(icon);
        if (resTypeEnum == ResTypeEnum.VIP_LEVEL) {
            object.setName(resTypeEnum.getNameEn() + " " + resId);
        } else {
            object.setName(SLangType.ENGLISH == slang ? resTypeEnum.getNameEn() : resTypeEnum.getNameAr());
        }
        if (resTypeEnum == ResTypeEnum.COIN || resTypeEnum == ResTypeEnum.DIAMONDS) {
            object.setTag(SLangType.ENGLISH == slang ? resTypeEnum.getTagEn().formatted(WalletUtils.diamondsForDisplay(num)) : resTypeEnum.getTagAr().formatted(WalletUtils.diamondsForDisplay(num)));
        } else {
            object.setTag(0 >= num ? SLangType.ENGLISH == slang ? PackService.PERMANENT : PackService.PERMANENT_AR : SLangType.ENGLISH == slang ? resTypeEnum.getTagEn().formatted(num) : resTypeEnum.getTagAr().formatted(num));
        }
        return object;
    }

    public ResTypeEnum getResTypeEnum() {
        return ResTypeEnum.getByType(resType);
    }

    public Integer getResId() {
        return resId;
    }

    public void setResId(Integer resId) {
        this.resId = resId;
    }

    public Integer getResType() {
        return resType;
    }

    public void setResType(Integer resType) {
        this.resType = resType;
    }

    public String getIcon() {
        return icon;
    }

    public String getCartoon() {
        return cartoon;
    }

    public void setCartoon(String cartoon) {
        this.cartoon = cartoon;
    }

    public void setIcon(String icon) {
        this.icon = icon;
    }

    public Integer getNum() {
        return num;
    }

    public void setNum(Integer num) {
        this.num = num;
    }

    public int getLockType() {
        return lockType;
    }

    public void setLockType(int lockType) {
        this.lockType = lockType;
    }

    public Integer getDays() {
        return days;
    }

    public void setDays(Integer days) {
        this.days = days;
    }

    public int getInRoomScreen() {
        return inRoomScreen;
    }

    public void setInRoomScreen(int inRoomScreen) {
        this.inRoomScreen = inRoomScreen;
    }

    public int getAllRoomScreen() {
        return allRoomScreen;
    }

    public void setAllRoomScreen(int allRoomScreen) {
        this.allRoomScreen = allRoomScreen;
    }

    public int getBroadcast() {
        return broadcast;
    }

    public void setBroadcast(int broadcast) {
        this.broadcast = broadcast;
    }
}
