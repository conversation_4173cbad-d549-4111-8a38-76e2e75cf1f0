package com.quhong.mongo.data;

import com.quhong.mongo.dao.AnchorDataStatDao;
import org.bson.types.ObjectId;
import org.springframework.data.annotation.Id;
import org.springframework.data.mongodb.core.mapping.Document;

/**
 * <AUTHOR>
 * @date 2024/12/26
 */
@Document(collection = AnchorDataStatDao.TABLE_NAME)
public class AnchorDataStatData {

    @Id
    private ObjectId _id;
    private String uid;
    private int familyId;
    private int intDate;
    private int workDays; // 工作天数
    private int liveWorkDays; // 直播工作天数
    private int chatWorkDays; // 语聊工作天数
    private int upMicTime; // 上麦总时长
    private int liveTime; // 直播时长
    private int chatTime; // 语聊时长
    private long roomReceiveBean; // 房间收礼钻石
    private long roomCharmIncome; // 房间收入
    private long liveRoomCharmIncome; // 直播房魅力值收入
    private long chatRoomCharmIncome; // 语聊房魅力值收入
    private int sendGiftUserNum; // 给主播送礼的人数

    public ObjectId get_id() {
        return _id;
    }

    public void set_id(ObjectId _id) {
        this._id = _id;
    }

    public String getUid() {
        return uid;
    }

    public void setUid(String uid) {
        this.uid = uid;
    }

    public int getFamilyId() {
        return familyId;
    }

    public void setFamilyId(int familyId) {
        this.familyId = familyId;
    }

    public int getIntDate() {
        return intDate;
    }

    public void setIntDate(int intDate) {
        this.intDate = intDate;
    }

    public int getWorkDays() {
        return workDays;
    }

    public void setWorkDays(int workDays) {
        this.workDays = workDays;
    }

    public int getLiveWorkDays() {
        return liveWorkDays;
    }

    public void setLiveWorkDays(int liveWorkDays) {
        this.liveWorkDays = liveWorkDays;
    }

    public int getChatWorkDays() {
        return chatWorkDays;
    }

    public void setChatWorkDays(int chatWorkDays) {
        this.chatWorkDays = chatWorkDays;
    }

    public int getUpMicTime() {
        return upMicTime;
    }

    public void setUpMicTime(int upMicTime) {
        this.upMicTime = upMicTime;
    }

    public int getLiveTime() {
        return liveTime;
    }

    public void setLiveTime(int liveTime) {
        this.liveTime = liveTime;
    }

    public int getChatTime() {
        return chatTime;
    }

    public void setChatTime(int chatTime) {
        this.chatTime = chatTime;
    }

    public long getRoomReceiveBean() {
        return roomReceiveBean;
    }

    public void setRoomReceiveBean(long roomReceiveBean) {
        this.roomReceiveBean = roomReceiveBean;
    }

    public long getRoomCharmIncome() {
        return roomCharmIncome;
    }

    public void setRoomCharmIncome(long roomCharmIncome) {
        this.roomCharmIncome = roomCharmIncome;
    }

    public long getLiveRoomCharmIncome() {
        return liveRoomCharmIncome;
    }

    public void setLiveRoomCharmIncome(long liveRoomCharmIncome) {
        this.liveRoomCharmIncome = liveRoomCharmIncome;
    }

    public long getChatRoomCharmIncome() {
        return chatRoomCharmIncome;
    }

    public void setChatRoomCharmIncome(long chatRoomCharmIncome) {
        this.chatRoomCharmIncome = chatRoomCharmIncome;
    }

    public int getSendGiftUserNum() {
        return sendGiftUserNum;
    }

    public void setSendGiftUserNum(int sendGiftUserNum) {
        this.sendGiftUserNum = sendGiftUserNum;
    }
}
