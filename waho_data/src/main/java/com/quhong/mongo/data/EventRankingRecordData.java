package com.quhong.mongo.data;

import com.quhong.mongo.dao.EventRankingRecordDao;
import org.springframework.data.mongodb.core.mapping.Document;

import java.util.Map;

@Document(collection = EventRankingRecordDao.TABLE_NAME)
public class EventRankingRecordData {

    private int eventId;
    private String rankingType;
    private Map<String, Long> rankingMap;

    public EventRankingRecordData() {
    }

    public EventRankingRecordData(int eventId, String rankingType, Map<String, Long> rankingMap) {
        this.eventId = eventId;
        this.rankingType = rankingType;
        this.rankingMap = rankingMap;
    }

    public int getEventId() {
        return eventId;
    }

    public void setEventId(int eventId) {
        this.eventId = eventId;
    }

    public String getRankingType() {
        return rankingType;
    }

    public void setRankingType(String rankingType) {
        this.rankingType = rankingType;
    }

    public Map<String, Long> getRankingMap() {
        return rankingMap;
    }

    public void setRankingMap(Map<String, Long> rankingMap) {
        this.rankingMap = rankingMap;
    }
}
