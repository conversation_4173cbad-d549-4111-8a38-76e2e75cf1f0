package com.quhong.redis;

import com.quhong.core.utils.DateHelper;
import com.quhong.mongo.dao.FamilyDevoteDao;
import com.quhong.mysql.dao.FamilyMemberDao;
import jakarta.annotation.Resource;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.context.annotation.Lazy;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.stereotype.Component;
import org.springframework.util.ObjectUtils;

import java.util.concurrent.TimeUnit;

@Component
@Lazy
public class FamilyDevoteHelperRedis {
    private final Logger logger = LoggerFactory.getLogger(getClass());
    private String expireDate = "";

    private static final String TAKE_MIC = "take_mic";
    private static final int TAKE_MIC_LIMIT = 480;

    private static final String PLAY_LUDO = "play_ludo";
    private static final int PLAY_LUDO_LIMIT = 200;
    private static final int PLAY_LUDO_DEVOTE = 20;

    private static final String PLAY_UMO = "play_umo";
    private static final int PLAY_UMO_LIMIT = 200;
    private static final int PLAY_UMO_DEVOTE = 20;

    private static final String PLAY_LUCKY_WHEEL = "play_lucky_wheel";
    private static final int PLAY_LUCKY_WHEEL_LIMIT = 200;
    private static final int PLAY_LUCKY_WHEEL_DEVOTE = 20;

    private static final String POST_MOMENT = "post_moment";
    private static final int POST_MOMENT_LIMIT = 60;
    private static final int POST_MOMENT_DEVOTE = 20;

    @Resource(name = DataRedisBean.MAIN_CLUSTER)
    private StringRedisTemplate clusterRedis;
    @Resource
    private FamilyDevoteDao familyDevoteDao;
    @Resource
    private FamilyMemberDao familyMemberDao;


    public void takeMicDevote(String uid, int takeMicMinutes) {
        incrFamilyDevote(uid, TAKE_MIC, takeMicMinutes, TAKE_MIC_LIMIT);
    }

    public void playLudoDevote(String uid) {
        incrFamilyDevote(uid, PLAY_LUDO, PLAY_LUDO_DEVOTE, PLAY_LUDO_LIMIT);
    }

    public void playUmoDevote(String uid) {
        incrFamilyDevote(uid, PLAY_UMO, PLAY_UMO_DEVOTE, PLAY_UMO_LIMIT);
    }

    public void playLuckyWheelDevote(String uid) {
        incrFamilyDevote(uid, PLAY_LUCKY_WHEEL, PLAY_LUCKY_WHEEL_DEVOTE, PLAY_LUCKY_WHEEL_LIMIT);
    }

    public void postMomentDevote(String uid) {
        incrFamilyDevote(uid, POST_MOMENT, POST_MOMENT_DEVOTE, POST_MOMENT_LIMIT);
    }

    public void incrFamilyDevote(String uid, String type, int devote, int devoteLimit) {
        int familyId = familyMemberDao.getAllMemberFromCache().getOrDefault(uid, 0);
        if (familyId == 0) {
            return;
        }
        int todayFamilyDevote = getTodayFamilyDevote(uid, familyId, type);
        int limitDevote = checkLimit(devote, todayFamilyDevote, devoteLimit);
        if (limitDevote > 0) {
            incrFamilyDevote(uid, familyId, type, limitDevote);
        }
    }

    private int getTodayFamilyDevote(String uid, int familyId, String type) {
        try {
            String dateStr = DateHelper.ARABIAN.formatDateInDay();
            String key = getKey(dateStr);
            Object value = clusterRedis.opsForHash().get(key, getHashKey(uid, familyId, type));
            return ObjectUtils.isEmpty(value) ? 0 : Integer.parseInt(String.valueOf(value));
        } catch (Exception e) {
            logger.error("getTodayFamilyDevote error uid={} type={}", uid, type, e);
            return 0;
        }
    }

    private void incrFamilyDevote(String uid, int familyId, String type, int devote) {
        try {
            String dateStr = DateHelper.ARABIAN.formatDateInDay();
            String key = getKey(dateStr);
            clusterRedis.opsForHash().increment(key, getHashKey(uid, familyId, type), devote);
            familyDevoteDao.incrDevote(uid, familyId, devote);
            logger.info("incrDevote uid={} familyId={} type={} devote={}", uid, familyId, type, devote);
            if (!dateStr.equals(expireDate)) {
                clusterRedis.expire(key, 2, TimeUnit.DAYS);
                expireDate = dateStr;
            }
        } catch (Exception e) {
            logger.error("incrFamilyDevote error uid={} type={}", uid, type, e);
        }
    }

    private int checkLimit(int devote, int current, int limit) {
        if (devote + current > limit) {
            devote = limit - current;
        }
        return devote;
    }

    private String getKey(String dateStr) {
        return "hash:familyDevoteLimit:" + dateStr;
    }

    private String getHashKey(String uid, int familyId, String type) {
        return type + ":" + uid + ":" + familyId;
    }
}
