package com.quhong.redis;

import com.quhong.constant.ExpireTimeConstant;
import com.quhong.core.utils.DateHelper;
import jakarta.annotation.Resource;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.context.annotation.Lazy;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.data.redis.core.ZSetOperations;
import org.springframework.stereotype.Component;

import java.math.BigDecimal;
import java.util.*;
import java.util.concurrent.TimeUnit;

@Lazy
@Component
public class ActivityOtherRedis {
    private static final Logger logger = LoggerFactory.getLogger(ActivityOtherRedis.class);

    @Resource(name = DataRedisBean.MAIN_CLUSTER)
    private StringRedisTemplate clusterTemplate;

    private String reachingExpireDate = "";

    /**
     *
     * @param activityId  活动名称
     * @param rankType  排行榜类型 1: 发送 2: 接收 3: 房间
     * @param giftId  礼物id  大于0  单独每个礼物榜
     * @param roundNum  活动轮次
     * @return key
     */
    public String getOtherActivityKey(String activityId, int rankType, int giftId, int roundNum) {
        return String.format("zset:otherActivity:%s:%s:%s:%s", activityId, rankType, giftId, roundNum);
    }

    /**
     * 获取分数
     */
    public int getOtherRankingScore(String activityId, String aid, int rankType, int giftId, int roundNum) {
        try{
            Double score = clusterTemplate.opsForZSet().score(getOtherActivityKey(activityId, rankType, giftId, roundNum), aid);
            return score != null ? score.intValue() : 0;
        } catch (Exception e) {
            logger.error("getScore error activityId={} aid={} rankType={}", activityId, aid, rankType, e);
            return 0;
        }
    }

    public void incrOtherRankingScore(String activityId, String aid, int score, int rankType, int giftId, int roundNum) {
        try {
            String key = getOtherActivityKey(activityId, rankType, giftId, roundNum);
            int curScore = getOtherRankingScore(activityId, aid, rankType, giftId, roundNum);
            double rankScore = new BigDecimal(curScore + score + "." + (Integer.MAX_VALUE - DateHelper.getNowSeconds())).doubleValue();
            clusterTemplate.opsForZSet().add(key, aid, rankScore);
            clusterTemplate.expire(key, ExpireTimeConstant.EXPIRE_DAYS_180, TimeUnit.DAYS);
        } catch (Exception e) {
            logger.error("incrRankingScore error activityId={} aid={} giftId={} score={}", activityId, aid, giftId, score, e);
        }
    }



    /**
     * 获取排行榜
     *
     * @param activityId 活动
     * @param rankType   排行榜类型
     * @param length     排行榜长度
     */
    public List<String> getOtherRankingList(String activityId, int rankType, int giftId, int roundNum, int length) {
        List<String> rankingList = new ArrayList<>();
        String key = getOtherActivityKey(activityId, rankType, giftId, roundNum);
        Set<ZSetOperations.TypedTuple<String>> rangeWithScores = clusterTemplate.opsForZSet().reverseRangeWithScores(key, 0, length - 1);
        if (null == rangeWithScores) {
            return rankingList;
        }
        for (ZSetOperations.TypedTuple<String> rangeWithScore : rangeWithScores) {
            if (null == rangeWithScore.getValue() || null == rangeWithScore.getScore()) {
                continue;
            }
            rankingList.add(rangeWithScore.getValue());
        }
        return rankingList;
    }

    /**
     * 获取排行榜
     *
     * @param activityId 活动
     * @param rankType   排行榜类型
     * @param length     排行榜长度
     */
    public List<String> getOtherRankingList(String activityId, int rankType, int length, int roundNum) {
        List<String> rankingList = new ArrayList<>();
        String key = getOtherActivityKey(activityId, rankType, roundNum);
        Set<ZSetOperations.TypedTuple<String>> rangeWithScores = clusterTemplate.opsForZSet().reverseRangeWithScores(key, 0, length - 1);
        if (null == rangeWithScores) {
            return rankingList;
        }
        for (ZSetOperations.TypedTuple<String> rangeWithScore : rangeWithScores) {
            if (null == rangeWithScore.getValue() || null == rangeWithScore.getScore()) {
                continue;
            }
            rankingList.add(rangeWithScore.getValue());
        }
        return rankingList;
    }

    /**
     * 获取带分数排行榜
     *
     * @param activityId 活动名称
     * @param rankType   排行榜类型
     * @param length     排行榜长度
     */
    public Map<String, Integer> getOtherRankingMap(String activityId, int rankType, int giftId, int roundNum, int length) {
        Map<String, Integer> linkedRankMap = new LinkedHashMap<>();
        String key = getOtherActivityKey(activityId, rankType, giftId, roundNum);
        Set<ZSetOperations.TypedTuple<String>> rangeWithScores = clusterTemplate.opsForZSet().reverseRangeWithScores(key, 0, length - 1);
        if (null == rangeWithScores) {
            return linkedRankMap;
        }
        for (ZSetOperations.TypedTuple<String> rangeWithScore : rangeWithScores) {
            if (null == rangeWithScore.getValue() || null == rangeWithScore.getScore()) {
                continue;
            }
            linkedRankMap.put(rangeWithScore.getValue(), rangeWithScore.getScore().intValue());
        }
        return linkedRankMap;
    }

    /**
     * 获取指定分数指定长度段带分数排行榜
     *
     * @param activityId 活动名称
     * @param rankType   排行榜类型
     * @param length     排行榜长度
     */
    public Map<String, Integer> getOtherRankingMapByScore(String activityId, int rankType, int giftId, int roundNum, int length, int min, int max) {
        Map<String, Integer> linkedRankMap = new LinkedHashMap<>();
        String key = getOtherActivityKey(activityId, rankType, giftId, roundNum);
        Set<ZSetOperations.TypedTuple<String>> rangeWithScores = clusterTemplate.opsForZSet().reverseRangeByScoreWithScores(key, min, max, 0, length);
        if (null == rangeWithScores) {
            return linkedRankMap;
        }
        for (ZSetOperations.TypedTuple<String> rangeWithScore : rangeWithScores) {
            if (null == rangeWithScore.getValue() || null == rangeWithScore.getScore()) {
                continue;
            }
            linkedRankMap.put(rangeWithScore.getValue(), rangeWithScore.getScore().intValue());
        }
        return linkedRankMap;
    }

    /**
     * 获取指定分数带分数排行榜
     *
     * @param activityId 活动名称
     * @param rankType   排行榜类型
     */
    public Map<String, Integer> getOtherRankingMapByScore(String activityId, int rankType, int giftId, int roundNum, int min, int max) {
        Map<String, Integer> linkedRankMap = new LinkedHashMap<>();
        String key = getOtherActivityKey(activityId, rankType, giftId, roundNum);
        Set<ZSetOperations.TypedTuple<String>> rangeWithScores = clusterTemplate.opsForZSet().reverseRangeByScoreWithScores(key, min, max);
        if (null == rangeWithScores) {
            return linkedRankMap;
        }
        for (ZSetOperations.TypedTuple<String> rangeWithScore : rangeWithScores) {
            if (null == rangeWithScore.getValue() || null == rangeWithScore.getScore()) {
                continue;
            }
            linkedRankMap.put(rangeWithScore.getValue(), rangeWithScore.getScore().intValue());
        }
        return linkedRankMap;
    }


    /**
     * 获取排名，返回的数据=redis的排行+1
     */
    public int getOtherRank(String activityId, String aid, int rankType, int giftId, int roundNum) {
        try {
            String key = getOtherActivityKey(activityId, rankType, giftId, roundNum);
            Long rank = clusterTemplate.opsForZSet().reverseRank(key, aid);
            return rank != null ? rank.intValue() + 1 : 0;
        } catch (Exception e) {
            logger.error("getOtherRank error activityId={} aid={} rankType={}", activityId, aid, rankType, e);
            return 0;
        }
    }



    /**
     * 记录每日排行榜
     * @param activityId  活动名称
     * @param rankType  排行榜类型 1: 发送 2: 接收 3: 房间
     * @return key
     */
    private String getOtherActivityDailyKey(String activityId, int rankType, String dailyNum, int giftId, int roundNum) {
        return String.format("zset:otherActivity:%s:%s:%s:%s:%s", activityId, rankType, dailyNum, giftId, roundNum);
    }

    /**
     * 获取每日分数
     */
    public int getOtherRankingDailyScore(String activityId, String aid, int rankType, String dailyNum, int giftId, int roundNum) {
        try{
            Double score = clusterTemplate.opsForZSet().score(getOtherActivityDailyKey(activityId, rankType, dailyNum, giftId, roundNum), aid);
            return score != null ? score.intValue() : 0;
        } catch (Exception e) {
            logger.error("getScore error activityId={} aid={} rankType={}", activityId, aid, rankType, e);
            return 0;
        }
    }

    public void incrOtherRankingDailyScore(String activityId, String aid, int score, int rankType, String dailyNum, int giftId, int roundNum) {
        try {
            String key = getOtherActivityDailyKey(activityId, rankType, dailyNum, giftId, roundNum);
            int curScore = getOtherRankingDailyScore(activityId, aid, rankType, dailyNum, giftId, roundNum);
            double rankScore = new BigDecimal(curScore + score + "." + (Integer.MAX_VALUE - DateHelper.getNowSeconds())).doubleValue();
            clusterTemplate.opsForZSet().add(key, aid, rankScore);
            clusterTemplate.expire(key, ExpireTimeConstant.EXPIRE_DAYS_180, TimeUnit.DAYS);
        } catch (Exception e) {
            logger.error("incrOtherRankingDailyScore error activityId={} aid={} giftId={} score={}", activityId, aid, giftId, score, e);
        }
    }

    /**
     * 获取每日带分数排行榜
     *
     * @param activityId 活动名称
     * @param rankType   排行榜类型
     * @param length     排行榜长度
     */
    public Map<String, Integer> getOtherRankingDailyMap(String activityId, int rankType, String dailyNum, int giftId, int roundNum, int length) {
        Map<String, Integer> linkedRankMap = new LinkedHashMap<>();
        String key = getOtherActivityDailyKey(activityId, rankType, dailyNum, giftId, roundNum);
        Set<ZSetOperations.TypedTuple<String>> rangeWithScores = clusterTemplate.opsForZSet().reverseRangeWithScores(key, 0, length - 1);
        if (null == rangeWithScores) {
            return linkedRankMap;
        }
        for (ZSetOperations.TypedTuple<String> rangeWithScore : rangeWithScores) {
            if (null == rangeWithScore.getValue() || null == rangeWithScore.getScore()) {
                continue;
            }
            linkedRankMap.put(rangeWithScore.getValue(), rangeWithScore.getScore().intValue());
        }
        return linkedRankMap;
    }


    /**
     * 获取排名，返回的数据=redis的排行+1
     */
    public int getOtherDailyRank(String activityId, String aid, int rankType, String dailyNum, int giftId, int roundNum) {
        try {
            String key = getOtherActivityDailyKey(activityId, rankType, dailyNum, giftId, roundNum);
            Long rank = clusterTemplate.opsForZSet().reverseRank(key, aid);
            return rank != null ? rank.intValue() + 1 : 0;
        } catch (Exception e) {
            logger.error("getOtherDailyRank error activityId={} aid={} rankType={}", activityId, aid, rankType, e);
            return 0;
        }
    }



    /**
     * 等级勋章另外记录一个key
     * @param activityId 活动名称
     * @param rankType 排行榜类型
     * @return key
     */
    private String getOtherReachingKey(String activityId, int rankType, int giftId, int roundNum) {
        return String.format("zset:otherReaching:%s:%s:%s:%s", activityId, rankType, giftId, roundNum);
    }

    /**
     * 获取等级勋章指定分数带分数排行榜
     *
     * @param activityId 活动名称
     * @param rankType   排行榜类型
     */
    public Map<String, Integer> getOtherReachingMapByScore(String activityId, int rankType, int giftId, int roundNum, int min, int max) {
        Map<String, Integer> linkedRankMap = new LinkedHashMap<>();
        String key = getOtherReachingKey(activityId, rankType, giftId, roundNum);
        Set<ZSetOperations.TypedTuple<String>> rangeWithScores = clusterTemplate.opsForZSet().reverseRangeByScoreWithScores(key, min, max);
        if (null == rangeWithScores) {
            return linkedRankMap;
        }
        for (ZSetOperations.TypedTuple<String> rangeWithScore : rangeWithScores) {
            if (null == rangeWithScore.getValue() || null == rangeWithScore.getScore()) {
                continue;
            }
            linkedRankMap.put(rangeWithScore.getValue(), rangeWithScore.getScore().intValue());
        }
        return linkedRankMap;
    }

    public int getOtherReachingScore(String activityId, String uid, int rankType, int giftId, int roundNum) {
        try{
            String key = getOtherReachingKey(activityId, rankType, giftId, roundNum);
            Double score = clusterTemplate.opsForZSet().score(key, uid);
            if (score != null) {
                return score.intValue();
            }
            return 0;
        } catch (Exception e) {
            logger.error("getReachingScore error activityName={} uid={} rankType={}", activityId, uid, rankType, e);
            return 0;
        }
    }

    public int incrOtherReachingScore(String activityId, String aid, int score, int rankType, int giftId, int roundNum) {
        try {
            String key = getOtherReachingKey(activityId, rankType, giftId, roundNum);
            Double value = clusterTemplate.opsForZSet().incrementScore(key, aid, score);
            String dateStr = DateHelper.ARABIAN.formatDateInDay();
            if (!dateStr.equals(reachingExpireDate)) {
                clusterTemplate.expire(key, 180, TimeUnit.DAYS);
                reachingExpireDate = dateStr;
            }
            return value != null ? value.intValue() : 0;
        } catch (Exception e) {
            logger.error("incrOtherReachingScore error activityId={} aid={} score={}", activityId, aid, score, e);
            return 0;
        }
    }

    public String getCelebrityTop1(String activityId) {
        String key = getCelebrityRankingKey(activityId);
        try {
            Set<ZSetOperations.TypedTuple<String>> rangeWithScores = clusterTemplate.opsForZSet().reverseRangeWithScores(key, 0, 0);
            if (null == rangeWithScores) {
                return "";
            }
            for (ZSetOperations.TypedTuple<String> rangeWithScore : rangeWithScores) {
                return rangeWithScore.getValue();
            }
        } catch (Exception e) {
            logger.error("getCelebrityTop1 error. activityId={} {}", activityId, e.getMessage(), e);
        }
        return "";
    }

    private String getCelebrityRankingKey(String activityId) {
        return "zset:celebrityRanking_" + activityId;
    }

    /**
     *
     * @param activityId  活动名称
     * @param rankType  排行榜类型 1: 发送 2: 接收 3: 房间
     * @return key
     */
    private String getOtherActivityKey(String activityId, int rankType, int roundNum) {
        if(roundNum > 0){
            return String.format("zset:otherActivity:%s:%s:%s", activityId, rankType, roundNum);
        }else {
            return String.format("zset:otherActivity:%s:%s", activityId, rankType);
        }
    }
}
