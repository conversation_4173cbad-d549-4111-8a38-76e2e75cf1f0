package com.quhong.redis;

import com.quhong.core.utils.DateHelper;
import jakarta.annotation.Resource;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.data.redis.core.StringRedisTemplate;

import java.util.Collections;
import java.util.Set;
import java.util.concurrent.TimeUnit;

public class RoomHotCardRedis {
    
    private static final Logger logger = LoggerFactory.getLogger(RoomHotCardRedis.class);

    @Resource(name = DataRedisBean.MAIN_CLUSTER)
    private StringRedisTemplate clusterTemplate;
    
    public void addHotCardRoom(String roomId, int endTime) {
        try {
            String key = getHotCardRoomKey();
            // 删除过期数据
            clusterTemplate.opsForZSet().removeRangeByScore(key, 0, DateHelper.getNowSeconds());
            clusterTemplate.opsForZSet().add(key, roomId, endTime);
            clusterTemplate.expire(key, 1, TimeUnit.DAYS);
        } catch (Exception e) {
            logger.error("addHotCardRoom error. roomId={} endTime={} {}", roomId, endTime, e.getMessage(), e);
        }
    }

    public int getRoomHotCardEndTime(String roomId) {
        try {
            Double score = clusterTemplate.opsForZSet().score(getHotCardRoomKey(), roomId);
            return null == score ? 0 : score.intValue();
        } catch (Exception e) {
            logger.error("getRoomHotCardEndTime error. roomId={} {}", roomId, e.getMessage(), e);
            return 0;
        }
    }
    
    public Set<String> getHotCardRoomSet() {
        try {
            return clusterTemplate.opsForZSet().rangeByScore(getHotCardRoomKey(), DateHelper.getNowSeconds(), Integer.MAX_VALUE);
        } catch (Exception e) {
            logger.error("getHotCardRoomSet error. {}", e.getMessage(), e);
        }
        return Collections.emptySet();
    }

    private String getHotCardRoomKey() {
        return "zset:hotCardRoom";
    }
}
