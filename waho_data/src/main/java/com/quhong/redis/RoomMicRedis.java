package com.quhong.redis;

import com.alibaba.fastjson.JSON;
import com.quhong.config.CaffeineCacheConfig;
import com.quhong.constant.ExpireTimeConstant;
import com.quhong.core.utils.DateHelper;
import com.quhong.mongo.dao.AnchorDataStatDao;
import com.quhong.msg.obj.RoomMicInfoObject;
import com.quhong.vo.RoomMicListVo;
import jakarta.annotation.Resource;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.context.annotation.Lazy;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.stereotype.Component;
import org.springframework.util.ObjectUtils;
import org.springframework.util.StringUtils;

import java.util.*;
import java.util.concurrent.TimeUnit;

@Component
@Lazy
public class RoomMicRedis {
    private static final Logger logger = LoggerFactory.getLogger(RoomMicRedis.class);
    private static final int EXPIRE_DAY = 5;
    private String micTimeExpireDate = "";

    @Resource(name = DataRedisBean.MAIN_CLUSTER)
    private StringRedisTemplate clusterTemplate;
    @Resource
    private RoomMicRedis roomMicRedis;
    @Resource
    private AnchorDataStatDao anchorDataStatDao;

    public boolean isMember(String roomId, int position) {
        String key = getKey(roomId);
        try {
            return Boolean.TRUE.equals(clusterTemplate.opsForSet().isMember(key, position + ""));
        } catch (Exception e) {
            logger.error("room mic is member. roomId={} {}", roomId, e.getMessage(), e);
        }
        return false;
    }

    private String getKey(String roomId) {
        return "room_mic_set_" + roomId;
    }

    public void setForbidTime(String roomId, String uid) {
        String key = getForbidKey(roomId, uid);
        try {
            clusterTemplate.opsForValue().set(key, String.valueOf(DateHelper.getNowSeconds() + 60 * 60), ExpireTimeConstant.EXPIRE_HOURS_ONE, TimeUnit.HOURS);
        } catch (Exception e) {
            logger.error("set mic forbid error. roomId={} uid={} {}", roomId, uid, e.getMessage(), e);
        }
    }

    public void removeForbidTime(String roomId, String uid) {
        String key = getForbidKey(roomId, uid);
        try {
            clusterTemplate.delete(key);
        } catch (Exception e) {
            logger.error("remove mic forbid error. roomId={} uid={} {}", roomId, uid, e.getMessage(), e);
        }
    }

    /**
     * 是否禁麦
     */
    public boolean isForbid(String roomId, String uid) {
        int forbidEndTime = getMicForbidTime(roomId, uid);
        if (forbidEndTime <= 0) {
            return false;
        }
        int curTime = DateHelper.getNowSeconds();
        return curTime < forbidEndTime;
    }

    public int getMicForbidTime(String roomId, String uid) {
        try {
            String key = getForbidKey(roomId, uid);
            String value = clusterTemplate.opsForValue().get(key);
            if (StringUtils.isEmpty(value)) {
                return 0;
            }
            return Integer.parseInt(value);
        } catch (Exception e) {
            logger.error("mic forbid error. uid={} roomId={}{}", uid, roomId, e.getMessage(), e);
        }
        return 0;
    }

    public int getForbidTime(String roomId, String uid) {
        try {
            String key = getForbidKey(roomId, uid);
            String value = clusterTemplate.opsForValue().get(key);
            if (StringUtils.isEmpty(value)) {
                return 0;
            }
            int time = Integer.parseInt(value);
            if (time < DateHelper.getNowSeconds()) {
                clusterTemplate.delete(key);
                return 0;
            } else {
                return time;
            }
        } catch (Exception e) {
            logger.error("mic forbid error. uid={} roomId={}{}", uid, roomId, e.getMessage(), e);
        }
        return 0;
    }

    public boolean isAllMicDown(String roomId) {
        try {
            return Boolean.TRUE.equals(clusterTemplate.hasKey(getAllMicDownKey(roomId)));
        } catch (Exception e) {
            logger.error("get is all mic down error. roomId={} msg={}", roomId, e.getMessage());
        }
        return false;
    }

    public void setAllMicDown(String roomId) {
        try {
            clusterTemplate.opsForValue().set(getAllMicDownKey(roomId), String.valueOf(1));
        } catch (Exception e) {
            logger.error("set is all mic down error. roomId={} msg={}", roomId, e.getMessage());
        }
    }

    public void deleteAllMicDown(String roomId) {
        try {
            clusterTemplate.delete(getAllMicDownKey(roomId));
        } catch (Exception e) {
            logger.error("delete is all mic down error. roomId={} msg={}", roomId, e.getMessage());
        }
    }

    private String getForbidKey(String roomId, String uid) {
        return roomId + "_" + uid;
    }

    private String getAllMicDownKey(String roomId) {
        return "all_mic_down_" + roomId;
    }

    /**
     * 获取room模块写入的最新数据，json数据
     */
    public String getRoomMicJsonFromRedis(String roomId) {
        try {
            return clusterTemplate.opsForValue().get(getRoomMicKey(roomId));
        } catch (Exception e) {
            logger.error("getRoomMicJsonFromRedis error. roomId={} msg={}", roomId, e.getMessage());
        }
        return null;
    }

    /**
     * 获取room模块写入的最新数据
     */
    public RoomMicListVo getRoomMicFromRedis(String roomId) {
        try {
            String json = getRoomMicJsonFromRedis(roomId);
            if (StringUtils.isEmpty(json)) {
                return null;
            }
            return JSON.parseObject(json, RoomMicListVo.class);
        } catch (Exception e) {
            logger.error("getRoomMicFromRedis error. roomId={} msg={}", roomId, e.getMessage());
        }
        return null;
    }

    /**
     * 获取room模块写入的最新数据
     */
    public Set<String> getThirdPartMicSet(String roomId) {
        try {
            String json = getRoomMicJsonFromRedis(roomId);
            if (StringUtils.isEmpty(json)) {
                return null;
            }
            Set<String> result = new HashSet<>();
            RoomMicListVo roomMicListVo = JSON.parseObject(json, RoomMicListVo.class);
            for (RoomMicInfoObject micObj : roomMicListVo.getList()) {
                if (null != micObj.getUser() && !StringUtils.isEmpty(micObj.getUser().getThirdPartId())) {
                    result.add(micObj.getUser().getThirdPartId());
                }
            }
            return result;
        } catch (Exception e) {
            logger.error("getThirdPartMicSet error. roomId={} msg={}", roomId, e.getMessage());
        }
        return null;
    }

    /**
     * 获取room模块写入的最新数据
     */
    public Set<String> getRoomMicSetRedis(String roomId) {
        Set<String> result = new HashSet<>();
        try {
            String json = getRoomMicJsonFromRedis(roomId);
            if (StringUtils.isEmpty(json)) {
                return result;
            }
            RoomMicListVo roomMicListVo = JSON.parseObject(json, RoomMicListVo.class);
            for (RoomMicInfoObject micObj : roomMicListVo.getList()) {
                if (null != micObj.getUser() && !StringUtils.isEmpty(micObj.getUser().getAid())) {
                    result.add(micObj.getUser().getAid());
                }
            }
            return result;
        } catch (Exception e) {
            logger.error("getRoomMicSetRedis error. roomId={} msg={}", roomId, e.getMessage());
        }
        return result;
    }

    /**
     * 获取房间麦位列表，按照序号排序
     */
    public List<String> getRoomMicList(String roomId) {
        List<String> result = new ArrayList<>();
        try {
            String json = getRoomMicJsonFromRedis(roomId);
            if (StringUtils.isEmpty(json)) {
                return result;
            }
            List<RoomMicInfoObject> list = JSON.parseObject(json, RoomMicListVo.class).getList();
            list.sort(Comparator.comparing(RoomMicInfoObject::getIndex));
            for (RoomMicInfoObject micObj : list) {
                if (null != micObj.getUser() && !StringUtils.isEmpty(micObj.getUser().getAid())) {
                    result.add(micObj.getUser().getAid());
                }
            }
            return result;
        } catch (Exception e) {
            logger.error("getRoomMicSetRedis error. roomId={} msg={}", roomId, e.getMessage());
        }
        return result;
    }

    /**
     * 获取房间麦位列表，key=aid,value=index
     */
    public Map<String, Integer> getRoomMicMap(String roomId) {
        try {
            String json = getRoomMicJsonFromRedis(roomId);
            if (ObjectUtils.isEmpty(json)) {
                return Collections.emptyMap();
            }
            Map<String, Integer> result = new HashMap<>();
            List<RoomMicInfoObject> list = JSON.parseObject(json, RoomMicListVo.class).getList();
            for (RoomMicInfoObject micObj : list) {
                if (null != micObj.getUser() && !ObjectUtils.isEmpty(micObj.getUser().getAid())) {
                    result.put(micObj.getUser().getAid(), micObj.getIndex());
                }
            }
            return result;
        } catch (Exception e) {
            logger.error("getRoomMicMap error. roomId={} msg={}", roomId, e.getMessage());
            return Collections.emptyMap();
        }
    }

    /**
     * room模块写入的最新麦位数据
     */
    public void saveRoomMicToRedis(String roomId, RoomMicListVo vo) {
        try {
            clusterTemplate.opsForValue().set(getRoomMicKey(roomId), JSON.toJSONString(vo), 3, TimeUnit.HOURS);
        } catch (Exception e) {
            logger.error("save saveRoomMicToRedis error. roomId={} msg={}", roomId, e.getMessage());
        }
    }

    /**
     * 删除麦位缓存
     */
    public void deleteRoomMic(String roomId) {
        try {
            clusterTemplate.delete(getRoomMicKey(roomId));
        } catch (Exception e) {
            logger.error("deleteRoomMic error. roomId={} msg={}", roomId, e.getMessage());
        }
    }

    /**
     * 房间麦位信息
     */
    private String getRoomMicKey(String roomId) {
        return "room_mic_" + roomId;
    }

    /**
     * 房间麦位人数统计信息
     */
    private String getMicActorCountKey() {
        return "hash:micActorCount";
    }

    public void setMicActorCount(String roomId, long count) {
        clusterTemplate.opsForHash().put(getMicActorCountKey(), roomId, String.valueOf(count));
    }

    public int getMicActorCount(String roomId) {
        Object count = clusterTemplate.opsForHash().get(getMicActorCountKey(), roomId);
        return null != count ? Integer.parseInt(count.toString()) : 0;
    }

    public void delMicActorCount(String roomId) {
        clusterTemplate.opsForHash().delete(getMicActorCountKey(), roomId);
    }

    public Map<String, Integer> getMicActorCountMap() {
        Map<String, Integer> result = new HashMap<>();
        Map<Object, Object> entries = clusterTemplate.opsForHash().entries(getMicActorCountKey());
        entries.forEach((k, v) -> result.put(String.valueOf(k), Integer.parseInt(String.valueOf(v))));
        return result;
    }

    /**
     * 增加上麦时长
     *
     * @param hashKey uid_roomId
     */
    public void addMicTime(String hashKey, long addTime) {
        try {
            String dateStr = DateHelper.ARABIAN.formatDateInDay();
            String key = getMicTimeKey(dateStr);
            clusterTemplate.opsForHash().increment(key, hashKey, addTime);
            if (!dateStr.equals(micTimeExpireDate)) {
                clusterTemplate.expire(key, EXPIRE_DAY, TimeUnit.DAYS);
                micTimeExpireDate = dateStr;
            }
            anchorDataStatDao.incAnchorHourUpMicTime(hashKey, addTime);
        } catch (Exception e) {
            logger.error("add mic time error. hashKey={} addTime={}", hashKey, addTime, e);
        }
    }

    /**
     * 获取所有的上麦时长
     */
    @Cacheable(value = "cache", cacheManager = CaffeineCacheConfig.EXPIRE_1M_AFTER_WRITE,
            key = "#root.method.name+T(org.springframework.util.StringUtils).arrayToDelimitedString(args,'-')")
    public Map<String, Integer> getMicTimeMap(String dateStr) {
        try {
            Map<Object, Object> entries = clusterTemplate.opsForHash().entries(getMicTimeKey(dateStr));
            Map<String, Integer> result = new HashMap<>();
            for (Map.Entry<Object, Object> entry : entries.entrySet()) {
                result.put(entry.getKey() + "", Integer.parseInt(String.valueOf(entry.getValue())));
            }
            return result;
        } catch (Exception e) {
            logger.error("gat mic time error.", e);
            return new HashMap<>();
        }
    }

    /**
     * 获取用户今天的上麦时长
     */
    public Map<String, Integer> getUserTodayMicTimeMap(String uid) {
        return getUserMicTimeMap(uid, DateHelper.ARABIAN.formatDateInDay());
    }

    /**
     * 获取用户昨天的上麦时长
     */
    public Map<String, Integer> getUserYesterdayMicTimeMap(String uid) {
        return getUserMicTimeMap(uid, DateHelper.ARABIAN.getYesterdayStr(new Date()));
    }

    private Map<String, Integer> getUserMicTimeMap(String uid, String strDate) {
        try {
            Map<String, Integer> entries = roomMicRedis.getMicTimeMap(strDate);
            Map<String, Integer> result = new HashMap<>();
            for (Map.Entry<String, Integer> entry : entries.entrySet()) {
                String strKey = entry.getKey();
                String[] ids = strKey.split("_");
                String aid = ids[0];
                String roomId = ids[1];
                if (!uid.equals(aid)) {
                    continue;
                }
                result.put(roomId, entry.getValue());
            }
            return result;
        } catch (Exception e) {
            logger.error("gat mic time error.", e);
            return new HashMap<>();
        }
    }

    /**
     * 记录用户当天总上麦时长
     */
    private String getMicTimeKey(String dateStr) {
        return "hash:mic:time:" + dateStr;
    }

    private String getUpMicTypeKey(String uid) {
        return "str:upMicType:" + uid;
    }

    /**
     * 记录上麦方式
     */
    public void setUpMicType(String uid, int upMicType) {
        try {
            clusterTemplate.opsForValue().set(getUpMicTypeKey(uid), upMicType + "", 24, TimeUnit.HOURS);
        } catch (Exception e) {
            logger.error("setUpMicType error.", e);
        }
    }

    public int getUpMicType(String uid) {
        try {
            String value = clusterTemplate.opsForValue().get(getUpMicTypeKey(uid));
            if (StringUtils.isEmpty(value)) {
                return 0;
            }
            return Integer.parseInt(value);
        } catch (Exception e) {
            logger.error("getUpMicType error. uid={} {}", uid, e.getMessage(), e);
            return 0;
        }
    }

    private String getApplyForMicKey(String uid) {
        return "str:applyForMicStatus:" + uid;
    }

    /**
     * 记录申请上麦，埋点用
     */
    public void setApplyForMic(String uid) {
        try {
            clusterTemplate.opsForValue().set(getApplyForMicKey(uid), "true", 12, TimeUnit.HOURS);
        } catch (Exception e) {
            logger.error("setApplyForMic error.", e);
        }
    }

    public boolean isApplyForMic(String uid) {
        try {
            String key = getApplyForMicKey(uid);
            boolean isApplyForMic = "true".equals(clusterTemplate.opsForValue().get(key));
            if (isApplyForMic) {
                clusterTemplate.delete(key);
            }
            return isApplyForMic;
        } catch (Exception e) {
            logger.error("isApplyForMic error. uid={} {}", uid, e.getMessage(), e);
            return false;
        }
    }
}
