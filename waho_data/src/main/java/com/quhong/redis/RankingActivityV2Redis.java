package com.quhong.redis;

import com.baomidou.mybatisplus.core.toolkit.CollectionUtils;
import com.quhong.config.CaffeineCacheConfig;
import com.quhong.constant.ExpireTimeConstant;
import com.quhong.core.date.DateSupport;
import com.quhong.core.utils.DateHelper;
import jakarta.annotation.Resource;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.context.annotation.Lazy;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.data.redis.core.ZSetOperations;
import org.springframework.stereotype.Component;

import java.math.BigDecimal;
import java.util.*;
import java.util.concurrent.TimeUnit;

@Lazy
@Component
public class RankingActivityV2Redis {

    private static final Logger logger = LoggerFactory.getLogger(RankingActivityV2Redis.class);

    private static final int EXTRA_LENGTH = 50;

    @Resource(name = DataRedisBean.MAIN_CLUSTER)
    private StringRedisTemplate clusterTemplate;
    @Resource
    private WhitelistRedis whitelistRedis;


    /**
     * 冲榜活动排行榜
     */
    public void incrRankingScore(String activityId, String aid, int score, int rankingType, int timePeriodType) {
        try {
            String key = getRankingActivityKey(activityId, rankingType, timePeriodType);
            int curScore = getScore(activityId, aid, rankingType, timePeriodType);
            double rankScore = new BigDecimal(curScore + score + "." + (Integer.MAX_VALUE - DateHelper.getNowSeconds())).doubleValue();
            clusterTemplate.opsForZSet().add(key, aid + "", rankScore);
            clusterTemplate.expire(key, 180, TimeUnit.DAYS);
        } catch (Exception e) {
            logger.error("incrRankingScore error activityId={} aid={} score={}", activityId, aid, score, e);
        }
    }

    /**
     * 获取排行榜
     *
     * @param activityId 活动id
     * @param rankingType   排行榜类型
     * @param length     排行榜长度
     */
    public List<String> getRankingList(String activityId, int rankingType, int timePeriodType, int length) {
        List<String> rankingList = new ArrayList<>();
        String key = getRankingActivityKey(activityId, rankingType, timePeriodType);
        Set<ZSetOperations.TypedTuple<String>> rangeWithScores = clusterTemplate.opsForZSet().reverseRangeWithScores(key, 0, length + EXTRA_LENGTH - 1);
        if (null == rangeWithScores) {
            return rankingList;
        }
        Set<String> whaleSet = whitelistRedis.getWhitelistSetFromCache(WhitelistRedis.WHALE_PROTECT_LIST);
        for (ZSetOperations.TypedTuple<String> rangeWithScore : rangeWithScores) {
            if (null == rangeWithScore.getValue() || null == rangeWithScore.getScore()) {
                continue;
            }
            if (!CollectionUtils.isEmpty(whaleSet) && whaleSet.contains(rangeWithScore.getValue())) {
                continue;
            }
            rankingList.add(rangeWithScore.getValue());
            if (rankingList.size() >= length) {
                break;
            }
        }
        return rankingList;
    }

    /**
     * 获取排行榜
     */
    public List<String> getRankingList(String activityId, int rankingType, String strDate, int length) {
        List<String> rankingList = new ArrayList<>();
        String key = getRankingActivityKey(activityId, rankingType, strDate);
        Set<ZSetOperations.TypedTuple<String>> rangeWithScores = clusterTemplate.opsForZSet().reverseRangeWithScores(key, 0, length + EXTRA_LENGTH - 1);
        if (null == rangeWithScores) {
            return rankingList;
        }
        Set<String> whaleSet = whitelistRedis.getWhitelistSetFromCache(WhitelistRedis.WHALE_PROTECT_LIST);
        for (ZSetOperations.TypedTuple<String> rangeWithScore : rangeWithScores) {
            if (null == rangeWithScore.getValue() || null == rangeWithScore.getScore()) {
                continue;
            }
            if (!CollectionUtils.isEmpty(whaleSet) && whaleSet.contains(rangeWithScore.getValue())) {
                continue;
            }
            rankingList.add(rangeWithScore.getValue());
            if (rankingList.size() >= length) {
                break;
            }
        }
        return rankingList;
    }

    /**
     * 获取排行榜
     */
    public Map<String, Integer> getRankingMap(String activityId, int rankingType, String strDate, int length) {
        String key = getRankingActivityKey(activityId, rankingType, strDate);
        return getRankingMap(key, length);
    }

    /**
     * 获取带分数排行榜
     *
     * @param activityId 活动id
     * @param rankingType   排行榜类型
     * @param length     排行榜长度
     */
    public Map<String, Integer> getRankingMap(String activityId, int rankingType, int timePeriodType, int length) {
        String key = getRankingActivityKey(activityId, rankingType, timePeriodType);
        return getRankingMap(key, length);
    }

    /**
     * 获取带分数排行榜
     *
     * @param key        排行榜key
     * @param length     排行榜长度
     */
    public Map<String, Integer> getRankingMap(String key, int length) {
        Map<String, Integer> linkedRankMap = new LinkedHashMap<>();
        Set<ZSetOperations.TypedTuple<String>> rangeWithScores = clusterTemplate.opsForZSet().reverseRangeWithScores(key, 0, length + EXTRA_LENGTH - 1);
        if (null == rangeWithScores) {
            return linkedRankMap;
        }
        Set<String> whaleSet = whitelistRedis.getWhitelistSetFromCache(WhitelistRedis.WHALE_PROTECT_LIST);
        for (ZSetOperations.TypedTuple<String> rangeWithScore : rangeWithScores) {
            if (null == rangeWithScore.getValue() || null == rangeWithScore.getScore()) {
                continue;
            }
            if (!CollectionUtils.isEmpty(whaleSet) && whaleSet.contains(rangeWithScore.getValue())) {
                continue;
            }
            linkedRankMap.put(rangeWithScore.getValue(), rangeWithScore.getScore().intValue());
            if (linkedRankMap.size() >= length) {
                break;
            }
        }
        return linkedRankMap;
    }


    /**
     * 获取排名，返回的数据=redis的排行+1
     */
    public int getRank(String activityId, String aid, int rankingType, int timePeriodType) {
        List<String> rankingList = getRankingList(activityId, rankingType, timePeriodType, 200);
        int rank = 1;
        for (String rankingAid : rankingList) {
            if (rankingAid.equals(aid)) {
                return rank;
            }
            rank++;
        }
        return 0;
    }

    /**
     * 获取分数
     */
    public int getScore(String activityId, String aid, int rankingType, int timePeriodType) {
        try {
            Double score = clusterTemplate.opsForZSet().score(getRankingActivityKey(activityId, rankingType, timePeriodType), aid + "");
            return score != null ? score.intValue() : 0;
        } catch (Exception e) {
            logger.error("getScore error activityId={} aid={} rankingType={}", activityId, aid, rankingType, e);
            return 0;
        }
    }

    public void removeRankingUser(String activityId, int rankingType, int timePeriodType, String aid) {
        try {
            clusterTemplate.opsForZSet().remove(getRankingActivityKey(activityId, rankingType, timePeriodType), aid + "");
        } catch (Exception e) {
            logger.error("removeRankingUser. error activityId={} aid={} rankingType={}", activityId, aid, rankingType, e);
        }
    }

    public void addRankingBlacklist(String activityId, String aid) {
        try {
            String key = getRankingBlacklistKey(activityId);
            clusterTemplate.opsForSet().add(key, aid);
            clusterTemplate.expire(key, ExpireTimeConstant.EXPIRE_DAYS_THIRTY, TimeUnit.DAYS);
        } catch (Exception e) {
            logger.error("addRankingBlacklist error. activityId={} aid={} {}", activityId, aid, e.getMessage(), e);

        }
    }

    @Cacheable(value = "cache", cacheManager = CaffeineCacheConfig.EXPIRE_15S_AFTER_WRITE,
            condition = "T(com.quhong.core.config.ServerConfig).isProduct()",
            key = "#root.method.name+T(org.springframework.util.StringUtils).arrayToDelimitedString(args,'-')")
    public Set<String> getBlacklistUidSet(String activityId) {
        try {
            String key = getRankingBlacklistKey(activityId);
            return clusterTemplate.opsForSet().members(key);
        } catch (Exception e) {
            logger.error("getBlacklistUidList error. activityId={} {}", activityId, e.getMessage(), e);
            return Collections.emptySet();
        }
    }

    public String getStrDate(int type) {
        return switch (type) {
            case 1 -> DateHelper.ARABIAN.formatDateInDay();
            case 2 -> DateSupport.ARABIAN.getStrCurrentWeek();
            case 3 -> DateHelper.ARABIAN.formatDateInMonth();
            default -> "";
        };
    }

    public String getStrDate(int type, long timeMillis) {
        Date date = new Date(timeMillis);
        return switch (type) {
            case 1 -> DateHelper.ARABIAN.formatDateInDay(date);
            case 2 -> DateSupport.ARABIAN.getStrWeek(date);
            case 3 -> DateHelper.ARABIAN.formatDateInMonth(date);
            default -> "";
        };
    }

    public String getYesterdayStrDate(int type) {
        return getStrDate(type, System.currentTimeMillis() - 86400000L);
    }

    public List<String> getStrDateList(int timePeriodType, int startTime, int endTime) {
        switch (timePeriodType) {
            case 1 -> {
                return DateSupport.ARABIAN.getDaysBetween(startTime, endTime);
            }
            case 2 -> {
                return DateSupport.ARABIAN.getWeeksBetween(startTime, endTime);
            }
            case 3 -> {
                List<String> tableSuffixList = DateHelper.ARABIAN.getTableSuffixList(startTime, endTime);
                return tableSuffixList.stream().map(k->k.replace("_", "-")).toList();
            }
            default -> {
                return Collections.singletonList("");
            }
        }
    }

    public void addGotRewardUser(String activityId, String aid, int rankingType, String strDate) {
        try {
            String key = getGotRewardUserKey(activityId, rankingType, strDate);
            clusterTemplate.opsForSet().add(key, aid);
            clusterTemplate.expire(key, 45, TimeUnit.DAYS);
        } catch (Exception e) {
            logger.error("addGotRewardUser error. activityId={} aid={} rankingType={} strDate={} {}", activityId, aid, rankingType, strDate, e.getMessage(), e);
        }
    }

    public Set<String> getGotRewardUserSet(String activityId, int rankingType, String strDate) {
        try {
            String key = getGotRewardUserKey(activityId, rankingType, strDate);
            return clusterTemplate.opsForSet().members(key);
        } catch (Exception e) {
            logger.error("getGotRewardUserSet error. activityId={}rankingType={} strDate={} {}", activityId, rankingType, strDate, e.getMessage(), e);
            return Collections.emptySet();
        }
    }

    private String getRankingBlacklistKey(String activityId) {
        return "set:rankingBlacklist:" + activityId;
    }

    public String getRankingActivityKey(String activityId, int rankingType, int timePeriodType) {
        String strDate = getStrDate(timePeriodType);
        return getRankingActivityKey(activityId, rankingType, strDate);
    }

    public String getRankingActivityKey(String activityId, int rankingType, String strDate) {
        return "zset:activityRanking:" + activityId + ":" + rankingType + ":" + strDate;
    }

    private String getGotRewardUserKey(String activityId, int rankingType, int timePeriodType) {
        String strDate = getStrDate(timePeriodType);
        return getGotRewardUserKey(activityId, rankingType, strDate);
    }

    public String getGotRewardUserKey(String activityId, int rankingType, String strDate) {
        return "set:gotRankingRewardUser:" + activityId + ":" + rankingType + ":" + strDate;
    }
}
