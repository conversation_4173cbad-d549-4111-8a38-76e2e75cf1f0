package com.quhong.redis;

import com.quhong.core.date.DateSupport;
import jakarta.annotation.Resource;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.context.annotation.Lazy;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.stereotype.Component;
import org.springframework.util.ObjectUtils;

import java.util.List;
import java.util.concurrent.TimeUnit;

@Lazy
@Component
public class LuckyLotteryRedis {
    private static final Logger logger = LoggerFactory.getLogger(LuckyLotteryRedis.class);
    private static final int COMMON_EXPIRE_DAYS = 45;
    private static final int FREE_NUM_EXPIRE_DAYS = 45;
    @Resource
    private StringRedisTemplate activityTemplate;


    // 免费抽奖key
    private String getUserFreeKey(String activityId, String uid) {
        return "str:free_draw" + activityId + ":" + DateSupport.ARABIAN.yyyyMMdd() + ":" + uid;
    }

    // 参与人数key
    private String getJoinUserKey(String activityId) {
        return "set:join:" + activityId;
    }

    public int getJoinUserNums(String activityId) {
        try {
            Long joinUserNums = activityTemplate.opsForSet().size(getJoinUserKey(activityId));
            return ObjectUtils.isEmpty(joinUserNums) ? 0 : joinUserNums.intValue();
        } catch (Exception e) {
            logger.info("getJoinUserNums error activityId={}  score={}", activityId, e);
            return 0;
        }
    }

    public void setJoinUserNums(String activityId, String uid) {
        try {

            boolean joinUserFlag = Boolean.TRUE.equals(activityTemplate.hasKey(getJoinUserKey(activityId)));
            activityTemplate.opsForSet().add(getJoinUserKey(activityId), uid);
            if (!joinUserFlag) {
                activityTemplate.expire(getJoinUserKey(activityId), COMMON_EXPIRE_DAYS, TimeUnit.DAYS);
            }

        } catch (Exception e) {
            logger.info("setJoinUserNums error activityId={}  score={}", activityId, e);
        }
    }


    // 用户免费使用数量
    public int getUserFreeNums(String activityId, String uid) {
        try {
            String userFreeKey = getUserFreeKey(activityId, uid);
            String userFreeNums = activityTemplate.opsForValue().get(userFreeKey);
            return ObjectUtils.isEmpty(userFreeNums) ? -1 : Integer.parseInt(userFreeNums);
            // if (userFreeNums == null){
            //     activityTemplate.opsForValue().set(userFreeKey, String.valueOf(FreeNums), 3, TimeUnit.DAYS);
            //     // Long userCurNum = clusterTemplate.opsForValue().increment(userFreeKey, FreeNums);
            //     // clusterTemplate.expire(userFreeKey, 3, TimeUnit.DAYS);
            //     // String userCurNum = clusterTemplate.opsForValue().get(userFreeKey);
            //     return FreeNums;
            // }else {
            //     return Integer.parseInt(userFreeNums);
            // }
        } catch (Exception e) {
            logger.info("getUserFreeNums error activityId={}  score={}", activityId, e);
            return 0;
        }

    }

    public void incUserFreeNums(String activityId, String uid, int num) {
        try {
            activityTemplate.opsForValue().increment(getUserFreeKey(activityId, uid), num);
        } catch (Exception e) {
            logger.info("getUserFreeNums error activityId={}  score={}", activityId, e);
        }

    }

    public void setUserFreeNums(String activityId, String uid, int leftNum) {
        try {
            activityTemplate.opsForValue().set(getUserFreeKey(activityId, uid),
                    String.valueOf(leftNum), COMMON_EXPIRE_DAYS, TimeUnit.DAYS);
        } catch (Exception e) {
            logger.info("setUserFreeNums error activityId={}  score={}", activityId, e);
        }

    }


    // 奖池key
    private String getDrawPoolKey(String activityId) {
        return "list:draw_pool:" + activityId;
    }

    public int getPoolSize(String activityId) {
        try {
            Long poolSize = activityTemplate.opsForList().size(getDrawPoolKey(activityId));
            return poolSize != null ? poolSize.intValue() : 0;
        } catch (Exception e) {
            logger.info("getJoinUserNums error activityId={}  score={}", activityId, e);
            return 0;
        }
    }


    public void initPoolSize(String activityId, List<String> rewardConfigList) {
        try {
            // List<String> poolList = new ArrayList<>();
            // for(LuckyLotteryActivity.RewardConfigDetail rewardConfig:rewardConfigList){
            //     String rewardKey = rewardConfig.getRewardType() + ":" + rewardConfig.getRewardIndex();
            //     for (int i=0; i < rewardConfig.getRewardPercent(); i++){
            //         poolList.add(rewardKey);
            //     }
            // }
            //
            // Collections.shuffle(poolList);
            // RedisSerializer<String> keySerializer = (RedisSerializer<String>) activityTemplate.getKeySerializer();
            // byte[] drawPoolKey = keySerializer.serialize(getDrawPoolKey(activityId));
            //
            // activityTemplate.executePipelined((RedisCallback<Object>) pipeLine -> {
            //     try {
            //         for (String key : poolList) {
            //             pipeLine.rPush(drawPoolKey, keySerializer.serialize(key));
            //         }
            //         pipeLine.expire(drawPoolKey, TimeUnit.DAYS.toSeconds(180));
            //     } catch (Exception e) {
            //         logger.error("initPoolSize activityId={} error={}", activityId,  e);
            //     }
            //     return null;
            // });
            activityTemplate.opsForList().rightPushAll(getDrawPoolKey(activityId), rewardConfigList);
            activityTemplate.expire(getDrawPoolKey(activityId), COMMON_EXPIRE_DAYS, TimeUnit.DAYS);

        } catch (Exception e) {
            logger.info("initPoolSize error activityId={}  score={}", activityId, e);
        }
    }

    public void deletePoolSize(String activityId) {
        try {
            activityTemplate.delete(getDrawPoolKey(activityId));
        } catch (Exception e) {
            logger.info("deletePoolSize error activityId={}  score={}", activityId, e);
        }
    }

    // 抽奖
    public String drawLuckyKey(String activityId, String uid) {
        try {
            String drawLuckyKey = activityTemplate.opsForList().leftPop(getDrawPoolKey(activityId));
            return drawLuckyKey != null ? drawLuckyKey : "";
        } catch (Exception e) {
            logger.info("getJoinUserNums error activityId={}  score={}", activityId, e);
            return "";
        }
    }


    /**
     * 用户总抽奖次数
     */
    private String getUserTotalDrawKey(String activityId, String uid) {
        return "str:user_total_draw:" + activityId + ":" + uid;
    }

    public int getUserTotalDrawNums(String activityId, String uid) {
        try {
            String userTotalDraw = activityTemplate.opsForValue().get(getUserTotalDrawKey(activityId, uid));
            return userTotalDraw != null ? Integer.parseInt(userTotalDraw) : 0;
        } catch (Exception e) {
            logger.info("getUserTotalDrawNums error activityId={}  score={}", activityId, e);
            return 0;
        }
    }

    public void incUserTotalDrawNums(String activityId, String uid) {
        try {
            String userTotalDrawKey = getUserTotalDrawKey(activityId, uid);
            String userTotalDraw = activityTemplate.opsForValue().get(userTotalDrawKey);
            activityTemplate.opsForValue().increment(userTotalDrawKey);
            if (userTotalDraw == null) {
                activityTemplate.expire(userTotalDrawKey, FREE_NUM_EXPIRE_DAYS, TimeUnit.DAYS);
            }

        } catch (Exception e) {
            logger.info("incUserTotalDrawNums error activityId={}  score={}", activityId, e);
        }
    }


    /**
     * 用户每日抽奖次数
     */
    private String getUserDailyDrawKey(String activityId, String uid) {
        return "str:user_daily_draw:" + activityId + ":" + DateSupport.ARABIAN.yyyyMMdd() + ":" + uid;
    }

    public int getUserDailyDrawNums(String activityId, String uid) {
        try {
            String userDailyDraw = activityTemplate.opsForValue().get(getUserDailyDrawKey(activityId, uid));
            return userDailyDraw != null ? Integer.parseInt(userDailyDraw) : 0;
        } catch (Exception e) {
            logger.info("getUserDailyDrawNums error activityId={}  score={}", activityId, e);
            return 0;
        }
    }

    public void incUserDailyDrawNums(String activityId, String uid) {
        try {
            String userDailyDrawKey = getUserDailyDrawKey(activityId, uid);
            String userDailyDraw = activityTemplate.opsForValue().get(userDailyDrawKey);
            activityTemplate.opsForValue().increment(userDailyDrawKey);
            if (userDailyDraw == null) {
                activityTemplate.expire(userDailyDrawKey, FREE_NUM_EXPIRE_DAYS, TimeUnit.DAYS);
            }

        } catch (Exception e) {
            logger.info("incUserDailyDrawNums error activityId={}  score={}", activityId, e);
        }
    }


}
