package com.quhong.redis;

import jakarta.annotation.Resource;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.context.annotation.Lazy;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.data.redis.core.ZSetOperations;
import org.springframework.stereotype.Component;

import java.util.Collections;
import java.util.LinkedHashMap;
import java.util.Map;
import java.util.Set;
import java.util.concurrent.TimeUnit;

@Lazy
@Component
public class RoomEventRedis {

    private static final Logger logger = LoggerFactory.getLogger(RoomEventRedis.class);

    public static final String DIAMOND_RANKING = "diamondRanking"; // 送礼钻石榜
    public static final String CHARM_RANKING = "charmRanking"; // 收礼钻石榜

    @Resource(name = DataRedisBean.MAIN_CLUSTER)
    private StringRedisTemplate redisTemplate;

    public void incRankingScore(String uid, int eventId, String rankingType, int score) {
        try {
            String key = getRankingKey(rankingType, eventId);
            redisTemplate.opsForZSet().incrementScore(key, uid, score);
            redisTemplate.expire(key, 3, TimeUnit.DAYS);
        } catch (Exception e) {
            logger.error("incRankingScore error. rankingType={} eventId={} score={} {}", rankingType, eventId, score, e.getMessage(), e);
        }
    }

    public int getRankingSize(int eventId, String rankingType) {
        try {
            String key = getRankingKey(rankingType, eventId);
            Long size = redisTemplate.opsForZSet().size(key);
            return size != null ? size.intValue() : 0;
        } catch (Exception e) {
            logger.error("getRankingSize error. eventId={} rankingType={} {}", eventId, rankingType, e.getMessage(), e);
            return 0;
        }
    }

    public Map<String, Long> getRankingMap(int eventId, String rankingType, int start, int end) {
        try {
            String key = getRankingKey(rankingType, eventId);
            Map<String, Long> linkedRankMap = new LinkedHashMap<>();
            Set<ZSetOperations.TypedTuple<String>> rangeWithScores = redisTemplate.opsForZSet().reverseRangeWithScores(key, start, end - 1);
            if (null != rangeWithScores) {
                for (ZSetOperations.TypedTuple<String> rangeWithScore : rangeWithScores) {
                    if (null == rangeWithScore.getValue() || null == rangeWithScore.getScore()) {
                        continue;
                    }
                    linkedRankMap.put(rangeWithScore.getValue(), rangeWithScore.getScore().longValue());
                }
            }
            return linkedRankMap;
        } catch (Exception e) {
            logger.error("getRankingMap error. eventId={} rankingType={} start={} end={} {}", eventId, rankingType, start, end, e.getMessage(), e);
            return Collections.emptyMap();
        }
    }

    private String getRankingKey(String rankingType, int eventId) {
        return "zset:roomEvent:" + rankingType + ":" + eventId;
    }

    public int getEnterRoomUserNum(int eventId) {
        try {
            String key = getEnterRoomKey(eventId);
            Long size = redisTemplate.opsForSet().size(key);
            return size != null ? size.intValue() : 0;
        } catch (Exception e) {
            logger.error("getEnterRoomUserNum error. eventId={} {}", eventId, e.getMessage(), e);
            return 0;
        }
    }

    public void addEnterRoomUser(int eventId, String uid) {
        try {
            String key = getEnterRoomKey(eventId);
            redisTemplate.opsForSet().add(key, uid);
            redisTemplate.expire(key, 3, TimeUnit.DAYS);
        } catch (Exception e) {
            logger.error("addEnterRoomUser error. eventId={} uid={} {}", eventId, uid, e.getMessage(), e);
        }
    }

    private String getEnterRoomKey(int eventId) {
        return "set:roomEvent:enterRoomUser:" + eventId;
    }
}
