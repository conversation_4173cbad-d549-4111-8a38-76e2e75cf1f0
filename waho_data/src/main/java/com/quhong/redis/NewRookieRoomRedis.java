package com.quhong.redis;

import com.quhong.cache.CacheMap;
import com.quhong.config.CaffeineCacheConfig;
import com.quhong.utils.ActorUtils;
import jakarta.annotation.PostConstruct;
import jakarta.annotation.Resource;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.context.annotation.Lazy;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import java.util.HashSet;
import java.util.Set;

@Lazy
@Component
public class NewRookieRoomRedis {

    private static final Logger logger = LoggerFactory.getLogger(NewRookieRoomRedis.class);

    private static final long CACHE_TIME_MILLIS = 3 * 60 * 1000L;
    private static final String ALL_ROOKIE_ROOM = "all_rookie_room";

    private final CacheMap<String, Set<String>> cacheMap;

    public NewRookieRoomRedis() {
        cacheMap = new CacheMap<>(CACHE_TIME_MILLIS);
    }

    @PostConstruct
    public void postInit() {
        cacheMap.start();
    }

    @Resource(name = DataRedisBean.MAIN_CLUSTER)
    private StringRedisTemplate redisTemplate;
    @Resource
    private WhitelistRedis whitelistRedis;

    @Cacheable(value = "canSeeRookieRoom", key = "#p0", condition = "T(com.quhong.core.config.ServerConfig).isProduct()",
            cacheManager = CaffeineCacheConfig.EXPIRE_10M_AFTER_WRITE)
    public Boolean canSeeRookieRoom(String uid) {
        // 白名单用户排除以上限制，永久可见
        if (whitelistRedis.inWhitelist(WhitelistRedis.ROOKIE_ROOM_VISIBLE, uid)) {
            return true;
        }
        // 仅面向新设备注册的新用户，且注册30天内可见，超过30天列表不展示
        return ActorUtils.isNewRegisterActor(uid, 30);
    }

    public Set<String> getNewRookieRoom(Set<String> filterRoomIdSet) {
        Set<String> rookieRoomIdSet = getNewRookieRoomByRedis();
        if (CollectionUtils.isEmpty(filterRoomIdSet)) {
            return null;
        }
        Set<String> uidSet = new HashSet<>();
        for (String roomId : rookieRoomIdSet) {
            if (filterRoomIdSet.contains(roomId)) {
                uidSet.add(roomId);
            }
        }
        return uidSet;
    }

    public Set<String> getNewRookieRoomByRedis() {
        Set<String> uidSet = redisTemplate.opsForSet().members(getKey());
        if (!CollectionUtils.isEmpty(uidSet)) {
            return uidSet;
        }
        return new HashSet<>();
    }

    public boolean isNewRookieRoomByRedis(String roomId) {
        return Boolean.TRUE.equals(redisTemplate.opsForSet().isMember(getKey(), roomId));
    }

    public Set<String> getAllNewRookieRoom() {
        try {
            Set<String> rookieRoomIdSet = cacheMap.getData(ALL_ROOKIE_ROOM);
            if (!CollectionUtils.isEmpty(rookieRoomIdSet)) {
                return rookieRoomIdSet;
            }
            rookieRoomIdSet = redisTemplate.opsForSet().members(getKey());
            if (!CollectionUtils.isEmpty(rookieRoomIdSet)) {
                cacheMap.cacheData(ALL_ROOKIE_ROOM, rookieRoomIdSet);
                return rookieRoomIdSet;
            }
        } catch (Exception e) {
            logger.error("get all rookie room error. {}", e.getMessage(), e);
        }
        return new HashSet<>();
    }

    public boolean isNewRookieRoom(String roomId) {
        try {
            return getAllNewRookieRoom().contains(roomId);
        } catch (Exception e) {
            logger.error("get is rookie room error. roomId={} {}", roomId, e.getMessage(), e);
            return false;
        }
    }

    private String getKey() {
        return "op:new:rookie:room";
    }
}
