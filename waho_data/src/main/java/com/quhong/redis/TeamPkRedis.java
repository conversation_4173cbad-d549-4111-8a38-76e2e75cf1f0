package com.quhong.redis;

import com.quhong.constant.AchievementConstant;
import com.quhong.core.utils.DateHelper;
import com.quhong.mongo.dao.TeamPkDao;
import com.quhong.mongo.data.PackConfigData;
import com.quhong.mongo.data.PackData;
import com.quhong.mq.CommonData;
import com.quhong.mq.MqSenderService;
import com.quhong.service.PackService;
import com.quhong.utils.WalletUtils;
import jakarta.annotation.Resource;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.context.annotation.Lazy;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.data.redis.core.ZSetOperations;
import org.springframework.stereotype.Component;

import java.math.BigDecimal;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.concurrent.TimeUnit;

@Lazy
@Component
public class TeamPkRedis {

    private static final Logger logger = LoggerFactory.getLogger(TeamPkRedis.class);

    private static final String TEAM_PK_WAITING_KEY = "zset:teamPkWaiting";
    private static final String TEAM_PK_RUNNING = "zset:teamPkRunning";
    private static final String TEAM_PK_ROOM = "set:teamPkRoom";

    public static final List<Integer> RED_TEAM = List.of(0, 1, 4, 5);
    public static final List<Integer> BLUE_TEAM = List.of(2, 3, 6, 7);

    @Resource(name = DataRedisBean.MAIN_CLUSTER)
    private StringRedisTemplate redisTemplate;
    @Resource
    private PackService packService;
    @Resource
    private MqSenderService mqSenderService;

    /**
     * 获取队伍，1红色方，2蓝色方
     */
    public int getTeam(int micIndex) {
        return RED_TEAM.contains(micIndex) ? 1 : 2;
    }

    public void addTeamPkWaitEndTime(String pkId, String roomId, int endTime) {
        redisTemplate.opsForZSet().add(TEAM_PK_WAITING_KEY, pkId, endTime);
        redisTemplate.opsForValue().set(getTeamPkStatusKey(roomId), pkId, 1, TimeUnit.HOURS);
    }

    public void removeTeamPkWaitEndTime(String pkId) {
        redisTemplate.opsForZSet().remove(TEAM_PK_WAITING_KEY, pkId);
    }

    public Set<String> getWaitEndPkIds(int timestamp) {
        return redisTemplate.opsForZSet().rangeByScore(TEAM_PK_WAITING_KEY, 0, timestamp);
    }

    public void addTeamPkEndTime(String roomId, String pkId, int endTime) {
        redisTemplate.opsForZSet().add(TEAM_PK_RUNNING, pkId, endTime);
        redisTemplate.opsForSet().add(TEAM_PK_ROOM, roomId);
    }

    public void removeTeamPkEndTime(String pkId, String roomId) {
        redisTemplate.opsForZSet().remove(TEAM_PK_RUNNING, pkId);
        redisTemplate.opsForSet().remove(TEAM_PK_ROOM, roomId);
        removeTeamPkId(roomId);
    }

    public Set<String> getTeamPkEndIds(int timestamp) {
        return redisTemplate.opsForZSet().rangeByScore(TEAM_PK_RUNNING, 0, timestamp);
    }

    public Set<String> getTeamPkRoomIds() {
        return redisTemplate.opsForSet().members(TEAM_PK_ROOM);
    }

    /**
     * 获取当前房间有效的teamPk
     */
    public String getTeamPkId(String roomId) {
        return redisTemplate.opsForValue().get(getTeamPkStatusKey(roomId));
    }

    public void removeTeamPkId(String roomId) {
        redisTemplate.delete(getTeamPkStatusKey(roomId));
    }

    private String getTeamPkStatusKey(String roomId) {
        return "str:teamPkStatus:" + roomId;
    }

    /**
     * teamPk阵营分数
     */
    private String getTeamPkScoreKey(String teamPkId, int micIndex) {
        return "str:teamPkScore:" + teamPkId + ":" + (RED_TEAM.contains(micIndex) ? "red" : "blue");
    }

    public void incrTeamPkScore(String teamPkId, int micIndex, int score) {
        String key = getTeamPkScoreKey(teamPkId, micIndex);
        redisTemplate.opsForValue().increment(key, score);
        redisTemplate.expire(key, 2, TimeUnit.DAYS);
    }

    public int getTeamPkScore(String teamPkId, int micIndex) {
        String score = redisTemplate.opsForValue().get(getTeamPkScoreKey(teamPkId, micIndex));
        return score != null ? Integer.parseInt(score) : 0;
    }

    /**
     * teamPk接收分数
     */
    private String getTeamPkContestantKey(String teamPkId, int micIndex) {
        return "zset:teamPkContestant:" + teamPkId + ":" + (RED_TEAM.contains(micIndex) ? "red" : "blue");
    }

    public void incrContestantScore(String teamPkId, String aid, int micIndex, int score) {
        String key = getTeamPkContestantKey(teamPkId, micIndex);
        redisTemplate.opsForZSet().incrementScore(key, aid, score);
        redisTemplate.expire(key, 2, TimeUnit.DAYS);
        incrTeamPkScore(teamPkId, micIndex, score);
    }

    public int getContestantScore(String teamPkId, String aid, int micIndex) {
        Double score = redisTemplate.opsForZSet().score(getTeamPkContestantKey(teamPkId, micIndex), aid);
        return score != null ? score.intValue() : 0;
    }

    public Map<String, Integer> getTopContestantWithScore(String teamPkId, int micIndex, int length) {
        Map<String, Integer> linkedRankMap = new LinkedHashMap<>();
        String key = getTeamPkContestantKey(teamPkId, micIndex);
        Set<ZSetOperations.TypedTuple<String>> rangeWithScores = redisTemplate.opsForZSet().reverseRangeWithScores(key, 0, length - 1);
        if (null == rangeWithScores) {
            return linkedRankMap;
        }
        for (ZSetOperations.TypedTuple<String> rangeWithScore : rangeWithScores) {
            if (null == rangeWithScore.getValue() || null == rangeWithScore.getScore()) {
                continue;
            }
            linkedRankMap.put(rangeWithScore.getValue(), rangeWithScore.getScore().intValue());
        }
        return linkedRankMap;
    }

    /**
     * teamPk发送分数
     */
    private String getTeamPkSupporterKey(String teamPkId, int micIndex) {
        return "zset:teamPkSupporter:" + teamPkId + ":" + (RED_TEAM.contains(micIndex) ? "red" : "blue");
    }

    public synchronized void incrSupporterScore(String teamPkId, String aid, int micIndex, int score) {
        String key = getTeamPkSupporterKey(teamPkId, micIndex);
        int curScore = getSupporterScore(teamPkId, aid, micIndex);
        double rankScore = new BigDecimal(curScore + score + "." + (Integer.MAX_VALUE - DateHelper.getNowSeconds())).doubleValue();
        // 多节点存在并发问题
        redisTemplate.opsForZSet().add(key, aid, rankScore);
        redisTemplate.expire(key, 2, TimeUnit.DAYS);
        sendTeamPkMvpReward(teamPkId, aid, rankScore);
    }

    public int getSupporterScore(String teamPkId, String aid, int micIndex) {
        Double score = redisTemplate.opsForZSet().score(getTeamPkSupporterKey(teamPkId, micIndex), aid);
        return score != null ? score.intValue() : 0;
    }

    public Map<String, Integer> getTopSupporterWithScore(String teamPkId, int micIndex, int length) {
        Map<String, Integer> linkedRankMap = new LinkedHashMap<>();
        String key = getTeamPkSupporterKey(teamPkId, micIndex);
        Set<ZSetOperations.TypedTuple<String>> rangeWithScores = redisTemplate.opsForZSet().reverseRangeWithScores(key, 0, length - 1);
        if (null == rangeWithScores) {
            return linkedRankMap;
        }
        for (ZSetOperations.TypedTuple<String> rangeWithScore : rangeWithScores) {
            if (null == rangeWithScore.getValue() || null == rangeWithScore.getScore()) {
                continue;
            }
            linkedRankMap.put(rangeWithScore.getValue(), rangeWithScore.getScore().intValue());
        }
        return linkedRankMap;
    }

    private String getTeamPkMvpRewardKey(String teamPkId, String uid) {
        return "str:teamPkMvpReward:" + teamPkId + ":" + uid;
    }

    private void sendTeamPkMvpReward(String teamPkId, String uid, double score) {
        if (score < WalletUtils.diamondsToRaw(TeamPkDao.MVP1_SCORE)) {
            return;
        }
        PackConfigData badgePack = packService.getPackConfigData(PackService.TEAM_PK_BADGE_PACK);
        PackConfigData micFramePack = packService.getPackConfigData(PackService.TEAM_PK_MIC_FRAME_PACK);
        if (null == badgePack || null == micFramePack || null == badgePack.getPackList() || null == micFramePack.getPackList()) {
            return;
        }
        int mvpLevel = score >= WalletUtils.diamondsToRaw(TeamPkDao.MVP3_SCORE) ? 3 : score >= WalletUtils.diamondsToRaw(TeamPkDao.MVP2_SCORE) ? 2 : 1;
        String key = getTeamPkMvpRewardKey(teamPkId, uid);
        String lastSendStr = redisTemplate.opsForValue().get(key);
        int lastSendLevel = null == lastSendStr ? 0 : Integer.parseInt(lastSendStr);
        if (lastSendLevel < mvpLevel) {
            List<PackData> badgeReward = badgePack.getPackList();
            List<PackData> micFrameReward = micFramePack.getPackList();
            for (int i = lastSendLevel; i < mvpLevel; i++) {
                if (badgeReward.size() >= i) {
                    packService.sendPackage(uid, badgeReward.get(i), 8);
                }
                if (micFrameReward.size() >= i) {
                    packService.sendPackage(uid, micFrameReward.get(i), 8);
                }
                logger.info("send team pk mvp reward. teamPkId={} uid={} level={}", teamPkId, uid, i);
            }
            if (lastSendLevel == 0) {
                // 用户成就：团战MVP
                mqSenderService.sendUserAchievementToMq(new CommonData(uid, AchievementConstant.TEAM_PK_MVP, 1));
            }
            redisTemplate.opsForValue().set(key, String.valueOf(mvpLevel), 2, TimeUnit.DAYS);
        }
    }
}
