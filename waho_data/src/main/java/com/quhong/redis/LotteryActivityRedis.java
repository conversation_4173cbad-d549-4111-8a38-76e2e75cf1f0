package com.quhong.redis;

import com.quhong.constant.ExpireTimeConstant;
import com.quhong.core.utils.DateHelper;
import jakarta.annotation.Resource;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.context.annotation.Lazy;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.data.redis.core.ZSetOperations;
import org.springframework.stereotype.Component;
import org.springframework.util.StringUtils;

import java.math.BigDecimal;
import java.time.Duration;
import java.util.*;
import java.util.concurrent.TimeUnit;

/**
 * <AUTHOR>
 * @date 2024/5/15
 */
@Lazy
@Component
public class LotteryActivityRedis {

    private static final Logger logger = LoggerFactory.getLogger(LotteryActivityRedis.class);

    @Resource(name = DataRedisBean.MAIN_CLUSTER)
    private StringRedisTemplate clusterTemplate;


    /**
     * 活动排行榜
     */
    public void incrRankingScore(String activityId, String aid, int score, int rankingType) {
        try {
            String key = getRankingActivityKey(activityId, rankingType);
            int curScore = getScore(activityId, aid, rankingType);
            double rankScore = new BigDecimal(curScore + score + "." + (Integer.MAX_VALUE - DateHelper.getNowSeconds())).doubleValue();
            clusterTemplate.opsForZSet().add(key, aid + "", rankScore);
            clusterTemplate.expire(key, 180, TimeUnit.DAYS);
        } catch (Exception e) {
            logger.error("incrRankingScore error activityId={} aid={} score={}", activityId, aid, score, e);
        }
    }

    /**
     * 获取排行榜
     *
     * @param activityId 活动id
     * @param rankingType   排行榜类型
     * @param length     排行榜长度
     */
    public List<String> getRankingList(String activityId, int rankingType, int length) {
        List<String> rankingList = new ArrayList<>();
        String key = getRankingActivityKey(activityId, rankingType);
        Set<ZSetOperations.TypedTuple<String>> rangeWithScores = clusterTemplate.opsForZSet().reverseRangeWithScores(key, 0, length - 1);
        if (null == rangeWithScores) {
            return rankingList;
        }
        for (ZSetOperations.TypedTuple<String> rangeWithScore : rangeWithScores) {
            if (null == rangeWithScore.getValue() || null == rangeWithScore.getScore()) {
                continue;
            }
            rankingList.add(rangeWithScore.getValue());
        }
        return rankingList;
    }

    /**
     * 获取带分数排行榜
     *
     * @param activityId 活动id
     * @param rankingType   排行榜类型
     * @param length     排行榜长度
     */
    public Map<String, Integer> getRankingMap(String activityId, int rankingType, int length) {
        String key = getRankingActivityKey(activityId, rankingType);
        return getRankingMap(key, length);
    }

    /**
     * 获取带分数排行榜
     *
     * @param key        排行榜key
     * @param length     排行榜长度
     */
    public Map<String, Integer> getRankingMap(String key, int length) {
        Map<String, Integer> linkedRankMap = new LinkedHashMap<>();
        Set<ZSetOperations.TypedTuple<String>> rangeWithScores = clusterTemplate.opsForZSet().reverseRangeWithScores(key, 0, length - 1);
        if (null == rangeWithScores) {
            return linkedRankMap;
        }
        for (ZSetOperations.TypedTuple<String> rangeWithScore : rangeWithScores) {
            if (null == rangeWithScore.getValue() || null == rangeWithScore.getScore()) {
                continue;
            }
            linkedRankMap.put(rangeWithScore.getValue(), rangeWithScore.getScore().intValue());
        }
        return linkedRankMap;
    }

    /**
     * 获取排名，返回的数据=redis的排行+1
     */
    public int getRank(String activityId, String aid, int rankingType) {
        try {
            Long rank = clusterTemplate.opsForZSet().reverseRank(getRankingActivityKey(activityId, rankingType), aid + "");
            return rank != null ? rank.intValue() + 1 : 0;
        } catch (Exception e) {
            logger.error("getRank error activityId={} aid={} rankingType={}", activityId, aid, rankingType, e);
            return 0;
        }
    }

    /**
     * 获取分数
     */
    public int getScore(String activityId, String aid, int rankingType) {
        try {
            Double score = clusterTemplate.opsForZSet().score(getRankingActivityKey(activityId, rankingType), aid + "");
            return score != null ? score.intValue() : 0;
        } catch (Exception e) {
            logger.error("getScore error activityId={} aid={} rankingType={}", activityId, aid, rankingType, e);
            return 0;
        }
    }

    public void removeRankingUser(String activityId, int rankingType, String aid) {
        try {
            clusterTemplate.opsForZSet().remove(getRankingActivityKey(activityId, rankingType), aid + "");
        } catch (Exception e) {
            logger.error("removeRankingUser. error activityId={} aid={} rankingType={}", activityId, aid, rankingType, e);
        }
    }

    public void addGotRewardUser(String activityId, String aid, int rankingType) {
        try {
            String key = getGotRewardUserKey(activityId, rankingType);
            clusterTemplate.opsForSet().add(key, aid);
            clusterTemplate.expire(key, 3, TimeUnit.DAYS);
        } catch (Exception e) {
            logger.error("addGotRewardUser error. activityId={} aid={} rankingType={} {}", activityId, aid, rankingType, e.getMessage(), e);
        }
    }

    public Set<String> getGotRewardUserSet(String activityId, int rankingType) {
        try {
            String key = getGotRewardUserKey(activityId, rankingType);
            return clusterTemplate.opsForSet().members(key);
        } catch (Exception e) {
            logger.error("getGotRewardUserSet error. activityId={} rankingType={} {}", activityId, rankingType, e.getMessage(), e);
            return Collections.emptySet();
        }
    }

    public int changeLotteryPoint(String activityId, String uid, int change) {
        String key = getLotteryPointsKey(activityId);
        int balance = clusterTemplate.opsForHash().increment(key, uid, change).intValue();
        clusterTemplate.expire(key, 30, TimeUnit.DAYS);
        return balance;
    }

    public int getLotteryPoint(String activityId, String uid) {
        try {
            String strValue = (String)clusterTemplate.opsForHash().get(getLotteryPointsKey(activityId), uid);
            return StringUtils.hasLength(strValue) ? Integer.parseInt(strValue) : 0;
        } catch (Exception e) {
            logger.error("getLotteryPoint error. activityId={} uid={} {}", activityId, uid, e.getMessage(), e);
            return 0;
        }
    }

    public int getPoolSize(String activityId) {
        try {
            Long poolSize = clusterTemplate.opsForList().size(getLotteryPoolKey(activityId));
            return poolSize != null ? poolSize.intValue() : 0;
        } catch (Exception e) {
            logger.error("getPoolSize error. activityId={} {}", activityId, e.getMessage(), e);
            return 0;
        }
    }

    public void pushRewardInPool(String activityId, List<String> rewardList) {
        String key = getLotteryPoolKey(activityId);
        try {
            clusterTemplate.opsForList().rightPushAll(key, rewardList);
            clusterTemplate.expire(key, 30, TimeUnit.DAYS);
        } catch (Exception e) {
            logger.error("pushRewardInPool error. activityId={} {}", activityId, e.getMessage(), e);
        }
    }

    public List<String> popRewardFromPool(String activityId, int num) {
        String key = getLotteryPoolKey(activityId);
        try {
            return clusterTemplate.opsForList().leftPop(key, num);
        } catch (Exception e) {
            logger.error("popRewardFromPool error. activityId={} {}", activityId, e.getMessage(), e);
            return Collections.emptyList();
        }
    }

    public void addAwardNotify(String activityId, int slang, String strNotify) {
        String key = getAwardNotifyKey(activityId, slang);
        try {
            Long listSize = clusterTemplate.opsForList().leftPush(key, strNotify);
            clusterTemplate.expire(key, ExpireTimeConstant.EXPIRE_DAYS_THIRTY, TimeUnit.DAYS);
            if (null != listSize && listSize > 100) {
                clusterTemplate.opsForList().trim(key, 0, 50);
            }
        } catch (Exception e) {
            logger.error("addAwardNotify error. activityId={} strNotify={} {}", activityId, strNotify, e.getMessage(), e);
        }
    }

    public List<String> getAwardNotifyList(String activityId, int slang) {
        try {
            String key = getAwardNotifyKey(activityId, slang);
            return clusterTemplate.opsForList().range(key, 0, 49);
        } catch (Exception e) {
            logger.error("getAwardNotifyList error. activityId={}", activityId, e);
            return Collections.emptyList();
        }
    }

    public String getRankingActivityKey(String activityId, int rankingType) {
        return "zset:lotteryActivityRanking:" + activityId + ":" + rankingType;
    }

    private String getGotRewardUserKey(String activityId, int rankingType) {
        return "set:gotRankingRewardUser:" + activityId + ":" + rankingType;
    }

    private String getLotteryPointsKey(String activityId) {
        return "hash:LotteryActivityPoint:" + activityId;
    }

    private String getLotteryPoolKey(String activityId) {
        return "list:lotteryPool_" + activityId;
    }

    private String getAwardNotifyKey(String activityId, int slang) {
        return "list:activityAwardNotify_" + activityId + "_" + slang;
    }
}
