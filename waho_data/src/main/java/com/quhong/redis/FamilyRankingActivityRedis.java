package com.quhong.redis;

import com.quhong.core.utils.DateHelper;
import jakarta.annotation.Resource;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.context.annotation.Lazy;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.data.redis.core.ZSetOperations;
import org.springframework.stereotype.Component;

import java.math.BigDecimal;
import java.util.*;
import java.util.concurrent.TimeUnit;

@Lazy
@Component
public class FamilyRankingActivityRedis {

    private static final Logger logger = LoggerFactory.getLogger(FamilyRankingActivityRedis.class);

    @Resource(name = DataRedisBean.MAIN_CLUSTER)
    private StringRedisTemplate clusterTemplate;
    private String rankingExpireDate = "";

    /**
     * 冲榜活动排行榜
     */
    public void incrRankingScore(String activityId, int familyId, int giftId, int score, int rankType, int rankGiftId) {
        try {
            String key = getRankingActivityKey(activityId, rankType, rankGiftId);
            int curScore = getScore(activityId, familyId, rankType, rankGiftId);
            double rankScore = new BigDecimal(curScore + score + "." + (Integer.MAX_VALUE - DateHelper.getNowSeconds())).doubleValue();
            clusterTemplate.opsForZSet().add(key, familyId + "", rankScore);
            String dateStr = DateHelper.ARABIAN.formatDateInDay();
            if (!dateStr.equals(rankingExpireDate)) {
                clusterTemplate.expire(key, 180, TimeUnit.DAYS);
                rankingExpireDate = dateStr;
            }
        } catch (Exception e) {
            logger.error("incrRankingScore error activityId={} familyId={} giftId={} score={}", activityId, familyId, giftId, score, e);
        }
    }

    /**
     * 获取排行榜
     *
     * @param activityId 活动id
     * @param rankType   排行榜类型
     * @param rankGiftId 指定礼物的排行榜
     * @param length     排行榜长度
     */
    public List<Integer> getRankingList(String activityId, int rankType, int rankGiftId, int length) {
        List<Integer> rankingList = new ArrayList<>();
        String key = getRankingActivityKey(activityId, rankType, rankGiftId);
        Set<ZSetOperations.TypedTuple<String>> rangeWithScores = clusterTemplate.opsForZSet().reverseRangeWithScores(key, 0, length - 1);
        if (null == rangeWithScores) {
            return rankingList;
        }
        for (ZSetOperations.TypedTuple<String> rangeWithScore : rangeWithScores) {
            if (null == rangeWithScore.getValue() || null == rangeWithScore.getScore()) {
                continue;
            }
            rankingList.add(Integer.parseInt(rangeWithScore.getValue()));
        }
        return rankingList;
    }

    /**
     * 获取带分数排行榜
     *
     * @param activityId 活动id
     * @param rankType   排行榜类型
     * @param rankGiftId 指定礼物的排行榜
     * @param length     排行榜长度
     */
    public Map<String, Integer> getRankingMap(String activityId, int rankType, int rankGiftId, int length) {
        Map<String, Integer> linkedRankMap = new LinkedHashMap<>();
        String key = getRankingActivityKey(activityId, rankType, rankGiftId);
        Set<ZSetOperations.TypedTuple<String>> rangeWithScores = clusterTemplate.opsForZSet().reverseRangeWithScores(key, 0, length - 1);
        if (null == rangeWithScores) {
            return linkedRankMap;
        }
        for (ZSetOperations.TypedTuple<String> rangeWithScore : rangeWithScores) {
            if (null == rangeWithScore.getValue() || null == rangeWithScore.getScore()) {
                continue;
            }
            linkedRankMap.put(rangeWithScore.getValue(), rangeWithScore.getScore().intValue());
        }
        return linkedRankMap;
    }

    /**
     * 获取排名，返回的数据=redis的排行+1
     */
    public int getRank(String activityId, int familyId, int rankType, int rankGiftId) {
        try {
            Long rank = clusterTemplate.opsForZSet().reverseRank(getRankingActivityKey(activityId, rankType, rankGiftId), familyId + "");
            return rank != null ? rank.intValue() + 1 : 0;
        } catch (Exception e) {
            logger.error("getRank error activityId={} familyId={} rankType={}", activityId, familyId, rankType, e);
            return 0;
        }
    }

    /**
     * 获取分数
     */
    public int getScore(String activityId, int familyId, int rankType, int rankGiftId) {
        try {
            Double score = clusterTemplate.opsForZSet().score(getRankingActivityKey(activityId, rankType, rankGiftId), familyId + "");
            return score != null ? score.intValue() : 0;
        } catch (Exception e) {
            logger.error("getScore error activityId={} familyId={} rankType={}", activityId, familyId, rankType, e);
            return 0;
        }
    }



    public void incrRankingInFamilyScore(String activityId, int familyId, String aid, int giftId, int score, int rankType, int rankGiftId) {
        try {
            String key = getRankingInFamilyKey(activityId, familyId, rankType, rankGiftId);
            int curScore = getRankingInFamilyScore(activityId, familyId, aid, rankType, rankGiftId);
            double rankScore = new BigDecimal(curScore + score + "." + (Integer.MAX_VALUE - DateHelper.getNowSeconds())).doubleValue();
            clusterTemplate.opsForZSet().add(key, aid, rankScore);
            clusterTemplate.expire(key, 180, TimeUnit.DAYS);
        } catch (Exception e) {
            logger.error("incrRankingInFamilyScore error activityId={} familyId={} giftId={} score={}", activityId, familyId, giftId, score, e);
        }
    }

    public int getRankingInFamilyScore(String activityId, int familyId, String aid, int rankType, int rankGiftId) {
        try {
            Double score = clusterTemplate.opsForZSet().score(getRankingInFamilyKey(activityId, familyId, rankType, rankGiftId), aid);
            return score != null ? score.intValue() : 0;
        } catch (Exception e) {
            logger.error("getRankingInFamilyScore error activityId={} familyId={} aid={} rankType={}", activityId, familyId, aid, rankType, e);
            return 0;
        }
    }

    /**
     * 获取排行榜
     *
     * @param activityId 活动id
     * @param familyId 家族id
     * @param rankType   排行榜类型
     * @param rankGiftId 指定礼物的排行榜
     * @param length     排行榜长度
     */
    public List<String> getRankingInFamilyList(String activityId, int familyId, int rankType, int rankGiftId, int length) {
        List<String> rankingList = new ArrayList<>();
        String key = getRankingInFamilyKey(activityId, familyId, rankType, rankGiftId);
        Set<ZSetOperations.TypedTuple<String>> rangeWithScores = clusterTemplate.opsForZSet().reverseRangeWithScores(key, 0, length - 1);
        if (null == rangeWithScores) {
            return rankingList;
        }
        for (ZSetOperations.TypedTuple<String> rangeWithScore : rangeWithScores) {
            if (null == rangeWithScore.getValue() || null == rangeWithScore.getScore()) {
                continue;
            }
            rankingList.add(rangeWithScore.getValue());
        }
        return rankingList;
    }

    /**
     * 获取带分数排行榜
     *
     * @param activityId 活动id
     * @param familyId 家族id
     * @param rankType   排行榜类型
     * @param rankGiftId 指定礼物的排行榜
     * @param length     排行榜长度
     */
    public Map<String, Integer> getRankingInFamilyMap(String activityId, int familyId, int rankType, int rankGiftId, int length) {
        Map<String, Integer> linkedRankMap = new LinkedHashMap<>();
        String key = getRankingInFamilyKey(activityId, familyId, rankType, rankGiftId);
        Set<ZSetOperations.TypedTuple<String>> rangeWithScores = clusterTemplate.opsForZSet().reverseRangeWithScores(key, 0, length - 1);
        if (null == rangeWithScores) {
            return linkedRankMap;
        }
        for (ZSetOperations.TypedTuple<String> rangeWithScore : rangeWithScores) {
            if (null == rangeWithScore.getValue() || null == rangeWithScore.getScore()) {
                continue;
            }
            linkedRankMap.put(rangeWithScore.getValue(), rangeWithScore.getScore().intValue());
        }
        return linkedRankMap;
    }

    /**
     * 获取排名，返回的数据=redis的排行+1
     */
    public int getRankingInFamilyRank(String activityId, int familyId, String aid, int rankType, int rankGiftId) {
        try {
            Long rank = clusterTemplate.opsForZSet().reverseRank(getRankingInFamilyKey(activityId, familyId, rankType, rankGiftId), aid);
            return rank != null ? rank.intValue() + 1 : 0;
        } catch (Exception e) {
            logger.error("getRankingInFamilyRank error activityId={} familyId={} aid={} rankType={}", activityId, familyId, aid, rankType, e);
            return 0;
        }
    }


    private String getRankingActivityKey(String activityId, int rankType, int rankGiftId) {
        return "zset:familyRankingActivity:" + activityId + ":" + rankType + (0 == rankGiftId ? "" : ":" + rankGiftId);
    }

    private String getRankingInFamilyKey(String activityId, int familyId, int rankType, int rankGiftId) {
        return "zset:rankingInFamily:" + activityId + ":" + familyId + ":" + rankType + (0 == rankGiftId ? "" : ":" + rankGiftId);
    }
}
