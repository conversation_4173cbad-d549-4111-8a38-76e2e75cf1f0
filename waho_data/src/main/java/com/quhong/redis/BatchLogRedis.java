package com.quhong.redis;

import com.alibaba.fastjson.JSONObject;
import com.quhong.core.utils.DateHelper;
import com.quhong.monitor.MonitorSender;
import jakarta.annotation.Resource;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.stereotype.Component;
import org.springframework.util.ObjectUtils;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;

@Component
public class BatchLogRedis {
    private final static Logger logger = LoggerFactory.getLogger(BatchLogRedis.class);
    public static final String BATCH_MONEY_DETAIL = "list:batch_money_detail";
    public static final String BATCH_CHARM_LOG = "list:batch_charm_log";

    private static final int BATCH_SIZE = 2000;
    private int lastWarnTime = 0;

    @Resource(name = DataRedisBean.MAIN_CLUSTER)
    private StringRedisTemplate redisTemplate;
    @Resource
    private MonitorSender monitorSender;


    /**
     * 批量入库，先入先出
     */
    public void pushBatchLog(String key, String data) {
        try {
            Long length = redisTemplate.opsForList().rightPush(key, data);
            if (null != length && length > 3000000) {
                int nowSeconds = DateHelper.getNowSeconds();
                if (lastWarnTime < nowSeconds - 30 * 60) {
                    logger.error("backlog key={} length={}", key, length);
                    lastWarnTime = nowSeconds;
                    monitorSender.info("waho_java_exception", "批量入库Redis缓存过大", "key=" + key + ", length=" + length);
                }
            }
        } catch (Exception e) {
            logger.error("push batch log error. key={} data={} {}", key, data, e.getMessage());
        }
    }

    public <T> List<T> getBatchList(String key, Class<T> clazz) {
        List<String> list = redisTemplate.opsForList().range(key, 0, BATCH_SIZE - 1);
        if (ObjectUtils.isEmpty(list)) {
            return Collections.emptyList();
        }
        List<T> detailList = new ArrayList<>(list.size());
        for (String json : list) {
            try {
                detailList.add(JSONObject.parseObject(json, clazz));
            } catch (Exception e) {
                logger.error("parse json error. key={} data={} {}", key, json, e.getMessage());
            }
        }
        return detailList;
    }

    public void removeProcessedData(String key, int batchCount) {
        redisTemplate.opsForList().trim(key, batchCount, -1);
    }
}
