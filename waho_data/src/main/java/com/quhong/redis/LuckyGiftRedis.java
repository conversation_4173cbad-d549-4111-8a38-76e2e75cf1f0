package com.quhong.redis;

import com.alibaba.fastjson.JSON;
import com.quhong.utils.WalletUtils;
import jakarta.annotation.Resource;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.context.annotation.Lazy;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.concurrent.TimeUnit;

@Lazy
@Component
public class LuckyGiftRedis {
    private static final Logger logger = LoggerFactory.getLogger(LuckyGiftRedis.class);

    @Resource(name = DataRedisBean.MAIN_CLUSTER)
    private StringRedisTemplate clusterRedis;

    /**
     * @param jackpotType 1不参与结算的旧版幸运礼物，2新版10%结算新幸运礼物
     */
    private String getLuckyGiftJackpotKey(int jackpotType, int poolType) {
        if (1 == jackpotType) {
            return "str:luckyGiftJackpot:" + poolType;
        } else {
            return "str:luckyGiftJackpot:" + jackpotType + ":" + poolType;
        }
    }

    /**
     * 增加奖池
     *
     * @param prize       增加数额
     * @param jackpotType 1不参与结算的旧版幸运礼物，2新版10%结算新幸运礼物
     * @param poolType    1钻石奖池 2钻石jackpot小奖池
     */
    public double incrJackpot(double prize, int jackpotType, int poolType) {
        try {
            // logger.info("incrJackpot prize={} poolType={}", prize, poolType);
            Double value = clusterRedis.opsForValue().increment(getLuckyGiftJackpotKey(jackpotType, poolType), prize);
            return null == value ? 0 : value;
        } catch (Exception e) {
            logger.error("incrJackpot error. prize={} {}", prize, e.getMessage());
            return 0;
        }
    }

    public int getJackpot(int jackpotType, int poolType) {
        try {
            String strValue = clusterRedis.opsForValue().get(getLuckyGiftJackpotKey(jackpotType, poolType));
            return null == strValue ? 0 : Double.valueOf(strValue).intValue();
        } catch (Exception e) {
            logger.error("getJackpot error. {}", e.getMessage(), e);
            return 0;
        }
    }

    /**
     * @param jackpotType 1不参与结算的旧版幸运礼物，2新版10%结算新幸运礼物
     */
    public int getAllJackpot(int jackpotType) {
        // 目前还没有活动钻
        return WalletUtils.diamondsForDisplay(Math.max(getJackpot(jackpotType, 2) + 1234560, 0));
    }

    /**
     * 幸运礼物抽奖
     *
     * @param jackpotType jackpotType=1时，giftId=0，
     *                    jackpotType>1时，giftId=-jackpotType
     */
    public int jackpotTypeToGiftId(int jackpotType) {
        return jackpotType == 1 ? 0 : -jackpotType;
    }

    /**
     * 幸运礼物抽奖
     *
     * @param giftId 礼物id，奖池类悉尼关于礼物为0，共用一个奖池
     *               jackpotType=1时，giftId=0，
     *               jackpotType>1时，giftId=-jackpotType
     */
    public int incrPoolIndex(int giftId) {
        try {
            // logger.info("incrPoolIndex giftId={}", giftId);
            Long value = clusterRedis.opsForValue().increment(getLuckyGiftPoolIndexKey(giftId));
            return null == value ? 0 : value.intValue();
        } catch (Exception e) {
            logger.error("incrPoolIndex error.giftId={} {}", giftId, e.getMessage());
            return 0;
        }
    }

    public void setPoolIndex(int giftId, int poolIndex) {
        try {
            logger.info("setPoolIndex giftId={}", giftId);
            clusterRedis.opsForValue().set(getLuckyGiftPoolIndexKey(giftId), String.valueOf(poolIndex));
        } catch (Exception e) {
            logger.error("setPoolIndex error. giftId={} {}", giftId, e.getMessage());
        }
    }

    public int getPoolIndex(int giftId) {
        try {
            logger.info("getPoolIndex giftId={}", giftId);
            String value = clusterRedis.opsForValue().get(getLuckyGiftPoolIndexKey(giftId));
            return null == value ? 0 : Integer.parseInt(value);
        } catch (Exception e) {
            logger.error("getPoolIndex error. giftId={} {}", giftId, e.getMessage());
            return 0;
        }
    }

    public void clearLuckyGiftPool(int giftId) {
        try {
            clusterRedis.delete(getLuckyGiftPoolIndexKey(giftId));
            clusterRedis.delete(getLuckyGiftPrizeKey(giftId));
        } catch (Exception e) {
            logger.error("deletePoolSize error={}", e.getMessage());
        }
    }

    private String getLuckyGiftPoolIndexKey(int giftId) {
        return "str:luckyGiftPoolIndex:" + giftId;
    }

    public String getLottery(int giftId, int poolIndex) {
        try {
            Object o = clusterRedis.opsForHash().get(getLuckyGiftPrizeKey(giftId), String.valueOf(poolIndex));
            if (null == o) {
                return null;
            }
            return String.valueOf(o);
        } catch (Exception e) {
            logger.error("getLottery error poolIndex={} {}", poolIndex, e.getMessage(), e);
            return null;
        }
    }

    public void saveLuckyGiftPrizeMap(int giftId, Map<String, String> lotteryMap) {
        try {
            clusterRedis.opsForHash().putAll(getLuckyGiftPrizeKey(giftId), lotteryMap);
        } catch (Exception e) {
            logger.error("saveLuckyGiftPrizeMap error giftId={} {}", giftId, e.getMessage(), e);
        }
    }

    public void clearLuckyGiftPrizeMap(int giftId) {
        try {
            logger.info("clearLuckyGiftPrizeMap giftId={}", giftId);
            clusterRedis.delete(getLuckyGiftPrizeKey(giftId));
        } catch (Exception e) {
            logger.error("clearLuckyGiftPrizeMap error giftId={} {}", giftId, e.getMessage(), e);
        }
    }

    private String getLuckyGiftPrizeKey(int giftId) {
        return "hash:luckyGiftPrize:" + giftId;
    }

    public void addLuckyUser(List<String> rewardList, int giftId) {
        try {
            String key = getLuckyUserKey(giftId);
            Long length = clusterRedis.opsForList().leftPushAll(key, rewardList);
            if (null != length && length >= 50) {
                clusterRedis.expire(key, 180, TimeUnit.DAYS);
                clusterRedis.opsForList().trim(key, 0, 30);
            }
        } catch (Exception e) {
            logger.error("addLuckyUser error rewardList={} giftId={}", rewardList, giftId, e);
        }
    }

    /**
     * 获取最近20个最近中奖的用户
     */
    public List<LuckyUserInfo> getLuckyUser(int giftId) {
        try {
            List<String> members = clusterRedis.opsForList().range(getLuckyUserKey(giftId), 0, 20);
            if (null == members) {
                return Collections.emptyList();
            }
            List<LuckyUserInfo> resultList = new ArrayList<>(members.size());
            for (String json : members) {
                resultList.add(JSON.parseObject(json, LuckyUserInfo.class));
            }
            return resultList;
        } catch (Exception e) {
            logger.error("getLuckyUser error giftId={}", giftId, e);
            return Collections.emptyList();
        }
    }

    private String getLuckyUserKey(int giftId) {
        return "list:luckyUser:" + giftId;
    }

    public static class LuckyUserInfo {
        private String name; // 中奖名字
        private String head; // 中奖头像
        private String rewardName; // 中奖信息
        private String rewardNameAr; // 中奖信息

        public String buildJson(String name, String head, String rewardName, String rewardNameAr) {
            this.name = name;
            this.head = head;
            this.rewardName = rewardName;
            this.rewardNameAr = rewardNameAr;
            return JSON.toJSONString(this);
        }

        public String getName() {
            return name;
        }

        public void setName(String name) {
            this.name = name;
        }

        public String getHead() {
            return head;
        }

        public void setHead(String head) {
            this.head = head;
        }

        public String getRewardName() {
            return rewardName;
        }

        public void setRewardName(String rewardName) {
            this.rewardName = rewardName;
        }

        public String getRewardNameAr() {
            return rewardNameAr;
        }

        public void setRewardNameAr(String rewardNameAr) {
            this.rewardNameAr = rewardNameAr;
        }
    }
}
