package com.quhong.redis;

import com.quhong.config.CaffeineCacheConfig;
import com.quhong.core.config.ServerConfig;
import com.quhong.core.date.DateSupport;
import com.quhong.core.utils.DateHelper;
import jakarta.annotation.Resource;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.context.annotation.Lazy;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import java.util.List;
import java.util.concurrent.TimeUnit;

@Lazy
@Component
public class ThirdGameDauUserRedis {
    private static final Logger logger = LoggerFactory.getLogger(ThirdGameDauUserRedis.class);
    private static final int EXPIRE_DAY = 20;
    private String expireDate = "";

    @Resource(name = DataRedisBean.MAIN_CLUSTER)
    private StringRedisTemplate redisTemplate;

    /**
     * 记录用户在特定游戏的下注行为
     *
     * @param uid      用户ID
     * @param gameType 游戏类型
     */
    @Cacheable(value = "cache", cacheManager = CaffeineCacheConfig.EXPIRE_10M_AFTER_WRITE,
            unless = "#result==false",
            key = "#root.method.name+T(org.springframework.util.StringUtils).arrayToDelimitedString(args,'-')")
    public boolean recordPlayerBet(String uid, String gameType) {
        try {
            String dateStr = DateHelper.DEFAULT.formatDateInDay();
            String key = getKey(dateStr);
            // 使用Hash记录用户今日下注状态
            redisTemplate.opsForHash().put(key, uid + gameType, "1");
            if (!dateStr.equals(expireDate)) {
                redisTemplate.expire(key, EXPIRE_DAY, TimeUnit.DAYS);
                expireDate = dateStr;
            }
            return true;
        } catch (Exception e) {
            logger.error("Record player bet error. uid={}, gameType={}, error={}", uid, gameType, e.getMessage(), e);
            return false;
        }
    }

    public boolean isActivePlayer(String strDate, String aid, String gameType) {
        try {
            return redisTemplate.opsForHash().hasKey(getKey(strDate), aid + gameType);
        } catch (Exception e) {
            logger.error("isActivePlayer error. strDate={} {}", strDate, e.getMessage(), e);
            return false;
        }
    }

    @Cacheable(value = "cache", cacheManager = CaffeineCacheConfig.EXPIRE_10M_AFTER_WRITE,
            condition = "T(com.quhong.core.config.ServerConfig).isProduct()",
            key = "#root.method.name+T(org.springframework.util.StringUtils).arrayToDelimitedString(args,'-')")
    public boolean isContinuedNotActivePlayer(String aid, String gameType, int days) {
        int nowTime = DateHelper.getNowSeconds();
        List<String> strDateList = DateSupport.ARABIAN.getDaysBetween(nowTime - (int) TimeUnit.DAYS.toSeconds(days), nowTime);
        if (!CollectionUtils.isEmpty(strDateList)) {
            for (String strDate : strDateList) {
                // 2025-08-04 16:52:53
                if (ServerConfig.isProduct() && DateHelper.getNowSeconds() < 1754297573) {
                    // 数据还不存在，先不判断
                    if (!redisTemplate.hasKey(getKey(strDate))) {
                        return false;
                    }
                }
                if (isActivePlayer(strDate, aid, gameType)) {
                    return false;
                }
            }
        }
        return true;
    }

    private String getKey(String dateStr) {
        return "hash:thirdGameDauUser:" + dateStr;
    }
}
