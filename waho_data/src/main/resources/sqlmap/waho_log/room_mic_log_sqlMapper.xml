<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.quhong.mysql.mapper.waho_log.RoomMicLogMapper">
    <resultMap id="baseResultMap" type="com.quhong.mysql.data.RoomMicLogData">
        <result column="id" property="id"></result>
        <result column="room_id" property="roomId"></result>
        <result column="user_id" property="userId"></result>
        <result column="mic_position" property="micPosition"></result>
        <result column="mic_time" property="micTime"></result>
        <result column="os" property="os"></result>
        <result column="rookie_status" property="rookieStatus"></result>
        <result column="version_code" property="versionCode"></result>
        <result column="ctime" property="ctime"></result>
        <result column="mtime" property="mtime"></result>
    </resultMap>
    <sql id="baseSql">
        room_id,user_id,mic_position,mic_time,os,rookie_status,version_code,ctime,mtime
    </sql>
    <sql id="itemSql">
        #{item.roomId},#{item.userId},#{item.micPosition},#{item.micTime},#{item.os},#{item.rookieStatus},#{item.versionCode},#{item.ctime},#{item.mtime}
    </sql>
    <insert id="insert" useGeneratedKeys="true" keyProperty="item.id" keyColumn="id">
        insert ignore into s_room_mic_${tableSuffix} (<include refid="baseSql"/>) values (<include refid="itemSql"/>)
    </insert>

    <select id="actorAddUpMicTime" resultType="Integer" parameterType="String">
        SELECT SUM(mic_time) AS num
        FROM s_room_mic_${tableSuffix}
        WHERE user_id = #{userId}
          AND <![CDATA[ ctime >= #{startTime} AND ctime <= #{endTime} ]]>
    </select>

    <select id="actorAddUpMicTimeMap" resultType="com.quhong.mysql.data.RoomMicLogData">
        SELECT user_id, IFNULL(SUM(mic_time),0) AS mic_time
        FROM s_room_mic_${tableSuffix}
        WHERE user_id in
        <foreach item="item" collection="uidSet" open="(" separator="," close=")">
            #{item}
        </foreach>
          AND <![CDATA[ ctime >= #{startTime} AND ctime <= #{endTime} ]]>
        GROUP BY user_id
    </select>

    <select id="getUpMicTotalTime" resultType="java.lang.Integer">
        SELECT SUM(mic_time) AS num
        FROM s_room_mic_${tableSuffix}
        WHERE room_id = #{roomId} AND user_id = #{uid}
          AND <![CDATA[ ctime >= #{startTime} AND ctime <= #{endTime} ]]>
    </select>

    <select id="getUpMicUserNum" resultType="java.lang.Integer">
        SELECT count(*) from (SELECT DISTINCT user_id from (
        <foreach collection='tableSuffixList' item='tableSuffix' separator='UNION ALL'>
            SELECT user_id
            FROM s_room_mic_${tableSuffix}
            WHERE room_id = #{roomId} AND <![CDATA[ ctime >= #{startTime} AND ctime <= #{endTime} ]]> group by user_id
        </foreach>
        ) as stat ) as stat
    </select>
</mapper>
