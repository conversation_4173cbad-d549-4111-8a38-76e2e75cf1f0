<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.quhong.mysql.mapper.waho.RoomMicInfoMapper">
    <resultMap id="baseResultMap" type="com.quhong.mysql.data.RoomMicData">
        <result column="id" property="id"></result>
        <result column="roomId" property="room_id"></result>
        <result column="position" property="position"></result>
        <result column="mic_lock" property="micLock"></result>
        <result column="mute" property="mute"></result>
        <result column="uid" property="uid"></result>
        <result column="fb_gender" property="fbGender"></result>
        <result column="country" property="country"></result>
        <result column="ctime" property="ctime"></result>
        <result column="mtime" property="mtime"></result>
        <result column="up_mic_time" property="upMicTime"></result>
        <result column="third_part_id" property="thirdPartId"></result>
        <result column="mute_time" property="muteTime"></result>
        <result column="mic_type" property="micType"></result>
    </resultMap>
    <sql id="baseSql">
        room_id,position,mic_lock,mute,uid,fb_gender,country,ctime,mtime,up_mic_time,third_part_id,mute_time,mic_type
    </sql>
    <sql id="itemSql">
        #{item.roomId},#{item.position},#{item.micLock},#{item.mute},#{item.uid},#{item.fbGender},#{item.country},#{item.ctime},#{item.mtime},#{item.upMicTime},#{item.thirdPartId},#{item.muteTime},#{item.micType}
    </sql>
    <select id="getUidDataList" resultType="string">
        select uid from t_room_mic_info where room_id=#{roomId}
    </select>
    <select id="getData" resultMap="baseResultMap">
        select id, <include refid="baseSql"/> from t_room_mic_info where room_id=#{roomId} and position=#{position} limit 1
    </select>
    <select id="getDataList" resultMap="baseResultMap">
        select id, <include refid="baseSql"/> from t_room_mic_info where room_id=#{roomId}
    </select>
    <select id="getRoomMicDataList" resultMap="baseResultMap">
        select id, <include refid="baseSql"/> from t_room_mic_info where room_id=#{roomId} and uid != ''
    </select>
    <insert id="insert" useGeneratedKeys="true" keyProperty="id" keyColumn="id">
        insert into t_room_mic_info (<include refid="baseSql"/>) values (<include refid="itemSql"/>)
    </insert>
    <update id="update">
        update t_room_mic_info set mic_lock=#{item.micLock},uid=#{item.uid},fb_gender=#{item.fbGender},country=#{item.country},mtime=#{item.mtime},up_mic_time=#{item.upMicTime},third_part_id=#{item.thirdPartId},mute_time=#{item.muteTime},mute=#{item.mute},mic_type=#{item.micType} where id=#{item.id} limit 1
    </update>
    <select id="getRecommendDataList" resultMap="baseResultMap">
        SELECT rm.uid,
        rm.room_id,
        rm.fb_gender
        FROM t_room_mic_info rm
        WHERE rm.uid != ''
        <if test="country != null and country !=''">
            AND rm.country=#{country}
        </if>
        <if test="gender != null and gender != -1">
            AND rm.fb_gender=#{gender}
        </if>
        ORDER BY mtime DESC LIMIT 100
    </select>

    <select id="getRecommendUserDataList" resultMap="baseResultMap">
        SELECT uid,
        room_id,
        fb_gender
        FROM t_room_mic_info rm
        WHERE uid != ''
        <if test="gender != null and gender != -1">
            AND rm.fb_gender=#{gender}
        </if>
        ORDER BY mtime DESC LIMIT 500
    </select>

    <select id="getSameHobbyUidList" resultType="string">
        SELECT DISTINCT uid
        FROM t_user_label
        WHERE label_id in (SELECT label_id FROM t_user_label WHERE uid = #{uid})
        <if test="aidSet.size > 0">
            AND uid IN
            <foreach item="item" collection="aidSet" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
    </select>

    <select id="getFcmRoomList" resultType="string">
        SELECT
            room_id
        FROM t_room_mic_info
        WHERE
            uid != ''
        GROUP BY
            room_id
        HAVING
            count( uid ) >= 3
    </select>

    <select id="getAllOnMicUserList" resultType="com.quhong.mysql.data.RoomMicData">
        select id, <include refid="baseSql"/> from t_room_mic_info where uid != '' ORDER BY mtime DESC
    </select>
</mapper>
