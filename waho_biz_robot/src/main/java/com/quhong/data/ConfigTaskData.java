package com.quhong.data;

import com.github.benmanes.caffeine.cache.Cache;
import com.github.benmanes.caffeine.cache.Caffeine;
import com.quhong.mysql.data.BizRobotConfigData;
import com.quhong.robot.BizRobot;

import java.util.List;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.CopyOnWriteArrayList;
import java.util.concurrent.TimeUnit;

public class ConfigTaskData {
    // 配置参数
    private BizRobotConfigData configData;
    // 在房间的机器人
    private ConcurrentHashMap<String, List<BizRobot>> roomRobotMap = new ConcurrentHashMap<>();
    // 维护每个房间需要进入机器人的任务队列
    private Map<String, CopyOnWriteArrayList<EnterRoomTask>> ENTER_ROOM_MAP = new ConcurrentHashMap<>();
    // 机器人被踢达到一定数量后，将该房间加入到房间黑名单
    private final Cache<String, Boolean> roomKickBlockCache = Caffeine.newBuilder()
            .expireAfterWrite(8, TimeUnit.HOURS)
            .initialCapacity(100)
            .maximumSize(1000)
            .build();

    // 上一次添加大礼物进房间的时间戳
    private int addBgTaskTime;
    // 是否正在停止
    private boolean isStopping;

    public ConfigTaskData() {
    }

    public ConfigTaskData(BizRobotConfigData configData) {
        this.configData = configData;
    }

    public int getId() {
        return configData.getId();
    }

    public BizRobotConfigData getConfigData() {
        return configData;
    }

    public void setConfigData(BizRobotConfigData configData) {
        this.configData = configData;
    }

    public ConcurrentHashMap<String, List<BizRobot>> getRoomRobotMap() {
        return roomRobotMap;
    }

    public void setRoomRobotMap(ConcurrentHashMap<String, List<BizRobot>> roomRobotMap) {
        this.roomRobotMap = roomRobotMap;
    }

    public Map<String, CopyOnWriteArrayList<EnterRoomTask>> getENTER_ROOM_MAP() {
        return ENTER_ROOM_MAP;
    }

    public void setENTER_ROOM_MAP(Map<String, CopyOnWriteArrayList<EnterRoomTask>> ENTER_ROOM_MAP) {
        this.ENTER_ROOM_MAP = ENTER_ROOM_MAP;
    }

    public Cache<String, Boolean> getRoomKickBlockCache() {
        return roomKickBlockCache;
    }

    public int getAddBgTaskTime() {
        return addBgTaskTime;
    }

    public void setAddBgTaskTime(int addBgTaskTime) {
        this.addBgTaskTime = addBgTaskTime;
    }

    public boolean isStopping() {
        return isStopping;
    }

    public void setStopping(boolean stopping) {
        isStopping = stopping;
    }
}
