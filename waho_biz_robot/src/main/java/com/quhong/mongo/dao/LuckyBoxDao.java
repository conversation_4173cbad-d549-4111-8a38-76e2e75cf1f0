package com.quhong.mongo.dao;

import com.quhong.mongo.config.MongoBean;
import com.quhong.mongo.data.LuckyBoxData;
import jakarta.annotation.Resource;
import org.bson.types.ObjectId;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.data.mongodb.core.query.Criteria;
import org.springframework.data.mongodb.core.query.Query;
import org.springframework.stereotype.Component;


@Component
public class LuckyBoxDao {

    private static final Logger logger = LoggerFactory.getLogger(LuckyBoxDao.class);
    public static final String TABLE_NAME = "lucky_box";

    @Resource(name = MongoBean.MOVIES)
    private MongoTemplate mongoTemplate;

    public LuckyBoxData findData(String boxId) {
        try {
            Criteria criteria = Criteria.where("_id").is(new ObjectId(boxId));
            return mongoTemplate.findOne(new Query(criteria), LuckyBoxData.class);
        } catch (Exception e) {
            logger.error("find lucky box data error. boxId={} {}", boxId, e.getMessage(), e);
            return null;
        }
    }
}
