package com.quhong.executors.http;

import com.alibaba.fastjson.JSONObject;
import com.quhong.data.HttpReqData;
import com.quhong.data.RoomData;
import com.quhong.executors.msg.LuckyBoxExecutor;
import com.quhong.http.HttpResult;
import com.quhong.robot.Robot;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;

import java.util.concurrent.atomic.AtomicInteger;

@Component
public class LuckyBoxHandler extends AbstractHttpHandler {
    private static final Logger logger = LoggerFactory.getLogger(LuckyBoxHandler.class);
    public static final String RUSH_LUCKY_BOX_URL = "game/box/get";


    public void rushRequest(Robot robot, String boxId) {
        RoomData roomData = robot.getRoomData();
        if (null == robot.getRobotData().getLuckyBoxRoomId() || !robot.getRobotData().getLuckyBoxRoomId().equals(roomData.getRoomId())) {
            return;
        }
        logger.info("rush lucky box. uid={} boxId={}", robot.getUid(), boxId);
        robot.getRobotData().setLuckyBoxId(null);
        Integer boxMoney = LuckyBoxExecutor.boxData.getIfPresent(boxId);
        AtomicInteger rushCount = LuckyBoxExecutor.rushCount.getIfPresent(boxId);
        if (null != boxMoney && null != rushCount) {
            double rushRate = robot.getConfigData().getRushRate();
            if (rushCount.intValue() > boxMoney * (rushRate == 0 ? 0.5 : rushRate)) {
                logger.info("lucky box is had been plundered. boxId={} boxMoney={} rushCount={}", boxId, boxMoney, rushCount);
                return;
            }
        }
        LuckyBoxDTO reqData = new LuckyBoxDTO();
        reqData.copyFrom(robot);
        reqData.setRoom_id(roomData.getRoomId());
        reqData.setBox_id(boxId);
        requestV2(RUSH_LUCKY_BOX_URL, reqData, data -> {
            HttpResult httpResult = data.getBody();
            if (null == httpResult || httpResult.isError()) {
                return;
            }
            JSONObject jsonObject = (JSONObject) httpResult.getData();
            int money = jsonObject.getIntValue("money");
            AtomicInteger value = LuckyBoxExecutor.rushCount.get(boxId, k -> new AtomicInteger(0));
            int totalRush = value.addAndGet(money);
            robot.getRoomData().getLuckyBoxRoomSet().add(roomData.getRoomId());
            logger.info("rush lucky box success. uid={} boxId={} rushMoney={} totalRush={}", robot.getUid(), boxId, money, totalRush);
        });
    }

    public static class LuckyBoxDTO extends HttpReqData {
        private String room_id;
        private String box_id;
        private Integer x_pos = 0;
        private Integer y_pos = 0;

        public String getRoom_id() {
            return room_id;
        }

        public void setRoom_id(String room_id) {
            this.room_id = room_id;
        }

        public String getBox_id() {
            return box_id;
        }

        public void setBox_id(String box_id) {
            this.box_id = box_id;
        }

        public Integer getX_pos() {
            return x_pos;
        }

        public void setX_pos(Integer x_pos) {
            this.x_pos = x_pos;
        }

        public Integer getY_pos() {
            return y_pos;
        }

        public void setY_pos(Integer y_pos) {
            this.y_pos = y_pos;
        }
    }

    public static class GetLuckyBoxVO {
        private int money;
        private int beans;

        public int getMoney() {
            return money;
        }

        public void setMoney(int money) {
            this.money = money;
        }

        public int getBeans() {
            return beans;
        }

        public void setBeans(int beans) {
            this.beans = beans;
        }
    }
}
