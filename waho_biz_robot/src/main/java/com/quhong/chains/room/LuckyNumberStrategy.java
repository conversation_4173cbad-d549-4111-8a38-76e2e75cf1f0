package com.quhong.chains.room;

import com.quhong.chains.AbstractRobotStrategy;
import com.quhong.executors.http.LuckyNumberHandler;
import com.quhong.executors.msg.LuckyNumberExecutor;
import com.quhong.mysql.data.BizRobotConfigData;
import com.quhong.robot.Robot;
import org.springframework.util.StringUtils;

import java.util.concurrent.ThreadLocalRandom;
import java.util.concurrent.TimeUnit;

public class LuckyNumberStrategy extends AbstractRobotStrategy {
    private final LuckyNumberHandler luckyNumberHandler;
    private long lastSendTime = 0;
    private long inActiveTime = TimeUnit.MINUTES.toMillis(2);


    public LuckyNumberStrategy(Robot robot, LuckyNumberHandler luckyNumberHandler) {
        super(robot);
        this.needTick = true;
        this.luckyNumberHandler = luckyNumberHandler;
    }

    @Override
    protected void doDispose() {
        // for override
    }

    @Override
    public void start() {

    }

    /**
     * 1、页面增加机器人加入幸运数字游戏
     * 1.1 是否加入机器人到游戏中：是/否
     * 2、选择是，则加入幸运数字游戏
     * 3、机器人玩幸运数字规则说明：
     * 3.1  60秒内房间内有5个真实用户在玩幸运数字时，触发机器人玩幸运数字
     * 3.2 机器人不外部增派，只启用当前房间的机器人，如果当前无机器人则不触发。
     * 3.3 机器人发幸运数字频率：5秒发送一次
     * 3.4 机器人发幸运数字占比：默认50%，可调配
     * 3.5 60秒内低于5个真实用户发幸运数字，机器人停止任务。
     * 3.6 机器人账号无钻石或钻石余额不足时不玩幸运数字
     * 3.7 幸运数字出数规则和房间规则相同。
     */
    @Override
    protected void doOnTick() {
        synchronized (this.robot) {
            if (StringUtils.isEmpty(robot.getRoomId())) {
                return;
            }
            BizRobotConfigData configData = robot.getConfigData();
            if (1 != configData.getJoinLuckyNumber()) {
                return;
            }
            if (!LuckyNumberExecutor.luckyNumberActive(robot.getRoomId())) {
                return;
            }
            // 机器人发幸运数字占比：默认50%，可调配
            if (isTarget(this.robot.getUid(), configData.getLuckyNumberRate())) {
                // 机器人发幸运数字频率：5秒发送一次
                long nowTimeMillis = System.currentTimeMillis();
                long thresholdTime = ThreadLocalRandom.current().nextLong(5000, 7000);
                // 防止瞬间并发
                if (nowTimeMillis - lastSendTime > inActiveTime) {
                    // 让30%的机器人正常执行
                    if (ThreadLocalRandom.current().nextInt(0, 100) > 30) {
                        return;
                    }
                }
                if (nowTimeMillis - lastSendTime > thresholdTime) {
                    luckyNumberHandler.sendLuckyNum(this.robot);
                    lastSendTime = nowTimeMillis;
                }
            }
        }
    }

    public boolean isTarget(String uid, double ratio) {
        int modValue = (int) (1 / ratio);
        return uid.hashCode() % modValue == 0;
    }
}
