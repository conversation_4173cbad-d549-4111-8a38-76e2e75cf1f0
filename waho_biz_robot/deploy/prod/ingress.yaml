apiVersion: networking.k8s.io/v1
kind: Ingress
metadata:
  namespace: devops
  name: waho-biz-robot
  annotations:
    alb.ingress.kubernetes.io/group.name: waho
    alb.ingress.kubernetes.io/load-balancer-name: apiv2
    kubernetes.io/ingress.class: alb
    alb.ingress.kubernetes.io/scheme: internet-facing
    alb.ingress.kubernetes.io/target-type: ip
    alb.ingress.kubernetes.io/target-group-attributes: stickiness.enabled=true,stickiness.lb_cookie.duration_seconds=600
    alb.ingress.kubernetes.io/listen-ports: '[{"HTTPS": 443}, {"HTTP": 80}, {"HTTP": 8080}]'
    alb.ingress.kubernetes.io/certificate-arn: arn:aws:acm:eu-central-1:462636422633:certificate/5501b1f8-9efd-4dd0-9ed9-86205efff909
    alb.ingress.kubernetes.io/healthcheck-protocol: HTTP
    alb.ingress.kubernetes.io/backend-protocol: HTTP
    alb.ingress.kubernetes.io/healthcheck-path: /system/health_check
    alb.ingress.kubernetes.io/healthcheck-interval-seconds: '5'
    alb.ingress.kubernetes.io/healthcheck-timeout-seconds: '2'  #健康检查超时时间设置为2s
spec:
  rules:
    - host: apiv1.waho.live
      http:
        paths:
          - path: /biz_robot/
            pathType: Prefix
            backend:
              service:
                name: waho-biz-robot
                port:
                  number: 8080
